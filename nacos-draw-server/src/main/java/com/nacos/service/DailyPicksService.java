package com.nacos.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.business.db.mapper.ImgDrawDetlMapper;
import com.business.db.mapper.ImgDrawRecordMapper;
import com.business.db.model.po.ImgDrawDetlPO;
import com.business.enums.BAccountEnum;
import com.business.enums.MjParsedVersionEnum;
import com.business.mj.MJWebUtil;
import com.business.mj.model.MjWebRecentJobDTO;
import com.business.mj.model.MjWebResBodyBO;
import com.business.model.po.ImgDrawRecordPO;
import com.business.utils.BStringUtil;
import com.business.utils.BUrlUtil;
import com.nacos.config.OssClientConfig;
import com.nacos.ddimg.ImgDrawUtil;
import com.nacos.enums.CommonIntEnum;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.enums.ImgDrawEnum;
import com.nacos.enums.ImgOptModelEnum;
import com.nacos.exception.E;
import com.nacos.mjapi.MJCommonEnum;
import com.nacos.mjapi.model.MjAddImageBO;
import com.nacos.model.OssParamBO;
import com.nacos.utils.ImageUtil;
import com.nacos.utils.OSSUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service("DailyPicksService")
@Transactional(rollbackFor = E.class)
public class DailyPicksService {

    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;
    @Resource
    private ImgDrawDetlMapper imgDrawDetlMapper;

    public boolean getDailyPicks(MjWebRecentJobDTO mjWebRecentJobDTO) {
        List<MjWebResBodyBO> mjWebResBodyBOList = MJWebUtil.getNewRecentJob(mjWebRecentJobDTO);
        if (mjWebResBodyBOList == null || mjWebResBodyBOList.isEmpty()) {
            return false;
        }
        mjWebResBodyBOList.forEach(mjWebResBodyBO -> {
            long count = imgDrawRecordMapper.selectCount(
                    new LambdaQueryWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getUserId, BAccountEnum.OFFICIAL_ACCOUNT_ONE.getAccountId())
                            .eq(ImgDrawRecordPO::getMjJobId, mjWebResBodyBO.getParent_id())
            );
            String originalUrl = "https://cdn.midjourney.com/"+mjWebResBodyBO.getParent_id()+"/0_"+mjWebResBodyBO.getParent_grid()+".webp";
            String imgName = BStringUtil.getImageName(originalUrl);

            String url = mjUrlUploadOss(originalUrl, imgName);
            if (url != null) {
                System.out.println("mj绘画图："+url);
                ImgDrawRecordPO imgDrawRecordPO;
                ImgDrawDetlPO imgDrawDetlPO;
                try {
                    imgDrawRecordPO = initImgDrawRecordPO(mjWebResBodyBO);
                    imgDrawDetlPO = initImgDrawDetlPO(imgDrawRecordPO, mjWebResBodyBO.getParent_grid(), url);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                imgDrawRecordMapper.insert(imgDrawRecordPO);
                imgDrawDetlMapper.insert(imgDrawDetlPO);
            }
        });
        return true;
    }

    private static ImgDrawRecordPO initImgDrawRecordPO(MjWebResBodyBO mjWebResBodyBO) throws Exception{
        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setId(IdWorker.getId());
        imgDrawRecordPO.setUserId(BAccountEnum.OFFICIAL_ACCOUNT_ONE.getAccountId());
        imgDrawRecordPO.setSuperId(0l);
        imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.OPT_ATTRIBUTE_END.getValue());

        if (mjWebResBodyBO.getParsed_version().equals(MjParsedVersionEnum.MODEL_JICHU_2.getMjValue())) {
            System.out.println("mj绘画图："+mjWebResBodyBO.getParsed_version());
            imgDrawRecordPO.setModeAttribute(ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue());
        }
        if (mjWebResBodyBO.getParsed_version().equals(MjParsedVersionEnum.MODEL_JICHU_V2_1.getMjValue())) {
            System.out.println("mj绘画图："+mjWebResBodyBO.getParsed_version());
            imgDrawRecordPO.setModeAttribute(ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue());
        }

        if (BStringUtil.strMatchUrl(mjWebResBodyBO.getFull_command())) {
            MjAddImageBO mjAddImageBO = new MjAddImageBO();
            mjAddImageBO.setDefImages(getDefImages(mjWebResBodyBO.getFull_command()));
            imgDrawRecordPO.setInitImgObject(JSON.toJSONString(mjAddImageBO));
        }

        System.out.println("======================================================");
        System.out.println(mjWebResBodyBO.getFull_command());
        System.out.println("======================================================");

        String promptInit = removeStrSpecialSymbols(mjWebResBodyBO.getFull_command());
        imgDrawRecordPO.setPromptInit(extractHeavyText(promptInit));
        imgDrawRecordPO.setPromptUse(promptInit);
        imgDrawRecordPO.setDescription("每日精选");
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());
        imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(mjWebResBodyBO.getWidth(), mjWebResBodyBO.getHeight()));
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());
        imgDrawRecordPO.setWidth(mjWebResBodyBO.getWidth());
        imgDrawRecordPO.setHeight(mjWebResBodyBO.getHeight());
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
        double dzQuantity = DDUseRuleEnum.getJobMJ(imgDrawRecordPO.getModeAttribute(), MJCommonEnum.MJ_SPEED_KEY_2.getIntValue());
        imgDrawRecordPO.setUseDdQua(dzQuantity);
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());
        imgDrawRecordPO.setFinishTime(System.currentTimeMillis());
        imgDrawRecordPO.setMjJobId(mjWebResBodyBO.getParent_id());
        imgDrawRecordPO.setMjIsRelaxed(0);
        imgDrawRecordPO.setMjAccountId(1005l);
        return imgDrawRecordPO;
    }

    private static ImgDrawDetlPO initImgDrawDetlPO(ImgDrawRecordPO imgDrawRecordPO, int imgIndex, String url) {
        ImgDrawDetlPO imgDrawDetlPO = new ImgDrawDetlPO();
        imgDrawDetlPO.setId(IdWorker.getId());
        imgDrawDetlPO.setDrawRecordId(imgDrawRecordPO.getId());
        imgDrawDetlPO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawDetlPO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawDetlPO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawDetlPO.setImgIndex(imgIndex);
        imgDrawDetlPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
        imgDrawDetlPO.setIsSave(CommonIntEnum.SHOW_FALSE.getIntValue());
        imgDrawDetlPO.setIsOpen(CommonIntEnum.SHOW_FALSE.getIntValue());
        imgDrawDetlPO.setImgSourceUrl(url);
        imgDrawDetlPO.setImgUrl(url);
        imgDrawDetlPO.setWhDivide(imgDrawRecordPO.getWhDivide());
        imgDrawDetlPO.setImgWidth(imgDrawRecordPO.getWidth());
        imgDrawDetlPO.setImgHeight(imgDrawRecordPO.getHeight());
        imgDrawDetlPO.setImgType("image/webp");
        imgDrawDetlPO.setImgHue("0x000000");
        return imgDrawDetlPO;
    }

    public static String mjUrlUploadOss(String imgUrl, String imgName) {
        String imgUrlBase64 = ImageUtil.getImgUrlToBate64(imgUrl);
        if (StringUtils.isNotBlank(imgUrlBase64)) {
            OssParamBO imageUrlOss = OSSUtils.uploadBase64(OssClientConfig.ENDPOINT, OssClientConfig.ACCESSKEYID, OssClientConfig.SECRETACCESSKEY,
                    OssClientConfig.BUCKET_NAME, imgUrlBase64, imgName, 1);
            if (ObjectUtil.isNotEmpty(imageUrlOss)) {
                return imageUrlOss.getImageUrl();
            }
        }
        return null;
    }

    public static List<MjAddImageBO.MjImageBO> getDefImages(String full_command) throws Exception {
        // 使用空格将字符串拆分成单词
        List<MjAddImageBO.MjImageBO> defImages = new ArrayList<>();//垫图图片列表
        String[] urls = full_command.split("\\s+");
        // 遍历单词数组，检查是否为 URL 格式
        for (String url : urls) {
            if (isURL(url)) { // 检查是否为 URL 格式
                MjAddImageBO.MjImageBO mjImageBO = new MjAddImageBO.MjImageBO();
                String imgUrl = mjUrlUploadOss(url, IdWorker.getIdStr());
                if (!imgUrl.isBlank()) {
                    System.out.println("垫图："+mjImageBO.getUrl());
                    mjImageBO.setUrl(BUrlUtil.getBaseCdnUrl(imgUrl));
                    defImages.add(mjImageBO);
                }
            } else {
                break; // 如果不是 URL 格式，则结束循环
            }
        }
        return defImages;
    }

    // 检查是否为 URL 格式的辅助方法
    private static boolean isURL(String str) {
        return str.matches("https?://\\S+");
    }

    public static String removeStrSpecialSymbols(String originalStr) {
        // 定义匹配 URL 的正则表达式模式
        String urlPattern = "https?://\\S+";
        String removePattern = "(--cref|--cw|--sref|--sw)";
        String result = originalStr.replaceAll(urlPattern + "|" + removePattern, "");
        return result.trim();
    }

    public static String extractHeavyText(String input) {
        return input.substring(0, input.indexOf("--")).trim();
    }

}
