package com.nacos.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.business.db.model.dto.DelRecycleBinDTO;
import com.business.db.model.dto.RecycleBinQueryDTO;
import com.business.db.model.po.RecycleBinPO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;

import java.util.List;

/**
 * @className: com.intelligent.bot.service.mj、RecycleBinService
 * @description: 我的画廊 ------> 回收站
 * @author: Yl
 * @createDate: 2023-09-02 10:26
 * @version: 1.0
 */
public interface IRecycleBinService extends IService<RecycleBinPO> {
    /**
     * 查询首页画廊列表
     * @param dto
     * @return
     */
    Result<?> queryPage(RecycleBinQueryDTO dto);

    /**
     * 从回收站恢复图片到我的画廊
     * @param dto
     * @return
     */
    Result<?> resMyGallery(BaseDeleteEntity dto);


    /**
     * 从回收站彻底删除图片
     * @return
     */
    Result<?> completelyDel(BaseDeleteEntity dto);


    /**
     * 一键删除
     * @param dto
     * @return
     */
    Result<?> oneClickDel(DelRecycleBinDTO dto);

    /**
     * 批量添加回收站
     * @param list
     * @return
     */
    Result<?> batchInsert(List<RecycleBinPO> list);

    /**
     * 检测是否到期
     * ****到期了就删除回收站
     */
    Integer checkForExpire();
}
