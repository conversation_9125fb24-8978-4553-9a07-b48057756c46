package com.nacos.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.business.db.mapper.FlowRecordMapper;
import com.business.db.mapper.ImgDrawRecordMapper;
import com.business.db.mapper.PayRecordMapper;
import com.business.db.mapper.UserDDRecordMapper;
import com.business.db.model.bo.UserRightsConfigBO;
import com.business.db.model.po.FlowRecordPO;
import com.business.db.model.po.PayRecordPO;
import com.business.db.model.po.UserDDRecordPO;
import com.business.enums.BPayRecordEnum;
import com.business.enums.BRedisKeyEnum;
import com.business.enums.BUserRightsConfigEnum;
import com.business.model.po.ImgDrawRecordPO;
import com.business.utils.BDateUtil;
import com.business.utils.BMemberUtil;
import com.nacos.auth.JwtNewUtil;
import com.nacos.enums.CommonEnum;
import com.nacos.enums.CommonResultEnum;
import com.nacos.enums.ImgDrawEnum;
import com.nacos.enums.UserDDrecordEnum;
import com.nacos.exception.DzBalanceE;
import com.nacos.exception.IBusinessException;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.CheckService;
import com.nacos.utils.DateUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class CheckServiceImpl implements CheckService {

    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;
    @Resource
    private PayRecordMapper payRecordMapper;
    @Resource
    private UserDDRecordMapper userDDRecordMapper;
    @Resource
    private FlowRecordMapper flowRecordMapper;

    /**
     * 获取用户点子余额
     * @param userId 用户id
     * @param deduct 扣除金额
     * @return 结果
     */
    @Override
    public Result<Boolean> getResidueDDDeduct(Long userId, Double deduct) {
        // 条件：用户id，过期时间大于当前时间，总余额大于使用余额，按过期时间升序排序
        List<UserDDRecordPO> userDDRecordPOS = userDDRecordMapper.selectList(
                new LambdaQueryWrapper<UserDDRecordPO>()
                        .eq(UserDDRecordPO::getUserId, userId)
                        .ge(UserDDRecordPO::getExpirationTime, DateUtil.getDateNowShanghai())
                        .and(i -> i.last("total > total_usage"))
                        .orderByAsc(UserDDRecordPO::getExpirationTime)
        );
        log.info("简化条件后的查询结果: {}", userDDRecordPOS);
        if (userDDRecordPOS == null || userDDRecordPOS.isEmpty()) {
            // return Result.ERROR("点子余额不足");
            throw new DzBalanceE("剩余点子数不足");
        }
        //装载是否存在回退的余额记录
        UserDDRecordPO userDDRecordPOReturn = null;
        List<UserDDRecordPO> userDDRecordPOActivityList = new ArrayList<>();
        List<UserDDRecordPO> userDDRecordPOTaskList = new ArrayList<>();
        List<UserDDRecordPO> userDDRecordPOPayList = new ArrayList<>();

        double total = 0;
        double totalUsage = 0;
        ///遍历4种类型
        for (UserDDRecordPO userDDRecordPO : userDDRecordPOS) {
            //记录一下总余额
            total = new BigDecimal(Double.toString(total)).add(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue();
            totalUsage = new BigDecimal(Double.toString(totalUsage)).add(new BigDecimal(userDDRecordPO.getTotalUsage().toString())).doubleValue();
            //回退余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_RETURN.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOReturn = userDDRecordPO;
            }
            //活动余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_ACTIVITY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOActivityList.add(userDDRecordPO);
            }
            //任务余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_TASK.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOTaskList.add(userDDRecordPO);
            }
            //支付余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_PAY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOPayList.add(userDDRecordPO);
            }
        }
        double residue = new BigDecimal(total).subtract(new BigDecimal(totalUsage)).doubleValue();
        if (residue < deduct) {
            // return Result.ERROR("点子余额不足");
            throw new DzBalanceE(CommonResultEnum.DZ_BALANCE_ERROR.getValue());
        }
        //点子余额充足，扣除
        //1优先扣除回退点子数量
        if (userDDRecordPOReturn != null) {
            Result<Double> result = getUserDDRecordPODeduct(userDDRecordPOReturn, deduct);
            if (result.getData() == null) {
                throw new DzBalanceE("更新点点余额失败");
                // return Result.ERROR("更新点点余额失败");
            } else if (result.getData() > 0) {
                deduct = result.getData();
            } else {
                return Result.SUCCESS(true);
            }
        }

        //活动余额不为空
        if (!userDDRecordPOActivityList.isEmpty() && deduct > 0) {
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOActivityList) {
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deduct);
                log.info("====result= {}", result);
                if (result.getData() == null) {
                    throw new DzBalanceE("更新点点余额失败");
                    // return Result.ERROR("更新点点余额失败");
                } else if (result.getData() > 0) {
                    deduct = result.getData();
                } else {
                    return Result.SUCCESS(true);
                }
            }
        }

        //任务余额不为空
        if (!userDDRecordPOTaskList.isEmpty() && deduct > 0) {
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOTaskList) {
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deduct);
                if (result.getData() == null) {
                    throw new DzBalanceE("更新点点余额失败");
                    // return Result.ERROR("更新点点余额失败");
                } else if (result.getData() > 0) {
                    deduct = result.getData();
                } else {
                    return Result.SUCCESS(true);
                }
            }
        }

        //支付余额不为空
        if (!userDDRecordPOPayList.isEmpty() && deduct > 0) {
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOPayList) {
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deduct);
                if (result.getData() == null) {
                    throw new DzBalanceE("更新点点余额失败");
                    // return Result.ERROR("更新点点余额失败");
                } else if (result.getData() > 0) {
                    deduct = result.getData();
                } else {
                    return Result.SUCCESS(true);
                }
            }
        }
        throw new DzBalanceE("剩余点子数不足");
        //return Result.ERROR("点点余额不足");
    }

    @Override
    public void ddDeduct(Long userId, Double deduct, String remark) {
        Double deductDD = deduct;
        // 查询用户点子余额，条件：用户id，过期时间大于当前时间，总余额大于使用余额，按过期时间升序排序
        List<UserDDRecordPO> userDDRecordPOS = userDDRecordMapper.selectList(
                new LambdaQueryWrapper<UserDDRecordPO>()
                        .eq(UserDDRecordPO::getUserId, userId)
                        .ge(UserDDRecordPO::getExpirationTime, DateUtil.getDateNowShanghai())
                        .and(i -> i.last("total > total_usage"))
                        .orderByAsc(UserDDRecordPO::getExpirationTime)
        );
        if (userDDRecordPOS == null || userDDRecordPOS.isEmpty()) {
            throw new DzBalanceE("剩余点子数不足");
        }
        log.info("用户点子余额 {}", userDDRecordPOS);
        //装载是否存在回退的余额记录
        UserDDRecordPO userDDRecordPOReturn = null;
        List<UserDDRecordPO> userDDRecordPOActivityList = new ArrayList<>();
        List<UserDDRecordPO> userDDRecordPOTaskList = new ArrayList<>();
        List<UserDDRecordPO> userDDRecordPOPayList = new ArrayList<>();

        double total = 0;
        double totalUsage = 0;
        ///遍历4种类型
        for (UserDDRecordPO userDDRecordPO : userDDRecordPOS) {
            //记录一下总余额
            total = new BigDecimal(Double.toString(total)).add(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue();
            totalUsage = new BigDecimal(Double.toString(totalUsage)).add(new BigDecimal(userDDRecordPO.getTotalUsage().toString())).doubleValue();
            //回退余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_RETURN.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOReturn = userDDRecordPO;
            }
            //活动余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_ACTIVITY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOActivityList.add(userDDRecordPO);
            }
            //任务余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_TASK.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOTaskList.add(userDDRecordPO);
            }
            //支付余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_PAY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOPayList.add(userDDRecordPO);
            }
        }
        double residue = new BigDecimal(total).subtract(new BigDecimal(totalUsage)).doubleValue();
        if (residue < deductDD) {
            throw new DzBalanceE(CommonResultEnum.DZ_BALANCE_ERROR.getValue());
        }
        //点子余额充足，扣除
        //1优先扣除回退点子数量
        if (userDDRecordPOReturn != null) {
            Result<Double> result = getUserDDRecordPODeduct(userDDRecordPOReturn, deductDD);
            if (result.getData() == null) {
                throw new DzBalanceE("更新点点余额失败");
            } else if (result.getData() > 0) {
                deductDD = result.getData();
            } else {
                deductDD = 0.0;
            }
        }

        //活动余额不为空
        if (!userDDRecordPOActivityList.isEmpty() && deductDD > 0) {
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOActivityList) {
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deductDD);
                log.info("====result= {}", result);
                if (result.getData() == null) {
                    throw new DzBalanceE("更新点点余额失败");
                } else if (result.getData() > 0) {
                    deductDD = result.getData();
                } else {
                    deductDD = 0.0;
                }
            }
        }

        //任务余额不为空
        if (!userDDRecordPOTaskList.isEmpty() && deductDD > 0) {
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOTaskList) {
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deductDD);
                if (result.getData() == null) {
                    throw new DzBalanceE("更新点点余额失败");
                } else if (result.getData() > 0) {
                    deductDD = result.getData();
                } else {
                    deductDD = 0.0;
                }
            }
        }

        //支付余额不为空
        if (!userDDRecordPOPayList.isEmpty() && deductDD > 0) {
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOPayList) {
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deductDD);
                if (result.getData() == null) {
                    throw new DzBalanceE("更新点点余额失败");
                } else if (result.getData() > 0) {
                    deductDD = result.getData();
                } else {
                    deductDD = 0.0;
                }
            }
        }
        //扣除完毕
        if (deductDD <= 0) {
            FlowRecordPO flowRecordSub = FlowRecordPO.builder()
                    .userId(userId)
                    .num(deduct)
                    .recordType(CommonEnum.COMM_ONE.getValue())
                    .remark(remark)
                    .build();
            if (flowRecordMapper.insert(flowRecordSub) < 1) {
                throw new DzBalanceE("扣除点子失败");
            }
        }
    }

    public Result<Double> getUserDDRecordPODeduct(UserDDRecordPO userDDRecordPO, Double deduct) {
        double returnDeduct = new BigDecimal(userDDRecordPO.getTotalUsage().toString()).add(new BigDecimal(deduct.toString())).doubleValue();
        LambdaUpdateWrapper<UserDDRecordPO> updateWrapper = Wrappers.lambdaUpdate(UserDDRecordPO.class);
        updateWrapper.eq(UserDDRecordPO::getUserId, userDDRecordPO.getUserId());
        updateWrapper.eq(UserDDRecordPO::getId, userDDRecordPO.getId());

        //足额被扣除：直接返回
        if (returnDeduct <= userDDRecordPO.getTotal()) {
            updateWrapper.set(UserDDRecordPO::getTotalUsage, returnDeduct);
            if (userDDRecordMapper.update(null, updateWrapper) > 0) {
                return Result.SUCCESS((double) 0);
            }
            return Result.ERROR("更新点点余额失败");
        }
        //非足额扣除
        updateWrapper.set(UserDDRecordPO::getTotalUsage, userDDRecordPO.getTotal());
        if (userDDRecordMapper.update(null, updateWrapper) < 1) {
            return Result.ERROR("更新点点余额失败");
        }
        return Result.SUCCESS(new BigDecimal(String.valueOf(returnDeduct)).subtract(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue());
    }

    @Override
    public boolean setReturnDD(Long userId, Double ddQuantity) {
        LambdaQueryWrapper<UserDDRecordPO> queryWrapper = new LambdaQueryWrapper<UserDDRecordPO>()
                .eq(UserDDRecordPO::getUserId, userId)
                .eq(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_RETURN.getIntValue())
                .eq(UserDDRecordPO::getTypeItem, UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue());
        UserDDRecordPO userDDRecordPO = userDDRecordMapper.selectOne(queryWrapper);
        if (userDDRecordPO == null) {
            return userDDRecordMapper.insert(new UserDDRecordPO(
                    userId,
                    Long.valueOf(UserDDrecordEnum.SOURCE_ID_INVALID.getIntValue()),
                    UserDDrecordEnum.TYPE_RETURN.getIntValue(),
                    UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue(),
                    ddQuantity,
                    DateUtil.getDateAddMonth(DateUtil.getDateNowShanghai(), 600)
            )) > 0;
        }
        LambdaUpdateWrapper<UserDDRecordPO> updateWrapper = Wrappers.lambdaUpdate(UserDDRecordPO.class);
        updateWrapper.eq(UserDDRecordPO::getUserId, userId)
                .eq(UserDDRecordPO::getId, userDDRecordPO.getId())
                .eq(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_RETURN.getIntValue())
                .eq(UserDDRecordPO::getTypeItem, UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue())
                .set(UserDDRecordPO::getTotal, new BigDecimal(userDDRecordPO.getTotal().toString()).add(new BigDecimal(ddQuantity.toString())).doubleValue())
                .set(UserDDRecordPO::getTotalUsage, userDDRecordPO.getTotalUsage());
        return userDDRecordMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 检测并发数量
     * @return 返回是否成功true/false
     * @throws IBusinessException 业务异常
     */
    public Integer checkConcurrentCount() throws IBusinessException {
        // 查询用户vip记录，查询条件是用户id，支付状态为成功，订单类型为vip或svip，过期时间大于当前时间，按过期时间降序排序，取一条记录
        PayRecordPO payRecordPO = payRecordMapper.selectOne(
                new LambdaQueryWrapper<PayRecordPO>()
                        .eq(PayRecordPO::getUserId, JwtNewUtil.getUserId())
                        .eq(PayRecordPO::getState, BPayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                        .in(PayRecordPO::getOrderType, BPayRecordEnum.ORDER_TYPE_VIP.getIntValue(), BPayRecordEnum.ORDER_TYPE_SVIP.getIntValue())
                        .ge(PayRecordPO::getExpirationTime, BDateUtil.getDateNowShanghai())
                        .orderByDesc(PayRecordPO::getExpirationTime)
                        .last("LIMIT 0,1")
        );
        // 校验用户并发数量：设置默认并发数量为1
        int memberLevel = 0;
        int concurrentCount = 1;
        // 如果用户有vip记录，则获取vip配置
        if (payRecordPO != null && payRecordPO.getVipConfigId() != null) {
            int vipConfigId = payRecordPO.getVipConfigId().intValue();
            List<UserRightsConfigBO> userRightsConfigBOList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.VIP_RIGHTS_CONFIG.getKey()), new TypeReference<List<UserRightsConfigBO>>() {
            });
            UserRightsConfigBO userRightsConfig = com.business.utils.ImgDrawUtil.getUserRightsConfigById(userRightsConfigBOList, BUserRightsConfigEnum.RIGHTS_TASK_CONCURRENCY.getIntValue());
            memberLevel = BMemberUtil.getMemberLevelByVipConfigId(vipConfigId);
            concurrentCount = BMemberUtil.getMemberConcurrentCount(vipConfigId, userRightsConfig);
        }
        // 查询并发数量，查询条件是用户id，状态为排队中或进行中，功能类型为绘图
        long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getUserId, JwtNewUtil.getUserId())
                        .in(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_QUEUING.getValue(), ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
        );
        if (count >= concurrentCount) {
            return null;
        }
        return memberLevel;
    }

}
