package com.nacos.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.business.db.mapper.DrawGalleryMapper;
import com.business.db.mapper.ImgCollectMapper;
import com.business.db.mapper.ImgDrawDetlMapper;
import com.business.db.mapper.ImgLikeMapper;
import com.business.db.model.dto.*;
import com.business.db.model.po.*;
import com.business.db.model.vo.GalleryQueryVO;
import com.business.db.model.vo.ImgGalleryVO;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.nacos.enums.DeletedStatusEnum;
import com.nacos.enums.InteractTitleEnum;
import com.nacos.enums.InteractTypeEnum;
import com.nacos.enums.MyGalleryTypeEnum;
import com.nacos.result.Result;
import com.nacos.service.IGalleryService;
import com.nacos.service.IRecycleBinService;
import com.nacos.service.sys.ISysInteractionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * @className: com.intelligent.bot.service.mj.impl-> GalleryServiceImpl
 * @description: 画廊相关
 * @author: Admin
 * @createDate: 2023-06-19 10:19
 * @version: 1.0
 */
@Slf4j
@Service
public class GalleryServiceImpl extends ServiceImpl<DrawGalleryMapper, GalleryPO> implements IGalleryService {

    @Resource
    private ImgCollectMapper imgCollectMapper;
    @Resource
    private ImgLikeMapper imgLikeMapper;
    @Resource
    private IRecycleBinService recycleBinService;


    @Resource
    private ISysInteractionService sysInteractionService;
    @Resource
    private ImgDrawDetlMapper imgDrawDetlMapper;//绘图明细记录


    /**
     * 查询我的收藏列表: app 2.0
     * @param dto
     * @return
     */
    @Override
    public Result<Page<ImgGalleryVO>> queryCollectPage(GalleryQueryDTO dto) {
        if(ObjectUtil.isNull(dto.getPainterId())){
            dto.setPainterId(dto.getUserId());
        }
        Page<ImgGalleryVO> page = new Page<>(dto.getPageNumber(),dto.getPageSize());
        if (dto.getJobType() == MyGalleryTypeEnum.COLLECT.getType()){
            return Result.SUCCESS(this.baseMapper.queryCollectPage(page,dto));
        }
        return Result.SUCCESS(this.baseMapper.queryLikePage(page,dto));
    }

    /**
     * 查询个人画廊列表
     * @param dto
     * @return
     */
    @Override
    public Result<Page<GalleryQueryVO>> queryMyGalleryPage(GalleryQueryDTO dto) {
        if(ObjectUtil.isNull(dto.getPainterId()))
            dto.setPainterId(dto.getUserId());
        Page<GalleryQueryVO> page = new Page<>(dto.getPageNumber(),dto.getPageSize());
        return Result.SUCCESS(this.baseMapper.queryMyGalleryPage(page,dto));
    }

    /**
     * 查询个人画廊列表 new
     * @param dto
     * @return
     */
    @Override
    public Result<?> queryMyGalleryNewPage(GalleryQueryNewDTO dto) {
        Page<GalleryQueryVO> page = new Page<>(dto.getPageNumber(),dto.getPageSize());
        if(ObjectUtil.isNull(dto.getPainterId()))
            dto.setPainterId(dto.getUserId());
        return Result.SUCCESS(this.baseMapper.queryMyGalleryNewPage(page,dto));
    }


    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Result<?> collectWorks(ImgCollectDTO dto) {
        if(ObjectUtil.isNull(dto.getTaskId()) || ObjectUtil.isNull(dto.getImgId())){
            return Result.ERROR("参数不可为空！");
        }
        List<ImgCollectPO> galleryCollect = imgCollectMapper.selectList(
            new LambdaQueryWrapper<ImgCollectPO>()
                .eq(ImgCollectPO::getTaskId,dto.getTaskId())
                .eq(ImgCollectPO::getImgId, dto.getImgId())
                .eq(ImgCollectPO::getUserId,dto.getUserId())
        );
        if(CollectionUtil.isEmpty(galleryCollect) && galleryCollect.size()==0){
            ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectById(dto.getImgId());
            //MjTaskPO mjTaskPO = mjTaskService.getById(dto.getTaskId());
            if (ObjectUtil.isNotNull(imgDrawDetlPO) && ObjectUtil.isNotNull(imgDrawDetlPO.getUserId())) {
                SysInteractionPO sysInteractionPO = SysInteractionPO.buildSysInteraction(imgDrawDetlPO.getUserId(), dto.getUserId(), dto.getTaskId(), dto.getImgId(),
                        InteractTitleEnum.COLLECT.getTitle(), InteractTypeEnum.COLLECT.getType(), imgDrawDetlPO.getImgUrl());
                sysInteractionService.save(sysInteractionPO);
                sendMessage(imgDrawDetlPO.getUserId(), sysInteractionPO);
            }
            ImgCollectPO collect = new ImgCollectPO();
            collect.setUserId(dto.getUserId());
            collect.setTaskId(dto.getTaskId());
            collect.setImgId(dto.getImgId());
            imgCollectMapper.insert(collect);
            imgDrawDetlMapper.update(null, new LambdaUpdateWrapper<ImgDrawDetlPO>()
                            .eq(ImgDrawDetlPO::getId, dto.getImgId())
                            .setSql("collect_qua = collect_qua + 1")
            );
        }else {
            imgCollectMapper.deleteCollect(dto.getTaskId(),dto.getImgId(),dto.getUserId());
            imgDrawDetlMapper.update(null, new LambdaUpdateWrapper<ImgDrawDetlPO>()
                    .eq(ImgDrawDetlPO::getId, dto.getImgId())
                    .setSql("collect_qua = collect_qua - 1")
            );
        }
        return Result.SUCCESS();
    }

    private void sendMessage(Long userId, SysInteractionPO sysInteractionPO) {
        boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(userId, BMessageSendEnum.INTERACTION_PUSH,JSONObject.toJSONString(sysInteractionPO)));
        log.info("互动消息推送状态:{}", state);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Result<?> likeWorks(ImgLikeDTO dto) {
        if(ObjectUtil.isNull(dto.getTaskId()) || ObjectUtil.isNull(dto.getImgId())){
            return Result.ERROR("参数不可为空！");
        }
        List<ImgLikePO> galleryLikeList = imgLikeMapper.selectList(
                new LambdaQueryWrapper<ImgLikePO>()
                    .eq(ImgLikePO::getTaskId,dto.getTaskId())
                    .eq(ImgLikePO::getImgId, dto.getImgId())
                    .eq(ImgLikePO::getUserId,dto.getUserId()));
        if(CollectionUtil.isEmpty(galleryLikeList) && galleryLikeList.size()==0){
            ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectById(dto.getImgId());
            //MjTaskPO mjTaskPO = mjTaskService.getById(dto.getTaskId());
            if (ObjectUtil.isNotNull(imgDrawDetlPO) && ObjectUtil.isNotNull(imgDrawDetlPO.getUserId())) {
                SysInteractionPO sysInteractionPO = SysInteractionPO.buildSysInteraction(imgDrawDetlPO.getUserId(), dto.getUserId(), dto.getTaskId(), dto.getImgId(),
                        InteractTitleEnum.LIKE.getTitle(), InteractTypeEnum.LIKE.getType(), imgDrawDetlPO.getImgUrl());
                sysInteractionService.save(sysInteractionPO);
                sendMessage(imgDrawDetlPO.getUserId(), sysInteractionPO);
            }
            ImgLikePO like = new ImgLikePO();
            like.setUserId(dto.getUserId());
            like.setTaskId(dto.getTaskId());
            like.setImgId(dto.getImgId());
            imgLikeMapper.insert(like);
            //修改点赞数量
            imgDrawDetlMapper.update(null, new LambdaUpdateWrapper<ImgDrawDetlPO>()
                    .eq(ImgDrawDetlPO::getId, dto.getImgId())
                    .setSql("good_qua = good_qua + 1")
            );
        }else {
            imgLikeMapper.deleteLike(dto.getTaskId(),dto.getImgId(),dto.getUserId());
            imgDrawDetlMapper.update(null, new LambdaUpdateWrapper<ImgDrawDetlPO>()
                    .eq(ImgDrawDetlPO::getId, dto.getImgId())
                    .setSql("good_qua = good_qua - 1")
            );
        }
        return Result.SUCCESS();
    }

    /**
     * 查询我的点赞列表
     * @param dto
     * @return
     */
    @Override
    public Result<?> queryMyLikePage(GalleryQueryDTO dto) {
        if(ObjectUtil.isNull(dto.getPainterId())){
            dto.setPainterId(dto.getUserId());
        }
        Page<GalleryQueryVO> page = new Page<>(dto.getPageNumber(),dto.getPageSize());
        return Result.SUCCESS(this.baseMapper.queryMyLikePage(page,dto));
    }

    /**
     * 删除我的画廊
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Result<?> delMyGallery(DelMyGalleryDTO dto) {
        this.baseMapper.batchDelMyGallery(DeletedStatusEnum.ISYES.getCode(), dto.getImgIds());
        List<RecycleBinPO> recycleBinList = new ArrayList<>();
        dto.getImgIds().forEach(imgId -> {
            recycleBinList.add(RecycleBinPO.buildRecycleBin(dto.getUserId(), imgId));
        });
        return Result.SUCCESS(recycleBinService.batchInsert(recycleBinList));
    }
}
