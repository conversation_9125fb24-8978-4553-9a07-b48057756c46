package com.nacos.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.business.db.mapper.*;
import com.business.db.model.dto.ImgDrawOptDTO;
import com.business.db.model.po.*;
import com.business.db.model.vo.ImgDrawDetlVO;
import com.business.db.model.vo.ImgDrawHistoryVO;
import com.business.db.model.vo.ImgModelConfigVO;
import com.business.db.model.vo.MjScaleVO;
import com.business.enums.*;
import com.business.le.LeonardoUtil;
import com.business.le.model.LeonardoStyleBO;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.business.mj.BMJStopUsingUtil;
import com.business.mj.MJAccountUtil;
import com.business.model.po.AdminMjAccountConfigPO;
import com.business.model.po.ImgDrawRecordPO;
import com.business.tengxunyun.BTengXunUtil;
import com.business.utils.BDateUtil;
import com.business.utils.BStringUtil;
import com.business.utils.BThirdPartyKey;
import com.nacos.alioss.OSSApis;
import com.nacos.auth.JwtNewUtil;
import com.nacos.baiduaiapi.model.FaceDetectionResponseBodyVO;
import com.nacos.ddimg.ImgDrawUtil;
import com.nacos.ddimg.model.ImgDrawDTO;
import com.nacos.ddimg.model.ImgScaleDTO;
import com.nacos.ddimg.model.MJAccountBO;
import com.nacos.ddimg.model.MJStyleDTO;
import com.nacos.enums.*;
import com.nacos.enums.goapi.CurrentStatusEnum;
import com.nacos.exception.DzBalanceE;
import com.nacos.exception.IBusinessException;
import com.nacos.mjapi.MJAPIUtil;
import com.nacos.mjapi.MJApis;
import com.nacos.mjapi.model.JobInfo;
import com.nacos.mjapi.model.JobStatusBO;
import com.nacos.mjapi.model.MJAccountHeaderBO;
import com.nacos.mjapp.MjWebSocketListener;
import com.nacos.model.OssParamBO;
import com.nacos.model.VipGradeBO;
import com.nacos.novitaaiapi.model.dto.MergeFaceRequestBodyDTO;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.*;
import com.nacos.service.mp.IUserDDRecordService;
import com.nacos.utils.*;
import com.nacos.utils.ai.AiFaceUtil;
import com.nacos.utils.goapi.FaceSwapVO;
import com.nacos.utils.goapi.GoApiUtil;
import com.nacos.utils.oss.AliOSSUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
public class DrawServiceImpl implements DrawService {

    @Resource
    private IUserDDRecordService iUserDDRecordService;

    @Resource
    private UserDDRecordMapper userDDRecordMapper;
    @Resource
    private ImgModelConfigMapper imgModelConfigMapper;
    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;//绘图记录
    @Resource
    private ImgDrawDetlMapper imgDrawDetlMapper;//绘图详情
    @Resource
    private RedisServer redisServer;//消息推送
    @Resource
    private AsyncService asyncService;
    @Resource
    private IImgFaceTemplateService imgFaceTemplateService;
    @Resource
    private CheckBalanService checkBalanService;
    @Resource
    private DzRollbackService dzRollbackService;
    @Resource
    private CheckService checkService;
    @Resource
    private FlowRecordMapper flowRecordMapper;//点子流水记录
    @Resource
    private AdminMjAccountConfigMapper adminMjAccountConfigMapper;


    @Override
    public Result<Object> submitJob(ImgDrawDTO imgDrawDTO) throws IBusinessException {
        if (imgDrawDTO == null) {
            return Result.ERROR("参数不能为空");
        }
        if (!BStringUtil.isStringWithinLimit(imgDrawDTO.getPrompt())) {
            return Result.ERROR("提示词超出平台规定范围，请修改后重试");
        }
        ImgModelConfigPO imgModelConfigPO = imgModelConfigMapper.selectById(imgDrawDTO.getModelId());
        if (imgModelConfigPO == null) {
            return Result.ERROR("参数不能为空");
        }
        //校验 角色图片列表 || 风格图片列表 是否支持
        if (imgDrawDTO.getIsAddImage() != null && imgDrawDTO.getIsAddImage()) {
            if (imgDrawDTO.getMjAddImageBO() != null && imgDrawDTO.getMjAddImageBO().getCrefImages() != null
                    && !imgDrawDTO.getMjAddImageBO().getCrefImages().isEmpty()
                    && imgModelConfigPO.getIsSupportCref() == BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue()
            ) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_CREF_ERROR.getValue());
            }
            if (imgDrawDTO.getMjAddImageBO() != null && imgDrawDTO.getMjAddImageBO().getSrefImages() != null
                    && !imgDrawDTO.getMjAddImageBO().getSrefImages().isEmpty()
                    && imgModelConfigPO.getIsSupportSref() == BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue()) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_SREF_ERROR.getValue());
            }
        }

        imgDrawDTO.setUserId(JwtNewUtil.getUserId());
        imgDrawDTO.setPrompt(BStringUtil.stringToWrap(imgDrawDTO.getPrompt()));
        //解析字符串
        ImgModelConfigVO imgModelConfigVO = new ImgModelConfigVO(imgModelConfigPO);
        Integer checkConcurrentCount = checkConcurrentCount();
        if (checkConcurrentCount == null) {
            return Result.ERROR("绘图数量已达到上限");
        }
        boolean isVip = checkConcurrentCount > 1; //设置规则为vip用户真实规则

        // 校验跳转mj接口执行n5、n6；v5、v6
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N6.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6_1.getValue()) {
            return jobMj(isVip, imgDrawDTO, imgModelConfigVO);
        }
        //校验跳转dalle-3接口执行
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_DE.getValue()) {
            imgDrawDTO.setInitImgUrls(null);// de执行绘画无垫图功能
            return jobDalle(isVip, imgDrawDTO, imgModelConfigVO);
        }
        // sd3绘画模型 == 校验跳转sd-3接口执行
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_SD.getValue()) {
            imgDrawDTO.setInitImgUrls(null);// de执行绘画无垫图功能
            return jobSd(isVip, imgDrawDTO, imgModelConfigVO);
        }
        // le绘画模型 == 校验跳转le接口执行
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_LE.getValue()) {
            imgDrawDTO.setInitImgUrls(null);// de执行绘画无垫图功能
            return jobLe(isVip, imgDrawDTO, imgModelConfigVO);
        }
        return Result.ERROR(CommonResultEnum.NOT_ERROR.getValue());
    }

    private Result<Object> jobMj(boolean isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) {
        //校验MJ账号是否正常
        if (BMJStopUsingUtil.checkAllNot()) {
            return Result.ERROR("模型维护升级中...");
        }
        Integer mjIsRelaxed = MJAccountUtil.initIsSpeedRelaxed();

        //校验账号是否能使用
        mjIsRelaxed = BMJStopUsingUtil.handleSpeed(mjIsRelaxed);
        if (mjIsRelaxed == null) {
            return Result.ERROR("模型维护升级中...");
        }
        //校验是否存在封禁操作
        String isUserNot = BMJStopUsingUtil.isNotUserMJDraw(imgDrawDTO.getUserId());
        if (isUserNot != null) {
            return Result.ERROR(isUserNot);
        }

        JobInfo jobInfo;
        MJAccountHeaderBO mjAccountHeaderBO;
        try {
            if (isNotMJLock()) {
                return Result.ERROR("目前人数较多,请稍后再试");
            }
            // mj执行绘画
            imgDrawDTO.setPromptUse(imgDrawDTO.getPrompt());//格式化请求参数：api 方式无需进行翻译
            //查询是否存在可执行账号
            mjAccountHeaderBO = ImgDrawPUtil.getMjParam(imgDrawDTO, imgModelConfigVO, mjIsRelaxed);
            // TODO 不存在：说明账号不够用了，直接进行排队:暂时停用排队
            if (mjAccountHeaderBO.getToken() == null) {
                /*ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecord(imgDrawDTO, imgModelConfigVO, mjAccountHeaderBO);
                imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_QUEUING.getValue());
                if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
                    ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
                    imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
                    imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
                    return Result.SUCCESS(imgDrawHistoryVO);
                }
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_ERROR.getValue());*/
                return Result.ERROR("目前人数较多,请稍后再试");
            }
            // 存在可用mj账号，直接生成进行中的任务

            try {
                jobInfo = MJApis.submitJobDraw(mjAccountHeaderBO);
            } catch (Exception e) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_UNKNOWN_ERROR.getValue());
            }
            if (jobInfo == null) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_UNKNOWN_ERROR.getValue());
            }

            //校验：封号异常；token异常；时间失效异常；指令异常
            if (jobInfo.getIsPendingModMessage() || jobInfo.getIsTokenExhausted() || jobInfo.getIsCreditsExhausted() || jobInfo.getIsBannedPromptDetected() || jobInfo.getIsInvalidLink()) {
                if (jobInfo.getIsBannedPromptDetected()) {
                    BMJStopUsingUtil.prohibitMJDraw(imgDrawDTO.getUserId(), imgDrawDTO.getPrompt());
                }

                return Result.ERROR(mjError(jobInfo, mjAccountHeaderBO));
            }

            if (mjAccountHeaderBO.getIsPro()) {
                //重置快速账号信息
                ImgDrawPUtil.initMjAccountBPRO();
            } else {
                //重置普通账号信息
                ImgDrawPUtil.initMjAccountBPT();
            }
            MJAccountUtil.changeSpeedRatio();//重置速度
        } finally {
            RedisUtil.releaseLock(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_LOCK.getStrKey());//释放锁
        }

        try {
            // TODO 创建进行中任务
            ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecord(imgDrawDTO, imgModelConfigVO, mjAccountHeaderBO);

            // TODO 扣除用户点子
            double dzQuantity = DDUseRuleEnum.getJobMJ(imgModelConfigVO.getAttribute(), imgDrawDTO.getMjSpeedKey());

            if (isVip) {
                dzQuantity = dzQuantity / 2;
            }

            String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
            String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
            FlowRecordPO flowRecordSub = null;
            if (optTitleOne != null) {
                flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
            }
            checkBalanService.checkUser(imgDrawDTO.getUserId(), dzQuantity, flowRecordSub);

            imgDrawRecordPO.setUseDdQua(dzQuantity);
            imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
            imgDrawRecordPO.setUserId(imgDrawDTO.getUserId());//装载用户id
            imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
            imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
            imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
            imgDrawRecordPO.setMjJobId(jobInfo.getJob_id());//装载任务id
            imgDrawRecordPO.setMjIsRelaxed(mjIsRelaxed);
            imgDrawRecordPO.setMjAccountId(mjAccountHeaderBO.getMjAccountId());
            //装载宽高尺寸：mj接口返回的宽高尺寸
            imgDrawRecordPO.setWidth(jobInfo.getWidth());
            imgDrawRecordPO.setHeight(jobInfo.getHeight());
            imgDrawRecordPO.setImgQuantity(jobInfo.getBatch_size());
            imgDrawRecordPO.setFinalPrompt(jobInfo.getFull_command());
            imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());
            imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(jobInfo.getWidth(), jobInfo.getHeight()));
            //校验是否垫图
            if (imgDrawDTO.getInitImgUrls() != null && !imgDrawDTO.getInitImgUrls().isEmpty()) {
                imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(imgDrawDTO.getInitImgUrls()));
            }
            if (imgDrawDTO.getIsAddImage() != null && imgDrawDTO.getIsAddImage() && imgDrawDTO.getMjAddImageBO() != null) {
                imgDrawRecordPO.setInitImgUrls(mjAccountHeaderBO.getInitImgUrls());
                imgDrawRecordPO.setInitImgObject(JSON.toJSONString(imgDrawDTO.getMjAddImageBO()));
            }

            if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
                ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
                if (jobInfo.getBatch_size() > 0) {
                    imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
                    imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
                    imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
                    imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
                    imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());
                    imgDrawHistoryVO.setPrompt(imgDrawRecordPO.getPromptInit());
                    imgDrawHistoryVO.setPromptUse(imgDrawRecordPO.getPromptUse());
                    imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
                    imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
                    imgDrawHistoryVO.setImgQuantity(jobInfo.getBatch_size());
                    imgDrawHistoryVO.setInitImgObject(imgDrawRecordPO.getInitImgObject());
                    List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();
                    for (int i = 0; i < jobInfo.getBatch_size(); i++) {
                        ImgDrawDetlVO imgDrawDetlVO = new ImgDrawDetlVO();
                        imgDrawDetlVO.setDrawRecordId(imgDrawRecordPO.getId());
                        imgDrawDetlVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
                        imgDrawDetlVO.setImgIndex(i);
                        imgDrawDetlVO.setWhDivide(ImgDrawUtil.getWhDivide(jobInfo.getWidth(), jobInfo.getHeight()));
                        imgDrawDetlVO.setImgWidth(jobInfo.getWidth());
                        imgDrawDetlVO.setImgHeight(jobInfo.getHeight());
                        imgDrawDetlVOS.add(imgDrawDetlVO);
                    }
                    imgDrawHistoryVO.setImgDrawDetls(imgDrawDetlVOS);
                    log.info("imgDrawHistoryVO= {}", imgDrawHistoryVO);
                    //缓存任务有效期为一天
                    RedisUtil.setValueSeconds(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_JOB_ID.getStrKey(), imgDrawRecordPO.getMjJobId()), JSONObject.toJSONString(imgDrawHistoryVO), 1, TimeUnit.DAYS);
                    //异步渐显推送
                    MJAPIUtil.sendHandshakeRequest(imgDrawRecordPO.getMjJobId(), new MjWebSocketListener(imgDrawRecordPO.getMjJobId(), redisServer));
                }
                return Result.SUCCESS(imgDrawHistoryVO);
            }
        } catch (DzBalanceE e) {
            log.error("点子不足充值 {}", e.getMessage(), e);//释放锁
            return Result.ERROR_DZ(CommonResultEnum.DZ_BALANCE_ERROR.getValue());
        } catch (Exception e) {
            log.error("文生图操作时异常错误 {}", e.getMessage(), e);//释放锁
        }
        return Result.ERROR(CommonResultEnum.DRAW_MJ_API_ERROR.getValue());
    }

    private Result<Object> jobDalle(boolean isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        //格式化请求参数：api 方式无需进行翻译
        imgDrawDTO.setPromptUse(imgDrawDTO.getPrompt());
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecordDe(imgDrawDTO, imgModelConfigVO);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setUserId(imgDrawDTO.getUserId());//装载用户id
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        //装载宽高尺寸：mj接口返回的宽高尺寸
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());
        Map<String, Object> map = ImgDrawPUtil.getDalleWhDivideByImgScaleKey(imgDrawDTO.getImgScaleKey());
        imgDrawRecordPO.setWhDivide(Double.valueOf(map.get("whDivide").toString()));
        imgDrawRecordPO.setPromptUse(imgDrawRecordPO.getPromptUse().concat(" DALL·E 3 --ar ").concat((String) map.get("size")));

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());

        // 获取字典配置
        // Map<Long, String> dictConfigMap = DictConfigCache.getDictConfigChatMap();
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.CHAT_KEY.getDictType());
        if (dictConfigMap == null) {
            return Result.ERROR("模型升级中...");
        }

        // TODO 扣除用户点子 ===== 查询点子规则数据
        double deDrawDeductQua;
        if (imgDrawDTO.getImgScaleKey() == DalleSizeEnum.DALLE_ONE_TO_ONE.getKey()) {
            deDrawDeductQua = DDUseRuleEnum.getDDUseRuleQuality(DDUseRuleEnum.DRAW_DE_SIZE_ONE);//dalle 3扣除点点数量
        } else {
            deDrawDeductQua = DDUseRuleEnum.getDDUseRuleQuality(DDUseRuleEnum.DRAW_DE_SIZE_TWO);//dalle 3扣除点点数量
        }
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        ;
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), deDrawDeductQua, flowRecordSub);

        imgDrawRecordPO.setUseDdQua(deDrawDeductQua);
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            try {
                asyncService.asynExecutionDraw(imgDrawRecordPO, (String) map.get("size"), imgDrawDTO, imgDrawHistoryVO, deDrawDeductQua, dictConfigMap);
                return Result.SUCCESS(imgDrawHistoryVO);
            } catch (Exception e) {
                log.error("==++==dall= {}", e.getMessage());
                dzRollbackService.rollback(imgDrawRecordPO.getUserId(), deDrawDeductQua, optTitleOne.concat(optTitleTwo != null ? optTitleTwo : ""));
                imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
            }
        }
        return Result.ERROR(CommonResultEnum.DRAW_MJ_API_ERROR.getValue());
    }

    private Result<Object> jobSd(boolean isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        //格式化请求参数：api 方式需进行翻译
        String promptUse = BTengXunUtil.textToEnglish(imgDrawDTO.getPrompt(), "zh", "en");
        if (promptUse == null) {
            return Result.ERROR("提示词不能为空，请输入提示词在重试");
        }
        imgDrawDTO.setPromptUse(promptUse);
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecordDe(imgDrawDTO, imgModelConfigVO);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        //装载宽高尺寸
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());

        // 获取字典配置
        // Map<Long, String> dictConfigMap = DictConfigCache.getDictConfigChatMap();
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(200L);

        //初始化尺寸
        String ar = "1:1";
        double whDecide = 1;
        List<ImgScaleDTO> imgScales = imgModelConfigVO.getImgScales();
        for (ImgScaleDTO imgScale : imgScales) {
            if (Objects.equals(imgScale.getKey(), imgDrawDTO.getImgScaleKey())) {
                System.out.println(imgScale.toString());
                ar = imgDrawDTO.getImgScaleIsTrue() ? imgScale.getWidth() + ":" + imgScale.getHeight() : imgScale.getHeight() + ":" + imgScale.getWidth();
                whDecide = imgDrawDTO.getImgScaleIsTrue() ?
                        (new BigDecimal(String.valueOf(imgScale.getWidth())).divide(new BigDecimal(String.valueOf(imgScale.getHeight())), 6, RoundingMode.HALF_UP).doubleValue()) :
                        (new BigDecimal(String.valueOf(imgScale.getHeight())).divide(new BigDecimal(String.valueOf(imgScale.getWidth())), 6, RoundingMode.HALF_UP).doubleValue());
            }
        }

        imgDrawRecordPO.setWhDivide(whDecide);
        imgDrawRecordPO.setPromptUse(imgDrawRecordPO.getPromptUse().concat(" SD 3 --ar ").concat(ar));

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());

        // TODO 扣除用户点子 ===== 查询点子规则数据
        isVip = iUserDDRecordService.getUserIsVip();
        double sdDrawDeductQua = BDDUseNumEnum.getDDUseByIsVip(BDDUseNumEnum.SUNO_AUDIO, isVip);
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        ;
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), sdDrawDeductQua, flowRecordSub);

        imgDrawRecordPO.setUseDdQua(sdDrawDeductQua);
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            try {
                // TODO SD3绘画模型 暂时不用，后期对接
                asyncService.asynExecutionDraw(imgDrawRecordPO, ar, imgDrawDTO, imgDrawHistoryVO, sdDrawDeductQua, dictConfigMap);
                return Result.SUCCESS(imgDrawHistoryVO);
            } catch (Exception e) {
                log.error("==++==dall= {}", e.getMessage());
                dzRollbackService.rollback(imgDrawRecordPO.getUserId(), sdDrawDeductQua, optTitleOne.concat(optTitleTwo != null ? optTitleTwo : ""));
                imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
            }
        }
        return Result.ERROR(CommonResultEnum.DRAW_MJ_API_ERROR.getValue());
    }

    private Result<Object> jobLe(boolean isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        // 获取配置
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.LE_DRAW_KEY.getDictType());
        if (dictConfigMap == null) {
            log.info("LE没有可用的key: {}", "请立即更换");
            return Result.ERROR("模型升级维护中...");
        }

        //格式化请求参数：api 方式需进行翻译
        String promptUse = BTengXunUtil.textToEnglish(imgDrawDTO.getPrompt(), "zh", "en");
        if (promptUse == null) {
            return Result.ERROR("提示词不能为空，请输入提示词在重试");
        }
        imgDrawDTO.setPromptUse(promptUse);
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecordDe(imgDrawDTO, imgModelConfigVO);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_FOUR.getValue());
        imgDrawRecordPO.setIsPublish(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        // 装载宽高尺寸
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());

        // LE请求参数封装
        LeonardoStyleBO leonardoStyleBO = new LeonardoStyleBO();
        leonardoStyleBO.setNumImages(4);
        leonardoStyleBO.setPrompt(promptUse);
        leonardoStyleBO.setNegativePrompt("Disharmonious colorsative Prompt，NSFW,  watermarks, double body, double face, double features, incorrect posture, two heads, two faces, plastic, deformed, blurry, messed up eyes, crossed eyes, disfigured, poorly drawn face, mutation, mutated, ugly, poorly drawn hands, missing limb, blurry, floating limbs, disconnected limbs, malformed hands, out of focus, long neck, long body, long fingers, blender, doll, cropped, low-res, , out of frame, double two heads, blurred, ugly, disfigured, too many fingers, repetitive, grainy, extra limbs, poor anatomy, high pass filter, airbrush, zoomed, soft light, smooth skin, extra limbs, extra fingers, mutated hands, uneven proportions, blind, ugly eyes, dead eyes, blur, out of shot, out of focus");

        //初始化尺寸
        String ar = "1:1";
        double whDecide = 1;
        List<ImgScaleDTO> imgScales = imgModelConfigVO.getImgScales();
        for (ImgScaleDTO imgScale : imgScales) {
            if (Objects.equals(imgScale.getKey(), imgDrawDTO.getImgScaleKey())) {
                ar = imgDrawDTO.getImgScaleIsTrue() ? imgScale.getWidth() + ":" + imgScale.getHeight() : imgScale.getHeight() + ":" + imgScale.getWidth();
                whDecide = imgDrawDTO.getImgScaleIsTrue() ?
                        (new BigDecimal(String.valueOf(imgScale.getWidth())).divide(new BigDecimal(String.valueOf(imgScale.getHeight())), 6, RoundingMode.HALF_UP).doubleValue()) :
                        (new BigDecimal(String.valueOf(imgScale.getHeight())).divide(new BigDecimal(String.valueOf(imgScale.getWidth())), 6, RoundingMode.HALF_UP).doubleValue());
                int[] ratio = BLEDrowSizeEnum.getLeWidthAndHeight(ar);
                leonardoStyleBO.setWidth(ratio[0]);
                leonardoStyleBO.setHeight(ratio[1]);
            }
        }

        List<LeonardoStyleBO.Element> elements = new ArrayList<>();
        List<MJStyleDTO> imgStyles = (List<MJStyleDTO>) imgModelConfigVO.getMjStyles();
        for (MJStyleDTO imgStyle : imgStyles) {
            if (Objects.equals(imgStyle.getKey(), imgDrawDTO.getMjStyleKey())) {
                leonardoStyleBO.setAlchemy(imgStyle.getAlchemy());
                leonardoStyleBO.setPhotoReal(imgStyle.getPhotoReal());
                if (imgStyle.getAkUUID() == null || imgStyle.getAkUUID().isEmpty()) {
                    elements = null;
                } else {
                    float weight = com.business.utils.ImgDrawUtil.getStylizeValue(imgDrawDTO.getMjStylizeValue());
                    String[] akUUIDArray = imgStyle.getAkUUID().split(",");
                    for (String akUUID : akUUIDArray) {
                        elements.add(new LeonardoStyleBO.Element(akUUID, weight));
                    }
                }
                if (imgStyle.getModelId() != null && !imgStyle.getModelId().isEmpty()) {
                    leonardoStyleBO.setModelId(imgStyle.getModelId());
                } else {
                    leonardoStyleBO.setModelId(null);
                }
                if (imgStyle.getPresetStyle() != null && !imgStyle.getPresetStyle().isEmpty()) {
                    leonardoStyleBO.setPresetStyle(imgStyle.getPresetStyle());
                } else {
                    leonardoStyleBO.setPresetStyle(null);
                }
                if (imgStyle.getPhotoReal() != null && imgStyle.getPhotoReal() == true) {
                    leonardoStyleBO.setPhotoRealVersion("v2");
                }
                leonardoStyleBO.setPrompt(leonardoStyleBO.getPrompt().concat((imgStyle.getKeyword() == null ? "" : imgStyle.getKeyword())));
            }
        }
        leonardoStyleBO.setElements(elements);

        imgDrawRecordPO.setWhDivide(whDecide);
        imgDrawRecordPO.setPromptUse(imgDrawRecordPO.getPromptUse().concat(" LE --ar ").concat(ar));

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
        imgDrawHistoryVO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_FOUR.getValue());
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());

        // TODO 扣除用户点子 ===== 查询点子规则数据
        isVip = iUserDDRecordService.getUserIsVip();
        double leDrawDeductQua = BDDUseNumEnum.getDDUseByIsVip(BDDUseNumEnum.LE_DRAW, isVip);
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        ;
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), leDrawDeductQua, flowRecordSub);
        imgDrawRecordPO.setUseDdQua(leDrawDeductQua);

        try {
            String leJobId = LeonardoUtil.postLeonardoTextToImage(leonardoStyleBO, dictConfigMap.get(DictConfigEnum.LE_DRAW_KEY.getDictKey()));
            imgDrawRecordPO.setLeJobId(leJobId);
            if (imgDrawRecordMapper.insert(imgDrawRecordPO) < 0) {
                throw new IBusinessException("绘画记录保存失败");
            }
            return Result.SUCCESS(imgDrawHistoryVO);
        } catch (Exception e) {
            log.error("==++==LE绘图失败= {}", e.getMessage());
            dzRollbackService.rollback(imgDrawRecordPO.getUserId(), leDrawDeductQua, optTitleOne.concat(optTitleTwo != null ? optTitleTwo : ""));
            imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                    .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                    .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
        }
        return Result.ERROR(CommonResultEnum.DRAW_MJ_API_ERROR.getValue());
    }

    private static ImgDrawHistoryVO intiImgDrawHistoryVO(ImgDrawRecordPO imgDrawRecordPO) {
        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
        imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawHistoryVO.setPrompt(imgDrawRecordPO.getPromptInit());
        imgDrawHistoryVO.setPromptUse(imgDrawRecordPO.getPromptUse());
        imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
        imgDrawHistoryVO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        return imgDrawHistoryVO;
    }

    /**
     * 初始化任务
     *
     * @param imgDrawDTO
     * @param imgModelConfigVO
     * @param mjAccountHeaderBO
     * @return
     * @throws IBusinessException
     */
    private ImgDrawRecordPO initImgDrawRecord(ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO, MJAccountHeaderBO mjAccountHeaderBO) throws IBusinessException {
        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.MJ_OPT_ATTRIBUTE_DRAW.getValue());
        imgDrawRecordPO.setModeAttribute(imgModelConfigVO.getAttribute());
        imgDrawRecordPO.setUserId(JwtNewUtil.getUserId());
        imgDrawRecordPO.setPromptInit(imgDrawDTO.getPrompt());
        imgDrawRecordPO.setPromptUse((imgDrawDTO.getPromptUse() == null ? "" : imgDrawDTO.getPromptUse()) + " " + mjAccountHeaderBO.getCompletePrompt());
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(mjAccountHeaderBO));
        return imgDrawRecordPO;
    }

    /**
     * 初始化任务de
     *
     * @param imgDrawDTO
     * @param imgModelConfigVO
     * @return
     * @throws IBusinessException
     */
    private ImgDrawRecordPO initImgDrawRecordDe(ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setId(IdWorker.getId());
        imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.DALLE_OPT_ATTRIBUTE_DRAW.getValue());
        imgDrawRecordPO.setModeAttribute(imgModelConfigVO.getAttribute());
        imgDrawRecordPO.setUserId(JwtNewUtil.getUserId());
        imgDrawRecordPO.setPromptInit(imgDrawDTO.getPrompt());
        imgDrawRecordPO.setPromptUse(imgDrawDTO.getPrompt());
        imgDrawRecordPO.setDescription("dall-e-3 draw " + imgDrawDTO.getPrompt());
        return imgDrawRecordPO;
    }

    /**
     * 初始化任务sd 请勿删除 有用
     *
     * @param imgDrawDTO
     * @param imgModelConfigVO
     * @return
     * @throws IBusinessException
     */
    private ImgDrawRecordPO initImgDrawRecordSd(ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setId(IdWorker.getId());
        // imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.SD_OPT_ATTRIBUTE_DRAW.getValue());
        imgDrawRecordPO.setModeAttribute(imgModelConfigVO.getAttribute());
        imgDrawRecordPO.setUserId(imgDrawDTO.getUserId());
        imgDrawRecordPO.setPromptInit(imgDrawDTO.getPrompt());
        imgDrawRecordPO.setPromptUse(imgDrawDTO.getPromptUse());
        imgDrawRecordPO.setDescription("sd-3 draw " + imgDrawDTO.getPrompt());
        return imgDrawRecordPO;
    }

    /**
     * 校验并发数量：null超出数量，有值为并发数量值
     * 绘图调用1
     * 图片编辑调用2
     */
    private Integer checkConcurrentCount() throws IBusinessException {
        // 查询用户vip记录
        UserDDRecordPO userDDRecordPO = userDDRecordMapper.selectOne(
                new LambdaQueryWrapper<UserDDRecordPO>()
                        .eq(UserDDRecordPO::getUserId, JwtNewUtil.getUserId())
                        .eq(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_PAY.getIntValue())
                        .in(UserDDRecordPO::getTypeItem, UserDDrecordEnum.TYPE_ITEM_PAY_VIP.getIntValue(), UserDDrecordEnum.TYPE_ITEM_PAY_SVIP.getIntValue())
                        .gt(UserDDRecordPO::getExpirationTime, DateUtil.getDateNowShanghai())
                        .orderByDesc(UserDDRecordPO::getExpirationTime)
                        .last("limit 0,1")
        );
        // 校验用户并发数量：设置默认并发数量为1
        int concurrentCount = 1;
        VipGradeBO vipGradeBO = null;
        if (userDDRecordPO != null && Objects.equals(userDDRecordPO.getTypeItem(), UserDDrecordEnum.TYPE_ITEM_PAY_VIP.getIntValue())) {
            vipGradeBO = JSONObject.parseObject((String) RedisUtil.getValue(BRedisKeyEnum.VIP_GRADE_VIP.getKey()), VipGradeBO.class);
        }
        if (userDDRecordPO != null && Objects.equals(userDDRecordPO.getTypeItem(), UserDDrecordEnum.TYPE_ITEM_PAY_SVIP.getIntValue())) {
            vipGradeBO = JSONObject.parseObject((String) RedisUtil.getValue(BRedisKeyEnum.VIP_GRADE_SVIP.getKey()), VipGradeBO.class);
        }
        if (vipGradeBO != null && vipGradeBO.getDrawConcurrency() > 0) {
            concurrentCount = vipGradeBO.getDrawConcurrency();
        }
        // 查询并发数量
        long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getUserId, JwtNewUtil.getUserId())
                        .in(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_QUEUING.getValue(), ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
        );
        if (count >= concurrentCount) {
            return null;
        }
        //校验并发是否存在
        return concurrentCount;
    }

    /**
     * 检查是否为 mj 接口锁是否阻塞，阻塞直接返回错误
     */
    private boolean isNotMJLock() {
        //创建锁：最大重试次数=3；超时释放时间=120秒
        int maxRetryAttempts = 3;
        boolean lockAcquired = false;
        int retryAttempts = 0;
        while (!lockAcquired && retryAttempts <= maxRetryAttempts) {
            lockAcquired = RedisUtil.acquireLock(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_LOCK.getStrKey(), 120);
            if (!lockAcquired) {
                retryAttempts++;
            }
        }
        return !lockAcquired;
    }


    /**
     * 图片编辑
     *
     * @param imgDrawOptDTO 用户参数
     * @return
     */
    @Override
    public Result<Object> submitOpt(ImgDrawOptDTO imgDrawOptDTO) throws IBusinessException {
        // 注意事项：每次新增操作功能时：注意更新 checkKeyIsInvalid 方法内的校验信息
        if (ImgDrawUtil.checkKeyIsInvalid(imgDrawOptDTO.getOperate())) {
            return Result.ERROR("参数无效");
        }
        log.info("===++===imgDrawOptDTO= {}", imgDrawOptDTO);
        //1、校验操作详情是否存在
        ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectById(imgDrawOptDTO.getImgDrawDetlId());
        if (imgDrawDetlPO == null) {
            return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
        }
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectById(imgDrawDetlPO.getDrawRecordId());
        if (imgDrawRecordPO == null) {
            return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
        }

        //校验是否存在可操作权限
        Integer modeAttribute = ImgDrawPUtil.getModeAttributeByOpt(imgDrawDetlPO.getModeAttribute(), imgDrawOptDTO.getOperate());
        //Integer optAttribute = ImgDrawPUtil.getOptAttributeByOpt(imgDrawDetlPO.getModeAttribute(),imgDrawDetlPO.getOptAttribute(),imgDrawOptDTO.getOperate());
        Integer optAttribute = ImgDrawPUtil.getOptAttributeByOptNew(imgDrawDetlPO.getModeAttribute(), imgDrawDetlPO.getOptAttribute(), imgDrawOptDTO.getOperate());
        log.info("校验是否存在可操作权限, modeAttribute= {}, optAttribute= {}", modeAttribute, optAttribute);
        if (modeAttribute == null) {
            return Result.ERROR("暂不支持该操作");
        }
        //2、校验并发数量
        Integer checkConcurrentCount = checkConcurrentCount();
        if (checkConcurrentCount == null) {
            return Result.ERROR("可操作数量已达到上限");
        }
        //无需校验vip，全部走慢速逻辑
        //1、mj属性并且可以进行mj原生操作 校验跳转mj接口执行n5、n6；v5、v6
        if (imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue()
                || imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N6.getValue()
                || imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue()
                || imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue()
                || imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6_1.getValue()
        ) {
            //局部修改、缩放、低变化、高变化、上拓展、下拓展、左拓展、右拓展、方形拓展、放大2倍、放大4倍
            if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_REGION.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_SUBTLE.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_STRONG.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_TOP.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_BOTTOM.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_LEFT.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_RIGHT.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM_MAKE.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_2X.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_4X.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_CHANGE_AR.getValue()
            ) {
                //执行mj原生操作
                return optMj(imgDrawOptDTO, imgDrawRecordPO, imgDrawDetlPO, modeAttribute, optAttribute);
            }
            if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_FACE_FUSION.getValue()) {
                // TODO 进行三方换脸操作：异步调用换脸接口:1:检测人脸; 2:人脸融合  facialFusion
                return optFace(imgDrawOptDTO, imgDrawRecordPO, imgDrawDetlPO, modeAttribute, optAttribute);
            }
            return Result.ERROR("不支持该操作");
        }

        //2、DE模型、变体模型：目前仅支持换脸
        if (imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_DE.getValue()
                || imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_VARIANT.getValue()
        ) {
            if (ImgOptModelEnum.OPERATE_EDIT_FACE_FUSION.getValue() != imgDrawOptDTO.getOperate()) {
                return Result.ERROR("不支持该操作");
            }
            // TODO 进行三方换脸操作：异步调用换脸接口:1:检测人脸; 2:人脸融合
            return optFace(imgDrawOptDTO, imgDrawRecordPO, imgDrawDetlPO, modeAttribute, optAttribute);
        }

        return Result.SUCCESS();
    }

    @Override
    public Result<Object> imgSave(Long imgDrawId) throws IBusinessException {
        //TODO 保存图片：1校验图片是否下载完成，未下载则保存到oss然后返回
        ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectOne(
                new LambdaQueryWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getId, imgDrawId)
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
        if (imgDrawDetlPO == null || ObjectUtil.isEmpty(imgDrawDetlPO) || imgDrawDetlPO.getImgUrl() == null || imgDrawDetlPO.getImgUrl().isEmpty()) {
            return Result.ERROR("图片不存在");
        }
        if (imgDrawDetlPO.getImgUrl().contains("midjourney.com")) {
            String imgBase64 = ImageUtil.getImgUrlToBate64(imgDrawDetlPO.getImgUrl());
            OssParamBO ossParamBO = OSSApis.postOssUrl(imgBase64, CommonUtil.getImageName(imgDrawDetlPO.getImgUrl()), 1);
            if (ossParamBO != null) {
                if (imgDrawDetlMapper.update(null, new LambdaUpdateWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getId, imgDrawDetlPO.getId())
                        .set(ImgDrawDetlPO::getImgUrl, ossParamBO.getImageUrl())
                        .set(ImgDrawDetlPO::getImgWidth, ossParamBO.getImageWidth())
                        .set(ImgDrawDetlPO::getImgHeight, ossParamBO.getImageHeight())
                        .set(ImgDrawDetlPO::getImgSize, ossParamBO.getFileSize())
                        .set(ImgDrawDetlPO::getImgType, "image/webp")) > 0) {
                    return Result.SUCCESS(CommonStrEnum.IMAGE_PREFIX.getValue().concat(ossParamBO.getImageUrl()));
                }
            }
        }
        if (imgDrawDetlPO.getImgUrl().contains("https")) {
            return Result.SUCCESS(imgDrawDetlPO.getImgUrl());
        }
        return Result.SUCCESS(CommonStrEnum.IMAGE_PREFIX.getValue().concat(imgDrawDetlPO.getImgUrl()));
    }

    @Override
    public Result<Object> imgDrawRecordDelete(Long imgDrawId) {
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectOne(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawId)
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
        if (imgDrawRecordPO == null || ObjectUtil.isEmpty(imgDrawRecordPO)) {
            return Result.SUCCESS();
        }
        if (imgDrawRecordMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawId)
                        .set(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
        ) < 1) {
            return Result.ERROR("删除失败");
        }
        List<ImgDrawDetlPO> imgDrawDetlPOList = imgDrawDetlMapper.selectList(
                new LambdaQueryWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawId)
        );

        if (imgDrawDetlPOList == null || imgDrawDetlPOList.isEmpty()) {
            return Result.SUCCESS();
        }
        if (imgDrawDetlMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawId)
                        .set(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())

        ) < 1) {
            return Result.ERROR("删除失败");
        }
        return Result.SUCCESS();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> imgDrawDetlDelete(Long imgDrawDetlId) {
        //查询详情是否存在
        ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectOne(
                new LambdaQueryWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getId, imgDrawDetlId)
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
        if (imgDrawDetlPO == null || ObjectUtil.isEmpty(imgDrawDetlPO)) {
            return Result.SUCCESS();
        }
        if (imgDrawDetlMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawDetlPO::getId, imgDrawDetlId)
                        .set(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
        ) < 1) {
            return Result.ERROR("删除失败");
        }
        //查询任务列表详情是否存在
        List<ImgDrawDetlPO> imgDrawDetlPOList = imgDrawDetlMapper.selectList(
                new LambdaQueryWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawDetlPO.getDrawRecordId())
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
        if (imgDrawDetlPOList != null && !imgDrawDetlPOList.isEmpty()) {
            return Result.SUCCESS();
        }
        if (imgDrawRecordMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawDetlPO.getDrawRecordId())
                        .set(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
        ) < 1) {
            return Result.ERROR("删除失败");
        }
        return Result.SUCCESS();
    }

    private Result<Object> optFace(ImgDrawOptDTO imgDrawOptDTO, ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO, Integer modeAttribute, Integer optAttribute) {
        if (imgDrawDetlPO == null || ObjectUtil.isEmpty(imgDrawDetlPO)) {
            return Result.ERROR("换脸失败，任务不存在");
        }
        HashMap<String, String> map = new HashMap<>();
        map.put("url", CommonStrEnum.IMAGE_PREFIX.getValue() + imgDrawDetlPO.getImgUrl());
        FaceDetectionResponseBodyVO faceResBody = AiFaceUtil.detectionFacial(map);
        if (ObjectUtil.isNotEmpty(faceResBody) || faceResBody.getFace_num() == null || faceResBody.getFace_num() >= 2 || !faceResBody.getIs_face()) {
            return Result.ERROR("当前任务不支持换脸");
        }

        ImgFaceTemplatePO imgFaceTemplatePO = imgFaceTemplateService.getById(imgDrawOptDTO.getFaceTemplateId());
        imgDrawRecordPO.setId(IdWorker.getId());
        imgDrawRecordPO.setOptAttribute(optAttribute);
        imgDrawRecordPO.setModeAttribute(modeAttribute);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(ImgDrawEnum.IMG_NUMBER_ONE.getValue());

        //判断用户点子数是否充足
//        String pointRule = FlowRecordEnum.AIFACES.getRemark();
//        RuleConfigBO ruleConfig = RedisUtil.getCacheObject(CommonConst.SYS_RULE_CONFIG);
//        MjFlowRecordPO flowRecordSub = MjFlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(pointRule).build();
//        //Long orderId = checkBalanService.checkUser(taskPO.getUserId(), ruleConfig.getAiFaceDeductQua(), flowRecordSub);
        MergeFaceRequestBodyDTO mergeFaceReqBodyDTO = new MergeFaceRequestBodyDTO();
        mergeFaceReqBodyDTO.setFaceImageUrl(imgFaceTemplatePO.getImgUrl());
        mergeFaceReqBodyDTO.setImageFileUrl(CommonStrEnum.IMAGE_PREFIX.getValue() + imgDrawDetlPO.getImgUrl());
//        MjFlowRecordPO flowRecordPlus = MjFlowRecordPO.builder().recordType(CommonEnum.COMM_ZERO.getValue()).remark(pointRule).build();
//        long startTime = System.currentTimeMillis();

        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
        imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            // asyncService.asynExecutionFacialFusion(imgDrawRecordPO, mergeFaceReqBodyDTO, imgDrawHistoryVO);
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        return Result.ERROR("换脸失败，请重试");
    }

    private Result<Object> optMj(@NotNull ImgDrawOptDTO imgDrawOptDTO, ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO, Integer modeAttribute, Integer optAttribute) throws IBusinessException {
        JobInfo jobInfo = null;
        MJAccountHeaderBO mjAccountHeaderBO;
        Integer imgDrawRecordPOPdMjIsRelaxed;
        try {
            if (isNotMJLock()) {
                return Result.ERROR("目前人数较多,请稍后再试");
            }
            if (BMJStopUsingUtil.checkAllNot()) {
                return Result.ERROR("模型维护升级中...");
            }
            //默认快速账号，出现问题时自动切换
            //校验账号是否能使用
            imgDrawRecordPOPdMjIsRelaxed = BMJStopUsingUtil.handleSpeed(CommonIntEnum.IS_FALSE.getIntValue());
            if (imgDrawRecordPOPdMjIsRelaxed == null) {
                return Result.ERROR("模型维护升级中...");
            }
            if (imgDrawRecordPOPdMjIsRelaxed.equals(CommonIntEnum.IS_FALSE.getIntValue())) {
                mjAccountHeaderBO = JSONObject.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PRO_ACCOUNT.getStrKey()), MJAccountHeaderBO.class);
            } else {
                mjAccountHeaderBO = JSONObject.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PT_ACCOUNT.getStrKey()), MJAccountHeaderBO.class);
            }
            log.info("imgDrawRecordPO.getDescription= {}", imgDrawRecordPO.getDescription());
            MJAccountHeaderBO mjAccountHeaderBO2 = JSONObject.parseObject(imgDrawRecordPO.getDescription(), MJAccountHeaderBO.class);
            log.info("mjAccountHeaderBO2= {}", mjAccountHeaderBO2);
            mjAccountHeaderBO.setJsonParameter(mjAccountHeaderBO2.getJsonParameter());
            //账号不够用，无法调用mj接口
            if (mjAccountHeaderBO.getToken() == null) {
                return Result.ERROR("目前人数较多,请稍后再试");
            }
            //账号够用，执行mj操作
            try {
                //校验：放大2倍、放大4倍、放大2倍微变化、放大2倍强变化、
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_2X.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_4X.getValue()
                ) {
                    //调用放大接口
                    jobInfo = MJApis.submitJobUpscale(mjAccountHeaderBO2.getJsonParameter(), imgDrawOptDTO.getOperate(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), mjAccountHeaderBO);
                }
                //校验：微变化、强变化
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_SUBTLE.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_STRONG.getValue()
                ) {
                    //调用变化接口
                    jobInfo = MJApis.submitVary(mjAccountHeaderBO2.getJsonParameter(), imgDrawOptDTO.getOperate(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), mjAccountHeaderBO);
                }
                // 校验：上下左右平移
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_TOP.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_BOTTOM.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_LEFT.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_RIGHT.getValue()
                ) {
                    //调用变化接口
                    jobInfo = MJApis.submitPan(mjAccountHeaderBO2.getJsonParameter(), imgDrawOptDTO.getOperate(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), mjAccountHeaderBO);
                }
                // 校验：方形拓展
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM_MAKE.getValue()) {
                    //调用方形拓展接口
                    jobInfo = MJApis.submitSquare(mjAccountHeaderBO2.getJsonParameter(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), mjAccountHeaderBO);
                }
                //校验：局部修改
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_REGION.getValue()) {
                    if (imgDrawOptDTO.getVaryRegionPrompt() == null || imgDrawOptDTO.getVaryRegionPrompt().isEmpty() || imgDrawOptDTO.getVaryRegionMask() == null || imgDrawOptDTO.getVaryRegionMask().isEmpty()) {
                        return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
                    }
                    if (imgDrawOptDTO.getVaryRegionUrl() != null && imgDrawOptDTO.getVaryRegionUrl().getVrAll() != null) {
                        imgDrawOptDTO.setVaryRegionPrompt(imgDrawOptDTO.getVaryRegionPrompt() + imgDrawOptDTO.getVaryRegionUrl().getVrAll());
                    }
                    //调用局部修改接口
                    jobInfo = MJApis.submitVaryRegion(mjAccountHeaderBO2.getJsonParameter(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getVaryRegionPrompt(), imgDrawOptDTO.getVaryRegionMask(), mjAccountHeaderBO);
                }
                //校验：缩放
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM.getValue()) {
                    if (imgDrawOptDTO.getZoomFactorStr() == null || imgDrawOptDTO.getZoomFactorStr().isEmpty()) {
                        return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
                    }
                    //调用缩放接口:1.0 - 2.0 梯度 ：0.1
                    jobInfo = MJApis.submitZoom(mjAccountHeaderBO2.getJsonParameter(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getZoomFactorStr(), mjAccountHeaderBO);
                }
                //校验：zoom自由变化接口：新版
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_CHANGE_AR.getValue()) {
                    if (imgDrawOptDTO.getLocation() == null) {
                        return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
                    }
                    if (imgDrawOptDTO.getZoomFactorStr() == null || imgDrawOptDTO.getZoomFactorStr().isEmpty()) {
                        return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
                    }
                    String oldPrompt = imgDrawRecordPO.getPromptUse();
                    imgDrawRecordPO.setPromptUse(BStringUtil.promptUseMatcher(imgDrawRecordPO.getPromptUse(), imgDrawOptDTO.getZoomFactorStr()));
//                    mjAccountHeaderBO.setCompletePrompt(BStringUtil.promptUseMatcher(mjAccountHeaderBO.getCompletePrompt(), imgDrawOptDTO.getZoomFactorStr()));
//                    System.out.println(mjAccountHeaderBO.getJsonParameter());
                    jobInfo = MJApis.submitJobZoomNew(oldPrompt, mjAccountHeaderBO2.getJsonParameter(), imgDrawOptDTO.getLocation(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getZoomFactorStr(), mjAccountHeaderBO);
                }
            } catch (Exception e) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_UNKNOWN_ERROR.getValue());
            }

            //接口异常
            if (jobInfo == null) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_UNKNOWN_ERROR.getValue());
            }
            //校验：封号异常；token异常；时间失效异常；指令异常
            if (jobInfo.getIsPendingModMessage() || jobInfo.getIsTokenExhausted() || jobInfo.getIsCreditsExhausted() || jobInfo.getIsBannedPromptDetected() || jobInfo.getIsInvalidLink()) {
                return Result.ERROR(mjError(jobInfo, mjAccountHeaderBO));
            }
            // 重置快速账号：并及时释放任务锁
            ImgDrawPUtil.initMjAccountBPRO();
        } finally {
            RedisUtil.releaseLock(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_LOCK.getStrKey());//释放锁
        }
        boolean isVip = iUserDDRecordService.getUserIsVip();
        // TODO 扣除用户点子
        double dzQuantity = BDDUseNumEnum.getOptMJ(imgDrawOptDTO.getOperate(), isVip);
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(optAttribute);
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(optAttribute);
        //MjFlowRecordPO flowRecordSub = MjFlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(ImgOptModelEnum.getOptTitleAll(optAttribute)).build();
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        checkBalanService.checkUser(imgDrawDetlPO.getUserId(), dzQuantity, flowRecordSub);
        try {
            ImgDrawRecordPO imgDrawRecordPOPd = new ImgDrawRecordPO();
            imgDrawRecordPOPd.setUseDdQua(dzQuantity);
            imgDrawRecordPOPd.setOptAttribute(optAttribute);//设置操作属性
            imgDrawRecordPOPd.setModeAttribute(modeAttribute);//设置模型属性
            imgDrawRecordPOPd.setUserId(JwtNewUtil.getUserId());//关联用户
            imgDrawRecordPOPd.setSubmitTime(System.currentTimeMillis());//装载提交时间
            imgDrawRecordPOPd.setSuperId(imgDrawDetlPO.getDrawRecordId());//装载上级任务id
            imgDrawRecordPOPd.setOriginalImgId(imgDrawOptDTO.getImgDrawDetlId());
            if (imgDrawOptDTO.getVaryRegionPrompt() != null && StringUtils.isNotEmpty(imgDrawOptDTO.getVaryRegionPrompt())) {
                String varyRegionPrompt = "";
                String regionPrompt = imgDrawRecordPO.getPromptUse() == null ? "" : imgDrawRecordPO.getPromptUse();
                int indexOfDoubleDash = regionPrompt.indexOf("--");
                if (indexOfDoubleDash != -1) {
                    varyRegionPrompt = regionPrompt.substring(indexOfDoubleDash);
                }
                imgDrawRecordPOPd.setPromptInit(removeIwDirective(imgDrawOptDTO.getVaryRegionPrompt()));
                imgDrawRecordPOPd.setPromptUse(imgDrawOptDTO.getVaryRegionPrompt() + " " + varyRegionPrompt);
            } else {
                imgDrawRecordPOPd.setPromptInit(imgDrawRecordPO.getPromptInit());
                imgDrawRecordPOPd.setPromptUse(imgDrawRecordPO.getPromptUse());
            }
            if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM.getValue() || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_CHANGE_AR.getValue()) {
                imgDrawRecordPOPd.setOptDescribe(imgDrawOptDTO.getZoomFactorStr());
            }
            imgDrawRecordPOPd.setDescription(JSONObject.toJSONString(mjAccountHeaderBO));

            // TODO 2 存在：说明账号有闲置并发数量，直接创建进行中任务：需要进行测试
            imgDrawRecordPOPd.setStartTime(System.currentTimeMillis());//装载开始时间
            imgDrawRecordPOPd.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
            imgDrawRecordPOPd.setMjJobId(jobInfo.getJob_id());//装载任务id
            imgDrawRecordPOPd.setMjIsRelaxed(imgDrawRecordPOPdMjIsRelaxed);
            imgDrawRecordPOPd.setMjAccountId(mjAccountHeaderBO.getMjAccountId());
            //装载宽高尺寸：mj接口返回的宽高尺寸
            imgDrawRecordPOPd.setWidth(jobInfo.getWidth());
            imgDrawRecordPOPd.setHeight(jobInfo.getHeight());
            imgDrawRecordPOPd.setImgQuantity(jobInfo.getBatch_size());
            imgDrawRecordPOPd.setFinalPrompt(jobInfo.getFull_command());
            imgDrawRecordPOPd.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());
            imgDrawRecordPOPd.setWhDivide(ImgDrawUtil.getWhDivide(jobInfo.getWidth(), jobInfo.getHeight()));
            //创建进行中任务成功
            if (imgDrawRecordMapper.insert(imgDrawRecordPOPd) > 0) {
                ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
                if (jobInfo.getBatch_size() > 0) {
                    imgDrawHistoryVO.setId(imgDrawRecordPOPd.getId());
                    imgDrawHistoryVO.setUserId(imgDrawRecordPOPd.getUserId());
                    imgDrawHistoryVO.setModeAttribute(imgDrawRecordPOPd.getModeAttribute());
                    imgDrawHistoryVO.setOptAttribute(imgDrawRecordPOPd.getOptAttribute());
                    imgDrawHistoryVO.setPrompt(imgDrawRecordPOPd.getPromptInit());
                    imgDrawHistoryVO.setPromptUse(imgDrawRecordPOPd.getPromptUse());
                    imgDrawHistoryVO.setWhDivide(imgDrawRecordPOPd.getWhDivide());
                    imgDrawHistoryVO.setStatus(imgDrawRecordPOPd.getStatus());
                    imgDrawHistoryVO.setImgQuantity(jobInfo.getBatch_size());
                    List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();
                    for (int i = 0; i < jobInfo.getBatch_size(); i++) {
                        ImgDrawDetlVO imgDrawDetlVO = new ImgDrawDetlVO();
                        imgDrawDetlVO.setDrawRecordId(imgDrawRecordPOPd.getId());
                        imgDrawDetlVO.setOptAttribute(imgDrawRecordPOPd.getOptAttribute());
                        imgDrawDetlVO.setImgIndex(i);
                        imgDrawDetlVO.setWhDivide(ImgDrawUtil.getWhDivide(jobInfo.getWidth(), jobInfo.getHeight()));
                        imgDrawDetlVO.setImgWidth(jobInfo.getWidth());
                        imgDrawDetlVO.setImgHeight(jobInfo.getHeight());
                        imgDrawDetlVOS.add(imgDrawDetlVO);
                    }
                    imgDrawHistoryVO.setImgDrawDetls(imgDrawDetlVOS);
                    log.info("操作任务：{}，任务id：{}", imgDrawRecordPOPd, imgDrawRecordPOPd.getId());
                    //缓存任务有效期为一天
                    RedisUtil.setValueSeconds(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_JOB_ID.getStrKey(), imgDrawRecordPOPd.getMjJobId()), JSONObject.toJSONString(imgDrawHistoryVO), 1, TimeUnit.DAYS);
                    //异步渐显推送
                    MJAPIUtil.sendHandshakeRequest(imgDrawRecordPOPd.getMjJobId(), new MjWebSocketListener(imgDrawRecordPOPd.getMjJobId(), redisServer));
                }
                return Result.SUCCESS(imgDrawHistoryVO);
            }
        } catch (Exception e) {
            log.error("操作时异常错误 {}", e.getMessage(), e);//释放锁
        }
        return Result.ERROR(CommonResultEnum.DRAW_MJ_API_ERROR.getValue());
    }

    public static String removeIwDirective(String input) {
        return input.replaceAll("\\s--iw\\s\\d+(\\.\\d+)?", "");
    }

    private String mjError(JobInfo jobInfo, MJAccountHeaderBO mjAccountHeaderBO) {
        //1 提示词问题：提示词不符合社区标准
        if (jobInfo.getIsBannedPromptDetected()) {
            return CommonResultEnum.DRAW_MJ_API_BANNED_PROMPT_DETECTED.getValue();
        }
        //1 垫图链接无效：
        if (jobInfo.getIsInvalidLink()) {
            return CommonResultEnum.DRAW_MJ_API_INVALID_LINK.getValue();
        }

        //2 快速时间用完：清除快速账号；重新排序
        if (jobInfo.getIsCreditsExhausted()) {
            ImgDrawPUtil.resetMjAccount(mjAccountHeaderBO);//重置账号
            adminMjAccountConfigMapper.update(
                    null,
                    new LambdaUpdateWrapper<AdminMjAccountConfigPO>()
                            .eq(AdminMjAccountConfigPO::getId, mjAccountHeaderBO.getMjAccountId())
                            .set(AdminMjAccountConfigPO::getPeriodCredits, 0)
            );
            return CommonResultEnum.DRAW_MJ_API_ERROR.getValue();
        }
        //3 token失效问题：清除缓存账号；重新排序缓存账号；变更状态为异常，等待监听处理
        if (jobInfo.getIsTokenExhausted()) {
            ImgDrawPUtil.resetMjAccount(mjAccountHeaderBO);//重置账号
            adminMjAccountConfigMapper.update(
                    null,
                    new LambdaUpdateWrapper<AdminMjAccountConfigPO>()
                            .eq(AdminMjAccountConfigPO::getId, mjAccountHeaderBO.getMjAccountId())
                            .set(AdminMjAccountConfigPO::getDeleted, CommonIntEnum.IS_TRUE.getIntValue())
            );
            String jsonStr = JSONObject.toJSONString(mjAccountHeaderBO);
            log.error("执行重置账号信息：{}", jsonStr);
            BFeiShuUtil.sedCardErrorFromDraw(BFeiShuUtil.P1, "MJ提交绘图403错误", jsonStr, false, "自动关闭账号,人工处理");
            return CommonResultEnum.DRAW_MJ_API_ERROR.getValue();
        }

        //4 账号封号：清除缓存账号；重新排序缓存账号；变更状态为禁用，等待监听处理
        if (jobInfo.getIsPendingModMessage()) {
            ImgDrawPUtil.resetMjAccount(mjAccountHeaderBO);//重置账号
            adminMjAccountConfigMapper.update(
                    null,
                    new LambdaUpdateWrapper<AdminMjAccountConfigPO>()
                            .eq(AdminMjAccountConfigPO::getId, mjAccountHeaderBO.getMjAccountId())
                            .set(AdminMjAccountConfigPO::getIsUse, CommonIntEnum.ADMIN_IS_USE_FALSE.getIntValue())
            );
            return CommonResultEnum.DRAW_MJ_API_ERROR.getValue();
        }
        return CommonResultEnum.DRAW_MJ_API_ERROR.getValue();
    }


    @Override
    public void statusQueuingTasks() {
        //TODO 排队中任务：暂时停用
        // 排队中的任务进行处理
        Long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_QUEUING.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
        );
        log.info("排队中任务数量：{}", count);
        if (count < 1) {
            return;
        }

        // TODO 1.排队中任务执行;查询排队中任务执行结果（查询最早的8条排队中的数据：1秒执行8条）
        List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper.selectList(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_QUEUING.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
                        .ne(ImgDrawRecordPO::getMjJobId, null)
                        .ne(ImgDrawRecordPO::getMjJobId, "")
                        .orderByAsc(ImgDrawRecordPO::getCreateTime)
                        .last("limit 0,8")
        );
        //校验不存在任务直接返回
        if (imgDrawRecordPOList == null || imgDrawRecordPOList.isEmpty()) {
            return;
        }
    }

    //监听并处理进行中的mj绘图任务信息
    @Override
    public void statusInProgressTasks() {
        // 1、获取进行中的任务数量
        Long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .isNotNull(ImgDrawRecordPO::getMjJobId)//仅操作mj类型的任务
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
        );
        if (count < 1) {
            return;
        }
        log.info("绘图进行中任务数量：{}", count);
        try {
            // 2、获取进行中的任务信息列表
            List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper.selectList(
                    new LambdaQueryWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                            .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                            .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
                            .isNotNull(ImgDrawRecordPO::getMjJobId)
                            .orderByAsc(ImgDrawRecordPO::getCreateTime)
            );
            if (imgDrawRecordPOList == null || imgDrawRecordPOList.isEmpty()) {
                return;
            }
            List<String> jobIds = new ArrayList<>();
            // 3、装载jobIds 用户提交mj app api进行查询相关信息
            imgDrawRecordPOList.forEach(imgDrawRecordPO -> {
                if (imgDrawRecordPO.getMjJobId() != null && !imgDrawRecordPO.getMjJobId().isEmpty()) {
                    jobIds.add(imgDrawRecordPO.getMjJobId());
                }
            });
            if (jobIds.isEmpty()) {// 装载失败返回数据
                return;
            }
            log.info("需要进行查询的jobIds列表信息：{}", jobIds);
            MJAccountHeaderBO mjAccountHeaderBO = JSONObject.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PRO_ACCOUNT.getStrKey()), MJAccountHeaderBO.class);
            if (mjAccountHeaderBO == null || mjAccountHeaderBO.getToken() == null) {
                List<MJAccountBO> mjAccountBOList = JSONArray.parseArray(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PRO_ACCOUNT_LIST.getStrKey()), MJAccountBO.class);
                for (MJAccountBO mjAccountBO : mjAccountBOList) {
                    if (mjAccountBO.getAppToken() != null) {
                        mjAccountHeaderBO = new MJAccountHeaderBO();
                        mjAccountHeaderBO.setAppVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_APP_VERSION.getStrKey()));
                        mjAccountHeaderBO.setUserAgentVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_USER_AGENT.getStrKey()));
                        mjAccountHeaderBO.setToken(mjAccountBO.getAppToken());
                        mjAccountHeaderBO.setCookie(mjAccountBO.getAppCookies());
                        break;
                    }
                }
            }
            if (mjAccountHeaderBO == null || mjAccountHeaderBO.getToken() == null) {
                log.error("重大问题：获取账号信息失败===================>");
                return;
            }
            List<JobStatusBO> jobStatusBOList;
            try {
                Result<List<JobStatusBO>> result = MJApis.getJobStatus(jobIds, mjAccountHeaderBO);
                if (result.getStatus() == 403) {
                    ImgDrawPUtil.resetMjAccount(mjAccountHeaderBO);//重置账号
                    adminMjAccountConfigMapper.update(
                            null,
                            new LambdaUpdateWrapper<AdminMjAccountConfigPO>()
                                    .eq(AdminMjAccountConfigPO::getId, mjAccountHeaderBO.getMjAccountId())
                                    .set(AdminMjAccountConfigPO::getDeleted, CommonIntEnum.IS_TRUE.getIntValue())
                    );
                    String jsonString = JSONObject.toJSONString(mjAccountHeaderBO);
                    log.error("执行重置账号信息：{}", jsonString);
                    BFeiShuUtil.sedCardErrorFromDraw(BFeiShuUtil.P1, "获取mj-cookie失败", jsonString, false, "停用mj账号需手动处理");
                }
                jobStatusBOList = result.getData();
            } catch (Exception e) {
                log.error("拉取mj-api-绘图任务信息失败,{}", e.getMessage());
                return;
            }
            if (jobStatusBOList == null || jobStatusBOList.isEmpty()) {
                return;
            }
            // 4、推送任务状态信息
            for (JobStatusBO jobStatusBO : jobStatusBOList) {
                try {
                    //任务成功完成进行定时任务推送
                    if (jobStatusBO != null && ImgDrawUtil.getIsCommitted(jobStatusBO.getCurrentStatus())) {
                        // TODO 替换
                        boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(jobStatusBO.getJobId(), BMessageSendEnum.DRAW_JOB_PUSH, jobStatusBO));
                        log.info("任务推送消息状态: {}", state ? "成功" : "失败");
                        continue;
                    }
                    log.info("任务进行中无需进行推送 {}", jobStatusBO);
                    //任务进行中，跳过
                } catch (Exception e) {
                    log.error("任务推送消息失败: {}", e.getMessage(), e);
                }
            }
        } finally {
        }
    }

    @Override
    public void listenDalleDrawTimeOutFail() {
        List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper.selectList(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .lt(ImgDrawRecordPO::getCreateTime, DateUtil.getDateSubdivisionMinutes(BDateUtil.getDateNowShanghai(), com.nacos.constant.CommonConst.TASK_TIMEOUT_MINUTES_DALLE))
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
//                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
                        .isNull(ImgDrawRecordPO::getMjJobId)
//                        .isNull(ImgDrawRecordPO::getMjAccountId)
                        .orderByAsc(ImgDrawRecordPO::getCreateTime)
                        .last("limit 0,20")
        );
        if (imgDrawRecordPOList == null || imgDrawRecordPOList.isEmpty()) {
            return;
        }
        //未获取到锁，直接返回
        if (!getDrawTimeOutFailLock()) {
            return;
        }

        //1、查询未完成并且时间超过30分钟的绘图任务列表，统一按照失败进行处理
        try {
            for (ImgDrawRecordPO imgDrawRecordPO : imgDrawRecordPOList) {
//                double deDrawDeductQua;
//                if (Double.compare(imgDrawRecordPO.getWhDivide(), 1) == 0){
//                    deDrawDeductQua = DDUseRuleEnum.getDDUseRuleQuality(DDUseRuleEnum.DRAW_DE_SIZE_ONE);//dalle-3 扣除点点数量2
//                } else {
//                    deDrawDeductQua = DDUseRuleEnum.getDDUseRuleQuality(DDUseRuleEnum.DRAW_DE_SIZE_TWO);//dalle-3 扣除点点数量3
//                }
                double deDrawDeductQua = imgDrawRecordPO.getUseDdQua();
                ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
                imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_FAIL.getValue());

                asyncService.handleFailedTask(imgDrawRecordPO.getUserId(),
                        deDrawDeductQua,
                        ImgOptModelEnum.getOptTitleAll(imgDrawRecordPO.getOptAttribute()),
                        imgDrawHistoryVO,
                        "目前人数较多，请重试"
                );

            }
        } finally {
            RedisUtil.releaseLock(GlobalRedisKeyEnum.TASK_LOCK_REDIS_MJ_DRAW_FAIL_HANDLE.getStrKey());//释放锁
        }
    }

    private boolean getDrawTimeOutFailLock() {
        //创建锁：最大重试次数=3；超时释放时间=60秒
        int maxRetryAttempts = 3;
        boolean lockAcquired = false;
        int retryAttempts = 0;
        while (!lockAcquired && retryAttempts <= maxRetryAttempts) {
            lockAcquired = RedisUtil.acquireLock(GlobalRedisKeyEnum.TASK_LOCK_REDIS_MJ_DRAW_FAIL_HANDLE.getStrKey(), 60);
            if (!lockAcquired) {
                retryAttempts++;
            }
        }
        return lockAcquired;
    }

    //TODO .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setFailAndFhdd(ImgDrawRecordPO imgDrawRecordPO) {
        try {
            if (imgDrawRecordMapper.update(
                    null,
                    new LambdaUpdateWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                            .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
            ) < 1) {
                return;
            }
            //2、返回用户点子消耗数量
            boolean result = checkService.setReturnDD(imgDrawRecordPO.getUserId(), imgDrawRecordPO.getUseDdQua());
            if (!result) {
                log.error("返回用户点子消耗数量失败:手动回滚1");
                throw new RuntimeException();
            }
            FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ZERO.getValue()).remark(ImgOptModelEnum.getOptTitleAll(imgDrawRecordPO.getOptAttribute())).build();
            flowRecordSub.setUserId(imgDrawRecordPO.getUserId());
            flowRecordSub.setNum(imgDrawRecordPO.getUseDdQua());
            if (flowRecordMapper.insert(flowRecordSub) < 1) {
                log.error("返回用户点子消耗数量失败:手动回滚2");
                throw new RuntimeException();
            }

            // TODO 推送用户绘画失败信息=================？缺少一个
            JobStatusBO jobStatusBO = new JobStatusBO();
            jobStatusBO.setUserId(String.valueOf(imgDrawRecordPO.getUserId()));
            jobStatusBO.setJobId(imgDrawRecordPO.getMjJobId());
            jobStatusBO.setCurrentStatus("FAILURE");
            jobStatusBO.setFullCommand(imgDrawRecordPO.getPromptUse());
            jobStatusBO.setJobType(String.valueOf(imgDrawRecordPO.getFunType()));
            jobStatusBO.setBatchSize(imgDrawRecordPO.getImgQuantity());
            jobStatusBO.setEventWidth(imgDrawRecordPO.getWidth());
            jobStatusBO.setEventHeight(imgDrawRecordPO.getHeight());
            boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(jobStatusBO.getJobId(), BMessageSendEnum.DRAW_JOB_PUSH, jobStatusBO));
            log.info("任务失败推送消息:{}", state);


            //停止渐显的任务
            MJAPIUtil.getWebForithmClientMapShutdown(imgDrawRecordPO.getMjJobId());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }


    //获取个人写真任务结果
    @Override
    public void fetchFaceSwapTaskResult() {
        List<ImgDrawRecordPO> imgDrawRecordPOS = imgDrawRecordMapper.selectList(new LambdaQueryWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_PORTRAIT.getValue())
                .notIn(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue(), ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
                .isNotNull(ImgDrawRecordPO::getGoTaskId)
                .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
        imgDrawRecordPOS.stream()
                .filter(imgDrawRecordPO -> StringUtils.isNotBlank(imgDrawRecordPO.getGoTaskId()))
                .forEach(imgDrawRecordPO -> processImgDrawRecord(imgDrawRecordPO));
    }

    // 处理单个 ImgDrawRecordPO 的方法
    private void processImgDrawRecord(ImgDrawRecordPO imgDrawRecordPO) {
        try {
            String response = GoApiUtil.fetchFaceSwapResult(imgDrawRecordPO.getGoTaskId());
            if (StringUtils.isNotBlank(response)) {
                FaceSwapVO faceSwapVO = JSONObject.parseObject(response, FaceSwapVO.class);
                if (faceSwapVO.getCode() == 200) {
                    FaceSwapVO.ImageData imageData = faceSwapVO.getData();
                    String status = imageData.getStatus();
                    String image = imageData.getImage();
                    if (CurrentStatusEnum.SUCCESS.getStatus().equals(status)
                            && StringUtils.isNotBlank(image)) {
                        //TODO 处理成功的逻辑
                        String result = this.uploadImageToOSSPhoto(imgDrawRecordPO, imageData.getImage(), 9);
                        if (result == null) {
                            imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_FAILURE.getStatusName());
                            taskFailureProcessing(imgDrawRecordPO);
                        } else {
                            imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());
                            imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_SUCCESS.getStatusName());
                            updatePhotoTaskStatus(imgDrawRecordPO);
                            photoTaskFinished(imgDrawRecordPO);
                        }
                    } else if (CurrentStatusEnum.FAILED.getStatus().equals(status)) {
                        //TODO 处理失败的逻辑
                        imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_FAILURE.getStatusName());
                        taskFailureProcessing(imgDrawRecordPO);
                    }
                    log.info("====faceSwapVO= {}", faceSwapVO);
                } else {
                    imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_FAILURE.getStatusName());
                    taskFailureProcessing(imgDrawRecordPO);
                }
            }
        } catch (InterruptedException e) {
            imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_FAILURE.getStatusName());
            taskFailureProcessing(imgDrawRecordPO);
            throw new RuntimeException(e);
        }
    }

    @Transactional
    public void updatePhotoTaskStatus(ImgDrawRecordPO imgDrawRecordPO) {
        imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                .set(ImgDrawRecordPO::getStatus, imgDrawRecordPO.getStatus())
                .set(ImgDrawRecordPO::getFailReason, imgDrawRecordPO.getRemark()));
    }

    public void photoTaskFinished(ImgDrawRecordPO imgDrawRecordPO) {
        try {
            String notificationMessage = FlowRecordEnum.PHOTO.getRemark() + imgDrawRecordPO.getRemark();
            SysNotificationPO notificationPO = SysNotificationPO.buildSysNotification(
                    imgDrawRecordPO.getUserId(),
                    Integer.valueOf(BNotificationEnum.PHOTO_NOTIF.getIntValue()), //任务完成推送
                    notificationMessage,
                    imgDrawRecordPO.getPromptInit(), 1, imgDrawRecordPO.getId()
            );

            boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(imgDrawRecordPO.getUserId(), BMessageSendEnum.PHOTO_PUSH, JSONObject.toJSONString(notificationPO)));
            log.info("写真消息推送状态:{}", state);
        } catch (Exception e) {
            log.error("taskFinished= {}", e.getMessage(), e);
        }
    }

    private String uploadImageToOSSPhoto(ImgDrawRecordPO imgDrawRecordPO, String imgFile, Integer folder) {
        try {
            long startTime = System.currentTimeMillis();
            OssParamBO ossParamBO = AliOSSUtils.uploadBase64(imgFile, folder);
            long endTime = System.currentTimeMillis();
            System.out.println("图片存储耗时时间= " + (endTime - startTime) + " 毫秒");
            if (StringUtils.isNotEmpty(ossParamBO.getImageUrl())) {
                Double whDivide = ImgDrawUtil.getWhDivide(Integer.valueOf(ossParamBO.getImageWidth()), Integer.valueOf(ossParamBO.getImageHeight()));

                if (imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getWidth, ossParamBO.getImageWidth())
                        .set(ImgDrawRecordPO::getHeight, ossParamBO.getImageHeight())
                        .set(ImgDrawRecordPO::getWhDivide, whDivide)
                        .set(ImgDrawRecordPO::getFinishTime, System.currentTimeMillis())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue())) > 0
                ) {
                    ImgDrawDetlPO imgDrawDetlPO = ImgDrawDetlPO.buildImgDrawDetlPO(imgDrawRecordPO.getId(), imgDrawRecordPO.getOptAttribute(), imgDrawRecordPO.getModeAttribute(), imgDrawRecordPO.getUserId(),
                            0, ossParamBO.getImageUrl(), whDivide,
                            Long.valueOf(ossParamBO.getFileSize()), Integer.valueOf(ossParamBO.getImageWidth()), Integer.valueOf(ossParamBO.getImageHeight()),
                            "webp", null);
                    imgDrawDetlPO.setImgUrl(getAddresByUrl(ossParamBO.getImageUrl()));
                    imgDrawDetlPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
                    imgDrawDetlPO.setIsSave(CommonIntEnum.SHOW_FALSE.getIntValue());
                    imgDrawDetlPO.setIsOpen(CommonIntEnum.SHOW_FALSE.getIntValue());
                    imgDrawDetlMapper.insert(imgDrawDetlPO);
                    return ossParamBO.getImageUrl();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("uploadBase64ToOSS Exception error: {}, {}", e.getMessage(), e);
            return null;
        } catch (Throwable e) {
            log.error("uploadBase64ToOSS Throwable error: {}, {}", e.getMessage(), e);
            return null;
        }

    }

    private String getAddresByUrl(String imgUrl) {
        try {
            return new URI(imgUrl).getPath();
        } catch (URISyntaxException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void taskFailureProcessing(ImgDrawRecordPO imgDrawRecordPO) {
        FlowRecordPO flowRecordPlus = FlowRecordPO.builder().recordType(CommonEnum.COMM_ZERO.getValue()).remark(FlowRecordEnum.PHOTO.getRemark()).build();
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_FINISH_FAIL.getValue());
        updatePhotoTaskStatus(imgDrawRecordPO);
        photoTaskFinished(imgDrawRecordPO);
        asyncService.updateRemainingTimes(
                imgDrawRecordPO.getUserId(),
                DDUseRuleEnum.getDDUseRuleQuality(DDUseRuleEnum.DRAW_AI_PORTRAIT),
                flowRecordPlus,
                null
        );
    }

    //取消任务
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Result<Object> cancelJob(Long imgDrawId) throws IBusinessException {
        boolean isNotMJLock = false;
        if (isNotMJLock) {
            return Result.ERROR("任务进行中不能取消");
        }

        // 1、获取进行中的任务数量
        Long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawId));
        if (count < 1) {
            return Result.ERROR("任务不存在");
        }
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectById(imgDrawId);
        List<String> jobIds = new ArrayList<>();
        jobIds.add(imgDrawRecordPO.getMjJobId());
        log.info("手动取消需要进行查询的jobIds列表信息：{}", jobIds);

        MJAccountHeaderBO mjAccountHeaderBO = JSONObject.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PRO_ACCOUNT.getStrKey()), MJAccountHeaderBO.class);
        if (mjAccountHeaderBO == null || mjAccountHeaderBO.getToken() == null) {
            List<MJAccountBO> mjAccountBOList = JSONArray.parseArray(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PRO_ACCOUNT_LIST.getStrKey()), MJAccountBO.class);
            for (MJAccountBO mjAccountBO : mjAccountBOList) {
                if (mjAccountBO.getAppToken() != null) {
                    mjAccountHeaderBO = new MJAccountHeaderBO();
                    mjAccountHeaderBO.setAppVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_APP_VERSION.getStrKey()));
                    mjAccountHeaderBO.setUserAgentVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_USER_AGENT.getStrKey()));
                    mjAccountHeaderBO.setToken(mjAccountBO.getAppToken());
                    mjAccountHeaderBO.setCookie(mjAccountBO.getAppCookies());
                    break;
                }
            }
        }
        if (mjAccountHeaderBO == null || mjAccountHeaderBO.getToken() == null) {
            log.error("重大问题：获取账号信息失败===================>");
            return Result.ERROR("任务进行中，不能取消");
        }
        List<JobStatusBO> jobStatusBOList;
        try {
            Result<List<JobStatusBO>> result = MJApis.getJobStatus(jobIds, mjAccountHeaderBO);
            if (result.getStatus() == 403) {
                log.error("手动取消任务执行重置账号信息：{}", mjAccountHeaderBO);
                BFeiShuUtil.sedCardErrorFromDraw(BFeiShuUtil.P1, "手动取消MJ任务-拉取绘图任务", mjAccountHeaderBO.toString(), true, "403");
            }
            jobStatusBOList = result.getData();
        } catch (Exception e) {
            log.error("手动取消MJ任务-拉取mj-api-绘图任务信息失败", e);
            return Result.ERROR(CommonResultEnum.JOB_CANCEL_ONGOING_ERROR.getValue());
        }
        if (jobStatusBOList == null || jobStatusBOList.size() == 0) {
            return Result.ERROR(CommonResultEnum.JOB_CANCEL_ONGOING_ERROR.getValue());
        }
        JobStatusBO jobStatusBO = jobStatusBOList.get(0);
        //任务成功完成进行定时任务推送
        if (jobStatusBO != null && ImgDrawUtil.getIsCommitted(jobStatusBO.getCurrentStatus())) {
            return Result.ERROR(CommonResultEnum.JOB_CANCEL_ONGOING_ERROR.getValue());
        }

        // TODO 取消任务
        if (ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue() == imgDrawRecordPO.getModeAttribute()
                || ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N6.getValue() == imgDrawRecordPO.getModeAttribute()
                || ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue() == imgDrawRecordPO.getModeAttribute()
                || ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue() == imgDrawRecordPO.getModeAttribute()
                || ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6_1.getValue() == imgDrawRecordPO.getModeAttribute()
        ) {
            AdminMjAccountConfigPO adminMjAccountConfigPO = adminMjAccountConfigMapper.selectById(imgDrawRecordPO.getMjAccountId());
            if (adminMjAccountConfigPO == null) {
                return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
            }
            //TODO mj任务取消逻辑处理
            mjAccountHeaderBO.setToken(adminMjAccountConfigPO.getAppToken());

            if (MJApis.getMJJobsCancel(mjAccountHeaderBO, imgDrawRecordPO.getMjJobId())) {
                cancelJobRollback(imgDrawRecordPO); //回退点子公共方法
                RedisUtil.removeKey(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_JOB_ID.getStrKey(), imgDrawRecordPO.getMjJobId()));
                return Result.SUCCESS("任务取消成功");
            }
            return Result.ERROR(CommonResultEnum.JOB_CANCEL_FAIL_ERROR.getValue());
        }
        if (ImgOptModelEnum.DRAW_ATTRIBUTE_DE.getValue() == imgDrawRecordPO.getModeAttribute()) {
            //TODO dalle任务取消逻辑处理
            cancelJobRollback(imgDrawRecordPO); //回退点子公共方法
            return Result.SUCCESS(true);
        }
        return Result.ERROR(CommonResultEnum.JOB_CANCEL_FAIL_ERROR.getValue());
    }

    //TODO 客户手动取消任务退点子公共方法
    private void cancelJobRollback(ImgDrawRecordPO imgDrawRecordPO) throws IBusinessException {
        if (imgDrawRecordMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
                        .set(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
        ) < 1) {
            log.error("保存客户手动取消任务失败");
            throw new RuntimeException();
        }

        //2、返回用户点子消耗数量
        boolean result = checkService.setReturnDD(imgDrawRecordPO.getUserId(), imgDrawRecordPO.getUseDdQua());
        if (!result) {
            log.error("返回用户点子消耗数量失败:客户手动取消任务失败！");
            throw new RuntimeException();
        }

        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ZERO.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        flowRecordSub.setUserId(imgDrawRecordPO.getUserId());
        flowRecordSub.setNum(imgDrawRecordPO.getUseDdQua());
        if (flowRecordMapper.insert(flowRecordSub) < 1) {
            log.error("返回用户点子消耗数量失败:手动回滚2");
            throw new RuntimeException();
        }
    }

    //TODO 监听mj任务 超过30分钟自动取任务
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void listenDrawTimeOutCancelJob() {
        List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper.selectList(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .lt(ImgDrawRecordPO::getCreateTime, DateUtil.getDateSubdivisionMinutes(BDateUtil.getDateNowShanghai(), com.nacos.constant.CommonConst.TASK_TIMEOUT_MINUTES))
                        .in(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue(), ImgDrawEnum.STATUS_QUEUING.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
                        .isNotNull(ImgDrawRecordPO::getMjJobId)
                        .orderByAsc(ImgDrawRecordPO::getCreateTime)
                        .last("limit 0,10")
        );
        if (imgDrawRecordPOList == null || imgDrawRecordPOList.isEmpty()) {
            return;
        }
        log.info("监听任务超时任务列表:{}", imgDrawRecordPOList);
        //未获取到锁，直接返回
        if (!getDrawTimeOutFailLock()) {
            return;
        }
        imgDrawRecordPOList.forEach(imgDrawRecordPO -> {
            try {
                cancelJobRollback(imgDrawRecordPO); //回退点子公共方法
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

       /* AdminMjAccountConfigPO adminMjAccountConfigPO = adminMjAccountConfigMapper.selectById(imgDrawRecordPO.getMjAccountId());
        if (adminMjAccountConfigPO == null) {
            return;
        }*/
        //TODO mj任务取消逻辑处理 == 查询未完成并且时间超过30分钟的绘图任务列表，统一按照失败进行处理
       /* MJAccountHeaderBO mjAccountHeaderBO = new MJAccountHeaderBO();
        mjAccountHeaderBO.setToken(adminMjAccountConfigPO.getAppToken());
        mjAccountHeaderBO.setAppVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_APP_VERSION.getStrKey()));
        mjAccountHeaderBO.setUserAgentVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_USER_AGENT.getStrKey()));
        mjAccountHeaderBO.setCookie(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_COOKIE.getStrKey()));*/
        try {
            //if (MJApis.getMJJobsCancel(mjAccountHeaderBO, imgDrawRecordPO.getMjJobId())) {
            // TODO 推送用户绘画失败信息=================？缺少一个
               /* JobStatusBO jobStatusBO = new JobStatusBO();
                jobStatusBO.setUserId(String.valueOf(imgDrawRecordPO.getUserId()));
                jobStatusBO.setJobId(imgDrawRecordPO.getMjJobId());
                jobStatusBO.setCurrentStatus("FAILURE");
                jobStatusBO.setFullCommand(imgDrawRecordPO.getPromptUse());
                jobStatusBO.setJobType(String.valueOf(imgDrawRecordPO.getFunType()));
                jobStatusBO.setBatchSize(imgDrawRecordPO.getImgQuantity());
                jobStatusBO.setEventWidth(imgDrawRecordPO.getWidth());
                jobStatusBO.setEventHeight(imgDrawRecordPO.getHeight());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("type", WebSocketEnum.DRAW_JOB_PUSH.getPushType());
                jsonObject.put("userId", jobStatusBO.getJobId());
                jsonObject.put("object", JSONObject.toJSONString(jobStatusBO));
                String state = redisServer.sendMsg(jsonObject.toString());
                log.info("任务失败推送消息:{}", state);*/
            //  }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            //RedisUtil.releaseLock(GlobalRedisKeyEnum.TASK_LOCK_REDIS_MJ_DRAW_FAIL_HANDLE.getStrKey());//释放锁
        }
    }

    @Override
    public Result<Object> getAspectRatioScale() {
        List<MjScaleVO> mjScaleList = new LinkedList<>();
        MJApis.getScaleList().forEach(scale -> {
            String[] whDivides = scale.split(":");
            mjScaleList.add(MjScaleVO.builder().scale(scale).whDivide(ImgDrawUtil.getWhDivide(Integer.parseInt(whDivides[0]), Integer.parseInt(whDivides[1]))).build());
        });
        Map<String, Object> mjScaleMap = new HashMap<>();
        mjScaleMap.put("defaultIndex", 6);
        mjScaleMap.put("mjScaleList", mjScaleList);
        return Result.SUCCESS(mjScaleMap);
    }
}
