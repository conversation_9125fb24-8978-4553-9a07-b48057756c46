package com.nacos.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.business.db.mapper.UserDDRecordMapper;
import com.business.enums.BDDUseNumEnum;
import com.business.gpt.ALiTyqwGPTWebUtil;
import com.business.gpt.ChatGPTWebUtil;
import com.business.gpt.DifyToTextUtil;
import com.business.gpt.model.GptRequestBody;
import com.business.model.dto.GptTextDTO;
import com.business.model.vo.GptTextVO;
import com.business.utils.BThirdPartyKey;
import com.nacos.auth.JwtNewUtil;
import com.nacos.enums.CommonResultEnum;
import com.nacos.enums.DictConfigEnum;
import com.nacos.exception.DzBalanceE;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;
import com.nacos.service.CheckService;
import com.nacos.service.IGptTextService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class GptTextServiceImpl implements IGptTextService {

    @Resource
    private CheckService checkService;

    @Resource
    private UserDDRecordMapper userDDRecordMapper;

    @Override
    public Result<Object> generateCreativityPrompt(GptTextDTO gptTextDTO) throws IBusinessException {
        /*String responseBody = ChatGPTWebUtil.generateCreativityPrompt(new GptRequestBody(gptTextDTO.getContent(), gptTextDTO.getImageUrl()),
                dictConfigMap.get(DictConfigEnum.CHAT_KEY.getDictKey()), dictConfigMap.get(DictConfigEnum.CHAT_URL.getDictKey()));
        if (responseBody == null) {
            return Result.ERROR_EXCEPTION(CommonResultEnum.CREATIVE_PROMPT_ERROR.getValue());
        }
        GptResponseBody gptResponseBody = JSONObject.parseObject(responseBody, GptResponseBody.class);
        if (gptResponseBody == null || gptResponseBody.getChoices() == null) {
            return Result.ERROR_EXCEPTION(CommonResultEnum.CREATIVE_PROMPT_ERROR.getValue());
        }
        String[] contentStrings = gptResponseBody.getChoices().get(0).getMessage().getContent().trim().split("#");*/

        Long userID = JwtNewUtil.getUserId();

        // 获取字典配置
        // Map<Long, String> dictConfigMap = DictConfigCache.getDictConfigChatMap();
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.CHAT_KEY.getDictType());
        if (dictConfigMap == null) {
            log.error("generateCreativityPrompt= 缺少CHAT_KEY值");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        //校验剩余点子数量
        Double total = userDDRecordMapper.selectUserTotal(userID);
        double useDDQuantity = BDDUseNumEnum.CREATIVE_PROMPT.getDdUseNumDou();
        if (total == null || total < useDDQuantity) {
            throw new DzBalanceE("点数不足");
        }

        String[] contentStrings = null;
        if (gptTextDTO.getImageUrl() == null || gptTextDTO.getImageUrl().isEmpty()) {
            String responseBody = ChatGPTWebUtil.generateCreativityPromptNew(new GptRequestBody(gptTextDTO.getContent()));
            if (responseBody == null) {
                return Result.ERROR_EXCEPTION(CommonResultEnum.CREATIVE_PROMPT_ERROR.getValue());
            }
            contentStrings = responseBody.replaceAll("^#", "").replaceAll("\\s+", "").split("#");
        } else {
            String responseBody = ChatGPTWebUtil.generateCreativityPromptByImgNew(new GptRequestBody(gptTextDTO.getContent(), gptTextDTO.getImageUrl()));
            System.out.println(responseBody);
            if (responseBody == null) {
                return Result.ERROR_EXCEPTION(CommonResultEnum.CREATIVE_PROMPT_ERROR.getValue());
            }
            contentStrings = responseBody.replaceAll("^#", "").replaceAll("\\s+", "").split("#");
        }
        //扣除点子数量
        checkService.ddDeduct(userID, useDDQuantity, "创意提示词");
        return Result.SUCCESS(Arrays.asList(contentStrings));
    }

    @Override
    public Result<Object> generateCreativityPromptFcs(GptTextDTO gptTextDTO) throws IBusinessException {
        /*String responseBody = ChatGPTWebUtil.generateCreativityPrompt(new GptRequestBody(gptTextDTO.getContent(), gptTextDTO.getImageUrl()),
                dictConfigMap.get(DictConfigEnum.CHAT_KEY.getDictKey()), dictConfigMap.get(DictConfigEnum.CHAT_URL.getDictKey()));
        if (responseBody == null) {
            return Result.ERROR_EXCEPTION(CommonResultEnum.CREATIVE_PROMPT_ERROR.getValue());
        }
        GptResponseBody gptResponseBody = JSONObject.parseObject(responseBody, GptResponseBody.class);
        if (gptResponseBody == null || gptResponseBody.getChoices() == null) {
            return Result.ERROR_EXCEPTION(CommonResultEnum.CREATIVE_PROMPT_ERROR.getValue());
        }
        String[] contentStrings = gptResponseBody.getChoices().get(0).getMessage().getContent().trim().split("#");*/

        Long userID = JwtNewUtil.getUserId();

        // 获取字典配置
        // Map<Long, String> dictConfigMap = DictConfigCache.getDictConfigChatMap();
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.CHAT_KEY.getDictType());
        if (dictConfigMap == null) {
            log.error("generateCreativityPrompt= 缺少CHAT_KEY值");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        //校验剩余点子数量
//        Double total = userDDRecordMapper.selectUserTotal(userID);
//        double useDDQuantity = BDDUseNumEnum.CREATIVE_PROMPT.getDdUseNumDou();
//        if (total == null || total < useDDQuantity) {
//            throw new DzBalanceE("点数不足");
//        }

        String[] contentStrings = null;
//        if (gptTextDTO.getImageUrl() == null || gptTextDTO.getImageUrl().isEmpty()) {
            String responseBody = ALiTyqwGPTWebUtil.generateCreativityPromptNewFcs(new GptRequestBody(gptTextDTO.getContent()));
            if (responseBody == null) {
                return Result.ERROR_EXCEPTION(CommonResultEnum.CREATIVE_PROMPT_ERROR.getValue());
            }
            contentStrings = responseBody.replaceAll("^#", "").replaceAll("\\s+", "").split("#");
//        } else {
//            String responseBody = ChatGPTWebUtil.generateCreativityPromptByImgNew(new GptRequestBody(gptTextDTO.getContent(), gptTextDTO.getImageUrl()));
//            System.out.println(responseBody);
//            if (responseBody == null) {
//                return Result.ERROR_EXCEPTION(CommonResultEnum.CREATIVE_PROMPT_ERROR.getValue());
//            }
//            contentStrings = responseBody.replaceAll("^#", "").replaceAll("\\s+", "").split("#");
//        }
        //扣除点子数量
//        checkService.ddDeduct(userID, useDDQuantity, "创意提示词");
        return Result.SUCCESS(Arrays.asList(contentStrings));
    }

    @Override
    public Result<Object> generatePosterPrompt(GptTextDTO gptTextDTO) throws IBusinessException {
        String contentStrings = DifyToTextUtil.generatePosterPrompt(gptTextDTO);
        log.info("一键海报，生成提示词= " + contentStrings);
        if (contentStrings == null) {
            return Result.ERROR_EXCEPTION(CommonResultEnum.CREATIVE_PROMPT_ERROR.getValue());
        }
        Matcher matcher = Pattern.compile("\\{.*\\}").matcher(contentStrings);
        if (matcher.find()) {
            contentStrings = matcher.group();
        } else {
            contentStrings = null;
        }
        log.info("一键海报，生成提示词= " + contentStrings);
        GptTextVO gptTextVO = JSON.parseObject(contentStrings, new TypeReference<GptTextVO>() {
        });
        return Result.SUCCESS(gptTextVO);

    }


}
