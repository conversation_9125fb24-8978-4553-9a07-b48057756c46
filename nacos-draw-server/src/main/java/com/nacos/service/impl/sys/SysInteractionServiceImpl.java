package com.nacos.service.impl.sys;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.business.db.mapper.SysInteractionMapper;
import com.business.db.model.po.SysInteractionPO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;
import com.nacos.service.sys.ISysInteractionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 互动消息
*  @className: SysInteractionServiceImpl
*  @author: Myl
*  @createDate: 2023-12-21
*  @version: 1.4
 *
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class SysInteractionServiceImpl extends ServiceImpl<SysInteractionMapper, SysInteractionPO> implements ISysInteractionService {

    @Override
    public Result<Boolean> add(SysInteractionPO sysInteractionPO) {
        return Result.SUCCESS(this.save(sysInteractionPO));
    }

    @Override
    public Result<Boolean> update(SysInteractionPO sysInteractionPO) {
        return Result.SUCCESS(this.saveOrUpdate(sysInteractionPO));
    }

    @Override
    public Result<Boolean> delete(BaseDeleteEntity params) {
        return Result.SUCCESS(this.removeByIds(params.getIds()));
    }

}
