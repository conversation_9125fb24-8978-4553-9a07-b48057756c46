package com.nacos.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.business.aigc.mj.web.MJWebHttpUtil;
import com.business.aigc.mj.web.MjWebApisUtil;
import com.business.aigc.mj.web.model.MjHeaderDTO;
import com.business.aigc.mj.web.model.MjRequest;
import com.business.aigc.mj.web.model.MjRequestParam;
import com.business.aliapi.flux.FluxApiUtil;
import com.business.aliapi.flux.model.FluxResParamBO;
import com.business.bytes.ByteApisUtil;
import com.business.bytes.enums.ByteStatusEnum;
import com.business.bytes.model.PosterReqBO;
import com.business.db.mapper.*;
import com.business.db.model.dto.ImgDrawOptDTO;
import com.business.db.model.po.*;
import com.business.db.model.vo.*;
import com.business.enums.*;
import com.business.feishu.BFeiShuApis;
import com.business.le.LeonardoUtil;
import com.business.le.model.LeonardoParamBO;
import com.business.le.model.LeonardoStyleBO;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.business.mj.BMJStopUsingUtil;
import com.business.model.bo.AdminMjWebConfigBO;
import com.business.model.po.AdminMjAccountConfigPO;
import com.business.model.po.AdminMjWebConfigPO;
import com.business.model.po.ImgDrawRecordPO;
import com.business.tengxunyun.BAliYunUtil;
import com.business.tengxunyun.BTengXunUtil;
import com.business.utils.BDateUtil;
import com.business.utils.BFileUtil;
import com.business.utils.BStringUtil;
import com.business.utils.BThirdPartyKey;
import com.business.xingliu.XingLiuApiUtil;
import com.business.xingliu.model.XingLiuRequestBO;
import com.nacos.alioss.OSSApis;
import com.nacos.auth.JwtNewUtil;
import com.nacos.baiduaiapi.model.FaceDetectionResponseBodyVO;
import com.nacos.ddimg.ImgDrawUtil;
import com.nacos.ddimg.model.*;
import com.nacos.enums.*;
import com.nacos.enums.goapi.CurrentStatusEnum;
import com.nacos.exception.DzBalanceE;
import com.nacos.exception.IBusinessException;
import com.nacos.mjapi.BMJZoomEnum;
import com.nacos.mjapi.MJAPIUtil;
import com.nacos.mjapi.MJApis;
import com.nacos.mjapi.model.*;
import com.nacos.mjapp.MjWebSocketListener;
import com.nacos.model.OssParamBO;
import com.nacos.model.VipGradeBO;
import com.nacos.novitaaiapi.model.dto.MergeFaceRequestBodyDTO;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.*;
import com.nacos.service.mp.IUserDDRecordService;
import com.nacos.utils.*;
import com.nacos.utils.ai.AiFaceUtil;
import com.nacos.utils.goapi.FaceSwapVO;
import com.nacos.utils.goapi.GoApiUtil;
import com.nacos.utils.oss.AliOSSUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Service
@Slf4j
public class DrawWebServiceImpl implements DrawWebService {

    @Resource
    private IUserDDRecordService iUserDDRecordService;
    @Resource
    private PayRecordMapper payRecordMapper;
    @Resource
    private UserDDRecordMapper userDDRecordMapper;
    @Resource
    private ImgModelConfigMapper imgModelConfigMapper;
    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;//绘图记录
    @Resource
    private ImgDrawDetlMapper imgDrawDetlMapper;//绘图详情
    @Resource
    private RedisServer redisServer;//消息推送
    @Resource
    private AsyncService asyncService;
    @Resource
    private IImgFaceTemplateService imgFaceTemplateService;
    @Resource
    private CheckBalanService checkBalanService;
    @Resource
    private DzRollbackService dzRollbackService;
    @Resource
    private CheckService checkService;
    @Resource
    private FlowRecordMapper flowRecordMapper;//点子流水记录
    @Resource
    private AdminMjAccountConfigMapper adminMjAccountConfigMapper;
    @Resource
    private AdminMjWebConfigMapper adminMjWebConfigMapper;
    @Resource
    private ImgDrawStyleConfigMapper imgDrawStyleConfigMapper;

    private static int accountCount = 0;
    private static int currentIndex = 0;
    private static final List<AdminMjWebConfigBO> cacheAccountList = null;

    @Override
    public Result<Object> submitJob(ImgDrawDTO imgDrawDTO) throws IBusinessException {
        if (imgDrawDTO == null) {
            log.error("submitJob=参数不能为空");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        ImgModelConfigPO imgModelConfigPO = imgModelConfigMapper.selectById(imgDrawDTO.getModelId());
        if (imgModelConfigPO == null) {
            log.error("submitJob=模型不存在");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        //校验 角色图片列表 || 风格图片列表 是否支持
        if (imgDrawDTO.getIsAddImage() != null && imgDrawDTO.getIsAddImage()) {
            if (imgDrawDTO.getMjAddImageBO() != null && imgDrawDTO.getMjAddImageBO().getCrefImages() != null
                    && !imgDrawDTO.getMjAddImageBO().getCrefImages().isEmpty()
                    && imgModelConfigPO.getIsSupportCref() == BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue()
            ) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_CREF_ERROR.getValue());
            }
            if (imgDrawDTO.getMjAddImageBO() != null && imgDrawDTO.getMjAddImageBO().getSrefImages() != null
                    && !imgDrawDTO.getMjAddImageBO().getSrefImages().isEmpty()
                    && imgModelConfigPO.getIsSupportSref() == BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue()) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_SREF_ERROR.getValue());
            }
        }

        imgDrawDTO.setUserId(JwtNewUtil.getUserId());
        imgDrawDTO.setPrompt(BStringUtil.stringToWrap(imgDrawDTO.getPrompt()));
        //解析字符串
        ImgModelConfigVO imgModelConfigVO = new ImgModelConfigVO(imgModelConfigPO);
        Integer checkConcurrentCount = checkService.checkConcurrentCount();
        if (checkConcurrentCount == null) {
            log.error("submitJob=绘图数量已达到上限");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        // boolean isVip = checkConcurrentCount > 1; //设置规则为vip用户真实规则
        int isVip = checkConcurrentCount; //设置规则为vip用户真实规则

        // 校验跳转mj接口执行n5、n6；v5、v6
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N6.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6_1.getValue()) {
            return jobMj(isVip, imgDrawDTO, imgModelConfigVO);
        }
        // le绘画模型 == 校验跳转le接口执行
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_LE.getValue()) {
            imgDrawDTO.setInitImgUrls(null);
            return jobLe(isVip, imgDrawDTO, imgModelConfigVO);
        }
        // flux绘画模型 == 校验跳转flux接口执行
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_FLUX.getValue()) {
            imgDrawDTO.setInitImgUrls(null);
            return jobFlux(isVip, imgDrawDTO, imgModelConfigVO);
        }
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }

    @Override
    public Result<Object> submitDrawJob(ImgDrawDTO imgDrawDTO) throws IBusinessException {
        if (imgDrawDTO == null) {
            log.error("submitJob=参数不能为空");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        log.error("submitDrawJob=参数不能为空=" + JSONObject.toJSONString(imgDrawDTO));
        ImgModelConfigPO imgModelConfigPO = null;
        if (imgDrawDTO.getModelId() == null || imgDrawDTO.getModelId() == 13) {
            imgModelConfigPO = imgModelConfigMapper.selectById(8);
        } else {
            imgModelConfigPO = imgModelConfigMapper.selectById(imgDrawDTO.getModelId());
        }

        if (imgModelConfigPO == null) {
            log.error("submitDrawJob=模型不存在");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        //校验 角色图片列表 || 风格图片列表 是否支持
        if (imgDrawDTO.getIsAddImage() != null && imgDrawDTO.getIsAddImage()) {
            if (imgDrawDTO.getMjAddImageBO() != null && imgDrawDTO.getMjAddImageBO().getCrefImages() != null
                    && !imgDrawDTO.getMjAddImageBO().getCrefImages().isEmpty()
                    && imgModelConfigPO.getIsSupportCref() == BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue()
            ) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_CREF_ERROR.getValue());
            }
            if (imgDrawDTO.getMjAddImageBO() != null && imgDrawDTO.getMjAddImageBO().getSrefImages() != null
                    && !imgDrawDTO.getMjAddImageBO().getSrefImages().isEmpty()
                    && imgModelConfigPO.getIsSupportSref() == BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue()) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_SREF_ERROR.getValue());
            }
        }

        imgDrawDTO.setUserId(JwtNewUtil.getUserId());
        imgDrawDTO.setPrompt(BStringUtil.stringToWrap(imgDrawDTO.getPrompt()));
        //解析字符串
        ImgModelConfigVO imgModelConfigVO = new ImgModelConfigVO(imgModelConfigPO, null);
        Integer checkConcurrentCount = checkService.checkConcurrentCount();
        if (checkConcurrentCount == null) {
            log.error("submitJob=绘图数量已达到上限");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        int isVip = checkConcurrentCount; //设置规则为vip用户真实规则
        if (isVip == 0 && imgModelConfigPO.getIsVip() == 1) {
            return Result.ERROR_HY(CommonResultEnum.HY_BALANCE_ERROR.getValue());
        }

        // 校验跳转mj接口执行n5、n6；v5、v6
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N6.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue()
                || imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6_1.getValue()) {
            List<ImgVersionVO> mjVersions = imgModelConfigVO.getMjVersion();
            if (mjVersions != null && !mjVersions.isEmpty()) {
                ImgModelConfigPO finalImgModelConfigPO = imgModelConfigPO;
                ImgVersionVO imgVersionVO = mjVersions.stream().filter(versionVO -> versionVO.getAttribute() == finalImgModelConfigPO.getAttribute()).findFirst().orElse(null);
                if (imgVersionVO != null && imgVersionVO.getMjStyles() != null) {
                    imgModelConfigVO.setMjStyles(imgVersionVO.getMjStyles());
                }
            }
            return jobMj(isVip, imgDrawDTO, imgModelConfigVO);
        }
        // le通用
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_LE.getValue()) {
            imgDrawDTO.setInitImgUrls(null);
            return jobLe(isVip, imgDrawDTO, imgModelConfigVO);
        }
        // flux
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_FLUX.getValue()) {
            imgDrawDTO.setInitImgUrls(null);
            return jobFlux(isVip, imgDrawDTO, imgModelConfigVO);
        }
        // 即梦通用2.1绘画模型 == 校验跳转可伶接口执行
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_JIMENG.getValue()) {
            imgDrawDTO.setInitImgUrls(null);
            return jobJiMeng(isVip, imgDrawDTO, imgModelConfigVO);
        }
        // 星野
        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_XINGYE.getValue()) {
            imgDrawDTO.setInitImgUrls(null);
            return jobXingYe(isVip, imgDrawDTO, imgModelConfigVO);
        }
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }

    private Result<Object> jobMj(int isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) {
        // 新版会员模式参数
        String modelValue = BImgModelsEnum.MJ_MODEL_FAST.getStrValue();
        List<ImgModelsVO> mjModelList = imgModelConfigVO.getMjModels();
        if (mjModelList == null || mjModelList.isEmpty()) {
            log.error("jobMj=模型配置不存在，请联系客服");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        Optional<ImgModelsVO> resultModel = mjModelList.stream().filter(model -> model.getKey().intValue() == imgDrawDTO.getMjModeKey().intValue()).findFirst();
        // 检查是否存在匹配的结果
        if (resultModel.isPresent()) {
            ImgModelsVO imgModel = resultModel.get();
            modelValue = imgModel.getValue();
        } else {
            log.error("jobMj=休闲模式选择错误，请联系客服");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        boolean isLimitCasualMode = false;
        // 判断是否是 MJ_MODEL_FAST
        boolean isFastModel = BImgModelsEnum.MJ_MODEL_FAST.getStrValue().equals(modelValue);
        if (!isFastModel) {
            String frequency = RedisUtil.getValue(BRedisKeyEnum.MJ_CASUAL_LIMITING_FREQUENCY.getKey());
            if (frequency == null || frequency.isEmpty()) {
                frequency = "50";
            }
            if (getCasualModeDailyTotal(imgDrawDTO.getUserId(), LocalDate.now()) > Integer.parseInt(frequency)) {
                isLimitCasualMode = true;
            }
        }

        //校验是否存在封禁操作
        /*String isUserNot = BMJStopUsingUtil.isNotUserMJDraw(imgDrawDTO.getUserId());
        if (isUserNot!=null){
            return Result.ERROR(isUserNot);
        }*/

        JobInfo jobInfo = null;
        MJAccountHeaderBO mjAccountHeaderBO;
        ImgDrawRecordPO imgDrawRecordBO = new ImgDrawRecordPO();
        imgDrawRecordBO.setId(IdWorker.getId());
        imgDrawRecordBO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());
        try {
            if (isNotMJLock()) {
                log.error("jobMj=mj未释放锁，请稍后再试");
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            imgDrawDTO.setPromptUse(imgDrawDTO.getPrompt());//格式化请求参数：api 方式无需进行翻译
            //查询是否存在可执行账号
            mjAccountHeaderBO = ImgDrawWebPUtil.getMjParam(imgDrawDTO, imgModelConfigVO, 1);
            try {
                Long concurrency = imgDrawRecordMapper.selectCount(
                        new LambdaQueryWrapper<ImgDrawRecordPO>()
                                .in(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_QUEUING.getValue(), ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                                .eq(ImgDrawRecordPO::getMjIsRelaxed, ImgDrawEnum.SUPER_ID_DRAW.getValue())
                );

                List<AdminMjWebConfigBO> accountFastList = new ArrayList<>();
                List<AdminMjWebConfigBO> accountList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE.getKey()), new TypeReference<List<AdminMjWebConfigBO>>() {
                });
                if (!isFastModel || accountList == null || accountList.isEmpty() || concurrency == 6) {
                    accountList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE_TURTLE.getKey()), new TypeReference<List<AdminMjWebConfigBO>>() {
                    });
                    if (accountList != null || !accountList.isEmpty()) {
                        ++accountCount;
                        accountFastList = accountList.stream().filter(account -> (account.getPriorityLevel() != null && account.getPriorityLevel() == 2)).toList();
                    }
                    if (!accountFastList.isEmpty() && accountCount <= 4) {
                        accountList = accountFastList;
                    } else {
                        accountCount = 0;
                    }
                }
                if (accountList.isEmpty()) {
                    BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "没有可用的MJ账号", "没有可用的MJ账号，请尽快添加账号");
                    return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                }

                int randomIndex = ThreadLocalRandom.current().nextInt(accountList.size());
                AdminMjWebConfigBO randomAccount = accountList.get(randomIndex);

                /*Random random = new Random();
                int randomIndex = random.nextInt(accountList.size());
                AdminMjWebConfigBO randomAccount = accountList.get(randomIndex);*/
                // AdminMjWebConfigBO nextAccount = getNextAccount(accountList);
                if (randomAccount == null || randomAccount.getId() == null) {
                    BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "没有可用的MJ账号", "没有可用的MJ账号，请尽快添加账号");
                    return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                }
                imgDrawRecordBO.setMjIsRelaxed(randomAccount.getAccountSpeed().equals(BAccountSpeedEnum.ACCOUNT_RELAXED.getSpeed()) ? 1 : 0);

                assert mjAccountHeaderBO != null;
                mjAccountHeaderBO.setMjAccountId(randomAccount.getId());
                mjAccountHeaderBO.setPrivateFlag(randomAccount.getPriorityLevel());
                System.out.println("==轮循mj账号id:" + randomAccount.getChannelId());

                // 判断是否使用了自定义风格码--判断自定义风格码的取值-
                if (imgDrawDTO.getMjStyleKey() != null && imgDrawDTO.getMjStyleKey() == 3
                        && imgDrawDTO.getStyleCodeIds() != null && !imgDrawDTO.getStyleCodeIds().isEmpty()
                        && imgDrawDTO.getStyleCodeWeights() != null && !imgDrawDTO.getStyleCodeWeights().isEmpty()) {
                    List<Long> styleCodeIds = convertStringToList(imgDrawDTO.getStyleCodeIds());
                    List<String> styleCodeWeights = Arrays.asList(imgDrawDTO.getStyleCodeWeights().split(","));

                    mjAccountHeaderBO.setMjStyleCode(imgDrawStyleConfigMapper.selectByIds(styleCodeIds));
                    mjAccountHeaderBO.setMjStyleWeight(styleCodeWeights.stream()
                            .map(Double::parseDouble)
                            .collect(Collectors.toList()));
                    // 增加自定义风格码使用次数
                    imgDrawStyleConfigMapper.batchUpdate(styleCodeIds);
                }
                MjRequestParam mjRequestParam = MjWebApisUtil.createImageRequestParam(mjAccountHeaderBO, new MjHeaderDTO(randomAccount.getChannelId(), randomAccount.getCookie(), randomAccount.getAccountSpeed()), new MjRequest());

                if (mjRequestParam == null || mjRequestParam.getRequest() == null) {
                    BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "绘图参数错误", "绘图参数错误，请查看日志");
                    return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                }
                mjRequestParam.setIsFastModel(isFastModel);
                mjRequestParam.setImgDrawId(imgDrawRecordBO.getId());

                // 判断账号是否锁定了
                String accountLock = RedisUtil.getValue(BRedisKeyEnum.MJ_ACCOUNT_LOCK.getKey());
                if (Boolean.parseBoolean(accountLock)) {
                    imgDrawRecordBO.setCreateTime(DateUtil.getCurrentDateLaterDate(cn.hutool.core.date.DateUtil.date(), 1));
                    RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())),
                            JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                } else {
                    if (isFastModel) { // 不是休闲模式 则直接提交到mj
                        log.info("mj提交参数 {}：", JSONObject.toJSONString(mjRequestParam.getRequest()));
                        Object result = MjWebApisUtil.postCreateImage(mjRequestParam.getRequest(), mjRequestParam.getCookie(), ImgOptModelEnum.MJ_OPT_ATTRIBUTE_DRAW.getValue());
                        if (result instanceof JobInfo) {
                            jobInfo = (JobInfo) result;
                            System.out.println("==mj账号id:" + JSONObject.toJSONString(jobInfo));
                        } else if (result instanceof FailureJobInfo failureJobInfo) {
                            //校验：封号异常；token异常；时间失效异常；指令异常,当指令不符合规定时重复绘图 要封号暂停5分钟
                            /*if (jobInfo.getIsPendingModMessage() || jobInfo.getIsTokenExhausted() || jobInfo.getIsCreditsExhausted() || jobInfo.getIsBannedPromptDetected() || jobInfo.getIsInvalidLink()){
                                if (jobInfo.getIsBannedPromptDetected()){
                                    BMJStopUsingUtil.prohibitMJDraw(imgDrawDTO.getUserId(), imgDrawDTO.getPrompt());
                                }
                                return Result.ERROR(mjError(jobInfo,mjAccountHeaderBO));
                            }*/
                            Result<Object> objectResult = handleMjJobFailureType(failureJobInfo.getType(), randomAccount.getId(), randomAccount.getAccountName(), mjRequestParam, mjAccountHeaderBO.getInitImgUrls());
                            if (objectResult != null) {
                                return objectResult;
                            }
                        }
                    } else {
                        if (isLimitCasualMode) {  // 休闲模式限制绘图频率
                            imgDrawRecordBO.setCreateTime(DateUtil.getCurrentDateLaterDate(cn.hutool.core.date.DateUtil.date(), 7));
                            RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())),
                                    JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                        } else {
                            imgDrawRecordBO.setCreateTime(DateUtil.getCurrentDateLaterDate(cn.hutool.core.date.DateUtil.date(), 2));
                            RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())),
                                    JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                        }
                        imgDrawRecordBO.setStatus(ImgDrawEnum.STATUS_QUEUING.getValue());
                    }
                }
            } catch (Exception e) {
                log.error("jobMj绘图失败：{}", e);
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            if (jobInfo == null) {
                jobInfo = new JobInfo();
                jobInfo.setBatch_size(4);
            }
        } finally {
            RedisUtil.releaseLock(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_LOCK.getStrKey());//释放锁
        }

        try {
            // TODO 创建进行中任务
            ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecord(imgDrawRecordBO, imgDrawDTO, imgModelConfigVO, mjAccountHeaderBO);
            imgDrawRecordPO.setMjAccountId(mjAccountHeaderBO.getMjAccountId());
            if (isVip > 0) {
                imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue());
            } else {
                imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue());
            }

            // TODO 扣除用户点子
            double dzQuantity = BDDUseNumEnum.MJ_DRAW.getDdUseNumDou();
            String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
            String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
            FlowRecordPO flowRecordSub = null;
            if (optTitleOne != null) {
                flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
            }

            if (isFastModel) {
                if (isVip >= VipGradeEnum.MEMBER_BASE.getIntValue()) { // 如果是会员基础级别及以上，将 dzQuantity 减半
                    dzQuantity /= 2;
                }
                checkBalanService.checkUser(imgDrawDTO.getUserId(), dzQuantity, flowRecordSub);
            } else {
                if (isVip >= VipGradeEnum.MEMBER_NORM.getIntValue()) {
                    dzQuantity = 0;
                    checkBalanService.checkUserNew(imgDrawDTO.getUserId(), dzQuantity, flowRecordSub);
                } else {
                    checkBalanService.checkUser(imgDrawDTO.getUserId(), dzQuantity, flowRecordSub);
                }
            }

            if (imgDrawRecordBO.getCreateTime() != null) {
                imgDrawRecordPO.setCreateTime(imgDrawRecordBO.getCreateTime());
            }
            imgDrawRecordPO.setUseDdQua(dzQuantity);
            imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
            imgDrawRecordPO.setUserId(imgDrawDTO.getUserId());//装载用户id
            imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
            imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
            imgDrawRecordPO.setStatus(imgDrawRecordBO.getStatus());//装载任务状态
            imgDrawRecordPO.setMjJobId(jobInfo.getJob_id());//装载任务id
            imgDrawRecordPO.setMjIsRelaxed(imgDrawRecordBO.getMjIsRelaxed());
            imgDrawRecordPO.setMjIsCasual(!isFastModel ? 1 : 0);
            imgDrawRecordPO.setMjAccountId(mjAccountHeaderBO.getMjAccountId());
            //装载宽高尺寸：mj接口返回的宽高尺寸
            imgDrawRecordPO.setWidth(jobInfo.getWidth());
            imgDrawRecordPO.setHeight(jobInfo.getHeight());
            imgDrawRecordPO.setImgQuantity(jobInfo.getBatch_size());
            imgDrawRecordPO.setFinalPrompt(jobInfo.getFull_command());
            imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());
            if (jobInfo.getWidth() == 0 || jobInfo.getHeight() == 0) {
                imgDrawRecordPO.setWhDivide(mjAccountHeaderBO.getWhDivide());
            } else {
                imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(jobInfo.getWidth(), jobInfo.getHeight()));
            }

            //校验是否垫图
            if (imgDrawDTO.getInitImgUrls() != null && !imgDrawDTO.getInitImgUrls().isEmpty()) {
                imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(imgDrawDTO.getInitImgUrls()));
            }
            if (imgDrawDTO.getIsAddImage() != null && imgDrawDTO.getIsAddImage() && imgDrawDTO.getMjAddImageBO() != null) {
                imgDrawRecordPO.setInitImgUrls(mjAccountHeaderBO.getInitImgUrls());
                imgDrawRecordPO.setInitImgObject(JSON.toJSONString(imgDrawDTO.getMjAddImageBO()));
            }

            if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
                ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
                if (jobInfo.getBatch_size() > 0) {
                    imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
                    imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
                    imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
                    imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
                    imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());
                    imgDrawHistoryVO.setPrompt(imgDrawRecordPO.getPromptInit());
                    imgDrawHistoryVO.setPromptUse(imgDrawRecordPO.getPromptUse());
                    imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
                    imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
                    imgDrawHistoryVO.setImgQuantity(jobInfo.getBatch_size());
                    imgDrawHistoryVO.setInitImgObject(imgDrawRecordPO.getInitImgObject());
                    List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();
                    for (int i = 0; i < jobInfo.getBatch_size(); i++) {
                        ImgDrawDetlVO imgDrawDetlVO = new ImgDrawDetlVO();
                        imgDrawDetlVO.setDrawRecordId(imgDrawRecordPO.getId());
                        imgDrawDetlVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
                        imgDrawDetlVO.setImgIndex(i);
                        imgDrawDetlVO.setWhDivide(ImgDrawUtil.getWhDivide(jobInfo.getWidth(), jobInfo.getHeight()));
                        imgDrawDetlVO.setImgWidth(jobInfo.getWidth());
                        imgDrawDetlVO.setImgHeight(jobInfo.getHeight());
                        imgDrawDetlVOS.add(imgDrawDetlVO);
                    }
                    imgDrawHistoryVO.setImgDrawDetls(imgDrawDetlVOS);
                    imgDrawHistoryVO.setJobId(imgDrawRecordPO.getMjJobId());
                    log.info("imgDrawHistoryVO= {}", imgDrawHistoryVO);
                    if (imgDrawRecordPO.getMjJobId() != null) {
                        //缓存任务有效期为一天
                        RedisUtil.setValueSeconds(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_JOB_ID.getStrKey(), imgDrawRecordPO.getMjJobId()), JSONObject.toJSONString(imgDrawHistoryVO), 1, TimeUnit.DAYS);
                        //异步渐显推送
                        MJAPIUtil.sendHandshakeRequest(imgDrawRecordPO.getMjJobId(), new MjWebSocketListener(imgDrawRecordPO.getMjJobId(), redisServer));
                    }
                }
                return Result.SUCCESS(imgDrawHistoryVO);
            }
        } catch (DzBalanceE e) {
            log.error("点子不足充值 {}", e.getMessage(), e);//释放锁
            return Result.ERROR_DZ(CommonResultEnum.DZ_BALANCE_ERROR.getValue());
        } catch (Exception e) {
            log.error("文生图操作时异常错误 {}", e.getMessage(), e);//释放锁
        }
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }

    public static List<Long> convertStringToList(String ids) {
        String[] idsArray = ids.split(",");
        List<Long> result = new ArrayList<>(idsArray.length);
        for (String id : idsArray) {
            if (id.isEmpty()) {
                continue;
            }
            result.add(Long.parseLong(id));
        }
        return result;
    }

    // mj异常处理
    public Result<Object> handleMjJobFailureType(String type, Long accountId, String accountName, MjRequestParam mjRequestParam, String initImgUrls) throws IBusinessException {
        switch (type) {
            case "blocked" -> {
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P1, "MJ账号异常【blocked】", "账号[" + accountName + "],已封锁自动关闭账号");
                if (adminMjWebConfigMapper.update(null, new LambdaUpdateWrapper<AdminMjWebConfigPO>()
                        .set(AdminMjWebConfigPO::getStatus, 0)
                        .set(AdminMjWebConfigPO::getIsEnable, 0)
                        .set(AdminMjWebConfigPO::getRemark, "账号[" + accountName + "],已封锁自动关闭账号。")
                        .eq(AdminMjWebConfigPO::getId, accountId)) > 0) {
                    if (getMjWebAccountList()) {
                        BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号异常", "没有可用账号，尽快添加账号。");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                }
                RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                // return Result.ERROR(CommonResultEnum.DRAW_MJ_API_RELAXED_NEW_ERROR.getValue());
                return null;
            }
            case "subscription_required" -> {
                log.error("Due to extreme demand we can't provide a free trial right now. Please </subscribe:987795925764280351> to create images with Midjourney.");
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P1, "MJ提交编辑异常【subscription_required】", "由于需求太大，我们现在不能提供免费试用。请订阅使用Midjourney创建图像。");
                if (adminMjWebConfigMapper.update(null, new LambdaUpdateWrapper<AdminMjWebConfigPO>()
                        .set(AdminMjWebConfigPO::getStatus, 0)
                        .set(AdminMjWebConfigPO::getIsEnable, 0)
                        .set(AdminMjWebConfigPO::getRemark, "账号[" + accountName + "],由于需求太大，请订阅使用Midjourney创建图像。")
                        .eq(AdminMjWebConfigPO::getId, accountId)) > 0) {
                    if (getMjWebAccountList()) {
                        BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号异常", "没有可用账号，尽快添加账号。");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                }
                RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                return null;
            }
            case "credits_exhausted" -> {
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P1, "MJ账号异常【credits_exhausted】", "账号[" + accountName + "],快速时间已用完，自动切换到慢速");
                if (adminMjWebConfigMapper.update(null, new LambdaUpdateWrapper<AdminMjWebConfigPO>()
                        .set(AdminMjWebConfigPO::getAccountSpeed, "relaxed")
                        .set(AdminMjWebConfigPO::getRemark, "账号[" + accountName + "],快速时间已用完，自动切换到慢速。")
                        .eq(AdminMjWebConfigPO::getId, accountId)) > 0) {
                    if (getMjWebAccountList()) {
                        BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号异常", "没有可用账号，尽快添加账号。");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                }
                RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                // return Result.ERROR(CommonResultEnum.DRAW_MJ_API_RELAXED_NEW_ERROR.getValue());
                return null;
            }
            case "pending_mod_message" -> {
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P1, "MJ账号异常【pending_mod_message】", "账号[" + accountName + "]待审核账号，已被阻止访问midtravel。");
                if (adminMjWebConfigMapper.update(null, new LambdaUpdateWrapper<AdminMjWebConfigPO>()
                        .set(AdminMjWebConfigPO::getStatus, 0)
                        .set(AdminMjWebConfigPO::getIsEnable, 0)
                        .set(AdminMjWebConfigPO::getRemark, "账号[" + accountName + "]待审核账号，已被阻止访问midtravel。")
                        .eq(AdminMjWebConfigPO::getId, accountId)) > 0) {
                    if (getMjWebAccountList()) {
                        BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号异常", "没有可用账号，尽快添加账号。");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                }
                RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                // return Result.ERROR(CommonResultEnum.DRAW_MJ_API_RELAXED_NEW_ERROR.getValue());
                return null;
            }
            case "unknown_error" -> {
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P1, "MJ账号异常【unknown_error】", "账号[" + accountName + "],未经授权，账号无法使用。");
                if (adminMjWebConfigMapper.update(null, new LambdaUpdateWrapper<AdminMjWebConfigPO>()
                        .set(AdminMjWebConfigPO::getStatus, 0)
                        .set(AdminMjWebConfigPO::getIsEnable, 0)
                        .set(AdminMjWebConfigPO::getRemark, "账号[" + accountName + "],未经授权，账号无法使用。")
                        .eq(AdminMjWebConfigPO::getId, accountId)) > 0) {
                    if (getMjWebAccountList()) {
                        BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号异常", "没有可用账号，尽快添加账号。");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                }
                RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                // return Result.ERROR(CommonResultEnum.DRAW_MJ_API_RELAXED_NEW_ERROR.getValue());
                return null;
            }
            case "queue_full" -> {
                log.error("作业队列已满，稍后重试");
                RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                // return Result.ERROR(CommonResultEnum.DRAW_MJ_API_RELAXED_NEW_ERROR.getValue());
                return null;
            }
            case "forbidden_flag" -> {
                log.error("You are not allowed to use the stealth flag.");
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "forbidden_flag", "账号[" + accountName + "],You are not allowed to use the stealth flag.");
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());

                // RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK,String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam),30, TimeUnit.MINUTES);
                // return Result.ERROR(CommonResultEnum.DRAW_MJ_API_RELAXED_NEW_ERROR.getValue());
            }
            case "invalid_link" -> {
                log.error("存在不支持的图片,请换张图片试试");
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "Mj图片审核失败", "垫图无效, ".concat(initImgUrls));
                return Result.ERROR_MJ(CommonResultEnum.IMG_NOT_MATCH_ERROR.getValue());
            }
            case "invalid_parameter" -> {
                log.error("Expected a value for argument `--version`,参数--version需要一个值");
                return Result.ERROR("提示词错误[--version]错误,请删除调整后重试。");
            }
            case "image_denied" -> {
                log.error("Request cancelled due to image filters`由于图像过滤器，请求被取消, ".concat(initImgUrls));
                return Result.ERROR(CommonResultEnum.IMG_NOT_MATCH_ERROR.getValue());
            }
            case "banned_prompt" -> {
                log.error("Sorry! Our AI moderator thinks this prompt is probably against our community standards.\\n\\nPlease review our current community standards:\\n\\n**ALLOWED**\\n- Any image up to PG-13 rating involving fiction, fantasy, mythology.\\n- Real images that may be seen as respectful or light-hearted parodies, satire, caricatures\\n- Imaginary or exaggerated real-life scenarios, including absurd or humorous situations.\\n\\n**NOT ALLOWED**\\n- Disrespectful, harmful, misleading public figures/events portrayals or potential to mislead.\\n- Hate speech, explicit or real-world violence.\\n- Nudity or unconsented overtly sexualized public figures.\\n- Imagery that might be considered culturally insensitive\\n\\nThis AI system isn't perfect. If you find it rejecting something innocent please press the **Notify Developers** button and we will review it and try to further improve our performance. Thank you for your help!" +
                        "提示词违反了社区规定");
                return Result.ERROR(CommonResultEnum.PROMPT_WORD_ERROR.getValue());
            }
            case "banned_prompt_detected" -> {
                log.error("Sorry! Our AI moderator thinks this prompt is probably against our community standards.\\n\\nPlease review our current community standards:\\n\\n**ALLOWED**\\n- Any image up to PG-13 rating involving fiction, fantasy, mythology.\\n- Real images that may be seen as respectful or light-hearted parodies, satire, caricatures\\n- Imaginary or exaggerated real-life scenarios, including absurd or humorous situations.\\n\\n**NOT ALLOWED**\\n- Disrespectful, harmful, misleading public figures/events portrayals or potential to mislead.\\n- Hate speech, explicit or real-world violence.\\n- Nudity or unconsented overtly sexualized public figures.\\n- Imagery that might be considered culturally insensitive\\n\\nThis AI system isn't perfect. If you find it rejecting something innocent please press the **Notify Developers** button and we will review it and try to further improve our performance. Thank you for your help!" +
                        "提示词违反了社区规定。");
                return Result.ERROR(CommonResultEnum.PROMPT_WORD_ERROR.getValue());
            }
            case "nsfw_alert" -> {
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P1, "MJ账号异常【nsfw_alert】", "账号[" + accountName + "],您已经触发了一个适度警报。您的账户正在人工审核中。");
                if (adminMjWebConfigMapper.update(null, new LambdaUpdateWrapper<AdminMjWebConfigPO>()
                        .set(AdminMjWebConfigPO::getIsEnable, 0)
                        .set(AdminMjWebConfigPO::getRemark, "账号[" + accountName + "],您已经触发了一个适度警报。您的账户正在人工审核中。")
                        .eq(AdminMjWebConfigPO::getId, accountId)) > 0) {
                    if (getMjWebAccountList()) {
                        BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号异常", "没有可用账号，尽快添加账号。");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                }
                RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                // return Result.ERROR(CommonResultEnum.DRAW_MJ_API_RELAXED_NEW_ERROR.getValue());
                return null;
            }
            case "forbidden_error" -> {
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号异常【403】", "账号[" + accountName + "]报403，自动关闭账号,人工处理");
                RedisUtil.setIfAbsent(BRedisKeyEnum.MJ_ACCOUNT_LOCK.getKey(), "true", 60, TimeUnit.SECONDS);
                if (adminMjWebConfigMapper.update(null, new LambdaUpdateWrapper<AdminMjWebConfigPO>()
                        .set(AdminMjWebConfigPO::getIsEnable, 0)
                        .set(AdminMjWebConfigPO::getRemark, "报403错误，平均等待10分钟在启用")
                        .eq(AdminMjWebConfigPO::getId, accountId)) > 0) {
                    if (getMjWebAccountList()) {
                        BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号异常", "没有可用账号，尽快添加账号。");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                }
                RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                return null;
            }
            case "captcha_required" -> {
                log.error("Captcha required=需要验证码。");
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号需验证", "账号[" + accountName + "]报需要人工验证，账号已停用");
                if (adminMjWebConfigMapper.update(null, new LambdaUpdateWrapper<AdminMjWebConfigPO>()
                        .set(AdminMjWebConfigPO::getIsEnable, 0)
                        .set(AdminMjWebConfigPO::getRemark, "账号[" + accountName + "],MJ账号触发人工校验。")
                        .eq(AdminMjWebConfigPO::getId, accountId)) > 0) {
                    if (getMjWebAccountList()) {
                        BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号异常", "没有可用账号，尽快添加账号。");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                }
                RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 60, TimeUnit.MINUTES);
                return null;
            }
            case "internal_error" -> {
                log.error("调试接口异常");
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P1, "MJ提交编辑异常【internal_error】", "接口调用问题，需查看日志：" + mjRequestParam.getImgDrawId() + "\n Something went wrong.\\nYour unique trace: \n" +
                        "trimester lavish fried");
                return null;
            }
            case "invalid_request" -> {
                log.error("无效的请求");
                BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P1, "MJ提交编辑异常【invalid_request】", "无效的请求，不支持此操作：" + mjRequestParam.getImgDrawId() + "\n v6.1 2x upscale is only supported on version 6.1");
                return null;
            }
            default -> {
                log.error("未知的错误信息，请查看日志，是否出现了新的错误状态码");
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
        }
    }

    public boolean getMjWebAccountList() {
        List<AdminMjWebConfigBO> accountList = adminMjWebConfigMapper.selectAdminMjWebConfigBOList();
        if (accountList == null || accountList.isEmpty()) {
            log.error("WEB版本获取账号:{}", "没有可用账号");
            BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P1, "WEB版本获取账号败", "没有可用账号，请尽快添加账号");
            return true;
        }
        List<AdminMjWebConfigBO> accountListFast = accountList.stream().filter(account -> account.getAccountSpeed().equals(BAccountSpeedEnum.ACCOUNT_FAST.getSpeed())).toList();
        List<AdminMjWebConfigBO> accountListTurtle = accountList.stream().filter(account -> account.getAccountSpeed().equals(BAccountSpeedEnum.ACCOUNT_RELAXED.getSpeed())).toList();
        RedisUtil.setValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE.getKey(), JSONObject.toJSONString(accountListFast));
        RedisUtil.setValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE_TURTLE.getKey(), JSONObject.toJSONString(accountListTurtle));
        return false;

    }

    public static synchronized AdminMjWebConfigBO getNextAccount(List<AdminMjWebConfigBO> accounts) {
        if (accounts == null || accounts.isEmpty()) {
            throw new IllegalStateException("Account list is not initialized or empty");
        }
        // 确保 currentIndex 不超过列表长度
        if (currentIndex >= accounts.size()) {
            currentIndex = 0;
        }
        AdminMjWebConfigBO account = accounts.get(currentIndex);
        currentIndex = (currentIndex + 1) % accounts.size();
        return account;
    }

    private void resetMjAccount(Long accountId) {
        cacheAccountList.removeIf(account -> Objects.equals(account.getId(), accountId));
    }

    private Result<Object> jobDalle(int isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        //格式化请求参数：api 方式无需进行翻译
        imgDrawDTO.setPromptUse(imgDrawDTO.getPrompt());
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecordDe(imgDrawDTO, imgModelConfigVO);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setUserId(imgDrawDTO.getUserId());//装载用户id
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        //装载宽高尺寸：mj接口返回的宽高尺寸
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());
        Map<String, Object> map = ImgDrawPUtil.getDalleWhDivideByImgScaleKey(imgDrawDTO.getImgScaleKey());
        imgDrawRecordPO.setWhDivide(Double.valueOf(map.get("whDivide").toString()));
        imgDrawRecordPO.setPromptUse(imgDrawRecordPO.getPromptUse().concat(" DALL·E 3 --ar ").concat((String) map.get("size")));

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());

        // 获取字典配置
        // Map<Long, String> dictConfigMap = DictConfigCache.getDictConfigChatMap();
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.CHAT_KEY.getDictType());
        if (dictConfigMap == null) {
            log.error("jobDalle= 缺少CHAT_KEY值");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        // TODO 扣除用户点子 ===== 查询点子规则数据
        double deDrawDeductQua;
        if (imgDrawDTO.getImgScaleKey() == DalleSizeEnum.DALLE_ONE_TO_ONE.getKey()) {
            deDrawDeductQua = DDUseRuleEnum.getDDUseRuleQuality(DDUseRuleEnum.DRAW_DE_SIZE_ONE);//dalle 3扣除点点数量
        } else {
            deDrawDeductQua = DDUseRuleEnum.getDDUseRuleQuality(DDUseRuleEnum.DRAW_DE_SIZE_TWO);//dalle 3扣除点点数量
        }
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        ;
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), deDrawDeductQua, flowRecordSub);

        imgDrawRecordPO.setUseDdQua(deDrawDeductQua);
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            try {
                asyncService.asynExecutionDraw(imgDrawRecordPO, (String) map.get("size"), imgDrawDTO, imgDrawHistoryVO, deDrawDeductQua, dictConfigMap);
                return Result.SUCCESS(imgDrawHistoryVO);
            } catch (Exception e) {
                log.error("==++==dall= {}", e.getMessage());
                dzRollbackService.rollback(imgDrawRecordPO.getUserId(), deDrawDeductQua, optTitleOne.concat(optTitleTwo != null ? optTitleTwo : ""));
                imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
            }
        }
        log.error("jobDalle= 插入数据库失败");
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }

    private Result<Object> jobSd(int isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        //格式化请求参数：api 方式需进行翻译
        String promptUse = BTengXunUtil.textToEnglish(imgDrawDTO.getPrompt(), "zh", "en");
        imgDrawDTO.setPromptUse(promptUse);
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecordDe(imgDrawDTO, imgModelConfigVO);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        //装载宽高尺寸
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());

        // 获取字典配置
        // Map<Long, String> dictConfigMap = DictConfigCache.getDictConfigChatMap();
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(2001L);

        //初始化尺寸
        String ar = "1:1";
        double whDecide = 1;
        List<ImgScaleDTO> imgScales = imgModelConfigVO.getImgScales();
        for (ImgScaleDTO imgScale : imgScales) {
            if (Objects.equals(imgScale.getKey(), imgDrawDTO.getImgScaleKey())) {
                System.out.println(imgScale.toString());
                ar = imgDrawDTO.getImgScaleIsTrue() ? imgScale.getWidth() + ":" + imgScale.getHeight() : imgScale.getHeight() + ":" + imgScale.getWidth();
                whDecide = imgDrawDTO.getImgScaleIsTrue() ?
                        (new BigDecimal(String.valueOf(imgScale.getWidth())).divide(new BigDecimal(String.valueOf(imgScale.getHeight())), 6, RoundingMode.HALF_UP).doubleValue()) :
                        (new BigDecimal(String.valueOf(imgScale.getHeight())).divide(new BigDecimal(String.valueOf(imgScale.getWidth())), 6, RoundingMode.HALF_UP).doubleValue());
            }
        }

        imgDrawRecordPO.setWhDivide(whDecide);
        imgDrawRecordPO.setPromptUse(imgDrawRecordPO.getPromptUse().concat(" SD 3 --ar ").concat(ar));

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());

        // TODO 扣除用户点子 ===== 查询点子规则数据
        boolean isMember = iUserDDRecordService.getUserIsVip();
        double sdDrawDeductQua = BDDUseNumEnum.getDDUseByIsVip(BDDUseNumEnum.SUNO_AUDIO, isMember);
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        ;
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), sdDrawDeductQua, flowRecordSub);

        imgDrawRecordPO.setUseDdQua(sdDrawDeductQua);
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            try {
                // TODO SD3绘画模型 暂时不用，后期对接
                asyncService.asynExecutionDraw(imgDrawRecordPO, ar, imgDrawDTO, imgDrawHistoryVO, sdDrawDeductQua, dictConfigMap);
                return Result.SUCCESS(imgDrawHistoryVO);
            } catch (Exception e) {
                log.error("==++==dall= {}", e.getMessage());
                dzRollbackService.rollback(imgDrawRecordPO.getUserId(), sdDrawDeductQua, optTitleOne.concat(optTitleTwo != null ? optTitleTwo : ""));
                imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
            }
        }
        log.error("jobSd= 插入数据库失败");
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }

    /**
     * LE绘图
     * @param isVip 是否为会员
     * @param imgDrawDTO 绘图DTO
     * @param imgModelConfigVO 绘图模型配置VO
     * @return 结果
     * @throws IBusinessException 异常
     */
    private Result<Object> jobLe(int isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        if (imgDrawDTO.getMjAddImageBO() != null) {
            if (imgDrawDTO.getMjAddImageBO().getCrefImages() != null && imgDrawDTO.getMjAddImageBO().getCrefImages().size() > 1) {
                return Result.ERROR(CommonResultEnum.LE_DRAW_CREF_ERROR.getValue());
            }
            if (imgDrawDTO.getMjAddImageBO().getDefImages() != null && imgDrawDTO.getMjAddImageBO().getDefImages().size() > 1) {
                return Result.ERROR(CommonResultEnum.LE_DRAW_SREF_ERROR.getValue());
            }
            if (imgDrawDTO.getMjAddImageBO().getCrefImages() != null && !imgDrawDTO.getMjAddImageBO().getCrefImages().isEmpty()
                    && imgDrawDTO.getMjAddImageBO().getDefImages() != null && !imgDrawDTO.getMjAddImageBO().getDefImages().isEmpty()) {
                return Result.ERROR(CommonResultEnum.LE_DRAW_CREFANDSREF_ERROR.getValue());
            }
        }
        // 获取字典配置（Le）
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.LE_DRAW_KEY.getDictType());
        if (dictConfigMap == null) {
            log.info("jobLe=LE没有可用的key: {}", "请立即更换");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        String apiKey = dictConfigMap.get(DictConfigEnum.LE_DRAW_KEY.getDictKey());
        if (apiKey == null) {
            log.info("jobLe=LE没有可用的key: {}", "请立即更换");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        //格式化请求参数：api 方式需进行翻译
        String promptUse = BTengXunUtil.textToEnglish(imgDrawDTO.getPrompt(), "zh", "en");
        imgDrawDTO.setPromptUse(promptUse);
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecordDe(imgDrawDTO, imgModelConfigVO);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_FOUR.getValue());
        if (isVip > 0) {
            imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue());
        } else {
            imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue());
        }

        // 装载宽高尺寸
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());

        // LE请求参数封装
        LeonardoStyleBO leonardoStyleBO = new LeonardoStyleBO();
        leonardoStyleBO.setNumImages(4);
        leonardoStyleBO.setPrompt(promptUse);
        leonardoStyleBO.setNegativePrompt("Disharmonious colorsative Prompt，NSFW,  watermarks, double body, double face, double features, incorrect posture, two heads, two faces, plastic, deformed, blurry, messed up eyes, crossed eyes, disfigured, poorly drawn face, mutation, mutated, ugly, poorly drawn hands, missing limb, blurry, floating limbs, disconnected limbs, malformed hands, out of focus, long neck, long body, long fingers, blender, doll, cropped, low-res, , out of frame, double two heads, blurred, ugly, disfigured, too many fingers, repetitive, grainy, extra limbs, poor anatomy, high pass filter, airbrush, zoomed, soft light, smooth skin, extra limbs, extra fingers, mutated hands, uneven proportions, blind, ugly eyes, dead eyes, blur, out of shot, out of focus");

        //初始化尺寸
        String ar = "1:1";
        double whDecide = 1;
        List<ImgScaleDTO> imgScales = imgModelConfigVO.getImgScales();
        for (ImgScaleDTO imgScale : imgScales) {
            if (Objects.equals(imgScale.getKey(), imgDrawDTO.getImgScaleKey())) {
                ar = imgDrawDTO.getImgScaleIsTrue() ? imgScale.getWidth() + ":" + imgScale.getHeight() : imgScale.getHeight() + ":" + imgScale.getWidth();
                whDecide = imgDrawDTO.getImgScaleIsTrue() ?
                        (new BigDecimal(String.valueOf(imgScale.getWidth())).divide(new BigDecimal(String.valueOf(imgScale.getHeight())), 6, RoundingMode.HALF_UP).doubleValue()) :
                        (new BigDecimal(String.valueOf(imgScale.getHeight())).divide(new BigDecimal(String.valueOf(imgScale.getWidth())), 6, RoundingMode.HALF_UP).doubleValue());
                int[] ratio = BLEDrowSizeEnum.getLeWidthAndHeight(ar);
                leonardoStyleBO.setWidth(ratio[0]);
                leonardoStyleBO.setHeight(ratio[1]);
            }
        }

        List<LeonardoStyleBO.Element> elements = new ArrayList<>();
        List<MJStyleDTO> imgStyles = (List<MJStyleDTO>) imgModelConfigVO.getMjStyles();
        for (MJStyleDTO imgStyle : imgStyles) {
            if (Objects.equals(imgStyle.getKey(), imgDrawDTO.getMjStyleKey())) {
                leonardoStyleBO.setAlchemy(imgStyle.getAlchemy());
                leonardoStyleBO.setPhotoReal(imgStyle.getPhotoReal());
                if (imgStyle.getAkUUID() == null || imgStyle.getAkUUID().isEmpty()) {
                    elements = null;
                } else {
                    float weight = com.business.utils.ImgDrawUtil.getStylizeValue(imgDrawDTO.getMjStylizeValue());
                    String[] akUUIDArray = imgStyle.getAkUUID().split(",");
                    for (String akUUID : akUUIDArray) {
                        elements.add(new LeonardoStyleBO.Element(akUUID, weight));
                    }
                }
                if (imgStyle.getModelId() != null && !imgStyle.getModelId().isEmpty()) {
                    leonardoStyleBO.setModelId(imgStyle.getModelId());
                } else {
                    leonardoStyleBO.setModelId(null);
                }
                if (imgStyle.getPresetStyle() != null && !imgStyle.getPresetStyle().isEmpty()) {
                    leonardoStyleBO.setPresetStyle(imgStyle.getPresetStyle());
                } else {
                    leonardoStyleBO.setPresetStyle(null);
                }
                if (imgStyle.getPhotoReal() != null && imgStyle.getPhotoReal() == true) {
                    leonardoStyleBO.setPhotoRealVersion("v2");
                }
                leonardoStyleBO.setPrompt(leonardoStyleBO.getPrompt().concat((imgStyle.getKeyword() == null ? "" : imgStyle.getKeyword())));
            }
        }
        leonardoStyleBO.setElements(elements);
        imgDrawRecordPO.setWhDivide(whDecide);
        imgDrawRecordPO.setPromptUse(imgDrawRecordPO.getPromptUse().concat(" LE --ar ").concat(ar));

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
        imgDrawHistoryVO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_FOUR.getValue());
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());

        // TODO 扣除用户点子 ===== 查询点子规则数据
        boolean isMember = iUserDDRecordService.getUserIsVip();
        double leDrawDeductQua = BDDUseNumEnum.getDDUseByIsVip(BDDUseNumEnum.LE_DRAW, isMember);
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        ;
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), leDrawDeductQua, flowRecordSub);
        imgDrawRecordPO.setUseDdQua(leDrawDeductQua);

        try {
            String leJobId = null;
            List<String> imageList = null;
            if (imgDrawDTO.getIsAddImage() != null && imgDrawDTO.getIsAddImage() && imgDrawDTO.getMjAddImageBO() != null) {
                List<LeonardoParamBO> leonardoParamBOS = getBeddingParam(imgDrawDTO.getMjAddImageBO(), apiKey);
                imageList = leonardoParamBOS.stream().map(LeonardoParamBO::getImageUrl).toList();
                leonardoStyleBO.setControlnets(getControlnets(leonardoParamBOS));
                leJobId = LeonardoUtil.postLeonardoTextToImageTest(leonardoStyleBO, apiKey);
            } else {
                leJobId = LeonardoUtil.postLeonardoTextToImage(leonardoStyleBO, apiKey);
            }
            if (leJobId == null) {
                throw new IBusinessException("Le绘图失败");
            }
            imgDrawRecordPO.setLeJobId(leJobId);
            if (imgDrawDTO.getIsAddImage() != null && imgDrawDTO.getIsAddImage() && imgDrawDTO.getMjAddImageBO() != null) {
                imgDrawRecordPO.setInitImgUrls(imageList == null ? null : JSON.toJSONString(imageList));
                imgDrawRecordPO.setInitImgObject(JSON.toJSONString(imgDrawDTO.getMjAddImageBO()));
            }
            if (imgDrawRecordMapper.insert(imgDrawRecordPO) < 0) {
                throw new IBusinessException("绘画记录保存失败");
            }
            return Result.SUCCESS(imgDrawHistoryVO);

        } catch (Exception e) {
            log.error("==++==LE绘图失败= {}", e.getMessage());
            dzRollbackService.rollback(imgDrawRecordPO.getUserId(), leDrawDeductQua, optTitleOne.concat(optTitleTwo != null ? optTitleTwo : ""));
            imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                    .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                    .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
        }
        log.error("jobLe=LE绘图失败。");
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }

    /**
     * FLUX绘图
     * @param isVip 是否为vip用户
     * @param imgDrawDTO 绘图请求参数
     * @param imgModelConfigVO 模型配置
     * @return 结果
     * @throws IBusinessException 业务异常
     */
    private Result<Object> jobFlux(int isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        imgDrawDTO.setPromptUse(imgDrawDTO.getPrompt());
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecordDe(imgDrawDTO, imgModelConfigVO);
        imgDrawRecordPO.setDescription("FLUX draw " + imgDrawDTO.getPrompt());
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        if (isVip > 0) {
            imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue());
        } else {
            imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue());
        }

        // 装载宽高尺寸
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());

        //初始化尺寸
        String ar = "1:1";
        double whDecide = 1;
        List<ImgScaleDTO> imgScales = imgModelConfigVO.getImgScales();
        for (ImgScaleDTO imgScale : imgScales) {
            if (Objects.equals(imgScale.getKey(), imgDrawDTO.getImgScaleKey())) {
                ar = imgDrawDTO.getImgScaleIsTrue() ? imgScale.getWidth() + ":" + imgScale.getHeight() : imgScale.getHeight() + ":" + imgScale.getWidth();
                whDecide = imgDrawDTO.getImgScaleIsTrue() ?
                        (new BigDecimal(String.valueOf(imgScale.getWidth())).divide(new BigDecimal(String.valueOf(imgScale.getHeight())), 6, RoundingMode.HALF_UP).doubleValue()) :
                        (new BigDecimal(String.valueOf(imgScale.getHeight())).divide(new BigDecimal(String.valueOf(imgScale.getWidth())), 6, RoundingMode.HALF_UP).doubleValue());
            }
        }
        int[] ratio = BLEDrowSizeEnum.getFluxWidthAndHeight(ar);
        imgDrawRecordPO.setWhDivide(whDecide);
        imgDrawRecordPO.setPromptUse(imgDrawRecordPO.getPromptUse().concat(" FLUX --ar ").concat(ar));

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
        imgDrawHistoryVO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());

        // TODO 扣除用户点子 ===== 查询点子规则数据
        boolean isMember = iUserDDRecordService.getUserIsVip();
        double fluxDrawDeductQua = BDDUseNumEnum.getDDUseByIsVip(BDDUseNumEnum.FLUX_DRAW, isMember);
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        ;
        if (isMember) {
            checkBalanService.checkUserNew(imgDrawRecordPO.getUserId(), fluxDrawDeductQua, flowRecordSub);
        } else {
            checkBalanService.checkUser(imgDrawRecordPO.getUserId(), fluxDrawDeductQua, flowRecordSub);
        }
        imgDrawRecordPO.setUseDdQua(fluxDrawDeductQua);

        try {
            FluxResParamBO fluxResParamBO = FluxApiUtil.postFluxTextToImage(imgDrawDTO.getPrompt(), ratio[0] + "*" + ratio[1]);
            if (fluxResParamBO == null || fluxResParamBO.getOutput() == null || fluxResParamBO.getOutput().getTaskId() == null) {
                throw new IBusinessException("FLUX绘图失败");
            }
            imgDrawRecordPO.setLeJobId(fluxResParamBO.getOutput().getTaskId());
            if (imgDrawRecordMapper.insert(imgDrawRecordPO) < 0) {
                throw new IBusinessException("绘画记录保存失败");
            }
            return Result.SUCCESS(imgDrawHistoryVO);

        } catch (Exception e) {
            log.error("==++==FLUX绘图失败= {}", e.getMessage());
            dzRollbackService.rollback(imgDrawRecordPO.getUserId(), fluxDrawDeductQua, optTitleOne.concat(optTitleTwo != null ? optTitleTwo : ""));
            imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                    .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                    .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
        }
        log.error("jobFlux=FLUX绘图失败。");
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }

    /**
     * 即梦绘图，提交海报生成任务
     * @param isVip 是否为vip用户
     * @param imgDrawDTO 绘图请求参数
     * @param imgModelConfigVO 模型配置
     * @return 结果
     * @throws IBusinessException 业务异常
     */
    private Result<Object> jobJiMeng(int isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        imgDrawDTO.setPromptUse(imgDrawDTO.getPrompt());
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecordDe(imgDrawDTO, imgModelConfigVO);
        imgDrawRecordPO.setDescription("Jimeng 2.1 draw " + imgDrawDTO.getPrompt());
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        if (isVip > 0) {
            imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue());
        } else {
            imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue());
        }

        // 装载宽高尺寸
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());

        //初始化尺寸
        String ar = "1:1";
        double whDecide = 1;
        List<ImgScaleDTO> imgScales = imgModelConfigVO.getImgScales();
        for (ImgScaleDTO imgScale : imgScales) {
            if (Objects.equals(imgScale.getKey(), imgDrawDTO.getImgScaleKey())) {
                ar = imgDrawDTO.getImgScaleIsTrue() ? imgScale.getWidth() + ":" + imgScale.getHeight() : imgScale.getHeight() + ":" + imgScale.getWidth();
                whDecide = imgDrawDTO.getImgScaleIsTrue() ?
                        (new BigDecimal(String.valueOf(imgScale.getWidth())).divide(new BigDecimal(String.valueOf(imgScale.getHeight())), 6, RoundingMode.HALF_UP).doubleValue()) :
                        (new BigDecimal(String.valueOf(imgScale.getHeight())).divide(new BigDecimal(String.valueOf(imgScale.getWidth())), 6, RoundingMode.HALF_UP).doubleValue());
            }
        }
        int[] ratio = BLEDrowSizeEnum.getJiMengWidthAndHeight(ar);
        imgDrawRecordPO.setWhDivide(whDecide);
        imgDrawRecordPO.setPromptUse(imgDrawRecordPO.getPromptUse().concat(" JIMENG 2.1 --ar ").concat(ar));

        boolean isMember = isVip > 0;
        double jimengDrawDeductQua = 0;
        if (imgDrawDTO.getImgNumberKey() == null) {
            jimengDrawDeductQua = BDDUseNumEnum.getDDUseByIsVip(BDDUseNumEnum.JIMENG_DRAW, isMember);
            imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_FOUR.getValue());
        } else {
            List<ImgNumberDTO> imgNumbers = imgModelConfigVO.getImgNumber();
            ImgNumberDTO imgNumberDTO = imgNumbers.stream().filter(imgNumber -> imgNumber.getKey() == imgDrawDTO.getImgNumberKey()).findFirst().orElse(null);
            if (imgNumberDTO == null) {
                return Result.ERROR(CommonResultEnum.IMG_NUMBER_ERROR.getValue());
            }
            imgDrawRecordPO.setImgQuantity(Integer.valueOf(imgNumberDTO.getValue()));
            jimengDrawDeductQua = (isMember ? Double.parseDouble(imgNumberDTO.getDdVipUseNumStr()) : Double.parseDouble(imgNumberDTO.getDdUseNumStr()));
        }

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
        imgDrawHistoryVO.setImgQuantity(imgDrawRecordPO.getImgQuantity());
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());

        // TODO 扣除用户点子 ===== 查询点子规则数据
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), jimengDrawDeductQua, flowRecordSub);
        imgDrawRecordPO.setUseDdQua(jimengDrawDeductQua);

        try {
            PosterReqBO posterReqBO = new PosterReqBO();
            posterReqBO.setReqkey("high_aes_general_v21_L");
            posterReqBO.setPrompt(imgDrawDTO.getPromptUse());
            posterReqBO.setModelVersion("general_v2.1_L");
            posterReqBO.setReqScheduleConf("general_v20_9B_pe");
            posterReqBO.setScale(3.5F);
            posterReqBO.setDdimSteps(25);
            posterReqBO.setWidth(ratio[0]);
            posterReqBO.setHeight(ratio[1]);
            posterReqBO.setUsePreLlm(true);
            posterReqBO.setUseSr(true);
            posterReqBO.setReturnRrl(true);

            PosterReqBO.LogoInfo logoInfo = new PosterReqBO.LogoInfo();
            logoInfo.setAddLogo(false);
            posterReqBO.setLogoInfo(logoInfo);
            String taskId = getJiMengTaskIds(posterReqBO, imgDrawRecordPO.getImgQuantity());
            if (taskId == null) {
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            imgDrawRecordPO.setLeJobId(taskId);
            if (imgDrawRecordMapper.insert(imgDrawRecordPO) < 0) {
                throw new IBusinessException("即梦海报绘画记录保存失败");
            }
            return Result.SUCCESS(imgDrawHistoryVO);
        } catch (Exception e) {
            log.error("Jimeng绘图失败= {}", e.getMessage());
            dzRollbackService.rollback(imgDrawRecordPO.getUserId(), jimengDrawDeductQua, optTitleOne.concat(optTitleTwo != null ? optTitleTwo : ""));
            imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                    .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                    .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
            return Result.ERROR("提示词未通过审核，请检查");
        }
    }

    /**
     * 获取流星(即梦)绘图任务ID，获取海报生成任务ID
     * @param posterReqBO 海报生成所需参数对象
     * @param imgQuantity 生成图片数量
     * @return 生成任务的ID，失败返回null或错误状态码
     * @throws Exception 调用过程中可能抛出的异常
     */
    public static String getJiMengTaskIds(PosterReqBO posterReqBO, Integer imgQuantity) {
        ScheduledExecutorService scheduler = null;
        try {
            HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.VOLCANO_POSTER_API_KEY.getDictType());
            String apiKey = dictConfigMap.get(DictConfigEnum.VOLCANO_POSTER_API_KEY.getDictKey());
            if (StringUtils.isBlank(apiKey)) {
                log.info("即梦绘图没有可用的key: {}", "请立即更换");
                throw new Exception("即梦绘图模型升级维护中...");
            }
            scheduler = Executors.newScheduledThreadPool(1);
            List<String> taskIds = new ArrayList<>();
            AtomicInteger count = new AtomicInteger(0);

            ScheduledExecutorService finalScheduler = scheduler;
            Runnable task = new Runnable() {
                @Override
                public void run() {
                    if (count.get() < imgQuantity) { // 确保执行四次
                        String taskId = null;
                        try {
                            taskId = ByteApisUtil.postJimengTextToImageGenerations(posterReqBO);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                        if (taskId == null) {
                            throw new RuntimeException("流星绘图生成失败");
                        }
                        if (taskId.contains(String.valueOf(ByteStatusEnum.BLOCKLIST_INTERCEPTED.getStatus()))) {
                            throw new RuntimeException("提示词未检测通过，修改提示词重试");
                        }
                        taskIds.add(taskId);  // 将 taskId 添加到 list
                        count.incrementAndGet();
                    }
                    if (count.get() >= imgQuantity) {
                        finalScheduler.shutdown();  // 关闭线程池
                    }
                }
            };
            System.out.println("taskIds========================================= = " + taskIds);
            scheduler.scheduleAtFixedRate(task, 0, 1, TimeUnit.SECONDS);
            while (!scheduler.isTerminated()) {
                Thread.sleep(500); // 等待任务执行完毕
            }
            if (taskIds.isEmpty() && taskIds.size() < imgQuantity) {
                return null;
            }
            return String.join(",", taskIds);
        } catch (Exception e) {
            log.error("JIMENG灵感绘画提交任务失败= {}", e.getMessage());
            return null;
        } finally {
            if (scheduler != null && !scheduler.isShutdown()) {
                scheduler.shutdown();
            }
        }
    }

    private Result<Object> jobXingYe(int isVip, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        imgDrawDTO.setPromptUse(BAliYunUtil.textToEnglish(imgDrawDTO.getPrompt()));
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecordDe(imgDrawDTO, imgModelConfigVO);
        imgDrawRecordPO.setDescription("XINGYE draw " + imgDrawDTO.getPrompt());
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        if (isVip > 0) {
            imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue());
        } else {
            imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue());
        }

        // 获取字典配置（Le）
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.XINGYE_API_KEY.getDictType());
        if (dictConfigMap == null) {
            log.info("jobLe=星野没有可用的key: {}", "请立即更换");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        String xyApiKey = dictConfigMap.get(DictConfigEnum.XINGYE_API_KEY.getDictKey());
        if (xyApiKey == null) {
            log.info("jobLe=星野没有可用的key: {}", "请立即更换");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        String[] arrayApiKeys = xyApiKey.split(",");

        // 装载宽高尺寸
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());

        //初始化尺寸
        String ar = "1:1";
        double whDecide = 1;
        List<ImgScaleDTO> imgScales = imgModelConfigVO.getImgScales();
        for (ImgScaleDTO imgScale : imgScales) {
            if (Objects.equals(imgScale.getKey(), imgDrawDTO.getImgScaleKey())) {
                ar = imgDrawDTO.getImgScaleIsTrue() ? imgScale.getWidth() + ":" + imgScale.getHeight() : imgScale.getHeight() + ":" + imgScale.getWidth();
                whDecide = imgDrawDTO.getImgScaleIsTrue() ?
                        (new BigDecimal(String.valueOf(imgScale.getWidth())).divide(new BigDecimal(String.valueOf(imgScale.getHeight())), 6, RoundingMode.HALF_UP).doubleValue()) :
                        (new BigDecimal(String.valueOf(imgScale.getHeight())).divide(new BigDecimal(String.valueOf(imgScale.getWidth())), 6, RoundingMode.HALF_UP).doubleValue());
            }
        }
        int[] ratio = BLEDrowSizeEnum.getZhiPuWidthAndHeight(ar);

        imgDrawRecordPO.setWhDivide(whDecide);
        imgDrawRecordPO.setPromptUse(imgDrawRecordPO.getPromptUse().concat(" XINGYE --ar ").concat(ar));

        boolean isMember = isVip > 0;
        double xingyeDrawDeductQua = 2;
        if (imgDrawDTO.getImgNumberKey() == null) {
            xingyeDrawDeductQua = BDDUseNumEnum.getDDUseByIsVip(BDDUseNumEnum.XINGYE_DRAW, isMember);
            imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_FOUR.getValue());
        } else {
            List<ImgNumberDTO> imgNumbers = imgModelConfigVO.getImgNumber();
            ImgNumberDTO imgNumberDTO = imgNumbers.stream().filter(imgNumber -> imgNumber.getKey() == imgDrawDTO.getImgNumberKey()).findFirst().orElse(null);
            if (imgNumberDTO == null) {
                return Result.ERROR(CommonResultEnum.IMG_NUMBER_ERROR.getValue());
            }
            imgDrawRecordPO.setImgQuantity(Integer.valueOf(imgNumberDTO.getValue()));
            xingyeDrawDeductQua = (isMember ? Double.parseDouble(imgNumberDTO.getDdVipUseNumStr()) : Double.parseDouble(imgNumberDTO.getDdUseNumStr()));
        }

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
        imgDrawHistoryVO.setImgQuantity(imgDrawRecordPO.getImgQuantity());
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setInitImgUrls(imgDrawDTO.getInitImgUrls());

        // TODO 扣除用户点子 ===== 查询点子规则数据
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), xingyeDrawDeductQua, flowRecordSub);
        imgDrawRecordPO.setUseDdQua(xingyeDrawDeductQua);

        try {
            XingLiuRequestBO xingLiuRequestBO = new XingLiuRequestBO();
            XingLiuRequestBO.GenerateParams generateParams = new XingLiuRequestBO.GenerateParams();
            String taskId = null;
            if (imgDrawDTO.getIsAddImage() != null && imgDrawDTO.getIsAddImage()
                    && imgDrawDTO.getMjAddImageBO() != null && imgDrawDTO.getMjAddImageBO().getDefImages() != null) {
                List<MjAddImageBO.MjImageBO> defImgUrl = imgDrawDTO.getMjAddImageBO().getDefImages();
                imgDrawRecordPO.setInitImgUrls(defImgUrl.getFirst().getUrl());
                imgDrawRecordPO.setInitImgObject(JSON.toJSONString(imgDrawDTO.getMjAddImageBO()));

                xingLiuRequestBO.setTemplateUuid("07e00af4fc464c7ab55ff906f8acf1b7");
                generateParams.setPrompt(imgDrawDTO.getPromptUse());
                generateParams.setSourceImage(defImgUrl.getFirst().getUrl());
                XingLiuRequestBO.GenerateParams.ImageSize imageSize = new XingLiuRequestBO.GenerateParams.ImageSize();
                imageSize.setWidth(ratio[0]);
                imageSize.setHeight(ratio[1]);
                generateParams.setImageSize(imageSize);
                generateParams.setImgCount(imgDrawRecordPO.getImgQuantity());
                generateParams.setSteps(30);
                xingLiuRequestBO.setGenerateParams(generateParams);
                xingLiuRequestBO.getGenerateParams().setControlnet(null);
                taskId = XingLiuApiUtil.posXingLiuImgToImageGenerate(xingLiuRequestBO, arrayApiKeys[0], arrayApiKeys[1]);
            } else {
                xingLiuRequestBO.setTemplateUuid("5d7e67009b344550bc1aa6ccbfa1d7f4");
                generateParams.setPrompt(imgDrawDTO.getPromptUse());
                XingLiuRequestBO.GenerateParams.ImageSize imageSize = new XingLiuRequestBO.GenerateParams.ImageSize();
                imageSize.setWidth(ratio[0]);
                imageSize.setHeight(ratio[1]);
                generateParams.setImageSize(imageSize);
                generateParams.setImgCount(imgDrawRecordPO.getImgQuantity());
                generateParams.setSteps(30);
                xingLiuRequestBO.setGenerateParams(generateParams);
                xingLiuRequestBO.getGenerateParams().setControlnet(null);
                taskId = XingLiuApiUtil.posXingLiuTextToImageGenerate(xingLiuRequestBO, arrayApiKeys[0], arrayApiKeys[1]);
            }
            if (taskId == null) {
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            imgDrawRecordPO.setLeJobId(taskId);
            if (imgDrawRecordMapper.insert(imgDrawRecordPO) < 0) {
                throw new IBusinessException("星野绘画记录保存失败");
            }
            return Result.SUCCESS(imgDrawHistoryVO);
        } catch (Exception e) {
            log.error("星野绘图失败= {}", e.getMessage());
            dzRollbackService.rollback(imgDrawRecordPO.getUserId(), xingyeDrawDeductQua, optTitleOne.concat(optTitleTwo != null ? optTitleTwo : ""));
            imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                    .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                    .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
        }
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }

    @NotNull
    private static List<LeonardoStyleBO.Controlnets> getControlnets(List<LeonardoParamBO> leonardoParamBOS) {
        List<LeonardoStyleBO.Controlnets> controlnets = new ArrayList<>();
        for (LeonardoParamBO leonardoParamBO : leonardoParamBOS) {
            LeonardoStyleBO.Controlnets controlnet = new LeonardoStyleBO.Controlnets();
            controlnet.setInfluence(leonardoParamBO.getInfluence());
            controlnet.setInitImageId(leonardoParamBO.getImageId());
            controlnet.setInitImageType("UPLOADED");
            controlnet.setPreprocessorId(leonardoParamBO.getPreprocessorId());
            controlnet.setStrengthType(leonardoParamBO.getStrengthType());
            controlnets.add(controlnet);
        }
        return controlnets;
    }

    private List<LeonardoParamBO> getBeddingParam(MjAddImageBO mjAddImageBO, String apiKey) throws Exception {
        System.out.println(JSONObject.toJSONString(mjAddImageBO));
        List<LeonardoParamBO> leonardoParamBOList = new ArrayList<>();
        if (mjAddImageBO.getSrefImages() != null && !mjAddImageBO.getSrefImages().isEmpty()) { //风格
            Map<String, Object> objectMap = LeonardoStyleBO.getLePreprocessorId("S", mjAddImageBO.getSrefWeight());
            if (objectMap == null) {
                throw new IBusinessException("风格参数错误");
            }
            Double influence = null;
            if (mjAddImageBO.getSrefImages().size() > 1) {
                influence = 0.50;
            }
            for (MjAddImageBO.MjImageBO mjImageBO : mjAddImageBO.getSrefImages()) {
                String imageId = uploadImgToLe(mjImageBO.getUrl(), apiKey);
                leonardoParamBOList.add(new LeonardoParamBO((Integer) objectMap.get("preprocessorId"), (String) objectMap.get("strengthType"), imageId, influence, mjImageBO.getUrl()));
            }
        }
        if (mjAddImageBO.getDefImages() != null && !mjAddImageBO.getDefImages().isEmpty()) { //垫图
            Map<String, Object> objectMap = LeonardoStyleBO.getLePreprocessorId("D", mjAddImageBO.getDefWeight());
            if (objectMap == null) {
                throw new IBusinessException("参考参数错误");
            }
            for (MjAddImageBO.MjImageBO mjImageBO : mjAddImageBO.getDefImages()) {
                String imageId = uploadImgToLe(mjImageBO.getUrl(), apiKey);
                leonardoParamBOList.add(new LeonardoParamBO((Integer) objectMap.get("preprocessorId"), (String) objectMap.get("strengthType"), imageId, null, mjImageBO.getUrl()));
            }
        }
        if (mjAddImageBO.getCrefImages() != null && !mjAddImageBO.getCrefImages().isEmpty()) { //角色
            Map<String, Object> objectMap = LeonardoStyleBO.getLePreprocessorId("C", mjAddImageBO.getCrefWeight());
            if (objectMap == null) {
                throw new IBusinessException("角色参数错误");
            }
            for (MjAddImageBO.MjImageBO mjImageBO : mjAddImageBO.getCrefImages()) {
                String imageId = uploadImgToLe(mjImageBO.getUrl(), apiKey);
                leonardoParamBOList.add(new LeonardoParamBO((Integer) objectMap.get("preprocessorId"), (String) objectMap.get("strengthType"), imageId, null, mjImageBO.getUrl()));
            }
        }
        return leonardoParamBOList;
    }

    private String uploadImgToLe(String imgUrl, String apiKey) throws Exception {
        String url = imgUrl + "?x-oss-process=image/format,png/resize,w_1024,h_1024";
        byte[] imageData = BFileUtil.downloadImageGetByte(url);
        Map<String, String> stringMap = LeonardoUtil.getPresignedUrlUploadingImg(apiKey);
        if (stringMap == null) {
            throw new IBusinessException("图片初始化失败,请重试");
        }
        boolean isSuccess = LeonardoUtil.presignedUrlUploadingImg(stringMap.get("url"), stringMap.get("fields"), imageData);
        if (!isSuccess) {
            throw new IBusinessException("图片上传失败,请重试");
        }
        return stringMap.get("imageId");
    }


    private static ImgDrawHistoryVO intiImgDrawHistoryVO(ImgDrawRecordPO imgDrawRecordPO) {
        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
        imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawHistoryVO.setPrompt(imgDrawRecordPO.getPromptInit());
        imgDrawHistoryVO.setPromptUse(imgDrawRecordPO.getPromptUse());
        imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
        imgDrawHistoryVO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        return imgDrawHistoryVO;
    }

    /**
     * 初始化任务
     */
    private ImgDrawRecordPO initImgDrawRecord(ImgDrawRecordPO imgDrawRecordPO, ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO, MJAccountHeaderBO mjAccountHeaderBO) throws IBusinessException {
        imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.MJ_OPT_ATTRIBUTE_DRAW.getValue());
        // imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.DALLE_OPT_ATTRIBUTE_DRAW.getValue()); 当mj编辑不好用 用这个
        imgDrawRecordPO.setModeAttribute(imgModelConfigVO.getAttribute());
        imgDrawRecordPO.setUserId(JwtNewUtil.getUserId());
        imgDrawRecordPO.setPromptInit(imgDrawDTO.getPrompt());
        imgDrawRecordPO.setPromptUse((imgDrawDTO.getPromptUse() == null ? "" : imgDrawDTO.getPromptUse()) + " " + mjAccountHeaderBO.getCompletePrompt());
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(mjAccountHeaderBO));
        return imgDrawRecordPO;
    }

    /**
     * 初始化任务de
     */
    private ImgDrawRecordPO initImgDrawRecordDe(ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setId(IdWorker.getId());
        imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.DALLE_OPT_ATTRIBUTE_DRAW.getValue());
        imgDrawRecordPO.setModeAttribute(imgModelConfigVO.getAttribute());
        imgDrawRecordPO.setUserId(JwtNewUtil.getUserId());
        imgDrawRecordPO.setPromptInit(imgDrawDTO.getPrompt());
        imgDrawRecordPO.setPromptUse(imgDrawDTO.getPrompt());
        imgDrawRecordPO.setDescription("dall-e-3 draw " + imgDrawDTO.getPrompt());
        return imgDrawRecordPO;
    }

    /**
     * 初始化任务sd 请勿删除 有用
     */
    private ImgDrawRecordPO initImgDrawRecordSd(ImgDrawDTO imgDrawDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setId(IdWorker.getId());
        // imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.SD_OPT_ATTRIBUTE_DRAW.getValue());
        imgDrawRecordPO.setModeAttribute(imgModelConfigVO.getAttribute());
        imgDrawRecordPO.setUserId(imgDrawDTO.getUserId());
        imgDrawRecordPO.setPromptInit(imgDrawDTO.getPrompt());
        imgDrawRecordPO.setPromptUse(imgDrawDTO.getPromptUse());
        imgDrawRecordPO.setDescription("sd-3 draw " + imgDrawDTO.getPrompt());
        return imgDrawRecordPO;
    }

    /**
     * 旧版 查看会员及校验并发数量：null超出数量，有值为并发数量值
     * 绘图调用1
     * 图片编辑调用2
     */
    private Integer checkConcurrentCount() throws IBusinessException {
        // 查询用户vip记录
        UserDDRecordPO userDDRecordPO = userDDRecordMapper.selectOne(
                new LambdaQueryWrapper<UserDDRecordPO>()
                        .eq(UserDDRecordPO::getUserId, JwtNewUtil.getUserId())
                        .eq(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_PAY.getIntValue())
                        .in(UserDDRecordPO::getTypeItem, UserDDrecordEnum.TYPE_ITEM_PAY_VIP.getIntValue(), UserDDrecordEnum.TYPE_ITEM_PAY_SVIP.getIntValue())
                        .gt(UserDDRecordPO::getExpirationTime, DateUtil.getDateNowShanghai())
                        .orderByDesc(UserDDRecordPO::getExpirationTime)
                        .last("limit 0,1")
        );
        // 校验用户并发数量：设置默认并发数量为1
        int concurrentCount = 1;
        // 原来来的并发数获取接口
        VipGradeBO vipGradeBO = null;
        if (userDDRecordPO != null && Objects.equals(userDDRecordPO.getTypeItem(), UserDDrecordEnum.TYPE_ITEM_PAY_VIP.getIntValue())) {
            vipGradeBO = JSONObject.parseObject((String) RedisUtil.getValue(BRedisKeyEnum.VIP_GRADE_VIP.getKey()), VipGradeBO.class);
        }
        if (userDDRecordPO != null && Objects.equals(userDDRecordPO.getTypeItem(), UserDDrecordEnum.TYPE_ITEM_PAY_SVIP.getIntValue())) {
            vipGradeBO = JSONObject.parseObject((String) RedisUtil.getValue(BRedisKeyEnum.VIP_GRADE_SVIP.getKey()), VipGradeBO.class);
        }
        if (vipGradeBO != null && vipGradeBO.getDrawConcurrency() > 0) {
            concurrentCount = vipGradeBO.getDrawConcurrency();
        }

        // 查询并发数量
        long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getUserId, JwtNewUtil.getUserId())
                        .in(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_QUEUING.getValue(), ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
        );
        if (count >= concurrentCount) {
            return null;
        }
        //校验并发是否存在
        return concurrentCount;
    }

    /**
     * 检查是否为 mj 接口锁是否阻塞，阻塞直接返回错误
     */
    private boolean isNotMJLock() {
        //创建锁：最大重试次数=3；超时释放时间=120秒
        int maxRetryAttempts = 3;
        boolean lockAcquired = false;
        int retryAttempts = 0;
        long waitTime = 1000; // 等待时间（例如，2000毫秒，即2秒）

        while (!lockAcquired && retryAttempts <= maxRetryAttempts) {
            lockAcquired = RedisUtil.acquireLock(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_LOCK.getStrKey(), 120);
            if (!lockAcquired) {
                retryAttempts++;

                try {
                    // 等待指定时间再重试
                    Thread.sleep(waitTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // Restore the interrupted status
                    log.error("isNotMJLock= Thread interrupted while waiting to retry lock acquisition", e);
                    break;
                }

            }
        }
        return !lockAcquired;
    }

    /**
     * 图片编辑
     *
     * @param imgDrawOptDTO 用户参数
     * @return
     */
    @Override
    public Result<Object> submitOpt(ImgDrawOptDTO imgDrawOptDTO) throws IBusinessException {
        // 注意事项：每次新增操作功能时：注意更新 checkKeyIsInvalid 方法内的校验信息
        if (ImgDrawUtil.checkKeyIsInvalid(imgDrawOptDTO.getOperate())) {
            log.error("submitOpt=参数无效");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        log.info("===++===imgDrawOptDTO= {}", imgDrawOptDTO);
        //1、校验操作详情是否存在
        ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectById(imgDrawOptDTO.getImgDrawDetlId());
        if (imgDrawDetlPO == null) {
            log.error("submitOpt=imgDrawDetlPO操作详情不存在");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectById(imgDrawDetlPO.getDrawRecordId());
        if (imgDrawRecordPO == null) {
            log.error("submitOpt=imgDrawRecordPO操作记录不存在");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        //校验 角色图片列表 || 风格图片列表 是否支持
        if (imgDrawOptDTO.getVaryRegionUrl() != null && imgDrawOptDTO.getVaryRegionUrl().getVrUrl() != null) {
            initMjAddImageBO(imgDrawOptDTO);
            if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue()
                    || imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue()) {
                if (imgDrawOptDTO.getMjAddImageBO() != null
                        && imgDrawOptDTO.getMjAddImageBO().getCrefImages() != null
                        && !imgDrawOptDTO.getMjAddImageBO().getCrefImages().isEmpty()) {
                    return Result.ERROR(CommonResultEnum.DRAW_MJ_CREF_ERROR.getValue());
                }
                if (imgDrawOptDTO.getMjAddImageBO() != null
                        && imgDrawOptDTO.getMjAddImageBO().getSrefImages() != null
                        && !imgDrawOptDTO.getMjAddImageBO().getSrefImages().isEmpty()) {
                    return Result.ERROR(CommonResultEnum.DRAW_MJ_SREF_ERROR.getValue());
                }
            }
        }

        //校验是否存在可操作权限
        Integer modeAttribute = ImgDrawPUtil.getModeAttributeByOpt(imgDrawDetlPO.getModeAttribute(), imgDrawOptDTO.getOperate());
        //Integer optAttribute = ImgDrawPUtil.getOptAttributeByOpt(imgDrawDetlPO.getModeAttribute(),imgDrawDetlPO.getOptAttribute(),imgDrawOptDTO.getOperate());
        Integer optAttribute = ImgDrawPUtil.getOptAttributeByOptNew(imgDrawDetlPO.getModeAttribute(), imgDrawDetlPO.getOptAttribute(), imgDrawOptDTO.getOperate());
        if (modeAttribute == null) {
            log.error("submitOpt=modeAttribute操作权限不存在");
            log.info("校验是否存在可操作权限, modeAttribute= {}, optAttribute= {}", modeAttribute, optAttribute);
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        //2、校验并发数量
        Integer checkConcurrentCount = checkService.checkConcurrentCount();
        if (checkConcurrentCount == null) {
            return Result.ERROR(CommonResultEnum.TASK_CONCURRENT_COUNT.getValue());
        }
        //无需校验vip，全部走慢速逻辑
        //1、mj属性并且可以进行mj原生操作 校验跳转mj接口执行n5、n6；v5、v6
        if (imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue()
                || imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N6.getValue()
                || imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue()
                || imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue()
                || imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6_1.getValue()
        ) {
            //局部修改、缩放、低变化、高变化、上拓展、下拓展、左拓展、右拓展、方形拓展、放大2倍、放大4倍
            if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_REGION.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_SUBTLE.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_STRONG.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_TOP.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_BOTTOM.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_LEFT.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_RIGHT.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM_MAKE.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_2X.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_4X.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_CHANGE_AR.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_FINE_TUNING_REMIX.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_FINE_TUNING_REMIX_SUBTLE.getValue()
            ) {
                //执行mj原生操作
                return optMj(imgDrawOptDTO, imgDrawRecordPO, imgDrawDetlPO, modeAttribute, optAttribute);
            }
            if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_FACE_FUSION.getValue()) {
                // TODO 进行三方换脸操作：异步调用换脸接口:1:检测人脸; 2:人脸融合  facialFusion
                return optFace(imgDrawOptDTO, imgDrawRecordPO, imgDrawDetlPO, modeAttribute, optAttribute);
            }
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        //2、DE模型、变体模型：目前仅支持换脸
        if (imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_DE.getValue()
                || imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_VARIANT.getValue()
        ) {
            if (ImgOptModelEnum.OPERATE_EDIT_FACE_FUSION.getValue() != imgDrawOptDTO.getOperate()) {
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            // TODO 进行三方换脸操作：异步调用换脸接口:1:检测人脸; 2:人脸融合
            return optFace(imgDrawOptDTO, imgDrawRecordPO, imgDrawDetlPO, modeAttribute, optAttribute);
        }

        return Result.SUCCESS();
    }

    @Override
    public Result<Object> imgSave(Long imgDrawId) throws IBusinessException {
        //TODO 保存图片：1校验图片是否下载完成，未下载则保存到oss然后返回
        ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectOne(
                new LambdaQueryWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getId, imgDrawId)
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
        if (imgDrawDetlPO == null || ObjectUtil.isEmpty(imgDrawDetlPO) || imgDrawDetlPO.getImgUrl() == null || imgDrawDetlPO.getImgUrl().isEmpty()) {
            log.error("imgSave=图片不存在==");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        if (imgDrawDetlPO.getImgUrl().contains("midjourney.com")) {
            String imgBase64 = ImageUtil.getImgUrlToBate64(imgDrawDetlPO.getImgUrl());
            if (imgBase64 == null) {
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            OssParamBO ossParamBO = OSSApis.postOssUrl(imgBase64, CommonUtil.getImageName(imgDrawDetlPO.getImgUrl()), 1);
            if (ossParamBO != null) {
                if (imgDrawDetlMapper.update(null, new LambdaUpdateWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getId, imgDrawDetlPO.getId())
                        .set(ImgDrawDetlPO::getImgUrl, ossParamBO.getImageUrl())
                        .set(ImgDrawDetlPO::getImgWidth, ossParamBO.getImageWidth())
                        .set(ImgDrawDetlPO::getImgHeight, ossParamBO.getImageHeight())
                        .set(ImgDrawDetlPO::getImgSize, ossParamBO.getFileSize())
                        .set(ImgDrawDetlPO::getImgType, "image/webp")) > 0) {
                    return Result.SUCCESS(CommonStrEnum.IMAGE_PREFIX.getValue().concat(ossParamBO.getImageUrl()));
                }
            }
        }
        if (imgDrawDetlPO.getImgUrl().contains("https")) {
            return Result.SUCCESS(imgDrawDetlPO.getImgUrl());
        }
        return Result.SUCCESS(CommonStrEnum.IMAGE_PREFIX.getValue().concat(imgDrawDetlPO.getImgUrl()));
    }

    @Override
    public Result<Object> imgDrawRecordDelete(Long imgDrawId) {
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectOne(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawId)
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
        if (imgDrawRecordPO == null || ObjectUtil.isEmpty(imgDrawRecordPO)) {
            return Result.SUCCESS();
        }
        if (imgDrawRecordMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawId)
                        .set(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
        ) < 1) {
            return Result.ERROR(CommonResultEnum.HISTORY_DELETION_ERROR.getValue());
        }
        List<ImgDrawDetlPO> imgDrawDetlPOList = imgDrawDetlMapper.selectList(
                new LambdaQueryWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawId)
        );

        if (imgDrawDetlPOList == null || imgDrawDetlPOList.isEmpty()) {
            return Result.SUCCESS();
        }
        if (imgDrawDetlMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawId)
                        .set(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())

        ) < 1) {
            return Result.ERROR(CommonResultEnum.HISTORY_DELETION_ERROR.getValue());
        }
        return Result.SUCCESS();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> imgDrawDetlDelete(Long imgDrawDetlId) {
        //根据imgDrawDetlId查询imgDrawRecord状态
        ImgDrawRecordPO recordPO = imgDrawRecordMapper.selectListByDetlId(imgDrawDetlId);
        if (recordPO.getStatus() == ImgDrawEnum.STATUS_IN_PROGRESS.getValue()) {
            return Result.ERROR(CommonResultEnum.HISTORY_PROGRESS_ERROR.getValue());
        }
        //查询详情是否存在
        ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectOne(
                new LambdaQueryWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getId, imgDrawDetlId)
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
        if (imgDrawDetlPO == null || ObjectUtil.isEmpty(imgDrawDetlPO)) {
            return Result.SUCCESS();
        }
        if (imgDrawDetlMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawDetlPO::getId, imgDrawDetlId)
                        .set(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
        ) < 1) {
            return Result.ERROR(CommonResultEnum.HISTORY_DELETION_ERROR.getValue());
        }
        //查询任务列表详情是否存在
        List<ImgDrawDetlPO> imgDrawDetlPOList = imgDrawDetlMapper.selectList(
                new LambdaQueryWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawDetlPO.getDrawRecordId())
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
        if (imgDrawDetlPOList != null && !imgDrawDetlPOList.isEmpty()) {
            return Result.SUCCESS();
        }
        if (imgDrawRecordMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawDetlPO.getDrawRecordId())
                        .set(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
        ) < 1) {
            return Result.ERROR(CommonResultEnum.HISTORY_DELETION_ERROR.getValue());
        }
        return Result.SUCCESS();
    }

    private Result<Object> optFace(ImgDrawOptDTO imgDrawOptDTO, ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO, Integer modeAttribute, Integer optAttribute) {
        HashMap<String, String> map = new HashMap<>();
        map.put("url", CommonStrEnum.IMAGE_PREFIX.getValue() + imgDrawDetlPO.getImgUrl());
        FaceDetectionResponseBodyVO faceResBody = AiFaceUtil.detectionFacial(map);
        if (ObjectUtil.isNotEmpty(faceResBody) || faceResBody.getFace_num() == null || faceResBody.getFace_num() >= 2 || !faceResBody.getIs_face()) {
            return Result.ERROR("当前任务不支持换脸");
        }

        ImgFaceTemplatePO imgFaceTemplatePO = imgFaceTemplateService.getById(imgDrawOptDTO.getFaceTemplateId());
        imgDrawRecordPO.setId(IdWorker.getId());
        imgDrawRecordPO.setOptAttribute(optAttribute);
        imgDrawRecordPO.setModeAttribute(modeAttribute);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(ImgDrawEnum.IMG_NUMBER_ONE.getValue());

        //判断用户点子数是否充足
//        String pointRule = FlowRecordEnum.AIFACES.getRemark();
//        RuleConfigBO ruleConfig = RedisUtil.getCacheObject(CommonConst.SYS_RULE_CONFIG);
//        MjFlowRecordPO flowRecordSub = MjFlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(pointRule).build();
//        //Long orderId = checkBalanService.checkUser(taskPO.getUserId(), ruleConfig.getAiFaceDeductQua(), flowRecordSub);
        MergeFaceRequestBodyDTO mergeFaceReqBodyDTO = new MergeFaceRequestBodyDTO();
        mergeFaceReqBodyDTO.setFaceImageUrl(imgFaceTemplatePO.getImgUrl());
        mergeFaceReqBodyDTO.setImageFileUrl(CommonStrEnum.IMAGE_PREFIX.getValue() + imgDrawDetlPO.getImgUrl());
//        MjFlowRecordPO flowRecordPlus = MjFlowRecordPO.builder().recordType(CommonEnum.COMM_ZERO.getValue()).remark(pointRule).build();
//        long startTime = System.currentTimeMillis();

        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
        imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            // asyncService.asynExecutionFacialFusion(imgDrawRecordPO, mergeFaceReqBodyDTO, imgDrawHistoryVO);
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        return Result.ERROR("换脸失败，请重试");
    }

    private Result<Object> optMj(@NotNull ImgDrawOptDTO imgDrawOptDTO, ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO, Integer modeAttribute, Integer optAttribute) throws IBusinessException {
        // 判断账号是否锁定了
        String accountLock = RedisUtil.getValue(BRedisKeyEnum.MJ_ACCOUNT_LOCK.getKey());
        if (Boolean.parseBoolean(accountLock)) {
            log.error("optMj=账号被锁定，请稍后再试, 预计1分钟");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        JobInfo jobInfo = null;
        MJAccountHeaderBO mjAccountHeaderBO;
        Integer imgDrawRecordPOPdMjIsRelaxed;
        Long drawRecordId = IdWorker.getId();
        try {
            if (isNotMJLock()) {
                log.error("optMj=账号被锁定，请稍后再试, 预计1分钟");
            }

            List<AdminMjWebConfigBO> accountList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE.getKey()), new TypeReference<List<AdminMjWebConfigBO>>() {
            });
            if (accountList == null || accountList.isEmpty()) {
                accountList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE_TURTLE.getKey()), new TypeReference<List<AdminMjWebConfigBO>>() {
                });
            /* if (accountList != null && !accountList.isEmpty()) {
                    accountList = accountList.stream().filter(account -> (account.getPriorityLevel() != null && account.getPriorityLevel() == 2)).toList();
                }*/
            }
            /*else {
                accountList = accountList.stream().filter(account -> (account.getPriorityLevel() != null && account.getPriorityLevel() == 2)).toList();
                if (accountList == null || accountList.isEmpty()) {
                    accountList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE_TURTLE.getKey()), new TypeReference<List<AdminMjWebConfigBO>>(){});
                    if (accountList != null && !accountList.isEmpty()) {
                        accountList = accountList.stream().filter(account -> (account.getPriorityLevel() != null && account.getPriorityLevel() == 2)).toList();
                    }
                }
            }*/
            System.out.println("accountList" + accountList);
            if (accountList == null || accountList.isEmpty()) {
                log.error("optMj=账号池为空，请稍后再试-1");
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());

            }
            // 如果是微調高變化則需要60美金賬號，30美金賬號不支持微調高變化
            /*if (optAttribute == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_REMIX_SUBTLE.getValue()) {
                accountList = accountList.stream().filter(account -> (null == account.getPriorityLevel() ? 2 : account.getPriorityLevel()) == 2).toList();
            }*/

            int randomIndex = ThreadLocalRandom.current().nextInt(accountList.size());
            AdminMjWebConfigBO randomAccount = accountList.get(randomIndex);

            /*Random random = new Random();
            int randomIndex = random.nextInt(accountList.size());
            AdminMjWebConfigBO randomAccount = accountList.get(randomIndex);*/
            // AdminMjWebConfigBO nextAccount = getNextAccount(accountList);
            if (randomAccount == null || randomAccount.getId() == null) {
                log.error("optMj=账号池为空，请稍后再试-2");
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }

            //默认快速账号，出现问题时自动切换
            //校验账号是否能使用
            imgDrawRecordPOPdMjIsRelaxed = BMJStopUsingUtil.isMjAccountSpeed(randomAccount.getAccountSpeed());
            if (imgDrawRecordPOPdMjIsRelaxed == null) {
                log.error("optMj=账号池为空，请稍后再试-3");
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }

            /*if (imgDrawRecordPOPdMjIsRelaxed.equals(CommonIntEnum.IS_FALSE.getIntValue())){
                mjAccountHeaderBO = JSONObject.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PRO_ACCOUNT.getStrKey()), MJAccountHeaderBO.class);
            }else {
                mjAccountHeaderBO = JSONObject.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PT_ACCOUNT.getStrKey()), MJAccountHeaderBO.class);
            }*/

            log.info("imgDrawRecordPO.getDescription= {}", imgDrawRecordPO.getDescription());
            MJAccountHeaderBO mjAccountHeaderBO2 = JSONObject.parseObject(imgDrawRecordPO.getDescription(), MJAccountHeaderBO.class);
            log.info("mjAccountHeaderBO2= {}", mjAccountHeaderBO2);
            mjAccountHeaderBO = new MJAccountHeaderBO();
            mjAccountHeaderBO.setMjAccountId(randomAccount.getId());
            mjAccountHeaderBO.setWebParams(imgDrawRecordPO.getPromptInit());
            mjAccountHeaderBO.setJsonParameter(mjAccountHeaderBO2.getJsonParameter());

            //账号不够用，无法调用mj接口
            /*if (mjAccountHeaderBO.getToken() == null) {
                return Result.ERROR("目前人数较多,请稍后再试");
            }*/

            String jobType = null;
            if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_2X.getValue()
                    || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_4X.getValue()
            ) {
                jobType = MjWebApisUtil.submitJobUpscale(mjAccountHeaderBO2.getJsonParameter(), imgDrawOptDTO.getOperate());
            } else if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_CHANGE_AR.getValue()) {
                jobType = MjWebApisUtil.submitJobUpscalePan(imgDrawOptDTO.getLocation());
            } else {
                jobType = MjWebApisUtil.submitJobUpscaleStrong(imgDrawOptDTO.getOperate());
            }
            if (jobType == null) {
                log.error("optMj=无效的操作类型...");
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            try {
                //校验：放大2倍、放大4倍、放大2倍微变化、放大2倍强变化、
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_2X.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_4X.getValue()
                ) {
                    //调用放大接口
                    // jobInfo = MJApis.submitJobUpscale(mjAccountHeaderBO2.getJsonParameter(), imgDrawOptDTO.getOperate(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), mjAccountHeaderBO);
                    MjRequest mjRequest = new MjRequest();
                    mjRequest.setType(jobType);
                    MjRequestParam mjRequestParam = MjWebApisUtil.createImageRequestParam(mjAccountHeaderBO, new MjHeaderDTO(randomAccount.getChannelId(),
                            randomAccount.getCookie(), randomAccount.getAccountSpeed(), imgDrawRecordPO.getMjJobId(), "upscale", imgDrawDetlPO.getImgIndex(),
                            imgDrawOptDTO.getOperate()), mjRequest);
                    if (mjRequestParam == null || mjRequestParam.getRequest() == null) {
                        log.error("optMj=操作参数错误-1...");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                    mjRequestParam.setImgDrawId(drawRecordId);
                    // RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK,String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam),30, TimeUnit.MINUTES);
                    Object result = MjWebApisUtil.postCreateImage(mjRequestParam.getRequest(), mjRequestParam.getCookie(), optAttribute);
                    if (result instanceof JobInfo) {
                        jobInfo = (JobInfo) result;
                    } else if (result instanceof FailureJobInfo failureJobInfo) {
                        Result<Object> objectResult = handleMjJobFailureType(failureJobInfo.getType(), randomAccount.getId(), randomAccount.getAccountName(), mjRequestParam, imgDrawRecordPO.getInitImgUrls());
                        if (objectResult != null) {
                            return objectResult;
                        }
                    }
                    if (jobInfo == null) {
                        jobInfo = new JobInfo();
                        jobInfo.setBatch_size(1);
                    }
                }
                //校验：微变化、强变化
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_SUBTLE.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_STRONG.getValue()
                ) {
                    //调用变化接口
                    // jobInfo = MJApis.submitVary(mjAccountHeaderBO2.getJsonParameter(), imgDrawOptDTO.getOperate(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), mjAccountHeaderBO);
                    MjRequestParam mjRequestParam = MjWebApisUtil.createImageRequestParam(mjAccountHeaderBO, new MjHeaderDTO(
                            randomAccount.getChannelId(), randomAccount.getCookie(), randomAccount.getAccountSpeed(), imgDrawRecordPO.getMjJobId(),
                            jobType, imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getOperate()), new MjRequest());
                    if (mjRequestParam == null || mjRequestParam.getRequest() == null) {
                        log.error("optMj=操作参数错误...");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                    mjRequestParam.setImgDrawId(drawRecordId);
                    // RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK,String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 30, TimeUnit.MINUTES);
                    Object result = MjWebApisUtil.postCreateImage(mjRequestParam.getRequest(), mjRequestParam.getCookie(), optAttribute);
                    if (result instanceof JobInfo) {
                        jobInfo = (JobInfo) result;
                    } else if (result instanceof FailureJobInfo failureJobInfo) {
                        Result<Object> objectResult = handleMjJobFailureType(failureJobInfo.getType(), randomAccount.getId(), randomAccount.getAccountName(), mjRequestParam, imgDrawRecordPO.getInitImgUrls());
                        if (objectResult != null) {
                            return objectResult;
                        }
                    }
                    if (jobInfo == null) {
                        jobInfo = new JobInfo();
                        jobInfo.setBatch_size(4);
                    }
                }
                // 校验：上下左右平移
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_TOP.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_BOTTOM.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_LEFT.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_PAN_RIGHT.getValue()
                ) {
                    //调用变化接口
                    jobInfo = MJApis.submitPan(mjAccountHeaderBO2.getJsonParameter(), imgDrawOptDTO.getOperate(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), mjAccountHeaderBO);
                }
                // 校验：方形拓展
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM_MAKE.getValue()) {
                    //调用方形拓展接口
                    jobInfo = MJApis.submitSquare(mjAccountHeaderBO2.getJsonParameter(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), mjAccountHeaderBO);
                }
                //校验：局部修改
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_REGION.getValue()) {
                    if (imgDrawOptDTO.getVaryRegionPrompt() == null || imgDrawOptDTO.getVaryRegionPrompt().isEmpty() || imgDrawOptDTO.getVaryRegionMask() == null || imgDrawOptDTO.getVaryRegionMask().isEmpty()) {
                        return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
                    }
                    if (imgDrawOptDTO.getVaryRegionUrl() != null && imgDrawOptDTO.getVaryRegionUrl().getVrAll() != null) {
                        imgDrawOptDTO.setVaryRegionPrompt(imgDrawOptDTO.getVaryRegionPrompt() + imgDrawOptDTO.getVaryRegionUrl().getVrAll());
                    }
                    //调用局部修改接口
                    // jobInfo = MJApis.submitVaryRegion(mjAccountHeaderBO2.getJsonParameter(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(),imgDrawOptDTO.getVaryRegionPrompt(),imgDrawOptDTO.getVaryRegionMask(), mjAccountHeaderBO);
                    // 上传局部修改图片到mj -- 暂时不用
                    // String maskUrl = MjWebApisUtil.postMjUploadMask(imgDrawOptDTO.getVaryRegionMask(), imgDrawRecordPO.getMjJobId(), randomAccount.getCookie());
                    if (imgDrawOptDTO.getVaryRegionMask() == null) {
                        log.error("optMj=局部修改蒙板不能为空...");
                        return Result.ERROR(CommonResultEnum.VARY_REGION_PROMPT_ERROR.getValue());
                    }

                    MjRequest mjRequest = new MjRequest();
                    MjRequest.Frame frame = new MjRequest.Frame();
                    frame.setWidth(imgDrawRecordPO.getWidth());
                    frame.setHeight(imgDrawRecordPO.getHeight());
                    mjRequest.setFrame(frame);
                    MjRequest.Parent parent = new MjRequest.Parent();
                    parent.setWidth(imgDrawRecordPO.getWidth());
                    parent.setHeight(imgDrawRecordPO.getHeight());
                    parent.setX(0);
                    parent.setY(0);
                    mjRequest.setParent(parent);
                    mjRequest.setAlphaMask("data:image/png;base64,".concat(imgDrawOptDTO.getVaryRegionMask()));

                    mjAccountHeaderBO.setWebParams(imgDrawOptDTO.getVaryRegionPrompt());
                    MjRequestParam mjRequestParam = MjWebApisUtil.createImageRequestParam(mjAccountHeaderBO, new MjHeaderDTO(randomAccount.getChannelId(), randomAccount.getCookie(),
                            randomAccount.getAccountSpeed(), imgDrawRecordPO.getMjJobId(), jobType, imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getOperate()), mjRequest);
                    if (mjRequestParam == null || mjRequestParam.getRequest() == null) {
                        log.error("optMj=操作参数错误...");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                    mjRequestParam.setImgDrawId(drawRecordId);
                    // RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK,String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 30, TimeUnit.MINUTES);
                    Object result = MjWebApisUtil.postCreateImage(mjRequestParam.getRequest(), mjRequestParam.getCookie(), optAttribute);
                    if (result instanceof JobInfo) {
                        jobInfo = (JobInfo) result;
                    } else if (result instanceof FailureJobInfo failureJobInfo) {
                        Result<Object> objectResult = handleMjJobFailureType(failureJobInfo.getType(), randomAccount.getId(), randomAccount.getAccountName(), mjRequestParam, imgDrawRecordPO.getInitImgUrls());
                        if (objectResult != null) {
                            return objectResult;
                        }
                    }
                    if (jobInfo == null) {
                        jobInfo = new JobInfo();
                        jobInfo.setBatch_size(4);
                    }
                }

                //校验：缩放
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM.getValue()) {
                    if (imgDrawOptDTO.getZoomFactorStr() == null || imgDrawOptDTO.getZoomFactorStr().isEmpty()) {
                        log.error("optMj=suofang-缩放参数错误。");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                    if (imgDrawOptDTO.getZoomFactorStr().equals("1")) {
                        imgDrawOptDTO.setZoomFactorStr("1.1");
                    }
                    //调用缩放接口:1.0 - 2.0 梯度 ：0.1
                    // jobInfo = MJApis.submitZoom(mjAccountHeaderBO2.getJsonParameter(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(),imgDrawOptDTO.getZoomFactorStr(), mjAccountHeaderBO);
                    MjRequest mjRequest = new MjRequest();
                    mjRequest.setZoomFactor((int) (50 * (3 - (Double.parseDouble(imgDrawOptDTO.getZoomFactorStr())))));
                    MjRequestParam mjRequestParam = MjWebApisUtil.createImageRequestParam(mjAccountHeaderBO, new MjHeaderDTO(randomAccount.getChannelId(), randomAccount.getCookie(),
                            randomAccount.getAccountSpeed(), imgDrawRecordPO.getMjJobId(), jobType, imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getOperate()), mjRequest);
                    if (mjRequestParam == null || mjRequestParam.getRequest() == null) {
                        log.error("optMj=suofang-操作参数错误...");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                    mjRequestParam.setImgDrawId(drawRecordId);
                    // RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK,String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 30, TimeUnit.MINUTES);
                    Object result = MjWebApisUtil.postCreateImage(mjRequestParam.getRequest(), mjRequestParam.getCookie(), optAttribute);
                    if (result instanceof JobInfo) {
                        jobInfo = (JobInfo) result;
                    } else if (result instanceof FailureJobInfo failureJobInfo) {
                        Result<Object> objectResult = handleMjJobFailureType(failureJobInfo.getType(), randomAccount.getId(), randomAccount.getAccountName(), mjRequestParam, imgDrawRecordPO.getInitImgUrls());
                        if (objectResult != null) {
                            return objectResult;
                        }
                    }
                    if (jobInfo == null) {
                        jobInfo = new JobInfo();
                        jobInfo.setBatch_size(4);
                    }
                }
                //校验：zoom自由变化接口：新版
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_CHANGE_AR.getValue()) {
                    if (imgDrawOptDTO.getLocation() == null) {
                        return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
                    }
                    if (imgDrawOptDTO.getZoomFactorStr() == null || imgDrawOptDTO.getZoomFactorStr().isEmpty()) {
                        return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
                    }
                    String oldPrompt = imgDrawRecordPO.getPromptUse();
                    imgDrawRecordPO.setPromptUse(BStringUtil.promptUseMatcher(imgDrawRecordPO.getPromptUse(), imgDrawOptDTO.getZoomFactorStr()));
                    // jobInfo = MJApis.submitJobZoomNew(oldPrompt,mjAccountHeaderBO2.getJsonParameter(), imgDrawOptDTO.getLocation(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getZoomFactorStr(), mjAccountHeaderBO);
                    // WEB绘图自由缩放接口
                    if (imgDrawOptDTO.getLocation() == 2) {
                        MjRequest mjRequest = new MjRequest();
                        mjRequest.setZoomFactor(75);
                        JSONObject jsonObject = JSON.parseObject(mjAccountHeaderBO.getJsonParameter());
                        jsonObject.getJSONObject("parameters").put("ar", imgDrawOptDTO.getZoomFactorStr());
                        mjAccountHeaderBO.setJsonParameter(JSON.toJSONString(jsonObject));
                        MjRequestParam mjRequestParam = MjWebApisUtil.createImageRequestParam(mjAccountHeaderBO, new MjHeaderDTO(randomAccount.getChannelId(), randomAccount.getCookie(),
                                randomAccount.getAccountSpeed(), imgDrawRecordPO.getMjJobId(), jobType, imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getOperate(), imgDrawOptDTO.getLocation()), mjRequest);
                        if (mjRequestParam == null || mjRequestParam.getRequest() == null) {
                            log.error("optMj=操作参数错误...");
                            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                        }
                        mjRequestParam.setImgDrawId(drawRecordId);
                        // RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK,String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 30, TimeUnit.MINUTES);
                        Object result = MjWebApisUtil.postCreateImage(mjRequestParam.getRequest(), mjRequestParam.getCookie(), optAttribute);
                        if (result instanceof JobInfo) {
                            jobInfo = (JobInfo) result;
                        } else if (result instanceof FailureJobInfo failureJobInfo) {
                            Result<Object> objectResult = handleMjJobFailureType(failureJobInfo.getType(), randomAccount.getId(), randomAccount.getAccountName(), mjRequestParam, imgDrawRecordPO.getInitImgUrls());
                            if (objectResult != null) {
                                return objectResult;
                            }
                        }
                        if (jobInfo == null) {
                            jobInfo = new JobInfo();
                            jobInfo.setBatch_size(4);
                        }
                    } else {
                        String jsonString = submitJobZoomNew(oldPrompt, mjAccountHeaderBO2.getJsonParameter(), imgDrawOptDTO.getLocation(), imgDrawRecordPO.getMjJobId(), imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getZoomFactorStr(), mjAccountHeaderBO);
                        JSONObject jsonObjectDir = JSON.parseObject(jsonString);
                        assert jsonObjectDir != null;
                        int direction = jsonObjectDir.getIntValue("direction");
                        double fraction = jsonObjectDir.getDoubleValue("fraction");
                        MjRequest mjRequest = new MjRequest();
                        mjRequest.setStitch(true);
                        mjRequest.setPanDirection(direction);
                        mjRequest.setPanFraction(fraction);
                        JSONObject jsonObject = JSON.parseObject(mjAccountHeaderBO.getJsonParameter());
                        jsonObject.getJSONObject("parameters").put("ar", "F");
                        mjAccountHeaderBO.setJsonParameter(JSON.toJSONString(jsonObject));
                        MjRequestParam mjRequestParam = MjWebApisUtil.createImageRequestParam(mjAccountHeaderBO, new MjHeaderDTO(randomAccount.getChannelId(), randomAccount.getCookie(), randomAccount.getAccountSpeed(), imgDrawRecordPO.getMjJobId(), jobType, imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getOperate(), imgDrawOptDTO.getLocation()), mjRequest);
                        if (mjRequestParam == null || mjRequestParam.getRequest() == null) {
                            log.error("optMj=操作参数错误...");
                            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                        }
                        mjRequestParam.setImgDrawId(drawRecordId);
                        // RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK,String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 30, TimeUnit.MINUTES);
                        Object result = MjWebApisUtil.postCreateImage(mjRequestParam.getRequest(), mjRequestParam.getCookie(), optAttribute);
                        if (result instanceof JobInfo) {
                            jobInfo = (JobInfo) result;
                        } else if (result instanceof FailureJobInfo failureJobInfo) {
                            Result<Object> objectResult = handleMjJobFailureType(failureJobInfo.getType(), randomAccount.getId(), randomAccount.getAccountName(), mjRequestParam, imgDrawRecordPO.getInitImgUrls());
                            if (objectResult != null) {
                                return objectResult;
                            }
                        }
                        if (jobInfo == null) {
                            jobInfo = new JobInfo();
                            jobInfo.setBatch_size(4);
                        }
                    }
                }

                //校验：微调低变化、微调高变化
                if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_FINE_TUNING_REMIX.getValue()
                        || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_FINE_TUNING_REMIX_SUBTLE.getValue()
                ) {
                    mjAccountHeaderBO.setWebParams(imgDrawOptDTO.getVaryRegionPrompt());
                    MjRequestParam mjRequestParam = MjWebApisUtil.createImageRequestParam(mjAccountHeaderBO, new MjHeaderDTO(randomAccount.getChannelId(),
                            randomAccount.getCookie(), randomAccount.getAccountSpeed(), imgDrawRecordPO.getMjJobId(), jobType, imgDrawDetlPO.getImgIndex(), imgDrawOptDTO.getOperate()), new MjRequest());
                    if (mjRequestParam == null || mjRequestParam.getRequest() == null) {
                        log.error("optMj=操作参数错误...");
                        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
                    }
                    mjRequestParam.setImgDrawId(drawRecordId);
                    // RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK,String.valueOf(mjRequestParam.getImgDrawId())), JSONObject.toJSONString(mjRequestParam), 30, TimeUnit.MINUTES);
                    Object result = MjWebApisUtil.postCreateImage(mjRequestParam.getRequest(), mjRequestParam.getCookie(), optAttribute);
                    if (result instanceof JobInfo) {
                        jobInfo = (JobInfo) result;
                    } else if (result instanceof FailureJobInfo failureJobInfo) {
                        Result<Object> objectResult = handleMjJobFailureType(failureJobInfo.getType(), randomAccount.getId(), randomAccount.getAccountName(), mjRequestParam, imgDrawRecordPO.getInitImgUrls());
                        if (objectResult != null) {
                            return objectResult;
                        }
                    }
                    if (jobInfo == null) {
                        jobInfo = new JobInfo();
                        jobInfo.setBatch_size(4);
                    }
                }
            } catch (Exception e) {
                log.error("optMj绘图失败：" + e);
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }

            //接口异常
            if (jobInfo == null) {
                log.error("optMj-jobInfo图片编辑失败：null");
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            //校验：封号异常；token异常；时间失效异常；指令异常
            /*if (jobInfo.getIsPendingModMessage() || jobInfo.getIsTokenExhausted() || jobInfo.getIsCreditsExhausted() || jobInfo.getIsBannedPromptDetected() || jobInfo.getIsInvalidLink()){
                return Result.ERROR(mjError(jobInfo,mjAccountHeaderBO));
            }
            // 重置快速账号：并及时释放任务锁
            ImgDrawPUtil.initMjAccountBPRO();*/
        } finally {
            RedisUtil.releaseLock(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_LOCK.getStrKey());//释放锁
        }
        boolean isVip = iUserDDRecordService.getUserIsVip();
        // TODO 扣除用户点子
        double dzQuantity = BDDUseNumEnum.getOptMJ(imgDrawOptDTO.getOperate(), isVip);
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(optAttribute);
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(optAttribute);
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        checkBalanService.checkUser(imgDrawDetlPO.getUserId(), dzQuantity, flowRecordSub);
        try {
            ImgDrawRecordPO imgDrawRecordPOPd = new ImgDrawRecordPO();
            imgDrawRecordPOPd.setId(drawRecordId);
            imgDrawRecordPOPd.setMjAccountId(mjAccountHeaderBO.getMjAccountId());
            imgDrawRecordPOPd.setUseDdQua(dzQuantity);
            imgDrawRecordPOPd.setOptAttribute(optAttribute);//设置操作属性
            imgDrawRecordPOPd.setModeAttribute(modeAttribute);//设置模型属性
            imgDrawRecordPOPd.setUserId(JwtNewUtil.getUserId());//关联用户
            imgDrawRecordPOPd.setSubmitTime(System.currentTimeMillis());//装载提交时间
            imgDrawRecordPOPd.setSuperId(imgDrawDetlPO.getDrawRecordId());//装载上级任务id
            imgDrawRecordPOPd.setOriginalImgId(imgDrawOptDTO.getImgDrawDetlId());
            //校验是否垫图
            if (imgDrawOptDTO.getInitImgUrls() != null && !imgDrawOptDTO.getInitImgUrls().isEmpty()) {
                imgDrawRecordPOPd.setInitImgUrls(JSONArray.toJSONString(imgDrawOptDTO.getInitImgUrls()));
            }
            if (imgDrawOptDTO.getMjAddImageBO() != null) {
                imgDrawRecordPOPd.setInitImgObject(JSON.toJSONString(imgDrawOptDTO.getMjAddImageBO()));
            }
            if (imgDrawOptDTO.getVaryRegionPrompt() != null && StringUtils.isNotEmpty(imgDrawOptDTO.getVaryRegionPrompt())) {
                String varyRegionPrompt = "";
                String regionPrompt = imgDrawRecordPO.getPromptUse() == null ? "" : imgDrawRecordPO.getPromptUse();
                int indexOfDoubleDash = regionPrompt.indexOf("--");
                if (indexOfDoubleDash != -1) {
                    varyRegionPrompt = regionPrompt.substring(indexOfDoubleDash);
                }
                imgDrawRecordPOPd.setPromptInit(removeIwDirective(imgDrawOptDTO.getVaryRegionPrompt()));
                imgDrawRecordPOPd.setPromptUse(imgDrawOptDTO.getVaryRegionPrompt() + " " + varyRegionPrompt);
            } else {
                imgDrawRecordPOPd.setPromptInit(imgDrawRecordPO.getPromptInit());
                imgDrawRecordPOPd.setPromptUse(imgDrawRecordPO.getPromptUse());
            }
            if (imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM.getValue() || imgDrawOptDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_CHANGE_AR.getValue()) {
                imgDrawRecordPOPd.setOptDescribe(imgDrawOptDTO.getZoomFactorStr());
            }
            imgDrawRecordPOPd.setDescription(JSONObject.toJSONString(mjAccountHeaderBO));

            // TODO 2 存在：说明账号有闲置并发数量，直接创建进行中任务：需要进行测试
            imgDrawRecordPOPd.setStartTime(System.currentTimeMillis());//装载开始时间
            imgDrawRecordPOPd.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
            imgDrawRecordPOPd.setMjJobId(jobInfo.getJob_id());//装载任务id
            imgDrawRecordPOPd.setMjIsRelaxed(imgDrawRecordPOPdMjIsRelaxed);
            imgDrawRecordPOPd.setMjAccountId(mjAccountHeaderBO.getMjAccountId());
            //装载宽高尺寸：mj接口返回的宽高尺寸
            imgDrawRecordPOPd.setWidth(jobInfo.getWidth());
            imgDrawRecordPOPd.setHeight(jobInfo.getHeight());
            imgDrawRecordPOPd.setImgQuantity(jobInfo.getBatch_size());
            imgDrawRecordPOPd.setFinalPrompt(jobInfo.getFull_command());
            imgDrawRecordPOPd.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());
            imgDrawRecordPOPd.setWhDivide(ImgDrawUtil.getWhDivide(jobInfo.getWidth(), jobInfo.getHeight()));
            //创建进行中任务成功
            if (imgDrawRecordMapper.insert(imgDrawRecordPOPd) > 0) {
                ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
                if (jobInfo.getBatch_size() > 0) {
                    imgDrawHistoryVO.setId(imgDrawRecordPOPd.getId());
                    imgDrawHistoryVO.setUserId(imgDrawRecordPOPd.getUserId());
                    imgDrawHistoryVO.setModeAttribute(imgDrawRecordPOPd.getModeAttribute());
                    imgDrawHistoryVO.setOptAttribute(imgDrawRecordPOPd.getOptAttribute());
                    imgDrawHistoryVO.setPrompt(imgDrawRecordPOPd.getPromptInit());
                    imgDrawHistoryVO.setPromptUse(imgDrawRecordPOPd.getPromptUse());
                    imgDrawHistoryVO.setWhDivide(imgDrawRecordPOPd.getWhDivide());
                    imgDrawHistoryVO.setStatus(imgDrawRecordPOPd.getStatus());
                    imgDrawHistoryVO.setImgQuantity(jobInfo.getBatch_size());
                    List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();
                    for (int i = 0; i < jobInfo.getBatch_size(); i++) {
                        ImgDrawDetlVO imgDrawDetlVO = new ImgDrawDetlVO();
                        imgDrawDetlVO.setDrawRecordId(imgDrawRecordPOPd.getId());
                        imgDrawDetlVO.setOptAttribute(imgDrawRecordPOPd.getOptAttribute());
                        imgDrawDetlVO.setImgIndex(i);
                        imgDrawDetlVO.setWhDivide(ImgDrawUtil.getWhDivide(jobInfo.getWidth(), jobInfo.getHeight()));
                        imgDrawDetlVO.setImgWidth(jobInfo.getWidth());
                        imgDrawDetlVO.setImgHeight(jobInfo.getHeight());
                        imgDrawDetlVOS.add(imgDrawDetlVO);
                    }
                    imgDrawHistoryVO.setImgDrawDetls(imgDrawDetlVOS);
                    log.info("操作任务：{}，任务id：{}", imgDrawRecordPOPd, imgDrawRecordPOPd.getId());
                    //缓存任务有效期为一天
                    RedisUtil.setValueSeconds(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_JOB_ID.getStrKey(), imgDrawRecordPOPd.getMjJobId()), JSONObject.toJSONString(imgDrawHistoryVO), 1, TimeUnit.DAYS);
                    //异步渐显推送
                    MJAPIUtil.sendHandshakeRequest(imgDrawRecordPOPd.getMjJobId(), new MjWebSocketListener(imgDrawRecordPOPd.getMjJobId(), redisServer));
                }
                return Result.SUCCESS(imgDrawHistoryVO);
            }
        } catch (Exception e) {
            log.error("操作时异常错误 {}", e.getMessage(), e);//释放锁
        }
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }

    private void initMjAddImageBO(ImgDrawOptDTO imgDrawOptDTO) {
        ImgDrawOptDTO.VaryRegionUrl varyRegionUrl = imgDrawOptDTO.getVaryRegionUrl();
        imgDrawOptDTO.setVaryRegionPrompt(imgDrawOptDTO.getVaryRegionPrompt() + varyRegionUrl.getVrAll());
        imgDrawOptDTO.setInitImgUrls(Collections.singletonList(varyRegionUrl.getVrUrl()));

        MjAddImageBO mjAddImage = new MjAddImageBO();
        //1参考；2风格；--sref|--sw  3角色：(--cref|--cw|)";
        if (Objects.equals(varyRegionUrl.getVrType(), ImgOptModelEnum.MJ_PADDING_REFERENCE.getValue())) {
            MjAddImageBO.MjImageBO mjImageBO = MjAddImageBO.getInstance();
            mjImageBO.setUrl(varyRegionUrl.getVrUrl());
            mjAddImage.setDefImages(List.of(mjImageBO));
            mjAddImage.setDefWeight(Double.valueOf(varyRegionUrl.getVrWight()));
        } else if (Objects.equals(varyRegionUrl.getVrType(), ImgOptModelEnum.MJ_PADDING_STYLE.getValue())) {
            MjAddImageBO.MjImageBO mjImageBO = MjAddImageBO.getInstance();
            mjImageBO.setUrl(varyRegionUrl.getVrUrl());
            mjAddImage.setSrefImages(List.of(mjImageBO));
            mjAddImage.setSrefWeight(varyRegionUrl.getVrWight());
        } else if (Objects.equals(varyRegionUrl.getVrType(), ImgOptModelEnum.MJ_PADDING_ROLE.getValue())) {
            MjAddImageBO.MjImageBO mjImageBO = MjAddImageBO.getInstance();
            mjImageBO.setUrl(varyRegionUrl.getVrUrl());
            mjAddImage.setCrefImages(List.of(mjImageBO));
            mjAddImage.setCrefWeight(varyRegionUrl.getVrWight());
        }
        imgDrawOptDTO.setMjAddImageBO(mjAddImage);
    }

    private long getCasualModeDailyTotal(Long userId, LocalDate localDate) {
        try {
            return imgDrawRecordMapper.selectCount(
                    new LambdaQueryWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getUserId, userId)
                            .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue())
                            .eq(ImgDrawRecordPO::getMjIsCasual, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue())
                            .between(ImgDrawRecordPO::getCreateTime, localDate.atStartOfDay(), localDate.atTime(LocalTime.MAX))
            );
        } catch (Exception e) {
            log.error("获取休闲模式总数" + e.toString());
            return 60;
        }
    }

    public static String submitJobZoomNew(String oldPrompt, String requestBodyJson, Integer location, String parentJobId, int index, String scale, MJAccountHeaderBO mjAccountHeaderBO) {
        List<String> scaleList = MjWebApisUtil.getScaleList();
        if (location == null || location < 0 || location > 3 || !scaleList.contains(scale) || index < 0 || index > 3) {
            log.error("参数错误\nrequestBodyJson{}\nlocation{}\nparentJobId{}\nindex{}\nscale{}", requestBodyJson, location, parentJobId, index, scale);
            return null;
        }
        ++index;

        JSONObject jsonObject = JSONObject.parseObject(requestBodyJson);
        JSONObject parameters = jsonObject.getJSONObject("parameters");
        String ar = null;
        log.info("promptUse:{}", oldPrompt);
        Pattern pattern = Pattern.compile("--ar\\s(\\d+:\\d+)");
        Matcher matcher = pattern.matcher(oldPrompt);
        if (matcher.find()) {
            ar = matcher.group(1);
            parameters.put("ar", ar);
            jsonObject.put("parameters", parameters);
            requestBodyJson = jsonObject.toJSONString();
        }
        if (ar == null) {
            return null;
        }
        log.info("ar:{}", ar);
        log.info("scale:{}", scale);
        Double fractionEnum = BMJZoomEnum.getBMJZoomEnum(ar, scale);

        String[] arParts = ar.split(":");
        int defaultW = Integer.parseInt(arParts[0]);
        int defaultH = Integer.parseInt(arParts[1]);
        String[] scaleParts = scale.split(":");
        int scaleW = Integer.parseInt(scaleParts[0]);
        int scaleH = Integer.parseInt(scaleParts[1]);
        String requestSubmitJobsBodyJson;
        int ok = 17;
        RoundingMode roundingMode = RoundingMode.DOWN;
        //上下：0为下拓展；2为上拓展；
        if (scaleW < scaleH) {
            int newRatio = defaultW * scaleH;
            BigDecimal dd = new BigDecimal(String.valueOf(newRatio)).divide(new BigDecimal(String.valueOf(scaleW)), ok, roundingMode);
            System.out.println(dd);
            System.out.println(dd.subtract(new BigDecimal(String.valueOf(defaultH))).doubleValue());
            System.out.println(new BigDecimal("3").subtract(new BigDecimal(String.valueOf("3"))));

            double newRatio2 = new BigDecimal(String.valueOf(newRatio)).divide(new BigDecimal(String.valueOf(scaleW)), ok, roundingMode).subtract(new BigDecimal(String.valueOf(defaultH))).doubleValue();
            double fraction = new BigDecimal(String.valueOf(newRatio2)).divide(new BigDecimal(String.valueOf(defaultH)), ok, roundingMode).doubleValue();
            fraction = fractionEnum == null ? fraction : fractionEnum;
            if (fraction <= 0) {
                return null;
            }

            JSONObject jsonObjectParam = new JSONObject();
            jsonObjectParam.put("direction", (location == 1 ? 2 : 0));//2为上拓展；1为右拓展；0为下拓展；3为左拓展
            jsonObjectParam.put("fraction", fraction);//平移比例，猜测0.5为1；1状态的1半:需要进行计算
            log.info("上下拓展比例：{}", jsonObjectParam.toJSONString());
            return jsonObjectParam.toJSONString();
        } else {
            //左右：1为右拓展；3为左拓展
            int newRatio = scaleW * defaultH;
            double newRatio2 = new BigDecimal(String.valueOf(newRatio)).divide(new BigDecimal(String.valueOf(scaleH)), ok, roundingMode).subtract(new BigDecimal(String.valueOf(defaultW))).doubleValue();
            double fraction = new BigDecimal(String.valueOf(newRatio2)).divide(new BigDecimal(String.valueOf(defaultW)), ok, roundingMode).doubleValue();
            fraction = fractionEnum == null ? fraction : fractionEnum;
            if (fraction <= 0) {
                return null;
            }
            requestSubmitJobsBodyJson = MJAPIUtil.requestSubmitJobsBodyJsonPanNew(requestBodyJson, parentJobId, index, (location == 1 ? 1 : 3), fraction);
            log.info("左右拓展比例：{}", requestSubmitJobsBodyJson);

            JSONObject jsonObjectParam = new JSONObject();
            jsonObjectParam.put("direction", (location == 1 ? 3 : 1));//2为上拓展；1为右拓展；0为下拓展；3为左拓展
            jsonObjectParam.put("fraction", fraction);//平移比例，猜测0.5为1；1状态的1半:需要进行计算
            log.info("左右拓展比例：{}", jsonObjectParam.toJSONString());
            return jsonObjectParam.toJSONString();
        }
    }

    public static String removeIwDirective(String input) {
        return input.replaceAll("--sref\\s+\\S+|--cref\\s+\\S+|--cw\\s+\\d+|--sw\\s+\\d+", "").replaceAll("\\s--iw\\s\\d+(\\.\\d+)?", "").trim();
    }

    private String mjError(JobInfo jobInfo, MJAccountHeaderBO mjAccountHeaderBO) {
        //1 提示词问题：提示词不符合社区标准
        if (jobInfo.getIsBannedPromptDetected()) {
            return CommonResultEnum.PROMPT_WORD_ERROR.getValue();
        }
        //1 垫图链接无效：
        if (jobInfo.getIsInvalidLink()) {
            return CommonResultEnum.IMG_NOT_MATCH_ERROR.getValue();
        }

        //2 快速时间用完：清除快速账号；重新排序
        if (jobInfo.getIsCreditsExhausted()) {
            ImgDrawPUtil.resetMjAccount(mjAccountHeaderBO);//重置账号
            adminMjAccountConfigMapper.update(
                    null,
                    new LambdaUpdateWrapper<AdminMjAccountConfigPO>()
                            .eq(AdminMjAccountConfigPO::getId, mjAccountHeaderBO.getMjAccountId())
                            .set(AdminMjAccountConfigPO::getPeriodCredits, 0)
            );
            return CommonResultEnum.DRAW_MJ_API_ERROR.getValue();
        }
        //3 token失效问题：清除缓存账号；重新排序缓存账号；变更状态为异常，等待监听处理
        if (jobInfo.getIsTokenExhausted()) {
            ImgDrawPUtil.resetMjAccount(mjAccountHeaderBO);//重置账号
            adminMjAccountConfigMapper.update(
                    null,
                    new LambdaUpdateWrapper<AdminMjAccountConfigPO>()
                            .eq(AdminMjAccountConfigPO::getId, mjAccountHeaderBO.getMjAccountId())
                            .set(AdminMjAccountConfigPO::getDeleted, CommonIntEnum.IS_TRUE.getIntValue())
            );
            String jsonStr = JSONObject.toJSONString(mjAccountHeaderBO);
            log.error("执行重置账号信息：{}", jsonStr);
            BFeiShuUtil.sedCardErrorFromDraw(BFeiShuUtil.P1, "MJ提交绘图403错误", jsonStr, false, "自动关闭账号,人工处理");
            return CommonResultEnum.DRAW_MJ_API_ERROR.getValue();
        }

        //4 账号封号：清除缓存账号；重新排序缓存账号；变更状态为禁用，等待监听处理
        if (jobInfo.getIsPendingModMessage()) {
            ImgDrawPUtil.resetMjAccount(mjAccountHeaderBO);//重置账号
            adminMjAccountConfigMapper.update(
                    null,
                    new LambdaUpdateWrapper<AdminMjAccountConfigPO>()
                            .eq(AdminMjAccountConfigPO::getId, mjAccountHeaderBO.getMjAccountId())
                            .set(AdminMjAccountConfigPO::getIsUse, CommonIntEnum.ADMIN_IS_USE_FALSE.getIntValue())
            );
            return CommonResultEnum.DRAW_MJ_API_ERROR.getValue();
        }
        return CommonResultEnum.DRAW_MJ_API_ERROR.getValue();
    }


    @Override
    public void statusQueuingTasks() {
        //TODO 排队中任务：暂时停用
        // 排队中的任务进行处理
        Long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_QUEUING.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
        );
        log.info("排队中任务数量：{}", count);
        if (count < 1) {
            return;
        }

        // TODO 1.排队中任务执行;查询排队中任务执行结果（查询最早的8条排队中的数据：1秒执行8条）
        List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper.selectList(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_QUEUING.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
                        .ne(ImgDrawRecordPO::getMjJobId, null)
                        .ne(ImgDrawRecordPO::getMjJobId, "")
                        .orderByAsc(ImgDrawRecordPO::getCreateTime)
                        .last("limit 0,8")
        );
        //校验不存在任务直接返回
        if (imgDrawRecordPOList == null || imgDrawRecordPOList.isEmpty()) {
            return;
        }
    }

    public static Map<Long, List<String>> convertListToMap(List<ImgDrawRecordPO> personList) {
        Map<Long, List<String>> map = new HashMap<>();
        for (ImgDrawRecordPO imgDrawRecordPO : personList) {
            Long mjAccountId = imgDrawRecordPO.getMjAccountId();
            String mjJobId = imgDrawRecordPO.getMjJobId();
            if (!map.containsKey(mjAccountId)) {
                map.put(mjAccountId, new ArrayList<>());
            }
            map.get(mjAccountId).add(mjJobId);
        }
        return map;
    }

    //监听并处理进行中的mj绘图任务信息
    @Override
    public void statusInProgressTasks() {
        // 如果Mj账号锁定了， 就等待释放完锁在执行
        String accountLock = RedisUtil.getValue(BRedisKeyEnum.MJ_ACCOUNT_LOCK.getKey());
        if (Boolean.parseBoolean(accountLock)) {
            return;
        }

        // 1、获取进行中的任务数量
        Long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .isNotNull(ImgDrawRecordPO::getMjJobId)//仅操作mj类型的任务
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
        );
        if (count < 1) {
            return;
        }
        log.info("绘图进行中任务数量：{}", count);
        try {
            // 2、获取进行中的任务信息列表
            List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper.selectList(
                    new LambdaQueryWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                            .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                            .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
                            .isNotNull(ImgDrawRecordPO::getMjJobId)
                            .orderByAsc(ImgDrawRecordPO::getCreateTime)
            );
            if (imgDrawRecordPOList == null || imgDrawRecordPOList.isEmpty()) {
                return;
            }

            // 3、装载jobIds 用户提交mj app api进行查询相关信息
            List<String> jobIds = new ArrayList<>();
            imgDrawRecordPOList.forEach(imgDrawRecordPO -> {
                if (imgDrawRecordPO.getMjJobId() != null && !imgDrawRecordPO.getMjJobId().isEmpty()) {
                    jobIds.add(imgDrawRecordPO.getMjJobId());
                }
            });
            if (jobIds.isEmpty()) {// 装载失败返回数据
                return;
            }

            List<JobStatusBO> jobStatusBOList = null;
            try {
                //if (cacheAccountList == null || cacheAccountList.isEmpty()) {
                List<AdminMjWebConfigBO> accountList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE.getKey()), new TypeReference<List<AdminMjWebConfigBO>>() {
                });
                if (accountList == null || accountList.isEmpty()) {
                    accountList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE_TURTLE.getKey()), new TypeReference<List<AdminMjWebConfigBO>>() {
                    });
                }
                if (accountList == null || accountList.isEmpty()) {
                    log.error("重大问题：获取账号信息失败===================>");
                    BFeiShuApis.sedCardErrorFromMonitor(BFeiShuApis.P1, "MJ绘图无可用账号，请赶快添加账号", "Exception : 无账号", false, "自动停止任务");
                    return;
                }

                // AdminMjWebConfigBO nextAccount = getNextAccount(accountList); //每次一个新账号
                int randomIndex = ThreadLocalRandom.current().nextInt(accountList.size()); //随机账号
                AdminMjWebConfigBO nextAccount = accountList.get(randomIndex);
                if (nextAccount == null || nextAccount.getId() == null) {
                    log.error("重大问题：获取账号信息失败===================>");
                    BFeiShuApis.sedCardErrorFromMonitor(BFeiShuApis.P1, "MJ绘图无可用账号，请赶快添加账号", "Exception : 无账号", false, "自动停止任务");
                    return;
                }

                MjHeaderDTO mjHeaderDTO = new MjHeaderDTO();
                mjHeaderDTO.setCookie(nextAccount.getCookie());
                String responseBody = MjWebApisUtil.postJobState(jobIds, mjHeaderDTO);
                if (responseBody == null) {
                    return;
                } else if (responseBody.equals("forbidden_error")) {
                    BFeiShuUtil.sedCardWarnFromDraw(BFeiShuUtil.P3, "MJ账号异常【403】", "账号[" + nextAccount.getAccountName() + "]报403，自动关闭账号,人工处理");
                    RedisUtil.setIfAbsent(BRedisKeyEnum.MJ_ACCOUNT_LOCK.getKey(), "true", 60, TimeUnit.SECONDS);
                    if (adminMjWebConfigMapper.update(null, new LambdaUpdateWrapper<AdminMjWebConfigPO>()
                            .set(AdminMjWebConfigPO::getIsEnable, 0)
                            .set(AdminMjWebConfigPO::getRemark, "报403错误，平均等待10分钟在启用")
                            .eq(AdminMjWebConfigPO::getId, nextAccount.getId())) > 0) {
                        if (getMjWebAccountList()) {
                            return;
                        }
                    }
                }

                List<JobStatusBO> jobStatusList = JSONArray.parseArray(responseBody, JSONObject.class).stream().map(MJWebHttpUtil::createJobStatusBO).toList();
                jobStatusBOList = new ArrayList<>(jobStatusList);
                if (jobStatusBOList.isEmpty()) {
                    return;
                }

            } catch (Exception e) {
                log.error("拉取mj-api-绘图任务信息失败,{}", e.getMessage());
                return;
            }
            // 4、推送任务状态信息
            for (JobStatusBO jobStatusBO : jobStatusBOList) {
                try {
                    //任务成功完成进行定时任务推送
                    if (jobStatusBO != null && ImgDrawUtil.getIsCommitted(jobStatusBO.getCurrentStatus())) {
                        // TODO 替换
                        boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(jobStatusBO.getJobId(), BMessageSendEnum.DRAW_JOB_PUSH, jobStatusBO));
                        log.info("任务推送消息状态: {}", state ? "成功" : "失败");
                        continue;
                    }
                    log.info("任务进行中无需进行推送 {}", jobStatusBO);
                    //任务进行中，跳过
                } catch (Exception e) {
                    log.error("任务推送消息失败: {}", e.getMessage(), e);
                }
            }
        } finally {
        }
    }

    @Override
    public void listenDalleDrawTimeOutFail() {
        List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper.selectList(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .lt(ImgDrawRecordPO::getCreateTime, DateUtil.getDateSubdivisionMinutes(BDateUtil.getDateNowShanghai(), com.nacos.constant.CommonConst.TASK_TIMEOUT_MINUTES_DALLE))
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
                        .isNull(ImgDrawRecordPO::getMjJobId)
                        .isNull(ImgDrawRecordPO::getMjAccountId)
                        .orderByAsc(ImgDrawRecordPO::getCreateTime)
                        .last("limit 0,20")
        );
        if (imgDrawRecordPOList == null || imgDrawRecordPOList.isEmpty()) {
            return;
        }
        //未获取到锁，直接返回
        if (!getDrawTimeOutFailLock()) {
            return;
        }

        //1、查询未完成并且时间超过30分钟的绘图任务列表，统一按照失败进行处理
        try {
            for (ImgDrawRecordPO imgDrawRecordPO : imgDrawRecordPOList) {
                double deDrawDeductQua;
                if (Double.compare(imgDrawRecordPO.getWhDivide(), 1) == 0) {
                    deDrawDeductQua = DDUseRuleEnum.getDDUseRuleQuality(DDUseRuleEnum.DRAW_DE_SIZE_ONE);//dalle-3 扣除点点数量2
                } else {
                    deDrawDeductQua = DDUseRuleEnum.getDDUseRuleQuality(DDUseRuleEnum.DRAW_DE_SIZE_TWO);//dalle-3 扣除点点数量3
                }
                ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
                imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_FAIL.getValue());

                asyncService.handleFailedTask(imgDrawRecordPO.getUserId(),
                        deDrawDeductQua,
                        ImgOptModelEnum.getOptTitleAll(imgDrawRecordPO.getOptAttribute()),
                        imgDrawHistoryVO,
                        "目前人数较多，请重试"
                );

            }
        } finally {
            RedisUtil.releaseLock(GlobalRedisKeyEnum.TASK_LOCK_REDIS_MJ_DRAW_FAIL_HANDLE.getStrKey());//释放锁
        }
    }

    private boolean getDrawTimeOutFailLock() {
        //创建锁：最大重试次数=3；超时释放时间=60秒
        int maxRetryAttempts = 3;
        boolean lockAcquired = false;
        int retryAttempts = 0;
        while (!lockAcquired && retryAttempts <= maxRetryAttempts) {
            lockAcquired = RedisUtil.acquireLock(GlobalRedisKeyEnum.TASK_LOCK_REDIS_MJ_DRAW_FAIL_HANDLE.getStrKey(), 60);
            if (!lockAcquired) {
                retryAttempts++;
            }
        }
        return lockAcquired;
    }

    /**
     * 生成失败返回用户点子消耗数量
     * @param imgDrawRecordPO
     */
    //TODO .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setFailAndFhdd(ImgDrawRecordPO imgDrawRecordPO) {
        try {
            if (imgDrawRecordMapper.update(
                    null,
                    new LambdaUpdateWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                            .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
            ) < 1) {
                return;
            }
            //2、返回用户点子消耗数量
            boolean result = checkService.setReturnDD(imgDrawRecordPO.getUserId(), imgDrawRecordPO.getUseDdQua());
            if (!result) {
                log.error("返回用户点子消耗数量失败:手动回滚1");
                throw new RuntimeException();
            }
            FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ZERO.getValue()).remark(ImgOptModelEnum.getOptTitleAll(imgDrawRecordPO.getOptAttribute())).build();
            flowRecordSub.setUserId(imgDrawRecordPO.getUserId());
            flowRecordSub.setNum(imgDrawRecordPO.getUseDdQua());
            if (flowRecordMapper.insert(flowRecordSub) < 1) {
                log.error("返回用户点子消耗数量失败:手动回滚2");
                throw new RuntimeException();
            }

            // TODO 推送用户绘画失败信息=================？缺少一个
            JobStatusBO jobStatusBO = new JobStatusBO();
            jobStatusBO.setUserId(String.valueOf(imgDrawRecordPO.getUserId()));
            jobStatusBO.setJobId(imgDrawRecordPO.getMjJobId());
            jobStatusBO.setCurrentStatus("FAILURE");
            jobStatusBO.setFullCommand(imgDrawRecordPO.getPromptUse());
            jobStatusBO.setJobType(String.valueOf(imgDrawRecordPO.getFunType()));
            jobStatusBO.setBatchSize(imgDrawRecordPO.getImgQuantity());
            jobStatusBO.setEventWidth(imgDrawRecordPO.getWidth());
            jobStatusBO.setEventHeight(imgDrawRecordPO.getHeight());
            boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(jobStatusBO.getJobId(), BMessageSendEnum.DRAW_JOB_PUSH, jobStatusBO));
            log.info("任务失败推送消息:{}", state);


            //停止渐显的任务
            MJAPIUtil.getWebForithmClientMapShutdown(imgDrawRecordPO.getMjJobId());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }


    //获取个人写真任务结果
    @Override
    public void fetchFaceSwapTaskResult() {
        List<ImgDrawRecordPO> imgDrawRecordPOS = imgDrawRecordMapper.selectList(new LambdaQueryWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_PORTRAIT.getValue())
                .notIn(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue(), ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
                .isNotNull(ImgDrawRecordPO::getGoTaskId)
                .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
        imgDrawRecordPOS.stream()
                .filter(imgDrawRecordPO -> StringUtils.isNotBlank(imgDrawRecordPO.getGoTaskId()))
                .forEach(imgDrawRecordPO -> processImgDrawRecord(imgDrawRecordPO));
    }

    // 处理单个 ImgDrawRecordPO 的方法
    private void processImgDrawRecord(ImgDrawRecordPO imgDrawRecordPO) {
        try {
            String response = GoApiUtil.fetchFaceSwapResult(imgDrawRecordPO.getGoTaskId());
            if (StringUtils.isNotBlank(response)) {
                FaceSwapVO faceSwapVO = JSONObject.parseObject(response, FaceSwapVO.class);
                if (faceSwapVO.getCode() == 200) {
                    FaceSwapVO.ImageData imageData = faceSwapVO.getData();
                    String status = imageData.getStatus();
                    String image = imageData.getImage();
                    if (CurrentStatusEnum.SUCCESS.getStatus().equals(status) && StringUtils.isNotBlank(image)) {
                        //TODO 处理成功的逻辑
                        String result = this.uploadImageToOSSPhoto(imgDrawRecordPO, imageData.getImage(), 9);
                        if (result == null) {
                            imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_FAILURE.getStatusName());
                            taskFailureProcessing(imgDrawRecordPO);
                        } else {
                            imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());
                            updatePhotoTaskStatus(imgDrawRecordPO);
                            photoTaskFinished(imgDrawRecordPO, BNotificationEnum.PHOTO_NOTIF);
                        }
                    } else if (CurrentStatusEnum.FAILED.getStatus().equals(status)) {
                        //TODO 处理失败的逻辑
                        imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_FAILURE.getStatusName());
                        taskFailureProcessing(imgDrawRecordPO);
                    }
                    log.info("====faceSwapVO= {}", faceSwapVO);
                } else {
                    imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_FAILURE.getStatusName());
                    taskFailureProcessing(imgDrawRecordPO);
                }
            }
        } catch (InterruptedException e) {
            imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_FAILURE.getStatusName());
            taskFailureProcessing(imgDrawRecordPO);
            throw new RuntimeException(e);
        }
    }

    @Transactional
    public void updatePhotoTaskStatus(ImgDrawRecordPO imgDrawRecordPO) {
        imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                .set(ImgDrawRecordPO::getStatus, imgDrawRecordPO.getStatus())
                .set(ImgDrawRecordPO::getFailReason, imgDrawRecordPO.getRemark()));
    }

    public void photoTaskFinished(ImgDrawRecordPO imgDrawRecordPO, BNotificationEnum bNotificationEnum) {
        try {
            SysNotificationPO notificationPO = SysNotificationPO.buildSysNotification(
                    imgDrawRecordPO.getUserId(),
                    bNotificationEnum.getIntValue(), //任务完成推送
                    bNotificationEnum.getStrTitle(),
                    bNotificationEnum.getStrContent(), 1, imgDrawRecordPO.getId()
            );

            boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(imgDrawRecordPO.getUserId(), BMessageSendEnum.PHOTO_PUSH, JSONObject.toJSONString(notificationPO)));
            log.info("photoTaskFinished消息推送状态:{}", state);
        } catch (Exception e) {
            log.error("taskFinished= {}", e.getMessage(), e);
        }
    }

    private String uploadImageToOSSPhoto(ImgDrawRecordPO imgDrawRecordPO, String imgFile, Integer folder) {
        try {
            long startTime = System.currentTimeMillis();
            OssParamBO ossParamBO = AliOSSUtils.uploadBase64(imgFile, folder);
            long endTime = System.currentTimeMillis();
            System.out.println("图片存储耗时时间= " + (endTime - startTime) + " 毫秒");
            if (StringUtils.isNotEmpty(ossParamBO.getImageUrl())) {
                Double whDivide = ImgDrawUtil.getWhDivide(Integer.valueOf(ossParamBO.getImageWidth()), Integer.valueOf(ossParamBO.getImageHeight()));

                if (imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getWidth, ossParamBO.getImageWidth())
                        .set(ImgDrawRecordPO::getHeight, ossParamBO.getImageHeight())
                        .set(ImgDrawRecordPO::getWhDivide, whDivide)
                        .set(ImgDrawRecordPO::getFinishTime, System.currentTimeMillis())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue())) > 0
                ) {
                    ImgDrawDetlPO imgDrawDetlPO = ImgDrawDetlPO.buildImgDrawDetlPO(imgDrawRecordPO.getId(), imgDrawRecordPO.getOptAttribute(), imgDrawRecordPO.getModeAttribute(), imgDrawRecordPO.getUserId(),
                            0, ossParamBO.getImageUrl(), whDivide,
                            Long.valueOf(ossParamBO.getFileSize()), Integer.valueOf(ossParamBO.getImageWidth()), Integer.valueOf(ossParamBO.getImageHeight()),
                            "webp", null);
                    imgDrawDetlPO.setImgUrl(getAddresByUrl(ossParamBO.getImageUrl()));
                    imgDrawDetlPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
                    imgDrawDetlPO.setIsSave(CommonIntEnum.SHOW_FALSE.getIntValue());
                    imgDrawDetlPO.setIsOpen(CommonIntEnum.SHOW_FALSE.getIntValue());
                    imgDrawDetlMapper.insert(imgDrawDetlPO);
                    return ossParamBO.getImageUrl();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("uploadBase64ToOSS Exception error: {}, {}", e.getMessage(), e);
            return null;
        } catch (Throwable e) {
            log.error("uploadBase64ToOSS Throwable error: {}, {}", e.getMessage(), e);
            return null;
        }

    }

    private String getAddresByUrl(String imgUrl) {
        try {
            return new URI(imgUrl).getPath();
        } catch (URISyntaxException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void taskFailureProcessing(ImgDrawRecordPO imgDrawRecordPO) {
        FlowRecordPO flowRecordPlus = FlowRecordPO.builder().recordType(CommonEnum.COMM_ZERO.getValue()).remark(FlowRecordEnum.PHOTO.getRemark()).build();
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_FINISH_FAIL.getValue());
        updatePhotoTaskStatus(imgDrawRecordPO);
        photoTaskFinished(imgDrawRecordPO, BNotificationEnum.FAILURE_NOTIF);
        asyncService.updateRemainingTimes(
                imgDrawRecordPO.getUserId(),
                DDUseRuleEnum.getDDUseRuleQuality(DDUseRuleEnum.DRAW_AI_PORTRAIT),
                flowRecordPlus,
                null
        );
    }

    //取消任务
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Result<Object> cancelJob(Long imgDrawId) throws IBusinessException {
        if (true) {
            return Result.ERROR(CommonResultEnum.TASK_PROGRESS_ERROR.getValue());
        }

        // 1、获取进行中的任务数量
        Long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawId));
        if (count < 1) {
            return Result.ERROR(CommonResultEnum.TASK_CANCEL_FAIL_ERROR.getValue());
        }
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectById(imgDrawId);
        List<String> jobIds = new ArrayList<>();
        jobIds.add(imgDrawRecordPO.getMjJobId());
        log.info("手动取消需要进行查询的jobIds列表信息：{}", jobIds);

        MJAccountHeaderBO mjAccountHeaderBO = JSONObject.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PRO_ACCOUNT.getStrKey()), MJAccountHeaderBO.class);
        if (mjAccountHeaderBO == null || mjAccountHeaderBO.getToken() == null) {
            List<MJAccountBO> mjAccountBOList = JSONArray.parseArray(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PRO_ACCOUNT_LIST.getStrKey()), MJAccountBO.class);
            for (MJAccountBO mjAccountBO : mjAccountBOList) {
                if (mjAccountBO.getAppToken() != null) {
                    mjAccountHeaderBO = new MJAccountHeaderBO();
                    mjAccountHeaderBO.setAppVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_APP_VERSION.getStrKey()));
                    mjAccountHeaderBO.setUserAgentVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_USER_AGENT.getStrKey()));
                    mjAccountHeaderBO.setToken(mjAccountBO.getAppToken());
                    mjAccountHeaderBO.setCookie(mjAccountBO.getAppCookies());
                    break;
                }
            }
        }
        if (mjAccountHeaderBO == null || mjAccountHeaderBO.getToken() == null) {
            log.error("重大问题：获取账号信息失败===================>");
            return Result.ERROR(CommonResultEnum.TASK_PROGRESS_ERROR.getValue());
        }
        List<JobStatusBO> jobStatusBOList;
        try {
            Result<List<JobStatusBO>> result = MJApis.getJobStatus(jobIds, mjAccountHeaderBO);
            if (result.getStatus() == 403) {
                log.error("手动取消任务执行重置账号信息：{}", mjAccountHeaderBO);
                BFeiShuUtil.sedCardErrorFromDraw(BFeiShuUtil.P1, "手动取消MJ任务-拉取绘图任务", mjAccountHeaderBO.toString(), true, "403");
            }
            jobStatusBOList = result.getData();
        } catch (Exception e) {
            log.error("手动取消MJ任务-拉取mj-api-绘图任务信息失败", e);
            return Result.ERROR(CommonResultEnum.TASK_CANCEL_FAIL_ERROR.getValue());
        }
        if (jobStatusBOList == null || jobStatusBOList.size() == 0) {
            return Result.ERROR(CommonResultEnum.TASK_CANCEL_FAIL_ERROR.getValue());
        }
        JobStatusBO jobStatusBO = jobStatusBOList.get(0);
        //任务成功完成进行定时任务推送
        if (jobStatusBO != null && ImgDrawUtil.getIsCommitted(jobStatusBO.getCurrentStatus())) {
            return Result.ERROR(CommonResultEnum.TASK_CANCEL_FAIL_ERROR.getValue());
        }

        // TODO 取消任务
        if (ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue() == imgDrawRecordPO.getModeAttribute()
                || ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N6.getValue() == imgDrawRecordPO.getModeAttribute()
                || ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue() == imgDrawRecordPO.getModeAttribute()
                || ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue() == imgDrawRecordPO.getModeAttribute()
                || ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6_1.getValue() == imgDrawRecordPO.getModeAttribute()
        ) {
            AdminMjAccountConfigPO adminMjAccountConfigPO = adminMjAccountConfigMapper.selectById(imgDrawRecordPO.getMjAccountId());
            if (adminMjAccountConfigPO == null) {
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            //TODO mj任务取消逻辑处理
            mjAccountHeaderBO.setToken(adminMjAccountConfigPO.getAppToken());

            if (MJApis.getMJJobsCancel(mjAccountHeaderBO, imgDrawRecordPO.getMjJobId())) {
                cancelJobRollback(imgDrawRecordPO); //回退点子公共方法
                RedisUtil.removeKey(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_JOB_ID.getStrKey(), imgDrawRecordPO.getMjJobId()));
                return Result.SUCCESS(CommonResultEnum.TASK_CANCEL_SUCCESS_ERROR.getValue());
            }
            return Result.ERROR(CommonResultEnum.TASK_CANCEL_FAIL_ERROR.getValue());
        }
        if (ImgOptModelEnum.DRAW_ATTRIBUTE_DE.getValue() == imgDrawRecordPO.getModeAttribute()) {
            //TODO dalle任务取消逻辑处理
            cancelJobRollback(imgDrawRecordPO); //回退点子公共方法
            return Result.SUCCESS(true);
        }
        return Result.ERROR(CommonResultEnum.TASK_CANCEL_FAIL_ERROR.getValue());
    }

    //TODO 客户手动取消任务退点子公共方法
    private void cancelJobRollback(ImgDrawRecordPO imgDrawRecordPO) throws IBusinessException {
        if (imgDrawRecordMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
                        .set(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
        ) < 1) {
            log.error("保存客户手动取消任务失败");
            throw new RuntimeException();
        }

        //2、返回用户点子消耗数量
        boolean result = checkService.setReturnDD(imgDrawRecordPO.getUserId(), imgDrawRecordPO.getUseDdQua());
        if (!result) {
            log.error("返回用户点子消耗数量失败:客户手动取消任务失败！");
            throw new RuntimeException();
        }

        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ZERO.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        flowRecordSub.setUserId(imgDrawRecordPO.getUserId());
        flowRecordSub.setNum(imgDrawRecordPO.getUseDdQua());
        if (flowRecordMapper.insert(flowRecordSub) < 1) {
            log.error("返回用户点子消耗数量失败:手动回滚2");
            throw new RuntimeException();
        }
    }

    //TODO 监听mj任务 超过30分钟自动取任务
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void listenDrawTimeOutCancelJob() {
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectOne(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .lt(ImgDrawRecordPO::getCreateTime, DateUtil.getDateSubdivisionMinutes(BDateUtil.getDateNowShanghai(), com.nacos.constant.CommonConst.TASK_TIMEOUT_MINUTES))
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
                        .isNotNull(ImgDrawRecordPO::getMjJobId)
                        .orderByAsc(ImgDrawRecordPO::getCreateTime)
                        .last("limit 0,1")
        );
        if (imgDrawRecordPO == null) {
            return;
        }
        log.info("监听任务超时任务列表:{}", imgDrawRecordPO);
        try {
            photoTaskFinished(imgDrawRecordPO, BNotificationEnum.DRAW_NOTIF);
            cancelJobRollback(imgDrawRecordPO); //回退点子公共方法
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Result<Object> getAspectRatioScale() {
        List<MjScaleVO> mjScaleList = new LinkedList<>();
        MJApis.getScaleList().forEach(scale -> {
            String[] whDivides = scale.split(":");
            mjScaleList.add(MjScaleVO.builder().scale(scale).whDivide(ImgDrawUtil.getWhDivide(Integer.parseInt(whDivides[0]), Integer.parseInt(whDivides[1]))).build());
        });
        Map<String, Object> mjScaleMap = new HashMap<>();
        mjScaleMap.put("defaultIndex", 6);
        mjScaleMap.put("mjScaleList", mjScaleList);
        return Result.SUCCESS(mjScaleMap);
    }

    @Override
    public void listenMjDrawQueueTask() {
        String accountLock = RedisUtil.getValue(BRedisKeyEnum.MJ_ACCOUNT_LOCK.getKey());
        if (Boolean.parseBoolean(accountLock)) {
            return;
        }
        Long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
                        .in(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue(), ImgDrawEnum.STATUS_QUEUING.getValue())
                        .isNull(ImgDrawRecordPO::getMjJobId)//仅操作mj类型的任务
                        .and(qw -> qw
                                .in(ImgDrawRecordPO::getModeAttribute,
                                        ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue(),
                                        ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N6.getValue(),
                                        ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue(),
                                        ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue(),
                                        ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6_1.getValue())
                                .or()
                                .in(ImgDrawRecordPO::getOptAttribute,
                                        ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_2X.getValue(),
                                        ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_4X.getValue(),
                                        ImgOptModelEnum.MJ_OPT_ATTRIBUTE_SUBTLE.getValue(),
                                        ImgOptModelEnum.MJ_OPT_ATTRIBUTE_STRONG.getValue(),
                                        ImgOptModelEnum.MJ_OPT_ATTRIBUTE_VARY_REGION.getValue(),
                                        ImgOptModelEnum.MJ_OPT_ATTRIBUTE_ZOOM_CUSTOM.getValue(),
                                        ImgOptModelEnum.MJ_OPT_ATTRIBUTE_ZOOM_CHANGE_AR.getValue(),
                                        ImgOptModelEnum.MJ_OPT_ATTRIBUTE_REMIX.getValue(),
                                        ImgOptModelEnum.MJ_OPT_ATTRIBUTE_REMIX_SUBTLE.getValue())
                        )
                        .orderByAsc(ImgDrawRecordPO::getCreateTime)
        );
        if (count < 1) {
            return;
        }
        log.info("绘图进行中任务数量：{}", count);
        try {
            // 2、获取进行中的任务信息列表
            List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper.selectList(
                    new LambdaQueryWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_DRAW.getValue())
                            .in(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue(), ImgDrawEnum.STATUS_QUEUING.getValue())
                            .isNull(ImgDrawRecordPO::getMjJobId)//仅操作mj类型的任务
                            .and(qw -> qw
                                    .in(ImgDrawRecordPO::getModeAttribute,
                                            ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue(),
                                            ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N6.getValue(),
                                            ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue(),
                                            ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue(),
                                            ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6_1.getValue())
                                    .or()
                                    .in(ImgDrawRecordPO::getOptAttribute,
                                            ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_2X.getValue(),
                                            ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_4X.getValue(),
                                            ImgOptModelEnum.MJ_OPT_ATTRIBUTE_SUBTLE.getValue(),
                                            ImgOptModelEnum.MJ_OPT_ATTRIBUTE_STRONG.getValue(),
                                            ImgOptModelEnum.MJ_OPT_ATTRIBUTE_VARY_REGION.getValue(),
                                            ImgOptModelEnum.MJ_OPT_ATTRIBUTE_ZOOM_CUSTOM.getValue(),
                                            ImgOptModelEnum.MJ_OPT_ATTRIBUTE_ZOOM_CHANGE_AR.getValue(),
                                            ImgOptModelEnum.MJ_OPT_ATTRIBUTE_REMIX.getValue(),
                                            ImgOptModelEnum.MJ_OPT_ATTRIBUTE_REMIX_SUBTLE.getValue())
                            )
                            .orderByAsc(ImgDrawRecordPO::getCreateTime)
            );
            if (imgDrawRecordPOList == null || imgDrawRecordPOList.isEmpty()) {
                return;
            }
            long concurrency = imgDrawRecordPOList.stream().filter(drawRecordPO -> drawRecordPO.getMjIsRelaxed() != null && drawRecordPO.getMjIsRelaxed() == 0).count();
            for (ImgDrawRecordPO imgDrawRecordPO : imgDrawRecordPOList) {
                if (imgDrawRecordPO.getStatus() == ImgDrawEnum.STATUS_QUEUING.getValue()) {
                    if (imgDrawRecordPO.getCreateTime().compareTo(DateUtil.getDateNowShanghai()) > 0) {
                        continue;
                    }
                }
                String mjTask = RedisUtil.getValue(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_DRAW_SUBMIT_TASK, String.valueOf(imgDrawRecordPO.getId())));
                if (mjTask == null) {
                    drawTaskFinishedUpdateDrawRecord(imgDrawRecordPO);
                    drawTaskFinished(imgDrawRecordPO);
                    break;
                }
                MjRequestParam mjRequestParam = JSON.parseObject(mjTask, MjRequestParam.class);
                if (mjRequestParam == null || ObjectUtil.isEmpty(mjRequestParam)) {
                    drawTaskFinishedUpdateDrawRecord(imgDrawRecordPO);
                    drawTaskFinished(imgDrawRecordPO);
                    break;
                }
                boolean isFastModel = mjRequestParam.getIsFastModel();

                List<AdminMjWebConfigBO> accountFastList = new ArrayList<>();
                List<AdminMjWebConfigBO> accountList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE.getKey()), new TypeReference<List<AdminMjWebConfigBO>>() {
                });
                if (!isFastModel || accountList == null || accountList.isEmpty() || concurrency == 5) {
                    accountList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_WEB_ACCOUNT_INFO_CACHE_TURTLE.getKey()), new TypeReference<List<AdminMjWebConfigBO>>() {
                    });
                }
                if (accountList == null || accountList.isEmpty()) {
                    drawTaskFinishedUpdateDrawRecord(imgDrawRecordPO);
                    drawTaskFinished(imgDrawRecordPO);
                    break;
                } else {
                    ++accountCount;
                    accountFastList = accountList.stream().filter(account -> (account.getPriorityLevel() != null && account.getPriorityLevel() == 2)).toList();
                }
                if (!accountFastList.isEmpty() && accountCount <= 5) {
                    accountList = accountFastList;
                } else {
                    accountCount = 0;
                }

                JobInfo jobInfo = null;
                for (AdminMjWebConfigBO adminMjWebConfigBO : accountList) {
                    Object result = MjWebApisUtil.postCreateImage(mjRequestParam.getRequest(), adminMjWebConfigBO.getCookie(), imgDrawRecordPO.getOptAttribute());
                    if (result != null) {
                        if (result instanceof JobInfo) {
                            jobInfo = (JobInfo) result;
                            System.out.println("==mj账号id:" + JSONObject.toJSONString(jobInfo));
                        } else if (result instanceof FailureJobInfo failureJobInfo) {
                            Result<Object> objectResult = handleMjJobFailureType(failureJobInfo.getType(), adminMjWebConfigBO.getId(), adminMjWebConfigBO.getAccountName(), mjRequestParam, imgDrawRecordPO.getInitImgUrls());
                            if (objectResult != null) {
                                System.out.println(JSONObject.toJSONString(objectResult));
                            }
                        }
                    }
                    if (jobInfo != null) {
                        break;
                    }

                    // 在这里添加等待时间，等待1秒（1000毫秒）
                    try {
                        Thread.sleep(1000); // 等待1秒
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        log.info("mj排队等待线程中断=" + e.getMessage());
                        continue;
                    }
                }

                if (jobInfo == null) {
                    log.error("绘图任务提交失败，请检查账号信息是否正确");
                    drawTaskFinishedUpdateDrawRecord(imgDrawRecordPO);
                    drawTaskFinished(imgDrawRecordPO);
                    break;
                }

                if (imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .set(ImgDrawRecordPO::getMjJobId, jobInfo.getJob_id())
                        .set(ImgDrawRecordPO::getWidth, jobInfo.getWidth())
                        .set(ImgDrawRecordPO::getHeight, jobInfo.getHeight())
                        .set(ImgDrawRecordPO::getImgQuantity, jobInfo.getBatch_size())
                        .set(ImgDrawRecordPO::getFinalPrompt, jobInfo.getFull_command())
                        .set(ImgDrawRecordPO::getMjIsRelaxed, isFastModel ? 0 : 1)
                        .set(ImgDrawRecordPO::getWhDivide, ImgDrawUtil.getWhDivide(jobInfo.getWidth(), jobInfo.getHeight()))) > 0) {
                    ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
                    if (jobInfo.getBatch_size() > 0) {
                        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
                        imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
                        imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
                        imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
                        imgDrawHistoryVO.setInitImgUrls(imgDrawRecordPO.getInitImgUrls() == null ? null : Arrays.asList(imgDrawRecordPO.getInitImgUrls().split(",")));
                        imgDrawHistoryVO.setPrompt(imgDrawRecordPO.getPromptInit());
                        imgDrawHistoryVO.setPromptUse(imgDrawRecordPO.getPromptUse());
                        imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
                        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
                        imgDrawHistoryVO.setImgQuantity(jobInfo.getBatch_size());
                        imgDrawHistoryVO.setInitImgObject(imgDrawRecordPO.getInitImgObject());
                        List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();
                        for (int i = 0; i < jobInfo.getBatch_size(); i++) {
                            ImgDrawDetlVO imgDrawDetlVO = new ImgDrawDetlVO();
                            imgDrawDetlVO.setDrawRecordId(imgDrawRecordPO.getId());
                            imgDrawDetlVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
                            imgDrawDetlVO.setImgIndex(i);
                            imgDrawDetlVO.setWhDivide(ImgDrawUtil.getWhDivide(jobInfo.getWidth(), jobInfo.getHeight()));
                            imgDrawDetlVO.setImgWidth(jobInfo.getWidth());
                            imgDrawDetlVO.setImgHeight(jobInfo.getHeight());
                            imgDrawDetlVOS.add(imgDrawDetlVO);
                        }
                        imgDrawHistoryVO.setImgDrawDetls(imgDrawDetlVOS);
                        log.info("imgDrawHistoryVO= {}", imgDrawHistoryVO);
                        //缓存任务有效期为一天
                        RedisUtil.setValueSeconds(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_JOB_ID.getStrKey(), jobInfo.getJob_id()), JSONObject.toJSONString(imgDrawHistoryVO), 1, TimeUnit.DAYS);
                        //异步渐显推送
                        MJAPIUtil.sendHandshakeRequest(jobInfo.getJob_id(), new MjWebSocketListener(jobInfo.getJob_id(), redisServer));
                    }
                }
            }
            log.info("绘图进入排队。。。");
        } catch (Exception e) {
            e.getMessage();
            log.info("绘图任务提交失败，请检查账号信息是否正确=" + e.getMessage());
        }
    }

    private boolean checkTimeDifference(Date startDate, int minutes) {
        long differenceInMillis = new Date().getTime() - startDate.getTime();
        long differenceInMinutes = TimeUnit.MILLISECONDS.toMinutes(differenceInMillis);
        return differenceInMinutes >= minutes;
    }

    public void drawTaskFinishedUpdateDrawRecord(ImgDrawRecordPO imgDrawRecordPO) {
        imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
        FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ZERO.getDtoKey()).remark("灵感绘画").build();
        asyncService.updateRemainingTimes(imgDrawRecordPO.getUserId(), imgDrawRecordPO.getUseDdQua(), flowRecordPO, null);
    }

    public void drawTaskFinished(ImgDrawRecordPO imgDrawRecordPO) {
        try {
            SysNotificationPO notificationPO = SysNotificationPO.buildSysNotification(
                    imgDrawRecordPO.getUserId(),
                    BNotificationEnum.FAILURE_NOTIF.getIntValue(), //任务完成推送
                    BNotificationEnum.FAILURE_NOTIF.getStrTitle(),
                    "", 1, imgDrawRecordPO.getId()
            );

            boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(imgDrawRecordPO.getUserId(), BMessageSendEnum.NOTIFICATION_PUSH, JSONObject.toJSONString(notificationPO)));
            log.info("【队列】通知消息推送状态:{}", state);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("taskFinished= {}", e.getMessage(), e);
        }
    }

}
