package com.nacos.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.business.bytes.ByteApisUtil;
import com.business.bytes.enums.ByteStatusEnum;
import com.business.bytes.model.PosterReqBO;
import com.business.db.mapper.*;
import com.business.db.model.dto.goapi.GoApiPhotoDTO;
import com.business.db.model.dto.goapi.GoApiUpscaleDTO;
import com.business.db.model.dto.kong.BytePosterDTO;
import com.business.db.model.dto.sd.SDToSketchDTO;
import com.business.db.model.po.*;
import com.business.db.model.vo.ImgModelConfigVO;
import com.business.enums.*;
import com.business.le.LeonardoUtil;
import com.business.le.model.LeonardoBO;
import com.business.le.model.LeonardoStyleBO;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.business.model.po.ImgDrawRecordPO;
import com.business.model.vo.ImgDrawDetlVO;
import com.business.model.vo.ImgDrawHistoryVO;
import com.business.tengxunyun.BTengXunUtil;
import com.business.utils.BFileUtil;
import com.business.utils.BStringUtil;
import com.business.utils.BThirdPartyKey;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.alioss.OSSApis;
import com.nacos.auth.JwtNewUtil;
import com.nacos.constant.CommonConst;
import com.nacos.constant.DrawConst;
import com.nacos.ddimg.ImgDrawUtil;
import com.nacos.ddimg.model.ImgScaleDTO;
import com.nacos.enums.*;
import com.nacos.enums.goapi.CurrentStatusEnum;
import com.nacos.exception.DzBalanceE;
import com.nacos.exception.E;
import com.nacos.exception.IBusinessException;
import com.nacos.model.OssParamBO;
import com.nacos.novitaaiapi.model.GoApiFaceResponseBodyVO;
import com.nacos.novitaaiapi.model.dto.MergeFaceRequestBodyDTO;
import com.nacos.result.Result;
import com.nacos.service.*;
import com.nacos.service.mp.IUserDDRecordService;
import com.nacos.utils.ImgDrawPUtil;
import com.nacos.utils.OSSUtils;
import com.nacos.utils.ai.AiFaceUtil;
import com.nacos.utils.goapi.GoApiUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class KongAreaServiceImpl implements IKongAreaService {

    @Resource
    private DzRollbackService dzRollbackService;
    @Resource
    private IUserDDRecordService iUserDDRecordService;
    @Resource
    private ImgDrawDetlService imgDrawDetlService;//绘图明细
    @Resource
    private CheckBalanService checkBalanService;
    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;//绘图记录
    @Resource
    private ImgDrawDetlMapper imgDrawDetlMapper;
    @Resource
    private ImgModelConfigMapper imgModelConfigMapper;
    @Resource
    private IImgFaceTemplateService imgFaceTemplateService;
    @Resource
    private IUserDDRecordService userDDRecordService;
    @Resource
    private PhotoTopicSceneMapper photoTopicSceneMapper;
    @Resource
    private FunctionStyleConfigMapper functionStyleConfigMapper;
    @Resource
    private UserDDRecordMapper userDDRecordMapper;
    @Resource
    private CheckService checkService;
    @Resource
    private AsyncService asyncService;
    @Resource
    private DrawService drawService;
    // 高清重绘任务进度
    private static int progress = 0;

    @Override
    @Transactional(rollbackFor = {Exception.class, E.class})
    public Result<Object> personalPhotoFaceSwap(GoApiPhotoDTO goApiPhotoDTO) throws IBusinessException {
        ImgFaceTemplatePO imgFaceTemplatePO = imgFaceTemplateService.getById(goApiPhotoDTO.getFaceTemplateId());
        if (imgFaceTemplatePO == null || ObjectUtil.isEmpty(imgFaceTemplatePO)) {
            return Result.ERROR("写真模板不存在");
        }
        PhotoTopicScenePO topicScenePO = photoTopicSceneMapper.selectById(goApiPhotoDTO.getSceneId());
        if (topicScenePO == null || ObjectUtil.isEmpty(topicScenePO)) {
            return Result.ERROR("写真场景不存在");
        }

        // 校验用户是否为VIP用户-暂时不用
        boolean isVip = userDDRecordService.getUserIsVip();
        // 判断用户点子数是否充足 TODO 扣除用户点子 ===== 查询点子规则数据
        double dzQuantity = BDDUseNumEnum.getOptMJ(ImgOptModelEnum.GOAPI_OPERATE_EDIT_PHOTO.getValue(), isVip);
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(FlowRecordEnum.PHOTO.getRemark()).build();
        // TODO 扣除点子
        checkBalanService.checkUser(goApiPhotoDTO.getUserId(), dzQuantity, flowRecordSub);

        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecordDe();
        imgDrawRecordPO.setUseDdQua(dzQuantity);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setUserId(goApiPhotoDTO.getUserId());//装载用户id
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_PORTRAIT.getValue());
        imgDrawRecordPO.setPhotoSceneId(goApiPhotoDTO.getSceneId());
        if (goApiPhotoDTO.getFunctionConfigId() == null) {
            GoApiFaceResponseBodyVO goApiFaceResBodyVO = submitPersonalPhoto(imgFaceTemplatePO, topicScenePO);
            log.info("====++++++====提交写真返回参数= {}", goApiFaceResBodyVO);
            // TODO J旧的方法 asyncService.personalPhoto(dto, imgDrawRecordPO, dzQuantity, imgFaceTemplatePO);
            if (goApiFaceResBodyVO == null || goApiFaceResBodyVO.getData() == null) {
                drawService.taskFailureProcessing(imgDrawRecordPO);
                return Result.ERROR("写真任务失败,请重试");
            }
            imgDrawRecordPO.setGoTaskId(goApiFaceResBodyVO.getData().getTask_id());
        } else if (goApiPhotoDTO.getFunctionConfigId().intValue() == BFunctionConfigEnum.FUNCTION_CONFIG_PHOTO.getIntValue()) {
            asyncService.asynExecutionFacialFusion(imgDrawRecordPO, imgFaceTemplatePO, topicScenePO);
        } else {
            return Result.ERROR("功能配置错误");
        }
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            return Result.SUCCESS(String.valueOf(imgDrawRecordPO.getId()));
        }
        // TODO 失败退点子
        drawService.taskFailureProcessing(imgDrawRecordPO);
        return Result.ERROR("写真任务失败,请重试");
    }

    ;

    public GoApiFaceResponseBodyVO submitPersonalPhoto(ImgFaceTemplatePO imgFaceTemplatePO, PhotoTopicScenePO topicScenePO) {
        MergeFaceRequestBodyDTO mergeFaceReqBodyDTO = new MergeFaceRequestBodyDTO();
        mergeFaceReqBodyDTO.setFaceImageUrl(imgFaceTemplatePO.getImgUrl());
        mergeFaceReqBodyDTO.setImageFileUrl(topicScenePO.getSceneUrl());
        log.info("换脸参数MergeFaceRequestBodyDTO= {}", mergeFaceReqBodyDTO);
        long startTime = System.currentTimeMillis();
        GoApiFaceResponseBodyVO goApiFaceResponseBodyVO = AiFaceUtil.goApiFaceSwap(mergeFaceReqBodyDTO);
        long endTime = System.currentTimeMillis();
        System.out.println("GoApi换脸耗时时间= " + (endTime - startTime) + " 毫秒");
        if (goApiFaceResponseBodyVO != null && goApiFaceResponseBodyVO.getCode() == 200) {
            return goApiFaceResponseBodyVO;
        }
        return null;
    }

    ;

    private ImgDrawRecordPO initImgDrawRecordDe() {
        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setId(IdWorker.getId());
        imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.CC_OPT_ATTRIBUTE_FACE_FUSION.getValue());
        imgDrawRecordPO.setModeAttribute(ImgOptModelEnum.DRAW_ATTRIBUTE_VARIANT.getValue());
        imgDrawRecordPO.setPromptInit(FlowRecordEnum.PHOTO.getRemark());
        imgDrawRecordPO.setPromptUse(FlowRecordEnum.PHOTO.getRemark());
        imgDrawRecordPO.setDescription(FlowRecordEnum.PHOTO.getRemark());
        return imgDrawRecordPO;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, E.class})
    public Result<Object> upscaleImgEditing(GoApiUpscaleDTO goApiUpscaleDTO) throws IBusinessException {
        if (goApiUpscaleDTO.getUserId() == null) {
            goApiUpscaleDTO.setUserId(JwtNewUtil.getUserId());
        }
        long imageSize = ImgDrawUtil.getImageSize(goApiUpscaleDTO.getImgWidth(), goApiUpscaleDTO.getImgHeight());
        log.info("==*==上传图片尺寸=" + imageSize + "====图片宽=" + goApiUpscaleDTO.getImgWidth() + "====图片高=" + goApiUpscaleDTO.getImgHeight());
        String taskId = null;
        try {
            String imgUrl = goApiUpscaleDTO.getImgUrl();
            if (goApiUpscaleDTO.getOperate() == ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_2X.getValue()
                    || goApiUpscaleDTO.getOperate() == ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_4X.getValue()) {
                int shortSide = 0;
                int longSide = 0;
                if (goApiUpscaleDTO.getImgWidth().intValue() > goApiUpscaleDTO.getImgHeight().intValue()) {
                    shortSide = goApiUpscaleDTO.getImgHeight().intValue();
                    longSide = goApiUpscaleDTO.getImgWidth().intValue();
                } else {
                    longSide = goApiUpscaleDTO.getImgHeight().intValue();
                    shortSide = goApiUpscaleDTO.getImgWidth().intValue();
                }
                if (longSide >= 32 && longSide <= 1920 && shortSide >= 32 && shortSide <= 1080) {
                } else {
                    return Result.ERROR("请使用分辨率在1920x1080以下的图片!");
                }
                long imageMBSize = ImgDrawUtil.getImageMBSize(imageSize);
                if (imageMBSize > 3) {
                    return Result.ERROR("图片大小不能大于3M!");
                }
//                int[] size = BFileUtil.compressionAspectRatio(goApiUpscaleDTO.getImgWidth(), goApiUpscaleDTO.getImgHeight(), 1, 1024, 1);
//                taskId = GoApiUtil.imageUpscaleHighDefinition(imgUrl + "?x-oss-process=image/format,png/resize,w_"+ size[0] +",h_"+ size[1], GoApiUtil.getScale(goApiUpscaleDTO.getOperate()));
                //随便加的，没啥用，为了符合逻辑通顺
                taskId = String.valueOf(goApiUpscaleDTO.getOperate());
            } else if (goApiUpscaleDTO.getOperate() == ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_REDRAW.getValue()) {
                String url = imgUrl + "?x-oss-process=image/format,png/resize,w_2048,h_2048";
                byte[] imageData = BFileUtil.downloadImageGetByte(url);
                if (imageData == null) {
                    return Result.ERROR("图片下载失败,请重试");
                }

                HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.LE_DRAW_KEY.getDictType());
                if (dictConfigMap == null) {
                    return Result.ERROR("模型维护中...");
                }
                Map<String, String> stringMap = LeonardoUtil.getPresignedUrlUploadingImg(dictConfigMap.get(DictConfigEnum.LE_DRAW_KEY.getDictKey()));
                if (stringMap == null) {
                    Result.ERROR("图片初始化失败,请重试");
                }
                boolean isSuccess = LeonardoUtil.presignedUrlUploadingImg(stringMap.get("url"), stringMap.get("fields"), imageData);
                if (!isSuccess) {
                    Result.ERROR("图片上传失败,请重试");
                }

                taskId = LeonardoUtil.generativeUpscale(new LeonardoBO(stringMap.get("imageId"), null, null, null), dictConfigMap.get(DictConfigEnum.LE_DRAW_KEY.getDictKey()));
                /*taskId = LeonardoUtil.imageUpscaleRedrawing(new LeonardoBO(stringMap.get("imageId"), goApiUpscaleDTO.getUpscalerStyle(),
                        goApiUpscaleDTO.getCreativityStrength(), goApiUpscaleDTO.getUpscaleMultiplier()), file.getAbsolutePath());*/
            } else {
                return Result.ERROR("没有可执行的操作");
            }
        } catch (Exception e) {
            log.error("leonardo-upscale-redraw-error {}", e.getMessage(), e);
            return Result.ERROR("目前人数较多,请稍后再试");
        }
        if (taskId == null) {
            log.error("leonardo-upscale-redraw-error: " + taskId);
            return Result.ERROR("目前人数较多,请稍后再试");
        }

        //校验剩余点子数量
        Double total = userDDRecordMapper.selectUserTotal(goApiUpscaleDTO.getUserId());
        boolean isVip = iUserDDRecordService.getUserIsVip();
        double dzQuantity = BDDUseNumEnum.getOptMJ(goApiUpscaleDTO.getOperate(), isVip);
        if (total == null || total < dzQuantity) {
            throw new DzBalanceE("剩余点子数不足");
        }
        Integer optAttribute = ImgDrawPUtil.getOptAttributeByOptNew(null, null, goApiUpscaleDTO.getOperate());

        String optTitleOne = ImgOptModelEnum.getOptTitleOne(optAttribute);
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(optAttribute);
        String description = optTitleOne.concat(optTitleTwo != null ? ":" + optTitleTwo : "");

        // 初始化任务记录
        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setId(IdWorker.getId());
        imgDrawRecordPO.setModeAttribute(ImgOptModelEnum.DRAW_ATTRIBUTE_VARIANT.getValue());
        imgDrawRecordPO.setOptAttribute(optAttribute);
        imgDrawRecordPO.setDescription(description);
        imgDrawRecordPO.setUseDdQua(dzQuantity);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setUserId(goApiUpscaleDTO.getUserId());//装载用户id
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_HEIGHT.getValue());
        // 初始化任务详情
        ImgDrawDetlPO imgDrawDetlPO = new ImgDrawDetlPO();
        imgDrawDetlPO.setDrawRecordId(imgDrawRecordPO.getId());
        imgDrawDetlPO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawDetlPO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawDetlPO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawDetlPO.setImgIndex(1);//固定1
        imgDrawDetlPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
        imgDrawDetlPO.setImgSourceUrl(goApiUpscaleDTO.getImgUrl());
        imgDrawDetlPO.setIsOpen(CommonIntEnum.IS_FALSE.getIntValue());//不公开，换枚举
        if (goApiUpscaleDTO.getOperate().intValue() == ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_2X.getValue()
                || goApiUpscaleDTO.getOperate().intValue() == ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_4X.getValue()
                || goApiUpscaleDTO.getOperate().intValue() == ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_8X.getValue()) {
            imgDrawRecordPO.setGoTaskId(taskId);
        } else {
            imgDrawRecordPO.setLeJobId(taskId);
        }
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) < 1) {
            return Result.ERROR("生成绘画记录表失败");
        }
        if (imgDrawDetlMapper.insert(imgDrawDetlPO) < 1) {
            return Result.ERROR("生成绘画记录详情表失败");
        }
        //扣除点子数量
        checkService.ddDeduct(goApiUpscaleDTO.getUserId(), dzQuantity, description);
        return Result.SUCCESS(String.valueOf(imgDrawRecordPO.getId()));
    }


    @Override
    @Transactional
    public void fetchUpscaleTaskResult() throws Exception {
        List<ImgDrawRecordPO> imgDrawRecordPOS = getPendingImgDrawRecords();
        if (imgDrawRecordPOS == null || imgDrawRecordPOS.isEmpty()) {
            return;
        }
        List<String> strProgress = Arrays.asList("15", "30", "45", "50", "69", "83", "90");
        for (ImgDrawRecordPO imgDrawRecordPO : imgDrawRecordPOS) {
            String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
            String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
            ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectOne(new LambdaQueryWrapper<ImgDrawDetlPO>()
                    .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawRecordPO.getId()));
            if (imgDrawDetlPO == null) {
                handleFailedTask(imgDrawRecordPO);
                continue;
            }
            // 判断类型是否是2、4、8倍放大
            if (imgDrawRecordPO.getOptAttribute().intValue() == ImgOptModelEnum.GOAPI_OPT_ATTRIBUTE_UPSCALE_2X.getValue()
                    || imgDrawRecordPO.getOptAttribute().intValue() == ImgOptModelEnum.GOAPI_OPT_ATTRIBUTE_UPSCALE_4X.getValue()) {
                //通过oss上传生成任务
                Long upscaleFactor = 2L;
                if (imgDrawRecordPO.getOptAttribute().intValue() == ImgOptModelEnum.GOAPI_OPT_ATTRIBUTE_UPSCALE_2X.getValue()) {
                    upscaleFactor = 2L;
                } else if (imgDrawRecordPO.getOptAttribute().intValue() == ImgOptModelEnum.GOAPI_OPT_ATTRIBUTE_UPSCALE_4X.getValue()) {
                    upscaleFactor = 4L;
                } else {
                    log.error("操作类型传递错误！");
                    break;
                }
                String result = OSSApis.makeSuperResolutionImageRequest(imgDrawDetlPO.getImgSourceUrl(), upscaleFactor);

                if (result.startsWith("http://")) {
                    URL url = new URL(result);
                    String fileName = Paths.get(url.getPath()).getFileName().toString();
                    boolean isSuccessful = handleCompleteTask(imgDrawRecordPO, imgDrawDetlPO, fileName, result);
                    if (!isSuccessful) {
                        handleFailedTask(imgDrawRecordPO);
                        continue;
                    }
                    progress = 0;
                    imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_SUCCESS.getStatusName());
                    notificationMessage(imgDrawRecordPO, optTitleOne, optTitleTwo);
                } else {
                    handleFailedTask(imgDrawRecordPO);
                    continue;
                }
            } else {
//                // 处理le高清重绘任务
//                String result = handleRedrawing(imgDrawRecordPO, imgDrawDetlPO);
//                log.info("le高清重绘任务结果：{}", result);
//                if (result.equals("FAILED")) {
//                    handleFailedTask(imgDrawRecordPO);
//                    continue;
//                }
//                if (result.contains("https://")) {
//                    boolean isSuccessful = handleCompleteTask(imgDrawRecordPO, imgDrawDetlPO, imgDrawRecordPO.getLeJobId(), result);
//                    if (!isSuccessful) {
//                        handleFailedTask(imgDrawRecordPO);
//                        continue;
//                    }
//                    progress = 0;
//                    imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_SUCCESS.getStatusName());
//                    notificationMessage(imgDrawRecordPO, optTitleOne, optTitleTwo);
//                }
                System.out.println("暂时不用");
            }
            // 构建 ImgDrawHistoryVO
            ++progress;
            String jobRunningSchedule = strProgress.size() > (progress - 1) ? strProgress.get(progress - 1) : strProgress.get(strProgress.size() - 1);
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
            imgDrawHistoryVO.setJobRunningSchedule(jobRunningSchedule + "%");
            imgDrawHistoryVO.setImgDrawDetls(getImgDrawDetlVOS(Arrays.asList(imgDrawDetlPO), imgDrawHistoryVO));
            // 推送通知
            taskMessagePush(imgDrawHistoryVO, "任务已完成");
        }
    }

    private String handleMagnification(String taskId) {
        // todo 处理goapi放大任务
        String responseBody = GoApiUtil.fetchImageUpscaleResult(taskId);
        if (responseBody == null) {
            return CurrentStatusEnum.FAILED.getStatus();
        }
        JSONObject jsonObject = JSONObject.parseObject(responseBody);
        JSONObject dataObject = jsonObject.getJSONObject("data");
        String status = dataObject.getString("status");
        if (CurrentStatusEnum.FAILED.getStatus().equals(status)) {
            return CurrentStatusEnum.FAILED.getStatus();
        }
        if (CurrentStatusEnum.SUCCESS.getStatus().equals(status)) {
            JSONObject taskInputObject = dataObject.getJSONObject("task_info").getJSONObject("task_input");
            JSONObject taskOutputObject = dataObject.getJSONObject("task_result").getJSONObject("task_output");
            String imgUrlOriginal = taskInputObject.getString("image");
            String imgUrl = taskOutputObject.getString("image_url");
            if (imgUrlOriginal == null || imgUrl == null) {
                return CurrentStatusEnum.FAILED.getStatus();
            }
            return imgUrl;
        }
        return CurrentStatusEnum.PROCESSING.getStatus();
    }

    private String handleRedrawing(ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO) {
        // todo 处理le高清重绘任务
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.LE_DRAW_KEY.getDictType());
        if (dictConfigMap == null) {
            log.info("LE没有可用的key: {}", "请立即更换");
            return "FAILED";
        }

        String responseBody = LeonardoUtil.getUpscaledImgVariationId(imgDrawRecordPO.getLeJobId(), dictConfigMap.get(DictConfigEnum.LE_DRAW_KEY.getDictKey()));
        if (responseBody == null) {
            return "FAILED";
        }
        JSONObject jsonObject = JSONObject.parseObject(responseBody);
        JSONArray variationsArray = jsonObject.getJSONArray("generated_image_variation_generic");
        if (variationsArray == null || variationsArray.isEmpty()) {
            return "FAILED";
        }
        JSONObject variationObj = variationsArray.getJSONObject(0);
        if ("COMPLETE".equals(variationObj.getString("status"))) {
            return variationObj.getString("url");
        }
        return "PENDING";
    }

    private Boolean handleCompleteTask(ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO, String fileName, String imageUrl) {
        // todo 处理成功的任务
        OssParamBO ossParamBO = uploadImageToOss(fileName.concat("_0"), imageUrl);
        if (ossParamBO == null) {
            return false;
        }
        Double whDivide = ImgDrawUtil.getWhDivide(Integer.valueOf(ossParamBO.getImageWidth()), Integer.valueOf(ossParamBO.getImageHeight()));
        // 绘画主表赋值
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());
        imgDrawRecordPO.setFinishTime(System.currentTimeMillis());
        imgDrawRecordPO.setWidth(Integer.valueOf(ossParamBO.getImageWidth()));
        imgDrawRecordPO.setHeight(Integer.valueOf(ossParamBO.getImageHeight()));
        imgDrawRecordPO.setWhDivide(whDivide);
        // 绘画明细赋值
        imgDrawDetlPO.setImgUrl(ossParamBO.getImageUrl());
        imgDrawDetlPO.setImgWidth(Integer.valueOf(ossParamBO.getImageWidth()));
        imgDrawDetlPO.setImgHeight(Integer.valueOf(ossParamBO.getImageHeight()));
        imgDrawDetlPO.setWhDivide(whDivide);
        imgDrawDetlPO.setImgType("image/webp");

        if (imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                .set(ImgDrawRecordPO::getStatus, imgDrawRecordPO.getStatus())
                .set(ImgDrawRecordPO::getFinishTime, imgDrawRecordPO.getFinishTime())
                .set(ImgDrawRecordPO::getWidth, imgDrawRecordPO.getWidth())
                .set(ImgDrawRecordPO::getHeight, imgDrawRecordPO.getHeight())
                .set(ImgDrawRecordPO::getWhDivide, imgDrawRecordPO.getWhDivide())) < 1) {
            return false;
        }
        if (imgDrawDetlMapper.update(null, new LambdaUpdateWrapper<ImgDrawDetlPO>()
                .eq(ImgDrawDetlPO::getId, imgDrawDetlPO.getId())
                .set(ImgDrawDetlPO::getImgUrl, imgDrawDetlPO.getImgUrl())
                .set(ImgDrawDetlPO::getImgWidth, imgDrawDetlPO.getImgWidth())
                .set(ImgDrawDetlPO::getImgHeight, imgDrawDetlPO.getImgHeight())
                .set(ImgDrawDetlPO::getWhDivide, imgDrawDetlPO.getWhDivide())
                .set(ImgDrawDetlPO::getImgType, imgDrawDetlPO.getImgType())) < 1) {
            return false;
        }
        return true;
    }

    private List<ImgDrawRecordPO> getPendingImgDrawRecords() {
        return imgDrawRecordMapper.selectList(new LambdaQueryWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_HEIGHT.getValue())
                .notIn(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue(), ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
                .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
        );
    }

    /**
     * 上传图片到OSS
     * @param fileName 文件名
     * @param imgUrl 图片URL
     * @return OssParamBO
     */
    public OssParamBO uploadImageToOss(String fileName, String imgUrl) {
        int maxRetries = 3; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                OssParamBO ossParamBO = OSSUtils.uploadURL(DrawConst.endpoint, DrawConst.accessKeyId, DrawConst.secretAccessKey, DrawConst.bucketName,
                        imgUrl, fileName, 1);
                if (ossParamBO != null) {
                    return ossParamBO;
                }
                log.error("uploadImageToOss 图片上传失败，重试次数：" + (retryCount + 1), imgUrl);
            } catch (Exception e) {
                log.error("上传图片到OSS发生异常，重试次数：" + (retryCount + 1), e);
            }
            retryCount++;
        }
        log.error("uploadImageToOss 图片上传失败，超过最大重试次数", imgUrl);
        return null;
    }

    @Transactional(rollbackFor = {Exception.class, E.class})
    public void handleFailedTask(ImgDrawRecordPO imgDrawRecordPO) {
        // todo 处理成功的任务
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        String description = optTitleOne.concat(optTitleTwo != null ? ":" + optTitleTwo : "");
        FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ZERO.getDtoKey()).remark(description).build();
        asyncService.updateRemainingTimes(imgDrawRecordPO.getUserId(), imgDrawRecordPO.getUseDdQua(), flowRecordPO, null);
        imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
        );

        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
        imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_FAIL.getValue());
        // 推送通知
        taskMessagePush(imgDrawHistoryVO, "任务失败");
        imgDrawRecordPO.setRemark(MjTaskStatusEnum.DRAW_TASK_FAILURE.getStatusName());
        notificationMessage(imgDrawRecordPO, optTitleOne, optTitleTwo == null ? optTitleOne : optTitleTwo);
    }

    private void taskMessagePush(ImgDrawHistoryVO imgDrawHistoryVO, String message) {
        if (imgDrawHistoryVO == null) {
            log.warn("imgDrawHistoryVO is null. Unable to push message.");
            return;
        }
        boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(imgDrawHistoryVO.getUserId(), BMessageSendEnum.VIDEO_JOB_SD_PUSH, JSONObject.toJSONString(imgDrawHistoryVO)));
        log.info("高清放大任务推送状态:{}", state);
    }


    public void notificationMessage(ImgDrawRecordPO imgDrawRecordPO, String optTitleOne, String optTitleTwo) {
        try {
            String notificationMessage = optTitleOne + imgDrawRecordPO.getRemark();
            SysNotificationPO notificationPO = SysNotificationPO.buildSysNotification(
                    imgDrawRecordPO.getUserId(),
                    BNotificationEnum.HIGH_REDRAW.getIntValue(), //任务完成推送
                    notificationMessage,
                    optTitleTwo, 1, imgDrawRecordPO.getId()
            );
            boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(imgDrawRecordPO.getUserId(), BMessageSendEnum.NOTIFICATION_PUSH, JSONObject.toJSONString(notificationPO)));
            log.info("高清放大任务推送状态:{}", state);
        } catch (Exception e) {
            log.error("taskFinished= {}", e.getMessage(), e);
        }
    }

    private static ImgDrawHistoryVO intiImgDrawHistoryVO(ImgDrawRecordPO imgDrawRecordPO) {
        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setFunType(imgDrawRecordPO.getFunType());
        imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
        imgDrawHistoryVO.setImgQuantity(imgDrawRecordPO.getImgQuantity());
        imgDrawHistoryVO.setTimeTitle(ImgDrawUtil.getTimeTitle(imgDrawRecordPO.getCreateTime()));
        imgDrawHistoryVO.setOptTitleOne(ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute()));
        return imgDrawHistoryVO;
    }

    @NotNull
    private static List<ImgDrawDetlVO> getImgDrawDetlVOS(List<ImgDrawDetlPO> imgDrawDetlPOList, ImgDrawHistoryVO imgDrawHistoryVO) {
        List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();
        for (ImgDrawDetlPO imgDrawDetlPO : imgDrawDetlPOList) {
            ImgDrawDetlVO imgDrawDetlVO = new ImgDrawDetlVO();
            imgDrawDetlVO.setId(imgDrawDetlPO.getId());
            imgDrawDetlVO.setDrawRecordId(imgDrawDetlPO.getDrawRecordId());
            imgDrawDetlVO.setOptAttribute(imgDrawDetlPO.getOptAttribute());
            imgDrawDetlVO.setImgIndex(imgDrawDetlPO.getImgIndex());
            imgDrawDetlVO.setWhDivide(imgDrawDetlPO.getWhDivide());
            imgDrawDetlVO.setImgWidth(imgDrawDetlPO.getImgWidth());
            imgDrawDetlVO.setImgHeight(imgDrawDetlPO.getImgHeight());
            imgDrawDetlVO.setImgHue(imgDrawDetlPO.getImgHue());
            imgDrawDetlVO.setImgSize(imgDrawDetlPO.getImgSize());
            imgDrawDetlVO.setImgSourceUrl(imgDrawDetlPO.getImgSourceUrl());
            imgDrawDetlVO.setImgUrl(imgDrawDetlPO.getImgUrl());
            imgDrawDetlVO.setImgType(imgDrawDetlPO.getImgType());
            imgDrawDetlVO.setIsPublish(imgDrawDetlPO.getIsPublish());
            imgDrawDetlVO.setVideoUrl(imgDrawDetlPO.getVideoUrl());
            if (Objects.equals(imgDrawDetlPO.getIsPublish(), BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue())) {
                imgDrawHistoryVO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue());
            }
            imgDrawDetlVOS.add(imgDrawDetlVO);
        }
        return imgDrawDetlVOS;
    }

    // TODO SD草图上色、风格迁移-------暂时不用
    // TODO 替换等功能正常使用
    @Override
    @Transactional(rollbackFor = {Exception.class, E.class})
    public Result<Object> imgUpscaleOptControl(SDToSketchDTO sdToSketchDTO) throws IBusinessException {
        if (sdToSketchDTO.getUserId() == null) {
            sdToSketchDTO.setUserId(JwtNewUtil.getUserId());
        }
        if (sdToSketchDTO.getOperate() == null) {
            sdToSketchDTO.setOperate(BFunctionConfigEnum.getAttributeEditByIntValue(sdToSketchDTO.getFunctionConfigId()));
        }
        if (sdToSketchDTO.getOperate() == null) {
            return Result.ERROR("系统繁忙，请稍后再试");
        }

        // 获取配置
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.LE_DRAW_KEY.getDictType());
        if (dictConfigMap == null) {
            log.info("LE没有可用的key: {}", "请立即更换");
            return Result.ERROR("模型升级维护中...");
        }
        // 校验剩余点子数量
        Double total = userDDRecordMapper.selectUserTotal(sdToSketchDTO.getUserId());
        boolean isVip = iUserDDRecordService.getUserIsVip();
        double dzQuantity = BDDUseNumEnum.getOptMJ(sdToSketchDTO.getOperate(), isVip);
        if (total == null || total < dzQuantity) {
            throw new DzBalanceE("剩余点子数不足");
        }

        // 获取图片大小
        long imageSize = ImgDrawUtil.getImageSize(sdToSketchDTO.getImgWidth(), sdToSketchDTO.getImgHeight());
        log.info("==*==上色图片尺寸=" + imageSize + "====图片宽=" + sdToSketchDTO.getImgWidth() + "====图片高=" + sdToSketchDTO.getImgHeight());
        String imageUrl = null;
        List<LeonardoStyleBO.Element> elements = null;
        // 效验是否是带有指令的功能
        // sdToSketchDTO.getOperate().intValue() == ImgOptModelEnum.CONTROL_OPERATE_EDIT_SKETCH_PAINT.getValue() // TODO sd原稿上色 暂时不用效果不好
        if (sdToSketchDTO.getOperate().intValue() == ImgOptModelEnum.CONTROL_OPERATE_EDIT_STRUCTURE_STYLE.getValue() //风格
                || sdToSketchDTO.getOperate().intValue() == ImgOptModelEnum.OPERATE_EDIT_INPAINT_REPLACE.getValue()) //替换
        {
            if (sdToSketchDTO.getPromptInit() == null) {
                return Result.ERROR("指令不能为空");
            }
            if (BStringUtil.isChinese(sdToSketchDTO.getPromptInit())) {
                sdToSketchDTO.setPromptUse(BTengXunUtil.textToEnglish(sdToSketchDTO.getPromptInit(), "zh", "en"));
            }
            if (sdToSketchDTO.getPromptUse() == null) {
                return Result.ERROR("网络不稳定请重试");
            }
            double whDivide = ImgDrawUtil.getWhDivide(sdToSketchDTO.getImgWidth(), sdToSketchDTO.getImgHeight());
            // 判断宽高比是否在1:2.5和2.5:1之间
            if (!(whDivide > 1.0 / 2.5 && whDivide < 2.5 / 1.0)) {
                return Result.ERROR("图片尺寸过宽或过高, 重新选择");
            }
            if (imageSize > CommonConst.LIMIT_MAXIMUM_IMAGE_SIZE_SKETCH) {
                int[] size = BFileUtil.compressionAspectRatio(sdToSketchDTO.getImgWidth(), sdToSketchDTO.getImgHeight(), 1, 3072, 1);
                imageUrl = sdToSketchDTO.getImgUrl() + "?x-oss-process=image/format,png/resize,w_" + size[0] + ",h_" + size[1];
            } else {
                imageUrl = sdToSketchDTO.getImgUrl();
            }
        } else if (sdToSketchDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_HS_INPAINTING.getValue()
                || sdToSketchDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_OUTPAINT_ZOOM.getValue()
                || sdToSketchDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_FREE_SCALING.getValue()) {
            if (imageSize > CommonConst.LIMIT_MAXIMUM_IMAGE_SIZE_OUTPAINTING) {
                int[] size = BFileUtil.compressionAspectRatio(sdToSketchDTO.getImgWidth(), sdToSketchDTO.getImgHeight(), 1, 3072, 1);
                imageUrl = sdToSketchDTO.getImgUrl() + "?x-oss-process=image/format,png/resize,w_" + size[0] + ",h_" + size[1];
            } else {
                imageUrl = sdToSketchDTO.getImgUrl();
            }
            if ((sdToSketchDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_OUTPAINT_ZOOM.getValue()
                    || sdToSketchDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_FREE_SCALING.getValue()) && sdToSketchDTO.getOutpaintFactor() == null) {
                return Result.ERROR("拓展、缩放必传值为空");
            }
        } else if (sdToSketchDTO.getOperate() == ImgOptModelEnum.LE_OPERATE_EDIT_SKETCH_PAINT.getValue()) {
            if (sdToSketchDTO.getStyleTypeId().intValue() == 0) {
                if (sdToSketchDTO.getPromptInit() != null && BStringUtil.isChinese(sdToSketchDTO.getPromptInit())) {
                    sdToSketchDTO.setPromptUse(BTengXunUtil.textToEnglish(sdToSketchDTO.getPromptInit(), "zh", "en"));
                }
                if (sdToSketchDTO.getPromptUse() == null) {
                    sdToSketchDTO.setPromptUse(sdToSketchDTO.getPromptInit());
                }
            } else if (sdToSketchDTO.getStyleTypeId().intValue() == 1) {
                FunctionStyleConfigPO functionStyleConfigPO = functionStyleConfigMapper.selectOne(new LambdaQueryWrapper<FunctionStyleConfigPO>()
                        .eq(FunctionStyleConfigPO::getUseStyle, sdToSketchDTO.getPromptUse()));
                if (functionStyleConfigPO == null) {
                    return Result.ERROR("未找到风格属性，请重新选择风格");
                }
                String chaHuaPrompt = "";
                if (functionStyleConfigPO.getUseStyle().intValue() == BOriginalColouringEnum.ORIGINAL_COLOURING_INSET.getKey()) {
                    // todo 默认提示词以达到想要的效果
                    chaHuaPrompt = "Color the line draft in a flat illustration style";
                } else if (functionStyleConfigPO.getUseStyle().intValue() == BOriginalColouringEnum.ORIGINAL_COLOURING_FLAT_INSET.getKey()) {
                    elements = new ArrayList<LeonardoStyleBO.Element>();
                    elements.add(new LeonardoStyleBO.Element(BOriginalColouringEnum.ORIGINAL_COLOURING_FLAT_INSET.getAkUUid()));
                } else if (functionStyleConfigPO.getUseStyle().intValue() == BOriginalColouringEnum.ORIGINAL_COLOURING_CRYSTAL.getKey()) {
                    elements = new ArrayList<LeonardoStyleBO.Element>();
                    elements.add(new LeonardoStyleBO.Element(BOriginalColouringEnum.ORIGINAL_COLOURING_CRYSTAL.getAkUUid()));
                }
                if (sdToSketchDTO.getPrompt() != null && !sdToSketchDTO.getPrompt().isEmpty() && BStringUtil.isChinese(sdToSketchDTO.getPrompt())) {
                    sdToSketchDTO.setPromptUse(BTengXunUtil.textToEnglish(sdToSketchDTO.getPrompt(), "zh", "en") + chaHuaPrompt);
                    sdToSketchDTO.setPromptInit(functionStyleConfigPO.getNameShow() + " " + sdToSketchDTO.getPrompt());
                } else {
                    sdToSketchDTO.setPromptInit(functionStyleConfigPO.getNameShow());
                }
                if (sdToSketchDTO.getPromptUse() == null) {
                    sdToSketchDTO.setPromptUse("colour");
                }
                sdToSketchDTO.setStyleValue(functionStyleConfigPO.getStyleValue());
                sdToSketchDTO.setModelId(functionStyleConfigPO.getModelId());
            }
            int[] size = BFileUtil.compressionAspectRatio(sdToSketchDTO.getImgWidth(), sdToSketchDTO.getImgHeight(), 1, 2048, 1);
            imageUrl = sdToSketchDTO.getImgUrl() + "?x-oss-process=image/format,png/resize,w_" + size[0] + ",h_" + size[1];
        }
        if (imageUrl == null) {
            return Result.ERROR("图片尺寸过大,请重新上传");
        }
        List<ImgDrawDetlPO> imgDrawDetlPOList = new ArrayList<>();
        Integer optAttribute = ImgDrawPUtil.getOptAttributeByOptNew(null, null, sdToSketchDTO.getOperate());
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(optAttribute);
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(optAttribute);
        String description = optTitleOne.concat(optTitleTwo != null ? ":" + optTitleTwo : "");

        ImgDrawRecordPO imgDrawRecordPO = intiImgDrawRecordPO(sdToSketchDTO, optAttribute, description, dzQuantity); // 初始化任务记录
        ImgDrawDetlPO imgDrawDetlPO = intiImgDrawDetlPO(imgDrawRecordPO, sdToSketchDTO.getImgUrl()); // 初始化任务记录明细

        try {
            if (sdToSketchDTO.getOperate() == ImgOptModelEnum.LE_OPERATE_EDIT_SKETCH_PAINT.getValue()) {
                Map<String, String> stringMap = LeonardoUtil.getPresignedUrlUploadingImg(dictConfigMap.get(DictConfigEnum.LE_DRAW_KEY.getDictKey()));
                if (stringMap == null) {
                    return Result.ERROR("获取上传参数错误");
                }
                byte[] imageData = BFileUtil.downloadImageGetByte(imageUrl);
                boolean isSuccess = LeonardoUtil.presignedUrlUploadingImg(stringMap.get("url"), stringMap.get("fields"), imageData);
                if (!isSuccess) {
                    return Result.ERROR("上传图片报错");
                }
                // 限制文本的宽高，特殊处理
                final int minSize = 512;
                final int maxSize = 1024;
                final int step = 8;
                int[] size = BFileUtil.compressionAspectRatio(sdToSketchDTO.getImgWidth(), sdToSketchDTO.getImgHeight(), minSize, maxSize, step);

                String generationId = LeonardoUtil.postLeonardoImageColour(
                        new LeonardoStyleBO(size[0], size[1], sdToSketchDTO.getStrength(), sdToSketchDTO.getPromptUse(), stringMap.get("imageId"),
                                sdToSketchDTO.getModelId(), sdToSketchDTO.getStyleValue(), elements), dictConfigMap.get(DictConfigEnum.LE_DRAW_KEY.getDictKey()));
                if (generationId == null) {
                    return Result.ERROR("原稿上色失败");
                }
                imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_TWO.getValue());
                imgDrawRecordPO.setLeJobId(generationId);
                for (int i = 0; i < ImgDrawEnum.IMG_NUMBER_TWO.getValue(); i++) {
                    ImgDrawDetlPO imgDrawDetl = intiImgDrawDetlPO(imgDrawRecordPO, sdToSketchDTO.getImgUrl());
                    imgDrawDetl.setImgIndex(i);
                    imgDrawDetlPOList.add(imgDrawDetl);
                }
            } else {
                byte[] imageData = BFileUtil.downloadImageGetByte(imageUrl);
                if (imageData.length > (5 * 1024 * 1024)) {
                    byte[] bytes = BFileUtil.resizeImage(imageData);
                    imageData = bytes;
                }
                // 异步执行上色任务=>
                imgDrawDetlPOList.add(imgDrawDetlPO);
                asyncService.sdSketchToFineControl(sdToSketchDTO, imgDrawRecordPO, imgDrawDetlPO, imageData);
            }
        } catch (Exception e) {
            log.error("sdSketchToFineControl 异常 {}", e.getMessage(), e);
            return Result.ERROR("目前排队人数较多，稍后重试");
        }
        // 保存绘画记录和历史记录
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) < 1) {
            return Result.ERROR("生成绘画记录表失败");
        }
        if (!imgDrawDetlService.saveBatch(imgDrawDetlPOList)) {
            return Result.ERROR("生成绘画记录详情表失败");
        }
        //扣除点子数量
        checkService.ddDeduct(sdToSketchDTO.getUserId(), dzQuantity, description);
        return Result.SUCCESS(String.valueOf(imgDrawRecordPO.getId()));
    }

    @NotNull
    private static ImgDrawRecordPO intiImgDrawRecordPO(SDToSketchDTO sdToSketchDTO, Integer optAttribute, String description, double dzQuantity) {
        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setId(IdWorker.getId());
        imgDrawRecordPO.setModeAttribute(ImgOptModelEnum.DRAW_ATTRIBUTE_VARIANT.getValue());
        imgDrawRecordPO.setOptAttribute(optAttribute);
        imgDrawRecordPO.setPromptInit(sdToSketchDTO.getPromptInit());
        imgDrawRecordPO.setPromptUse(sdToSketchDTO.getPromptInit());
        imgDrawRecordPO.setDescription(description);
        imgDrawRecordPO.setUseDdQua(dzQuantity);
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setUserId(sdToSketchDTO.getUserId());//装载用户id
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
        imgDrawRecordPO.setFunType(sdToSketchDTO.getFunctionConfigId());
        return imgDrawRecordPO;
    }

    @NotNull
    private static ImgDrawDetlPO intiImgDrawDetlPO(ImgDrawRecordPO imgDrawRecordPO, String imageUrl) {
        ImgDrawDetlPO imgDrawDetlPO = new ImgDrawDetlPO();
        imgDrawDetlPO.setDrawRecordId(imgDrawRecordPO.getId());
        imgDrawDetlPO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawDetlPO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawDetlPO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawDetlPO.setImgIndex(1);//固定1
        imgDrawDetlPO.setImgSourceUrl(imageUrl);
        imgDrawDetlPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
        imgDrawDetlPO.setIsOpen(CommonIntEnum.IS_FALSE.getIntValue());//不公开，换枚举
        return imgDrawDetlPO;
    }

    // 处理上色成功的任务
    private Boolean handleColoringCompleteTask(ImgDrawRecordPO imgDrawRecordPO, List<ImgDrawDetlPO> imgDrawDetlPOList, String imageUrlStr) {
        Double whDivide = null;
        Integer imgWidth = null;
        Integer imgHeight = null;

        List<String> imageUrls = allocationList(imgDrawDetlPOList, imageUrlStr);
        for (int i = 0; i < imgDrawDetlPOList.size(); i++) {
            ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlPOList.get(i);
            OssParamBO ossParamBO = uploadImageToOss(imgDrawRecordPO.getLeJobId() + "_" + i, imageUrls.get(i));
            if (ossParamBO == null) {
                ossParamBO = new OssParamBO();
                ossParamBO.setImageUrl(imageUrls.get(i));
            }
            whDivide = ImgDrawUtil.getWhDivide(Integer.valueOf(ossParamBO.getImageWidth()), Integer.valueOf(ossParamBO.getImageHeight()));
            // 绘画明细赋值
            imgWidth = Integer.valueOf(ossParamBO.getImageWidth());
            imgHeight = Integer.valueOf(ossParamBO.getImageHeight());

            // 绘画明细赋值
            imgDrawDetlPO.setImgUrl(ossParamBO.getImageUrl());
            imgDrawDetlPO.setImgWidth(imgWidth);
            imgDrawDetlPO.setImgHeight(imgHeight);
            imgDrawDetlPO.setWhDivide(whDivide);
            imgDrawDetlPO.setImgType("image/webp");

            // 绘画主表赋值
            imgDrawRecordPO.setWidth(imgWidth);
            imgDrawRecordPO.setHeight(imgHeight);
            imgDrawRecordPO.setWhDivide(whDivide);

            if (imgDrawDetlMapper.update(null, new LambdaUpdateWrapper<ImgDrawDetlPO>()
                    .eq(ImgDrawDetlPO::getId, imgDrawDetlPO.getId())
                    .set(ImgDrawDetlPO::getImgUrl, imgDrawDetlPO.getImgUrl())
                    .set(ImgDrawDetlPO::getImgWidth, imgDrawDetlPO.getImgWidth())
                    .set(ImgDrawDetlPO::getImgHeight, imgDrawDetlPO.getImgHeight())
                    .set(ImgDrawDetlPO::getWhDivide, imgDrawDetlPO.getWhDivide())
                    .set(ImgDrawDetlPO::getImgType, "image/webp")) < 1) {
                return false;
            }

        }

        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());
        imgDrawRecordPO.setFinishTime(System.currentTimeMillis());
        // 跟新绘画记录主表
        if (imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                .set(ImgDrawRecordPO::getStatus, imgDrawRecordPO.getStatus())
                .set(ImgDrawRecordPO::getFinishTime, imgDrawRecordPO.getFinishTime())
                .set(ImgDrawRecordPO::getWidth, imgDrawRecordPO.getWidth())
                .set(ImgDrawRecordPO::getHeight, imgDrawRecordPO.getHeight())
                .set(ImgDrawRecordPO::getWhDivide, imgDrawRecordPO.getWhDivide())) < 1) {
            return false;
        }
        return true;
    }

    private List<String> allocationList(List<ImgDrawDetlPO> imgDrawDetlPOList, String imageUrlStr) {
        List<String> imageUrlList = Arrays.stream(imageUrlStr.split(", ")).collect(Collectors.toList());
        int urlsSize = imageUrlList.size();
        int drawDetlSize = imgDrawDetlPOList.size();
        if (drawDetlSize == urlsSize) {
            return imageUrlList;
        }
        if (drawDetlSize < urlsSize) {
            return new ArrayList<>(imageUrlList.subList(0, drawDetlSize));
        }
        return null;
    }

    // TODO 一键海报
    @Override
    @Transactional(rollbackFor = {Exception.class, E.class})
    public Result<Object> submitPosterJob(BytePosterDTO bytePosterDTO) throws IBusinessException {
        ImgModelConfigPO imgModelConfigPO = imgModelConfigMapper.selectById(bytePosterDTO.getModelId());
        if (imgModelConfigPO == null) {
            log.error("submitJob=模型不存在");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        bytePosterDTO.setUserId(JwtNewUtil.getUserId());
        //解析字符串
        ImgModelConfigVO imgModelConfigVO = new ImgModelConfigVO(imgModelConfigPO);
        Integer checkConcurrentCount = checkService.checkConcurrentCount();
        if (checkConcurrentCount == null) {
            log.error("submitJob=绘图数量已达到上限");
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }

        int isVip = checkConcurrentCount; //设置规则为vip用户真实规则

        if (imgModelConfigPO.getAttribute() == ImgOptModelEnum.DRAW_ATTRIBUTE_JIMENG.getValue()) {
            bytePosterDTO.setInitImgUrls(null);
            return jobJiMeng(isVip, bytePosterDTO, imgModelConfigVO);
        }
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }

    private Result<Object> jobJiMeng(int isVip, BytePosterDTO bytePosterDTO, ImgModelConfigVO imgModelConfigVO) throws IBusinessException {
        bytePosterDTO.setPromptUse(bytePosterDTO.getPrompt());

        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setId(IdWorker.getId());
        imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.DALLE_OPT_ATTRIBUTE_DRAW.getValue());
        imgDrawRecordPO.setModeAttribute(imgModelConfigVO.getAttribute());
        imgDrawRecordPO.setUserId(bytePosterDTO.getUserId());
        imgDrawRecordPO.setPromptInit("“" + bytePosterDTO.getMainTitle() + "”" + bytePosterDTO.getPrompt());
        imgDrawRecordPO.setPromptUse(bytePosterDTO.getPrompt());
        imgDrawRecordPO.setDescription("Jimeng draw " + bytePosterDTO.getPrompt());
        imgDrawRecordPO.setCreateTime(DateUtil.date());
        imgDrawRecordPO.setOperateTime(DateUtil.date());
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());//装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());//装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());//装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());//装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_TWO.getValue());
        imgDrawRecordPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue());
        // 装载宽高尺寸
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());

        //初始化尺寸
        String ar = "1:1";
        double whDecide = 1;

        String imgScaleStr = "[{\"key\":1,\"width\":\"9\",\"height\":\"16\",\"isTrue\":true,\"isFixed\":false},{\"key\":2,\"width\":\"16\",\"height\":\"9\",\"isTrue\":true,\"isFixed\":false},{\"key\":3,\"width\":\"3\",\"height\":\"4\",\"isTrue\":true,\"isFixed\":false},{\"key\":4,\"width\":\"4\",\"height\":\"3\",\"isTrue\":true,\"isFixed\":false},{\"key\":5,\"width\":\"1\",\"height\":\"1\",\"isTrue\":true,\"isFixed\":false}]";
        ObjectMapper objectMapper = new ObjectMapper();
        List<ImgScaleDTO> imgScales = null;
        try {
            imgScales = objectMapper.readValue(imgScaleStr, new TypeReference<List<ImgScaleDTO>>() {
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (ImgScaleDTO imgScale : imgScales) {
            if (Objects.equals(imgScale.getKey(), bytePosterDTO.getImgScaleKey())) {
                ar = bytePosterDTO.getImgScaleIsTrue() ? imgScale.getWidth() + ":" + imgScale.getHeight() : imgScale.getHeight() + ":" + imgScale.getWidth();
                whDecide = bytePosterDTO.getImgScaleIsTrue() ?
                        (new BigDecimal(String.valueOf(imgScale.getWidth())).divide(new BigDecimal(String.valueOf(imgScale.getHeight())), 6, RoundingMode.HALF_UP).doubleValue()) :
                        (new BigDecimal(String.valueOf(imgScale.getHeight())).divide(new BigDecimal(String.valueOf(imgScale.getWidth())), 6, RoundingMode.HALF_UP).doubleValue());
            }
        }
        int[] ratio = BLEDrowSizeEnum.getJiMengWidthAndHeight(ar);

        imgDrawRecordPO.setWhDivide(whDecide);
        imgDrawRecordPO.setPromptUse(imgDrawRecordPO.getPromptUse().concat(" JIMENG 2.1 --ar ").concat(ar));

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO);
        imgDrawHistoryVO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setInitImgUrls(bytePosterDTO.getInitImgUrls());

        // TODO 扣除用户点子 ===== 查询点子规则数据
        boolean isMember = isVip > 0;
        double jimengDrawDeductQua = BDDUseNumEnum.getDDUseByIsVip(BDDUseNumEnum.BYTEAPI_OPT_ONECLICK_POSTER, isMember);
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(CommonEnum.COMM_ONE.getValue()).remark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "")).build();
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), jimengDrawDeductQua, flowRecordSub);
        imgDrawRecordPO.setUseDdQua(jimengDrawDeductQua);
        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(bytePosterDTO));

        try {
            PosterReqBO posterReqBO = new PosterReqBO();
            posterReqBO.setReqkey("high_aes_general_v21_L");
            posterReqBO.setPrompt(bytePosterDTO.getPromptUse() + " “" + bytePosterDTO.getMainTitle() + "” 超高清");
            posterReqBO.setModelVersion("general_v2.1_L");
            posterReqBO.setReqScheduleConf("general_v20_9B_pe");
            posterReqBO.setScale(5F);
            posterReqBO.setDdimSteps(50);
            posterReqBO.setWidth(ratio[0]);
            posterReqBO.setHeight(ratio[1]);
            posterReqBO.setUsePreLlm(bytePosterDTO.getPromptUse().length() > 100);
            posterReqBO.setUseSr(true);
            posterReqBO.setReturnRrl(true);

            PosterReqBO.LogoInfo logoInfo = new PosterReqBO.LogoInfo();
            logoInfo.setAddLogo(false);
            posterReqBO.setLogoInfo(logoInfo);
            String taskId = ByteApisUtil.postJimengTextToImageGenerations(posterReqBO);
            StringBuilder taskIds = new StringBuilder();
            if (taskId == null) {
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            String taskIdTow = ByteApisUtil.postJimengTextToImageGenerations(posterReqBO);
            if (taskId.contains(String.valueOf(ByteStatusEnum.BLOCKLIST_INTERCEPTED.getStatus()))) {
                return Result.ERROR("提示词未检测通过，修改提示词重试");
            }
            taskIds.append(taskId);
            if (taskIdTow != null && !taskIdTow.contains(String.valueOf(ByteStatusEnum.BLOCKLIST_INTERCEPTED.getStatus()))) {
                taskIds.append(",");
                taskIds.append(taskIdTow);
            }
            imgDrawRecordPO.setLeJobId(taskIds.toString());
            if (imgDrawRecordMapper.insert(imgDrawRecordPO) < 0) {
                throw new IBusinessException("即梦海报绘画记录保存失败");
            }
            return Result.SUCCESS(imgDrawHistoryVO);
        } catch (
                Exception e) {
            log.error("Jimeng绘图失败= {}", e.getMessage());
            dzRollbackService.rollback(imgDrawRecordPO.getUserId(), jimengDrawDeductQua, optTitleOne.concat(optTitleTwo != null ? optTitleTwo : ""));
            imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                    .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                    .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue()));
        }
        log.error("jobJimeng=JIMENG绘图失败。");
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }


}

