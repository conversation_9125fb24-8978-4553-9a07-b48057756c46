package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.business.db.mapper.ImgFaceTemplateMapper;
import com.business.db.model.po.ImgFaceTemplatePO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;
import com.nacos.service.IImgFaceTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ImgFaceTemplateServiceImpl extends ServiceImpl<ImgFaceTemplateMapper, ImgFaceTemplatePO> implements IImgFaceTemplateService {


    @Override
    public Result<?> delete(BaseDeleteEntity params) {
        return Result.SUCCESS(this.removeByIds(params.getIds()));
    }

    @Override
    public Result<?> selectAiTemplList(Long userId) {
        LambdaQueryWrapper<ImgFaceTemplatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImgFaceTemplatePO::getUserId, userId);
        queryWrapper.eq(ImgFaceTemplatePO::getDeleted, 0);
        return Result.SUCCESS(this.baseMapper.selectList(queryWrapper));
    }

}
