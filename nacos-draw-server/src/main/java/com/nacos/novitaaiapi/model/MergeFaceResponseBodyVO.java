package com.nacos.novitaaiapi.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "合并人脸返回对象")
public class MergeFaceResponseBodyVO {

    @Schema(description = "合并后的人脸图片", type = "String")
    private String image_file;

    @Schema(description = "合并后的人脸图片类型", type = "String")
    private String image_type;

    @Schema(description = "bate64展示图片", type = "String")
    private String image_Bate64;

    public String getImage_Bate64() {
        return "data:image/"+image_type+";base64,"+image_file;
    }
}
