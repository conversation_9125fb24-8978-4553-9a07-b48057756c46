package com.nacos.constant;


public class CommonConst {

    /**
     * 线程池CorePoolSize
     */
    public static final Integer NOTIFY_POOL_SIZE = 200;

    /**
     * 图片类型
     */
    public static final String IMAGE_SUFFIX = "image/webp";
    /**
     * =============================================================================================
     * go ---api
     */
    public static final String GO_API_KEY = "X-API-Key";

    /**
     * goApi换脸
     */
    public static final String FACE_SWAP_API_URL = "/api/face_swap/v1/async";

    /**
     * goApi换脸异步回调
     */
    public static final String FACE_SWAP_FETCH_API_URL = "/api/face_swap/v1/fetch";

    /**
     * goApi放大2倍
     */
    public static final String IMAGE_TOOLKIT_API_URL = "/api/image_toolkit/v2/create";

    /**
     * goApi放大2倍异步回调
     */
    public static final String IMAGE_TOOLKIT_FETCH_API_URL = "/api/image_toolkit/v2/fetch";

    /**
     * dall-e-3 绘图
     */
    public static final String GO_API_DALLE = "/v1/images/generations";


    /**
     * ===========================Mj 任务=================================
     */
    /**
     * mJ任务超时时间(分钟)
     */
    public static final int TASK_TIMEOUT_MINUTES = 40;
    /**
     * dalle任务超时时间(分钟)
     */
    public static final int TASK_TIMEOUT_MINUTES_DALLE = 30;
    /*
     * 音频任务超时时间(分钟)
     */
    public static final int TASK_TIMEOUT_MINUTES_AUDIO = 12;
    /**
     * 高清放大图片限制最大数
     */
    public static final long LIMIT_MAXIMUM_IMAGE_SIZE_ZOOM_2X = 4194304;
    public static final long LIMIT_MAXIMUM_IMAGE_SIZE_ZOOM_4X = 16777216;
    public static final long LIMIT_MAXIMUM_IMAGE_SIZE_ZOOM_8X = 36348841;
    /**
     * 高清重绘图片限制最大数
     */
    public static final long LIMIT_MAXIMUM_IMAGE_SIZE_REDRAW = 4194304;

    // 原稿上色-限制上传图片大小
    public static final long LIMIT_MAXIMUM_IMAGE_SIZE_SKETCH = 9437184;

    // 自由拓展-智能抹除-限制图片大小
    public static final long LIMIT_MAXIMUM_IMAGE_SIZE_OUTPAINTING = 16777216;

    // LE 上色下你只宽高为1536
    public static final int LIMIT_MAXIMUM_IMAGE_WIDTHANDHEIGHT = 1536;

}
