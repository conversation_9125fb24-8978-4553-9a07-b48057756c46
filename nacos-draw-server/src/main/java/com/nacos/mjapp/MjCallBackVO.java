package com.nacos.mjapp;

import lombok.Data;


@Data
public class MjCallBackVO {

    private Long id;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 任务类型
     * 1生成图片 2选中放大 3放大图片向上拓展 4放大图片向下拓展 5放大图片向左拓展 6放大图片向右拓展 7放大图片方形拓展 8放大图片高变化 9放大图片低变化 10选中其中的一张图
     * 11重新生成 12图转prompt 13图转prompt重新生成 14多图混合 15放大图片拓展 16抠图
     */
    private Integer action;


    /**
     * 提示词 关键词、指令、魔法--用户传递关键词
     */
    private String promptInit;


    /**
     * 任务状态 1未启动 2已提交 3执行中 4失败 5成功
     */
    private Integer status;

    /**
     * 任务进度
     */
    private String progress;

    /**
     * 图片url-----主图片地址
     */
    private String mainImgUrl;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 是否发布（0否 1是）
     */
    private Integer  isPublish;
}
