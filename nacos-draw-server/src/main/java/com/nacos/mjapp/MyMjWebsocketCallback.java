package com.nacos.mjapp;//package com.nacos.model.mjapp;
//
//import com.alibaba.fastjson2.JSONObject;
//import com.nacos.enums.WebSocketEnum;
//import com.nacos.mjapi.MJAPIUtil;
//import com.nacos.mjapi.model.JobStatusBO;
//import com.nacos.service.RedisServer;
//import lombok.extern.slf4j.Slf4j;
//import okhttp3.Call;
//import okhttp3.Callback;
//import okhttp3.Response;
//import org.jetbrains.annotations.NotNull;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.List;
//
//@Slf4j
////自定义回调
//public class MyMjWebsocketCallback implements Callback {
//    private final String jobId;//工作id
//    private final RedisServer redisServer;//redis缓存信息
//
//    // 构造函数，接收初始化参数
//    public MyMjWebsocketCallback(String jobId, RedisServer redisServer) {
//        this.jobId = jobId;
//        this.redisServer = redisServer;
//    }
//    @Override
//    public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
//        log.info("MyMjWebsocketCallback-onResponse: {}", response);
//        //校验为成功状态
//        if (response.isSuccessful() && response.body() != null && jobId != null && redisServer != null){
//            // 处理响应数据
//            while (response.body().contentLength() > 0) {
//                String responseData = response.body().string();
//                log.info("MyMjWebsocketCallback-onResponse: {}", responseData);
//                JSONObject data = JSONObject.parseObject(responseData, JSONObject.class);
//                //校验不为空：
//                if (data.containsKey("current_status") && data.containsKey("img_type") && data.containsKey("imgs") && !data.getJSONArray("imgs").isEmpty() && data.containsKey("percentage_complete")) {
//                    JobStatusBO jobStatusBO = new JobStatusBO();
//                    List<String> imagePaths = new ArrayList<>();
//                    String imgType = data.getString("img_type");
//                    jobStatusBO.setJobRunningSchedule(data.getInteger("percentage_complete") + "%");
//                    jobStatusBO.setCurrentStatus(data.getString("current_status"));
//                    for (int i = 0; i < data.getJSONArray("imgs").size(); i++) {
//                        JSONObject img = data.getJSONArray("imgs").getJSONObject(i);
//                        imagePaths.add("data:image/" + imgType.replaceAll("\\.", "") + ";base64," + img.getString("data"));
//                    }
//                    jobStatusBO.setJobStatusRunningImgs(imagePaths);
//                    JSONObject jsonObject = new JSONObject();
//                    jsonObject.put("type", WebSocketEnum.DRAW_JOB_PUSH.getPushType());
//                    jsonObject.put("userId", jobStatusBO.getJobId());
//                    jsonObject.put("object", JSONObject.toJSONString(jobStatusBO));
//                    String state = redisServer.sendMsg(jsonObject.toString());
//                    log.info("任务推送消息状态: {}", state);
//                }
//                //校验是否完成，完成则关闭推送
//                if ("completed".equals(data.getString("current_status"))) {
//                    break;
//                }
//            }
//        }
//        //关闭
//        MJAPIUtil.getWebForithmClientMapShutdown(jobId);
//    }
//
//    @Override
//    public void onFailure(@NotNull Call call, @NotNull IOException e) {
//        log.error("onFailure: " + e);
//        //关闭
//        MJAPIUtil.getWebForithmClientMapShutdown(jobId);
//    }
//
//}
//
