package com.nacos.mjapp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.nacos.base.BaseEntity;
import com.nacos.enums.MjTaskStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * mj 画图主任务表 BO  --- 四合一图片
 * <AUTHOR>
 * @version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MjTaskBO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 326308725675949330L;

    /**
     * goApi 任务id
     */
    private String taskId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 任务类型
     * 1生成图片 2选中放大 3放大图片向上拓展 4放大图片向下拓展 5放大图片向左拓展 6放大图片向右拓展 7放大图片方形拓展 8放大图片高变化 9放大图片低变化 10选中其中的一张图
     * 11重新生成 12图转prompt 13图转prompt重新生成 14多图混合 15放大图片拓展 16抠图
     */
    private Integer action;

    /**
     * 上一次的action
     */
    private Integer superAction;

    /**
     * 提示词 临时字段
     */
    private String prompt;

    /**
     * 提示词 关键词、指令、魔法--用户传递关键词
     */
    private String promptInit;

    /**
     * 提示词- 关键词、指令、魔法--真实传递到mj使用
     */
    private String promptUse;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 变换位置---图片位置
     */
    private Integer imgIndex;

    /**
     * 变换宽度
     */
    private Integer imgWidth;

    /**
     * 变换高度
     */
    private Integer imgHeight;

    /**
     * 图片显示类型：1mj图片  2是cos图片
     */
    private Integer showType;

    /**
     * 任务状态 1未启动 2已提交 3执行中 4失败 5成功
     */
    private Integer status = MjTaskStatusEnum.NOT_START.getStatus();

    /**
     * 图片url-----主图片地址
     */
    private String mainImgUrl;

    /**
     * mj任务推送图片地址
     */
    private String mjImgUrl;

    /**
     * 开始执行时间
     */
    private Long startTime;

    /**
     * 提交时间
     */
    private Long submitTime;

    /**
     * 结束时间--任务完成时间
     */
    private Long finishTime;

    /**
     * 失败原因
     */
    private String failReason;


    /**
     * mj 任务信息
     */
    private String finalPrompt;

    /**
     * 回调地址
     */
    private String notifyHook;

    /**
     * 关联任务 id
     */
    private Long relatedTaskId;

    /**
     * 消息 id
     */
    private String messageId;

    /**
     * 消息 hash
     */
    private String messageHash;

    /**
     * 父级 消息 hash
     */
    private String superMessageHash;

    /**
     * mj 种子 id
     */
    private String seed;

    /**
     * 进度消息ID
     */
    private String progressMessageId;

    /**
     * 是否执行过局部修改
     */
    private Integer isInpaint;
    /**
     * 是否执行过自定义拓展
     */
    private Integer isCustzoom;

    /**
     * 是否发布（0否 1是）
     */
    private Integer  isPublish;


    /**----------------------------------------------暂定字段------------------------------
     * 风格
     */
    private String style;

    /**
     * 使用点子数量
     */
    private Double payNum;

    /**
     * 任务进度
     */
    private String progress;

    /**
     * 消息唯一标识
     */
    private String nonce;

    private Integer flags;

    /**
     * 使用点子方式
     */
    private Long way;

    /**
     * 扣点子规则-----退钱使用
     */
    private String pointRule;

    /**
     * 推送类型
     */
    private Integer pushType;

    @JsonIgnore
    private final transient Object lock = new Object();


    public void success() {
        this.finishTime = System.currentTimeMillis();
        this.status = MjTaskStatusEnum.SUCCESS.getStatus();
        this.progress = "100%";
    }



}
