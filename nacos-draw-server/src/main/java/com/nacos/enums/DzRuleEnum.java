package com.nacos.enums;

import lombok.Getter;

/**
 * 扣除点子数类型
 */
@Getter
public enum DzRuleEnum {

    /**
     * MJ快速绘画需要扣除的点子数量
     */
    MJ_HIGH_SPEED_DEDUCTQUA("MJ_01"),
    /**
     * MJ绘画一次扣除点子数量
     */
    MJ_SUBTRACT_QUANTITY("MJ_02"),
    /**
     * MJ拓展扣除点子数量
     */
    MJ_EXPAND_DEDUCTION("MJ_03"),
    /**
     * MJ抠图扣除点子数
     */
    MJ_MATTING_DEDUCTION("MJ_04"),
    /**
     * MJ融合扣除点子数
     */
    MJ_FUSION_DEDUCTION("MJ_05"),
    /**
     * MJ极速绘图扣除点子数
     */
    MJ_TOP_SPEED_DEDUCTQUA("MJ_06"),
    /**
     * MJ放大2倍扣除点子数
     */
    MJ_PICTURE_2X_DEDUCTQUA("MJ_07"),

    /**
     * MJ放大4倍扣除点子数
     */
    MJ_PICTURE_4X_DEDUCTQUA("MJ_08"),
    /**
     * MJ放大4倍扣除点子数
     */
    MJ_TUSENGWENJ_DEDUCTQUA("MJ_09"),
    /**
     * AI换脸扣点子数
     */
    AI_FACE_DEDUCTQUA("AI_01"),
    /**
     * AI写真扣点子数
     */
    AI_PHOTO_DEDUCTQUA("AI_02"),
    /**
     * DALL-E绘图扣点子数
     */
    DALLE_THREE_DEDUCTQUA("DALL_E_3"),
    /**
     * gpt文案生成扣除点子数
     */
    GPT_SCENE_WRITER_DEDUCTIONS("GPT_01"),
    /**
     * 文案翻译扣除点子数
     */
    SCENE_TRANSLATION_DEDUCTIONS("STD_01");

    String ruleType;
    DzRuleEnum(String ruleType) {
        this.ruleType = ruleType;
    }
}
