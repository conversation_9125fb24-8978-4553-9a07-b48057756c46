package com.nacos.enums.goapi;

import lombok.Getter;

@Getter
public enum CurrentStatusEnum {

    /**
     * 等待执行
     */
    PENDING("pending"),
    /**
     * 已缓存尚未在队列中
     */
    STAGED("staged"),
    /**
     * 开始执行
     */
    STARTING("starting"),
    /**
     * 正在执行
     */
    PROCESSING("processing"),
    /**
     * 已经完成
     */
    FINISHED("finished"),
    SUCCESS("success"),
    /**
     * 执行失败
     */
    FAILED("failed"),
    /**
     * 等待重试
     */
    RETRY("retry");

    String status;

    CurrentStatusEnum(String status) {
        this.status = status;
    }

}
