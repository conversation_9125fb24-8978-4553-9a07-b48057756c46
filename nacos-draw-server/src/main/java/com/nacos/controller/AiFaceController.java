package com.nacos.controller;


import com.nacos.baiduaiapi.BaiduAiApis;
import com.nacos.baiduaiapi.BaiduImageUtil;
import com.nacos.baiduaiapi.model.FaceDetectionRequestBody;
import com.nacos.baiduaiapi.model.FaceDetectionResponseBodyVO;
import com.nacos.novitaaiapi.ImageUrlUtil;
import com.nacos.novitaaiapi.NovitaAiApis;
import com.nacos.novitaaiapi.model.MergeFaceRequestBody;
import com.nacos.novitaaiapi.model.MergeFaceResponseBodyVO;
import com.nacos.novitaaiapi.model.dto.MergeFaceRequestBodyDTO;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

@Log4j2
@RestController
@RequiredArgsConstructor
@RequestMapping("/aiface")
public class AiFaceController {

    @PostMapping("/detectionFacial")
    @Operation(summary = "AI人脸检测", description = "AI人脸检测，检测是否存在人脸，人脸数量等信息")
    public Result<FaceDetectionResponseBodyVO> detectionFacial(@RequestBody HashMap<String, String> map) {
        FaceDetectionRequestBody faceDetectionRequestBody = new FaceDetectionRequestBody();
        faceDetectionRequestBody.setImage(BaiduImageUtil.urlToBase64Max1024(map.get("url")));
        faceDetectionRequestBody.setImage_type("BASE64");
        faceDetectionRequestBody.setMax_face_num(10);
        FaceDetectionResponseBodyVO faceDetectionResponseBodyVO = BaiduAiApis.faceDetection(faceDetectionRequestBody);
        log.info("faceDetectionResponseBodyVO:{}", faceDetectionResponseBodyVO);
        return Result.SUCCESS(faceDetectionResponseBodyVO);
    }
    @PostMapping("/fusionFacial")
    @Operation(summary = "AI人脸融合", description = "AI人脸融合")
    public Result<MergeFaceResponseBodyVO> fusionFacial(@RequestBody MergeFaceRequestBodyDTO mergeFaceRequestBodyDTO) {
        MergeFaceRequestBody mergeFaceRequestBody = new MergeFaceRequestBody();
        mergeFaceRequestBody.setFace_image_file(ImageUrlUtil.urlToBase64Max1024(mergeFaceRequestBodyDTO.getFaceImageUrl()));
        mergeFaceRequestBody.setImage_file(ImageUrlUtil.urlToBase64Max1024(mergeFaceRequestBodyDTO.getImageFileUrl()));
        MergeFaceRequestBody.Extra extra = new MergeFaceRequestBody.Extra();
        //返回webp格式图片
        extra.setResponse_image_type("webp");
        mergeFaceRequestBody.setExtra(extra);
        MergeFaceResponseBodyVO mergeFaceResponseBodyVO = NovitaAiApis.postMergeFaceRequest(mergeFaceRequestBody);
        log.info("MergeFaceResponseBody:{}", mergeFaceResponseBodyVO);
        return Result.SUCCESS(mergeFaceResponseBodyVO);
    }

}
