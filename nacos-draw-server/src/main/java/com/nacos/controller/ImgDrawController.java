package com.nacos.controller;

import com.business.db.model.dto.ImgDrawOptDTO;
import com.business.tengxunyun.BTengXunUtil;
import com.nacos.auth.JwtNewUtil;
import com.nacos.ddimg.model.ImgDrawDTO;
import com.nacos.enums.CommonResultEnum;
import com.nacos.exception.IBusinessException;
import com.nacos.mjapi.MJCommonEnum;
import com.nacos.result.Result;
import com.nacos.service.DrawService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


//@Tag(name = "绘图接口", description = "绘图接口：通用接口")
//@RestController
//@RequestMapping("/imgDraw")
@Log4j2
public class ImgDrawController {

    @Resource
    private DrawService drawService;

    @Operation(summary = "提交绘图接口")
    @PostMapping(value = "/submitJob",name = "提交绘图接口")
    public Result<Object> submitJob(@RequestBody ImgDrawDTO imgDrawDTO) throws IBusinessException {
        Long userId = JwtNewUtil.getUserId();
        log.info("提交绘图接口:打印用户id,{}", userId);
        log.info("提交绘图接口:imgDrawDTO,{}", imgDrawDTO);
        if(BTengXunUtil.textToExamineFailByDraw(imgDrawDTO.getPrompt(), userId)){
            return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
        }
        if (Objects.equals(imgDrawDTO.getMjSpeedKey(), MJCommonEnum.MJ_SPEED_KEY_1.getIntValue())){
            return Result.ERROR(CommonResultEnum.DRAW_MJ_API_RELAXED_ERROR.getValue());
        }

        return drawService.submitJob(imgDrawDTO);
    }

    @Operation(summary = "图片编辑接口")
    @PostMapping(value = "/submitOpt",name = "图片编辑接口")
    public Result<Object> submitOpt(@RequestBody ImgDrawOptDTO imgDrawOptDTO) throws IBusinessException{
        Long userId = JwtNewUtil.getUserId();
        log.info("图片编辑接口:打印用户id,{}", userId);
        log.info("图片编辑接口:imgDrawOptDTO,{}", imgDrawOptDTO);
        if(BTengXunUtil.textToExamineFailByDraw(imgDrawOptDTO.getVaryRegionPrompt(), userId)){
            return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
        }
        return drawService.submitOpt(imgDrawOptDTO);
    }

    @Operation(summary = "撤销任务")
    @GetMapping(value = "/cancelJob/{imgDrawId}",name = "撤销任务")
    public Result<Object> cancelJob(@PathVariable Long imgDrawId) throws IBusinessException{
        return drawService.cancelJob(imgDrawId);
    }

    @Operation(summary = "获取纵横比尺寸")
    @GetMapping(value = "/aspectRatioScale",name = "获取纵横比尺寸接口")
    public Result<Object> getAspectRatioScale() {
        return drawService.getAspectRatioScale();
    }

    @Operation(summary = "图片保存接口")
    @PostMapping(value = "/imgSave/{imgDrawId}",name = "图片保存接口")
    public Result<Object> imgSave(@PathVariable Long imgDrawId) throws IBusinessException{
        return drawService.imgSave(imgDrawId);
    }

    @Operation(summary = "绘图任务删除接口")
    @GetMapping(value = "/imgDrawRecordDelete/{imgDrawId}",name = "绘图任务删除接口")
    public Result<Object> imgDrawRecordDelete(@PathVariable Long imgDrawId) {
        return drawService.imgDrawRecordDelete(imgDrawId);
    }

    @Operation(summary = "绘图详情删除接口")
    @GetMapping(value = "/imgDrawDetlDelete/{imgDrawDetlId}", name = "绘图详情删除接口")
    public Result<Object> imgDrawDetlDelete(@PathVariable Long imgDrawDetlId) {
        return drawService.imgDrawDetlDelete(imgDrawDetlId);
    }

//    @Operation(summary = "绘图详情删除接口")
//    @GetMapping(value = "/webhook", name = "绘图详情删除接口")
//    public Result<Object> imgDrawDetlDelete() {
//        return drawService.imgDrawDetlDelete(imgDrawDetlId);
//    }

}
