package com.nacos.controller;

import com.business.db.model.dto.goapi.GoApiPhotoDTO;
import com.business.db.model.dto.goapi.GoApiUpscaleDTO;
import com.business.db.model.dto.kong.BytePosterDTO;
import com.business.db.model.dto.sd.SDToSketchDTO;
import com.business.enums.BFunctionConfigEnum;
import com.business.tengxunyun.BTengXunUtil;
import com.nacos.auth.JwtNewUtil;
import com.nacos.enums.CommonResultEnum;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;
import com.nacos.service.IKongAreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "金刚区", description = "首页功能区")
@Log4j2
@RestController
@RequestMapping("/kong")
public class KongAreaController {

    @Resource
    private IKongAreaService kongAreaService;

    @Operation(summary = "个人写真接口GoApi")
    @PostMapping(value = "/personalPhoto", name = "AI换脸（个人写真）")
    public Result<Object> goApiPersonalPhoto(@Validated @RequestBody GoApiPhotoDTO goApiPhotoDTO) throws IBusinessException {
        return kongAreaService.personalPhotoFaceSwap(goApiPhotoDTO);
    }

    @Operation(summary = "个人写真接口新版")
    @PostMapping(value = "/personalPhoto/faceSwap", name = "AI换脸（个人写真）")
    public Result<Object> personalPhoto(@Validated @RequestBody GoApiPhotoDTO goApiPhotoDTO) throws IBusinessException {
        goApiPhotoDTO.setFunctionConfigId(BFunctionConfigEnum.FUNCTION_CONFIG_PHOTO.getIntValue());
        return kongAreaService.personalPhotoFaceSwap(goApiPhotoDTO);
    }

    @Operation(summary = "高清重绘")
    @PostMapping(value = "/upscaleImgEditing", name = "goApi放大2x、4x及le重绘")
    public Result<Object> upscaleImgEditing(@Validated @RequestBody GoApiUpscaleDTO goApiUpscaleDTO) throws Exception {
        return kongAreaService.upscaleImgEditing(goApiUpscaleDTO);
    }

    @Operation(summary = "图像高档控制(上色、风格迁移、替换、抹除、拓展)")
    @PostMapping(value = "/imgUpscaleOpt", name = "图像高档控制")
    public Result<Object> imgUpscaleOptControl(@Validated @RequestBody SDToSketchDTO sdToSketchDTO) throws IBusinessException {
        return kongAreaService.imgUpscaleOptControl(sdToSketchDTO);
    }

    @Operation(summary = "提交一键海报任务")
    @PostMapping(value = "/submitPosterJob", name = "提交一键海报任务")
    public Result<Object> submitPosterJob(@Validated @RequestBody BytePosterDTO bytePosterDTO) throws IBusinessException {
        Long userId = JwtNewUtil.getUserId();
        log.info("提交一键海报接口:打印用户id,{}", userId);
        log.info("提交一键海报接口:bytePosterDTO,{}", bytePosterDTO);
        if (bytePosterDTO == null) {
            return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
        }
        if (BTengXunUtil.textToExamineFailByDraw(bytePosterDTO.getPrompt(), userId)) {
            return Result.ERROR(CommonResultEnum.PROMPT_WORD_ERROR.getValue());
        }
        return kongAreaService.submitPosterJob(bytePosterDTO);
    }


}
