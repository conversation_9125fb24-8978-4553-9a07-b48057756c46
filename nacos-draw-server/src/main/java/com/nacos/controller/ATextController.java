package com.nacos.controller;

import com.alibaba.fastjson2.JSONArray;
import com.business.db.mapper.AdminMjAccountConfigMapper;
import com.business.db.mapper.ImgDrawRecordMapper;
import com.business.gpt.ChatGPTWebUtil;
import com.business.gpt.GptToTextUtil;
import com.business.mj.MJAccountUtil;
import com.business.mj.MJWebUtil;
import com.business.mj.MjWebAccountUtil;
import com.business.model.bo.MJRedisRefreshBO;
import com.business.model.po.AdminMjAccountConfigPO;
import com.business.model.po.ImgDrawRecordPO;
import com.nacos.ddimg.model.MJAccountBO;
import com.nacos.enums.CommonResultEnum;
import com.nacos.enums.GlobalRedisKeyEnum;
import com.nacos.mjapi.MJApis;
import com.nacos.mjapi.model.JobInfo;
import com.nacos.mjapi.model.JobNewInfoStatusDTO;
import com.nacos.mjapi.model.MJAccountHeaderBO;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.tool.BrotliInterceptor;
import com.nacos.utils.BFeiShuUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;


@Log4j2
@RestController
@RequestMapping("/text")
public class ATextController {

    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;//绘图记录
    @Resource
    private AdminMjAccountConfigMapper adminMjAccountConfigMapper;

    @Operation(summary = "手动通知飞书--测试")
    @PostMapping(value = "/sendWebhookFeishu",name = "sendWebhookFeishu")
    public Result<Object> sendWebhookFeishu(@RequestBody HashMap<String, Object> jsonObject) {
//        BFeiShuUtil.sendWebhookTaskInfo((String) jsonObject.get("session"));
        BFeiShuUtil.sedCardErrorFromDraw(BFeiShuUtil.P4,"点点 测试告警","点点报错了",true,"10分钟");
        return Result.SUCCESS();
    }

    @Operation(summary = "手动测试拓展")
    @PostMapping(value = "/pan",name = "pan")
    public Result<Object> pan(@RequestBody HashMap<String, Object> jsonObject) {
        return Result.SUCCESS();
    }

    @Operation(summary = "手动测试取消")
    @GetMapping(value = "/getMJJobsCancel",name = "getMJJobsCancel")
    public Result<Object> getMJJobsCancel(Long imgDrawId) {
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectById(imgDrawId);
        if (imgDrawRecordPO == null) {
            return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
        }
        AdminMjAccountConfigPO adminMjAccountConfigPO = adminMjAccountConfigMapper.selectById(imgDrawRecordPO.getMjAccountId());
        if (adminMjAccountConfigPO == null) {
            return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
        }
        //TODO mj任务取消逻辑处理
        MJAccountHeaderBO mjAccountHeaderBO = new MJAccountHeaderBO();
        mjAccountHeaderBO.setToken(adminMjAccountConfigPO.getAppToken());
        mjAccountHeaderBO.setAppVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_APP_VERSION.getStrKey()));
        mjAccountHeaderBO.setUserAgentVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_USER_AGENT.getStrKey()));
//        mjAccountHeaderBO.setCookie(RedisUtil.getValue(???));
        return Result.SUCCESS(MJApis.getMJJobsCancel(MJApis.getTextHeader(), imgDrawRecordPO.getMjJobId()));
    }

    @Operation(summary = "手动测试获取每日信息")
    @PostMapping(value = "/getMJJobsInfo",name = "getMJJobsInfo")
    public Result<Object> getMJJobsInfo() {
        return Result.SUCCESS(MJApis.getMJJobsInfo(MJApis.getTextHeader(),new JobNewInfoStatusDTO(1,"niji_top_day_baseline")));
//        return Result.SUCCESS(MJApis.getMJJobsInfo(MJApis.getTextHeader(),new JobNewInfoStatusDTO(1,"top_day")));
    }

    @Operation(summary = "手动绘画测试")
    @PostMapping(value = "/getMJSubmitJob",name = "getMJSubmitJob")
    public Result<Object> getMJSubmitJob(@RequestBody HashMap<String, Object> jsonObject) {
        MJAccountHeaderBO mjAccountHeaderBO = MJApis.getTextHeader();
        mjAccountHeaderBO.setJsonParameter(MJApis.submitJobDrawParam("relaxed","1:1",250, "\"version\":\"5.2\"","小马",null));
        return Result.SUCCESS(MJApis.submitJobDraw(mjAccountHeaderBO));
    }

    @Operation(summary = "手动绘画测试")
    @PostMapping(value = "/remix",name = "remix")
    public Result<Object> remix(@RequestBody HashMap<String, Object> jsonObject) {
        MJAccountHeaderBO mjAccountHeaderBO = MJApis.getTextHeader();
        mjAccountHeaderBO.setJsonParameter(MJApis.submitJobDrawParam("relaxed","1:1",250, "\"version\":\"5.2\"","小马",null));
        JobInfo jobInfo = MJApis.submitJobRemix(mjAccountHeaderBO.getJsonParameter(), true, (String) jsonObject.get("prompt"), (String) jsonObject.get("parentJobId"),1, mjAccountHeaderBO);
        return Result.SUCCESS(jobInfo);
    }

    @Operation(summary = "图生文测试")
    @PostMapping(value = "/toText",name = "toText")
    public Result<Object> toText(@RequestBody HashMap<String, Object> jsonObject) {
        // ChatGPTWebUtil.getImageToText((String) jsonObject.get("url"));
        return Result.SUCCESS();
    }

    @Operation(summary = "图生文2测试")
    @PostMapping(value = "/mjtoText",name = "mjtoText")
    public Result<Object> mjtoText(@RequestBody HashMap<String, Object> jsonObject) {
        MJWebUtil.getImageToText((String) jsonObject.get("url"));
        return Result.SUCCESS();
    }

    @Operation(summary = "测试web接口")
    @PostMapping(value = "/mjwebpost",name = "mjwebpost")
    public Result<Object> mjwebpost(@RequestBody HashMap<String, Object> jsonObject) {
        OkHttpClient client = new OkHttpClient();

        String requestBody = "{\"data\":[\""+jsonObject.get("url")+"\"]}";
        okhttp3.RequestBody body = okhttp3.RequestBody.create(requestBody, MediaType.parse("application/json"));

        Request request = new Request.Builder()
                .url("https://www.midjourney.com/api/app/picread")
                .post(body)
                .addHeader("Accept", "*/*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Type", "application/json")
                .addHeader("Content-Length", String.valueOf(requestBody.length()))
                .addHeader("Origin", "https://www.midjourney.com")
                .addHeader("Referer", "https://www.midjourney.com/imagine")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .addHeader("X-Csrf-Protection", "1")
                .addHeader("Cookie", (String) jsonObject.get("cookie"))
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                if (response.body() != null) {
                    System.out.println(response.body().string());
                }
            } else {
                System.out.println("Request failed1: " + response.code());
                System.out.println("Request failed1: " + response.toString());
            }
        } catch (Exception e){
            System.out.println("Request failed: " + e.getMessage());
        }
        return Result.SUCCESS();
    }

    @Operation(summary = "测试web接口")
    @PostMapping(value = "/mjwebget",name = "mjwebget")
    public Result<Object> mjwebget(@RequestBody HashMap<String, Object> jsonObject) {
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(new BrotliInterceptor())
                .build();

        String requestBody = "{\"data\":[\""+jsonObject.get("url")+"\"]}";

        Request request = new Request.Builder()
                .url((String) jsonObject.get("url"))
                .addHeader("Accept", "*/*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Type", "application/json; charset=utf-8")
//                .addHeader("Content-Type", "application/json")
//                .addHeader("Content-Length", String.valueOf(requestBody.length()))
                .addHeader("Origin", "https://www.midjourney.com")
                .addHeader("Referer", "https://www.midjourney.com/imagine")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .addHeader("X-Csrf-Protection", "1")
                .addHeader("Cookie", (String) jsonObject.get("cookie"))
                .get()
                .build();
        try {
            Response response = client.newCall(request).execute();
            log.info("response,{}",response);
            if (response.body() != null) {
                String rqbody = response.body().string();
                log.info("responsebody ,{}",rqbody);
                log.info("responsebody ,{}",response.body());
            }
            log.info("response.headers(),{}",response.headers());
            log.info("response.isSuccessful(),{}",response.isSuccessful());


        } catch (IOException e) {
            log.error("error",e);
        }

        return Result.SUCCESS();
    }

    @Operation(summary = "图生文-图片上传")
    @PostMapping(value = "/mjtotextupload_file",name = "mjtotextupload_file")
    public Result<Object> mjtotextupload_file(@RequestParam("file") MultipartFile file) {
        log.info("file,{}",file);
        log.info("file2,{}",file.getName());
        String fileName = file.getOriginalFilename();
        log.info("file333,{}",fileName);
        if (file.getOriginalFilename() == null){
            return Result.ERROR("上传失败");
        }
        String userId = "9738bc8f-3fa2-4151-ba68-800ec91546f9";
        MJWebUtil.uploadFile(file,userId);
        return Result.SUCCESS();
    }

    @Operation(summary = "图生文-mj-chat汉化")
    @PostMapping(value = "/mjtoen",name = "mjtoen")
    public Result<Object> mjtoen(@RequestBody HashMap<String, Object> jsonObject) {

        String a = "获取成功,蝴蝶，萌趣淡彩背景，银白蝴蝶翱翔，梦幻浅蓝渐变色调。配以书本小字，屏幕底部以“Run”刺青文案，运用“蝴蝶Lancolm”为肌理，简洁字体，极简风。文字包含“每一天都是你的第二次机会。”白底、白边、白阴影、白框线、白星星、白色闪光玻璃效果，闪亮的Lancolm式亮片。;白底、淡蓝色渐变天空配白云和中央飞蝶的iPhone壁纸设计，“蝴蝶”字样，周围散布细碎光芒，呈现柔和淡彩，营造宁静氛围，文字为“每日一筒”，风格简约。;白底，白色蝴蝶图案飞于天际，带有“蝴蝶”及“每一天都是又一个机会”的文字，浅蓝绿色渐变色调营造宁静氛围，适用于手机主屏背景或社交媒体头像，风格简约；白底，淡蓝色渐变天空，白云和空中飞翔的白蝴蝶，梦幻光效，卡通风格，“蝴蝶”文字置底，语“每一秒都是另一种生活”，采用柔和的淡彩色，简约设计，竖版壁纸，简洁美学，数字艺术，高清，数字插画，矢量化，平滑渐变，细节背景，水彩画风格。";

//        GptToTextUtil.getTextToEn((String) jsonObject.get("en"));
        return Result.SUCCESS(MJWebUtil.getImgToText(null,null));
    }

    @Operation(summary = "mj bate64测试")
    @PostMapping(value = "/mjbate64",name = "mjbate64")
    public Result<Object> mjbate64(@RequestBody HashMap<String, Object> jsonObject) {
        return Result.SUCCESS(MJWebUtil.getCookieToBate64((String) jsonObject.get("text")));
    }

    @Operation(summary = "mj bate64测试")
    @PostMapping(value = "/getBate64ToURLDecoder",name = "getBate64ToURLDecoder")
    public Result<Object> getBate64ToURLDecoder(@RequestBody HashMap<String, Object> jsonObject) {
        return Result.SUCCESS(MJWebUtil.getBate64ToURLDecoder((String) jsonObject.get("text")));
    }

    @Operation(summary = "mj bate64测试")
    @PostMapping(value = "/mai2n",name = "mai2n")
    public Result<Object> mai2n() {
        MjWebAccountUtil.mai2n();
        return Result.SUCCESS();
    }

    @Operation(summary = "刷新排序测试")
    @PostMapping(value = "/refreshAccount",name = "refreshAccount")
    public Result<Object> refreshAccount() {
        MJRedisRefreshBO mjRedisRefreshBO = new MJRedisRefreshBO();
        mjRedisRefreshBO.setAppVersion(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_APP_VERSION.getStrKey()));
        mjRedisRefreshBO.setUserAgent(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_MJ_USER_AGENT.getStrKey()));
        List<MJAccountBO> mjAccountBOs = JSONArray.parseArray(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PRO_ACCOUNT_LIST.getStrKey()), MJAccountBO.class);
        MJAccountUtil.refreshAccountPro(mjAccountBOs,mjRedisRefreshBO);
        List<MJAccountBO> mjAccountBOs2 = JSONArray.parseArray(RedisUtil.getValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PT_ACCOUNT_LIST.getStrKey()), MJAccountBO.class);
//        MJAccountUtil.refreshAccountPt(mjAccountBOs2, mjRedisRefreshBO);//重置慢速账号
        return Result.SUCCESS();
    }



}
