package com.business.aigc.mj.web;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.business.aigc.mj.web.model.MjRequest;

/**
 * MJWebHttpUtil 修复效果验证测试类
 * 验证所有修复是否生效，确保请求能够成功发送到Midjourney API
 */
public class MJWebHttpUtilTest {

    private static int testCount = 0;
    private static int passCount = 0;

    public static void main(String[] args) {
        MJWebHttpUtilTest test = new MJWebHttpUtilTest();

        System.out.println("=== MJ修复效果验证测试开始 ===");

        try {
            test.testMjRequestMetadataFields();
            test.testTextToImageSubmitParamJSON();
            test.testJSONFieldOrder();
            test.testAPIEndpointConfiguration();
            test.testBackwardCompatibility();
            test.testCompleteRequestFormat();

            System.out.println("\n=== 测试结果汇总 ===");
            System.out.println("总测试数: " + testCount);
            System.out.println("通过测试: " + passCount);
            System.out.println("失败测试: " + (testCount - passCount));

            if (passCount == testCount) {
                System.out.println("✅ 所有测试通过！修复效果验证成功！");
            } else {
                System.out.println("❌ 部分测试失败，需要检查修复实现");
            }

        } catch (Exception e) {
            System.err.println("测试执行异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private MjRequest createTestRequest() {
        // 创建测试用的MjRequest对象
        MjRequest testRequest = new MjRequest();
        testRequest.setChannelId("singleplayer_16a5d350-0987-4604-9266-296fc4effcd1");
        testRequest.setPrompt("一个中国女孩模特，梦幻写真拍照，超高清，近景特写 --ar 9:16 --v 6.1 --stylize 0");
        testRequest.setJobType("imagine");
        testRequest.setId("1945788809892667393");
        testRequest.setIndex(0);

        // 设置Flags
        MjRequest.Flags flags = new MjRequest.Flags();
        flags.setMode("relaxed");
        flags.setPrivate(false);
        testRequest.setFlags(flags);

        // 设置Metadata（包含所有6个字段）
        MjRequest.Metadata metadata = new MjRequest.Metadata();
        metadata.setImagePrompts(0);
        metadata.setImageReferences(0);
        metadata.setCharacterReferences(0);
        metadata.setIsMobile(null);
        metadata.setDepthReferences(0);
        metadata.setLightboxOpen(null);
        testRequest.setMetadata(metadata);

        return testRequest;
    }

    // 简单的断言方法
    private static void assertTrue(boolean condition, String message) {
        testCount++;
        if (condition) {
            passCount++;
            System.out.println("✅ " + message);
        } else {
            System.out.println("❌ " + message);
        }
    }

    private static void assertEquals(Object expected, Object actual, String message) {
        testCount++;
        if ((expected == null && actual == null) || (expected != null && expected.equals(actual))) {
            passCount++;
            System.out.println("✅ " + message);
        } else {
            System.out.println("❌ " + message + " (期望: " + expected + ", 实际: " + actual + ")");
        }
    }

    private static void assertNotNull(Object obj, String message) {
        testCount++;
        if (obj != null) {
            passCount++;
            System.out.println("✅ " + message);
        } else {
            System.out.println("❌ " + message);
        }
    }

    private static void assertNull(Object obj, String message) {
        testCount++;
        if (obj == null) {
            passCount++;
            System.out.println("✅ " + message);
        } else {
            System.out.println("❌ " + message + " (实际值: " + obj + ")");
        }
    }

    public void testMjRequestMetadataFields() {
        System.out.println("\n--- 测试1: Metadata字段验证 ---");
        MjRequest testRequest = createTestRequest();

        // 验证Metadata类包含所有6个字段
        MjRequest.Metadata metadata = testRequest.getMetadata();

        assertNotNull(metadata, "Metadata对象不应为null");

        // 验证原有字段
        assertEquals(Integer.valueOf(0), metadata.getImagePrompts(), "imagePrompts字段应为0");
        assertEquals(Integer.valueOf(0), metadata.getImageReferences(), "imageReferences字段应为0");
        assertEquals(Integer.valueOf(0), metadata.getCharacterReferences(), "characterReferences字段应为0");

        // 验证新增字段
        assertNull(metadata.getIsMobile(), "isMobile字段应为null");
        assertEquals(Integer.valueOf(0), metadata.getDepthReferences(), "depthReferences字段应为0");
        assertNull(metadata.getLightboxOpen(), "lightboxOpen字段应为null");
    }

    public void testTextToImageSubmitParamJSON() {
        System.out.println("\n--- 测试2: JSON生成验证 ---");
        MjRequest testRequest = createTestRequest();

        // 测试生成的JSON格式是否正确
        String jsonParam = MJWebHttpUtil.textToImageSubmitParam(testRequest);

        assertNotNull(jsonParam, "生成的JSON参数不应为null");
        assertTrue(!jsonParam.isEmpty(), "生成的JSON参数不应为空");

        // 解析JSON验证结构
        JSONObject jsonObject = JSON.parseObject(jsonParam);

        // 验证基本字段
        assertEquals("singleplayer_16a5d350-0987-4604-9266-296fc4effcd1",
                    jsonObject.getString("channelId"), "channelId字段不匹配");
        assertEquals("imagine", jsonObject.getString("t"), "t字段不匹配");
        assertNull(jsonObject.get("roomId"), "roomId字段应为null");

        // 验证f对象
        JSONObject fObject = jsonObject.getJSONObject("f");
        assertNotNull(fObject, "f对象不应为null");
        assertEquals("relaxed", fObject.getString("mode"), "mode字段不匹配");
        assertEquals(false, fObject.getBoolean("private"), "private字段不匹配");

        // 验证metadata对象包含所有6个字段
        JSONObject metadataObject = jsonObject.getJSONObject("metadata");
        assertNotNull(metadataObject, "metadata对象不应为null");

        assertTrue(metadataObject.containsKey("isMobile"), "metadata应包含isMobile字段");
        assertTrue(metadataObject.containsKey("imagePrompts"), "metadata应包含imagePrompts字段");
        assertTrue(metadataObject.containsKey("imageReferences"), "metadata应包含imageReferences字段");
        assertTrue(metadataObject.containsKey("characterReferences"), "metadata应包含characterReferences字段");
        assertTrue(metadataObject.containsKey("depthReferences"), "metadata应包含depthReferences字段");
        assertTrue(metadataObject.containsKey("lightboxOpen"), "metadata应包含lightboxOpen字段");

        // 验证字段值
        assertNull(metadataObject.get("isMobile"), "isMobile应为null");
        assertEquals(Integer.valueOf(0), metadataObject.getInteger("imagePrompts"), "imagePrompts应为0");
        assertEquals(Integer.valueOf(0), metadataObject.getInteger("imageReferences"), "imageReferences应为0");
        assertEquals(Integer.valueOf(0), metadataObject.getInteger("characterReferences"), "characterReferences应为0");
        assertEquals(Integer.valueOf(0), metadataObject.getInteger("depthReferences"), "depthReferences应为0");
        assertNull(metadataObject.get("lightboxOpen"), "lightboxOpen应为null");
    }

    public void testJSONFieldOrder() {
        System.out.println("\n--- 测试3: JSON字段顺序验证 ---");
        MjRequest testRequest = createTestRequest();

        // 验证JSON字段顺序是否符合预期
        String jsonParam = MJWebHttpUtil.textToImageSubmitParam(testRequest);
        JSONObject jsonObject = JSON.parseObject(jsonParam);

        // 验证metadata字段顺序（isMobile应该在首位）
        JSONObject metadataObject = jsonObject.getJSONObject("metadata");
        String metadataJson = metadataObject.toJSONString();

        // 验证isMobile字段在metadata JSON中的位置
        assertTrue(metadataJson.contains("\"isMobile\""), "metadata JSON应包含isMobile字段");
        assertTrue(metadataJson.contains("\"depthReferences\""), "metadata JSON应包含depthReferences字段");
        assertTrue(metadataJson.contains("\"lightboxOpen\""), "metadata JSON应包含lightboxOpen字段");
    }

    public void testAPIEndpointConfiguration() {
        System.out.println("\n--- 测试4: API端点配置验证 ---");
        MjRequest testRequest = createTestRequest();

        // 验证API端点配置是否正确
        // 注意：这里我们无法直接测试private static final字段，
        // 但可以通过其他方式验证配置的正确性

        // 通过反射或其他方式验证host常量（如果需要的话）
        // 这里我们主要验证生成的请求格式是否正确
        String jsonParam = MJWebHttpUtil.textToImageSubmitParam(testRequest);
        assertNotNull(jsonParam, "API请求参数生成正常");

        // 验证请求参数包含必要字段
        JSONObject jsonObject = JSON.parseObject(jsonParam);
        assertTrue(jsonObject.containsKey("channelId"), "请求应包含channelId");
        assertTrue(jsonObject.containsKey("f"), "请求应包含f对象");
        assertTrue(jsonObject.containsKey("metadata"), "请求应包含metadata对象");
        assertTrue(jsonObject.containsKey("t"), "请求应包含t字段");
        assertTrue(jsonObject.containsKey("prompt"), "请求应包含prompt字段");
    }

    public void testBackwardCompatibility() {
        System.out.println("\n--- 测试5: 向后兼容性验证 ---");
        // 验证向后兼容性
        // 创建只有基本metadata字段的请求
        MjRequest basicRequest = new MjRequest();
        basicRequest.setChannelId("test-channel");
        basicRequest.setPrompt("test prompt");
        basicRequest.setJobType("imagine");
        
        MjRequest.Flags flags = new MjRequest.Flags();
        flags.setMode("fast");
        flags.setPrivate(true);
        basicRequest.setFlags(flags);
        
        MjRequest.Metadata basicMetadata = new MjRequest.Metadata();
        basicMetadata.setImagePrompts(1);
        basicMetadata.setImageReferences(2);
        basicMetadata.setCharacterReferences(3);
        // 不设置新字段，测试默认值处理
        basicRequest.setMetadata(basicMetadata);
        
        // 生成JSON应该正常工作
        String jsonParam = MJWebHttpUtil.textToImageSubmitParam(basicRequest);
        assertNotNull(jsonParam, "向后兼容性测试：JSON生成应正常");
        
        JSONObject jsonObject = JSON.parseObject(jsonParam);
        JSONObject metadataObject = jsonObject.getJSONObject("metadata");
        
        // 验证新字段有默认值
        assertTrue(metadataObject.containsKey("isMobile"), "向后兼容性：应包含isMobile字段");
        assertTrue(metadataObject.containsKey("depthReferences"), "向后兼容性：应包含depthReferences字段");
        assertTrue(metadataObject.containsKey("lightboxOpen"), "向后兼容性：应包含lightboxOpen字段");
    }

    public void testCompleteRequestFormat() {
        System.out.println("\n--- 测试6: 完整请求格式验证 ---");
        MjRequest testRequest = createTestRequest();

        // 验证完整请求格式与预期一致
        String jsonParam = MJWebHttpUtil.textToImageSubmitParam(testRequest);
        JSONObject jsonObject = JSON.parseObject(jsonParam);

        // 验证完整的请求结构
        String expectedStructure = "{\n" +
            "    \"f\": {\n" +
            "        \"mode\": \"relaxed\",\n" +
            "        \"private\": false\n" +
            "    },\n" +
            "    \"channelId\": \"singleplayer_16a5d350-0987-4604-9266-296fc4effcd1\",\n" +
            "    \"roomId\": null,\n" +
            "    \"metadata\": {\n" +
            "        \"isMobile\": null,\n" +
            "        \"imagePrompts\": 0,\n" +
            "        \"imageReferences\": 0,\n" +
            "        \"characterReferences\": 0,\n" +
            "        \"depthReferences\": 0,\n" +
            "        \"lightboxOpen\": null\n" +
            "    },\n" +
            "    \"t\": \"imagine\",\n" +
            "    \"prompt\": \"一个中国女孩模特，梦幻写真拍照，超高清，近景特写 --ar 9:16 --v 6.1 --stylize 0\"\n" +
            "}";

        JSONObject expectedObject = JSON.parseObject(expectedStructure);

        // 逐个验证关键字段
        assertEquals(expectedObject.getString("channelId"), jsonObject.getString("channelId"), "channelId字段匹配");
        assertEquals(expectedObject.getString("t"), jsonObject.getString("t"), "t字段匹配");
        assertEquals(expectedObject.getString("prompt"), jsonObject.getString("prompt"), "prompt字段匹配");

        // 验证f对象
        JSONObject expectedF = expectedObject.getJSONObject("f");
        JSONObject actualF = jsonObject.getJSONObject("f");
        assertEquals(expectedF.getString("mode"), actualF.getString("mode"), "f.mode字段匹配");
        assertEquals(expectedF.getBoolean("private"), actualF.getBoolean("private"), "f.private字段匹配");

        // 验证metadata对象的字段数量
        JSONObject expectedMetadata = expectedObject.getJSONObject("metadata");
        JSONObject actualMetadata = jsonObject.getJSONObject("metadata");
        assertEquals(expectedMetadata.size(), actualMetadata.size(), "metadata字段数量应匹配");

        System.out.println("生成的完整JSON: " + jsonParam);
    }
}
