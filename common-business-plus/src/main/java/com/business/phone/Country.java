package com.business.phone;

import lombok.Getter;

@Getter
public enum Country {
    CN("中国", "China", "86"),
    HK("香港", "Hongkong", "852"),
    MO("澳门", "Macao", "853"),
    TW("台湾", "Taiwan", "886"),
    US("美国", "UnitedStatesofAmerica", "1"),
    CA("加拿大", "Canada", "1"),
    GB("英国", "UnitedKiongdom", "44"),
    DE("德国", "Germany", "49"),
    FR("法国", "France", "33"),
    BO("玻利维亚", "Bolivia", "591"),
    BR("巴西", "Brazil", "55"),
    CO("哥伦比亚", "Colombia", "57"),
    IT("意大利", "Italy", "39"),
    CI("科特迪瓦", "IvoryCoast", "225"),
    AE("阿拉伯联合酋长国", "UnitedArabEmirates", "971"),
    AF("阿富汗", "Afghanistan", "93"),
    AG("安提瓜和巴布达", "AntiguaandBarbuda", "1268"),
    AI("安圭拉岛", "Anguilla", "1264"),
    AL("阿尔巴尼亚", "Albania", "355"),
    AM("亚美尼亚", "Armenia", "374"),
    AS("阿森松", "Ascension", "247"),
    AO("安哥拉", "Angola", "244"),
    AD("安道尔共和国", "Andorra", "376"),
    AR("阿根廷", "Argentina", "54"),
    AT("奥地利", "Austria", "43"),
    AU("澳大利亚", "Australia", "61"),
    AZ("阿塞拜疆", "Azerbaijan", "994"),
    BB("巴巴多斯", "Barbados", "1246"),
    BD("孟加拉国", "Bangladesh", "880"),
    BE("比利时", "Belgium", "32"),
    BF("布基纳法索", "Burkina-faso", "226"),
    BG("保加利亚", "Bulgaria", "359"),
    BH("巴林", "Bahrain", "973"),
    BI("布隆迪", "Burundi", "257"),
    BJ("贝宁", "Benin", "229"),
    BL("巴勒斯坦", "Palestine", "970"),
    BM("百慕大群岛", "BermudaIs.", "1441"),
    BN("文莱", "Brunei", "673"),
    BS("巴哈马", "Bahamas", "1242"),
    BW("博茨瓦纳", "Botswana", "267"),
    BY("白俄罗斯", "Belarus", "375"),
    BZ("伯利兹", "Belize", "501"),
    KY("开曼群岛", "CaymanIs.", "1345"),
    CF("中非共和国", "CentralAfricanRepublic", "236"),
    CG("刚果", "Congo", "242"),
    CH("瑞士", "Switzerland", "41"),
    CK("库克群岛", "CookIs.", "682"),
    CL("智利", "Chile", "56"),
    CM("喀麦隆", "Cameroon", "237"),
    CR("哥斯达黎加", "CostaRica", "506"),
    CS("捷克", "Czech", "420"),
    CU("古巴", "Cuba", "53"),
    CY("塞浦路斯", "Cyprus", "357"),
    CZ("捷克", "CzechRepublic", "420"),
    DJ("吉布提", "Djibouti", "253"),
    DK("丹麦", "Denmark", "45"),
    DO("多米尼加共和国", "DominicaRep.", "1890"),
    DZ("阿尔及利亚", "Algeria", "213"),
    EC("厄瓜多尔", "Ecuador", "593"),
    EE("爱沙尼亚", "Estonia", "372"),
    EG("埃及", "Egypt", "20"),
    ES("西班牙", "Spain", "34"),
    ET("埃塞俄比亚", "Ethiopia", "251"),
    FI("芬兰", "Finland", "358"),
    FJ("斐济", "Fiji", "679"),
    FM("密克罗尼西亚", "Micronesia", "691"),
    FO("法罗群岛", "FaroeIs.", "298"),
    GA("加蓬", "Gabon", "241"),
    GD("格林纳达", "Grenada", "1809"),
    GE("格鲁吉亚", "Georgia", "995"),
    GF("法属圭亚那", "FrenchGuiana", "594"),
    GH("加纳", "Ghana", "233"),
    GI("直布罗陀", "Gibraltar", "350"),
    GL("格陵兰岛", "Greenland", "299"),
    GM("冈比亚", "Gambia", "220"),
    GN("几内亚", "Guinea", "224"),
    GR("希腊", "Greece", "30"),
    GT("危地马拉", "Guatemala", "502"),
    GU("关岛", "Guam", "1671"),
    GW("几内亚比绍共和国", "Guinea-bissau", "245"),
    GY("圭亚那", "Guyana", "592"),
    HN("洪都拉斯", "Honduras", "504"),
    HT("海地", "Haiti", "509"),
    HU("匈牙利", "Hungary", "36"),
    ID("印度尼西亚", "Indonesia", "62"),
    IE("爱尔兰", "Ireland", "353"),
    IL("以色列", "Israel", "972"),
    IN("印度", "India", "91"),
    IQ("伊拉克", "Iraq", "964"),
    IR("伊朗", "Iran", "98"),
    IS("冰岛", "Iceland", "354"),
    JM("牙买加", "Jamaica", "1876"),
    JO("约旦", "Jordan", "962"),
    JP("日本", "Japan", "81"),
    KE("肯尼亚", "Kenya", "254"),
    KG("吉尔吉斯坦", "Kyrgyzstan", "331"),
    KH("柬埔寨", "Kampuchea (Cambodia )", "855"),
    KP("朝鲜", "NorthKorea", "850"),
    KR("韩国", "Korea", "82"),
    KT("科特迪瓦共和国", "RepublicofCoteD'lvoire", "225"),
    KW("科威特", "Kuwait", "965"),
    KZ("哈萨克斯坦", "Kazakstan", "327"),
    LA("老挝", "Laos", "856"),
    LB("黎巴嫩", "Lebanon", "961"),
    LC("圣卢西亚", "St.Lucia", "1758"),
    LI("列支敦士登", "Liechtenstein", "423"),
    LK("斯里兰卡", "SriLanka", "94"),
    LR("利比里亚", "Liberia", "231"),
    LS("莱索托", "Lesotho", "266"),
    LT("立陶宛", "Lithuania", "370"),
    LU("卢森堡", "Luxembourg", "352"),
    LV("拉脱维亚", "Latvia", "371"),
    LY("利比亚", "Libya", "218"),
    MA("摩洛哥", "Morocco", "212"),
    MC("摩纳哥", "Monaco", "377"),
    MD("摩尔多瓦共和国", "Moldova,Republicof", "373"),
    MG("马达加斯加", "Madagascar", "261"),
    ML("马里", "Mali", "223"),
    MM("缅甸", "Burma", "95"),
    MN("蒙古", "Mongolia", "976"),
    MS("蒙特塞拉特岛", "MontserratIs", "1664"),
    MT("马耳他", "Malta", "356"),
    MU("毛里求斯", "Mauritius", "230"),
    MV("马尔代夫", "Maldives", "960"),
    MW("马拉维", "Malawi", "265"),
    MX("墨西哥", "Mexico", "52"),
    MY("马来西亚", "Malaysia", "60"),
    MZ("莫桑比克", "Mozambique", "258"),
    NA("纳米比亚", "Namibia", "264"),
    NE("尼日尔", "Niger", "977"),
    NG("尼日利亚", "Nigeria", "234"),
    NI("尼加拉瓜", "Nicaragua", "505"),
    NL("荷兰", "Netherlands", "31"),
    NO("挪威", "Norway", "47"),
    NP("尼泊尔", "Nepal", "977"),
    NR("瑙鲁", "Nauru", "674"),
    NZ("新西兰", "NewZealand", "64"),
    OM("阿曼", "Oman", "968"),
    PA("巴拿马", "Panama", "507"),
    PE("秘鲁", "Peru", "51"),
    PF("法属玻利尼西亚", "FrenchPolynesia", "689"),
    PG("巴布亚新几内亚", "PapuaNewCuinea", "675"),
    PH("菲律宾", "Philippines", "63"),
    PK("巴基斯坦", "Pakistan", "92"),
    PL("波兰", "Poland", "48"),
    PR("波多黎各", "PuertoRico", "1787"),
    PT("葡萄牙", "Portugal", "351"),
    PY("巴拉圭", "Paraguay", "595"),
    QA("卡塔尔", "Qatar", "974"),
    RO("罗马尼亚", "Romania", "40"),
    RU("俄罗斯", "Russia", "7"),
    SA("沙特阿拉伯", "SaudiArabia", "966"),
    SB("所罗门群岛", "SolomonIs", "677"),
    SC("塞舌尔", "Seychelles", "248"),
    SD("苏丹", "Sudan", "249"),
    SE("瑞典", "Sweden", "46"),
    SG("新加坡", "Singapore", "65"),
    SI("斯洛文尼亚", "Slovenia", "386"),
    SK("斯洛伐克", "Slovakia", "421"),
    SL("塞拉利昂", "SierraLeone", "232"),
    SM("圣马力诺", "SanMarino", "378"),
    SN("塞内加尔", "Senegal", "221"),
    SO("索马里", "Somali", "252"),
    SR("苏里南", "Suriname", "597"),
    ST("圣多美和普林西比", "SaoTomeandPrincipe", "239"),
    SV("萨尔瓦多", "EI", "503"),
    SY("叙利亚", "Syria", "963"),
    SZ("斯威士兰", "Swaziland", "268"),
    TD("乍得", "Chad", "235"),
    TG("多哥", "Togo", "228"),
    TH("泰国", "Thailand", "66"),
    TJ("塔吉克斯坦", "Tajikstan", "992"),
    TM("土库曼斯坦", "Turkmenistan", "993"),
    TN("突尼斯", "Tunisia", "216"),
    TO("汤加", "Tonga", "676"),
    TR("土耳其", "Turkey", "90"),
    TT("特立尼达和多巴哥", "TrinidadandTobago", "1809"),
    TV("图瓦卢", "Tuvalu", "688"),
    TZ("坦桑尼亚", "Tanzania", "255"),
    UA("乌克兰", "Ukraine", "380"),
    UG("乌干达", "Uganda", "256"),
    UY("乌拉圭", "Uruguay", "598"),
    UZ("乌兹别克斯坦", "Uzbekistan", "233"),
    VC("圣文森特岛", "SaintVincent", "1784"),
    VE("委内瑞拉", "Venezuela", "58"),
    VN("越南", "Vietnam", "84"),
    YE("也门", "Yemen", "967"),
    YU("南斯拉夫", "Yugoslavia", "381"),
    ZA("南非", "SouthAfrica", "27"),
    ZM("赞比亚", "Zambia", "260"),
    ZR("扎伊尔", "Zaire", "243"),
    ZW("津巴布韦", "Zimbabwe", "263");

    private final String cnName;
    private final String enName;
    private final String code;

    Country(String cnName, String enName, String code) {
        this.cnName = cnName;
        this.enName = enName;
        this.code = code;
    }

    public static Country findByCode(String code) {
        for (Country country : Country.values()) {
            if (country.code.equals(code)) {
                return country;
            }
        }
        return null;
    }

    public static Country findByCnName(String cnName) {
        for (Country country : Country.values()) {
            if (country.cnName.equals(cnName)) {
                return country;
            }
        }
        return null;
    }

    public static Country findByEnName(String enName) {
        for (Country country : Country.values()) {
            if (country.enName.equals(enName)) {
                return country;
            }
        }
        return null;
    }

}

