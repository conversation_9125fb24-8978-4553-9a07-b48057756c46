package com.business.db.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "管理平台登陆接口返回实体", description = "管理平台登陆接口返回实体")
public class AdminLoginVO {

    public AdminLoginVO(String token) {
        this.token = token;
        this.name = "超级管理员";
        this.avatarUrl = "https://cdn.diandiansheji.com/avatar/1775170763495673857.jpg";
    }

    @Schema(name = "token", type = "String")
    private String token;

    @Schema(name = "管理员名称", type = "String")
    private String name;

    @Schema(name = "管理员头像", type = "String")
    private String avatarUrl;

}
