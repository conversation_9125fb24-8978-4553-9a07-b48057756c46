package com.business.db.model.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName("user_good_record")
public class UserGoodRecordPO extends BaseEntity {

    @Schema(name = "主键id", type = "Long")
    private Long id;

    @Schema(name = "用户id", type = "Long")
    private Long userId;

    @Schema(name = "状态", type = "Integer")
    private Integer state;

    @TableField(value = "submit_time", fill = FieldFill.INSERT)
    @Schema(name = "创建时间（默认为创建时服务器时间）", type = "Date")
    @JsonFormat(timezone = "Asia/Shanghai",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    @TableField(value = "audit_time", fill = FieldFill.INSERT)
    @Schema(name = "创建时间（默认为创建时服务器时间）", type = "Date")
    @JsonFormat(timezone = "Asia/Shanghai",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    public UserGoodRecordPO() {
        super();
    }
    public UserGoodRecordPO(Long userId, Integer state, Date submitTime, Date auditTime) {
        this.id = IdWorker.getId();
        this.userId = userId;
        this.state = state;
        this.submitTime = submitTime;
        this.auditTime = auditTime;
    }


}
