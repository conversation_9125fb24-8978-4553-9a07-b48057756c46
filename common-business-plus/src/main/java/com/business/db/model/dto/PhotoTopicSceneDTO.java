package com.business.db.model.dto;


import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "写真场景表", description = "写真场景表")
public class PhotoTopicSceneDTO extends BasePageHelper {

    @Schema(name = "主题id", type = "Long", description = "主题id")
    private Long topicId;

    public PhotoTopicSceneDTO(Long topicId) {
        this.topicId = topicId;
    }
}
