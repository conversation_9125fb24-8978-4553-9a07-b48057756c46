package com.business.db.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("password_config")
@Schema(name = "PasswordConfigPO", description = "口令记录配置表")
public class PasswordConfigPO extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "口令兑换码", type = "String")
    private String codeStr;

    @Schema(name = "点点最大数量", type = "Double")
    private Double ddMaxQuantity;

    @Schema(name = "点点最小数量", type = "Double")
    private Double ddMinQuantity;

    @Schema(name = "失效时间", type = "Date")
    private Date expirationTime;

}
