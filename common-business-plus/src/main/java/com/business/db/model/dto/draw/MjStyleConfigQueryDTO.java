package com.business.db.model.dto.draw;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Schema(name = "MJ绘画风格配置查询分页", description = "MJ绘画风格配置查询分页")
public class MjStyleConfigQueryDTO extends BasePageHelper implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "", type = "Long")
    private Long languageTagId;

    @NotNull(message = "风格分类id不能为空！")
    @Schema(name = "风格分类id", type = "Long")
    private Long classifyId;

    @NotNull(message = "风格标签id不能为空！")
    @Schema(name = "风格标签id", type = "Long")
    private Long labelId;

}
