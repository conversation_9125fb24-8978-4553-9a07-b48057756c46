package com.business.db.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserRightsConfigShowVO {

    @Schema(name = "主键id", type = "Long")
    private Long id;

    @Schema(name = "权益名称", type = "String")
    private String rightsName;

    @Schema(name = "展示非会员权益", type = "String")
    private String notPackage;

    @Schema(name = "展示会员权益", type = "String")
    private String memberPackage;

    public UserRightsConfigShowVO() {}

    public UserRightsConfigShowVO(Long id, String rightsName, String notPackage, String memberPackage) {
        this.id = id;
        this.rightsName = rightsName;
        this.notPackage = notPackage;
        this.memberPackage = memberPackage;
    }

}
