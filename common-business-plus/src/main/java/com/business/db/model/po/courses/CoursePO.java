package com.business.db.model.po.courses;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 课程实体类
 * 对应数据库表：courses
 */
@Data
@TableName("courses")
public class CoursePO {

    /**
     * 主键，唯一标识课程
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 课程名称
     */
    private String title;

    /**
     * 课程介绍
     */
    private String introduction;

    /**
     * 课程价格，默认0.00
     */
    private BigDecimal price;

    /**
     * 课程封面图片链接
     */
    private String imageUrl;

    /**
     * 状态：0-下线, 1-上线, 2-草稿
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 购买人数
     */
    private Integer purchaseCount;

    /**
     * 学员评价数量
     */
    private Integer commentCount;

    /**
     * 课程内容简介
     */
    private String content;
} 