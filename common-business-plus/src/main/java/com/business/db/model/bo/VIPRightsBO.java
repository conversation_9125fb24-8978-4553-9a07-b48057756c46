package com.business.db.model.bo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(name = "消息使用【会员权益实体类】", description = "会员权益实体类")
public class VIPRightsBO extends BaseEntity {

    @Schema(name = "用户名称", type = "String")
    private String name;

    @Schema(name = "用户手机号", type = "String")
    private String mobile;

    @Schema(name = "用户ID", type = "Long")
    private Long userId;

    @Schema(name = "订单类型：1加油包；2VIP；3SVIP", type = "Integer")
    private Integer orderType;

    @Schema(name = "实际支付金额", type = "BigDecimal")
    private BigDecimal amount;

    @Schema(name = "到期时间", type = "Date")
    private String expirationTime;


    public static VIPRightsBO buileVIPRightsBO (String name, String mobile, Long userId, Integer orderType,
                                                        BigDecimal amount, String expirationTime) {
        VIPRightsBO vipRightsBO = new VIPRightsBO();
        vipRightsBO.name = name;
        vipRightsBO.mobile = mobile;
        vipRightsBO.userId = userId;
        vipRightsBO.orderType = orderType;
        vipRightsBO.amount = amount;
        vipRightsBO.expirationTime = expirationTime;
        return vipRightsBO;
    }

}
