package com.business.db.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class PersonalPageVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "用户ID", type = "Long")
    private Long id;

    @Schema(title = "姓名", type = "String")
    private String name;

    @Schema(title = "手机号", type = "String")
    private String mobile;

    @Schema(title = "头像地址", type = "String")
    private String avatar;

    @Schema(title = "DDIT", type = "String")
    private String ddid;

    @Schema(title = "关注数", type = "Integer")
    private Integer focusNum;

    @Schema(title = "粉丝数", type = "Integer")
    private Integer fansNum;

    @Schema(title = "收藏数", type = "Integer")
    private Integer collectNum;

    @Schema(title = "点赞数量", type = "Integer")
    private Integer likeNum;

    @Schema(title = "作品数量", type = "Integer")
    private Integer worksNum;

    @Schema(title = "是否关注 0否、1是", type = "Integer")
    private Integer focusFlag;

    @Schema(title = "是否会员 0否、1是", type = "String")
    private Integer levelFlag;

    @Schema(title = "当前套餐", type = "Long")
    private Long currentPackage;

    @Schema(name = "会员等级名称：普通；VIP；SVIP", type = "String")
    private String vipGradeName;

    @Schema(title = "是否提示点子数 0否、1是", type = "Integer")
    private Integer promptFlag;

    @Schema(title = "是否提示点子数 1提示 0不提示", type = "Integer")
    private Integer promptSign;

    @Schema(title = "是否同意上传音乐的权限（0，不同意，1同意）", type = "Integer")
    private Integer isAgreeUploadMusic;

}
