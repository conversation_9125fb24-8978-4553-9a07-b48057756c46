package com.business.db.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(FunctionConfigPO.TABLE_NAME)
@Schema(name = "功能配置实体", description = "功能配置实体")
public class FunctionConfigPO extends BaseEntity {

    public static final String TABLE_NAME = "function_config";

    @Schema(name = "主键id", type = "Long")
    private Long id;

    @Schema(name = "关联语言id", type = "Long")
    private Long languageTagId;

    @Schema(name = "功能名称", type = "String")
    private String name;

    @Schema(name = "tag标签内容", type = "String")
    private String tag;

    @Schema(name = "tag标签背景色", type = "String")
    private String tagColor;

    @Schema(name = "显示图标", type = "String")
    private String icon;

    @Schema(name = "类型", type = "Integer")
    private Integer type;

    @Schema(name = "路由跳转", type = "String")
    private String route;

    @Schema(name = "排序按升序", type = "Integer")
    private Integer sort;

    @Schema(name = "是否显示", type = "Integer")
    private Integer isShow;

    @Schema(name = "状态：1允许使用；0禁止使用", type = "int")
    private int state;

    @Schema(name = "状态禁用展示内容", type = "String")
    private String stateOffTag;

}
