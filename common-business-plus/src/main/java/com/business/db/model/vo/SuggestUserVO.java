package com.business.db.model.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * PC推荐用户返回实体
 * <AUTHOR>
 * @date 2023-12-27
 * @
 */
@Schema(name = "首页推荐用户" , description = "首页推荐用户")
@Data
public class SuggestUserVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 326308725675949330L;

    @Schema(name = "用户id" , type = "Long")
    private Long userId;

    @Schema(name = "用户头像" , type = "String")
    private String avatar;

    @Schema(name = "用户昵称" , type = "String")
    private String nickname;
}
