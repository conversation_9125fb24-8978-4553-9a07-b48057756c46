package com.business.db.model.po;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "点点到期返回实体", description = "点点到期返回实体")
public class DDExpirationPO {

    @Schema(name = "用户id", type = "Long")
    private Long userId;

    @Schema(name = "用户即将到期点子数量", type = "Double")
    private Double ddQuantity;

    @Schema(name = "查询日期时间", type = "Integer")
    private Integer days;

}
