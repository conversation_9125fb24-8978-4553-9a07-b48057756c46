package com.business.db.model.dto;

import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(title = "画廊查询接收参数实体")
public class CommunityQueryDTO extends BasePageHelper implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "提示词--模糊查询")
    private String prompt;

    @Deprecated
    @Schema(title = "排序条件")
    private String orderBy;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "任务类型 1收藏 2 点赞")
    private Integer jobType;

    @Schema(title = "绘画者Id")
    private Long painterId;

    @Schema(title = "是否分享")
    private Integer isShare;

    @Schema(title = "图片标签id")
    private Long imageTagId;

}
