package com.business.db.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * @description: 图片点赞
 * @author: m
 * @version: 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImgLikeDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull(message = "用户id不可为空！")
    private Long userId;

    /**
     * 任务ID
     */
    @NotNull(message = "任务id不可为空！")
    private Long taskId;

    /**
     * 图片ID
     */
    @NotNull(message = "图片id不可为空！")
    private Long imgId;
}
