package com.business.db.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

@Schema(name = "用户绑定手机DTO", description = "用户绑定手机DTO")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserBindMobileDTO implements Serializable {

    @Schema(name = "用户ID", type = "String")
    private String userId;

    @NotEmpty(message = "手机号不可为空！")
    @Schema(name = "手机号", type = "String")
    private String mobile;

    @Schema(name = "国际区号", type = "String")
    private String areaCode;

    @NotEmpty(message = "手机验证码不可为空！")
    @Schema(name = "手机验证码", type = "String")
    private String verificationCode;

    @Schema(name = "邀请人", type = "String")
    private String inviterCode;

    @Schema(name = "友盟设备Token", type = "String")
    private String deviceToken;

    @Schema(name = "登陆类型：1web，2app", type = "Integer")
    private Integer loginType;

    @Schema(name = "微信相关信息token", type = "String")
    private String wxToken;

    @Schema(name = "用户授权Token", type = "String")
    private String token;

    @Schema(name = "后期删除兼容老app", type = "String")
    private String aaaaaaa;

}

