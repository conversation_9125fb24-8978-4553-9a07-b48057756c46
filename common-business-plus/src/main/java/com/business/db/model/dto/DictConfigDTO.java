package com.business.db.model.dto;

import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

@EqualsAndHashCode(callSuper = true)
@Data
public class DictConfigDTO extends BasePageHelper {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "类型编码", type = "Long")
    private Long dictType;

    @Schema(name = "子key", type = "Long")
    private Long dictKey;

    @Schema(name = "值", type = "String")
    private String dictValue;

    @Schema(name = "是否使用", type = "Integer")
    private Integer isUse;

}
