package com.business.db.model;

import com.nacos.base.BasePageHelper;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "画廊查询参数实体", description = "画廊查询参数实体")
public class GalleryQueryNewDTO extends BasePageHelper implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "用户id不可为空！")
    @Schema(title = "用户ID", type = "Long")
    private Long userId;

    @Schema(title = "绘画者Id", type = "Long")
    private Long painterId;

    /**
     * 前缀路径
     */
    //private String prefixUrl = DrawConst.accessPath.concat(DrawConst.fileName);
    private String prefixUrl = CommonStrEnum.IMAGE_PREFIX.getValue();


}
