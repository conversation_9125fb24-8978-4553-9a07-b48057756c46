package com.business.db.model.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @className: com.intelligent.bot.model.res.sys-> UserInfoRes
 * @description: 用户信息
 * @author: Admin
 * @createDate: 2023-07-17 10:11
 * @version: 1.0
 */
@Data
public class UserInfoVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long id;
    /**
     * 姓名
     */
    private String name;

    /**
     * 会员昵称
     */
    private String nickName;

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 头像地址
     */
    private String avatar;
    /**
     * 地址
     */
    private String address;

    /**
     * 个人简介
     */
    private String personalProfile;
    /**
     * 会员标识 0否 1是
     */
    private Integer levelFlag;

    /**
     * 是否关注公众号 0未关注 1关注
     * 新版：绑定微信
     */
    private Integer isEvent;

    /**
     * 当前套餐总量
     */
    private Double total;

    /**
     * 套餐数
     */
    private Integer orderQuantity;

    /**
     * 余额
     */
    private Double remainingTimes;

    /**
     * 使用量
     */
    private Double usedQuantity;

    /**
     * 并发数
     */
    private Integer concurrency;
}
