package com.business.db.model.dto;

import com.drew.lang.annotations.NotNull;
import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.validation.annotation.Validated;

import java.io.Serial;
import java.io.Serializable;


@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "用户举报记录实体参数", description = "用户举报记录实体参数")
@Validated
public class SysReportRecordAuditDTO extends BasePageHelper implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull
    @Schema(name = "举报id", type = "int")
    private Long recordId;

    @NotNull
    @Schema(name = "处理id", type = "int")
    private Integer worksHandleId;

    @NotNull
    @Schema(name = "用户处理id", type = "int")
    private Integer userHandleId;

}
