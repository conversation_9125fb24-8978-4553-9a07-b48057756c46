package com.business.db.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(ImgTagConfigPO.TABLE_NAME)
@Schema(name = "图片标签配置实体", description = "图片标签配置实体")
public class ImgTagConfigPO extends BaseEntity {

    public static final String TABLE_NAME = "img_tag_config";

    @Schema(name = "主键id", type = "Long")
    private Long id;

    @Schema(name = "关联语言id", type = "Long")
    private Long languageTagId;

    @Schema(name = "标题", type = "String")
    private String title;

    @Schema(name = "关联检索表id字符串数组的存储方式", type = "String")
    private String imgSearchIds;

    @Schema(name = "排序按升序", type = "int")
    private int sort;

    @Schema(name = "是否显示", type = "int")
    private int isShow;

}
