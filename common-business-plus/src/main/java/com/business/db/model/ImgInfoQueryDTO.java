package com.business.db.model;

import com.nacos.enums.CommonStrEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * @className: com.intelligent.bot.model.req.mj-> GalleryQueryReq
 * @description:
 * @author: Admin
 * @createDate: 2023-07-03 16:56
 * @version: 1.0
 */
@Data
public class ImgInfoQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotNull(message = "用户id不可为空！")
    private Long userId;

    /**
     * 图片Id
     */
    @NotNull(message = "图片id不可为空！")
    private Long imgId;

    /**
     * 前缀路径
     */
    //private String prefixUrl = DrawConst.accessPath.concat(DrawConst.fileName);
    private String prefixUrl = CommonStrEnum.IMAGE_PREFIX.getValue();

}
