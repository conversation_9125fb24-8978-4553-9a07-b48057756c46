package com.business.db.model.dto;

import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.validation.annotation.Validated;

import java.io.Serial;
import java.io.Serializable;


@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "用户举报记录实体参数", description = "用户举报记录实体参数")
@Validated
public class SysReportRecordQueryDTO extends BasePageHelper implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "状态：0 待处理， 1 已处理", type = "int")
    private int state;
}
