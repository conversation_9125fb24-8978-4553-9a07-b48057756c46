package com.business.db.model.po.admin;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ope_statistics_user")
@Schema(name = "运营统计用户返回实体", description = "运营统计用户返回实体")
public class OpeStatisticsUserPO extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "日活跃用户数量", type = "Long")
    private Integer activeQuantity;

    @Schema(name = "日新增用户数量", type = "Long")
    private Long addQuantity;

    @Schema(name = "日分享用户数量", type = "Long")
    private Long shareAddQuantity;

    @Schema(name = "日支付用户数量", type = "Long")
    private Long payQuantity;

    @Schema(name = "任务登记时间：yyyymmdd", type = "String")
    private String taskDate;

}
