package com.business.db.model.dto;

import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "写真主题（封面）查询参数", description = "写真主题（封面）查询参数")
public class PhotoTopicDTO extends BasePageHelper {

    @Schema(name = "关联语言id", type = "Long")
    private Long languageTagId;

    @Schema(name = "关联主题类型id", type = "Long")
    private Long topicTypeId;

    @Schema(name = "主题标题", type = "String")
    @NotNull(message = "主题标题不能为空")
    private String topicTitle;

    @Schema(name = "状态：1 上架 2下架", type = "Integer")
    private Integer status;

    @Schema(name = "id", type = "Long")
    private Long id;

}
