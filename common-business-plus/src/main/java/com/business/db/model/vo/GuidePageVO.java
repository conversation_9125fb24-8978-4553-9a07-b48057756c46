package com.business.db.model.vo;

import com.nacos.enums.CommonStrEnum;
import lombok.Data;

@Data
public class GuidePageVO {

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 变换宽度
     */
    private Integer imgWidth;

    /**
     * 变换高度
     */
    private Integer imgHeight;

    public String getImgUrl() {
        if (imgUrl != null && !imgUrl.contains("http")) {
            //装载前缀
            return CommonStrEnum.IMAGE_PREFIX.getValue() + imgUrl;
        }
        return imgUrl;
    }

}
