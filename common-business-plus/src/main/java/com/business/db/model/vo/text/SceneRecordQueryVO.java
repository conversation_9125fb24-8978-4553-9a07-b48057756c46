package com.business.db.model.vo.text;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @className: SceneRecordQueryVO
 * @description: 查询文案内容列表
 * @author: MYL
 * @createDate: 2024-01-27 13:11
 * @version: 1.0
 */
@Data
public class SceneRecordQueryVO {

    @Schema(name = "id", type = "Long")
    private Long id;

    @Schema(name = "场景ID", type = "Long")
    private Long sceneId;

    @Schema(name = "标题", type = "String")
    private String title;

    @Schema(name = "问题", type = "String")
    private String problem;

    @Schema(name = "内容", type = "String")
    private String message;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;
}
