package com.business.db.model.dto;

import com.nacos.enums.ImgOptModelEnum;
import com.nacos.mjapi.model.MjAddImageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(name = "绘图操作参数实体", description = "绘图操作参数实体")
@Data
@Valid
public class ImgDrawOptDTO {

    @NotNull(message = "绘图详情ID不能为空！")
    @Schema(name = "提取方法…", type = "Long")
    private Long imgDrawDetlId;

    @NotNull(message = "操作键值不能为空！")
    @Schema(name = "操作键值", type = "Integer")
    private Integer operate;

    @Schema(name = "缩放值参数：缩放时必传", type = "String")
    private String zoomFactorStr;

    @Schema(name = "局部修改变化参数内容：局部修改时必传", type = "String")
    private String varyRegionPrompt;

    @Schema(name = "局部修改蒙板：Bate64：局部修改时必传", type = "String")
    private String varyRegionMask;

    @Schema(name = "AI换脸模板Id", type = "Long")
    private Long faceTemplateId;

    @Schema(name = "纵横比=位置", type = "Integer")
    private Integer location;

    @Schema(name = "局部修改垫图操作", type = "VaryRegionUrl")
    private VaryRegionUrl varyRegionUrl;

    @Schema(name = "垫图图片地址", type = "Array")
    private List<String> initImgUrls;
    @Schema(name = "垫图实体：客户端提交", type = "MjAddImageBO")
    private MjAddImageBO mjAddImageBO;

    @Data
    public static class VaryRegionUrl {

        @Schema(name = "图片路径", type = "String")
        private String vrUrl;

        @Schema(name = "图片类型", type = "Integer")
        private Integer vrType;

        @Schema(name = "权重", type = "Integer")
        private Integer vrWight;

        @Schema(name = "组合", type = "String")
        private String vrAll;

        public String getVrAll() {
            if (vrUrl == null || vrType == null || vrWight == null) {
                return null;
            }
            //1参考；2风格；--sref|--sw  3角色：(--cref|--cw|)";
            if (vrType == ImgOptModelEnum.MJ_PADDING_REFERENCE.getValue()){
                vrAll = " "+vrUrl+" --iw "+ vrWight;
            }
            if (vrType == ImgOptModelEnum.MJ_PADDING_STYLE.getValue()){
                vrAll = " --sref "+vrUrl+" --sw "+ vrWight;
            }
            if (vrType == ImgOptModelEnum.MJ_PADDING_ROLE.getValue()){
                vrAll = " --cref "+vrUrl+" --cw "+ vrWight;
            }
            return vrAll;
        }
    }


}
