package com.business.db.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(name = "海报操作配置实体返回", description = "海报操作配置实体返回")
public class PosterOptConfigVO {


    @Schema(name = "主键", type = "Long")
    private Long id;

    @Schema(name = "当前操作", type = "int")
    private Integer operate;

    @Schema(name = "名称", type = "String")
    private String name;

    @Schema(name = "跳转路由", type = "String")
    private String route;

    @Schema(name = "操作图标", type = "String")
    private String iconUrl;

    @Schema(name = "消耗点子数", type = "Double")
    private Double expendDdQua;

    @Schema(name = "是否vip功能", type = "Integer")
    private Integer isVip;

    @Schema(name = "排序", type = "Integer")
    private Integer sort;

    @Schema(name = "普通用户消耗点子数量", type = "int")
    private String useDDQua;

    @Schema(name = "Vip消耗点子数量", type = "int")
    private String vipUseDDQua;

}
