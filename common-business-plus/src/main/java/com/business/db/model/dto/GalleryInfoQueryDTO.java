package com.business.db.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


@Data
@Schema(name = "画廊查询参数实体", description = "画廊查询参数实体")
public class GalleryInfoQueryDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(title = "用户id不可为空", type = "String")
    private Long userId;

    /**
     * 图片Id
     */
    @NotNull(message = "图片id不可为空！")
    private Long imgId;


}
