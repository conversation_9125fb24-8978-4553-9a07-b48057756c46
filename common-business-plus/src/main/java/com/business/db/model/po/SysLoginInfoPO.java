package com.business.db.model.po;


import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 登录信息表
 * <AUTHOR>
 * @date 2024-01-14
 *
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_login_info")
public class SysLoginInfoPO implements Serializable {

    @Schema(title = "主键")
    private Long id;

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "登录IP地址")
    private String ipAddr;

    @Schema(title = "登录地点")
    private String loginLocation;

    @Schema(title = "浏览器类型")
    private String browser;

    @Schema(title = "操作系统")
    private String app;

    @Schema(title = "登录状态（0成功 1失败）")
    private Integer status;

    @Schema(title = "提示消息")
    private String msg;

    @Schema(title = "访问时间 ")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date loginTime;


    public static SysLoginInfoPO buildSysLoginInfoAdd(Long userId, String browser, String app, String msg){
        SysLoginInfoPO sysLoginInfoPO = new SysLoginInfoPO();
        sysLoginInfoPO.id = IdWorker.getId();
        sysLoginInfoPO.userId = userId;
        sysLoginInfoPO.browser = browser;
        sysLoginInfoPO.app = app;
        sysLoginInfoPO.status = 0;
        sysLoginInfoPO.msg = msg;
        sysLoginInfoPO.loginTime = DateUtil.date();
        return sysLoginInfoPO;
    }

    public static SysLoginInfoPO buildSysLoginInfoUpdate(Long id, String browser, String app, String msg, Date loginTime){
        SysLoginInfoPO sysLoginInfoPO = new SysLoginInfoPO();
        sysLoginInfoPO.id = id;
        sysLoginInfoPO.browser = browser;
        sysLoginInfoPO.app = app;
        sysLoginInfoPO.status = 0;
        sysLoginInfoPO.msg = msg;
        sysLoginInfoPO.loginTime = loginTime;
        return sysLoginInfoPO;
    }

}
