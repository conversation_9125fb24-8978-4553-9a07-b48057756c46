package com.business.db.model.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @className: com.intelligent.bot.model.excel-> ExportExchange
 * @description: 兑换码导出
 * @author: Admin
 * @createDate: 2023-08-17 10:01
 * @version: 1.0
 */
@Data
public class ExportExchange implements Serializable {
    private static final long serialVersionUID = 1L;
    /**兑换编号**/
    @Excel(name = "兑换码", type = 1, height = 8, width = 25)
    private String code;
    /**展示名称**/
    @Excel(name = "兑换码名称", width = 15,height = 8, type = 1)
    private String name;
    /**兑换点子数量*/
    @Excel(name = "点子数量", type = 10, height = 8, width = 25)
    private Double accountNum;
    /**使用时间天**/
    @Excel(name = "有效天数", type = 10, height = 8, width = 25)
    private Integer useExpiryDay;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 21, height = 8, format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Override
    public String toString() {
        return "ExportExchange{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", accountNum=" + accountNum +
                ", createTime=" + createTime +
                '}';
    }
}
