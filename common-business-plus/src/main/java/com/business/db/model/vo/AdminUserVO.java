package com.business.db.model.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Schema(name = "写真主题（封面）分页数据", description = "写真主题（封面）分页数据")
public class AdminUserVO {

    @Schema(name = "主键id", type = "Long")
    private Long id;

    @Schema(name = "关联主题类型id", type = "Long")
    private Long topicTypeId;

    @Schema(name = "主题标题", type = "String")
    private String topicTitle;

    @Schema(name = "主题标题英文", type = "String")
    private String topicTitleEn;

    @Schema(name = "主题地址", type = "String")
    private String topicUrl;

    @Schema(name = "是否热门（0否 1是）", type = "Integer")
    private Integer isHot;

    @Schema(name = "图片类型", type = "String")
    private String imgType;

    @Schema(name = "变换宽度", type = "Integer")
    private Integer imgWidth;

    @Schema(name = "变换高度", type = "Integer")
    private Integer imgHeight;

    @Schema(name = "图片大小", type = "Long")
    private Long imgSize;

    @Schema(name = "图片主色调", type = "String")
    private String imgHue;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(name = "创建时间（默认为创建时服务器时间）", type = "Date")
    @JsonFormat(timezone = "Asia/Shanghai",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
