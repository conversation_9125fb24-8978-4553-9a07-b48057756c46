package com.business.db.model.dto.pay;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

@Schema(name = "微信h5支付参数实体", description = "微信h5支付参数实体")
@Data
@Validated
public class WxH5PayCourseDTO {

    @NotBlank(message = "订单编号不能为空")
    @Schema(name = "课程id", type = "Long")
    private Long coursesId;

    @Schema(name = "订单编号", type = "String")
    private String orderNo;

    @NotBlank(message = "用户code不能为空")
    @Schema(name = "用户code", type = "String")
    private String code;

}
