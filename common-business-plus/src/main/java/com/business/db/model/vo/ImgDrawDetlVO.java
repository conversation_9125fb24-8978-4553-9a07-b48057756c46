package com.business.db.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


@Data
@Schema(name = "绘图详情实体", description = "绘图详情实体")
public class ImgDrawDetlVO implements Serializable {

    @Schema(name = "绘图详情主键 ID", type = "Long")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(name = "绘图记录 ID", type = "Long")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drawRecordId;

    @Schema(name = "操作属性", type = "int")
    private Integer optAttribute;

    @Schema(name = "模型属性", type = "int")
    private Integer modeAttribute;

    @Schema(name = "图片索引位置", type = "int")
    private Integer imgIndex;

    @Schema(name = "源图片地址", type = "String")
    private String imgSourceUrl;

    @Schema(name = "本地存储地址", type = "String")
    private String imgUrl;

    @Schema(name = "宽高尺寸", type = "double")
    private Double whDivide;

    @Schema(name = "图片大小", type = "Long")
    private Long imgSize;

    @Schema(name = "图片宽", type = "int")
    private Integer imgWidth;

    @Schema(name = "图片高", type = "int")
    private Integer imgHeight;

    @Schema(name = "图片类型", type = "String")
    private String imgType;

    @Schema(name = "图片主色调", type = "String")
    private String imgHue;

    @Schema(name = "是否发布", type = "int")
    private Integer isPublish;

    @Schema(name = "音频Mp3 地址", type = "String")
    private String audioUrl;

    @Schema(name = "音频Mp4 地址", type = "String")
    private String videoUrl;

    public String getImgUrl() {
        if (imgUrl == null) {
            return null;
        }
        //校验渐显bate64直接返回
        if (imgUrl.contains("data:image")) {
            return imgUrl;
        }
        if (!imgUrl.contains("https")) {
            //装载前缀
            return CommonStrEnum.IMAGE_PREFIX.getValue() + imgUrl;
        }
        return imgUrl;
    }
}
