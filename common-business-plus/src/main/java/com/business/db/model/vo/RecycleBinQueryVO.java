package com.business.db.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @className: com.intelligent.bot.model.res.mj.RecycleBinQueryRes
 * @description: 回收站相关参数
 * @author: Yl
 * @createDate: 2023-09-04 09:37
 * @version: 1.0
 */
@Data
public class RecycleBinQueryVO {

    /**
     * 回收站id
     */
    private Long id;

    /**
     * 删除时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date deleteTime;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 图片id
     */
    private Long imageId;

    /**
     * 图片url
     */
    private String imageUrl;

    /**
     * 剩余天数
     */
    private Integer remainDays;

}
