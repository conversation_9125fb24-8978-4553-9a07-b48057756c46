package com.business.db.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_flow_record")
@Schema(name = "点子流水记录", description = "用户点子流水记录")
public class FlowRecordPO extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "用户ID", type = "Long")
    private Long userId;

    @Schema(name = "记录类型：0-增加、1-消耗", type = "Integer")
    private Integer recordType;

    @Schema(name = "操作类型：查看具体枚举", type = "Integer")
    private Integer operateType;

    @Schema(name = "点子数量", type = "Double")
    private Double num;

    @Schema(name = "备注", type = "String")
    private String remark;
}
