package com.business.db.model.dto.draw;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "MJ绘画风格配置", description = "MJ绘画风格配置表")
public class ImgDrawStyleConfigDTO extends BasePageHelper implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "id", type = "Long")
    private Long id;

    @Schema(name = "风格分类id", type = "Long")
    private Long classifyId;

    @Schema(name = "风格标签id‘", type = "Long")
    private Long labelId;

    @Schema(name = "风格名称", type = "String")
    private String styleName;

    @Schema(name = "风格码", type = "String")
    private String styleCode;

    @Schema(name = "使用次数", type = "Integer")
    private Integer useCount;

    @Schema(name = "是否使用", type = "Integer")
    private Integer isUse;

    @Schema(name = "（使用次数排序 0：升序，1：倒序）", type = "Long")
    private Integer sort;

    @Schema(name = "置顶（0：不置顶，1：置顶）", type = "Long")
    private Integer pinned;

    @Schema(name = "是否推荐", type = "Integer")
    private Integer isRecommend;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(name = "创建时间（默认为创建时服务器时间）", type = "Date")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
