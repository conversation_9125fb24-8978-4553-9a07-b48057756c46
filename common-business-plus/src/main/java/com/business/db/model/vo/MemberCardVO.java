package com.business.db.model.vo;

import lombok.Data;

/**
 * 充值会员实体  前端展示
 */

@Data
public class MemberCardVO {

    /**
     * 用户名
     */
    private String name;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 支付方式
     */
    private Double paymentMethod;

    /**
     * 消息实体
     */
    private String message;


    /**
     *
     */
    public static MemberCardVO build(String name, String avatar, String levelName, Double paymentMethod, String message) {
        MemberCardVO memberCardVO = new MemberCardVO();
        memberCardVO.setName(name);
        memberCardVO.setAvatar(avatar);
        memberCardVO.setLevelName(levelName);
        memberCardVO.setPaymentMethod(paymentMethod);
        memberCardVO.setMessage(message);
        return memberCardVO;
    }



}
