package com.business.db.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(name = "用户点点数量记录前端返回", description = "用户点点数量记录前端返回")
public class UserDDRecordVO extends BaseEntity {

    @Schema(name = "用户userid", type = "Long")
    private Long userId;

    @Schema(name = "操作源id", type = "Long")
    private Long sourceId;

    @Schema(name = "类型", type = "Integer")
    private Integer type;

    @Schema(name = "子类型", type = "Integer")
    private Integer typeItem;

    @Schema(name = "总量", type = "BigDecimal")
    private Double total;

    @Schema(name = "总使用量", type = "BigDecimal")
    private Double totalUsage;

    @Schema(name = "到期时间", type = "Date")
    @JsonFormat(timezone = "Asia/Shanghai", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expirationTime;

    @Schema(name = "使用状态：1使用中，2已过期, 0已用完", type = "Integer")
    private Integer state;

}
