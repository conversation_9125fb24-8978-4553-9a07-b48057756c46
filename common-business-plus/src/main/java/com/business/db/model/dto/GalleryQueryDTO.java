package com.business.db.model.dto;

import com.nacos.base.BasePageHelper;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(title = "收藏、点赞参数实体")
public class GalleryQueryDTO extends BasePageHelper implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(title = "提示词--模糊查询")
    private String prompt;

    @Schema(title = "排序条件")
    private String orderBy;

    @Schema(title = "用户id")
    @NotNull(message = "用户id不可为空！")
    private Long userId;

    @Schema(title = "任务类型 1收藏 2 点赞")
    private Integer jobType;

    @Schema(title = "是否公开")
    private Integer isHimself;

    @Schema(title = "绘画者Id")
    private Long painterId;

    @Schema(title = "是否分享")
    private Integer isShare;

    @Schema(title = "图片标签id")
    private Long imageTagId;

    @Schema(title = "图片前缀")
    private String prefixUrl = CommonStrEnum.IMAGE_PREFIX.getValue();

    @Schema(title = "兼容版本（不传为老版本，传了为新版本）")
    private String version = null;

}
