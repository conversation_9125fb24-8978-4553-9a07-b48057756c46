package com.business.db.model.po.user;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * IP黑名单实体类
 */
@Schema(description = "IP黑名单实体类")
@TableName("ip_blacklist")
@Data
public class BlacklistIPPO {
    
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;
    
    /**
     * IP地址
     */
    @Schema(description = "IP地址")
    private String ip;
    
    /**
     * 加入黑名单原因
     */
    @Schema(description = "加入黑名单原因")
    private String reason;
    
    /**
     * 加入黑名单时间
     */
    @Schema(description = "加入黑名单时间")
    private Date createTime;
    
    /**
     * 黑名单到期时间
     */
    @Schema(description = "黑名单到期时间")
    private Date expireTime;
    
    /**
     * 黑名单状态：1-有效，0-已过期
     */
    @Schema(description = "黑名单状态：1-有效，0-已过期")
    private Integer status;
    
    /**
     * 违规次数
     */
    @Schema(description = "违规次数")
    private Integer violationCount;
    
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
} 