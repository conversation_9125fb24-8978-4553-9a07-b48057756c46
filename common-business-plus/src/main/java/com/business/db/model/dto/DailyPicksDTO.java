package com.business.db.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Schema(name = "每日精选参数", description = "每日精选参数实体")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DailyPicksDTO extends BasePageHelper implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "用户id", type = "Long")
    private Long userId;

    @Schema(name = "登录人id", type = "Long")
    private Long loginUserId;

    @Schema(name = "当前日期", type = "Long")
    private Date createDate;

    @Schema(name = "精选类型", type = "Long")
    private Integer picksType;
}
