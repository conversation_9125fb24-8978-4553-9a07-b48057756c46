package com.business.db.model.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * sys 系统---互动实体类
 * <AUTHOR>
 * @version: 1.0
 */
@Data
public class SysInteractionVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 326308725675949330L;

    @Schema(name = "id", type = "Long")
    private Long id;

    @Schema(name = "用户id", type = "Long")
    private Long userId;

    @Schema(name = "目标用户id", type = "Long")
    private Long targUserId;

    @Schema(name = "用户名称", type = "String")
    private String name;

    @Schema(name = "用户头像", type = "String")
    private String avatar;

    @Schema(name = "任务ID", type = "Long")
    private Long taskId;

    @Schema(name = "图片ID", type = "Long")
    private Long imgId;

    @Schema(name = "图片地址", type = "String")
    private String imgUrl;

    @Schema(name = "是否已读（0未读， 1已读）", type = "Integer")
    private Integer isRead;

    @Schema(name = "是否关注 （0没关注，1已关注）", type = "Integer")
    private Integer isFocus;

    @Schema(name = "互动标题", type = "String")
    private String interactTitle;

    @Schema(name = "互动类型 （0喜欢， 1收藏，2是关注）", type = "Integer")
    private Integer interactType;

    @Schema(name = "显示时间", type = "Date")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date showTime;

    @Schema(name = "显示时间字符串", type = "String")
    private String showTimeStr;

    public String getImgUrl() {
        if (imgUrl != null && !imgUrl.contains("https")) {
            //装载前缀
            return CommonStrEnum.IMAGE_PREFIX.getValue() + imgUrl;
        }
        return imgUrl;
    }

}
