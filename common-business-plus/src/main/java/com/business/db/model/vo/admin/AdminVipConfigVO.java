package com.business.db.model.vo.admin;


import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("vip_config")
@Schema(name="vip配置信息实体", description="vip配置信息实体")
public class AdminVipConfigVO extends BaseEntity {

    @Schema(name = "主键id", type = "Long")
    private Long id;

    @Schema(name = "方位（0左上 1 右上 2左下 3右下 4居中）", type = "Integer")
    private String direction;

    @Schema(name = "标签", type = "String")
    private String tag;

    @Schema(name = "标签颜色", type = "String")
    private String tagColor;

    @Schema(name = "等级名称", type = "String")
    private String title;

    @Schema(name = "等级,分类", type = "Integer")
    private Integer type;

    @Schema(name = "排序", type = "Integer")
    private Integer sort;

    @Schema(name = "原价", type = "BigDecimal")
    private BigDecimal originalPrice;

    @Schema(name = "现价", type = "BigDecimal")
    private BigDecimal currentPrice;

    @Schema(name = "节省金额", type = "BigDecimal")
    private BigDecimal saveAmount;

    @Schema(name = "购买月数量", type = "Integer")
    private Integer month;

    @Schema(name = "每月点子数量", type = "Double")
    private Double originalMonthQuantity;

    @Schema(name = "实际每月点子数量", type = "Double")
    private Double currentMonthQuantity;

    @Schema(name = "单次使用成本", type = "Double")
    private Double costPerUse;

    @Schema(name = "是否可复购", type = "Integer")
    private Integer repurchaseFlag;

    @Schema(name = "会员内容介绍", type = "String")
    private String details;

    @Schema(name = "是否显示", type = "Integer")
    private Integer isShow;
}
