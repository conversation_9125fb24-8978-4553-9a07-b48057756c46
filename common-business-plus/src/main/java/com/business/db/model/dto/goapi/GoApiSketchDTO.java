package com.business.db.model.dto.goapi;

import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 提交风格接收参数对象
 */
@Data
public class GoApiSketchDTO extends BasePageHelper {

    @Schema(title = "用户id")
    private Long userId;

    @Schema(title = "当前操作id")
    @NotNull(message = "操作id不能为空")
    private Integer operate;

    @Schema(title = "图片url")
    @NotNull(message = "图片路径不能为空")
    private String imgUrl;

    @Schema(title = "图片宽")
    @NotNull(message = "图片宽不能为空")
    private Integer imgWidth;

    @Schema(title = "图片高")
    @NotNull(message = "高不能为空")
    private Integer imgHeight;

    // le 重绘参数
    @Schema(title = "高档风格")
    private String upscalerStyle;

    @Schema(title = "创意”程度, 越高细节就越多")
    private Integer creativityStrength;

    @Schema(title = "必须在1和2之间")
    private Float upscaleMultiplier;

}
