package com.business.db.model.vo;

import com.business.enums.DailySignTaskEnum;
import com.business.utils.BStringUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(name = "InviteInfoVO", description = "用户邀请显示信息")
public class InviteInfoVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "用户ID", type = "Long")
    private Long userId;

    @Schema(name = "点点所有总数量", type = "Double")
    private Double ddAllQuantity;

    @Schema(name = "邀请总人数", type = "Integer")
    private Integer manAllQuantity;

    @Schema(name = "有效邀请人数", type = "Integer")
    private Integer manQuantity;

    @Schema(name = "点点单次赠送数量", type = "Double")
    private Double ddGiveQuantity;

    @Schema(name = "邀请码", type = "String")
    private String code;

    @Schema(name = "邀请规则", type = "String")
    private String giveRule;

    @Schema(name = "总奖励", type = "String")
    private String giveSvipAllNum;

    @Schema(name = "邀请码链接", type = "String")
    private String codeUrl;

    public InviteInfoVO(Long userId) {
        this.userId = userId;
        this.ddAllQuantity = (double) 0;
        this.manAllQuantity = 0;
        this.manQuantity = 0;
        this.ddGiveQuantity = (double) 0;
    }

    public String getDdAllQuantity() {
        return BStringUtil.doubleToString(new BigDecimal(manQuantity.toString()).multiply(new BigDecimal(ddGiveQuantity.toString())).doubleValue());
    }

    public String getDdGiveQuantity() {
        return BStringUtil.doubleToString(ddGiveQuantity);
    }

    public String getGiveRule() {
        return DailySignTaskEnum.INVITE_RULE_DESCRIPTION.getDescription();
    }

    public String getGiveSvipAllNum() {
        return DailySignTaskEnum.INVITE_GIVE_SVIP_ALL_NUM.getDescription().replace("${giveSvipAllNum}", giveSvipAllNum==null ? "0" : giveSvipAllNum);
    }
}
