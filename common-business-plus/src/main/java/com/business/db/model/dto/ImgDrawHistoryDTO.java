package com.business.db.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;



@EqualsAndHashCode(callSuper = true)
@Schema(name = "绘图历史记录参数实体", description = "绘图历史记录参数实体")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ImgDrawHistoryDTO extends BasePageHelper {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "语言id", type = "Long")
    private Long languageTagId;

    @Schema(name = "功能类型（1绘图；2写真；3视频；4音频）", type = "Integer")
    private Integer funType;

}
