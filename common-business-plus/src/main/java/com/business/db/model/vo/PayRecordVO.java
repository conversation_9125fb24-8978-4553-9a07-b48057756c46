package com.business.db.model.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(name = "支付记录返回实体", description = "支付记录返回实体")
public class PayRecordVO {

    @Schema(name = "用户ID", type = "Long")
    private Long userId;

    @Schema(name = "套餐名称", type = "String")
    private String setMealName;

    @Schema(name = "用户支付订单编号", type = "String")
    private String orderNo;

    @Schema(name = "订单类型：1加油包；2VIP；3SVIP", type = "Integer")
    private Integer orderType;

    @Schema(name = "支付类型", type = "Integer")
    private Integer type;

    @Schema(name = "实际支付金额", type = "BigDecimal")
    private BigDecimal amount;

    @Schema(name = "总量", type = "Double")
    private Double total;

    @Schema(name = "总使用量", type = "Double")
    private Double totalUsage;

    @Schema(name = "会员月数量", type = "Integer")
    private Integer month;

    @Schema(name = "每月更新数量", type = "Double")
    private Double monthQuantity;

    @JsonFormat(timezone = "Asia/Shanghai",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(name = "到期时间", type = "Date")
    private String expirationTime;

    @Schema(name = "支付状态：0未使用；1使用中；2已用完；3已失效", type = "Integer")
    private Integer useState;

    @Schema(name = "创建时间（默认为创建时服务器时间）", type = "Date")
    @JsonFormat(timezone = "Asia/Shanghai",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    public PayRecordVO() {
    }

    public PayRecordVO(String setMealName, Long userId, String orderNo, Integer orderType, Integer type, BigDecimal amount, Double total, Double totalUsage, Integer month, Double monthQuantity, String expirationTime, Integer useState, Date createTime) {
        this.setMealName = setMealName;
        this.userId = userId;
        this.orderNo = orderNo;
        this.orderType = orderType;
        this.type = type;
        this.amount = amount;
        this.total = total;
        this.totalUsage = totalUsage;
        this.month = month;
        this.monthQuantity = monthQuantity;
        this.expirationTime = expirationTime;
        this.useState = useState;
        this.createTime = createTime;
    }
}
