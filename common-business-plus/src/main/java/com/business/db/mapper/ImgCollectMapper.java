package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.po.ImgCollectPO;
import org.apache.ibatis.annotations.Param;

/**
 * @description: 图片收藏
 * @author: Admin
 * @version: 1.0
 */
public interface ImgCollectMapper extends BaseMapper<ImgCollectPO> {
    /**
     * 取消收藏删除关联数据
     * @param taskId
     * @param imgId
     * @param userId
     * @return
     */
    int deleteCollect(@Param("taskId") Long taskId,
                      @Param("imgId") Long imgId,
                      @Param("userId") Long userId);
}
