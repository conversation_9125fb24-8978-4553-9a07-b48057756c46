package com.business.db.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.bo.EveryDayDrawBO;
import com.business.db.model.dto.CommunityQueryDTO;
import com.business.db.model.dto.PhotoHistoryDTO;
import com.business.db.model.po.admin.AdminUserDrawBO;
import com.business.db.model.vo.GuidePageVO;
import com.business.db.model.vo.ImgCommunityVO;
import com.business.model.po.ImgDrawRecordPO;
import com.business.db.model.vo.*;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * &#064;description:  画图主任务
 */
public interface ImgDrawRecordMapper extends BaseMapper<ImgDrawRecordPO> {

    Integer updateByIds(@Param("drawIds") List<Long> drawIds);

//    Page<ImgCommunityVO> queryHomePage(Page<ImgCommunityVO> page, @Param("dto") CommunityQueryDTO dto);
    List<ImgCommunityVO> queryHomeList(@Param("dto") CommunityQueryDTO dto);
    Long queryHomeListTotal(@Param("dto") CommunityQueryDTO dto);

    List<GuidePageVO> guidePageList();

    //查询昨日绘图数量统计
    List<EveryDayDrawBO> selectDrawQuantityYesterdayList();

    //上周绘图数量统计
    List<EveryDayDrawBO> selectDrawQuantityWeekList();

    //上月绘图数量
    List<EveryDayDrawBO> selectDrawQuantityMonthList();

    Page<PhotoHistoryVO> queryPhotoHistoryPage(Page<PhotoHistoryVO> page, @Param("dto") PhotoHistoryDTO dto);

    PhotoHistoryVO selectPhotoTaskOne(@Param("taskId") Long taskId);

    List<ImgDrawRecordPO> selectImgDrawRecordPOList();

    List<AdminUserDrawBO> selectUserCasualModleStatList(int maximum);

    ImgDrawRecordPO selectListByDetlId(@Param("imgDrawDetlId") Long imgDrawDetlId);
}
