package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.po.ImgLikePO;
import org.apache.ibatis.annotations.Param;

/**
 * @className: com.intelligent.bot.dao-> TaskLikeDao
 * @description: 图片点赞
 * @author: Admin
 * @createDate: 2023-07-31 16:42
 * @version: 1.0
 */
public interface ImgLikeMapper extends BaseMapper<ImgLikePO> {
    /**
     * 取消点赞删除关联数据
     * @param taskId
     * @param imgId
     * @param userId
     * @return
     */
    int deleteLike(@Param("taskId") Long taskId,
                   @Param("imgId") Long imgId,
                   @Param("userId") Long userId);
}
