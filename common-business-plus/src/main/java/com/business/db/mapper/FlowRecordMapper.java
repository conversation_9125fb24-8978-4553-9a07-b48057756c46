package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.FlowRecordDTO;
import com.business.db.model.po.FlowRecordPO;
import com.business.db.model.vo.FlowRecordVO;
import org.apache.ibatis.annotations.Param;

/**
 * @className: com.intelligent.bot.dao-> FlowRecordDao
 * @description: 点子流水记录
 * @author: Admin
 * @createDate: 2023-08-14 14:45
 * @version: 1.0
 */
public interface FlowRecordMapper extends BaseMapper<FlowRecordPO> {
    /**
     * 查询点子消耗记录
     * @param page
     * @param dto
     * @return
     */
    IPage<FlowRecordVO> selectMyRecord(Page<FlowRecordVO> page, @Param("req") FlowRecordDTO dto);

    Integer deleteFlowRecord(@Param("userId") Long userId);
}
