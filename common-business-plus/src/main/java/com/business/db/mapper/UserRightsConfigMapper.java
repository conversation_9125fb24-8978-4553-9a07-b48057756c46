package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.po.DDExpirationPO;
import com.business.db.model.po.UserDDRecordPO;
import com.business.db.model.po.UserRightsConfigPO;
import com.business.db.model.vo.UserRightsConfigVO;

import java.util.List;

public interface UserRightsConfigMapper extends BaseMapper<UserRightsConfigPO> {

    List<UserRightsConfigVO> selectUserRightsConfigList();

}
