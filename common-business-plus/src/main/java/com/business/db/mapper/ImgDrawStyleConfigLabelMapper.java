package com.business.db.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.po.draw.ImgDrawStyleConfigLabelPO;
import com.business.db.model.po.draw.ImgDrawStyleLabelPO;
import com.business.db.model.vo.draw.ImgDrawStyleLabelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ImgDrawStyleConfigLabelMapper extends BaseMapper<ImgDrawStyleConfigLabelPO> {

    Integer insertBatch(List<ImgDrawStyleConfigLabelPO> list);

    List<ImgDrawStyleLabelPO> selectList(@Param("configId") Long configId);

    List<Long> selectStyleConfigIdsByLabelId(@Param("labelId") Long labelId);

    List<ImgDrawStyleLabelVO> selectByStyleConfigIds(@Param("styleConfigIds") List<Long> styleConfigIds);

    Long deleteByStyleConfigId(@Param("styleConfigId") Long styleConfigId);

    List<ImgDrawStyleLabelPO> selectListByIds();
}
