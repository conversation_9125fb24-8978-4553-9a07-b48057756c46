package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.PhotoTopicSceneDTO;
import com.business.db.model.po.PhotoTopicScenePO;
import com.business.db.model.vo.PhotoTopicSceneVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 写真场景
 * <AUTHOR> @Data
 */
public interface PhotoTopicSceneMapper extends BaseMapper<PhotoTopicScenePO> {

    Page<PhotoTopicSceneVO> queryPage(Page<PhotoTopicSceneVO> page, @Param("dto") PhotoTopicSceneDTO dto);


    List<PhotoTopicSceneVO> selectPhotoTopicScenes(@Param("dto") PhotoTopicSceneDTO dto);

    List<PhotoTopicSceneVO> selectPhotoTopicScenesByTopicIds(@Param("topicIds") List<Long> topicIds);

    int delPhotoTopicScenesByTopicId(@Param("topicId") Long topicId);
}
