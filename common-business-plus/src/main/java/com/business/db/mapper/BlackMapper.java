package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.BlackQueryDTO;
import com.business.db.model.po.BlacklistPO;
import com.business.db.model.vo.BlacklistVO;
import org.apache.ibatis.annotations.Param;
/**
 * 渠道分类（渠道类别）
 * @className: SemChannelSortDao
 * @author: Admin
 * @createDate: 2023-10-12
 * @version: 1.4
 *
 */
public interface BlackMapper extends BaseMapper<BlacklistPO> {


    Page<BlacklistVO> queryPage(Page<BlacklistVO> page, @Param("dto") BlackQueryDTO dto);
}
