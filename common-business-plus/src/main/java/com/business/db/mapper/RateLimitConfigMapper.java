package com.business.db.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.business.db.model.po.user.RateLimitConfigPO;

import java.util.List;

/**
 * 限流配置Mapper接口
 */
@Mapper
public interface RateLimitConfigMapper {
    
    /**
     * 获取所有启用的限流配置
     * @return 限流配置列表
     */
    @Select("SELECT * FROM rate_limit_config WHERE status = 1")
    List<RateLimitConfigPO> findAllEnabled();
    
    /**
     * 根据配置键获取配置
     * @param configKey 配置键
     * @return 限流配置
     */
    @Select("SELECT * FROM rate_limit_config WHERE config_key = #{configKey} LIMIT 1")
    RateLimitConfigPO findByKey(@Param("configKey") String configKey);
    
    /**
     * 更新配置值
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 影响行数
     */
    @Update("UPDATE rate_limit_config SET config_value = #{configValue}, update_time = NOW() WHERE config_key = #{configKey}")
    int updateValue(@Param("configKey") String configKey, @Param("configValue") Integer configValue);
} 