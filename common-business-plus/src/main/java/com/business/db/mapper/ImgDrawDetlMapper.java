package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.bo.ImgDrawReportBO;
import com.business.db.model.dto.DailyPicksDTO;
import com.business.db.model.dto.GalleryInfoQueryDTO;
import com.business.db.model.dto.GalleryQueryDTO;
import com.business.db.model.po.ImgDrawDetlPO;
import com.business.db.model.po.ImgDrawDetlUpdatePO;
import com.business.db.model.vo.DailyPicksVO;
import com.business.db.model.vo.ImgCommunityVO;
import com.business.db.model.vo.ImgGalleryVO;
import com.business.db.model.vo.PhotoHistoryVO;
import com.business.model.bo.AudioOngoingStatusBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ImgDrawDetlMapper extends BaseMapper<ImgDrawDetlPO> {

    Integer updateByIds(@Param("list") List<Long> list);

    Integer updateBatchById(@Param("list") List<ImgDrawDetlUpdatePO> list);

    PhotoHistoryVO selectPhotoTaskOne(@Param("imgDrawId") Long imgDrawId);

    // 查询画廊列表：app 2.0 画廊查询
    Page<ImgGalleryVO> queryGalleryPage(Page<ImgGalleryVO> page, @Param("dto") GalleryQueryDTO dto);

    ImgCommunityVO queryGalleryByImgId(@Param("dto") GalleryInfoQueryDTO dto);

    Page<DailyPicksVO> dailyPicksPage(Page<DailyPicksVO> page, @Param("dto") DailyPicksDTO dto);


    //进行中音频job
    List<AudioOngoingStatusBO> selectAudioOngoingStatus();

    ImgDrawReportBO selectByDrawDetlId(@Param("imgDrawId") Long imgDrawId);

}
