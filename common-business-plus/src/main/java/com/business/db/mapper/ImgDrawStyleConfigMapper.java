package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.po.draw.ImgDrawStyleConfigPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ImgDrawStyleConfigMapper extends BaseMapper<ImgDrawStyleConfigPO> {

    List<String> selectByIds(@Param("ids") List<Long> ids);

    Integer batchUpdate(@Param("ids") List<Long> ids);

    /**
     *
     * @param classifyId
     * @param labelId
     * @param isRecommend （0，默认，1，推荐）
     * @return
     */
    List<ImgDrawStyleConfigPO> selectStyleConfigIdsByClassifyIdAndLabelId(@Param("classifyId") Long classifyId,@Param("labelId") Long labelId,@Param("isRecommend") Integer isRecommend  );


}
