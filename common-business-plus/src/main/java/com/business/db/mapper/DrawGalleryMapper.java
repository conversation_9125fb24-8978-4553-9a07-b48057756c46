package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.GalleryQueryDTO;
import com.business.db.model.dto.GalleryQueryNewDTO;
import com.business.db.model.dto.ImgInfoQueryDTO;
import com.business.db.model.po.GalleryPO;
import com.business.db.model.vo.GalleryQueryVO;
import com.business.db.model.vo.ImgGalleryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @className: com.intelligent.bot.dao-> GalleryDao
 * @description: 画廊相关
 * @author: Admin
 * @createDate: 2023-06-19 10:20
 * @version: 1.0
 */
public interface DrawGalleryMapper extends BaseMapper<GalleryPO> {

    /**
     * 查询首页画廊列表
     * @param page
     * @return
     */
    Page<GalleryQueryVO> queryPage(Page<GalleryQueryVO> page, @Param("dto") GalleryQueryDTO dto);

    /**
     * 根据user、imgid查找图片信息
     * @param dto
     * @return
     */
    GalleryQueryVO queryGalleryByImgId(@Param("dto") ImgInfoQueryDTO dto);

    /**
     * 查询我的收藏列表
     * @param page
     * @param dto
     * @return
     */
    Page<GalleryQueryVO> queryMyCollectPage(Page<GalleryQueryVO> page, @Param("dto") GalleryQueryDTO dto);

    /**
     * 查询我的点赞列表
     * @param page
     * @param dto
     * @return
     */
    Page<GalleryQueryVO> queryMyLikePage(Page<GalleryQueryVO> page, @Param("dto") GalleryQueryDTO dto);

    /**
     * 查询个人画廊列表
     * @param page
     * @param dto
     * @return
     */
    Page<GalleryQueryVO> queryMyGalleryPage(Page<GalleryQueryVO> page, @Param("dto") GalleryQueryDTO dto);

    /**
     * 查询个人画廊列表 new
     * @param page
     * @param dto
     * @return
     */
    Page<GalleryQueryVO> queryMyGalleryNewPage(Page<GalleryQueryVO> page, @Param("dto") GalleryQueryNewDTO dto);

    /**
     * 修改画廊点赞数量
     * @param taskId
     * @param imgId
     * @param num
     * @return
     */
    int updateGoodNum(@Param("taskId") Long taskId, @Param("imgId") Long imgId, @Param("num") int num);

    /**
     * 修改画廊收藏数量
     * @param taskId
     * @param imgId
     * @param num
     * @return
     */
    int updateCollectNum(@Param("taskId") Long taskId, @Param("imgId") Long imgId, @Param("num") int num);



    /**
     * 批量删除我的画廊
     * @param list
     * @return
     */
    int batchDelMyGallery(@Param("deleted") Integer deleted, @Param("list") List<Long> list);

    /**
     * 查询画廊列表：app 2.0 画廊查询
     * @param page
     * @param dto 参数
     * @return
     */
    Page<ImgGalleryVO> queryGalleryPage(Page<ImgGalleryVO> page, @Param("dto") GalleryQueryNewDTO dto);

    /**
     * 查询画廊列表：app 2.0 收藏列表
     * @param page
     * @param dto 参数
     * @return
     */
    Page<ImgGalleryVO> queryCollectPage(Page<ImgGalleryVO> page, @Param("dto") GalleryQueryDTO dto);

    /**
     * 查询画廊列表：app 2.0 点赞列表
     * @param page
     * @param dto 参数
     * @return
     */
    Page<ImgGalleryVO> queryLikePage(Page<ImgGalleryVO> page, @Param("dto") GalleryQueryDTO dto);

}
