package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.po.DDExpirationPO;
import com.business.db.model.po.KnowledgeSpacePO;
import com.business.db.model.po.UserDDRecordPO;

import java.util.List;

public interface KnowledgeSpaceMapper extends BaseMapper<KnowledgeSpacePO> {

    List<KnowledgeSpacePO> selectExpirationByDays(Integer days);

    Double selectUserTotal(Long userId);

}
