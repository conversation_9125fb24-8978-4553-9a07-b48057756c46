package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.SysInteractionQueryDTO;
import com.business.db.model.po.SysInteractionPO;
import com.business.db.model.vo.SysInteractionVO;
import io.lettuce.core.dynamic.annotation.Param;

/**
 * 互动消息
 * @className: SysInteractionMapper
 * @author: Myl
 * @createDate: 2023-12-21
 * @version: 1.4
 *
 */
public interface SysInteractionMapper extends BaseMapper<SysInteractionPO> {


    Page<SysInteractionVO> queryPage(Page<SysInteractionVO> page, @Param("dto") SysInteractionQueryDTO dto);

    boolean updateRead();
}
