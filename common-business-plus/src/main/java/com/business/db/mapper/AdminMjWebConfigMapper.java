package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.model.bo.AdminMjWebConfigBO;
import com.business.model.po.AdminMjAccountConfigPO;
import com.business.model.po.AdminMjWebConfigPO;

import java.util.List;


/**
 * mj web 账号 配置
 */
public interface AdminMjWebConfigMapper extends BaseMapper<AdminMjWebConfigPO> {

    List<AdminMjWebConfigBO> selectAdminMjWebConfigBOList();

    AdminMjWebConfigBO selectAdminMjWebConfigBO(Long id);

    AdminMjWebConfigBO selectAdminMjWebConfigBORand();

}
