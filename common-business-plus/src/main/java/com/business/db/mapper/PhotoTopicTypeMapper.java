package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.PhotoTopicTypeDTO;
import com.business.db.model.po.PhotoTopicTypePO;
import com.business.db.model.vo.PhotoTopicTypeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 写真类型
 * <AUTHOR> @Data
 */
public interface PhotoTopicTypeMapper extends BaseMapper<PhotoTopicTypePO> {

    Page<PhotoTopicTypeVO> queryPage(Page<PhotoTopicTypeVO> page, @Param("dto") PhotoTopicTypeDTO dto);

    List<PhotoTopicTypeVO> selectPhotoTopicTypes(@Param("languageTagId") Long languageTagId, @Param("parentId") Long parentId);
}
