package com.business.db.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.AdminUserLoginDTO;
import com.business.db.model.po.admin.AdminUserPO;
import com.business.db.model.vo.AdminUserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AdminUserMapper extends BaseMapper<AdminUserPO> {

    Page<AdminUserVO> queryPage(Page<AdminUserVO> page, @Param("dto") AdminUserLoginDTO dto);

}
