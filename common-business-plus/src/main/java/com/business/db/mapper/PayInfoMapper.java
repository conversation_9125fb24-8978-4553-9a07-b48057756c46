package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.po.PayInfoPO;
import com.business.db.model.vo.MemberCardVO;
import com.business.db.model.vo.PayInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface PayInfoMapper extends BaseMapper<PayInfoPO> {

    List<MemberCardVO> queryMemberCardList();

    Integer queryUserConcurrency(@Param("userId") Long userId);

    List<PayInfoVO> queryMemberExpireTime(@Param("userId") Long userId);

    Integer deletePayInfo(@Param("userId") Long userId);

    /* 查询用户的有效期*/
    Date queryPayExpirationTime(@Param("userId") Long userId);


}
