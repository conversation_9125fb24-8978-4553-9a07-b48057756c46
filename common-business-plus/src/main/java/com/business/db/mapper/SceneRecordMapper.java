package com.business.db.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.text.SceneRecordQueryDTO;
import com.business.db.model.po.SceneRecordPO;
import com.business.db.model.vo.text.SceneRecordQueryVO;
import org.apache.ibatis.annotations.Param;

public interface SceneRecordMapper extends BaseMapper<SceneRecordPO> {

    /**
     * 查询用户文案
     * @param page
     * @param dto
     * @return
     */
    Page<SceneRecordQueryVO> querySceneRecordPage(@Param("page") Page<SceneRecordQueryVO> page,
                                                  @Param("dto") SceneRecordQueryDTO dto);

    Page<SceneRecordQueryVO> querySceneRecordsBySceneId(@Param("page") Page<SceneRecordQueryVO> page,
                                                        @Param("dto") SceneRecordQueryDTO dto);
}
