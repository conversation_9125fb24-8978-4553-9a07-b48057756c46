package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.bo.PayRecordBO;
import com.business.db.model.po.PayRecordPO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface PayRecordMapper extends BaseMapper<PayRecordPO> {

    /**
     * 查询用户等级(支付订单)
     * @param userId 用户id (使用时注意判断null设置为 1)
     * @return 返回等级
     */
    Integer queryUserGrade(Long userId);

    List<PayRecordBO> queryExpirationTime(@Param("timeFiveBeforeExpiration") String timeFiveBeforeExpiration, @Param("timeOneBeforeExpiration") String timeOneBeforeExpiration);

    Long selectGiveSvipCount(@Param("userId") Long userId,
                             @Param("firstDayOfMonth") LocalDate firstDayOfMonth,
                             @Param("lastDayOfMonth") LocalDate lastDayOfMonth);

}
