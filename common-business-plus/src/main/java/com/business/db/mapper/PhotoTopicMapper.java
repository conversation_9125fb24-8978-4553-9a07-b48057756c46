package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.PhotoTopicDTO;
import com.business.db.model.po.PhotoTopicPO;
import com.business.db.model.vo.PhotoTopicVO;
import org.apache.ibatis.annotations.Param;


/**
 * 写真主题
 * <AUTHOR> @Data
 */
public interface PhotoTopicMapper extends BaseMapper<PhotoTopicPO> {

    Page<PhotoTopicVO> queryPage(Page<PhotoTopicVO> page, @Param("dto") PhotoTopicDTO dto);

    Page<PhotoTopicVO> queryPhotoTopicPage(Page<PhotoTopicVO> page, @Param("dto") PhotoTopicDTO dto);

    PhotoTopicVO queryPhotoTopicPage(@Param("dto") PhotoTopicDTO dto);

}
