package com.business.db.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.po.draw.ImgDrawStyleClassifyLabelPO;
import com.business.db.model.po.draw.ImgDrawStyleClassifyPO;
import com.business.db.model.po.draw.ImgDrawStyleConfigClassifyPO;
import com.business.db.model.vo.draw.ImgDrawStyleClassifyVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ImgDrawStyleConfigClassifyMapper extends BaseMapper<ImgDrawStyleConfigClassifyPO> {

    Integer insertBatch(List<ImgDrawStyleConfigClassifyPO> list);

    List<ImgDrawStyleClassifyPO> selectList(@Param("configId") Long configId);

    List<Long> selectStyleConfigIdsByClassifyId(@Param("classifyId") Long classifyId);

    List<ImgDrawStyleClassifyVO> selectByStyleConfigIds(@Param("styleConfigIds") List<Long> styleConfigIds);

    Long deleteByStyleConfigId(@Param("styleConfigId") Long styleConfigId);


    List<ImgDrawStyleClassifyPO> selectListByIds();

    List<ImgDrawStyleClassifyLabelPO> selectClassifyLabelLists();
}
