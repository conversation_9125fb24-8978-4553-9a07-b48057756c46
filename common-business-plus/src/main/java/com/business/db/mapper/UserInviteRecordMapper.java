package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.po.UserInviteRecordPO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

public interface UserInviteRecordMapper extends BaseMapper<UserInviteRecordPO> {

    Long selectCountByMonth(@Param("userId") Long userId,
                         @Param("firstDayOfMonth") LocalDate firstDayOfMonth,
                         @Param("lastDayOfMonth") LocalDate lastDayOfMonth);

    Long selectCount(@Param("userId") Long userId,
                     @Param("firstDayOfMonth") LocalDate firstDayOfMonth,
                     @Param("lastDayOfMonth") LocalDate lastDayOfMonth);

}
