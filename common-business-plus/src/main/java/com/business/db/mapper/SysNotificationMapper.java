package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.business.db.model.dto.SysNotificationQueryDTO;
import com.business.db.model.po.SysNotificationPO;
import com.business.db.model.vo.SysActivityNotificationVO;
import com.business.db.model.vo.SysNotificationVO;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * 通知消息
 * @className: SysInteractionMapper
 * @author: Myl
 * @createDate: 2023-12-21
 * @version: 1.4
 *
 */
public interface SysNotificationMapper extends BaseMapper<SysNotificationPO> {

    Page<SysNotificationVO> queryPage(Page<SysNotificationVO> page, @Param("dto") SysNotificationQueryDTO dto);

    Page<SysActivityNotificationVO> queryActivityPage(Page<SysNotificationVO> page, @Param("dto") SysNotificationQueryDTO dto);

    boolean updateRead();

    Long selectUnreadNotification(@Param("userId") Long userId);

    List<SysNotificationPO> selectUnreadNotificationList(@Param("userId") Long userId, @Param("messageId") Long messageId);


}
