
package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.FansOrCollectQueryDTO;
import com.business.db.model.dto.admin.UserQueryPageDTO;
import com.business.db.model.po.UserPO;
import com.business.db.model.po.admin.OpeStatisticsUserPO;
import com.business.db.model.vo.*;
import com.business.db.model.vo.admin.*;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

public interface UserMapper extends BaseMapper<UserPO> {

    PersonalPageVO personalPage(@Param("userId") Long userId,
                                @Param("loginUserId") Long loginUserId);

    UserPO getByInvCodeUser(@Param("invitationCode") String invitationCode);

    UserInfoVO userInfoById(Long userId);

    /**
     * 微信扫码新用户使用
     * @param userId
     * @return
     */
    UserPO getByUserId(@Param("userId") Long userId);

    UserPO getByFromUser(@Param("unionId") String unionId,
                         @Param("openId") String openId,
                       @Param("mobile") String mobile,
                       @Param("userId") Long userId);

    /**
     * 获取渠道粉丝--注册送点子数参数
     * @param
     * @return
     */
    UserParamVO getFansParam(@Param("userId") Long userId);

    List<SuggestUserVO> getSuggestUsers();

    /* 查询所有邀请码进行查重校验 */
    List<String> getUserInviteCode();

    Long selectFansCount(@Param("userId") Long userId,
                         @Param("firstDayOfMonth") LocalDate firstDayOfMonth,
                         @Param("lastDayOfMonth") LocalDate lastDayOfMonth);

    OpeStatisticsUserPO queryOpeStatisticsByYesterday();

    Integer queryActiveUserToday(@Param("time") String time);

    Page<UserQueryPageVO> queryUserPage(@Param("page") Page<UserQueryPageVO> page, @Param("dto") UserQueryPageDTO dto);

    List<UsersListVO> selectUserList();

    UserStatisticsVO queryUserStatisticsByToday();

    List<DailyOrderVO> queryDailyOrder();

    List<DailyOrderPriceVO> queryDailyOrderPrice();

    Page<FansOrFollowVO> queryFansPage(Page<FansOrFollowVO> page, @io.lettuce.core.dynamic.annotation.Param("dto") FansOrCollectQueryDTO dto);

    Page<FansOrFollowVO> queryFollowPage(Page<FansOrFollowVO> page, @io.lettuce.core.dynamic.annotation.Param("dto") FansOrCollectQueryDTO dto);
}
