package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.business.db.model.po.UserAwardSvipRecordPO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

public interface UserAwardSvipRecordMapper extends BaseMapper<UserAwardSvipRecordPO> {

    Integer selectState(@Param("userId") Long userId,
                     @Param("firstDayOfMonth") LocalDate firstDayOfMonth,
                     @Param("lastDayOfMonth") LocalDate lastDayOfMonth);

}
