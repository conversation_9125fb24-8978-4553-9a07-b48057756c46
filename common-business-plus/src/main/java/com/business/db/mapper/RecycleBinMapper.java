package com.business.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.RecycleBinQueryDTO;
import com.business.db.model.po.RecycleBinPO;
import com.business.db.model.vo.RecycleBinQueryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @className: com.intelligent.bot.dao.RecycleBinDao
 * @description: 我的画廊 --------> 回收站
 * @author: Yl
 * @createDate: 2023-09-02 10:28
 * @version: 1.0
 */
public interface RecycleBinMapper extends BaseMapper<RecycleBinPO> {

    /**
     * 查询回收站列表
     * @param page
     * @return
     */
    Page<RecycleBinQueryVO> queryPage(Page<RecycleBinQueryVO> page, @Param("dto") RecycleBinQueryDTO dto);

    /**
     * 回收站物理删除
     * @param list
     * @return
     */
    Integer batchDelRecycle(List<Long> list);
    /**
     * 批量插入回收站
     * @param list
     * @return
     */
    Integer batchInsert(List<RecycleBinPO> list);

    /**
     * 检测30天到期删除
     * @return
     */
    Integer checkForExpire();

    /**
     * 根据回收站id查找图片路径
     * @param recycleIds
     * @return
     */
    List<Long> getImgIds(@Param("recycleIds") List<Long> recycleIds);


}
