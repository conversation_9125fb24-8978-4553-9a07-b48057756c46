package com.business.db.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.business.db.model.po.user.BlacklistIPPO;

import java.util.Date;
import java.util.List;

/**
 * IP黑名单数据访问接口
 */
@Mapper
public interface BlacklistIPMapper {
    
    /**
     * 插入一条黑名单记录
     * @param blacklistIPPO 黑名单记录
     * @return 影响行数
     */
    @Insert("INSERT INTO blacklist_ip (ip, reason, create_time, expire_time, status, violation_count, remark) " +
            "VALUES (#{ip}, #{reason}, #{createTime}, #{expireTime}, #{status}, #{violationCount}, #{remark})")
    int insert(BlacklistIPPO blacklistIPPO);
    
    /**
     * 根据IP查询有效的黑名单记录
     * @param ip IP地址
     * @return 黑名单记录，如果不存在或已失效则返回null
     */
    @Select("SELECT * FROM blacklist_ip WHERE ip = #{ip} AND status = 1 AND expire_time > NOW() LIMIT 1")
    BlacklistIPPO findValidByIp(@Param("ip") String ip);
    
    /**
     * 更新黑名单状态
     * @param id 记录ID
     * @param status 状态：1-有效，0-无效
     * @return 影响行数
     */
    @Update("UPDATE blacklist_ip SET status = #{status} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 更新黑名单过期时间
     * @param id 记录ID
     * @param expireTime 新的过期时间
     * @return 影响行数
     */
    @Update("UPDATE blacklist_ip SET expire_time = #{expireTime} WHERE id = #{id}")
    int updateExpireTime(@Param("id") Long id, @Param("expireTime") Date expireTime);
    
    /**
     * 查询所有有效的黑名单记录
     * @return 黑名单记录列表
     */
    @Select("SELECT * FROM blacklist_ip WHERE status = 1 AND expire_time > NOW()")
    List<BlacklistIPPO> findAllValid();
    
    /**
     * 清理已过期的黑名单记录（将状态设为无效）
     * @return 影响行数
     */
    @Update("UPDATE blacklist_ip SET status = 0 WHERE status = 1 AND expire_time <= NOW()")
    int cleanExpired();
} 