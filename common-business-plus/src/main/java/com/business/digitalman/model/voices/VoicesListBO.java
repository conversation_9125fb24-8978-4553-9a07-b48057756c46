package com.business.digitalman.model.voices;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class VoicesListBO {

    @JSONField(name = "voice_id")
    private String voiceId;

    @J<PERSON>NField(name = "language")
    private String language;

    @J<PERSON><PERSON>ield(name = "gender")
    private String gender;

    @JSO<PERSON>ield(name = "name")
    private String name;

    @JSONField(name = "preview_audio")
    private String previewAudio;

    @JSONField(name = "support_pause")
    private Boolean supportPause;

    @JSONField(name = "emotion_support")
    private Boolean emotionSupport;

    @JSONField(name = "support_interactive_avatar")
    private Boolean supportInteractiveAvatar;

}
