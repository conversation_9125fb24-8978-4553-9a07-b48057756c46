package com.business.digitalman.model.voices;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class VoicesResBO {

    @JSONField(name = "error")
    private String error;

    @JSONField(name = "data")
    private DataBO data;

    @Data
    public static class DataBO {

        @JSONField(name = "voices")
        private List<VoicesListBO> voices;

    }

}
