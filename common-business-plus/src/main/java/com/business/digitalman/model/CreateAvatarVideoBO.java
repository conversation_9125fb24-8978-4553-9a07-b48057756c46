package com.business.digitalman.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class CreateAvatarVideoBO {

    @J<PERSON>NField(name = "caption")
    private Boolean caption;

    @J<PERSON><PERSON>ield(name = "title")
    private String title;

    @J<PERSON><PERSON>ield(name = "video_inputs")
    private VideoInputs videoInputs;

    @JSONField(name = "dimension")
    private Dimension dimension;

    @JSONField(name = "callback_id")
    private String callbackId;

    @J<PERSON><PERSON>ield(name = "callback_url")
    private String callbackUrl;

    @Data
    public static class VideoInputs {
        @J<PERSON><PERSON>ield(name = "character")
        private Integer character;

        @<PERSON><PERSON><PERSON><PERSON>(name = "voice")
        private String voice;

        @JSO<PERSON>ield(name = "background")
        private String background;
    }

    @Data
    public static class Dimension {
        @JSONField(name = "width")
        private Integer width;

        @J<PERSON><PERSON>ield(name = "height")
        private Integer height;
    }

}
