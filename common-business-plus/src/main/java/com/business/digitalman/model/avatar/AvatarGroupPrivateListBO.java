package com.business.digitalman.model.avatar;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class AvatarGroupPrivateListBO {

    @JSONField(name = "avatar_id")
    private String avatarId;

    @JSONField(name = "avatar_name")
    private String avatarName;

    @J<PERSON><PERSON>ield(name = "gender")
    private String gender;

    @JSONField(name = "preview_image_url")
    private String previewImageUrl;

    @JSONField(name = "preview_video_url")
    private String previewVideoUrl;

}
