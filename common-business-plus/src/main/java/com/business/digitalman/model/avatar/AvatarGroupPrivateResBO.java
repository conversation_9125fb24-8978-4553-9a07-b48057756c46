package com.business.digitalman.model.avatar;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class AvatarGroupPrivateResBO {

    @JSONField(name = "error")
    private String error;

    @JSONField(name = "data")
    private DataBO data;

    @Data
    public static class DataBO {

        @JSONField(name = "avatar_list")
        private List<AvatarGroupPrivateListBO> avatarList;

    }

}
