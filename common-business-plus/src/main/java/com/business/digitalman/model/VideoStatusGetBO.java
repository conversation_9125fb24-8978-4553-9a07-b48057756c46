package com.business.digitalman.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class VideoStatusGetBO {

    @JSONField(name = "id")
    private String id;

    @JSONField(name = "status")
    private String status;

    @J<PERSON>NField(name = "thumbnail_url")
    private String thumbnailUrl;

    @<PERSON><PERSON><PERSON>ield(name = "video_url")
    private String videoUrl;

    @JSONField(name = "video_url_caption")
    private String videoUrlCaption;

    @JSONField(name = "callback_id")
    private String callbackId;

    @JSONField(name = "caption_url")
    private String captionUrl;

    @JSONField(name = "created_at")
    private Long createdAt;

    @JSONField(name = "duration")
    private String duration;

    @J<PERSON><PERSON>ield(name = "error")
    private String error;

    @JSONField(name = "gif_url")
    private String gifUrl;


}
