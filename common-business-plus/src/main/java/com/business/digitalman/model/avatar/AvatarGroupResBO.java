package com.business.digitalman.model.avatar;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class AvatarGroupResBO {

    @JSONField(name = "error")
    private String error;

    @JSONField(name = "data")
    private DataBO data;

    @Data
    public static class DataBO {

        @J<PERSON>NField(name = "total_count")
        private Integer totalCount;

        @JSONField(name = "avatar_group_list")
        private List<AvatarGroupListBO> avatarGroupList;

    }

}
