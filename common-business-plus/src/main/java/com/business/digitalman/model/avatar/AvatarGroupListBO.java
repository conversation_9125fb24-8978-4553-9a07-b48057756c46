package com.business.digitalman.model.avatar;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class AvatarGroupListBO {

    @JSONField(name = "id")
    private String id;

    @JSONField(name = "name")
    private String name;

    @J<PERSON>NField(name = "created_at")
    private Float createdAt;

    @JSONField(name = "num_looks")
    private Integer numLooks;

    @JSONField(name = "preview_image_url")
    private String previewImageUrl;

    @JSONField(name = "group_type")
    private String groupType;

    @JSONField(name = "train_status")
    private String trainStatus;

}
