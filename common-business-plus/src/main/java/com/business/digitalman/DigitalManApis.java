package com.business.digitalman;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSONObject;
import com.business.digitalman.model.VideoStatusGetBO;
import com.business.digitalman.model.avatar.AvatarGroupPrivateResBO;
import com.business.digitalman.model.avatar.AvatarGroupResBO;
import com.business.digitalman.model.voices.VoicesResBO;
import com.business.kling.model.KlingTextVideoResBO;
import com.nacos.tool.BrotliInterceptor;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.File;
import java.util.concurrent.TimeUnit;


@Slf4j
@Schema(title = "limits api工具类")
public class DigitalManApis {

    private static final String HEYGEN_API = "https://api.heygen.com";
    private static final String HEYGEN_TWO_API = "https://api2.heygen.com";
    private static final String wd_XAPI_KEY = "OTk4NTRjOTMxZjNiNGRiM2E0YzkzNzg3MGM0NTA4YzktMTczNjEzMzc1Ng=="; //我的
    private static final String XAPI_KEY = "NjY0NzU2ZTE4NjRlNDhlYWJmZjdmNDgzMzRiZDMwNTYtMTczNDM1OTE1MA=="; //冯姐给的
    private static final String WZ_XAPI_KEY = "NWQ0ODIwY2I2ODlhNDU5OGJkN2RiOWJkMjUwZjA0MTQtMTY4MjE2NTk2MQ=="; //王总给的

    private static final String HEYGEN_COOKIE = "intercom-device-id-oiknz8io=4b7db201-33f9-4625-b30e-44635f580be6; _ga=GA1.1.1757433958.1714018585; _ga_RLFPG6VDC0=GS1.1.1714022035.1.1.1714022088.7.0.0; ko_id=5d4e9edb-a5d9-4a28-8129-4d015a90bb85; cookie_perms=%7B%22_all%22%3A1%7D; _fbp=fb.1.1730361361466.811671025709341483; _gcl_au=1.1.213772898.1730362037; syft.anonymous_id=%2236f25d8b-6f6f-4fba-b2dc-3ea77a7ed4ff%22; syft.initial_source_touch={%22clickIds%22:{%22gclid%22:%22CjwKCAiA34S7BhAtEiwACZzv4UARqAP8GbaMYIsOeSSmh9gutgZ6Q8q7ZyuHNgC0NTfKeSn0pK25URoCYb0QAvD_BwE%22}}; heygen_space=; syft.user_id=%<EMAIL>%22; syft.user_traits={%22username%22:%<EMAIL>%22%2C%22address%22:{}%2C%22email%22:%<EMAIL>%22%2C%22source%22:%22form_submit%22}; heygen_session=eyJzZXNzaW9uX3Rva2VuIjogImV5SjBiMnRsYmlJNklDSmxaalppTVRFek1XTmxNV0UwT1dVek9XWmpOemxoWVdNNU16ZzRZVFE1TXlJc0lDSjBiMnRsYmw5MGVYQmxJam9nSW5KbFozVnNZWElpTENBaVkzSmxZWFJsWkY5aGRDSTZJREUzTXpRMU1USTROelo5In0=; heygen_is_login=true; x-movio-shared-v-id=xU7QV35FNpWNOjgx58CgmKZ43uUGV73B; rewardful.referral={%22id%22:%228c0d5e31-e998-44a1-9b69-dc9573122af0%22%2C%22created_at%22:%222024-12-30T10:42:00.814Z%22%2C%22affiliate%22:{%22id%22:%2279e6abeb-254b-4fb1-8ba7-cedf5fb8e7e5%22%2C%22name%22:%22mandy%20tayler%22%2C%22first_name%22:%22mandy%22%2C%22last_name%22:%22tayler%22%2C%22token%22:%22s5%22}%2C%22campaign%22:{%22id%22:%226d8bc933-8d79-41f1-a19f-83e325cc9360%22%2C%22name%22:%22HeyGen%20Affiliate%20Program-New%22}%2C%22coupon%22:null%2C%22cookie%22:{%22domain%22:%22heygen.com%22}}; _clck=v3s6qp%7C2%7Cfs8%7C0%7C1828; cf_clearance=uuTFNq41EDrmft1qH.xi7L3ya0FfjtyoP4dRqv_Onjo-1735786053-*******-bTARMBzIY0FAmOyTYRqzEtYn6XE_O0M4FKh8gl0DdbsF6HwOCmumoVOD2fT3dywj_ZxpsKeZUFyH02bU39RfhVNk.xDuj4Tc3LLnQFJk3kYckhaDK00qN7k5BELfhoH9PlLluYws78UGi6shxQeqmPa6Ndzve_ycG0sdVFCMstDxMSGf6UsYKRuNyYGF2iufppALvvebIBtNMvLNkXS2U2zQTmTl5eU3Ix3GuqM7YZOunEwyya8ECIHqaC5gz1OZve.zKRWE1mCmkSKVTs1ygzrVtODmN0z.KeQUzZB3PxKV6WbL_HgIaT0HPR9j4Qyc6s1Yit15hXPm0o1RhYgEXNWXefHrjihKh5VLADc4abp35xUzur.FJwZCRs2IotMd5Ch4dbkmkCQPE_M9vvVq9Q; syft.source_touch={}; _ga_360875918=GS1.1.1736245962.6.0.1736245962.0.0.0; _gcl_aw=GCL.1736245974.Cj0KCQiAvvO7BhC-ARIsAGFyToUj8DUhH4MdysWxP9Evh_ctXISppW1nTnO3QcQxPZooaxEIy43M5xUaAiteEALw_wcB; _gcl_gs=2.1.k1$i1736245968$u88296322; intercom-session-oiknz8io=VHVxTHgxTHl6aTRHM2FTVlNJdmZzaTg4MlA4WklLNXFwZUk4Qk5HaW9WOWw1WkRaRHFWSndkcFdDTXo4QVNEZi0tSFJSN2NXb2hFZjluNmwvbGtIQ3V0Zz09--f6b2bd710bf4d6a0800b7df2279e6d2fc3c275dc; _ga_PMMWB6MFPC=GS1.1.1736327033.69.1.1736328681.35.0.0; syft.session={%22id%22:%22bd553b5f-cd2d-41e7-af5a-86f8d7390f4a%22%2C%22startTime%22:%222025-01-08T09:29:58.103Z%22%2C%22lastActivityTime%22:126025.5%2C%22content%22:[]}; ph_phc_bcoaBa4uZdMp0CzXr7QhZRRpnNGgBRRZ1GStvuJVAgJ_posthog=%7B%22distinct_id%22%3A%22664756e1864e48eabff7f48334bd3056%22%2C%22%24sesid%22%3A%5B1736328722123%2C%2201944527-f218-7e86-8d07-5b62f3e8fd8c%22%2C1736327033368%5D%2C%22%24epp%22%3Atrue%7D";

    @Getter
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    // 照片
    public static void uploadHeygenImage() {
        MediaType mediaType = MediaType.parse("image/png");
        RequestBody body = RequestBody.create(mediaType, "data:image/jpeg;name=20241112-131157.jpg;base64,/9j/2wBDAAYEBQYFB");
        Request request = new Request.Builder()
                .url("https://upload.heygen.com/v1/asset")
                .post(body)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "image/png")
                .addHeader("x-api-key", XAPI_KEY)
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("可灵AI获取任务结果状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                System.out.println(JSONObject.parseObject(responseBody, KlingTextVideoResBO.class));
            }
        } catch (Exception e) {
            log.error("可灵AI拉取任务失败：{}", e.getMessage());
        }
    }

    // 视频
    public static void uploadHeygenVideo() throws Exception {
        MediaType mediaType = MediaType.parse("video/mp4");
        File videoFile = new File("I:/360Downloads/mp4/1610_1736233064.mp4");
        RequestBody body = RequestBody.create(videoFile, mediaType);
        Request request = new Request.Builder()
                .url("https://upload.heygen.com/v1/asset")
                .post(body)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "video/mp4")
                .addHeader("x-api-key", XAPI_KEY)
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("可灵AI获取任务结果状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            System.out.println("============================");
            System.out.println(responseBody);
            System.out.println("============================");
        } catch (Exception e) {
            log.error("可灵AI拉取任务失败：{}", e.getMessage());
        }
    }

    // 音频
    public static void uploadHeygenAudio() {
        MediaType mediaType = MediaType.parse("audio/mpeg");
        RequestBody body = RequestBody.create(mediaType, "data:audio/mpeg;name=misuck.mp3;base64,SUQzAwAAAAAAKFRT");
        Request request = new Request.Builder()
                .url("https://upload.heygen.com/v1/asset")
                .post(body)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "audio/mpeg")
                .addHeader("x-api-key", XAPI_KEY)
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("可灵AI获取任务结果状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                System.out.println(JSONObject.parseObject(responseBody, KlingTextVideoResBO.class));
            }
        } catch (Exception e) {
            log.error("可灵AI拉取任务失败：{}", e.getMessage());
        }
    }

    public static AvatarGroupResBO getDigitalmanAvatarGroup() {
        Request request = new Request.Builder()
                .url("https://api.heygen.com/v2/avatar_group.list?include_public=false")
                .get()
                .addHeader("accept", "application/json")
                .addHeader("x-api-key", XAPI_KEY)
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("获取头像组Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                return JSON.parseObject(responseBody, new TypeReference<AvatarGroupResBO>() {
                });
            }
        } catch (Exception e) {
            log.error("获取头像组失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    public static AvatarGroupPrivateResBO getDigitalmanAvatarByGroupId(String groupId) {
        Request request = new Request.Builder()
                .url("https://api.heygen.com/v2/avatar_group/" + groupId + "/avatars")
                .get()
                .addHeader("accept", "application/json")
                .addHeader("x-api-key", XAPI_KEY)
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("获取私有头像组Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                return JSON.parseObject(responseBody, new TypeReference<AvatarGroupPrivateResBO>() {
                });
            }
        } catch (Exception e) {
            log.error("获取私有头像组失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    // 获取声音
    public static VoicesResBO getDigitalmanVoices() {
        Request request = new Request.Builder()
                .url(HEYGEN_API + "/v2/voices")
                .get()
                .addHeader("accept", "application/json")
                .addHeader("x-api-key", XAPI_KEY)
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("获取声音失败Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                return JSON.parseObject(responseBody, new TypeReference<VoicesResBO>() {
                });
            }
        } catch (Exception e) {
            log.error("获取声音失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    // 生成视频
    public static void generateVideo() {
        String content = "{\n" +
                "  \"video_inputs\": [\n" +
                "    {\n" +
                "      \"character\": {\n" +
                "        \"type\": \"avatar\",\n" +
                "        \"avatar_id\": \"785164ff86564400a8beca95624548e5\",\n" +
                "        \"avatar_style\": \"normal\"\n" +
                "      },\n" +
                "      \"voice\": {\n" +
                "        \"type\": \"text\",\n" +
                "        \"input_text\": \"Welcome to the HeyGen API!\",\n" +
                "        \"voice_id\": \"0214ac51f93e420f8711d568dcfbc50e\",\n" +
                "        \"speed\": 1.1\n" +
                "      }\n" +
                "    }\n" +
                "  ],\n" +
                "  \"dimension\": {\n" +
                "    \"width\": 1280,\n" +
                "    \"height\": 720\n" +
                "  }\n" +
                "}";
        RequestBody reqBody = RequestBody.create(content, MediaType.parse("application/json"));
        Request request = new Request.Builder()
                .url(HEYGEN_API + "/v2/video/generate")
                .post(reqBody)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("x-api-key", XAPI_KEY)
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("可灵AI获取任务结果状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            System.out.println(responseBody);
            if (response.isSuccessful()) {
                System.out.println(JSONObject.parseObject(responseBody, KlingTextVideoResBO.class));
            }
        } catch (Exception e) {
            log.error("可灵AI拉取任务失败：{}", e.getMessage());
        }
    }

    public static String pacificDraftCreate() {
        RequestBody requestBody = RequestBody.create("{}", MediaType.get("application/json"));
        Request request = new Request.Builder()
                .url(HEYGEN_TWO_API + "/v1/pacific/draft/create")
                .post(requestBody)
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Length", "2")
                .addHeader("Content-Type", "application/json")
                .addHeader("Cookie", HEYGEN_COOKIE)
                .addHeader("Origin", "https://app.heygen.com")
                .addHeader("Priority", "u=1, i")
                .addHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.info("视频id，状态码：" + response.code());
            if (response.body() != null) {
                String responseBody = response.body().string();
                log.info("获取视频id，responseBody：{}", responseBody);
                if (response.isSuccessful()) {
                    JSONObject jsonObject = JSONObject.parseObject(responseBody);
                    int code = jsonObject.getIntValue("code");
                    JSONObject dataObject = jsonObject.getJSONObject("data");
                    if (code == 100 && dataObject != null) {
                        return dataObject.getString("video_id");
                    }
                }
            }
        } catch (Exception e) {
            log.error("创建视频id失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    public static String postDigitalmanVideoGenerate(String requestBody) {
        Request request = new Request.Builder()
                .url(HEYGEN_TWO_API + "/v3/video.generate")
                .post(RequestBody.create(requestBody, MediaType.parse("application/json")))
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Length", "3931")
                .addHeader("Content-Type", "application/json")
                .addHeader("Cookie", HEYGEN_COOKIE)
                .addHeader("Priority", "u=1, i")
                .addHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            System.out.println(response.code());
            System.out.println(responseBody);
            if (response.isSuccessful()) {
                System.out.println(responseBody);
                return responseBody;
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage());
        }
        return null;
    }

    public static VideoStatusGetBO getDigitalmanVideoDetails(String videoId) {
        Request request = new Request.Builder()
                .url("https://api.heygen.com/v1/video_status.get?video_id=" + videoId)
                .get()
                .addHeader("accept", "application/json")
                .addHeader("x-api-key", XAPI_KEY)
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("获取数字视频任务结果状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("获取数字视频任务结果=" + responseBody);
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                int code = jsonObject.getIntValue("code");
                JSONObject dataObject = jsonObject.getJSONObject("data");
                if (code == 100 && dataObject != null) {
                    return JSON.parseObject(dataObject.toJSONString(), new TypeReference<VideoStatusGetBO>() {
                    });
                }
            }
        } catch (Exception e) {
            log.error("获取数字视频任务失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    public static String deleteDigitalmanVideo(String videoId) {
        Request request = new Request.Builder()
                .url("https://api.heygen.com/v1/video.delete?video_id=" + videoId)
                .delete(null)
                .addHeader("accept", "application/json")
                .addHeader("x-api-key", XAPI_KEY)
                .build();
        try (Response response = client.newCall(request).execute()) {
            int responseCode = response.code();
            log.error("删除数字视频任务结果状态码Code：" + responseCode);
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("删除数字视频任务结果=" + responseBody);
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                int code = jsonObject.getIntValue("code");
                if (code == 100) {
                    return "删除成功";
                }
            }
        } catch (Exception e) {
            log.error("删除数字视频任务失败：{}", e.getMessage());
            return null;
        }
        return null;
    }


}
