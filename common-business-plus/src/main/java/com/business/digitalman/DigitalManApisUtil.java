package com.business.digitalman;

import com.alibaba.fastjson.JSONObject;
import com.business.digitalman.model.VideoStatusGetBO;
import com.business.digitalman.model.avatar.AvatarGroupListBO;
import com.business.digitalman.model.avatar.AvatarGroupPrivateListBO;
import com.business.digitalman.model.avatar.AvatarGroupPrivateResBO;
import com.business.digitalman.model.avatar.AvatarGroupResBO;
import com.business.digitalman.model.voices.VoicesListBO;
import com.business.digitalman.model.voices.VoicesResBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Slf4j
@Schema(title = "DigitalManApisUtil api工具类")
public class DigitalManApisUtil {

    // 获取声音列表
    public static List<VoicesListBO> getDigitalmanVoices() {
        VoicesResBO voicesResBO = DigitalManApis.getDigitalmanVoices();
        if (voicesResBO == null || voicesResBO.getData() == null) {
            return null;
        }
        return voicesResBO.getData().getVoices();
    }

    // 获取头像列表
    public static List<AvatarGroupListBO> getDigitalmanAvatarGroup() {
        AvatarGroupResBO avatarGroupResBO = DigitalManApis.getDigitalmanAvatarGroup();
        if (avatarGroupResBO == null || avatarGroupResBO.getData() == null) {
            return null;
        }
        log.info("获取头像组：{}", JSONObject.toJSONString(avatarGroupResBO.getData().getAvatarGroupList()));
        return avatarGroupResBO.getData().getAvatarGroupList().stream()
                .filter(avatar -> "PRIVATE".equals(avatar.getGroupType()))
                .toList();
    }

    // 根据头像组id获取头像列表
    public static List<AvatarGroupPrivateListBO> getDigitalmanAvatarByGroupId(String groupId) {
        AvatarGroupPrivateResBO avatarGroupPrivateResBO = DigitalManApis.getDigitalmanAvatarByGroupId(groupId);
        if (avatarGroupPrivateResBO == null || avatarGroupPrivateResBO.getData() == null) {
            return null;
        }
        return avatarGroupPrivateResBO.getData().getAvatarList();
    }

    public static String postDigitalmanVoiceGenerate() {
        return DigitalManApis.pacificDraftCreate();
    }

    public static String postDigitalmanVideoGenerate(String requestBody) {
        return DigitalManApis.postDigitalmanVideoGenerate(requestBody);
    }

    // 获取视频详情
    public static VideoStatusGetBO getDigitalmanVideoDetails(String voiceId) {
        return DigitalManApis.getDigitalmanVideoDetails(voiceId);
    }

    // 删除视频
    public static String deleteDigitalmanVideo(String videoId) {
        return DigitalManApis.deleteDigitalmanVideo(videoId);
    }

}
