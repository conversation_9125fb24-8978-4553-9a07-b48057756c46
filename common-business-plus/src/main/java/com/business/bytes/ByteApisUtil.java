package com.business.bytes;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.business.bytes.enums.ByteStatusEnum;
import com.business.bytes.model.*;
import com.business.utils.BOssUtil;
import com.business.utils.BThirdPartyKey;
import com.nacos.enums.DictConfigEnum;
import com.nacos.utils.BFeiShuUtil;
import com.volcengine.service.visual.IVisualService;
import com.volcengine.service.visual.impl.VisualServiceImpl;
import com.volcengine.service.visual.model.request.*;
import com.volcengine.service.visual.model.response.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;


@Slf4j
@Schema(title = "byte api工具类")
public class ByteApisUtil {

    // 字节跳动API的访问密钥与密钥在字典表中的存储，不再硬编码
    private static final String ACCESS_KEY_FALLBACK = "AKLTZWI4NmVlNTFjZWRiNGNkNGJhZTA5ZTJlNjQ2M2FlYWI";
    private static final String SETSECRET_KEY_FALLBACK = "TVdJNE5EWmtORGxtWXpJMU5ERmlObUptTURBNVltVTBaRGRtWVRKaU9HRQ==";

    // 字节跳动视觉服务客户端
    private static IVisualService visualService;

    /**
     * 获取字节跳动API密钥
     * 优先从字典表获取，获取不到则使用默认值
     * 
     * @return 密钥信息，包含ACCESS_KEY和SECRET_KEY
     */
    private static String[] getByteApiKeys() {
        String accessKey = ACCESS_KEY_FALLBACK;
        String secretKey = SETSECRET_KEY_FALLBACK;
        
        try {
            // 从字典表获取字节API密钥信息
            HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.VOLCANO_POSTER_API_KEY.getDictType());
            if (dictConfigMap != null) {
                // 使用火山引擎海报API密钥，因为它同样是字节系的API
                String apiKey = dictConfigMap.get(DictConfigEnum.VOLCANO_POSTER_API_KEY.getDictKey());
                if (StringUtils.isNotBlank(apiKey)) {
                    // 如果apiKey是一个复合字符串，我们可以尝试拆分它
                    // 假设密钥格式为 "accessKey,secretKey"
                    String[] keys = apiKey.split(",");
                    if (keys.length >= 2) {
                        accessKey = keys[0];
                        secretKey = keys[1];
                    } else {
                        // 如果不是复合格式，我们保留原来的逻辑
                        log.info("字节API密钥格式不正确，使用默认值");
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取字节API密钥失败，使用默认值: {}", e.getMessage());
        }
        
        return new String[]{accessKey, secretKey};
    }

    /**
     * 初始化视觉服务客户端
     * 如果客户端未初始化，则创建实例并设置访问密钥
     */
    private static void initializeVisualService() {
        if (visualService == null) {
            visualService = VisualServiceImpl.getInstance();
            String[] apiKeys = getByteApiKeys();
            visualService.setAccessKey(apiKeys[0]);
            visualService.setSecretKey(apiKeys[1]);
        }
    }

    /**
     * 图片修复/擦除API调用
     * 将图片中特定区域(由蒙版标记)进行修复或擦除
     * 
     * @param byteInpaintingBO 图片修复所需参数对象
     * @return 修复后的图片数据(JSON字符串格式)，失败返回null
     */
    public static String postImgToImgInpainting(ByteInpaintingBO byteInpaintingBO) {
        initializeVisualService();
        VisualImg2ImgInpaintingRequest inpaintingRequest = new VisualImg2ImgInpaintingRequest();
        inpaintingRequest.setReqKey("i2i_inpainting");
        ArrayList<String> binaryDataBase64 = new ArrayList<String>();
        binaryDataBase64.add(byteInpaintingBO.getImageData());
        binaryDataBase64.add(byteInpaintingBO.getMaskData());
        inpaintingRequest.setBinary_data_base64(binaryDataBase64);

        inpaintingRequest.setSteps(byteInpaintingBO.getSteps());
        inpaintingRequest.setStrength(byteInpaintingBO.getStrength());
        inpaintingRequest.setScale(byteInpaintingBO.getScale());
        inpaintingRequest.setSeed(0);
        try {
            VisualImg2ImgInpaintingResponse response = visualService.img2ImgInpainting(inpaintingRequest);
            log.info("postImg2ImgInpainting= {}", response.toString());
            if (response.getCode() != 10000) {
                log.info("byte抹除失败：｛｝", response.getMessage());
                return null;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("image", (!response.getData().getBinaryDataBase64().isEmpty() ? response.getData().getBinaryDataBase64().getFirst() : null));
            jsonObject.put("finish_reason", "SUCCESS");
            jsonObject.put("seed", 0);
            return jsonObject.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("byte抹除失败：｛｝", e.getMessage());
            return null;
        }
    }

    /**
     * 图片修复编辑API调用
     * 将图片中特定区域(由蒙版标记)进行修复或替换，可指定自定义提示词
     * 
     * @param byteInpaintingEditBO 图片修复编辑所需参数对象
     * @return 修复编辑后的图片数据(JSON字符串格式)，失败返回null
     */
    public static String postImgToImgInpaintingEdit(ByteInpaintingEditBO byteInpaintingEditBO) {
        initializeVisualService();
        VisualImg2ImgInpaintingEditRequest inpaintingEditRequest = new VisualImg2ImgInpaintingEditRequest();
        inpaintingEditRequest.setReqKey("i2i_inpainting_edit");
        ArrayList<String> binaryDataBase64 = new ArrayList<String>();
        binaryDataBase64.add(byteInpaintingEditBO.getImageData());
        binaryDataBase64.add(byteInpaintingEditBO.getMaskData());
        inpaintingEditRequest.setBinary_data_base64(binaryDataBase64);
        inpaintingEditRequest.setCustomPrompt(byteInpaintingEditBO.getCustomPrompt());

        inpaintingEditRequest.setSteps(byteInpaintingEditBO.getSteps());
        inpaintingEditRequest.setScale(byteInpaintingEditBO.getScale());
        inpaintingEditRequest.setSeed(-1);
        try {
            VisualImg2ImgInpaintingEditResponse response = visualService.imgInpaintingEdit(inpaintingEditRequest);
            if (response.getCode() != 10000) {
                log.info("byte编辑失败：{}", response.getMessage());
                log.info("postImgToImgInpaintingEdit= {}", response.toString());
                return null;
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("image", (!response.getData().getBinaryDataBase64().isEmpty() ? response.getData().getBinaryDataBase64().getFirst() : null));
                jsonObject.put("finish_reason", "SUCCESS");
                jsonObject.put("seed", 0);
                return jsonObject.toJSONString();
            }
        } catch (Exception e) {
            log.error("byte编辑失败：{}", e.getMessage());
            return null;
        }
    }


    /**
     * 图片转视频3D生成API调用
     * 将静态图片转换为具有3D效果的短视频
     * 
     * @param byteVideo3dBO 图片转视频所需参数对象
     * @param videoJobId 视频任务ID，用于标识视频任务
     * @return 生成的视频URL路径，失败返回null
     * @throws Exception 调用过程中可能抛出的异常
     */
    public static String postImg2Video3dGenerate(ByteVideo3dBO byteVideo3dBO, String videoJobId) throws Exception {
        initializeVisualService();
        VisualImg2Video3DRequest video3DRequest = new VisualImg2Video3DRequest();
        video3DRequest.setReqKey("img2video3d");
        ArrayList<String> binaryData = new ArrayList<>();
        binaryData.add(byteVideo3dBO.getImageData());
        video3DRequest.setBinaryDataBase64(binaryData);

        VisualImg2Video3DRequest.RenderSpec render = new VisualImg2Video3DRequest.RenderSpec();
        render.setMode(byteVideo3dBO.getMode());
        render.setLongSide(byteVideo3dBO.getLongSide());
        render.setFrameNum(byteVideo3dBO.getFrameNum());
        render.setFps(byteVideo3dBO.getFps());
        render.setUseFlow(-1);

        ArrayList<Float> speed = new ArrayList<>();
        speed.add(0F);
        speed.add(1F);
        render.setSpeedShift(speed);
        video3DRequest.setRenderSpec(render);

        try {
            VisualImg2Video3DResponse response = visualService.img2Video3D(video3DRequest);
            log.info("postImg2Video3dGenerate", response.toString());
            if (response.getCode() == 10000) {
                String base64String = response.getData().getBinaryDataBase64().get(0);
                return uploadSDVideo(Base64.getDecoder().decode(base64String), videoJobId);
            }
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "镜头生成视频失败", "ERRORInfo=" + response.getMessage());
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "镜头生成视频失败", "ERRORInfo=" + e.getMessage());
            return null;
        }
    }

    /**
     * 上传视频到OSS存储
     * 将生成的视频字节数据上传到对象存储服务
     * 
     * @param fileBytes 视频的字节数据
     * @param videoJobId 视频任务ID，用于命名和标识视频
     * @return 上传成功后的视频URL路径，失败返回null
     */
    public static String uploadSDVideo(byte[] fileBytes, String videoJobId) {
        int maxRetries = 3; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadVideo(fileBytes, videoJobId, 14);
                if (videoPath != null) {
                    return videoPath;
                }
                log.error("uploadImageToOss 图片上传失败，重试次数：" + (retryCount + 1), videoJobId);
            } catch (Exception e) {
                log.error("上传图片到OSS发生异常，重试次数：" + (retryCount + 1), e);
            }
            retryCount++;
        }
        log.error("uploadImageToOss 图片上传失败，超过最大重试次数， 视频id=", videoJobId);
        return null;
    }

    /**
     * 图片外扩展API调用
     * 对图片进行向外扩展，可实现图片的无缝延伸
     * 
     * @param byteOutpaintingBO 图片外扩展所需参数对象
     * @return 扩展后的图片数据(JSON字符串格式)，失败返回null
     */
    public static String postImg2ImgOutpainting(ByteOutpaintingBO byteOutpaintingBO) {
        initializeVisualService();
        VisualImg2ImgOutpaintingRequest outpaintingRequest = new VisualImg2ImgOutpaintingRequest();
        outpaintingRequest.setReqKey("i2i_outpainting");
        ArrayList<String> binaryDataBase64 = new ArrayList<String>();
        binaryDataBase64.add(byteOutpaintingBO.getImageData());
        outpaintingRequest.setBinary_data_base64(binaryDataBase64);
        outpaintingRequest.setCustomPrompt(byteOutpaintingBO.getCustomPrompt());
        outpaintingRequest.setSteps(byteOutpaintingBO.getSteps());
        outpaintingRequest.setStrength(byteOutpaintingBO.getStrength());
        outpaintingRequest.setScale(byteOutpaintingBO.getScale());
        outpaintingRequest.setSeed(byteOutpaintingBO.getSeed());

        outpaintingRequest.setTop(byteOutpaintingBO.getTop());
        outpaintingRequest.setBottom(byteOutpaintingBO.getBottom());
        outpaintingRequest.setLeft(byteOutpaintingBO.getLeft());
        outpaintingRequest.setRight(byteOutpaintingBO.getRight());

        outpaintingRequest.setMax_height(byteOutpaintingBO.getMaxHeight());
        outpaintingRequest.setMax_width(byteOutpaintingBO.getMaxWidth());
        try {
            VisualImg2ImgOutpaintingResponse response = visualService.Img2ImgOutpainting(outpaintingRequest);
            log.info("postImg2ImgOutpainting", response.toString());
            if (response.getCode() == 10000) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("image", (!response.getData().getBinaryDataBase64().isEmpty() ? response.getData().getBinaryDataBase64().getFirst() : null));
                jsonObject.put("finish_reason", "SUCCESS");
                jsonObject.put("seed", 0);
                return jsonObject.toJSONString();
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            log.info("byte抹除失败：｛｝", e.getMessage());
            return null;
        }
    }

    /**
     * 人脸融合API调用（公测版本）
     * 将一个人脸图像融合到另一个图像中的人脸位置
     * 
     * @param byteFaceSwapBO 人脸融合所需参数对象
     * @return 融合后的图片列表（Base64编码），失败返回null
     */
    public static List<String> postImageToImageFaceSwap(ByteFaceSwapBO byteFaceSwapBO) {
        initializeVisualService();
        VisualFaceSwapV2Request faceSwapV2Request = new VisualFaceSwapV2Request();
        //注意 3.0版本传faceswap || 3.3版本传face_swap3_3
        faceSwapV2Request.setReqKey("face_swap3_3");
        if (byteFaceSwapBO.getBinaryDataBase64() == null && byteFaceSwapBO.getImageUrls() != null) {
            faceSwapV2Request.setImageUrls(byteFaceSwapBO.getImageUrls());
        }
        if (byteFaceSwapBO.getImageUrls() == null && byteFaceSwapBO.getBinaryDataBase64() != null) {
            faceSwapV2Request.setBinaryDataBase64(byteFaceSwapBO.getBinaryDataBase64());
        }

        faceSwapV2Request.setFaceType(byteFaceSwapBO.getFaceType());
        ArrayList<VisualFaceSwapV2Request.MergeInfos> mergeInfosList = new ArrayList<>();
        VisualFaceSwapV2Request.MergeInfos mergeInfos = new VisualFaceSwapV2Request.MergeInfos();
        mergeInfos.setLocation(byteFaceSwapBO.getLocation());
        mergeInfos.setTemplate_location(byteFaceSwapBO.getTemplateLocation());
        mergeInfosList.add(mergeInfos);
        faceSwapV2Request.setMergeInfos(mergeInfosList);
        faceSwapV2Request.setDoRisk(byteFaceSwapBO.getDoRisk());
        faceSwapV2Request.setSourceSimilarity(byteFaceSwapBO.getSourceSimilarity());
        faceSwapV2Request.setGpen(byteFaceSwapBO.getGpen());
        try {
            log.info("postImageToImageFaceSwap req=" + JSONObject.toJSONString(faceSwapV2Request));
            VisualFaceSwapV2Response response = visualService.faceSwapV2(faceSwapV2Request);
            log.info("postImageToImageFaceSwap resp code=" + response.getCode());
            if (response.getCode() == 200 || response.getCode() == 10000) {
                return postEnhancePhotoV2(response.getData().getBinaryDataBase64());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("byte人脸融合失败：｛｝" + e.getMessage());
            return null;
        }
        return null;
    }

    /**
     * 图片增强API调用
     * 对图片进行质量增强处理
     * 
     * @param imageBase64s 需要增强的图片Base64编码列表
     * @return 增强后的图片列表（Base64编码），失败返回null
     */
    public static List<String> postEnhancePhotoV2(List<String> imageBase64s) {
        if (imageBase64s == null || imageBase64s.size() == 0) {
            return null;
        }
        initializeVisualService();
        VisualEnhancePhotoV2Request photoV2Request = new VisualEnhancePhotoV2Request();
        photoV2Request.setReqKey("lens_lqir");
        photoV2Request.setResolutionBoundary("2k");

        ArrayList<String> binaryData = new ArrayList<>();
        binaryData.add(imageBase64s.get(0));
        photoV2Request.setBinaryDataBase64(binaryData);
        try {
            VisualEnhancePhotoV2Response response = visualService.enhancePhotoV2(photoV2Request);
            log.info("postEnhancePhotoV2 resp code=" + response.getCode());
            if (response.getCode() == 200 || response.getCode() == 10000) {
                List<String> dataBase64 = response.getData().getBinaryDataBase64();
                // saveBase64ImageToFile(dataBase64.get(0), "换脸增强后的图片.png");
                return dataBase64;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    /**
     * 一键海报生成API调用
     * 提交海报生成任务，根据文本描述生成海报图片
     * 
     * @param posterReqBO 海报生成所需参数对象
     * @return 生成任务的ID，失败返回null或错误状态码
     * @throws Exception 调用过程中可能抛出的异常
     */
    public static String postJimengTextToImageGenerations(PosterReqBO posterReqBO) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(posterReqBO, JSONWriter.Feature.WriteMapNullValue));
        
        // 从字典表获取字节API密钥
        String[] apiKeys = getByteApiKeys();
        String accessKey = apiKeys[0];
        String secretKey = apiKeys[1];
        
        // 检查密钥是否有效
        if (StringUtils.isBlank(accessKey) || StringUtils.isBlank(secretKey)) {
            log.error("字节一键海报生成没有可用的key");
            throw new RuntimeException("字节一键海报模型升级维护中...");
        }
        
        PosterApiUtil posterApiUtil = new PosterApiUtil(accessKey, secretKey);
        PosterPostResBO posterPostResBO = posterApiUtil.postJimengTextToImageGenerations(jsonObject.toString().getBytes());
        if (posterPostResBO == null) {
            return null;
        }
        int status = posterPostResBO.getStatus();
        if (status == ByteStatusEnum.SUCCESS.getStatus()) {
            return posterPostResBO.getData().getTaskId();
        }
        ByteStatusEnum byteStatusEnum = ByteStatusEnum.fromStatus(status);
        if (status == ByteStatusEnum.BLOCKLIST_INTERCEPTED.getStatus()) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "一键海报失败", "提示词命中了版权。ERRORInfo=" + byteStatusEnum.getMessage());
            return String.valueOf(status);
        }
        if (byteStatusEnum != null) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "一键海报失败", "ERRORInfo=" + byteStatusEnum.getMessage());
        }
        return null;
    }

    /**
     * 获取海报生成任务结果
     * 根据任务ID查询一键海报生成的结果
     * 
     * @param taskId 海报生成任务ID
     * @return 海报生成结果对象，失败返回null
     * @throws Exception 调用过程中可能抛出的异常
     */
    public static PosterGetResBO getJimengTaskResult(String taskId) throws Exception {
        // 配置海报logo信息
        JSONObject logoInfo = new JSONObject();
        logoInfo.put("add_logo", false);
        logoInfo.put("position", 0);
        logoInfo.put("language", 0);
        logoInfo.put("opacity", 0.3);
        logoInfo.put("logo_text_content", "点点设计");

        // 构建请求参数
        JSONObject reqJson = new JSONObject();
        reqJson.put("logo_info", logoInfo);
        reqJson.put("return_url", true);

        JSONObject finalJson = new JSONObject();
        finalJson.put("req_key", "high_aes_general_v21_L");
        finalJson.put("task_id", taskId);
        finalJson.put("req_json", reqJson.toString());

        // 从字典表获取字节API密钥
        String[] apiKeys = getByteApiKeys();
        
        // 调用API获取结果
        PosterApiUtil posterApiUtil = new PosterApiUtil(apiKeys[0], apiKeys[1]);
        PosterGetResBO posterGetResBO = posterApiUtil.getJimengTaskResult(finalJson.toString().getBytes());
        if (posterGetResBO == null) {
            return null;
        }
        if (posterGetResBO.getStatus() == ByteStatusEnum.SUCCESS.getStatus()) {
            return posterGetResBO;
        }
        ByteStatusEnum byteStatusEnum = ByteStatusEnum.fromStatus(posterGetResBO.getStatus());
        if (byteStatusEnum != null) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "获取海报失败", "ERRORInfo=" + byteStatusEnum.getMessage());
        }
        return null;
    }


}
