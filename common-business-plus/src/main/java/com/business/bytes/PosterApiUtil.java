package com.business.bytes;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.business.bytes.model.PosterGetResBO;
import com.business.bytes.model.PosterPostResBO;
import com.nacos.tool.BrotliInterceptor;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;


@Slf4j
@Schema(title = "即梦生成海报图片 -- api工具类")
public class PosterApiUtil {

    @Getter
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    private static final BitSet URLENCODER = new BitSet(256);
    private static final String CONST_ENCODE = "0123456789ABCDEF";
    public static final Charset UTF_8 = StandardCharsets.UTF_8;

    private final String region;
    private final String service;
    private final String schema;
    private final String host;
    private final String path;
    private final String ak;
    private final String sk;

    static {
        int i;
        for (i = 97; i <= 122; ++i) {
            URLENCODER.set(i);
        }
        for (i = 65; i <= 90; ++i) {
            URLENCODER.set(i);
        }
        for (i = 48; i <= 57; ++i) {
            URLENCODER.set(i);
        }
        URLENCODER.set('-');
        URLENCODER.set('_');
        URLENCODER.set('.');
        URLENCODER.set('~');
    }

    public PosterApiUtil(String accessKey, String secretKey) {
        this.region = "cn-north-1";
        this.service = "cv";
        this.host = "visual.volcengineapi.com";
        this.schema = "https";
        this.path = "/";
        this.ak = accessKey;
        this.sk = secretKey;
    }

    /**
     * 即梦生成海报图片
     * @param body
     * @return
     * @throws Exception
     */
    public PosterPostResBO postJimengTextToImageGenerations(byte[] body) throws Exception {
        String xContentSha256 = hashSHA256(body);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String xDate = sdf.format(new Date());
        String shortXDate = xDate.substring(0, 8);
        String contentType = "application/json";

        String signHeader = "host;x-date;x-content-sha256;content-type";

        SortedMap<String, String> realQueryList = new TreeMap<>(new HashMap<>());
        realQueryList.put("Action", "CVSync2AsyncSubmitTask");
        realQueryList.put("Version", "2022-08-31");

        String method = "POST";
        StringBuilder querySB = new StringBuilder();
        for (Map.Entry<String, String> entry : realQueryList.entrySet()) {
            querySB.append(signStringEncoder(entry.getKey()))
                    .append("=")
                    .append(signStringEncoder(entry.getValue()))
                    .append("&");
        }
        querySB.deleteCharAt(querySB.length() - 1);

        String canonicalStringBuilder = method + "\n" + path + "\n" + querySB + "\n" +
                "host:" + host + "\n" +
                "x-date:" + xDate + "\n" +
                "x-content-sha256:" + xContentSha256 + "\n" +
                "content-type:" + contentType + "\n" +
                "\n" +
                signHeader + "\n" +
                xContentSha256;

        String hashcanonicalString = hashSHA256(canonicalStringBuilder.getBytes());
        String credentialScope = shortXDate + "/" + region + "/" + service + "/request";
        String signString = "HMAC-SHA256" + "\n" + xDate + "\n" + credentialScope + "\n" + hashcanonicalString;

        byte[] signKey = genSigningSecretKeyV4(sk, shortXDate, region, service);
        String signature = HexFormat.of().formatHex(hmacSHA256(signKey, signString));

        HttpUrl url = new HttpUrl.Builder()
                .scheme(schema)
                .host(host)
                .addPathSegment(path)
                .addQueryParameter("Action", "CVSync2AsyncSubmitTask") //CVProcess
                .addQueryParameter("Version", "2022-08-31")
                .build();

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .addHeader("Host", host)
                .addHeader("X-Date", xDate)
                .addHeader("X-Content-Sha256", xContentSha256)
                .addHeader("Content-Type", contentType)
                .addHeader("Authorization", "HMAC-SHA256" +
                        " Credential=" + ak + "/" + credentialScope +
                        ", SignedHeaders=" + signHeader +
                        ", Signature=" + signature);

        RequestBody requestBody = RequestBody.create(body, MediaType.get(contentType));
        requestBuilder.method(method, requestBody);

        Request request = requestBuilder.build();
        try (Response response = client.newCall(request).execute()) {
            int responseCode = response.code();
            String responseBody = response.body() != null ? response.body().string() : null;
            log.info("生成海报 responseCode={}", responseCode);
            log.info("生成海报 responseBody={}", responseBody);
            if (responseCode == 200 || responseCode == 400) {
                return JSON.parseObject(responseBody, new TypeReference<PosterPostResBO>() {
                });
            }
        }
        return null;
    }

    /**
     * 即梦获取海报结果
     * @param body
     * @return
     * @throws Exception
     */
    public PosterGetResBO getJimengTaskResult(byte[] body) throws Exception {
        String xContentSha256 = hashSHA256(body);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String xDate = sdf.format(new Date());
        String shortXDate = xDate.substring(0, 8);
        String contentType = "application/json";

        String signHeader = "host;x-date;x-content-sha256;content-type";

        SortedMap<String, String> realQueryList = new TreeMap<>(new HashMap<>());
        realQueryList.put("Action", "CVSync2AsyncGetResult");
        realQueryList.put("Version", "2022-08-31");

        String method = "POST";
        StringBuilder querySB = new StringBuilder();
        for (Map.Entry<String, String> entry : realQueryList.entrySet()) {
            querySB.append(signStringEncoder(entry.getKey()))
                    .append("=")
                    .append(signStringEncoder(entry.getValue()))
                    .append("&");
        }
        querySB.deleteCharAt(querySB.length() - 1);

        String canonicalStringBuilder = method + "\n" + path + "\n" + querySB + "\n" +
                "host:" + host + "\n" +
                "x-date:" + xDate + "\n" +
                "x-content-sha256:" + xContentSha256 + "\n" +
                "content-type:" + contentType + "\n" +
                "\n" +
                signHeader + "\n" +
                xContentSha256;

        String hashcanonicalString = hashSHA256(canonicalStringBuilder.getBytes());
        String credentialScope = shortXDate + "/" + region + "/" + service + "/request";
        String signString = "HMAC-SHA256" + "\n" + xDate + "\n" + credentialScope + "\n" + hashcanonicalString;

        byte[] signKey = genSigningSecretKeyV4(sk, shortXDate, region, service);
        String signature = HexFormat.of().formatHex(hmacSHA256(signKey, signString));

        HttpUrl url = new HttpUrl.Builder()
                .scheme(schema)
                .host(host)
                .addPathSegment(path)
                .addQueryParameter("Action", "CVSync2AsyncGetResult")
                .addQueryParameter("Version", "2022-08-31")
                .build();

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .addHeader("Host", host)
                .addHeader("X-Date", xDate)
                .addHeader("X-Content-Sha256", xContentSha256)
                .addHeader("Content-Type", contentType)
                .addHeader("Authorization", "HMAC-SHA256" +
                        " Credential=" + ak + "/" + credentialScope +
                        ", SignedHeaders=" + signHeader +
                        ", Signature=" + signature);

        RequestBody requestBody = RequestBody.create(body, MediaType.get(contentType));
        requestBuilder.method(method, requestBody);

        Request request = requestBuilder.build();
        try (Response response = client.newCall(request).execute()) {
            int responseCode = response.code();
            String responseBody = response.body() != null ? response.body().string() : null;
            log.info("获取海报结果 responseCode={}", responseCode);
            log.info("获取海报结果 responseBody={}", responseBody);
            if (responseCode == 200 || responseCode == 400) {
                return JSON.parseObject(responseBody, new TypeReference<PosterGetResBO>() {
                });
            }
        }
        return null;
    }

    public static String hashSHA256(byte[] content) throws Exception {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            return HexFormat.of().formatHex(md.digest(content));
        } catch (Exception e) {
            throw new Exception("Unable to compute hash while signing request: " + e.getMessage(), e);
        }
    }

    public static byte[] hmacSHA256(byte[] key, String content) throws Exception {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(key, "HmacSHA256"));
            return mac.doFinal(content.getBytes());
        } catch (Exception e) {
            throw new Exception("Unable to calculate a request signature: " + e.getMessage(), e);
        }
    }

    private byte[] genSigningSecretKeyV4(String secretKey, String date, String region, String service) throws Exception {
        byte[] kDate = hmacSHA256((secretKey).getBytes(), date);
        byte[] kRegion = hmacSHA256(kDate, region);
        byte[] kService = hmacSHA256(kRegion, service);
        return hmacSHA256(kService, "request");
    }

    private String signStringEncoder(String source) {
        StringBuilder builder = new StringBuilder(source.length());
        ByteBuffer bb = UTF_8.encode(source);
        while (bb.hasRemaining()) {
            int b = bb.get() & 255;
            if (URLENCODER.get(b)) {
                builder.append((char) b);
            } else if (b == 32) {
                builder.append("%20");
            } else {
                builder.append("%");
                char hex1 = CONST_ENCODE.charAt(b >> 4);
                char hex2 = CONST_ENCODE.charAt(b & 15);
                builder.append(hex1);
                builder.append(hex2);
            }
        }
        return builder.toString();
    }
}
