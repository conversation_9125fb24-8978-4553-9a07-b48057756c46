package com.business.bytes.model;

import lombok.Data;

import java.util.List;

//byte api视频接口参数
@Data
public class ByteVideo3dBO {

    private String imageData;//模板base64

    private Integer mode;
    private Integer longSide;
    private Integer frameNum; //视频帧数
    private Integer fps; //视频帧率
    private Integer useFlow; //是否叠加
    private List<Float> speedShift; //视频曲线变速

    public static ByteVideo3dBO initByteVideo3d(String imageData, Integer mode, Integer longSide, Integer frameNum, Integer fps) {
        ByteVideo3dBO sdToSketchBO = new ByteVideo3dBO();
        sdToSketchBO.imageData = imageData;
        sdToSketchBO.mode = mode;
        sdToSketchBO.longSide = (longSide == null ? 1920 : longSide);
        sdToSketchBO.frameNum = (frameNum == null ? 90 : frameNum);
        sdToSketchBO.fps = (fps == null ? 30 : fps);
        return sdToSketchBO;
    }


}
