package com.business.bytes.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "即梦海报请求参数", description = "即梦海报请求参数")
public class PosterReqBO {

    @Schema(title = "取固定值为high_aes_general_v21_L")
    @JSONField(name = "req_key")
    private String reqkey;

    @Schema(title = "提示词 json结构")
    @JSONField(name = "prompt")
    private String prompt;

    @Schema(title = "模型版本名称，固定值：general_v2.1_L")
    @JSONField(name = "model_version")
    private String modelVersion;

    @Schema(title = "默认值：general_v20_9B_pe\n" +
            "标准版：general_v20_9B_rephraser\n" +
            "美感版：general_v20_9B_pe")
    @JSONField(name = "req_schedule_conf")
    private String reqScheduleConf;

    @JSONField(name = "seed")
    private Integer seed;

    @Schema(title = "影响文本描述的程度：默认值：3.5\n" +
            "取值范围：[1, 10]")
    @JSONField(name = "scale")
    private Float scale;

    @Schema(title = "生成图像的步数：默认值：25\n" +
            "取值范围：[1, 200]\n" +
            "推荐取值范围：[1, 50]")
    @JSONField(name = "ddim_steps")
    private Integer ddimSteps;

    @JSONField(name = "width")
    private Integer width;

    @JSONField(name = "height")
    private Integer height;

    @Schema(title = "开启文本扩写")
    @JSONField(name = "use_pre_llm")
    private boolean usePreLlm;

    @Schema(title = "True：文生图+AIGC超分\n" +
            "False：文生图")
    @JSONField(name = "use_sr")
    private boolean useSr;

    @Schema(title = "输出是否返回图片链接 （链接有效期为24小时）")
    @JSONField(name = "return_url")
    private boolean returnRrl;

    @Schema(title = "水印信息")
    @JSONField(name = "logo_info")
    private LogoInfo logoInfo;

    @Data
    public static class LogoInfo {
        @Schema(title = "是否添加水印。True为添加，False不添加。默认不添加")
        @JSONField(name = "add_logo")
        private boolean addLogo;

        @Schema(title = "水印的位置:0-右下角\n" +
                "1-左下角\n" +
                "2-左上角\n" +
                "3-右上角\n" +
                "默认0")
        @JSONField(name = "position")
        private Integer position;

        @Schema(title = "水印的语言，取值如下：\n" +
                "0-中文（AI生成）\n" +
                "1-英文（Generated by AI）")
        @JSONField(name = "language")
        private Integer language;

        @Schema(title = "水印的不透明度，取值范围0-1，1表示完全不透明，默认0.3")
        @JSONField(name = "opacity")
        private Float opacity;

        @Schema(title = "明水印自定义内容")
        @JSONField(name = "logo_text_content")
        private String logoTextContent;
    }

}
