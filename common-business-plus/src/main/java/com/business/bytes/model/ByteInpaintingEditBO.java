package com.business.bytes.model;

import lombok.Data;

//byte api抹除接口参数
@Data
public class ByteInpaintingEditBO {

    private String customPrompt;//提示词
    private String imageData;//模板base64
    private String maskData;//区域base64
    private Integer steps; //采样步数，生成图像的精细程度
    private Boolean returnUrl;//是否返回图片链接
    private Float scale;//文本描述的程度
    private Integer seed;//随机种子值[ 0 .. 4294967294 ]
    private LogoInfo logoInfo;//水印信息

    public static ByteInpaintingEditBO initByteInpaintingEdit(String imageData, String maskData,
                                                              String customPrompt, Integer steps, Float scale) {
        ByteInpaintingEditBO byteInpaintingEditBO = new ByteInpaintingEditBO();
        byteInpaintingEditBO.customPrompt = customPrompt;
        byteInpaintingEditBO.imageData = imageData;
        byteInpaintingEditBO.maskData = maskData;
        byteInpaintingEditBO.steps = (steps == null ? 25 : steps);
        byteInpaintingEditBO.scale = (scale == null ? 5 : scale);
        return byteInpaintingEditBO;
    }

    @Data
    static class LogoInfo {

    }


}
