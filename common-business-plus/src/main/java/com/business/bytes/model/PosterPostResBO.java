package com.business.bytes.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(title = "Byte图片生成海报图片响应参数")
@Data
public class PosterPostResBO {

    @Schema(title = "结果状态码")
    @JSONField(name = "code")
    private Integer code;

    @Schema(title = "json结构")
    @JSONField(name = "data")
    private DataObj data;

    @Schema(title = "当请求失败时返回此字符串")
    @JSONField(name = "message")
    private String message;

    @Schema(title = "请求的唯一ID")
    @JSONField(name = "request_id")
    private String requestId;

    @JSONField(name = "status")
    private Integer status;

    @JSONField(name = "time_elapsed")
    private String timeElapsed;

    @Data
    public static class DataObj {

        @JSONField(name = "task_id")
        private String taskId;

    }

}
