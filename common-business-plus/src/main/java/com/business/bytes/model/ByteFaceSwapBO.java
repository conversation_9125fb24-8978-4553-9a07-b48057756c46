package com.business.bytes.model;

import lombok.Data;

import java.util.ArrayList;

//byte api视频接口参数
@Data
public class ByteFaceSwapBO {

    private ArrayList<String> binaryDataBase64; //模板base64《2选1》
    private ArrayList<String> imageUrls; //模板url 《2选1》
    private String faceType;
    private Integer location; //指定素材图中序号
    private Integer templateLocation; //指定模板图中序号
    private Boolean doRisk; //是否需要审核
    private String sourceSimilarity; //人脸相似度: 范围[0~1]，越大越相似.
    private Double gpen; //高清效果,支持[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1]

    public ByteFaceSwapBO() {}

    public ByteFaceSwapBO(ArrayList<String> binaryDataBase64, ArrayList<String> imageUrls, Integer location, Integer templateLocation) {
        this.binaryDataBase64 = binaryDataBase64;
        this.imageUrls = imageUrls;
        this.faceType = "l2r";
        this.location = location;
        this.templateLocation = templateLocation;
        this.doRisk = false;
        this.sourceSimilarity = "1";
        this.gpen = 1d;
    }
}
