package com.business.bytes.model;

import lombok.Data;

@Data
public class ByteOutpaintingBO {

    private String imageData; //base64
    private String customPrompt; //提示词
    private Integer steps; //生成图像的精细程度，越大效果可能更好，但相应的耗时会剧增
    private Float strength; //越小越接近原图，越大越接近文本控制，如果设成0就和原图一模一样
    private Float scale; //影响文本描述的程度
    private Integer seed = 0; //随机种子
    private Float top; //向上扩展比例
    private Float bottom; //向下扩展比例
    private Float left; //向左扩展比例
    private Float right; //向右扩展比例
    private Integer maxHeight = 4096;
    private Integer maxWidth = 4096;


    public ByteOutpaintingBO() {
    }

    public ByteOutpaintingBO(String imageData, Float top, Float bottom, Float left, Float right) {
        this.imageData = imageData;
        this.customPrompt = (customPrompt == null ? "四边扩展" : customPrompt);
        this.steps = (steps == null ? 50 : steps);
        this.strength = (strength == null ? 0.4f : strength);
        this.scale = (scale == null ? 6 : scale);
        this.top = (top == null ? 0 : top);
        this.bottom = (bottom == null ? 0 : bottom);
        this.left = (left == null ? 0 : left);
        this.right = (right == null ? 0 : right);
        this.maxHeight = maxHeight;
        this.maxWidth = maxWidth;
    }
}
