package com.business.bytes.model;

import lombok.Data;

//byte api抹除接口参数
@Data
public class ByteInpaintingBO {

    private String imageData;//模板base64
    private String maskData;//区域base64
    private Integer steps; //采样步数，生成图像的精细程度
    private Float strength;//如果设成0就和原图一模一样
    private Float scale;//文本描述的程度
    private Integer seed;//随机种子值[ 0 .. 4294967294 ]

    public static ByteInpaintingBO initByteInpainting(String imageData, String maskData, Integer steps, Float strength, Float scale) {
        ByteInpaintingBO sdToSketchBO = new ByteInpaintingBO();
        sdToSketchBO.imageData = imageData;
        sdToSketchBO.maskData = maskData;
        sdToSketchBO.steps = (steps == null ? 30 : steps);
        sdToSketchBO.strength = (strength == null ? 0.8F : strength);
        sdToSketchBO.scale = (scale == null ? 7F : scale);
        return sdToSketchBO;
    }


}
