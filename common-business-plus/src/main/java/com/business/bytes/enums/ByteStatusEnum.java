package com.business.bytes.enums;

import lombok.Getter;

@Getter
public enum ByteStatusEnum {

    SUCCESS(10000, "请求成功"),
    IMAGE_AUDIT_FAILED(50411, "输入图片审核未通过"),
    IMAGE_AUDIT_FAILED_50511(50511, "输入图片审核未通过"),
    TEXT_AUDIT_FAILED(50412, "输入文本审核未通过"),
    TEXT_AUDIT_FAILED_50512(50512, "输入文本审核未通过"),
    BLOCKLIST_INTERCEPTED(50413, "输入文本NER、IP、Blocklist等拦截");

    private final int status;
    private final String message;

    ByteStatusEnum(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public static ByteStatusEnum fromStatus(int status) {
        for (ByteStatusEnum ps : ByteStatusEnum.values()) {
            if (ps.getStatus() == status) {
                return ps;
            }
        }
        return null;
    }

}
