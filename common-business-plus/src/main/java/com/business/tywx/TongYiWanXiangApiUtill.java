package com.business.tywx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.business.aigc.tusiart.TusiJobStateEnum;
import com.business.tywx.enums.TongYiWanXiangEnums;
import com.business.tywx.model.TongYiWanXiangVideoRequestBO;
import com.business.tywx.model.TongYiWanXiangVideoResponseBO;
import com.nacos.utils.BFeiShuUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Schema(title = "通义万相API工具类")
public class TongYiWanXiangApiUtill {

    /**
     * 创建首尾帧生视频任务
     *
     * @param requestBO 请求参数
     * @param apiKey    API密钥
     * @return 任务ID，如果创建失败则返回null
     */
    public static String createKeyframeToVideoTask(TongYiWanXiangVideoRequestBO requestBO, String apiKey) {
        String taskId = TongYiWanXiangApis.createKeyframeToVideoTask(requestBO, apiKey);
        if (taskId == null) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "通义万相首尾帧生视频任务创建失败", 
                    "请求参数: " + JSON.toJSONString(requestBO));
        }
        return taskId;
    }

    /**
     * 获取首尾帧生视频任务结果
     *
     * @param taskId 任务ID
     * @param apiKey API密钥
     * @return 视频URL或状态（RUNNING，FAILED）
     */
    public static String getKeyframeToVideoTaskResult(String taskId, String apiKey) {
        TongYiWanXiangVideoResponseBO responseBO = TongYiWanXiangApis.getKeyframeToVideoTaskResult(taskId, apiKey);
        
        // 查询失败
        if (responseBO == null) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "通义万相首尾帧生视频任务结果查询失败", "taskId=" + taskId);
            return TusiJobStateEnum.FAILED.getState();
        }
        
        // 解析任务状态
        TongYiWanXiangVideoResponseBO.Output output = responseBO.getOutput();
        if (output == null) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "通义万相首尾帧生视频任务结果异常: output为空", "taskId=" + taskId);
            return TusiJobStateEnum.FAILED.getState();
        }
        
        String taskStatus = output.getTask_status();
        
        // 任务失败
        if (TongYiWanXiangEnums.TaskStatus.FAILED.getStatus().equals(taskStatus)) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "通义万相首尾帧生视频任务执行失败", 
                    "taskId=" + taskId + ", 错误码=" + output.getCode() + ", 错误信息=" + output.getMessage());
            return TusiJobStateEnum.FAILED.getState();
        }
        
        // 任务还在进行中
        if (!TongYiWanXiangEnums.TaskStatus.SUCCEEDED.getStatus().equals(taskStatus)) {
            return TusiJobStateEnum.RUNNING.getState();
        }
        
        // 任务成功，返回视频URL
        if (output.getVideo_url() != null && !output.getVideo_url().isEmpty()) {
            log.info("通义万相首尾帧生视频任务成功: taskId={}, videoUrl={}", taskId, output.getVideo_url());
            return output.getVideo_url();
        } else {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "通义万相首尾帧生视频任务异常: 任务成功但无视频URL", "taskId=" + taskId);
            return TusiJobStateEnum.FAILED.getState();
        }
    }
}
