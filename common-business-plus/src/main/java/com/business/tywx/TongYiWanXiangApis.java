package com.business.tywx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.business.tywx.model.TongYiWanXiangVideoRequestBO;
import com.business.tywx.model.TongYiWanXiangVideoResponseBO;
import com.nacos.tool.BrotliInterceptor;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Schema(title = "通义万相-首尾帧生视频Api")
public class TongYiWanXiangApis {

    private static final String DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com";
    private static final String TASK_CREATE_PATH = "/api/v1/services/aigc/image2video/video-synthesis";
    private static final String TASK_QUERY_PATH = "/api/v1/tasks/";
    
    private static OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    /**
     * 创建首尾帧生视频任务
     *
     * @param requestBO 请求参数
     * @param apiKey    API密钥
     * @return 创建任务响应信息
     */
    public static String createKeyframeToVideoTask(TongYiWanXiangVideoRequestBO requestBO, String apiKey) {
        log.info("通义万相首尾帧生视频请求参数: {}", JSONObject.toJSONString(requestBO));
        
        Request request = new Request.Builder()
                .url(DASHSCOPE_API_URL + TASK_CREATE_PATH)
                .post(RequestBody.create(JSON.toJSONString(requestBO), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + apiKey)
                .addHeader("X-DashScope-Async", "enable")
                .build();
                
        try (Response response = client.newCall(request).execute()) {
            if (response.body() == null) {
                log.error("通义万相首尾帧生视频任务创建失败: 响应体为空");
                return null;
            }
            
            String responseBody = response.body().string();
            log.info("通义万相首尾帧生视频任务创建返回: {}", responseBody);
            
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSON.parseObject(responseBody);
                JSONObject outputObject = jsonObject.getJSONObject("output");
                if (outputObject != null && "PENDING".equals(outputObject.getString("task_status"))) {
                    return outputObject.getString("task_id");
                }
            }
            
            log.error("通义万相首尾帧生视频任务创建失败: {}", responseBody);
            return null;
        } catch (IOException e) {
            log.error("通义万相首尾帧生视频任务创建异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查询首尾帧生视频任务结果
     *
     * @param taskId 任务ID
     * @param apiKey API密钥
     * @return 任务结果响应信息
     */
    public static TongYiWanXiangVideoResponseBO getKeyframeToVideoTaskResult(String taskId, String apiKey) {
        log.info("查询通义万相首尾帧生视频任务结果: taskId={}", taskId);
        
        Request request = new Request.Builder()
                .url(DASHSCOPE_API_URL + TASK_QUERY_PATH + taskId)
                .get()
                .addHeader("Authorization", "Bearer " + apiKey)
                .build();
                
        try (Response response = client.newCall(request).execute()) {
            if (response.body() == null) {
                log.error("查询通义万相首尾帧生视频任务结果失败: 响应体为空");
                return null;
            }
            
            String responseBody = response.body().string();
            log.info("查询通义万相首尾帧生视频任务结果返回: {}", responseBody);
            
            if (response.isSuccessful()) {
                return JSON.parseObject(responseBody, TongYiWanXiangVideoResponseBO.class);
            }
            
            log.error("查询通义万相首尾帧生视频任务结果失败: {}", responseBody);
            return null;
        } catch (IOException e) {
            log.error("查询通义万相首尾帧生视频任务结果异常: {}", e.getMessage(), e);
            return null;
        }
    }
}
