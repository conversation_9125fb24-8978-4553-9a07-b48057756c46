package com.business.tywx.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Schema(description = "通义万相状态枚举")
public class TongYiWanXiangEnums {

    @Getter
    @AllArgsConstructor
    public enum TaskStatus {
        PENDING("PENDING", "任务排队中"),
        RUNNING("RUNNING", "任务处理中"),
        SUSPENDED("SUSPENDED", "任务挂起"),
        SUCCEEDED("SUCCEEDED", "任务执行成功"),
        FAILED("FAILED", "任务执行失败"),
        UNKNOWN("UNKNOWN", "任务不存在或状态未知");

        private final String status;
        private final String desc;

        public static TaskStatus getByStatus(String status) {
            return Arrays.stream(values())
                    .filter(item -> item.getStatus().equals(status))
                    .findFirst()
                    .orElse(UNKNOWN);
        }
    }

    @Getter
    @AllArgsConstructor
    public enum ErrorCode {
        INVALID_PARAMETER(400, "InvalidParameter", "请求参数不合法"),
        IP_INFRINGEMENT_SUSPECT(400, "IPInfringementSuspect", "输入数据(如提示词或图像)涉嫌知识产权侵权"),
        DATA_INSPECTION_FAILED(400, "DataInspectionFailed", "输入数据(如提示词或图像)可能包含敏感内容"),
        INTERNAL_ERROR(500, "InternalError", "服务异常");

        private final int httpStatus;
        private final String code;
        private final String desc;

        private static final Map<String, ErrorCode> CODE_MAP = new HashMap<>();

        static {
            for (ErrorCode errorCode : values()) {
                CODE_MAP.put(errorCode.getCode(), errorCode);
            }
        }

        public static ErrorCode getByCode(String code) {
            return CODE_MAP.getOrDefault(code, INTERNAL_ERROR);
        }

        public static String getErrorMessage(String code) {
            ErrorCode errorCode = getByCode(code);
            return errorCode.getDesc();
        }
    }
} 