package com.business.tywx.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "通义万相首尾帧生视频请求参数")
public class TongYiWanXiangVideoRequestBO {

    @Schema(description = "模型名称", example = "wanx2.1-kf2v-plus")
    private String model = "wanx2.1-kf2v-plus";

    @Schema(description = "输入的基本信息")
    private Input input;

    @Schema(description = "视频处理参数")
    private Parameters parameters;

    @Data
    @Schema(description = "输入参数")
    public static class Input {
        
        @Schema(description = "文本提示词，支持中英文，长度不超过800个字符", required = true)
        private String prompt;
        
        @Schema(description = "首帧图片URL", required = true)
        private String first_frame_url;
        
        @Schema(description = "尾帧图片URL", required = true)
        private String last_frame_url;
    }

    @Data
    @Schema(description = "视频处理参数")
    public static class Parameters {
        
        @Schema(description = "生成视频的分辨率档位，默认720P，当前仅支持720P")
        private String resolution = "720P";
        
        @Schema(description = "生成视频的时长，默认5秒，当前仅支持5秒固定时长生成")
        private Integer duration = 5;
        
        @Schema(description = "是否开启prompt智能改写，默认开启")
        private Boolean prompt_extend = true;
        
        @Schema(description = "随机数种子，用于控制模型生成内容的随机性，取值范围[0, 2147483647]")
        private Integer seed;
    }
} 