package com.business.tywx.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "通义万相首尾帧生视频响应参数")
public class TongYiWanXiangVideoResponseBO {

    @Schema(description = "请求唯一标识")
    private String request_id;

    @Schema(description = "任务输出信息")
    private Output output;

    @Schema(description = "输出信息统计")
    private Usage usage;

    @Data
    @Schema(description = "任务输出信息")
    public static class Output {
        
        @Schema(description = "任务ID")
        private String task_id;
        
        @Schema(description = "任务状态：PENDING(任务排队中), RUNNING(任务处理中), SUSPENDED(任务挂起), SUCCEEDED(任务执行成功), FAILED(任务执行失败), UNKNOWN(任务不存在或状态未知)")
        private String task_status;
        
        @Schema(description = "任务提交时间")
        private String submit_time;
        
        @Schema(description = "任务执行时间")
        private String scheduled_time;
        
        @Schema(description = "任务完成时间")
        private String end_time;
        
        @Schema(description = "视频URL，URL有效期为24小时")
        private String video_url;
        
        @Schema(description = "原始的输入prompt")
        private String orig_prompt;
        
        @Schema(description = "开启prompt智能改写后实际使用的prompt")
        private String actual_prompt;
        
        @Schema(description = "请求失败的错误码")
        private String code;
        
        @Schema(description = "请求失败的详细信息")
        private String message;
    }

    @Data
    @Schema(description = "输出信息统计")
    public static class Usage {
        
        @Schema(description = "生成视频的时长，单位秒")
        private Integer video_duration;
        
        @Schema(description = "生成视频的数量，固定为1")
        private Integer video_count;
        
        @Schema(description = "生成视频的比例，固定为standard")
        private String video_ratio;
    }
} 