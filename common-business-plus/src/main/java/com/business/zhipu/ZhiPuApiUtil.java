package com.business.zhipu;

import com.alibaba.fastjson2.JSON;
import com.business.aigc.tusiart.TusiJobStateEnum;
import com.business.db.model.po.ImgDrawDetlPO;
import com.business.model.po.ImgDrawRecordPO;
import com.business.utils.BOssUtil;
import com.business.zhipu.model.ZhiPuRequestBO;
import com.business.zhipu.model.ZhiPuResponseBO;
import com.nacos.config.OssClientConfig;
import com.nacos.utils.BFeiShuUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Schema(title = "智谱视频 api工具类")
public class ZhiPuApiUtil {

    public static final String STATUS_PROCESSING = "PROCESSING";// 处理中
    public static final String STATUS_SUCCESS = "SUCCESS";// 完成
    public static final String STATUS_FAIL = "FAIL"; // 失败


    /**
     * 智谱视频生成
     */
    public static String postZhiPuTextToVideoGenerations(ZhiPuRequestBO zhiPuRequestBO, String token) {
        ZhiPuResponseBO zhiPuResponseBO = ZhiPuApis.postZhiPuTextToVideoGenerations(zhiPuRequestBO, token);
        if (zhiPuResponseBO != null) {
            return zhiPuResponseBO.getId();
        }
        return null;
    }

    /**
     * 获取智谱任务结果
     */
    public static String getZhiPuVideoTaskResult(String taskId, String token, ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO) {
        ZhiPuResponseBO zhiPuResponseBO = ZhiPuApis.getZhiPuVideoTaskResult(taskId, token);
        if (zhiPuResponseBO == null) {
            return TusiJobStateEnum.FAILED.getState();
        }
        if (Objects.equals(zhiPuResponseBO.getTaskStatus(), STATUS_PROCESSING)) {
            return TusiJobStateEnum.RUNNING.getState();
        }
        if (Objects.equals(zhiPuResponseBO.getTaskStatus(), STATUS_FAIL)) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "智谱视频拉取任务失败", "getZhiPuVideoTaskResult.resp " + JSON.toJSONString(zhiPuResponseBO) + " jobId=" + taskId + " prompt= " + imgDrawRecordPO.getPromptInit());
            return TusiJobStateEnum.FAILED.getState();
        }
        if (Objects.equals(zhiPuResponseBO.getTaskStatus(), STATUS_SUCCESS)) {
            String url = zhiPuResponseBO.getVideoResult().get(0).getUrl();
            imgDrawDetlPO.setAudioUrl(url);
            log.info("智谱视频原地址= " + url);
            return uploadLumaVideo(url, taskId, 14, OssClientConfig.FILE_SUFFIX_VIDEO);
        }
        return TusiJobStateEnum.FAILED.getState();
    }


    // TODO 上传视频到oss
    public static String uploadLumaVideo(String fileUrl, String videoJobId, Integer folder, String suffix) {
        if (fileUrl == null) return null;
        int maxRetries = 6; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadURL(fileUrl, videoJobId, folder, suffix);
                if (videoPath != null) {
                    return videoPath;
                }
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                }
                log.error("智谱视频上传失败，重试次数：" + (retryCount + 1) + " videoJobId=" + videoJobId);

            } catch (Exception e) {
                log.error("智谱视频上传视频到OSS发生异常，重试次数：" + (retryCount + 1));
            }
            retryCount++;
        }
        log.error("智谱视频文件上传失败，超过最大重试次数， 视频id=" + videoJobId);
        return null;
    }


}
