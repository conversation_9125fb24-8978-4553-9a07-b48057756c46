package com.business.zhipu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.business.zhipu.model.ZhiPuRequestBO;
import com.business.zhipu.model.ZhiPuResponseBO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.tool.BrotliInterceptor;
import com.nacos.utils.BFeiShuUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.util.concurrent.TimeUnit;


@Slf4j
@Schema(title = "智谱视频Api")
public class ZhiPuApis {

    private static final String ZHIPU_API_KEY = "c65ba6b00fef7c06272d8527349afcae.lXJyA6lO2ULoMOPD";
    private static final String ZHIPU_API_URL = "https://open.bigmodel.cn";

    @Getter
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    // TODO 视频生成 ---------- API
    public static ZhiPuResponseBO postZhiPuTextToVideoGenerations(ZhiPuRequestBO zhiPuRequestBO, String apiKey) {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(zhiPuRequestBO, JSONWriter.Feature.WriteMapNullValue));
        log.error("智谱AI请求参数：{}", JSONObject.toJSONString(zhiPuRequestBO));
        Request request = new Request.Builder()
                .url(ZHIPU_API_URL + "/api/paas/v4/videos/generations")
                .post(RequestBody.create(jsonObject.toString(), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("Authorization", "Bearer " + (apiKey == null || apiKey.isEmpty() ? ZHIPU_API_KEY : apiKey))
                .build();
        int maxRetryCount = 3;  // 最大重试次数
        int retryCount = 0;  // 当前重试次数
        long retryInterval = 2000L;  // 重试间隔，单位：毫秒
        while (retryCount < maxRetryCount) {
            try (var response = client.newCall(request).execute()) {
                log.error("智谱AI请求状态码Code：" + response.code());
                assert response.body() != null;
                String responseBody = response.body().string();
                System.out.println("====智谱AI=" + responseBody);
                if (response.isSuccessful()) {
                    return JSON.parseObject(responseBody, new TypeReference<ZhiPuResponseBO>() {
                    });
                }
                if (responseBody.contains("error")) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    JsonNode rootNode = objectMapper.readTree(responseBody);
                    int code = rootNode.path("error").path("code").asInt();
                    if (code == 1113) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "智谱AI，您的账户已欠费，请充值后重试", "ERRORInfo=" + responseBody);
                    }
                }

                if (response.code() == 500 || response.code() == 429) {
                    retryCount++;
                    log.error("请求失败，正在重试... (重试次数: {}/{})", retryCount, maxRetryCount);
                    Thread.sleep(retryInterval);  // 等待一段时间再重试
                    continue;  // 重新发送请求
                }
            } catch (Exception e) {
                log.error("智谱AI请求失败：{}", e.getMessage());
                if (retryCount < maxRetryCount) {
                    retryCount++;
                    log.error("请求失败，正在重试... (重试次数: {}/{})", retryCount, maxRetryCount);
                    try {
                        Thread.sleep(retryInterval);  // 等待一段时间再重试
                    } catch (InterruptedException ex) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                    continue;
                }
            }
            break;
        }
        return null;
    }

    public static ZhiPuResponseBO getZhiPuVideoTaskResult(String taskId, String apiKey) {
        Request request = new Request.Builder()
                .url(ZHIPU_API_URL + "/api/paas/v4/async-result/" + taskId)
                .get()
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("Authorization", "Bearer " + (apiKey == null || apiKey.isEmpty() ? ZHIPU_API_KEY : apiKey))
                .build();

        int maxRetryCount = 3;  // 最大重试次数
        int retryCount = 0;  // 当前重试次数
        long retryInterval = 2000L;  // 重试间隔，单位：毫秒
        while (retryCount < maxRetryCount) {
            try (Response response = client.newCall(request).execute()) {
                log.info("智谱AI获取任务结果状态码Code：" + response.code());
                if (response.body() == null) {
                    log.error("响应体为空");
                    break;
                }
                String responseBody = response.body().string();
                log.info("==智谱AI获取任务结果=" + responseBody);
                if (response.isSuccessful()) {
                    return JSON.parseObject(responseBody, new TypeReference<ZhiPuResponseBO>() {
                    });
                }
                if (response.code() == 500 || response.code() == 429) {
                    retryCount++;
                    log.error("请求失败，正在重试... (重试次数: {}/{})", retryCount, maxRetryCount);
                    Thread.sleep(retryInterval);  // 等待一段时间再重试
                    continue;  // 重新发送请求
                }
            } catch (Exception e) {
                log.error("智谱AI拉取任务失败：{}", e.getMessage());
                if (retryCount < maxRetryCount) {
                    retryCount++;
                    log.error("请求失败，正在重试... (重试次数: {}/{})", retryCount, maxRetryCount);
                    try {
                        Thread.sleep(retryInterval);  // 等待一段时间再重试
                    } catch (InterruptedException ex) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                    continue;
                }
            }
            break;
        }
        return null;
    }

}
