package com.business.zhipu.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

//智谱AI-api视频请求接口参数
@Data
public class ZhiPuRequestBO {

    @JSONField(name = "model")
    private String model; //模型编码

    @JSONField(name = "prompt")
    private String prompt; //视频的文本描述

    @JSONField(name = "image_url")
    private String imageUrl; //图生视频，图片地址

    @JSONField(name = "request_id")
    private String requestId; //请求id

    @JSONField(name = "size")
    //图片尺寸，仅 cogview-3-plus 支持该参数。可选范围：[1024x1024,768x1344,864x1152,1344x768,1152x864,1440x720,720x1440]，默认是1024x1024。
    private String size;

    @JSONField(name = "user_id")
    private String userId; //用户id

    public ZhiPuRequestBO(String model, String prompt, String imageUrl) {
        this.model = model;
        this.prompt = prompt;
        this.imageUrl = imageUrl;
    }

    public ZhiPuRequestBO(String prompt, String imageUrl) {
        this.prompt = prompt;
        this.imageUrl = imageUrl;
    }

    public ZhiPuRequestBO() {
    }
}
