package com.business.zhipu.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

//智谱AI-响应实体接口参数
@Data
public class ZhiPuImgResponseBO {

    @JSONField(name = "created")
    private String created;

    @Schema(title = "视频生成结果")
    @JSONField(name = "data")
    private List<ImageUrl> data;

    @Data
    public static class ImageUrl {

        @Schema(title = "图片url")
        @JSONField(name = "url")
        private String url;

    }

}
