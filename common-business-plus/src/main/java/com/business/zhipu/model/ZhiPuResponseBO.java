package com.business.zhipu.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

//智谱AI-响应实体接口参数
@Data
public class ZhiPuResponseBO {

    @Schema(title = "任务id")
    @JSONField(name = "id")
    private String id;

    @Schema(title = "模型名称")
    @JSONField(name = "model")
    private String model;

    @Schema(title = "任务编号")
    @JSONField(name = "request_id")
    private String requestId;

    @Schema(title = "处理状态，" +
            "PROCESSING（处理中），" +
            "SUCCESS（成功），" +
            "FAIL（失败）")
    @JSONField(name = "task_status")
    private String taskStatus;

    @Schema(title = "视频生成结果")
    @JSONField(name = "video_result")
    private List<VideoResult> videoResult;

    @Data
    public static class VideoResult {

        @Schema(title = "视频url")
        @JSONField(name = "url")
        private String url;

        @Schema(title = "视频封面url")
        @JSONField(name = "cover_image_url")
        private String coverImageUrl;

    }

}
