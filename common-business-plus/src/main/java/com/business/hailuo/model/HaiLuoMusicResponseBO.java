package com.business.hailuo.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

//海螺音乐AI-响应实体接口参数
@Data
public class HaiLuoMusicResponseBO {

    @Schema(title = "data")
    @JSONField(name = "data")
    private MusicData data;

    @JSONField(name = "trace_id")
    private String traceId;

    @Schema(title = "状态码及其详情")
    @JSONField(name = "base_resp")
    private BaseResp baseResp;

    @Data
    public static  class MusicData {

        @Schema(title = "状态码1: 合成中；2: 已完成。")
        @JSONField(name = "status")
        private Integer status;

        @Schema(title = "音频数据")
        @JSONField(name = "audio")
        private String audio;

    }

    @Data
    public static  class BaseResp {

        @Schema(title = "状态码：错误码。接口常见错误码：" +
                "1001：超时，请重试；" +
                "1002：请求频率超限；" +
                "1004：api_key 未填写或错误；" +
                "1008：余额不足；" +
                "2013：用户填写参数错误，请检查输入；" +
                "2044：没有使用该接口的权限，请联系商务及售前")
        @JSONField(name = "status_code")
        private Integer statusCode;

        @Schema(title = "任务的状态消息")
        @JSONField(name = "status_msg")
        private String statusMsg;

    }

}
