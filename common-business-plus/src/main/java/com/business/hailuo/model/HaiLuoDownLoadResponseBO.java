package com.business.hailuo.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

//海螺AI-响应实体接口参数
@Data
public class HaiLuoDownLoadResponseBO {

    @Schema(title = "文件")
    @JSONField(name = "file")
    public DownLoadHaiLuoFile file;

    @Schema(title = "返回状态")
    @JSONField(name = "base_resp")
    public BaseResp base_resp;


    @Data
    static
    public class DownLoadHaiLuoFile {
        @Schema(title = "文件id")
        @JSONField(name = "file_id")
        private String fileId;

        @Schema(title = "字节大小")
        @JSONField(name = "bytes")
        private Integer bytes;

        @Schema(title = "创建时间")
        @JSONField(name = "created_at")
        private Integer createdAt;

        @Schema(title = "文件名字")
        @JSONField(name = "filename")
        private String filename;

        @Schema(title = "目标")
        @JSONField(name = "purpose")
        private String purpose;

        @Schema(title = "下载地址")
        @JSONField(name = "download_url")
        private String downloadUrl;
    }



    @Data
    static
    class BaseResp {
        @Schema(title = "状态码")
        @JSONField(name = "status_code")
        private Integer statusCode;

        @Schema(title = "任务的状态消息")
        @JSONField(name = "status_msg")
        private String statusMsg;
    }


}
