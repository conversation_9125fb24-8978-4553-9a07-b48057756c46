package com.business.hailuo.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

//海螺AI-api视频请求接口参数
@Data
public class HaiLuoRequestBO {

    @JSONField(name = "model")
    private String model; //模型编码

    @JSONField(name = "prompt")
    private String prompt; //视频的文本描述

    @JSONField(name = "prompt_optimizer")
    private Boolean promptOptimizer = true; //图生视频，图片地址

    @JSONField(name = "first_frame_image")
    private String firstFrameImage; //图片url 满足以下条件：格式为JPG/JPEG/PNG；长宽比大于2:5、小于5:2；短边像素大于300px；体积不大于20MB。

    public HaiLuoRequestBO() {}

    public HaiLuoRequestBO(String model, String prompt, String firstFrameImage) {
        this.model = model;
        this.prompt = prompt;
        this.firstFrameImage = firstFrameImage;
    }

    public HaiLuoRequestBO(String prompt, String firstFrameImage) {
        this.prompt = prompt;
        this.firstFrameImage = firstFrameImage;
    }
}
