package com.business.hailuo.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

//海螺AI-响应实体接口参数
@Data
public class HaiLuoResponseBO {

    @Schema(title = "任务id")
    @JSONField(name = "task_id")
    private String taskId;

    @Schema(title = "任务状态：包括以下状态：Processing-生成中 Success-成功 Fail-失败")
    @JSONField(name = "status")
    private String status;

    @Schema(title = "视频对应的文件ID")
    @JSONField(name = "file_id")
    private String fileId;

    @Schema(title = "状态码及其详情")
    @JSONField(name = "base_resp")
    private BaseResp baseResp;

    @Data
    public static  class BaseResp {

        @Schema(title = "状态码:" +
                "0，请求成功 " +
                "1002，触发限流，请稍后再试 " +
                "1004，账号鉴权失败，请检查 API-Key 是否填写正确 " +
                "1008，账号余额不足 1013，传入参数异常，请检查入参是否按要求填写 " +
                "1026，视频描述涉及敏感内容")
        @JSONField(name = "status_code")
        private Integer statusCode;

        @Schema(title = "任务的状态消息")
        @JSONField(name = "status_msg")
        private String statusMsg;

    }

}
