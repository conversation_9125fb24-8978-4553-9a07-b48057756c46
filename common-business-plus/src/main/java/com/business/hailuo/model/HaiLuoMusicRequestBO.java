package com.business.hailuo.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

//海螺AI-api视频请求接口参数
@Data
public class HaiLuoMusicRequestBO {

    @JSONField(name = "refer_voice")
    private String referVoice; //生成音乐时参考音色ID   refer_voice、refer_instrumental至少一个必填

    @JSONField(name = "refer_instrumental")
    private String referInstrumental; //生成音乐时参考伴奏ID  refer_voice、refer_instrumental至少一个必填

    @JSONField(name = "lyrics")
    private String lyrics; //歌词，使用换行符（\n）分隔每行歌词，使用两个连续换行符（\n\n）可以在歌词中间添加停顿，
                           // 使用双井号（##）添加在首尾可以添加伴奏，支持最长200字符（每个汉字、标点和字母都算1个字符）

    @JSONField(name = "model")
    private String model; //支持调用的模型：music-01。

    @JSONField(name = "stream")
    private Boolean stream; //是否开启流式，默认不开启

    @JSONField(name = "audio_setting")
    private AudioSetting audioSetting;

    @Data
    public static class AudioSetting {
        @JSONField(name = "sample_rate")
        private Integer sampleRate; //音频采样率，支持16000Hz、32000Hz、48000Hz，默认为16000Hz

        @JSONField(name = "bitrate")
        private Integer bitrate; //生成音乐的比特率，范围[32000, 64000，128000,256000]

        @JSONField(name = "format")
        private String format; //生成的音乐格式，范围["mp3","wav","pcm"]

        public static AudioSetting builderAudioSetting(){
            AudioSetting audioSetting = new AudioSetting();
            audioSetting.setSampleRate(44100);
            audioSetting.setBitrate(256000);
            audioSetting.setFormat("mp3");
            return audioSetting;
        }
    }

    public HaiLuoMusicRequestBO() {}
    public HaiLuoMusicRequestBO(String referVoice, String referInstrumental, String lyrics)
    {
        this.referVoice = referVoice;
        this.referInstrumental = referInstrumental;
        this.lyrics = lyrics;
        this.model = "music-01";
        this.audioSetting = AudioSetting.builderAudioSetting();
    }


}
