package com.business.hailuo;

import com.alibaba.fastjson.JSON;
import com.business.aigc.tusiart.TusiJobStateEnum;
import com.business.db.model.po.ImgDrawDetlPO;
import com.business.hailuo.model.*;
import com.business.model.po.ImgDrawRecordPO;
import com.business.utils.BDateUtil;
import com.business.utils.BOssUtil;
import com.nacos.config.OssClientConfig;
import com.nacos.redis.RedisUtil;
import com.nacos.utils.BFeiShuUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;


@Slf4j
@Schema(title = "海螺视频 api工具类")
public class HaiLuoApiUtil {

    public static final String STATUS_FAIL = "Fail";//运行中
    public static final String STATUS_SUCCESS = "Success";//完成
    public static final String STATUS_PROCESSING = "Processing";
    public static final String STATUS_QUEUEING = "Queueing";
    public static final String STATUS_PREPARING = "Preparing";

    /**
     * 海螺视频生成
     */
    public static String postHaiLuoTextToVideoGenerations(HaiLuoRequestBO haiLuoRequestBO, String token) {
        HaiLuoResponseBO haiLuoResponseBO = HaiLuoApis.postHaiLuoTextToVideoGenerations(haiLuoRequestBO, token);
        if (haiLuoResponseBO != null) {
            Integer statusCode = haiLuoResponseBO.getBaseResp().getStatusCode();
            switch (statusCode) {
                case 0 -> {
                    return haiLuoResponseBO.getTaskId();
                }
                case 2013, 1026 -> {
                    return "2013";
                }
                case 1002 -> {
                    return "429";
                }
                case 1008 -> {
                    String dateKey = RedisUtil.REDIS_HAOLUO_ACCOUNT_PREFIX + "-" + BDateUtil.getYearAndMonthAndDayAndHour();
                    String value = RedisUtil.getValue(dateKey);
                    if (StringUtils.isBlank(value)) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "海螺视频需要充值！", "postHaiLuoTextToVideoGenerations.resp " + JSON.toJSONString(haiLuoResponseBO));
                        RedisUtil.setValue(dateKey, "1");
                    }
                    return null;
                }
            }
        }
        return null;
    }

    /**
     * 获取海螺任务结果
     */
    public static String getHaiLuoVideoTaskResult(String taskId, String token, ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO) {
        HaiLuoResponseBO haiLuoResponseBO = HaiLuoApis.getHaiLuoVideoTaskResult(taskId, token);
        if (haiLuoResponseBO == null) {
            return TusiJobStateEnum.FAILED.getState();
        }
        if (Objects.equals(haiLuoResponseBO.getStatus(), STATUS_PROCESSING)
                || Objects.equals(haiLuoResponseBO.getStatus(), STATUS_PREPARING)
                || Objects.equals(haiLuoResponseBO.getStatus(), STATUS_QUEUEING)) {
            return TusiJobStateEnum.RUNNING.getState();
        }
        if (Objects.equals(haiLuoResponseBO.getStatus(), STATUS_FAIL)) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "海螺视频拉取任务业务失败", "getHaiLuoVideoTaskResult.resp " + JSON.toJSONString(haiLuoResponseBO) + " jobId=" + taskId + " prompt= " + imgDrawRecordPO.getPromptInit());
            return TusiJobStateEnum.FAILED.getState();
        }
        if (Objects.equals(haiLuoResponseBO.getStatus(), STATUS_SUCCESS)) {
            String fileId = haiLuoResponseBO.getFileId();
            HaiLuoDownLoadResponseBO haiLuoDownLoadResponseBO = HaiLuoApis.downLoadHaiLuoVideoFile(fileId, token);
            if (haiLuoDownLoadResponseBO != null) {
                log.info("海螺视频原地址= " + haiLuoDownLoadResponseBO.getFile().getDownloadUrl());
                return uploadLumaVideo(haiLuoDownLoadResponseBO.getFile().getDownloadUrl(), taskId, 14, OssClientConfig.FILE_SUFFIX_VIDEO);
            }
        }
        return TusiJobStateEnum.FAILED.getState();
    }

    // TODO 上传视频到oss
    public static String uploadLumaVideo(String fileUrl, String videoJobId, Integer folder, String suffix) {
        if (fileUrl == null) return null;
        int maxRetries = 6; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadURL(fileUrl, videoJobId, folder, suffix);
                if (videoPath != null) {
                    return videoPath;
                }
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                }
                log.error("海螺视频上传失败，重试次数：" + (retryCount + 1) + " videoJobId=" + videoJobId);

            } catch (Exception e) {
                log.error("海螺视频上传视频到OSS发生异常，重试次数：" + (retryCount + 1));
            }
            retryCount++;
        }
        log.error("海螺视频文件上传失败，超过最大重试次数， 视频id=" + videoJobId);
        return null;
    }


    // TODO 海螺音乐生成
    public static String postHaiLuoTextToMusicGenerations(HaiLuoMusicRequestBO haiLuoMusicRequestBO, String token) {
        HaiLuoMusicResponseBO haiLuoMusicResponseBO = HaiLuoApis.postHaiLuoTextToMusicGenerations(haiLuoMusicRequestBO, token);
        log.info("=minimax海螺音乐生成结果：" + JSON.toJSONString(haiLuoMusicResponseBO));
        if (haiLuoMusicResponseBO != null) {
            HaiLuoMusicResponseBO.MusicData musicData = haiLuoMusicResponseBO.getData();
            HaiLuoMusicResponseBO.BaseResp baseResp = haiLuoMusicResponseBO.getBaseResp();
            if (musicData != null && musicData.getStatus() == 2) {
                return musicData.getAudio();
            }
            if (baseResp != null && baseResp.getStatusCode() != null) {
                Integer statusCode = baseResp.getStatusCode();
                if (1008 == statusCode) {
                    String dateKey = RedisUtil.REDIS_HAOLUO_ACCOUNT_PREFIX + "-" + BDateUtil.getYearAndMonthAndDayAndHour();
                    String value = RedisUtil.getValue(dateKey);
                    if (StringUtils.isBlank(value)) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "海螺需要充值！", "postHaiLuoTextToVideoGenerations.resp " + JSON.toJSONString(haiLuoMusicRequestBO));
                        RedisUtil.setValue(dateKey, "1");
                    }
                    return null;
                }
            }
        }
        return null;
    }

    public static void main(String[] args) throws Exception {
        String jingxuan = "精选流行曲目1,精选流行曲目2,精选流行曲目3,精选流行曲目4,精选流行曲目5,精选流行曲目6,精选流行曲目7,精选流行曲目8,精选流行曲目9,精选流行曲目10,精选流行曲目11,精选流行曲目12";
        String doushi = "精选都市曲目1,精选都市曲目2,精选都市曲目3,精选都市曲目4,精选都市曲目5,精选都市曲目6,精选都市曲目7,精选都市曲目8,精选都市曲目9";
        String yaogun = "精选摇滚曲目1,精选摇滚曲目2,精选摇滚曲目3,精选摇滚曲目4,精选摇滚曲目5,精选摇滚曲目6";
        String xiha = "精选嘻哈曲目1,精选嘻哈曲目2,精选嘻哈曲目3,精选嘻哈曲目4,精选嘻哈曲目5,精选嘻哈曲目6,精选嘻哈曲目7";
        String dianzi = "精选电子曲目1,精选电子曲目2,精选电子曲目3,精选电子曲目4,精选电子曲目5,精选电子曲目6,精选电子曲目7,精选电子曲目8,精选电子曲目9";
        String gudain = "精选古典曲目1";
        String RBsdfad = "精选R&B曲目1,精选R&B曲目2,精选R&B曲目3,精选R&B曲目4,精选R&B曲目5,精选R&B曲目6,精选R&B曲目7,精选R&B曲目8,精选R&B曲目9";

        HaiLuoMusicRequestBO haiLuoMusicRequestBO = new HaiLuoMusicRequestBO("vocal-2024112517033824-50357", null,
                "##在无垠的星空下\\n\\n梦开始飞翔\\n月光洒在心上\\n\\n温柔的想象\\n在这片宁静中\\n\\n我们自由歌唱##");
        String token = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
//        String hexString = postHaiLuoTextToMusicGenerations(haiLuoMusicRequestBO, token);
//
//
//        int length = hexString.length();
//        byte[] byteArray = new byte[length / 2];  // 每两个字符代表一个字节
//
//        for (int i = 0; i < length; i += 2) {
//            String byteString = hexString.substring(i, i + 2); // 取出每一对字符
//            byteArray[i / 2] = (byte) Integer.parseInt(byteString, 16); // 转换为字节
//        }
//        // 将字节数组写入到MP3文件
//        try (FileOutputStream fileOutputStream = new FileOutputStream("output_audio.mp3")) {
//            fileOutputStream.write(byteArray);  // 写入字节数据
//        }
//        System.out.println("音频文件已恢复为 output_audio.mp3");

        HaiLuoApis.uploadHaiLuoMusicFile(token);
    }
}
