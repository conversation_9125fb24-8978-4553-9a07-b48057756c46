package com.business.hailuo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.business.hailuo.model.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.tool.BrotliInterceptor;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.TimeUnit;


@Slf4j
@Schema(title = "海螺视频Api")
public class HaiLuoApis {

    private static final String HAILUO_API_KEY = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    private static final String HAILUO_GENERATIONS = "https://api.minimax.chat/v1/video_generation";
    private static final String HAILUO_ASYNC_RESULT = "https://api.minimax.chat/v1/query/video_generation?task_id=";
    public static final String HAILUO_DOWNLOAD_PRE = "https://api.minimax.chat/v1/files/retrieve?file_id=";
    public static final String HAILUO_MUSIC_GENERATIONS = "https://api.minimax.chat/v1/music_generation";


    @Getter
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    public static HaiLuoResponseBO postHaiLuoTextToVideoGenerations(HaiLuoRequestBO haiLuoRequestBO, String apiKey) {
        log.info("HaiLuo海螺AI请求参数：");
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(haiLuoRequestBO, JSONWriter.Feature.WriteMapNullValue));
        Request request = new Request.Builder()
                .url(HAILUO_GENERATIONS)
                .post(RequestBody.create(jsonObject.toString(), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("Authorization", "Bearer " + (apiKey == null || apiKey.isEmpty() ? HAILUO_API_KEY : apiKey))
                .build();
        try (var response = client.newCall(request).execute()) {
            log.error("海螺AI请求状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            System.out.println("====海螺AI=" + responseBody);
            if (response.isSuccessful()) {
                return JSON.parseObject(responseBody, new TypeReference<HaiLuoResponseBO>() {
                });
            }
            // 使用 ObjectMapper 解析 JSON
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(responseBody);
            int statusCode = rootNode.path("base_resp").path("status_code").asInt();
            if (2013 == statusCode) {
                HaiLuoResponseBO haiLuoResponseBO = new HaiLuoResponseBO();
                HaiLuoResponseBO.BaseResp baseResp = new HaiLuoResponseBO.BaseResp();
                baseResp.setStatusCode(statusCode);
                haiLuoResponseBO.setBaseResp(baseResp);
                return haiLuoResponseBO;
            }
        } catch (Exception e) {
            log.error("海螺AI请求失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    public static HaiLuoResponseBO getHaiLuoVideoTaskResult(String taskId, String apiKey) {
        Request request = new Request.Builder()
                .url(HAILUO_ASYNC_RESULT + taskId)
                .get()
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("Authorization", "Bearer " + (apiKey == null || apiKey.isEmpty() ? HAILUO_API_KEY : apiKey))
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("海螺AI获取任务结果状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            System.out.println("====海螺AI获取任务结果=" + responseBody);
            if (response.isSuccessful()) {
                return JSON.parseObject(responseBody, new TypeReference<HaiLuoResponseBO>() {
                });
            }
        } catch (Exception e) {
            log.error("海螺AI拉取任务失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    public static HaiLuoDownLoadResponseBO downLoadHaiLuoVideoFile(String fileId, String apiKey) {
        Request request = new Request.Builder()
                .url(HAILUO_DOWNLOAD_PRE + fileId)
                .get()
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("Authorization", "Bearer " + (apiKey == null || apiKey.isEmpty() ? HAILUO_API_KEY : apiKey))
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("海螺AI下载视频状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            System.out.println("====海螺AI下载视频结果=" + responseBody);
            if (response.isSuccessful()) {
                return JSONObject.parseObject(responseBody, HaiLuoDownLoadResponseBO.class);
            }
        } catch (Exception e) {
            log.error("海螺AI下载视频失败：{}", e.getMessage());
            return null;
        }
        return null;
    }


    // 音乐传作API
    public static HaiLuoMusicResponseBO postHaiLuoTextToMusicGenerations(HaiLuoMusicRequestBO haiLuoMusicRequestBO, String apiKey) {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(haiLuoMusicRequestBO, JSONWriter.Feature.WriteMapNullValue));
        System.out.println("================================");
        System.out.println(jsonObject);
        System.out.println("================================");
        RequestBody body = RequestBody.create(
                jsonObject.toString(),
                MediaType.parse("application/json; charset=utf-8")
        );
        Request request = new Request.Builder()
                .url(HAILUO_MUSIC_GENERATIONS)
                .post(body)
                .addHeader("Authorization", "Bearer " + (apiKey == null || apiKey.isEmpty() ? HAILUO_API_KEY : apiKey))
                .addHeader("Content-Type", "application/json")
                .build();
        try (Response response = client.newCall(request).execute()) {
            assert response.body() != null;
            String responseBody = response.body().string();
            if (response.isSuccessful() && response.body() != null) {
                return JSON.parseObject(responseBody, new TypeReference<HaiLuoMusicResponseBO>() {
                });
            } else {
                log.info("请求失败: " + responseBody);
                log.info("请求失败: " + response.code());
            }
        } catch (Exception e) {
            log.error("海螺音乐AI请求失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    // HaiLuoMusicResponseBO
    public static void uploadHaiLuoMusicFile(String token) throws Exception {
        String urlString = "https://cdn.hailuoai.com/prod/2024-08-21-15/musics/1724225820108495579-Lauv%20-%20I%20Like%20Me%20Better.mp3"; // 远程文件 URL
        URL url = new URL(urlString);

        // 创建 HttpURLConnection 来获取远程文件
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        InputStream inputStream = connection.getInputStream();

        // 读取流的所有字节
        byte[] fileBytes = inputStream.readAllBytes();

        // 创建 OkHttp 请求体上传文件
        MultipartBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("purpose", "song")
                .addFormDataPart("file", "misuck.mp3",
                        RequestBody.create(fileBytes, MediaType.parse("audio/mpeg")))
                .build();

        // 构建请求
        Request request = new Request.Builder()
                .url("https://api.minimax.chat/v1/music_upload")
                .post(requestBody)
                .addHeader("Authorization", "Bearer " + token)
                .build();

        // 发送请求
        Response response = client.newCall(request).execute();
        if (response.isSuccessful()) {
            System.out.println(response.body().string());
            System.out.println("File uploaded successfully!");
        } else {
            System.out.println("Failed to upload file: " + response.message());
        }

        inputStream.close();
    }

//        MultipartBody requestBody = new MultipartBody.Builder()
//                .setType(MultipartBody.FORM)
//                .addFormDataPart("purpose", "song") // 添加表单字段
//                .addFormDataPart(
//                        "file",
//                        "misuck",
//                        RequestBody.create(
//                                new File("https://cdn.diandiansheji.com/suno/style/misuck.mp3"),
//                                MediaType.parse("audio/mpeg")
//                        )
//                ) // 添加文件字段
//                .build();
//
//        Request request = new Request.Builder()
//                .url("https://api.minimax.chat/v1/music_upload")
//                .post(requestBody)
//                .addHeader("Authorization", "Bearer " + token)
//                .build();
//
//        // 发送请求并处理响应
//        try (Response response = client.newCall(request).execute()) {
//            if (response.isSuccessful()) {
//                // 获取 Trace-Id 和响应内容
//                String traceId = response.header("Trace-Id");
//                System.out.println("Trace-Id: " + traceId);
//                System.out.println("Response: " + response.body().string());
//            } else {
//                System.out.println("请求失败: " + response.code() + " " + response.message());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }

}
