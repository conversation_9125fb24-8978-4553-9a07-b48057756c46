package com.business.utils;

import com.business.db.model.po.ExchangeRecordPO;
import com.business.db.model.po.ExchangeRioPO;
import org.springframework.util.DigestUtils;

import java.util.ArrayList;
import java.util.List;


//操作工具
public class BOperateUtil {


    /*批量生成兑换码信息*/
    public static List<ExchangeRecordPO> getAddList(ExchangeRioPO exchangeRio){
        List<ExchangeRecordPO> exchangeRecordList = new ArrayList<>();
        for (int a = 0; a < exchangeRio.getOperateNun(); a++){
            ExchangeRecordPO exchangeRecord = getExchangeRecord(exchangeRio);
            exchangeRecordList.add(exchangeRecord);
        }
        System.out.println("ok1111111");
        System.out.println(exchangeRecordList);
        System.out.println("ok2222222");
        return exchangeRecordList;
    }

    private static ExchangeRecordPO getExchangeRecord(ExchangeRioPO exchangeRio) {
        ExchangeRecordPO exchangeRecord = new ExchangeRecordPO();
        exchangeRecord.setName(exchangeRio.getName());
        exchangeRecord.setAccountNum(exchangeRio.getAccountNum());
        exchangeRecord.setExpiryTime(exchangeRio.getExpiryTime());
        exchangeRecord.setUseExpiryDay(exchangeRio.getUseExpiryDay());
        exchangeRecord.setId(getId());
        exchangeRecord.setCode(getCode());
        return exchangeRecord;
    }

    /*加密生成兑换码信息*/
    public static String getCode(){
        String c = DigestUtils.md5DigestAsHex(("ddsj" + System.nanoTime()).getBytes());
        //16位，大写
        return c.substring(8, 24).toUpperCase();
    }

    private static Long getId(){
        return System.nanoTime();
    }

    public static void main(String[] args) {
        System.out.println(getCode());
    }
}
