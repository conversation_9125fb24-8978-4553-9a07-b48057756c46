package com.business.utils;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.oss.*;
import com.aliyun.oss.model.*;
import com.nacos.config.OssClientConfig;
import com.nacos.result.Result;

import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URI;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.List;
import java.util.Base64;
import java.util.ArrayList;
import java.util.stream.Collectors;

//阿里云图片工具
@Slf4j
public class BOssUtil {

    private static OkHttpClient client;
    public static OkHttpClient getInstance() {
        if (client == null) {
            client = new OkHttpClient.Builder()
                    .readTimeout(15, TimeUnit.MINUTES)
                    .writeTimeout(15, TimeUnit.MINUTES)
                    .build();
        }
        return client;
    }


    // 静态内部类，用于懒加载单例
    private static class OssClientHolder {
        private static final OSS INSTANCE = new OSSClientBuilder().build(
                OssClientConfig.ENDPOINT,
                OssClientConfig.ACCESSKEYID,
                OssClientConfig.SECRETACCESSKEY
        );
    }


    private static class OssClientHolderTimeOut {
        private static final OSS INSTANCE = createOssClient();

        private static OSS createOssClient() {
            // 创建 ClientBuilderConfiguration 对象
            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
            // 设置连接超时时间，单位为毫秒
            conf.setConnectionTimeout(10 * 1000); // 10秒
            // 设置读取数据超时时间，单位为毫秒
            conf.setSocketTimeout(15 * 1000); // 15秒
            // 设置最大连接数
//            conf.setMaxConnections(10000);
            conf.setMaxErrorRetry(3); // 设置最大重试次数为 3 次

            // 创建 OSS 实例并返回
            return new OSSClientBuilder().build(
                    OssClientConfig.ENDPOINT,
                    OssClientConfig.ACCESSKEYID,
                    OssClientConfig.SECRETACCESSKEY,
                    conf
            );
        }
    }

    public static OSS getOssInstance() {
        return OssClientHolderTimeOut.INSTANCE;
    }

    public static String uploadFile(InputStream inputStream, String filename, String idStr, Integer folder) {

        String objectName = OssClientConfig.getPath(folder).concat(idStr).concat(filename.substring(filename.lastIndexOf(".")));
        OSS ossClient = new OSSClientBuilder().build(OssClientConfig.ENDPOINT, OssClientConfig.ACCESSKEYID, OssClientConfig.SECRETACCESSKEY);
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(OssClientConfig.BUCKET_NAME,objectName, inputStream);
            ossClient.putObject(putObjectRequest); // 上传文件。
            return OssClientConfig.ACCESS_PATH.concat(objectName);
        } catch (OSSException oe) {
            log.error("阿里云OSS异常，Message,{}\nCode,{}\nRequest ID:,{}\nHost ID,{}",oe.getErrorMessage(),oe.getErrorCode(),oe.getRequestId(),oe.getHostId());
        } catch (ClientException ce) {
            log.error("阿里云OSS异常, 客户端异常{}",ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return "上传失败";
    }

    // 图片上传OSS
    public static String uploadImgDamage(InputStream inputStream, String filename, String idStr, Integer folder){
        try {
            Thread.sleep(2000);  // 休眠2秒（2000毫秒）
        } catch (InterruptedException e) {
            log.error("休眠异常",e);
        }
        inputStream = getDamageInputStream(inputStream, filename.substring(filename.lastIndexOf(".")));
        if (inputStream == null){
            return null;
        }
        return uploadFile(inputStream,filename,idStr,folder);
    }

    public static InputStream getDamageInputStream(InputStream inputStream,String filename){
        if (inputStream == null){
            return null;
        }
        try {
            // 设置压缩质量
            String extension = filename.substring(filename.lastIndexOf(".") + 1);
            log.info("格式===> {}",extension);
            BufferedImage originalImage = ImageIO.read(inputStream);  // 从 InputStream 读取图片
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            int tepMin = Math.min(originalWidth, originalHeight);
            if (tepMin < 1024){
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                ImageIO.write(originalImage, extension, os);
                InputStream is = new ByteArrayInputStream(os.toByteArray());
                os.close();
                return is;
            }
            int newWidth = 1024;  // 新的宽度
            int newHeight = 1024; // 新的高度
            if (originalWidth > originalHeight) {
                newHeight = (newWidth * originalHeight) / originalWidth;
            } else {
                newWidth = (newHeight * originalWidth) / originalHeight;
            }
            Image tmp = originalImage.getScaledInstance(newWidth, newHeight, Image.SCALE_SMOOTH);
            BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = resizedImage.createGraphics();
            g2d.drawImage(tmp, 0, 0, null);
            g2d.dispose();
            ByteArrayOutputStream os = new ByteArrayOutputStream();

            ImageWriter imageWriter = "webp".equals(extension) ? ImageIO.getImageWritersByFormatName("png").next() : ImageIO.getImageWritersByFormatName(extension).next();
            ImageWriteParam param = imageWriter.getDefaultWriteParam();
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            param.setCompressionQuality(0.5f);  // 设置压缩质量，范围从 0.0 到 1.0
            imageWriter.setOutput(ImageIO.createImageOutputStream(os));
            imageWriter.write(null, new javax.imageio.IIOImage(resizedImage, null, null), param);
            imageWriter.dispose();
            os.flush();
            ByteArrayInputStream bis = new ByteArrayInputStream(os.toByteArray());
            os.close();
            return bis;
        } catch (IOException e) {
            return null;
        }

    }

    //视频上传OSS
    public static String uploadVideo(byte[] fileBytes, String fileName, Integer folder) {
        String objectName = OssClientConfig.getPath(folder).concat(fileName.concat(OssClientConfig.FILE_SUFFIX_VIDEO));
        OSS ossClient = new OSSClientBuilder().build(OssClientConfig.ENDPOINT, OssClientConfig.ACCESSKEYID, OssClientConfig.SECRETACCESSKEY);
        try {
            InputStream inputStream = new ByteArrayInputStream(fileBytes); // 将字节数组转为输入流
            PutObjectRequest putObjectRequest = new PutObjectRequest(OssClientConfig.BUCKET_NAME,objectName, inputStream);
            ossClient.putObject(putObjectRequest); // 上传文件。
            return "/".concat(objectName);
        } catch (OSSException oe) {
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            return null;
        } catch (ClientException ce) {
            System.out.println("Error Message:" + ce.getMessage());
            return null;
        } catch (Throwable e) {
            return null;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    // TODO 网络url地址上传
    public static   String uploadURL(String url, String fileName, Integer folder, String suffix) {
        if(StringUtils.isBlank(url)){
            return null;
        }
        String objectName = OssClientConfig.getPath(folder).concat(fileName.concat(suffix));
//        OSS ossClient = new OSSClientBuilder().build(OssClientConfig.ENDPOINT, OssClientConfig.ACCESSKEYID, OssClientConfig.SECRETACCESSKEY);
        OSS ossClient = getOssInstance();
        log.info("uploadURL url="+url);
        InputStream inputStream = null;
        try {
//             inputStream = new URL(url).openStream();
            URL urlObj = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
            connection.setConnectTimeout(10000); // 设置连接超时时间为 10 秒
            connection.setReadTimeout(15000); // 设置读取超时时间为 15 秒
            inputStream = connection.getInputStream();
            inputStream = new BufferedInputStream(inputStream);
            ((BufferedInputStream) inputStream).mark(Integer.MAX_VALUE);
            log.info("uploadURL 通过url获取到stream");
            PutObjectRequest putObjectRequest = new PutObjectRequest(OssClientConfig.BUCKET_NAME,objectName, inputStream);
            ossClient.putObject(putObjectRequest);
            log.info("uploadURL resp = "+"/".concat(objectName));
            return "/".concat(objectName);
        } catch (OSSException oe) {
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            return null;
        } catch (Exception ce) {
            ce.printStackTrace();
            log.error("Error Message:" + ce.getMessage());
            return null;
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Error closing InputStream", e);
                }
            }
        }
    }
    // TODO 网络url地址上传
    public static   String uploadStreamUrl(InputStream inputStream, String fileName, Integer folder, String suffix) {

        String objectName = OssClientConfig.getPath(folder).concat(fileName.concat(suffix));
        OSS ossClient = getOssInstance();
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(OssClientConfig.BUCKET_NAME,objectName, inputStream);
            ossClient.putObject(putObjectRequest);
            return "/".concat(objectName);
        } catch (Exception ce) {
            ce.printStackTrace();
            log.error("uploadStreamUrl Error Message:" + ce.getMessage());
            return null;
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("uploadStreamUrl Error closing InputStream", e);
                }
            }
        }
    }

    // TODO 获取图片信息
    public static String getImageInfo(String objectName) throws Throwable {
        OSS ossClient = new OSSClientBuilder().build(OssClientConfig.ENDPOINT, OssClientConfig.ACCESSKEYID, OssClientConfig.SECRETACCESSKEY);
        try {
            String style = "image/info";  // 获取图片信息。
            Date expiration = new Date(new Date().getTime() + 1000 * 60 * 10 );    // 指定签名URL过期时间为10分钟。
            GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(OssClientConfig.BUCKET_NAME, objectName, HttpMethod.GET);
            req.setExpiration(expiration);
            req.setProcess(style);
            URL signedUrl = ossClient.generatePresignedUrl(req);
            HttpURLConnection connection = (HttpURLConnection) signedUrl.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("获取图片宽高信息 Response Code: ", responseCode);
            StringBuilder response = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                log.info("获取图片宽高信息 Response Body: {}", response.toString());
            }
            connection.disconnect();
            return response != null ? response.toString() : null;
        } catch (OSSException oe) {
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            return null;
        } catch (ClientException ce) {
            log.error("Error Message:" + ce.getMessage());
            return null;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }


    public static byte[] downloadVideo(String videoUrl) throws Exception {
        URL url = new URL(videoUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray();
        }
    }

    //获取阿里云图片的基色
    public static String getOssPicBaseColor(String url) throws IOException {
        url = url + "?x-oss-process=image/average-hue";
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Accept", "application/json")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("response: {}", response);
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                return jsonObject.getString("RGB");
            }
        }
        return null;
    }

    public static void main(String[] args) throws IOException {
//        String ossPicBaseColor = getOssPicBaseColor("https://cdn.diandiansheji.com/video/videoSquare/img/videoSquare_09_0.webp");
        InputStream inputStream = new URL("https://img.midjourneyapi.xyz/ephemeral/e630d0ec-1376-4dab-bd96-c76a20fbc081.mp4").openStream();

        System.out.println(inputStream);
    }

    // 数字人功能专用的OSS实例管理
    private static class DigitalOssClientHolder {
        private static volatile OSS instance;
        private static final Object lock = new Object();
        private static final ClientBuilderConfiguration config = createClientConfig();
        
        private static ClientBuilderConfiguration createClientConfig() {
            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
            // 设置连接超时时间为30秒
            conf.setConnectionTimeout(30_000);
            // 设置读取数据超时时间为60秒
            conf.setSocketTimeout(60_000);
            // 设置最大重试次数为5次
            conf.setMaxErrorRetry(5);
            // 设置最大连接数为200
            conf.setMaxConnections(200);
            // 空闲连接保留时间（5分钟）
            conf.setIdleConnectionTime(300);
            // 连接最大存活时间10分钟
            conf.setConnectionTTL(600_000);
            // 开启TCP连接保活功能
            conf.setSupportCname(true);
            return conf;
        }
        
        public static OSS getInstance() {
            if (instance == null) {
                synchronized (lock) {
                    if (instance == null) {
                        instance = createOssClient();
                        // 添加JVM关闭钩子，确保应用关闭时正确释放资源
                        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                            shutdown();
                        }));
                    }
                }
            }
            return instance;
        }

        private static OSS createOssClient() {
            return new OSSClientBuilder().build(
                OssClientConfig.ENDPOINT,
                OssClientConfig.ACCESSKEYID,
                OssClientConfig.SECRETACCESSKEY,
                config
            );
        }

        public static void shutdown() {
            synchronized (lock) {
                if (instance != null) {
                    try {
                        instance.shutdown();
                        instance = null;
                        log.info("OSS客户端已正确释放");
                    } catch (Exception e) {
                        log.error("关闭OSS客户端时发生异常", e);
                    }
                }
            }
        }

        public static void checkAndReinitialize() {
            synchronized (lock) {
                try {
                    if (instance != null && !instance.doesBucketExist(OssClientConfig.BUCKET_NAME)) {
                        log.warn("OSS连接异常，尝试重建连接...");
                        shutdown();
                        instance = createOssClient();
                        log.info("OSS连接已重建");
                    }
                } catch (Exception e) {
                    log.error("检查OSS连接状态时发生异常", e);
                    shutdown();
                    instance = createOssClient();
                }
            }
        }
    }

    /**
     * 获取数字人功能专用的OSS实例
     */
    public static OSS getDigitalOssInstance() {
        OSS ossClient = DigitalOssClientHolder.getInstance();
        // 在获取实例时检查连接状态
        DigitalOssClientHolder.checkAndReinitialize();
        return ossClient;
    }

    /**
     * 处理数字人文件上传的通用逻辑
     * @param inputStream 输入流
     * @param contentLength 内容长度（可选，-1表示未知）
     * @param fileName 文件名（不含后缀）
     * @param userId 用户ID
     * @param groupId 组ID
     * @param type 资源类型
     * （用户）0-授权视频目录，1-训练视频（数字人分身）分组存储，2-生成的数字人视频临时目录，3-拼接后的数字人视频目录，4-音频临时目录，5-音频目录，6-数字人形象封面目录，7-数字人形象头像目录，8-数字人视频封面目录
     * （系统）10-上传系统音频目录，11-上传系统数字人目录，12-上传系统音频临时目录，13-上传系统视频临时目录，14-数字人形象封面目录
     * @return 文件访问路径
     */
    private static String handleDigitalFileUpload(InputStream inputStream, long contentLength, 
            String fileName, String userId, String groupId, Integer type) {
        // 获取文件后缀
        String suffix = switch (type) {
            case 0, 1, 2, 3, 11, 13 -> OssClientConfig.FILE_SUFFIX_VIDEO;  // .mp4
            case 4, 5, 10, 12 -> OssClientConfig.FILE_SUFFIX_AUDIO;
            case 9 -> fileName.substring(fileName.lastIndexOf("."));// .mp3
            default -> throw new RuntimeException("获取文件后缀失败!");
        };
        
        // 处理文件名，移除可能存在的后缀
        if (fileName.toLowerCase().endsWith(suffix)) {
            fileName = fileName.substring(0, fileName.length() - suffix.length());
        }
        
        // 构建完整的存储路径
        String objectName = OssClientConfig.getDigitalPath(userId, groupId, type).concat(fileName).concat(suffix);

        // 使用数字人专用的OSS实例
        OSS ossClient = getDigitalOssInstance();
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                OssClientConfig.BUCKET_NAME,
                objectName,
                inputStream
            );

            // 设置文件元信息
            ObjectMetadata metadata = new ObjectMetadata();
            if (contentLength >= 0) {
                metadata.setContentLength(contentLength);
            }
            switch (type) {
                case 0, 1, 2, 3, 11, 13 -> metadata.setContentType("video/mp4");
                case 4, 5, 10, 12 -> metadata.setContentType("audio/mpeg");
            }
            putObjectRequest.setMetadata(metadata);

            // 执行上传
            ossClient.putObject(putObjectRequest);
            String domain = "https://cdn.diandiansheji.com";
            return domain + "/".concat(objectName);
            
        } catch (OSSException oe) {
            log.error("OSS服务端异常: ErrorCode={}, ErrorMessage={}, RequestId={}", 
                oe.getErrorCode(), oe.getErrorMessage(), oe.getRequestId());
            throw new RuntimeException("网络连接错误，详细信息：" + oe.getErrorMessage());
        } catch (Exception e) {
            log.error("上传文件发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("网络连接错误，详细信息：" + e.getMessage());
        }
    }

    /**
     * 上传数字人相关文件
     * @param fileBytes 文件字节数组
     * @param fileName 文件名（不含后缀）
     * @param userId 用户ID
     * @param groupId 组ID
     * @param type 资源类型
     * @return 文件访问路径
     */
    public static String uploadDigitalFile(byte[] fileBytes, String fileName, String userId, String groupId, Integer type) {
        try {
            return handleDigitalFileUpload(
                new ByteArrayInputStream(fileBytes),
                fileBytes.length,
                fileName,
                userId,
                groupId,
                type
            );
        } catch (Exception e) {
            log.error("处理文件上传时发生异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 通过URL上传数字人相关文件到OSS
     * @param fileUrl 文件URL
     * @param fileName 文件名（不含后缀）
     * @param userId 用户ID
     * @param groupId 组ID
     * @param type 资源类型
     * @return 文件访问路径
     */
    public static String uploadDigitalFileByUrl(String fileUrl, String fileName, String userId, String groupId, Integer type) {
        if (StringUtils.isBlank(fileUrl)) {
            log.error("文件URL不能为空");
            return null;
        }

        try {
            URI uri = new URI(fileUrl);
            URL url = uri.toURL();
            return handleDigitalFileUpload(
                url.openStream(),
                -1, // URL流的长度未知
                fileName,
                userId,
                groupId,
                type
            );
        } catch (Exception e) {
            log.error("处理URL文件上传时发生异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 视频拼接
     * @param videoList 要拼接的视频文件列表（按顺序拼接）
     * @param outputFileName 输出文件名
     * @param userId 用户ID
     * @return 异步处理结果，包含EventId、RequestId、TaskId
     */
    public static AsyncProcessObjectResult concatVideos(List<String> videoList, 
            String outputFileName, String userId) {
        if (videoList == null || videoList.size() < 2) {
            log.error("视频列表不能为空且至少需要两个视频进行拼接");
            return null;
        }

        // 构建输出存储路径
        String objectName = OssClientConfig.getDigitalPath(userId, null, 3)
                .concat(outputFileName).concat(OssClientConfig.FILE_SUFFIX_VIDEO);
        
        OSS ossClient = getDigitalOssInstance();
        try {
            // 对视频文件名称进行编码
            List<String> processedVideos = new ArrayList<>();
            for (String video : videoList) {
                // 移除域名前缀
                String processedVideo = video;
                if (video.startsWith("https://cdn.diandiansheji.com/")) {
                    processedVideo = video.substring("https://cdn.diandiansheji.com/".length());
                } else if (video.startsWith("http://cdn.diandiansheji.com/")) {
                    processedVideo = video.substring("http://cdn.diandiansheji.com/".length());
                }
                processedVideos.add(processedVideo);
            }
            
            // 获取源视频和需要拼接的视频
            String sourceVideo = processedVideos.get(0);  // 第一个视频作为源视频
            
            // 从第二个视频开始，这些是需要拼接的视频
            List<String> concatVideos = new ArrayList<>();
            for (int i = 1; i < processedVideos.size(); i++) {
                concatVideos.add(processedVideos.get(i));
            }
            
            // 对拼接视频进行Base64编码
            List<String> encodedVideos = concatVideos.stream()
                .map(video -> Base64.getUrlEncoder().withoutPadding().encodeToString(video.getBytes()))
                .collect(Collectors.toList());
            
            // 构建视频处理样式字符串
            StringBuilder styleBuilder = new StringBuilder();
            styleBuilder.append("video/concat,ss_0,f_mp4,vcodec_h264,fps_25,vb_1000000,acodec_aac,ab_96000,ar_48000,ac_2,align_1");
            
            // 添加拼接视频
            for (int i = 0; i < encodedVideos.size(); i++) {
                if (i == 0) {
                    styleBuilder.append("/pre,o_").append(encodedVideos.get(i));
                } else {
                    styleBuilder.append("/sur,o_").append(encodedVideos.get(i));
                }
                
                // 最后一个视频添加t_0
                if (i == encodedVideos.size() - 1) {
                    styleBuilder.append(",t_0");
                }
            }
            
            String style = styleBuilder.toString();
            
            // 构建异步处理指令
            String bucketEncoded = Base64.getUrlEncoder().withoutPadding().encodeToString(OssClientConfig.BUCKET_NAME.getBytes());
            String targetEncoded = Base64.getUrlEncoder().withoutPadding().encodeToString(objectName.getBytes());
            String process = String.format("%s|sys/saveas,b_%s,o_%s", style, bucketEncoded, targetEncoded);
            
            log.info("[concatVideos] 视频拼接处理指令: process={}", process);
            log.info("[concatVideos] 源视频路径: sourceVideo={}", sourceVideo);
            
            // 创建异步处理请求
            AsyncProcessObjectRequest request = new AsyncProcessObjectRequest(OssClientConfig.BUCKET_NAME, sourceVideo, process);

            // 执行异步处理任务
            AsyncProcessObjectResult response = ossClient.asyncProcessObject(request);
            log.info("[concatVideos] 提交视频拼接任务成功: videoList={}, taskId={}", 
                    videoList, response.getTaskId());
            return response;
            
        } catch (OSSException oe) {
            log.error("[concatVideos] OSS服务端异常: ErrorCode={}, ErrorMessage={}, RequestId={}", 
                oe.getErrorCode(), oe.getErrorMessage(), oe.getRequestId());
            return null;
        } catch (ClientException ce) {
            log.error("[concatVideos] OSS客户端异常: ErrorMessage={}", ce.getMessage());
            return null;
        } catch (Throwable e) {
            log.error("[concatVideos] 提交视频拼接任务发生异常", e);
            return null;
        }
    }

    /**
     * 从视频中截取帧作为封面
     * @param videoObjectName OSS中的视频对象名称
     * @param outputFileName 输出的封面图片文件名（不含后缀）
     * @param userId 用户ID
     * @param groupId 组ID
     * @return 异步处理结果，包含EventId、RequestId、TaskId
     */
    public static Result<String> extractVideoFrame(String videoObjectName, String outputFileName, String userId, String groupId, Integer type) {
        if (StringUtils.isBlank(videoObjectName) || StringUtils.isBlank(outputFileName)) {
            log.error("视频对象名称和输出文件名不能为空");
            return Result.ERROR("视频对象名称和输出文件名不能为空");
        }
        String errorMsg = null;
        // 构建输出文件存储路径
        String outCoverPath = OssClientConfig.getDigitalPath(userId, groupId, type).concat(outputFileName).concat(".webp");
        
        OSS ossClient = getDigitalOssInstance();
        try {
            // 在执行异步处理前增加检查
            if (!ossClient.doesObjectExist(OssClientConfig.BUCKET_NAME, videoObjectName)) {
                errorMsg = String.format("源视频文件不存在: %s", videoObjectName);
                log.error(errorMsg);
                return Result.ERROR(errorMsg);
            }

            // 构建视频处理样式字符串
            // 从视频开始处(0秒)截取1帧，输出为png格式，并设置分辨率720x1280，等比缩放不保留黑边
            StringBuilder style = new StringBuilder("video/snapshots,ss_0,f_png,w_720,h_1280,num_1,scaletype_fit");
            
            // 构建异步处理指令
            String bucketEncoded = Base64.getUrlEncoder().withoutPadding().encodeToString(OssClientConfig.BUCKET_NAME.getBytes());
            String targetEncoded = Base64.getUrlEncoder().withoutPadding().encodeToString(outCoverPath.getBytes());
            String process = String.format("%s|sys/saveas,b_%s,o_%s/notify,topic_QXVkaW9Db252ZXJ0", 
                    style.toString(), bucketEncoded, targetEncoded);

            // 创建异步处理请求
            AsyncProcessObjectRequest request = new AsyncProcessObjectRequest(OssClientConfig.BUCKET_NAME, videoObjectName, process);

            // 执行异步处理任务
            AsyncProcessObjectResult response = ossClient.asyncProcessObject(request);
            log.info("提交视频截帧任务成功: video={}, outCoverPath={}, taskId={}",
                    videoObjectName, outCoverPath, response.getTaskId());
            
            // 轮询检查生成文件是否存在（最多等待3分钟，每5秒检查一次）
            int maxAttempts = 72;
            for (int i = 0; i < maxAttempts; i++) {
                Thread.sleep(5000);
                // 直接检查目标文件是否存在
                boolean fileExists = ossClient.doesObjectExist(OssClientConfig.BUCKET_NAME, outCoverPath);
                if (fileExists) {
                    log.info("视频截帧文件生成成功，目标路径：{}", outCoverPath);
                    return Result.SUCCESS(outCoverPath);
                }
                log.info("文件尚未生成，等待重试... 当前次数：{}/{}", i+1, maxAttempts);
            }
            // 如果文件不存在，返回错误信息
            errorMsg = String.format("视频截帧文件生成超时，目标路径：%s", outCoverPath);
            log.error(errorMsg);
            return Result.ERROR(errorMsg);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("任务轮询被中断", e);
            return null;
        } catch (OSSException oe) {
            errorMsg = String.format("OSS服务端异常: ErrorCode={}, ErrorMessage={}, RequestId={}", 
                oe.getErrorCode(), oe.getErrorMessage(), oe.getRequestId());
            log.error(errorMsg);
            return Result.ERROR(errorMsg);
        } catch (ClientException ce) {
            errorMsg = String.format("OSS客户端异常: ErrorMessage={}", ce.getMessage());
            log.error(errorMsg);
            return Result.ERROR(errorMsg);
        } catch (Throwable e) {
            errorMsg = String.format("提交视频截帧任务发生异常: {}", e.getMessage());
            log.error(errorMsg);
            return Result.ERROR(errorMsg);
        }
        // 注意：这里不需要关闭ossClient，因为它是共享的单例实例
    }
}
