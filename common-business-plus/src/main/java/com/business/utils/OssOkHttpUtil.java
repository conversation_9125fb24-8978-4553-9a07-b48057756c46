package com.business.utils;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSSException;
import com.business.enums.BRedisKeyEnum;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.config.OssClientConfig;
import com.nacos.model.OssParamBO;
import com.nacos.redis.RedisUtil;
import com.nacos.tool.BrotliInterceptor;
import com.nacos.utils.OSSUtils;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

//Luma工具类
@Slf4j
public class OssOkHttpUtil {

    public static String cookie = "__cf_bm=9pALixtO6rHfJFU4q3iTLqv9hm8Byx_fXNbmE6e1OYQ-1726218951-1.0.1.1-Swtq2vXQ.zQGe.VAjOzNBEytjyhPbSHix1JCn8p53l_CUwwkChqMcLbE.t1AKb4LqfS2U7xJMAcqzF.SBrhxwQ; path=/; expires=Fri, 13-Sep-24 09:45:51 GMT; domain=.midjourney.com; HttpOnly; Secure; SameSite=None"  ;

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .followRedirects(true)  // 默认是 true，可以手动确认
            .followSslRedirects(true)  // 跟随 HTTPS 重定向
            .cookieJar(new CookieJar() {
                private final HashMap<HttpUrl, List<Cookie>> cookieStore = new HashMap<>();

                @Override
                public void saveFromResponse(HttpUrl url, List<Cookie> cookies) {
                    cookieStore.put(url, cookies);
                }

                @Override
                public List<Cookie> loadForRequest(HttpUrl url) {
                    List<Cookie> cookies = cookieStore.get(url);
                    return cookies != null ? cookies : new ArrayList<Cookie>();
                }
            })
            .build();


    public static String uploadFile(String url, String fileName, Integer folder, String suffix) {

        Request request = new Request.Builder()
                .url(url)
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36")
                .header("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                .header("Accept-Language", "en-US,en;q=0.9")
                .header("origin", url)  // 替换为实际的 referer 来源
                .header("content-type", "application/x-www-form-urlencoded")
                .header("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                .header("accept-encoding", "gzip, deflate, br, zstd")
                .header("cookie", "")
                .get()
                .build();


        for(int i=0;i<3;i++) {
            String value = RedisUtil.getValue(BRedisKeyEnum.MJ_DOWNLOAD_IMAGES_TOKEN.getKey());
            Request  requestNew = null;
            if(StringUtils.isNotBlank(value)){
                  requestNew = request.newBuilder()
                        .header("cookie", value)
                        .build();
            }else{
                requestNew =  request;
            }
            try (Response response = client.newCall(requestNew).execute()) {
                if (response.isSuccessful()) {
                    log.info("uploadFile code:{}", response.code());
                    log.info("uploadFile param:{}", JSON.toJSONString(requestNew));
                    Headers headers = response.headers();
                    String setCookie = headers.get("Set-Cookie");
                    if(StringUtils.isNotBlank(setCookie)){
                        RedisUtil.setValue(BRedisKeyEnum.MJ_DOWNLOAD_IMAGES_TOKEN.getKey(),setCookie);
                    }
                    InputStream inputStream = response.body().byteStream();
                    return  BOssUtil.uploadStreamUrl(inputStream, fileName, folder, suffix);
                }else if(403 == response.code()){
                    Headers headers = response.headers();
                    String setCookie = headers.get("Set-Cookie");
                    log.info("uploadFile 403 code:{}", response.code());
                    log.info("uploadFile 403 setCookie:{}", setCookie);
                    if(StringUtils.isNotBlank(setCookie)){
                        RedisUtil.setValue(BRedisKeyEnum.MJ_DOWNLOAD_IMAGES_TOKEN.getKey(),setCookie);
                    }
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    return null;
                }
            } catch (Exception e) {
                log.error("uploadFile上传文件失败：url="+ url);
            }
        }
        return null;
    }

    /**
     * 网络url上传OSS
     * @param
     * @return
     * @throws Exception
     */
    public static OssParamBO uploadOkHttpURL(String endpoint, String accessKeyId, String secretAccessKey,
                                             String bucketName, String imageUrl, String fileName, Integer folder) {
        OssParamBO ossParamBO = new OssParamBO();
        String objectName = OssClientConfig.getPath(folder).concat(fileName.concat(OssClientConfig.FILE_SUFFIX));
        try {
            String ossPath = uploadFile(imageUrl, fileName, 1, OssClientConfig.FILE_SUFFIX);
            if(StringUtils.isBlank(ossPath)){
                return null;
            }
            ossParamBO.setImageUrl(ossPath);
            long startTime = System.currentTimeMillis();
            String resBody = OSSUtils.getImageInfo(endpoint, accessKeyId, secretAccessKey, bucketName, objectName);
            long endTime = System.currentTimeMillis();
            log.info("获取图片宽高耗时时长= " + (endTime - startTime));
            if (resBody != null || StringUtils.isNotEmpty(resBody)) {
                JsonNode jsonNode = new ObjectMapper().readTree(resBody);
                ossParamBO.setImageWidth(jsonNode.get("ImageWidth").get("value").asText());
                ossParamBO.setImageHeight(jsonNode.get("ImageHeight").get("value").asText());
                ossParamBO.setFileSize(jsonNode.get("FileSize").get("value").asText());
            }
            return ossParamBO;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (Throwable ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        } finally {
            OSSUtils.shutdown();
        }
        return null;
    }


}
