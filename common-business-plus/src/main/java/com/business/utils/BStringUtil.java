package com.business.utils;

import cn.hutool.core.thread.ThreadException;
import com.business.db.model.vo.VipRechargeFcsVO;
import com.business.db.model.vo.VipRechargeNewVO;
import org.apache.commons.lang3.StringUtils;

import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class BStringUtil {

    private static final int MAXLENGTH = 1000; //字符串长度限制

    private static final int VIDEO_MAXLENGTH = 450; //生成视频字符串长度限制

    public static boolean isStringWithinLimit(String inputString) {
        return inputString.length() <= MAXLENGTH;
    }

    public static boolean isStringWithinLimitVideo(String inputString) {
        return inputString.length() <= VIDEO_MAXLENGTH;
    }

    public static String stringToWrap(String originalString) {
        if (StringUtils.isBlank(originalString)) {
            return null;
        }
        return originalString.replaceAll("\\r\\n|\\r|\\n|\\t", "").
                replaceAll("[“”\"]", "'");
    }

    public static String promptUseMatcher(String originalString, String newString) {
        String pattern3 = "(--ar|\"ar\":)\\s\\d+:\\d+";
        String result = "--ar\\s\\d+:\\d+";
        Pattern pattern = Pattern.compile(pattern3); // 创建 Pattern 对象
        Matcher matcher = pattern.matcher(originalString); // 创建 Matcher 对象// 执行替换操作
        return matcher.replaceAll("--ar " + newString); // 执行替换操作
    }

    //欧基里的尺寸--裁剪
    public static Map<String, Integer> getSimilarWidthHeight(int width, int height) {
        Dimension[] fixedSizes = {
                new Dimension(1024, 576),
                new Dimension(576, 1024),
                new Dimension(768, 768)
        };

        Dimension dynamicSize = new Dimension(width, height);
        Dimension closestSize = findClosestSize(dynamicSize, fixedSizes);
        Map<String, Integer> result = new HashMap<>();
        result.put("width", closestSize.width);
        result.put("height", closestSize.height);
        return result;
    }
    public static Dimension findClosestSize(Dimension dynamicSize, Dimension[] fixedSizes) {
        double minDistance = Double.MAX_VALUE;
        Dimension closestSize = null;

        for (Dimension fixedSize : fixedSizes) {
            double distance = calculateDistance(dynamicSize, fixedSize);
            if (distance < minDistance) {
                minDistance = distance;
                closestSize = fixedSize;
            }
        }

        return closestSize;
    }
    public static double calculateDistance(Dimension size1, Dimension size2) {
        int widthDifference = size1.width - size2.width;
        int heightDifference = size1.height - size2.height;

        return Math.sqrt(widthDifference * widthDifference + heightDifference * heightDifference);
    }

    public static String getImageName(String url) {
        System.out.println("+++======+++++匹配图片名称="+url);
        String regex = ".*/([^/]+)/([^/]+)\\.\\w+$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1).concat("_").concat(matcher.group(2));
        }
        return null;
    }

    public static boolean strMatchUrl(String string) {
        Pattern pattern = Pattern.compile("https?://\\S+");
        Matcher matcher = pattern.matcher(string);
        // 检查是否找到匹配的 URL
        if (matcher.find()) {
            return true;
        } else {
            return false;
        }
    }

    //获取剩余时间
    public static String getRemainingTime(Date createDate) {
        String orginalString = "视频将于${hours}小时${minutes}分钟后失效";
        long remainingTime = calculateRemainingTime(createDate);

        if (remainingTime <= 0) {
            return null;
        } else {
            return orginalString.replace("${hours}", String.valueOf(remainingTime / (60 * 60 * 1000)))
                    .replace("${minutes}", String.valueOf((remainingTime % (60 * 60 * 1000)) / (60 * 1000)));
        }
    }

    private static long calculateRemainingTime(Date createDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(createDate);
        calendar.add(Calendar.HOUR_OF_DAY, 24);
        Date expiryDate = calendar.getTime();
        return Math.max(0, expiryDate.getTime() - System.currentTimeMillis());
    }

    public static Date getDate24HoursLater(Date givenDate, int hours) {
        // 创建 Calendar 对象，并设置给定的日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(givenDate);
        // 将日期增加24小时
        calendar.add(Calendar.HOUR_OF_DAY, hours);
        // 获取24小时后的日期
        Date dateAfter24Hours = calendar.getTime();
        System.out.println("24小时后的日期：" + dateAfter24Hours);
        return dateAfter24Hours;
    }

    public static boolean isChinese(String text) {
        Pattern pattern = Pattern.compile("[\u4E00-\u9FA5]");
        Matcher matcher = pattern.matcher(text);
        return matcher.find();
    }

    public static int[] getCompressedImageWidthAndHeight(int originalWidth, int originalHeight) {
        int maxWidth = 2048;
        int maxHeight = 2048;
        if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
            return new int[]{originalWidth, originalHeight};
        }
        double widthRatio = (double) maxWidth / originalWidth;
        double heightRatio = (double) maxHeight / originalHeight;
        double scalingFactor = Math.min(widthRatio, heightRatio);
        int newWidth = (int) Math.round(originalWidth * scalingFactor);
        int newHeight = (int) Math.round(originalHeight * scalingFactor);
        return new int[]{newWidth, newHeight};
    }

    public static Map<String, Integer> parseString(String input) {
        Map<String, Integer> resultMap = new HashMap<>();
        String[] items = input.split(",");
        for (String item : items) {
            String[] parts = item.split("_");
            if (parts.length == 2) {
                String key = parts[0];
                try {
                    int value = Integer.parseInt(parts[1]);
                    resultMap.put(key, value);
                } catch (NumberFormatException e) {
                    return null;
                }
            } else {
                return null;
            }
        }
        return resultMap;
    }
    public static Map<String, Integer> parseStringByColon(String input) {
        Map<String, Integer> resultMap = new HashMap<>();
        String[] items = input.split(",");
        for (String item : items) {
            String[] parts = item.split(":");
            if (parts.length == 2) {
                String key = parts[0];
                try {
                    int value = Integer.parseInt(parts[1]);
                    resultMap.put(key, value);
                } catch (NumberFormatException e) {
                    return null;
                }
            } else {
                return null;
            }
        }
        return resultMap;
    }

    public static String extractingFileName(String imageUrl) {
        Pattern pattern = Pattern.compile("com/(.*)");
        Matcher matcher = pattern.matcher(imageUrl);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 将double类型转换为字符串（用于点子显示）
     * @param doubleValue double类型
     * @return 字符串类型
     */
    public static String doubleToString(Double doubleValue) {
        String stringValue = String.valueOf(doubleValue);
        if (stringValue.contains(".")) {
            // 判断小数部分是否为零
            if (stringValue.endsWith(".0")) {
                // 如果小数部分为零，返回整数部分
                return stringValue.substring(0, stringValue.indexOf("."));
            } else {
                // 如果小数部分不为零，返回原始字符串
                return stringValue;
            }
        } else {
            // 如果没有小数点，返回原始字符串
            return stringValue;
        }
    }

    public static java.util.List<VipRechargeNewVO.VipPerquisite> getVipPerquisite(){
        List<VipRechargeNewVO.VipPerquisite> vipPerquisites = new ArrayList<>();
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("会员权益", "用户", "会员"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("点数消耗", "100%", "50%"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("版权", false, true));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("绘图模型", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("音频模型", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("视频模型", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("高级功能", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("智能助理", false, true));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("同时进行任务数", "1", "5"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("广告", "广告", "无广告"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("水印", "水印", "去水印"));
        return vipPerquisites;
    }

    public static java.util.List<VipRechargeFcsVO.VipPerquisite> getVipPerquisiteFcs(){
        List<VipRechargeFcsVO.VipPerquisite> vipPerquisites = new ArrayList<>();
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("会员权益", "用户", "会员"));
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("点数消耗", "100%", "50%"));
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("版权", false, true));
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("绘图模型", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("音频模型", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("视频模型", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("高级功能", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("智能助理", false, true));
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("同时进行任务数", "1", "5"));
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("广告", "广告", "无广告"));
        vipPerquisites.add(new VipRechargeFcsVO.VipPerquisite("水印", "水印", "去水印"));
        return vipPerquisites;
    }

    // 匹配音频风格
    public static Long audioMatcherStyle(String audioStyle) {
        // 使用正则表达式匹配所有位数为4的数字
        Matcher matcher = Pattern.compile("\\b\\d{4}\\b").matcher(audioStyle);
        List<String> result = new ArrayList<>();
        String[] numbers = audioStyle.split(",");
        for (String number : numbers) {
            if (!matcher.reset(number).matches()) {
                result.add(number);
            }
        }
        if (result.isEmpty()) {
            return null;
        }
        return Long.valueOf(String.join(",", result));
    }

}
