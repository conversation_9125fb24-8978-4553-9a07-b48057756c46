package com.business.utils;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.business.db.model.bo.DictConfigBO;
import com.business.db.model.po.DictConfigPO;
import com.business.enums.BRedisKeyEnum;
import com.nacos.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

// 第三方密钥工具类
@Slf4j
public class BThirdPartyKey {

    private static final HashMap<Long, String> dictConfigChatMap = new HashMap<>();

    /**
     * 获取第三方密钥信息
     * @param dictType 字典类型
     * @return 第三方密钥信息
     */
    public static HashMap<Long, String> getSecretKeyInfo(Long dictType) {
        if (dictType == null) {
            return null;
        }
        String jsonDictConfigBO = RedisUtil.getValue(BRedisKeyEnum.SECRET_KEY_THIRD_PARTY_KEY_CACHE.getKey());
        if (jsonDictConfigBO == null) {
            return null;
        }
        List<DictConfigBO> dictConfigList = JSON.parseObject(jsonDictConfigBO, new TypeReference<List<DictConfigBO>>(){});
        if (dictConfigList.isEmpty()) {
            return null;
        }
        List<DictConfigBO> dictConfigBOList = filterByDictType(dictConfigList, dictType);
        if (dictConfigBOList == null || dictConfigBOList.isEmpty()) {
            return null;
        }
        dictConfigChatMap.clear();
        for (DictConfigBO dictConfigBO : dictConfigBOList) {
            dictConfigChatMap.put(dictConfigBO.getDictKey(), dictConfigBO.getDictValue());
        }
        return dictConfigChatMap;
    }

    /**
     * 过滤字典类型
     * @param dictConfigBOList 字典配置BO列表
     * @param dictType 字典类型
     * @return 字典配置BO列表
     */
    public static List<DictConfigBO> filterByDictType(List<DictConfigBO> dictConfigBOList, Long dictType) {
        return dictConfigBOList.stream()
                .filter(dictConfigBO -> dictType.equals(dictConfigBO.getDictType()))
                .collect(Collectors.toList());
    }

}
