package com.business.utils;

import com.business.db.model.bo.UserRightsConfigBO;
import com.business.db.model.vo.UserRightsConfigShowVO;
import com.business.db.model.vo.UserRightsConfigVO;
import com.business.enums.VipLevelEnum;
import com.nacos.enums.VipGradeEnum;

public class BMemberUtil {

    /**
     * 获取会员并发数量
     * 21, 25 -> 基础会员
     * 22, 26 -> 标准会员
     * 23, 27 -> 专业会员
     * 24, 28 -> 高级会员
     * 29, 99 -> 超级会员
     * @param vipConfigId vip配置id
     * @param userRightsConfig 会员权益信息
     * @return 会员并发数量
     */
    public static int getMemberConcurrentCount(int vipConfigId, UserRightsConfigBO userRightsConfig) {
        return switch (vipConfigId) {
            case 21, 25 -> userRightsConfig.getBasicPackageValue();
            case 22, 26 -> userRightsConfig.getSeriesPackageValue();
            case 23, 27 -> userRightsConfig.getProfePackageValue();
            case 24, 28 -> userRightsConfig.getHighPackageValue();
            case 29, 99 -> userRightsConfig.getLongPackageValue();
            default -> 1;
        };
    }

    /**
     * 根据vip配置id获取会员等级
     * 21, 25 -> 基础会员
     * 22, 26 -> 标准会员
     * 23, 27 -> 专业会员
     * 24, 28 -> 高级会员
     * 29, 99 -> 超级会员
     * @param vipConfigId vip配置id
     * @return 会员等级
     */
    public static int getMemberLevelByVipConfigId(int vipConfigId) {
        return switch (vipConfigId) {
            case 21, 25 -> VipGradeEnum.MEMBER_BASE.getIntValue();
            case 22, 26 -> VipGradeEnum.MEMBER_NORM.getIntValue();
            case 23, 27 -> VipGradeEnum.MEMBER_PROFE.getIntValue();
            case 24, 28, 29, 99 -> VipGradeEnum.MEMBER_HIGH.getIntValue();
            default -> 0;
        };
    }

    /**
     * 会员等级 user 查询专用
     * 8, 21, 25 -> 基础会员
     * 22, 26 -> 标准会员
     * 23, 27 -> 专业会员
     * 24, 28 -> 高级会员
     * 29, 99 -> 超级会员
     * @param vipConfigId vip配置id
     * @return 会员等级
     */
    public static int getMemberLevel(int vipConfigId) {
        return switch (vipConfigId) {
            case 8, 21, 25 -> VipLevelEnum.BASICS_LEVEL.getPackageLevel();
            case 22, 26 -> VipLevelEnum.STANDARD_LEVEL.getPackageLevel();
            case 23, 27 -> VipLevelEnum.PROFESSIONAL_LEVEL.getPackageLevel();
            case 24, 28 -> VipLevelEnum.ADVANCED_LEVEL.getPackageLevel();
            case 29, 99 -> VipLevelEnum.LONG_LEVEL.getPackageLevel();
            default -> 0;
        };
    }

    /**
     * 会员权益信息数据显示
     * 21, 25 -> 基础会员
     * 22, 26 -> 标准会员
     * 23, 27 -> 专业会员
     * 24, 28 -> 高级会员
     * 29, 99 -> 超级会员
     * @param vipConfigId vip配置id
     * @param userRightsConfigVO 会员权益信息
     * @return 会员权益信息
     */
    public static UserRightsConfigShowVO getUserRightsConfigShowVO(int vipConfigId, UserRightsConfigVO userRightsConfigVO) {
        return switch (vipConfigId) {
            case 21, 25 -> new UserRightsConfigShowVO(userRightsConfigVO.getId(), userRightsConfigVO.getRightsName(),
                    userRightsConfigVO.getNotPackage(), userRightsConfigVO.getBasicPackage());
            case 22, 26 -> new UserRightsConfigShowVO(userRightsConfigVO.getId(), userRightsConfigVO.getRightsName(),
                    userRightsConfigVO.getNotPackage(), userRightsConfigVO.getSeriesPackage());
            case 23, 27 -> new UserRightsConfigShowVO(userRightsConfigVO.getId(), userRightsConfigVO.getRightsName(),
                    userRightsConfigVO.getNotPackage(), userRightsConfigVO.getProfePackage());
            case 24, 28 -> new UserRightsConfigShowVO(userRightsConfigVO.getId(), userRightsConfigVO.getRightsName(),
                    userRightsConfigVO.getNotPackage(), userRightsConfigVO.getHighPackage());
            case 29, 99 -> new UserRightsConfigShowVO(userRightsConfigVO.getId(), userRightsConfigVO.getRightsName(),
                    userRightsConfigVO.getNotPackage(), userRightsConfigVO.getLongPackage());
            default -> null;
        };
    }

}
