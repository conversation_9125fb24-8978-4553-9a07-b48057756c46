package com.business.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.green20220302.Client;
import com.aliyun.green20220302.models.*;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.nacos.config.OssClientConfig;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class BImgAuditUtil {

    // 含有政治的标签
    public static final String LABEL = "political_politicalFigure.*";

    public static final String ADULTCONTENT = "pornographic_adultContent.*";

    /**
     * 创建请求客户端
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @param endpoint
     * @return
     * @throws Exception
     */
    public static Client createClient(String accessKeyId, String accessKeySecret, String endpoint) throws Exception {
        Config config = new Config();
        config.setAccessKeyId(accessKeyId);
        config.setAccessKeySecret(accessKeySecret);
        config.setEndpoint(endpoint);
        return new Client(config);
    }

    /**
     * 图片检测
     */
    public static boolean imageDetection(String imgurl) {
        boolean returnValue = false;
        try {
            ImageModerationResponse response = invokeImageDetection(imgurl);
            if (response != null && response.getStatusCode() == 200) {
                ImageModerationResponseBody body = response.getBody();
                log.info("imageDetection code= {}, msg= {}", body.getCode(), body.getMsg());
                returnValue = checkImageDetectionResults(body.getData().getResult());
            } else {
                log.error("imageDetection response not success. status: {}", response != null ? response.getStatusCode() : "unknown");
            }
        } catch (Exception e) {
            log.error("图片检测失败: {}", e.getMessage(), e);
        }
        return returnValue;
    }
    private static ImageModerationResponse invokeImageDetection(String imgurl) throws Exception {
        ImageModerationResponse response = invokeFunction(OssClientConfig.IMG_ACCESSKEYID, OssClientConfig.IMG_SECRETACCESSKEY, OssClientConfig.IMG_ENDPOINT, imgurl);
        if (response != null && response.getStatusCode() == 500) {
            response = invokeFunction(OssClientConfig.IMG_ACCESSKEYID, OssClientConfig.IMG_SECRETACCESSKEY, "green-cip.cn-beijing.aliyuncs.com", imgurl);
        }
        return response;
    }

    private static void processImageDetectionResults(List<ImageModerationResponseBody.ImageModerationResponseBodyDataResult> results) {
        for (ImageModerationResponseBody.ImageModerationResponseBodyDataResult result : results) {
            log.info("imageDetection label= {}, confidence= {}", result.getLabel(), result.getConfidence());
        }
    }

    private static boolean checkImageDetectionResults(List<ImageModerationResponseBody.ImageModerationResponseBodyDataResult> results) {
        for (ImageModerationResponseBody.ImageModerationResponseBodyDataResult result : results) {
            if (result.getLabel() != null && !result.getLabel().equals("nonLabel") && result.getConfidence() != null) {
                Matcher matcherLabel = Pattern.compile(LABEL).matcher(result.getLabel());
                Matcher matcherLabel1 = Pattern.compile(ADULTCONTENT).matcher(result.getLabel());
                if ((matcherLabel.matches() || matcherLabel1.matches()) && result.getConfidence() > OssClientConfig.IMG_CONFIDENCE_POLITICS) {
                    return true;
                }
                if (result.getConfidence() > OssClientConfig.IMG_CONFIDENCE) {
                    return true;
                }
            }
        }
        return false;
    }

    public static ImageModerationResponse invokeFunction(String accessKeyId, String accessKeySecret, String endpoint, String imgurl) throws Exception {
        //注意，此处实例化的client请尽可能重复使用，避免重复建立连接，提升检测性能。
        Client client = createClient(accessKeyId, accessKeySecret, endpoint);
        // 创建RuntimeObject实例并设置运行参数
        RuntimeOptions runtime = new RuntimeOptions();
        // 检测参数构造。
        Map<String, String> serviceParameters = new HashMap<>();
        //公网可访问的URL。
        serviceParameters.put("imageUrl", imgurl);
        serviceParameters.put("referer", "www.diandiansheji.com");
        //待检测数据唯一标识
        serviceParameters.put("dataId", UUID.randomUUID().toString());
        ImageModerationRequest request = new ImageModerationRequest();
        // 图片检测service：内容安全控制台图片增强版规则配置的serviceCode，示例：baselineCheck
        request.setService(OssClientConfig.IMG_SERVICE);
        request.setServiceParameters(JSONObject.toJSONString(serviceParameters));
        ImageModerationResponse response = null;
        try {
            response = client.imageModerationWithOptions(request, runtime);
        } catch (Exception e) {
            log.error("调用接口失败，异常信息为：{}", e.getMessage());
        }
        return response;
    }


    //TODO 本地图片检测 是否合规 ===============================================
    public static boolean isVPC = false;
    //文件上传token endpoint->token
    public static Map<String, DescribeUploadTokenResponseBody.DescribeUploadTokenResponseBodyData> tokenMap = new HashMap<>();
    //上传文件请求客户端
    public static OSS ossClient = null;

    /**
     * 创建上传文件请求客户端
     *
     * @param tokenData
     * @param isVPC
     */
    public static void getOssClient(DescribeUploadTokenResponseBody.DescribeUploadTokenResponseBodyData tokenData, boolean isVPC) {
        //注意，此处实例化的client请尽可能重复使用，避免重复建立连接，提升检测性能。
        if (isVPC) {
            ossClient = new OSSClientBuilder().build(tokenData.ossInternalEndPoint, tokenData.getAccessKeyId(), tokenData.getAccessKeySecret(), tokenData.getSecurityToken());
        } else {
            ossClient = new OSSClientBuilder().build(tokenData.ossInternetEndPoint, tokenData.getAccessKeyId(), tokenData.getAccessKeySecret(), tokenData.getSecurityToken());
        }
    }

    /**
     * 上传文件
     *
     * @param inputStream
     * @param tokenData
     * @return
     * @throws Exception
     */
    public static String uploadFile(InputStream inputStream, DescribeUploadTokenResponseBody.DescribeUploadTokenResponseBodyData tokenData) throws Exception {
        String objectName = tokenData.getFileNamePrefix() + UUID.randomUUID() + OssClientConfig.FILE_SUFFIX;
        PutObjectRequest putObjectRequest = new PutObjectRequest(tokenData.getBucketName(), objectName, inputStream);
        ossClient.putObject(putObjectRequest);
        return objectName;
    }

    public static ImageModerationResponse invokeFunction(String accessKeyId, String accessKeySecret, String endpoint, InputStream inputStream) throws Exception {
        //注意，此处实例化的client请尽可能重复使用，避免重复建立连接，提升检测性能。
        Client client = createClient(accessKeyId, accessKeySecret, endpoint);
        RuntimeOptions runtime = new RuntimeOptions();

        String bucketName = null;
        DescribeUploadTokenResponseBody.DescribeUploadTokenResponseBodyData uploadToken = tokenMap.get(endpoint);
        //获取文件上传token
        if (uploadToken == null || uploadToken.expiration <= System.currentTimeMillis() / 1000) {
            DescribeUploadTokenResponse tokenResponse = client.describeUploadToken();
            uploadToken = tokenResponse.getBody().getData();
            bucketName = uploadToken.getBucketName();
        }
        //上传文件请求客户端
        getOssClient(uploadToken, isVPC);

        //上传文件
        String objectName = uploadFile(inputStream, uploadToken);

        // 检测参数构造。
        Map<String, String> serviceParameters = new HashMap<>();
        //文件上传信息
        serviceParameters.put("ossBucketName", bucketName);
        serviceParameters.put("ossObjectName", objectName);
        serviceParameters.put("dataId", UUID.randomUUID().toString());

        ImageModerationRequest request = new ImageModerationRequest();
        // 图片检测service：内容安全控制台图片增强版规则配置的serviceCode，示例：baselineCheck
        // 支持service请参考：https://help.aliyun.com/document_detail/467826.html?0#p-23b-o19-gff
        request.setService("baselineCheck");
        request.setServiceParameters(JSON.toJSONString(serviceParameters));

        ImageModerationResponse response = null;
        try {
            response = client.imageModerationWithOptions(request, runtime);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    public static boolean localImageDetection(InputStream inputStream) throws Exception {
        boolean returnValue = false;
        ImageModerationResponse response = invokeFunction(OssClientConfig.IMG_ACCESSKEYID, OssClientConfig.IMG_SECRETACCESSKEY, OssClientConfig.IMG_ENDPOINT, inputStream);
        try {
            // 自动路由。
            if (response != null) {
                //区域切换到cn-beijing。
                if (500 == response.getStatusCode() || (response.getBody() != null && 500 == (response.getBody().getCode()))) {
                    // 接入区域和地址请根据实际情况修改。
                    response = invokeFunction(OssClientConfig.IMG_ACCESSKEYID, OssClientConfig.IMG_SECRETACCESSKEY, "green-cip.cn-beijing.aliyuncs.com", inputStream);
                }
            }
            // 打印检测结果。
            if (response != null && response.getStatusCode() == 200) {
                ImageModerationResponseBody body = response.getBody();
                if (body.getCode() == 200) {
                    System.out.println("检测结果：" + JSON.toJSONString(body.getData().getResult()));
                    returnValue = checkImageDetectionResults(body.getData().getResult());
                }
            }
            log.info("localImageDetection:{}", JSON.toJSONString(response));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return returnValue;
    }

}
