package com.business.utils;

import com.business.enums.BDailySignEnum;
import com.nacos.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Random;
import java.util.concurrent.TimeUnit;

//时间管理
@Slf4j
public class BDateUtil {

    public static String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    // 获取一周的星期
    public static int getDayOfWeek() {
        Calendar calendar = Calendar.getInstance();
        return switch (calendar.get(Calendar.DAY_OF_WEEK)) {
            case Calendar.MONDAY -> BDailySignEnum.SIGN_MONDAY.getCode();
            case Calendar.TUESDAY -> BDailySignEnum.SIGN_TUESDAY.getCode();
            case Calendar.WEDNESDAY -> BDailySignEnum.SIGN_WEDNESDAY.getCode();
            case Calendar.THURSDAY -> BDailySignEnum.SIGN_THURSDAY.getCode();
            case Calendar.FRIDAY -> BDailySignEnum.SIGN_FRIDAY.getCode();
            case Calendar.SATURDAY -> BDailySignEnum.SIGN_SATURDAY.getCode();
            case Calendar.SUNDAY -> BDailySignEnum.SIGN_SUNDAY.getCode();
            default -> 0;
        };
    }

    //获取当前日期的星期一 时间
    public static LocalDate getMondayOFTheDay() {
        return LocalDate.now().with(DayOfWeek.MONDAY);
    }

    //获取当前日期的星期日 时间
    public static LocalDateTime getSundayOFTheDay() {
        LocalDate now = LocalDate.now().with(DayOfWeek.SUNDAY);
        return now.atTime(LocalTime.MAX);
    }

    //获取当前月的第一天 时间
    public static LocalDate getFirstDayOfMonth() {
        return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
    }

    //获取当前月的最后一天 时间
    public static LocalDate getLastDayOfMonth() {
        return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 获取当前时间
     * @return 当前时间（上海时间）
     */
    public static Date getDateNowShanghai() {
        return Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
    }
    //获取当前时间的后一天
    public static Date getDateNowShanghai_1() {
        return Date.from(LocalDateTime.now().plusDays(1).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 增加月份
     * @param date 需要计算的日期
     * @param month 需要增加的月
     * @return 返回新计算的日期
     */
    public static Date getDateAddMonth(Date date, int month) {
        if (month < 0){
            return date;
        }
        // 将 Date 转换为 LocalDateTime，并使用系统时区
        LocalDateTime currentDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();;
        // 使用 plusMonths 方法添加月数
        LocalDateTime targetDateTime = currentDateTime.plusMonths(month);
        // 将 LocalDateTime 转换回 Date
        return Date.from(targetDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant());
    }


    public static Date getMjTokenRefresh15Minus() {
        Instant instant = DateUtil.getDateNowShanghai().toInstant();
        LocalDateTime localDateTime = instant.atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        LocalDateTime newDateTime = localDateTime.plusMinutes(5);
        return Date.from(newDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant());
    }

    public static Date getMjTokenRefreshMinus20Minus() {
        Instant instant = DateUtil.getDateNowShanghai().toInstant();
        LocalDateTime localDateTime = instant.atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        LocalDateTime newDateTime = localDateTime.minusMinutes(20);
        return Date.from(newDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant());
    }

    //随机装载刷新时间
    public static Date getTimeAddRandomMinute(Date timeD, int minuteMin, int minuteMax){
        if (timeD == null){
            return null;
        }
        int randomNumber = new Random().nextInt(minuteMax) + minuteMin;
        log.info("随机增加时间：{}",randomNumber);
        Instant instant = timeD.toInstant();
        LocalDateTime localDateTime = instant.atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        LocalDateTime newDateTime = localDateTime.plusMinutes(randomNumber);
        return Date.from(newDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant());
    }

    // 时间汉化格式 展示
    public static String timeFormatConversion(Date createdDate) {
        Date today = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("M月d日");
        long millisecondsInDay = 24 * 60 * 60 * 1000;
        long daysDifference = (today.getTime() - createdDate.getTime()) / millisecondsInDay;
        String formattedDate;

        if (daysDifference == 0) {
            formattedDate = "今天";
        } else if (daysDifference == 1) {
            formattedDate = "昨天";
        } else if (daysDifference == 2) {
            formattedDate = "前天";
        } else if (daysDifference >= 3 && daysDifference < 7) {
            formattedDate = daysDifference + "天前";
        } else if (daysDifference >= 7) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(createdDate);
            int createdYear = cal.get(Calendar.YEAR);
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);

            if (createdYear != currentYear) {
                formattedDate = new SimpleDateFormat("yyyy年M月d日").format(createdDate);
            } else {
                formattedDate = dateFormat.format(createdDate);
            }
        } else {
            formattedDate = dateFormat.format(createdDate);
        }
        return formattedDate;
    }


    public static long differenceMinutes(Date prevDate) {
        Date currentDate = cn.hutool.core.date.DateUtil.date();
        long diffMillis = currentDate.getTime() - prevDate.getTime();
        // 将毫秒数转换为分钟数
        return TimeUnit.MILLISECONDS.toMinutes(diffMillis);
    }

    public static String millisecondsToHm(long milliseconds) {
        long hours = milliseconds / 3600000;
        long minutes = (milliseconds % 3600000) / 60000;
        return String.format("%02d:%02d", hours, minutes);
    }

    public static String getStrTodayDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年M月d日"));
    }

    public static String getStrMonthDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy年M月"));
    }

    public static String getStrWeekCount() {
        LocalDate currentDate = LocalDate.now();
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int weekNumber = currentDate.get(weekFields.weekOfWeekBasedYear());
        return currentDate.getYear() + "年" + weekNumber + "周";
    }

    public static String getFileNameUseDate() {
        LocalDateTime currentTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
        return currentTime.format(formatter);
    }
    public static String getYearAndMonth() {
        LocalDateTime currentTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        return currentTime.format(formatter);
    }
    public static String getYearAndMonthAndDayAndHour() {
        LocalDateTime currentTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH");
        return currentTime.format(formatter);
    }

    public static void main(String[] args) {
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");

        // 格式化时间
        String formattedDateTime = currentTime.format(formatter);

        // 打印生成的文件名
        System.out.println(formattedDateTime);
    }
}
