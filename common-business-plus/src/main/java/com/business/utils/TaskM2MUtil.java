package com.business.utils;

import com.business.enums.BAudioModelEnum;
import com.business.model.aitask.audio.AudioTaskInfo;
import com.business.model.po.ImgDrawRecordPO;
import com.nacos.enums.ImgOptModelEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

//任务模型转模型
@Slf4j
public class TaskM2MUtil {

    //仅封装音频实体操作
    public static AudioTaskInfo ImgDrawRecordPO2AudioTaskInfo(ImgDrawRecordPO imgDrawRecordPO) {
        if (imgDrawRecordPO == null){
            return null;
        }
        AudioTaskInfo audioTaskInfo = new AudioTaskInfo();
        audioTaskInfo.setType(2);
        String[] ids = getList(imgDrawRecordPO.getAudioStyle());
        if (ids != null){
            for (String id : ids) {
                //词曲原声
                if (String.valueOf(BAudioModelEnum.VOICE_NATIVE.getId()).equals(id)){
                    audioTaskInfo.setIsPureMusic(false);
                }
                //伴奏
                if (String.valueOf(BAudioModelEnum.VOICE_ACCOMPANY.getId()).equals(id)){
                    audioTaskInfo.setIsPureMusic(true);
                }
                //样式id
                try {
                    if (Integer.parseInt(id) < 1000){
                        audioTaskInfo.setStyleId(Long.valueOf(id));
                    }
                } catch (Exception ignored) {
                    log.error("音频模型id转成long异常");
                }
                //1灵感
                if (String.valueOf(BAudioModelEnum.TYPE_LG.getId()).equals(id)){
                    audioTaskInfo.setType(BAudioModelEnum.TYPE_LG2.getId());
                    audioTaskInfo.setInspiration(imgDrawRecordPO.getPromptInit());
                }
                //3自定义
                if (String.valueOf(BAudioModelEnum.TYPE_ZDY.getId()).equals(id)){
                    audioTaskInfo.setType(BAudioModelEnum.TYPE_ZDY2.getId());
                    audioTaskInfo.setCustomStyle(imgDrawRecordPO.getPromptUse());
                }
            }
        }

        audioTaskInfo.setModelId((long) ImgOptModelEnum.AUDIO_ATTRIBUTE_SUNO.getValue());
        audioTaskInfo.setSongLabel(imgDrawRecordPO.getAudioTitle());
        audioTaskInfo.setSongLyrics(imgDrawRecordPO.getAudioLyric());
        //查询原始的suno音乐文件上传地址和时长
        audioTaskInfo.setContinueAt(imgDrawRecordPO.getOriginalImgId());
        audioTaskInfo.setMusicOssUrl(imgDrawRecordPO.getInitImgUrls());


        //校验为
        if (Objects.equals(BAudioModelEnum.TYPE_GJ2.getId(), audioTaskInfo.getType()) && (audioTaskInfo.getSongLabel() == null || audioTaskInfo.getSongLabel().isEmpty())){
            audioTaskInfo.setSongLabel(imgDrawRecordPO.getPromptInit());
        }
        return audioTaskInfo;
    }

    public static String[] getList(String str) {
        if (str == null || str.isEmpty()) {
            return null;
        }
        return str.split(",");
    }

}
