package com.business.utils;

import com.nacos.exception.E;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.Parser;
import org.apache.tika.parser.audio.AudioParser;
import org.apache.tika.parser.mp3.Mp3Parser;
import org.apache.tika.sax.BodyContentHandler;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Base64;

@Slf4j
public class BFileUtil {

    public static File downloadFileFromURL(String imageUrl, String destinationFilePath) throws IOException {
        URL url = new URL(imageUrl);
        URLConnection connection = url.openConnection();
        InputStream inputStream = connection.getInputStream();
        File file = new File(destinationFilePath);

        try (BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
             FileOutputStream fileOutputStream = new FileOutputStream(file)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = bufferedInputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }
        }
        return file;
    }

    public static File downloadFileFromURL(String imageUrl, String destinationFilePath, long targetSize) throws Exception {
        return saveCompressedImage(compressImage(imageUrl, targetSize), destinationFilePath);
    }

    public static BufferedImage compressImage(String imageUrl, long targetSize) throws IOException {
        URL url = new URL(imageUrl);
        URLConnection connection = url.openConnection();
        // 设置连接超时时间为5秒
        connection.setConnectTimeout(7000); // 设置连接超时时间为5秒
        connection.setReadTimeout(10000); // 设置读取超时时间为10秒

        BufferedImage originalImage = ImageIO.read(connection.getInputStream());
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        double compressionRatio = calculateImageNewSize(targetSize, originalWidth, originalHeight);
        int compressedWidth = (int) (originalWidth * compressionRatio);
        int compressedHeight = (int) (originalHeight * compressionRatio);
        log.info("==**==压缩后宽度：" + compressedWidth);
        log.info("==**==压缩后高度：" + compressedHeight);

        BufferedImage compressedImage = new BufferedImage(compressedWidth, compressedHeight, BufferedImage.TYPE_INT_RGB);
        compressedImage.getGraphics().drawImage(originalImage, 0, 0, compressedWidth, compressedHeight, null);
        return compressedImage;
    }

    public static File saveCompressedImage(BufferedImage compressedImage, String destinationFilePath) {
        File file = new File(destinationFilePath);
        try (FileOutputStream fos = new FileOutputStream(file)) {
            // 将压缩后的图片写入文件
            ImageIO.write(compressedImage, "jpg", fos);
        } catch (IOException e) {
            e.printStackTrace();
            log.info("==**==保存压缩后的图片失败");
            return null;
        }
        return file;
    }

    public static double calculateImageNewSize(long targetSize, int originalWidth, int originalHeight) {
        return Math.sqrt((double) targetSize / ImgDrawUtil.getImageSize(originalWidth, originalHeight));
    }

    public static byte[] downloadImageGetByte(String imageUrl) throws IOException {
        URL url = new URL(imageUrl);
        log.info("downloadImageGetByte img = "+ imageUrl);
        HttpURLConnection connection = (HttpURLConnection)url.openConnection();
        InputStream inputStream = connection.getInputStream();

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toByteArray();
        } finally {
            inputStream.close();
        }
    }

    // 视频专用下载文件方法
    public static byte[] downloadImageGetBytePlayVideo(String imageUrl) throws IOException {
        URL url = new URL(imageUrl);
        URLConnection connection = url.openConnection();
        int contentLength = connection.getContentLength();
        double fileSizeMB = (double) contentLength / (1024 * 1024);
        if (fileSizeMB > 5) {
            throw new E("图片不能大于5MB");
        }
        InputStream inputStream = connection.getInputStream();
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            inputStream.close();
        }
        return null;
    }

    // 自定义文件大小异常类
    public static class FileSizeException extends IOException {
        public FileSizeException(String message) {
            super(message);
        }
    }

    public static byte[] imageToBase64(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            InputStream inputStream = url.openStream();
            return inputStream.readAllBytes();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String byteArrayToBase64(byte[] byteArray) {
        try {
            // 使用Base64编码器对字节数组进行编码
            return Base64.getEncoder().encodeToString(byteArray);
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            return null;
        }
    }

    public static int[] compressionAspectRatio(int originalWidth, int originalHeight, int minSize, int maxSize, int step) {
        double aspectRatio = (double) originalWidth / originalHeight;
        int newWidth = originalWidth;
        int newHeight = originalHeight;
        if (originalWidth > maxSize || originalHeight > maxSize) {
            if (aspectRatio > 1) {
                newWidth = maxSize;
                newHeight = (int) Math.floor(newWidth / aspectRatio);
            } else {
                newHeight = maxSize;
                newWidth = (int) Math.floor(newHeight * aspectRatio);
            }
        }
        if (newWidth < minSize || newHeight < minSize) {
            if (aspectRatio > 1) {
                newWidth = minSize;
                newHeight = (int) Math.floor(newWidth / aspectRatio);
            } else {
                newHeight = minSize;
                newWidth = (int) Math.floor(newHeight * aspectRatio);
            }
        }
        newWidth -= newWidth % step;
        newHeight -= newHeight % step;
        if (newWidth < minSize) newWidth = minSize;
        if (newHeight < minSize) newHeight = minSize;
        if (newWidth > maxSize) newWidth = maxSize;
        if (newHeight > maxSize) newHeight = maxSize;
        return new int[]{newWidth, newHeight};
    }

    public static double getVideoSize(String videoUrl) {
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(videoUrl).openConnection();
            connection.setRequestMethod("HEAD");
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                long videoSizeInBytes = connection.getContentLengthLong();
                if (videoSizeInBytes > 0) {
                    return videoSizeInBytes / (1024.0 * 1024.0);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取视频大小失败");
        }
        return 0;
    }

    public static boolean isMp4Format(String urlString) {
        return urlString.endsWith(".mp4") ? true : false;
    }


    public static int[] getNewWidthAndHeightByAspectRatio(double aspectRatio) {
        int minSize = 512;
        int maxSize = 1024;
        int step = 8;
        int newWidth = 0;
        int newHeight = 0;

        // 遍历可能的新宽度，从最大值开始
        for (int width = maxSize; width >= minSize; width -= step) {
            // 根据宽高比例计算出对应的高度
            int height = (int) Math.round(width / aspectRatio);
            // 高度也要符合条件：在范围内且步长为8
            if (height >= minSize && height <= maxSize && height % step == 0) {
                newWidth = width;
                newHeight = height;
                break; // 找到第一个最大值后退出循环
            }
        }
        return new int[]{newWidth, newHeight};
    }

//    判定音乐文件是否是6-60s之内
public static boolean isAudioFileLengthValid(File file) {
    Metadata metadata = new Metadata();
    try (InputStream input = new FileInputStream(file)) {
        BodyContentHandler handler = new BodyContentHandler();
        ParseContext parseContext = new ParseContext();

        // 根据文件扩展名选择解析器
        Parser parser;
        if (file.getName().endsWith(".mp3")) {
            parser = new Mp3Parser();
        } else {
            parser = new AudioParser();
        }

        parser.parse(input, handler, metadata, parseContext);

        String durationStr = metadata.get("xmpDM:duration");
        if (durationStr != null) {
            double durationInSeconds = Double.parseDouble(durationStr) / 1000; // convert milliseconds to seconds
            return durationInSeconds >= 6 && durationInSeconds <= 60;
        }
    } catch (Exception e) {
        e.printStackTrace();
    }
    return false;
}

    public static byte[] resizeImage(byte[] imageData) throws IOException {
        // 将 byte[] 转换为 BufferedImage
        ByteArrayInputStream bis = new ByteArrayInputStream(imageData);
        BufferedImage originalImage = ImageIO.read(bis);

        // 获取原始宽度和高度
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();

        // 计算缩放比例
        double scale = Math.min((double) 2048 / originalWidth, (double) 2048 / originalHeight);

        // 计算新的尺寸
        int newWidth = (int) (originalWidth * scale);
        int newHeight = (int) (originalHeight * scale);

        // 创建缩小后的 BufferedImage
        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resizedImage.createGraphics();
        g2d.drawImage(originalImage.getScaledInstance(newWidth, newHeight, Image.SCALE_SMOOTH), 0, 0, null);
        g2d.dispose();

        // 将 BufferedImage 转换回 byte[]
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(resizedImage, "jpg", baos);

        byte[] resizedData = baos.toByteArray();

        // 检查图片大小是否小于 5MB，如果不满足，可以进一步压缩
        int imageSizeInBytes = resizedData.length;
        if (imageSizeInBytes > 5 * 1024 * 1024) {
            throw new IOException("Image size exceeds 5MB after resizing.");
        }

        return resizedData;
    }

}

