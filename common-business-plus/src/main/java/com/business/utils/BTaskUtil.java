package com.business.utils;

import com.alibaba.fastjson2.JSONObject;
import com.business.enums.BRedisKeyEnum;
import com.nacos.enums.UserDDrecordEnum;
import com.nacos.model.VipGradeBO;
import com.nacos.redis.RedisUtil;

import java.util.Objects;

//任务工具类
public class BTaskUtil {

    public static final int audioTitleLength = 50;
    public static final int audioLyricLength = 1000;
    public static final int audioCustomStyleLength = 20;
    public static final int audioInspirationLength = 200;

    //获取并发是否足够
    public static boolean isConcurrentNot(long count,Integer typeItem){
        // 校验用户并发数量：设置默认并发数量为1
        int concurrentCount = 1;
        VipGradeBO vipGradeBO = null;
        if (Objects.equals(typeItem, UserDDrecordEnum.TYPE_ITEM_PAY_VIP.getIntValue())) {
            vipGradeBO = JSONObject.parseObject( (String) RedisUtil.getValue(BRedisKeyEnum.VIP_GRADE_VIP.getKey()), VipGradeBO.class);
        }
        if (Objects.equals(typeItem, UserDDrecordEnum.TYPE_ITEM_PAY_SVIP.getIntValue())) {
            vipGradeBO = JSONObject.parseObject( (String) RedisUtil.getValue(BRedisKeyEnum.VIP_GRADE_SVIP.getKey()), VipGradeBO.class);
        }
        if (vipGradeBO != null && vipGradeBO.getDrawConcurrency() > 0) {
            concurrentCount = vipGradeBO.getDrawConcurrency();
        }
        return count >= concurrentCount;
    }
}
