package com.business.utils;

import com.business.db.model.bo.UserRightsConfigBO;
import com.business.enums.ImgDrawInstructEnum;
import com.business.model.bo.ImgDrawInstructBO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ImgDrawUtil {

    //统一汉化指令
    public static ImgDrawInstructBO getImgDrawPrompt(String prompt) {
        if (prompt == null || prompt.isEmpty()) {
            return null;
        }
        List<String> ddvalueList = new LinkedList<>();
        //替换mj回显问题
        for (ImgDrawInstructEnum item : ImgDrawInstructEnum.values()) {
            if (prompt.contains(item.getMjValue())) {
                ddvalueList.add(item.getDdValue().replaceAll("--", ""));
                prompt = prompt.replace(item.getMjValue(), item.getDdValue());
            }
        }
        String promptInstructStr = extractString(prompt);
        List<String> instructs = new ArrayList<>();
        //装载指令
        String[] parts = promptInstructStr.replaceAll("--慢速|--标准|--快速", "").split("--");
        for (String part : parts) {
            if (!part.trim().isEmpty()) {
                instructs.add(part.trim());
            }
        }
        //return new ImgDrawInstructBO(instructs,prompt);
        return new ImgDrawInstructBO(instructsSort(ddvalueList, instructs),prompt);
    }

    //提取指令
    public static String extractString(String input) {
        int index = input.indexOf("--");
        return (index != -1) ? input.substring(index).trim() : "";
    }

    public static List<String> getDDValues() {
        List<String> ddValueList = new ArrayList<>();
        for (ImgDrawInstructEnum instruction : ImgDrawInstructEnum.values()) {
            ddValueList.add(instruction.getDetails());
        }
        return ddValueList;
    }

    private static List<String> instructsSort(List<String> ddvalueList, List<String> instructs) {
        Map<Integer, String> dataMap = new TreeMap<>();
        for (String param : instructs) {
            for (int i = 0; i < ddvalueList.size(); i++) {
                if (param.contains(ddvalueList.get(i))) {
                    dataMap.put(i, param);
                    break;
                }
            }
        }
        List<String> params = new LinkedList<>();
        dataMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> params.add(entry.getValue()));
        return params;
    }

    public static double getWhDivide(Integer width, Integer height){
        try {
            if (width == null || height == null){
                return 1;
            }
            return new BigDecimal(String.valueOf(width)).divide(new BigDecimal(String.valueOf(height)), 6, RoundingMode.HALF_UP).doubleValue();
        } catch (Exception e) {
            return 1;
        }
    }

    public static float getStylizeValue(Integer stylizeValue){
        try {
            if (stylizeValue == null){
                return 1;
            }
            return (float) ((stylizeValue / 50) * 0.2 - 2);
        } catch (Exception e) {
            return 1;
        }
    }

    public static long getImageSize(Integer width, Integer height){
        try {
            if (width == null || height == null){
                return 1;
            }
            return new BigDecimal(String.valueOf(width)).multiply(new BigDecimal(String.valueOf(height))).longValue();
        } catch (Exception e) {
            return 1;
        }
    }

    public static UserRightsConfigBO getUserRightsConfigById(List<UserRightsConfigBO> rightsConfigList, int id) {
        // 使用 Stream API 过滤 id 为 1001 的数据
        Optional<UserRightsConfigBO> result = rightsConfigList.stream()
                .filter(rightsConfig -> rightsConfig.getId() == id)
                .findFirst();
        return result.orElse(null);
    }

}
