package com.business.utils;

import com.business.db.model.vo.VipRechargeNewVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class BUtils {

    /**
     * 将double类型转换为字符串（用于点子显示）
     * @param doubleValue double类型
     * @return 字符串类型
     */
    public static String doubleToString(Double doubleValue) {
        String stringValue = String.valueOf(doubleValue);
        if (stringValue.contains(".")) {
            // 判断小数部分是否为零
            if (stringValue.endsWith(".0")) {
                // 如果小数部分为零，返回整数部分
                return stringValue.substring(0, stringValue.indexOf("."));
            } else {
                // 如果小数部分不为零，返回原始字符串
                return stringValue;
            }
        } else {
            // 如果没有小数点，返回原始字符串
            return stringValue;
        }
    }

    public static List<VipRechargeNewVO.VipPerquisite> getVipPerquisite(){
        List<VipRechargeNewVO.VipPerquisite> vipPerquisites = new ArrayList<>();
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("会员权益", "用户", "会员"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("点数消耗", "100%", "50%"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("版权", false, true));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("绘图模型", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("音频模型", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("视频模型", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("高级功能", "基础", "解锁全部"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("智能助理", false, true));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("同时进行任务数", "1", "5"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("广告", "广告", "无广告"));
        vipPerquisites.add(new VipRechargeNewVO.VipPerquisite("水印", "水印", "去水印"));
        return vipPerquisites;
    }

    //获取一个10位数的随机seed值
    public  static Long getSeed(){
            Random random = new Random();
            long min = 1_000_000_000L;  // 10位数的最小值
            long max = 9_999_999_999L;  // 10位数的最大值
            return min + (long)(random.nextDouble() * (max - min));
    }

    //获取一个10位数的随机seed值
    public  static Long getNineSeed(){
        Random random = new Random();
        long min = 100_000_000L;  // 9位数的最小值
        long max = 999_999_999L;  // 9位数的最大值
        return min + (long)(random.nextDouble() * (max - min));
    }

}
