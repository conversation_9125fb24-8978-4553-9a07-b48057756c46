package com.business.utils;

import java.util.Random;
//随机数工具类
public class BRandomUtil {

    // 大写字母和数字的字符随机数
    public static String getCapsAndNumbers(int length) {
        String upperAlphabets = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String numbers = "0123456789";
        Random random = new Random();
        StringBuilder randomString = new StringBuilder();
        for (int i = 0; i < length; i++) {
            if (random.nextBoolean()) {
                randomString.append(upperAlphabets.charAt(random.nextInt(upperAlphabets.length())));
            } else {
                randomString.append(numbers.charAt(random.nextInt(numbers.length())));
            }
        }
        return randomString.toString();
    }

}
