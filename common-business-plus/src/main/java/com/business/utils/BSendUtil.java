package com.business.utils;

import cn.hutool.core.thread.ThreadException;

import java.util.regex.Pattern;

public class BSendUtil {
    private static final String SEND_REGISTER_CODE = "code:register:";//手机号登陆注册
    private static final String SEND_VERIFY_CODE = "code:verify:";
    private static final String SEND_BIND_CODE = "code:bind:"; //微信注册，绑定手机号
    private static final String SEND_ALTER_CODE = "code:alter:"; //变更手机号
    // 正则表达式匹配国际电话号码，允许以+开始，后面是数字0和8，且0可以重复一次或两次
    //private static final Pattern PHONE_PATTERN = Pattern.compile("^\\+?(\\d{1,3}(0|86)|86)$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^(\\+?86|0086|86)$");

    /**
     * 获取发送手机号redis key
     * @param use 用途
     * @param phone 手机号
     * @return
     */
    public static String getSendPhoneNumberRedisKeyByUse(int use, String phone){
        return switch (use) {
            case 1 -> SEND_REGISTER_CODE + phone;
            case 2 -> SEND_VERIFY_CODE + phone;
            case 3 -> SEND_BIND_CODE + phone;
            case 4 -> SEND_ALTER_CODE + phone;
            default -> throw new ThreadException("验证码用途非法！");
        };
    }

    public static boolean isValidPhoneNumber(String phoneNumber) {
        return PHONE_PATTERN.matcher(phoneNumber).matches();
    }

}
