package com.business.utils;

//公共url
public class BUrlUtil {

    private static final String BASE_URL_PREFIX_URL = "https://cdn.diandiansheji.com";//cdn加速前缀

    //点点内部域名
    public static String getBaseCdnUrl(String path){
        if (path == null) {
            return null;
        }
        if (path.contains("https")) {
            return path;
        }
        return BASE_URL_PREFIX_URL + path;
    }

    //获取水印路径
    public static String getOssWatermarkPath(){
        return "?x-oss-process=image/auto-orient,1/format,png/watermark,image_d2ViL3dhdGVybWFyay9kZF93YXRlcm1hcmsucG5nP3gtb3NzLXByb2Nlc3M9aW1hZ2UvcmVzaXplLFBfMjA,g_south,t_40,x_24,y_24";
    }

    public static String getOssWatermarkPath2(){
        return "watermark,image_d2ViL3dhdGVybWFyay9kZF93YXRlcm1hcmsucG5nP3gtb3NzLXByb2Nlc3M9aW1hZ2UvcmVzaXplLFBfMjA,g_south,t_40,x_24,y_24";
    }

    public static String respErrorInfoByCode(Integer code){
        if(code == null ){
            return null;
        }
        if(String.valueOf(code).startsWith("4")) {
            return switch  (code) {
                case 400 -> "请求格式不对或参数有误!";
                case 401 -> "无效的认证令牌!";
                case 403 -> "客户端无权访问此资源!";
                case 404 -> "请求的URL错误!";
                case 405 -> "请求方法不被允许!";
                case 429 -> "客户端发送的请求过多!";
                default -> "客户端错误！";
            };
        } else if (String.valueOf(code).startsWith("5")) {
            return switch  (code) {
                case 500 -> "服务器发生了未知错误!";
                case 502 -> "服务器收到的响应无效!";
                case 503 -> "服务器维护中!";
                case 504 -> "服务器响应超时!";
                default -> "服务端错误！";
            };
        }
        return "未知错误！";
    }

}
