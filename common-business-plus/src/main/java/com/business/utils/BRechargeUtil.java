package com.business.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

//充值工具包
public class BRechargeUtil {
    public static int isOther = 1;
    public static int isApple = 2;
    public static int isGoogle = 3;
    public static String isGoogleStr = "google store";
    public static String isAppleStr = "App Store";

    public static int getIdentity(String localLanguage, String appManufacturer){
        if (isAppleStr.equals(appManufacturer)){
            return isApple;
        }
        if (isGoogleStr.equals(appManufacturer)){
            return isGoogle;
        }
        return isOther;
    }
    public static boolean getCheckIsApple(int identity){
        return identity == BRechargeUtil.isApple;
    }

    public static boolean getCheckIsGoogle(int identity){
        return identity == BRechargeUtil.isGoogle;
    }

    //设置支付类型
    public static String getModePayment(String redisOpen){
        if ("2".equals(redisOpen)){
            return "2";
        }
        return "1";
    }

    public static String getYearPrice(String allPrice, String month){
        if (allPrice != null){
            return BUtils.doubleToString(new BigDecimal(allPrice).divide(new BigDecimal(month),0, RoundingMode.HALF_UP).doubleValue());
        }
        return null;
    }

    public static String getPaymentDefault(){
        return "5";
    }

}
