package com.business.luma;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.business.db.model.po.ImgDrawDetlPO;
import com.business.luma.model.*;
import com.business.luma.officeModel.LumaOfficeVideoReq;
import com.business.luma.officeModel.LumaOfficeVideoResq;
import com.business.model.po.ImgDrawRecordPO;
import com.business.utils.BOssUtil;
import com.nacos.config.OssClientConfig;
import com.nacos.ddimg.ImgDrawUtil;
import com.nacos.utils.BFeiShuUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

@Slf4j
public class LumaApis {

    public static final String STATUS_PENDING = "pending";//挂起、待处理
    public static final String STATUS_PROCESSING = "processing";
    public static final String STATUS_FAILED = "failed";//运行中
    public static final String STATUS_COMPLETION = "completed";//完成
    public static final String STATUS_RUNNING = "running";

    public static String getLumaAiImageUrl(File file, String token) {
        LumaPicResp lumaPicResp = LumaAiHttpUtil.getLumaUploadImageSecretKey(token);
        if (lumaPicResp == null) {
            return null;
        }
        boolean isUploadSuccessful = LumaAiHttpUtil.uploadImageToLuma(file, token, lumaPicResp.getPresignedUrl());
        if (isUploadSuccessful) {
            return lumaPicResp.getPublicUrl();
        }
        return null;
    }

    public static String postToGenerateLumaVideo(LumaVideoReq lumaVideoReq, String token) {
        return LumaAiHttpUtil.postLumaToGenerationVideo(lumaVideoReq, token);
    }
    public static String postToGenerateLumaOfficeVideo(LumaOfficeVideoReq lumaOfficeVideoReq, String token) {
        return LumaOfficialApiUtil.generateVideoFromLumaText(lumaOfficeVideoReq, token);
    }

    public static String postsToGenerateJobState(String jobId, String token, ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO) {
        LumaOfficeVideoResq videoJobState = LumaOfficialApiUtil.fetchVideoFromLumaText(jobId, token);
        if(videoJobState == null ){
            return STATUS_FAILED;
        }
        if (STATUS_FAILED.equals(videoJobState.getState())) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"梦工厂拉取任务业务失败","getLumaVideoJobState.resp "+ JSON.toJSONString(videoJobState)+" jobId="+jobId+" prompt= "+imgDrawRecordPO.getPromptInit());
            return STATUS_FAILED;
        }
        if (STATUS_COMPLETION.equals(videoJobState.getState())) {
            String aspectRatio = videoJobState.getRequest().getAspectRatio();
            String[] split = aspectRatio.split(":");

            imgDrawRecordPO.setWidth(Integer.valueOf(split[0]));
            imgDrawRecordPO.setHeight(Integer.valueOf(split[1]));
            imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(Integer.valueOf(split[0]), Integer.valueOf(split[1])));
            imgDrawDetlPO.setAudioUrl(videoJobState.getAssets().getVideo());
            log.info("梦工厂视频原地址= " + videoJobState.getAssets().getVideo());
            return uploadLumaVideo(videoJobState.getAssets().getVideo(), jobId, 14, OssClientConfig.FILE_SUFFIX_VIDEO);
        } else if (STATUS_PROCESSING.equals(videoJobState.getState())) {
            return STATUS_RUNNING;
        }
        return STATUS_RUNNING;
    }

    public static String postToGenerateLumaVideoExtend(VideoExtendReq videoExtendReq,String jobId, String token) {
        return LumaAiHttpUtil.postLumaToGenerationVideoExtend(videoExtendReq, jobId, token);
    }

    // TODO 上传视频到oss
    public static String uploadLumaVideo(String fileUrl, String videoJobId, Integer folder, String suffix) {
        if (fileUrl == null) return null;
        int maxRetries = 6; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadURL(fileUrl, videoJobId, folder, suffix);
                if (videoPath != null) {
                    return videoPath;
                }
                try{
                    Thread.sleep(500);
                }catch (InterruptedException e) {
                }
                log.error("梦工厂视频上传失败，重试次数：" + (retryCount + 1)+" videoJobId="+videoJobId);

            } catch (Exception e) {
                log.error("梦工厂上传图片到OSS发生异常，重试次数：" + (retryCount + 1) );
            }
            retryCount++;
        }
        log.error("梦工厂模型文件上传失败，超过最大重试次数， 视频id="+ videoJobId);
        return null;
    }

}
