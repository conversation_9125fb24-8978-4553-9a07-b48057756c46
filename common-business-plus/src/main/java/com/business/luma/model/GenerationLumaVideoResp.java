package com.business.luma.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class GenerationLumaVideoResp {

    @Schema(title = "id")
    @JSONField(name = "id")
    String id;

    @Schema(title = "指令")
    @JSONField(name = "prompt")
    String prompt;

    @Schema(title = "状态")
    @JSONField(name = "state")
    String state;

    @Schema(title = "创建时间")
    @JSONField(name = "created_at")
    String createdAt;

    @Schema(title = "视频")
    @JSONField(name = "video")
    String video;


    @Schema(title = "等待秒数")
    @JSONField(name = "estimate_wait_seconds")
    String estimateWaitSeconds;
}
