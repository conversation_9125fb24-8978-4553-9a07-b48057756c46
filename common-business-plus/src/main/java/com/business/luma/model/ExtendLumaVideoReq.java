package com.business.luma.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ExtendLumaVideoReq {

    @Schema(title = "用户指令")
    @JSONField(name = "user_prompt")
    String userPrompt;

    @Schema(title = "开始图")
    @JSONField(name = "image_url")
    String imageUrl;

    @Schema(title = "结束图")
    @JSONField(name = "image_end_url")
    String imageEndUrl;

    Boolean expand_prompt = true;



}
