package com.business.luma.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LumaPicResp {

    @JSONField(name = "id")
    private String id;

    @Schema(title = "预签名")
    @JSONField(name = "presigned_url")
    private String presignedUrl;

    @Schema(title = "图片地址")
    @JSONField(name = "public_url")
    private String publicUrl;

}
