package com.business.luma.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class VideoExtendReq {

    @JSONField(name = "prompt")
    String userPrompt;

    @JSONField(name = "image_url")
    String imageUrl;

    @J<PERSON>NField(name = "image_end_url")
    String imageEndUrl;

    @JSONField(name = "expand_prompt")
    private Boolean expandPrompt = true;

    @JSONField(name = "loop")
    Boolean loop = false;


    public VideoExtendReq(String userPrompt) {
        this.userPrompt = userPrompt;
    }

}
