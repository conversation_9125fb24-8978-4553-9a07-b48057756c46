package com.business.luma.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class LumaVideoReq {

//    @JSONField(name = "user_prompt")
//    private String userPrompt;
//
//    @JSONField(name = "image_url")
//    private String imageUrl;
//
//    @J<PERSON><PERSON>ield(name = "image_end_url")
//    private String imageEndUrl;
//
//    @JSONField(name = "aspect_ratio")
//    private String aspectRatio;
//
//    @JSONField(name = "expand_prompt")
//   private Boolean expandPrompt;
//
//    public LumaVideoReq() {}
//
//    public LumaVideoReq(String userPrompt, String imageUrl, String imageEndUrl) {
//        this.userPrompt = userPrompt == null ? "" : userPrompt;
//        this.imageUrl = imageUrl;
//        this.imageEndUrl = imageEndUrl;
//        this.aspectRatio = "16:9";
//        this.expandPrompt = true;
//    }

    @JSONField(name = "prompt")
    private String userPrompt;

    @JSO<PERSON>ield(name = "image_url")
    private String imageUrl;

    @JSONField(name = "image_end_url")
    private String imageEndUrl;

    @JSONField(name = "loop")
    private boolean loop = false;

    @JSONField(name = "expand_prompt")
    private Boolean expandPrompt = true;

    public LumaVideoReq() {}

    public LumaVideoReq(String prompt, String imageUrl, String imageEndUrl, boolean loop) {
        this.userPrompt = prompt == null ? "" : prompt;
        this.imageUrl = imageUrl;
        this.imageEndUrl = imageEndUrl;
        this.loop = loop;
        this.expandPrompt = true;
    }

}
