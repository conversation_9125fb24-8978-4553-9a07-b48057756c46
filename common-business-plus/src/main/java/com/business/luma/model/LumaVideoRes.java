package com.business.luma.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LumaVideoRes {

    @Schema(title = "视频id")
    @JSONField(name = "id")
    private String id;

    @Schema(title = "生成视频指令")
    @JSONField(name = "prompt")
    private String prompt;

    @Schema(title = "状态")
    @JSONField(name = "state")
    private String state;

    @Schema(title = "创建日期")
    @JSONField(name = "created_at")
    private String createdAt;

    @Schema(title = "视频信息")
    @JSONField(name = "video")
    private LumaVideo video;

    @JSONField(name = "liked")
    private String liked;

    @JSONField(name = "estimate_wait_seconds")
    private String estimateWaitSeconds;

    @Data
    public static class LumaVideo {

        @Schema(title = "图片地址")
        private String url;

        @Schema(title = "宽度")
        private String width;

        @Schema(title = "长度")
        private String height;

        @Schema(title = "简略图")
        private String thumbnail;
    }

}
