package com.business.luma.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LumaVideoResp {

    @Schema(title = "视频id")
    @JSONField(name = "id")
    String id;

    @Schema(title = "生成视频指令")
    @JSONField(name = "prompt")
    String prompt;

    @Schema(title = "状态")
    @JSONField(name = "state")
    String state;

    @Schema(title = "创建日期")
    @JSONField(name = "created_at")
    String createdAt;

    @Schema(title = "视频信息")
    @JSONField(name = "video")
    LumaVideo video;

    @Schema(title = "liked")
    @JSONField(name = "liked")
    String liked;

    @Schema(title = "等待时间")
    @JSONField(name = "estimate_wait_seconds")
    String estimateWaitSeconds;


}
