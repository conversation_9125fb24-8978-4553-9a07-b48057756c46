package com.business.luma.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LumaVideo {
    @Schema(title = "图片地址")
    @JSONField(name = "url")
    String url;

    @Schema(title = "图片地址")
    @JSONField(name = "url_no_watermark")
    String urlNoWatermark;

    @Schema(title = "宽度")
    @JSONField(name = "width")
    Integer width;

    @Schema(title = "长度")
    @JSONField(name = "height")
    Integer height;

    @Schema(title = "简略图")
    @JSONField(name = "thumbnail")
    String thumbnail;
}
