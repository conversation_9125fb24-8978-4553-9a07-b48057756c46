package com.business.luma.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LumaVideoGoApiResp {

    @Schema(title = "错误信息")
    @JSONField(name = "error_message")
    String errorMessage;

    @Schema(title = "生成视频信息")
    @JSONField(name = "generation")
    LumaVideoResp generation;

    @Schema(title = "输入信息")
    @JSONField(name = "input")
    String input;

    @Schema(title = "元信息")
    @JSONField(name = "metadata")
    String metadata;

    @Schema(title = "task_id")
    @JSONField(name = "task_id")
    String taskId;

    @Schema(title = "状态")
    @JSONField(name = "status")
    String status;

}
