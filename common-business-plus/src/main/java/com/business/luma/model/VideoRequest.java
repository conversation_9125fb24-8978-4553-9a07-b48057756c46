package com.business.luma.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class VideoRequest {

    @JSONField(name = "id")
    private String id;

    @JSONField(name = "prompt")
    private String prompt;

    @JSONField(name = "state")
    private String state;

    @JSONField(name = "created_at")
    private String createdAt;

    @JSONField(name = "video")
    private String video;

    @JSONField(name = "liked")
    private String liked;

    @JSONField(name = "estimate_wait_seconds")
    private String estimateWaitSeconds;
}
