package com.business.luma;

import com.alibaba.fastjson.JSONObject;
import com.nacos.tool.BrotliInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.util.concurrent.TimeUnit;

//Luma工具类
@Slf4j
public class LumaUploadPicUtil {


    private static final String LUMA_API_URL = "https://api.imgbb.com";

    private static final String API_KEY = "cdac31bcf05e342e28407ac45fdbb619";


    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    public static String getLumaUploadImage(String imgUrl) {
        // 创建表单请求体
        log.info("梦工厂上传图片到imgbb param：" + imgUrl);

        RequestBody formBody = new FormBody.Builder()
                .add("image", imgUrl)
                .build();
        Request request = new Request.Builder()
                .url(LUMA_API_URL + "/1/upload?key="+API_KEY)
                .post(formBody)
                .build();
        try(Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();

            log.info("梦工厂上传图片到imgbb Code：" + response.code());
            log.info("梦工厂上传图片到imgbb resp：" + responseBody);
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                JSONObject data = jsonObject.getJSONObject("data");
                String displayUrl = data.getString("display_url");
                return  displayUrl;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("梦工厂上传图片到imgbb失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    public static void main(String[] args) {
        String lumaUploadImage = getLumaUploadImage("https://image.diandiansheji.com/ai_model/sd/video/image/1846721344244387841.jpg");
        System.out.println(lumaUploadImage);
    }

}
