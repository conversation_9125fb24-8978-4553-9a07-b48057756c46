package com.business.luma.officeModel;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class LumaOfficeVideoReq {

    @J<PERSON><PERSON>ield(name = "prompt")
    private String prompt;

    @J<PERSON><PERSON>ield(name = "aspect_ratio")
    private String aspectRatio = "16:9";

    @JSONField(name = "loop")
    private Boolean  loop = false;

    @JSONField(name = "keyframes")
    private LumaKeyFrames keyframes ;

    public LumaOfficeVideoReq() {
    }

    public LumaOfficeVideoReq(String prompt, boolean loop) {
        this.prompt = prompt;
        this.loop = loop;
    }

    public LumaOfficeVideoReq(String prompt,  boolean loop, LumaKeyFrames keyframes) {
        this.prompt = prompt;
        this.loop = loop;
        this.keyframes = keyframes;
    }

    public LumaOfficeVideoReq(String prompt,  boolean loop, LumaKeyFrames keyframes,String aspectRatio) {
        this.prompt = prompt;
        this.loop = loop;
        this.aspectRatio = aspectRatio;
        this.keyframes = keyframes;
    }
}
