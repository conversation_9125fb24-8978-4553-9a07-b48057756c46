package com.business.luma.officeModel;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LumaOfficeVideoResq {

    @Schema(title = "视频id")
    @JSONField(name = "id")
    private String id;

    @Schema(title = "生成视频指令")
    @JSONField(name = "prompt")
    private String prompt;

    @Schema(title = "状态")
    @JSONField(name = "state")
    private String state;

    @Schema(title = "错误原因")
    @JSONField(name = "failure_reason")
    private String failureReason;

    @Schema(title = "创建日期")
    @JSONField(name = "created_at")
    private String createdAt;

    @Schema(title = "视频信息")
    @JSONField(name = "assets")
    private LumaVideo assets;

    @JSONField(name = "version")
    private String version;

    @JSONField(name = "code")
    private int code;

    @JSONField(name = "request")
    private requestResp request;

    @Data
    public static class LumaVideo {
        @Schema(title = "视频地址")
        private String video;
    }

    @Data
    public static class requestResp {

        @Schema(title = "命令")
        @JSONField(name = "prompt")
        private String prompt;

        @Schema(title = "宽高比")
        @JSONField(name = "aspect_ratio")
        private String aspectRatio;

        @Schema(title = "是否循环")
        @JSONField(name = "loop")
        private boolean loop;

        @Schema(title = "图片")
        @JSONField(name = "keyframes")
        private keyframes keyframes;
    }

    @Data
    public static class keyframes {

        @Schema(title = "首帧")
        @JSONField(name = "frame0")
        private frame frame0;

        @Schema(title = "尾帧")
        @JSONField(name = "frame1")
        private frame frame1;
    }

    @Data
    public static class frame {

        @Schema(title = "首帧")
        @JSONField(name = "type")
        private String type;

        @Schema(title = "尾帧")
        @JSONField(name = "url")
        private String url;
    }

}
