package com.business.luma;

import com.alibaba.fastjson.JSONObject;
import com.business.luma.officeModel.LumaOfficeVideoReq;
import com.business.luma.officeModel.LumaOfficeVideoResq;
import com.business.utils.BDateUtil;
import com.nacos.redis.RedisUtil;
import com.nacos.utils.BFeiShuUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.TimeUnit;

@Schema(title = "Luma官方API工具类")
@Slf4j
public class LumaOfficialApiUtil {

//    private static final String LUMA_API_URL = "https://api.lumalabs.ai";
    private static final String LUMA_API_URL = "https://lumaai.iworks.cn";

    private static final String LUMA_API_KEY = "luma-8a3f3e20-8f90-4926-a474-b7b39906bdbe-e5e7ee05-b6f0-41d2-a36e-7ef2428d3b03";

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .readTimeout(10, TimeUnit.MINUTES)
            .writeTimeout(10, TimeUnit.MINUTES)
            .build();


    // 生成文本到Luma视频
    public static String generateVideoFromLumaText(LumaOfficeVideoReq lumaOfficeVideoReq, String token) {
        // 文本参数
//        String json = "{ \"prompt\": \"an old lady\" }";
//
//        // 文本带循环/长宽比参数
//        String json1 = "{ \"prompt\": \"an old lady\",\"aspect_ratio\": \"16:9\",\"loop\": true }";
//
//        // 使用起始帧
//        String json2 = "{ \"prompt\": \"A tiger walking in snow\", " +
//                "\"keyframes\": { \"frame0\": { \"type\": \"image\", \"url\": \"https://storage.cdn-luma.com/dream_machine/7e4fe07f-1dfd-4921-bc97-4bcf5adea39a/video_0_thumb.jpg\" } } " +
//                "} }";
//
//        // 具有起始帧、循环、宽高比
//        String json3 = "{ \"prompt\": \"A tiger walking in snow\", " +
//                "\"keyframes\": { \"frame0\": { \"type\": \"image\", \"url\": \"https://storage.cdn-luma.com/dream_machine/7e4fe07f-1dfd-4921-bc97-4bcf5adea39a/video_0_thumb.jpg\" } }, " +
//                "\"loop\": false, \"aspect_ratio\": \"16:9\" }";
//
//        // 带有结束帧
//        String json4 = "{ \"prompt\": \"A tiger walking in snow\", \"keyframes\": { \"frame1\": { \"type\": \"image\", \"url\": \"https://storage.cdn-luma.com/dream_machine/7e4fe07f-1dfd-4921-bc97-4bcf5adea39a/video_0_thumb.jpg\" } }, \"loop\": false, \"aspect_ratio\": \"16:9\" }";
//
//        // 具有开始和结束关键帧
//        String json5 = "{ \"prompt\": \"A tiger walking in snow\", \"keyframes\": { \"frame0\": { \"type\": \"image\", \"url\": \"https://storage.cdn-luma.com/dream_machine/7e4fe07f-1dfd-4921-bc97-4bcf5adea39a/video_0_thumb.jpg\" }, \"frame1\": { \"type\": \"image\", \"url\": \"https://storage.cdn-luma.com/dream_machine/7e4fe07f-1dfd-4921-bc97-4bcf5adea39a/video_0_thumb.jpg\" } }, \"loop\": false, \"aspect_ratio\": \"16:9\" }";
//
//        // 延长视频
//        String json6 = "{ \"prompt\": \"The tiger rolls around\", \"keyframes\": { \"frame0\": { \"type\": \"generation\", \"id\": \"123e4567-e89b-12d3-a456-426614174000\" } } }";
//
//        // 反向延长视频
//        String json7 = "{ \"prompt\": \"The tiger rolls around\", \"keyframes\": { \"frame1\": { \"type\": \"generation\", \"id\": \"123e4567-e89b-12d3-a456-426614174000\" } } }";
//
//        // 使用结束帧来延长视频
//        String json8 = "{ \"prompt\": \"The tiger rolls around\", \"keyframes\": { \"frame0\": { \"type\": \"generation\", \"id\": \"123e4567-e89b-12d3-a456-426614174000\" }, \"frame1\": { \"type\": \"image\", \"url\": \"https://storage.cdn-luma.com/dream_machine/7e4fe07f-1dfd-4921-bc97-4bcf5adea39a/video_0_thumb.jpg\" } } }";
//
//        // 使用起始帧反向扩展视频
//        String json9 = "{ \"prompt\": \"The tiger rolls around\", \"keyframes\": { \"frame0\": { \"type\": \"image\", \"url\": \"https://storage.cdn-luma.com/dream_machine/7e4fe07f-1dfd-4921-bc97-4bcf5adea39a/video_0_thumb.jpg\" }, \"frame1\": { \"type\": \"generation\", \"id\": \"123e4567-e89b-12d3-a456-426614174000\" } } }";
//
//        // 在两个视频之间进行插值
//        String json10 = "{ \"prompt\": \"The tiger rolls around\", \"keyframes\": { \"frame0\": { \"type\": \"generation\", \"id\": \"123e4567-e89b-12d3-a456-426614174000\" }, \"frame1\": { \"type\": \"generation\", \"id\": \"123e4567-e89b-12d3-a456-426614174000\" } } }";

        String jsonParam = JSONObject.toJSONString(lumaOfficeVideoReq);
        RequestBody body = RequestBody.create(jsonParam, MediaType.get("application/json; charset=utf-8"));
        log.info("generateVideoFromLumaText param={}", jsonParam);
        Request request = new Request.Builder()
                .url(LUMA_API_URL + "/dream-machine/v1/generations") ///v1/generations
                .post(body)
                .addHeader("accept", "application/json")
                .addHeader("authorization", "Bearer "+ token)
                .build();

        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("generateVideoFromLumaText code={}",response.code());
                log.info("generateVideoFromLumaText responsebody={}",responseBody);
                if (response.isSuccessful() || response.code() == 201) {
                    if (null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        return jsonObject.getString("id");
                    }
                } else if (response.code() == 429) {
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "梦工厂任务提交太多",  "reqParam= " + jsonParam);
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex) {
                        return null;
                    }
                }else if(response.code() == 400){
                    if (attempt == 3) {
//                        responseBody = "{\"detail\":\"Insufficient credits\"}";
                        String dateKey = RedisUtil.REDIS_LUMA_ACCOUNT_PREFIX+"-"+ BDateUtil.getYearAndMonthAndDayAndHour();
                        String value = RedisUtil.getValue(token);
                        if(StringUtils.isBlank(value)) {
                            JSONObject jsonObject = JSONObject.parseObject(responseBody);
                            String detail = jsonObject.getString("detail");
                            if ("Insufficient credits".equals(detail))
                                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "梦工厂任务提交失败", "resp= " + responseBody + " 积分不足,请充值！");
                            RedisUtil.setValue(dateKey, "1");
                        }
                        return null;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("postTextToVideoLuma error: " + e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "梦工厂任务提交失败", "ERRORInfo=" + e.getMessage() + ",reqParam= " + jsonParam);
                    return null;
                }
            }
        }
        return null;
    }

    // 拉取Luma任务结果
    public static LumaOfficeVideoResq fetchVideoFromLumaText(String taskId,String key) {
        Request request = new Request.Builder()
                .url(LUMA_API_URL + "/dream-machine/v1/generations/" + taskId)
                .get()
                .addHeader("accept", "application/json")
                .addHeader("authorization", "Bearer "+ key)
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("luma视频返回Code = " + response.code());
                log.info("luma视频返回参数 = " + responseBody);
                if (response.isSuccessful()) {
                    if (null != response.body()) {
                        LumaOfficeVideoResq generation = JSONObject.parseObject(responseBody, LumaOfficeVideoResq.class);
                        return generation;
                    }
                } else if (403 == response.code()) {
                    if (attempt == 3) {
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex) {
                        return null;
                    }
                }else if(response.code() == 400){
                    if (attempt == 3) {
//                        responseBody = "{\"detail\":\"Insufficient credits\"}";
                        String dateKey = RedisUtil.REDIS_LUMA_ACCOUNT_PREFIX+"-"+ BDateUtil.getYearAndMonthAndDayAndHour();
                        String value = RedisUtil.getValue(key);
                        if(StringUtils.isBlank(value)) {
                            JSONObject jsonObject = JSONObject.parseObject(responseBody);
                            String detail = jsonObject.getString("detail");
                            if ("Insufficient credits".equals(detail))
                                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "梦工厂任务提交失败", "resp= " + responseBody + " 积分不足！");
                            RedisUtil.acquireLock(dateKey, 3600);
                        }
                        return null;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "梦工厂拉取任务失败", "ERRORInfo=" + e.getMessage() + ", jobId= " + taskId);
            }
        }

        return null;
    }

    // 获取所有支持的相机运动
    public static String getLumaCameraMotionList() {
        Request request = new Request.Builder()
                .url(LUMA_API_URL + "/dream-machine/v1/generations/camera_motion/list")
                .get()
                .addHeader("accept", "application/json")
                .addHeader("authorization", "Bearer "+ LUMA_API_KEY)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                System.out.println(response.body().string());
            } else {
                System.out.println("Request failed: " + response.code());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static void main(String[] args) {
//        System.out.println(generateVideoFromLumaText());
//        System.out.println(fetchVideoFromLumaText("5f9da1f1-9374-4306-a3f2-f0fe83265192","luma-8a3f3e20-8f90-4926-a474-b7b39906bdbe-e5e7ee05-b6f0-41d2-a36e-7ef2428d3b03"));
//        System.out.println(fetchVideoFromLumaText("6e507042-52ce-434a-b22c-a30c1058b141","luma-8a3f3e20-8f90-4926-a474-b7b39906bdbe-e5e7ee05-b6f0-41d2-a36e-7ef2428d3b03"));
        System.out.println(fetchVideoFromLumaText("576aee17-a1ab-412d-a889-4fa4a1d669a0","luma-8a3f3e20-8f90-4926-a474-b7b39906bdbe-e5e7ee05-b6f0-41d2-a36e-7ef2428d3b03"));
//        System.out.println(getLumaCameraMotionList());
    }

}
