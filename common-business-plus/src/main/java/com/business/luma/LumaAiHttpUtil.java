package com.business.luma;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.business.luma.model.*;
import com.business.utils.BUrlUtil;
import com.nacos.tool.BrotliInterceptor;
import com.nacos.utils.BFeiShuUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.File;
import java.util.concurrent.TimeUnit;

//Luma工具类
@Slf4j
public class LumaAiHttpUtil {
    // apivirginia-lumalabs.developer-ce9.workers.dev
    //https://apivirginia-lumalabs.developer-ce9.workers.dev

    private static final String LUMA_API_URL = "https://internal-api.virginia.labs.lumalabs.ai";
    private static int currentIndex = 0;
    //翻墙用的url
//    private static final String LUMA_API_URL_V2 = "https://lumaai.iworks.cn";
//    private static final String LUMA_API_URL_V2 = "https://api.goapi.ai";
    private static final String[] LUMA_API_URL_LIST = new String[]{ "https://proxy.goapi.xyz","https://api.goapi.xyz","https://api.midjourneyapi.xyz","https://api.goapi.ai"};

    private static  String LUMA_API_URL_V2 = getNextDomain();
    private static final String X_API_KEY = "8592048652aaa68c5ec6956a707dfa9c87dcced64863d69d43dec0b92e9bf2bb";
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    public static LumaPicResp getLumaUploadImageSecretKey(String token) {
        RequestBody requestBody = RequestBody.create("", MediaType.parse("application/json"));
        Request request = new Request.Builder()
                .url(LUMA_API_URL_V2 + "/api/photon/v1/generations/file_upload?file_type=image&filename=file.jpg")
                .post(requestBody)
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Cookie",token)
                .addHeader("Origin", "https://lumalabs.ai")
                .addHeader("Priority", "u=1, i")
                .addHeader("Referer", "https://lumalabs.ai/")
                .addHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        try(Response response = client.newCall(request).execute()) {
            log.error("梦工厂上传图片Code：" + response.code());
            String responseBody = response.body().string();
            System.out.println("====梦工厂="+responseBody);
            if (response.isSuccessful()) {
                return  JSON.parseObject(responseBody, new TypeReference<LumaPicResp>() {});
            }
        } catch (Exception e) {
            log.error("梦工厂上传图片失败：{}", e.getMessage());
            LUMA_API_URL_V2 = getNextDomain();
            return null;
        }
        return null;
    }

    //上传图片
    public static boolean uploadImageToLuma(File file,String token, String presignedUrl) {
        RequestBody fileBody = RequestBody.create(file,MediaType.parse("image/*"));
        Request request = new Request.Builder()
                .url(presignedUrl)
                .put(fileBody)
                .addHeader("accept", "application/json, text/plain, */*")
                .addHeader("content-type", "application/json")
                .addHeader("cookie",token)
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Length", "2")
                .addHeader("Origin", "https://lumalabs.ai")
                .addHeader("Referer", "https://lumalabs.ai/")
                .addHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        try(Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return true;
            }
        } catch (Exception e) {
            log.error("梦工厂图片上传失败：{}", e.getMessage());
            LUMA_API_URL_V2 = getNextDomain();
            return false;
        }
        return false;
    }

    public static String postLumaToGenerationVideo(LumaVideoReq lumaVideoReq, String token) {
        Request request = new Request.Builder()
//                .url(LUMA_API_URL_V2 + "/api/photon/v1/generations/")
                .url(LUMA_API_URL_V2 + "/api/luma/v1/video")
                .post(RequestBody.create(JSONObject.toJSONString(lumaVideoReq), MediaType.parse("application/json")))
                .addHeader("Content-Type", "application/json")
                .addHeader("X-API-Key",token)
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("梦工厂视频提交Code：" + response.code() + "。 请求结果为：" + responseBody);
                if (response.code() == 200 ) {
                    if (null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String data = jsonObject.getString("data");
                        JSONObject jsonObjectData = JSONObject.parseObject(data);
                        return jsonObjectData.getString("task_id");
                    }
                } else {
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"梦工厂任务提交失败","ERRORInfo="+ BUrlUtil.respErrorInfoByCode(response.code())+", reqParam= "+JSONObject.toJSONString(lumaVideoReq));
                        return null;
                    }
                    LUMA_API_URL_V2 = getNextDomain();
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            } catch (Exception e) {
                log.error("梦工厂视频提交失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"梦工厂任务提交失败","ERRORInfo="+ e.getMessage()+",reqParam= "+JSONObject.toJSONString(lumaVideoReq));
                    return null;
                }
                LUMA_API_URL_V2 = getNextDomain();
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                }
            }
        }
        return null;
    }

    public static LumaVideoGoApiResp getLumaVideoJobState(String jobId, String token) {
        Request request = new Request.Builder()
//                .url(LUMA_API_URL_V2 + "/api/photon/v1/generations/" + jobId)
                .url(LUMA_API_URL_V2 + "/api/luma/v1/video/" + jobId)
                .get()
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("X-API-Key",token)
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                log.error("梦工厂获取视频任务Code：" + response.code());
                if (response.isSuccessful()) {
                    if (null != response.body()) {
                        String responseBody = response.body().string();
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String data = jsonObject.getString("data");
                        LumaVideoGoApiResp generation =JSONObject.parseObject(data,LumaVideoGoApiResp.class);
                        log.info(JSONObject.toJSONString(generation));
                        return generation;
                    }
                } else {
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"梦工厂拉取任务失败","ERRORInfo="+ BUrlUtil.respErrorInfoByCode(response.code())+", jobId= "+jobId);
                        return null;
                    }
                    LUMA_API_URL_V2 = getNextDomain();
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex) {
                        return null;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("梦工厂模式获取视频失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"梦工厂拉取任务失败","ERRORInfo="+ e.getMessage()+", jobId= "+jobId);
                    return null;
                }
                LUMA_API_URL_V2 = getNextDomain();
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    return null;
                }
            }
        }
        return null;
    }

    public static String getDownloadVideoUrl(String jobId, String token) {
        Request request = new Request.Builder()
                .url(LUMA_API_URL_V2 + "/api/photon/v1/generations/"+ jobId +"/download_video_url")
                .get()
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .addHeader("Cookie",token)
                .addHeader("Origin", "https://lumalabs.ai")
                .addHeader("Priority", "u=1, i")
                .addHeader("Referer", "https://lumalabs.ai/")
                .addHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .build();
        try(Response response = client.newCall(request).execute()) {
            log.error("梦工厂视频下载任务Code：" + response.code());
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                return jsonObject.getString("url");
            }
        } catch (Exception e) {
            log.error("梦工厂视频下载任务失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    public static String postLumaToGenerationVideoExtend(VideoExtendReq videoExtendReq, String jobId,  String token) {
        Request request = new Request.Builder()
//                .url(LUMA_API_URL_V2 + "/api/photon/v1/generations/"+ jobId +"/extend")
                .url(LUMA_API_URL_V2 + "/api/luma/v1/video/"+ jobId +"/extend")
                .post(RequestBody.create(JSONObject.toJSONString(videoExtendReq), MediaType.parse("application/json")))
                .addHeader("Accept", "application/json")
                .addHeader("Content-Type", "application/json")
                .addHeader("X-API-Key", token)
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try(Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("梦工厂视频延长Code="+ response.code());
                log.info("梦工厂视频延长responseBody="+ responseBody);
                if (response.code() == 200 ) {
                    if(null != response.body()){
                        if (null != response.body()) {
                            JSONObject jsonObject = JSONObject.parseObject(responseBody);
                            String data = jsonObject.getString("data");
                            JSONObject jsonObjectData = JSONObject.parseObject(data);
                            return jsonObjectData.getString("task_id");
                        }
                    }
                } else {
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"梦工厂延长视频失败","ERRORInfo="+ BUrlUtil.respErrorInfoByCode(response.code())+", jobId= "+jobId);
                        return null;
                    }
                    LUMA_API_URL_V2 = getNextDomain();
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex) {
                        return null;
                    }
                }
            } catch (Exception e) {
                log.info("梦工厂视频延长失败：{}",e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"梦工厂延长视频失败","ERRORInfo="+ e.getMessage()+", jobId= "+jobId);
                    return null;
                }
                LUMA_API_URL_V2 = getNextDomain();
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    return null;
                }
            }
        }
        return null;
    }

    public static String getNextDomain(){
        int length = LUMA_API_URL_LIST.length;
         currentIndex = (currentIndex + 1) % length;
        return LUMA_API_URL_LIST[currentIndex];
    }

    public static void main(String[] args) {
//        LumaVideoReq lumaVideoReq = new LumaVideoReq();
//        lumaVideoReq.setUserPrompt("cat");
//        lumaVideoReq.setImageUrl("https://idotdesign.oss-cn-beijing.aliyuncs.com/mj/00000a00-33d8-4979-9dc1-ec7e00890455_0_0.webp");
//        String s = postLumaToGenerationVideo(lumaVideoReq, X_API_KEY);
//        System.out.println(s);


        String jobId = "d0d1728f-46b7-4ee6-bc27-0c3cef8e1025";
//        String jobId = "bcd32cce-630d-4ea9-9f08-0f5e003dc605";
        LumaVideoGoApiResp lumaVideoJobState = getLumaVideoJobState(jobId, X_API_KEY);
        System.out.println(JSON.toJSONString(lumaVideoJobState));

        VideoExtendReq videoExtendReq = new VideoExtendReq("cat");


//        String s = postLumaToGenerationVideoExtend(videoExtendReq, jobId, X_API_KEY);
//        System.out.println(s);



    }

}
