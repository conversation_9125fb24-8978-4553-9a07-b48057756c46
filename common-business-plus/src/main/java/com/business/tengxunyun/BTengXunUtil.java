package com.business.tengxunyun;

import com.alibaba.fastjson2.JSONObject;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.tms.v20200713.TmsClient;
import com.tencentcloudapi.tms.v20200713.models.TextModerationRequest;
import com.tencentcloudapi.tms.v20200713.models.TextModerationResponse;
import com.tencentcloudapi.tmt.v20180321.TmtClient;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateRequest;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Slf4j
public class BTengXunUtil {

    //腾讯翻译
    public static String textToEnglish(String prompt, String source, String target) {
        try{
            Credential cred = new Credential(SECRETID, SECRETKEY);
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(TEXT_TRANSLATE_ENDPOINT);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            TmtClient client = new TmtClient(cred, TRANSLATION_REGIONID, clientProfile);
            TextTranslateRequest textTranslateRequest = new TextTranslateRequest();
            textTranslateRequest.setSourceText(prompt);
            textTranslateRequest.setSource(source);
            textTranslateRequest.setTarget(target);
            textTranslateRequest.setProjectId(0L);
            textTranslateRequest.setUntranslatedText("无");
            TextTranslateResponse resp = client.TextTranslate(textTranslateRequest);
            return null != resp ? resp.getTargetText() : null;
        }catch (Exception e){
            log.error("translateToEnglish= {}", e.getMessage(), e);
            prompt = null;
        }
        return prompt;
    }

    //文案审核:true为审核不通过
    public static boolean textToExamineFailByText(String prompt, Long userId){
        return textToExamineFail(prompt, userId,"1775359428921397248");
    }
    //绘图审核:true为审核不通过
    public static boolean textToExamineFailByDraw(String prompt, Long userId){
        return textToExamineFail(prompt, userId,"1775359992086401024");
    }
    //音频审核:true为审核不通过
    public static boolean textToExamineFailByAudio(String prompt, Long userId){
        return textToExamineFail(prompt, userId,"1775369396710019072");
    }
    //视频审核:true为审核不通过
    public static boolean textToExamineFailByVideo(String prompt, Long userId){
        return textToExamineFail(prompt, userId,"1775371006610021058");
    }
    // 文本审核
    public static boolean textToExamineFailText(String prompt, Long userId){
        return textToExamineFail(prompt, userId,"1775359428921397248");
    }

    /**
     * 文本审核
     * @param prompt 文本内容
     * @param userId 用户id
     * @param scene 审核场景
     * @return 审核结果
     */
    private static boolean textToExamineFail(String prompt, Long userId, String scene) {
        try {
            //false为过滤审核成功
            //true为过滤审核失败
            if (prompt == null || prompt.isEmpty()){
                return false;
            }
            if (scene == null || scene.isEmpty()){
                return true;
            }
            TmsClient client = getTmsClient();
            // 设置文本审核请求
            TextModerationRequest req = new TextModerationRequest();
            // 设置文本审核内容
            req.setContent(Base64.getEncoder().encodeToString(prompt.getBytes(StandardCharsets.UTF_8)));
            // 设置文本审核业务类型
            req.setBizType(scene);
            // 设置文本审核数据id
            req.setDataId(String.valueOf(userId));
            // 获取文本审核响应
            TextModerationResponse resp = client.TextModeration(req);
            // 获取文本审核建议
            String suggestion = resp.getSuggestion();
            // 打印文本审核响应
            System.out.println("resp="+ JSONObject.toJSONString(resp));
            // 如果文本审核建议为通过或审核
            if("Pass".equals(suggestion) || "Review".equals(suggestion)){
                return false;
            }
        } catch (TencentCloudSDKException e) {
            log.error("textToExamine TencentCloudSDKException= {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("textToExamine Exception= {}", e.getMessage(), e);
        }
        return true;
    }

    /**
     * 获取文本审核客户端
     * @return 文本审核客户端
     */
    @NotNull
    private static TmsClient getTmsClient() {
        // 获取文本审核客户端
        Credential cred = new Credential(SECRETID, SECRETKEY);
        // 设置文本审核接口
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint(TEXT_SECURITY_ENDPOINT);
        // 设置文本审核客户端
        ClientProfile clientProfile = new ClientProfile();
        // 设置文本审核接口
        clientProfile.setHttpProfile(httpProfile);
        // 返回文本审核客户端
        return new TmsClient(cred,  TRANSLATION_REGIONID, clientProfile);
    }

    // Id
    public static final String SECRETID = "AKIDncHoIerz1zscCanojVAfQ0oU1To056EC";
    // key
    public static final String SECRETKEY = "0qX2OafYEj4KbfxKYgjcNEwlV6fEahq6";
    // 内容安全审核接口
    public static final String TEXT_SECURITY_ENDPOINT = "tms.tencentcloudapi.com";
    // 文本翻译接口
    public static final String TEXT_TRANSLATE_ENDPOINT = "tmt.tencentcloudapi.com";
    // 翻译区域
    public static final String TRANSLATION_REGIONID = "ap-beijing";

}
