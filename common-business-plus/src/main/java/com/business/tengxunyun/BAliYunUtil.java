package com.business.tengxunyun;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.alimt20181012.models.TranslateGeneralResponse;
import com.aliyun.green20220302.models.*;
import com.business.enums.AliyunTextDetectionServiceEnum;
import com.nacos.utils.BFeiShuUtil;
import lombok.extern.slf4j.Slf4j;

import com.alibaba.fastjson.JSON;
import com.aliyun.green20220302.Client;
import com.aliyun.teaopenapi.models.Config;
import org.apache.commons.lang3.StringUtils;


@Slf4j
public class BAliYunUtil {

//    public static final String ACCESSKEYID = "LTAI5t9A9sRyNs1a2AsDT7Hp";
//    public static final String SECRETACCESSKEY = "******************************";

    public static final String ACCESSKEYID = "LTAI5tQPrtMzh2WyjAmUxnhZ";
    public static final String SECRETACCESSKEY = "******************************";
    // 在类加载时就初始化（翻译）
    private static final com.aliyun.alimt20181012.Client INSTANCE;

    static {
        try {
            com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                    .setAccessKeyId(ACCESSKEYID)
                    .setAccessKeySecret(SECRETACCESSKEY);
//            config.endpoint = "mt.cn-hangzhou.aliyuncs.com";
            config.regionId = "cn-beijing";
            config.endpoint = "mt.aliyuncs.com";
//            config.endpoint = "mt-vpc.cn-hangzhou.aliyuncs.com";

            // 设置超时时间，单位是毫秒
            config.setConnectTimeout(5000);  // 设置连接超时时间为5秒
            config.setReadTimeout(10000);    // 设置读取超时时间为10秒

            INSTANCE = new com.aliyun.alimt20181012.Client(config);
        } catch (Exception e) {
            throw new RuntimeException("初始化 Aliyun Client 失败", e);
        }
    }
    // 提供访问实例的公共方法
    public static com.aliyun.alimt20181012.Client getClient() {
        return INSTANCE;
    }

    // 静态内部类用于实现单例模式（文本检测）
    private static class ClientHolder {
        private static final com.aliyun.green20220302.Client INSTANCE;

        static {
            try {
                com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                        .setAccessKeyId(ACCESSKEYID)
                        .setAccessKeySecret(SECRETACCESSKEY);
                config.regionId = "cn-beijing";
                config.endpoint = "green-cip.cn-beijing.aliyuncs.com";
//                config.endpoint = "green-cip-vpc.cn-beijing.aliyuncs.com";

                // 设置超时时间，单位是毫秒
                config.setConnectTimeout(5000);  // 设置连接超时时间为5秒
                config.setReadTimeout(10000);    // 设置读取超时时间为10秒

                INSTANCE = new com.aliyun.green20220302.Client(config);
            } catch (Exception e) {
                throw new RuntimeException("初始化 Aliyun Client 失败", e);
            }
        }
    }
    // 提供全局访问 Client 的方法
    public static com.aliyun.green20220302.Client getDetectionClient() {
        return ClientHolder.INSTANCE;
    }

    public static String textToEnglish(String prompt) {
        for(int i=1;i<=3;i++) {
            com.aliyun.alimt20181012.Client client = null;
            try {
                client = BAliYunUtil.getClient();
            } catch (Exception e) {
                System.out.println("aliyun翻译client 初始化失败！");
                return null;
            }
            com.aliyun.alimt20181012.models.TranslateGeneralRequest translateGeneralRequest = new com.aliyun.alimt20181012.models.TranslateGeneralRequest()
                    .setSourceLanguage("zh")
                    .setScene("general")
                    .setSourceText(prompt)
                    .setTargetLanguage("en")
                    .setFormatType("text");
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            try {
                // 复制代码运行请自行打印 API 的返回值
                TranslateGeneralResponse translateGeneralResponse = client.translateGeneralWithOptions(translateGeneralRequest, runtime);
                if (translateGeneralResponse != null && translateGeneralResponse.getBody() != null) {
                    String body = JSONObject.toJSONString(translateGeneralResponse.getBody());
                    JSONObject jsonObject = JSONObject.parseObject(body);
                    JSONObject data = jsonObject.getJSONObject("data");
                    log.info("textToEnglish 返回code=" + jsonObject.getIntValue("code"));
                    log.info("textToEnglish 返回resp=" + data.toString());
                    return data.getString("translated");
                }
            } catch (Exception error) {
                error.printStackTrace();
                if(i==3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "阿里云翻译失败", "textToEnglish.error:" + error.getMessage());
                }
            }
        }
        return null;
    }

    //文本检测
    public static Boolean AliyunDetectionPlus(String prompt,String serviceName)throws Exception  {
        if(StringUtils.isBlank(prompt)){
            return false;
        }
        Config config = new Config();
        config.setAccessKeyId(ACCESSKEYID);
        config.setAccessKeySecret(SECRETACCESSKEY);
        //接入区域和地址请根据实际情况修改
        config.setRegionId("cn-shanghai");
        config.setEndpoint("green-cip.cn-shanghai.aliyuncs.com");
        //连接时超时时间，单位毫秒（ms）。
        config.setReadTimeout(6000);
        //读取时超时时间，单位毫秒（ms）。
        config.setConnectTimeout(3000);
        Client client = new Client(config);

        JSONObject serviceParameters = new JSONObject();

        serviceParameters.put("content", prompt.trim());

        TextModerationPlusRequest textModerationPlusRequest = new TextModerationPlusRequest();
        // 检测类型
        textModerationPlusRequest.setService(serviceName);
        textModerationPlusRequest.setServiceParameters(serviceParameters.toJSONString());

        try {
            TextModerationPlusResponse response = client.textModerationPlus(textModerationPlusRequest);
            if (response.getStatusCode() == 200) {
                TextModerationPlusResponseBody result = response.getBody();
                Integer code = result.getCode();
                if (200 == code) {
                    TextModerationPlusResponseBody.TextModerationPlusResponseBodyData data = result.getData();
                    String riskLevel = data.getRiskLevel();
                    System.out.println(JSON.toJSONString(data, true));
                    if("none".equals(riskLevel)){
                        return false;
                    }
                }
            } else {
                System.out.println("response not success. status:" + response.getStatusCode());
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"阿里云文本检测 not success","textDetection.error:");
            }
        } catch (Exception e) {
            e.printStackTrace();
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"阿里云文本检测失败","textDetection.error:"+e.getMessage());
        }
        return true;
    }

    //文本检测
    public static Boolean AliyunDetection(String prompt,String serviceName) throws Exception {

        for(int i=1;i<=3;i++) {
            com.aliyun.green20220302.Client client = BAliYunUtil.getDetectionClient();
            com.aliyun.green20220302.models.TextModerationRequest textModerationRequest = new com.aliyun.green20220302.models.TextModerationRequest()
                    .setService(serviceName)
                    .setServiceParameters("{\"content\":\"" + prompt + "\"}");
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            try {
                // 复制代码运行请自行打印 API 的返回值
                TextModerationResponse textModerationResponse = client.textModerationWithOptions(textModerationRequest, runtime);
                TextModerationResponseBody body = textModerationResponse.getBody();
                log.info("AliyunDetection 返回code=" + body.getCode());
                if (200 == body.getCode()) {
                    TextModerationResponseBody.TextModerationResponseBodyData data = body.getData();
                    log.info("AliyunDetection 返回resp=" + JSON.toJSONString(body.getData()));
                    String reason = data.getReason();
                    System.out.println(reason);
                    if (StringUtils.isBlank(reason)) {
                        return false;
                    }
                }
            } catch (Exception error) {
                // 诊断地址
                if(i == 3) {
                    error.printStackTrace();
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "阿里云文本检测失败", "AliyunDetection.error:" + error.getMessage());
                }
            }
        }
        return true;
    }


    //视频文本检测
    public static Boolean videoDetection(String prompt)throws Exception  {
        return AliyunDetection(prompt, AliyunTextDetectionServiceEnum.VIDEOSERVICE.getName());
    }
    //音乐文本检测
    public static Boolean audioDetection(String prompt)throws Exception  {
        return AliyunDetection(prompt, AliyunTextDetectionServiceEnum.AUDIOSERVICE.getName());
    }
    //图片文本检测
    public static Boolean imageDetection(String prompt)throws Exception  {
        return AliyunDetection(prompt, AliyunTextDetectionServiceEnum.IMAGESERVICE.getName());
    }
    //语音文本检测
    public static Boolean speechDetection(String prompt)throws Exception  {
        return AliyunDetection(prompt, AliyunTextDetectionServiceEnum.SPEECHSERVICE.getName());
    }
    //文本检测
    public static Boolean textADetection(String prompt)throws Exception  {
        return AliyunDetection(prompt, AliyunTextDetectionServiceEnum.TEXTSERVICE.getName());
    }

    public static void main(String[] args) throws Exception {
        System.out.println("return："+videoDetection( "34jin1 ping"));
        System.out.println("return："+textToEnglish("你好吗，我的朋友"));
    }

}
