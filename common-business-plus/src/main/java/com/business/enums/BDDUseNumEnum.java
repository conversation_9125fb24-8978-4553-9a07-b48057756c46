package com.business.enums;

import com.nacos.enums.ImgOptModelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.Objects;

@Getter
@Schema(name = "点点使用使用数量枚举", description = "点点使用使用数量枚举")
public enum BDDUseNumEnum {

    MJ_DRAW("4", 4d, "2", 2d),//会员2个点子
    SD_DRAW("4", 4d, "2", 2d),//会员2个点子
    LE_DRAW("4", 4d, "2", 2d),//会员2个点子
    FLUX_DRAW("2", 2d, "0", 0d),//会员2个点子
    SUNO_AUDIO("4", 4d, "2", 2d),//会员5个点子
    JIMENG_DRAW("8", 8d, "4", 4d),//会员4个点子\
    XINGYE_DRAW("8", 8d, "4", 4d),//会员4个点子


    //mj操作点子登记
    Mj_OPT_VARY_REGION("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_ZOOM("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_FACE_FUSION("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_VARY_SUBTLE("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_VARY_STRONG("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_PAN_TOP("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_PAN_BOTTOM("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_PAN_LEFT("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_PAN_RIGHT("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_ZOOM_MAKE("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_ZOOM_CHANGE_AR("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_UPSCALE_2X("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_UPSCALE_4X("4", 4d, "2", 2d),//会员2个点子

    Mj_OPT_FINE_TUNING_REMIX("4", 4d, "2", 2d),//会员2个点子
    Mj_OPT_FINE_TUNING_REMIX_SUBTLE("4", 4d, "2", 2d),//会员2个点子

    //视频生成扣点子
    SD_OPT_VIDEO("10", 10d, "10", 10d),//SD视频
    LE_OPT_VIDEO("10", 10d, "5", 5d),//LE视频
    BYTE_OPT_VIDEO("4", 4d, "2", 2d),//字节视频
    PIKA_OPT_VIDEO("10", 10d, "5", 5d),//皮卡视频
    DOMO_OPT_VIDEO("10", 10d, "5", 5d),//哆莫视频
    LUMA_OPT_VIDEO("20", 20d, "20", 20d),//梦工厂视频
    RUNWAY2_OPT_VIDEO("10", 10d, "5", 5d),//RUNWAY-gen2视频
    RUNWAY3A_OPT_VIDEO("10", 10d, "5", 5d),//RUNWAY-gen3A视频

    ZHIPU_OPT_VIDEO("10", 10d, "5", 5d),//智谱视频
    HAILUO_OPT_VIDEO("60", 60d, "30", 30d),//海螺视频
    HAILUO_V2_OPT_VIDEO("60", 60d, "30", 30d),//海螺视频
    KLING_OPT_VIDEO("30", 30d, "15", 15d),//可灵视频
    DREAMFACTORY_OPT_VIDEO("80", 80d, "40", 40d),//梦工厂2.0视频-（wanx-模型）
    DAFENQI2_OPT_VIDEO("50", 50d, "25", 25d),//达芬奇1.5视频
    DAFENQI3_OPT_VIDEO("2", 2d, "1", 1d),//达芬奇视频转绘
    DAFENQI4_OPT_VIDEO("2", 2d, "1", 1d),//达芬奇角色驱动

    // 写真
    GOAPI_OPT_PHOTO("4", 4d, "2", 2d),//会员2个点子

    //高清重绘操作点子登记
    GOAPI_OPT_UPSCALE_2X("4", 4d, "2", 2d),//会员2个点子
    GOAPI_OPT_UPSCALE_4X("8", 8d, "4", 4d),//会员4个点子
    GOAPI_OPT_UPSCALE_8X("16", 16d, "8", 8d),//会员4个点子
    LEAPI_OPT_REDRAW("8", 8d, "4", 4d),//重绘

    // 控制图片
    SDAPI_OPT_SKETCH("4", 4d, "2", 2d),//上色
    SDAPI_OPT_STRUCTURE("4", 4d, "2", 2d),//风格迁移
    SDAPI_OPT_INPAINT("4", 4d, "2", 2d),//替换=局部修改

    LEAPI_OPT_SKETCH("4", 4d, "2", 2d),//le上色

    BYTEAPI_OPT_INPAINTING("4", 4d, "2", 2d),//抹除
    BYTEAPI_OPT_OUTPAINT("4", 4d, "2", 2d),//自由拓展
    BYTEAPI_OPT_FREE_SCALING("4", 4d, "2", 2d),//自由缩放
    BYTEAPI_OPT_ONECLICK_POSTER("4", 4d, "2", 2d),//一键海报-金刚区

    CREATIVE_PROMPT("1", 1d, "1", 1d),//创意提示词扣点子

    ;
    @Schema(description = "使用数量展示")
    private final String ddUseNumStr;

    @Schema(description = "使用数量计算")
    private final Double ddUseNumDou;

    @Schema(description = "会员使用数量展示")
    private final String ddVipUseNumStr;

    @Schema(description = "会员使用数量计算")
    private final Double ddVipUseNumDou;

    BDDUseNumEnum(String ddUseNumStr, Double ddUseNumDou, String ddVipUseNumStr, Double ddVipUseNumDou) {
        this.ddUseNumStr = ddUseNumStr;
        this.ddUseNumDou = ddUseNumDou;
        this.ddVipUseNumStr = ddVipUseNumStr;
        this.ddVipUseNumDou = ddVipUseNumDou;
    }

    public static double getOptMJ(Integer optType, boolean isVip) {
        if (ImgOptModelEnum.OPERATE_EDIT_VARY_REGION.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_VARY_REGION, isVip);
        }
        if (Objects.equals(ImgOptModelEnum.OPERATE_EDIT_ZOOM.getValue(), optType)) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_ZOOM, isVip);
        }
        if (Objects.equals(ImgOptModelEnum.OPERATE_EDIT_FACE_FUSION.getValue(), optType)) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_FACE_FUSION, isVip);
        }
        if (Objects.equals(ImgOptModelEnum.OPERATE_EDIT_VARY_STRONG.getValue(), optType)) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_VARY_STRONG, isVip);
        }
        if (Objects.equals(ImgOptModelEnum.OPERATE_EDIT_VARY_SUBTLE.getValue(), optType)) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_VARY_SUBTLE, isVip);
        }
        if (Objects.equals(ImgOptModelEnum.OPERATE_EDIT_PAN_TOP.getValue(), optType)) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_PAN_TOP, isVip);
        }
        if (Objects.equals(ImgOptModelEnum.OPERATE_EDIT_PAN_BOTTOM.getValue(), optType)) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_PAN_BOTTOM, isVip);
        }
        if (Objects.equals(ImgOptModelEnum.OPERATE_EDIT_PAN_LEFT.getValue(), optType)) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_PAN_LEFT, isVip);
        }
        if (Objects.equals(ImgOptModelEnum.OPERATE_EDIT_PAN_RIGHT.getValue(), optType)) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_PAN_RIGHT, isVip);
        }
        if (Objects.equals(ImgOptModelEnum.OPERATE_EDIT_ZOOM_MAKE.getValue(), optType)) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_ZOOM_MAKE, isVip);
        }
        if (Objects.equals(ImgOptModelEnum.OPERATE_EDIT_UPSCALE_2X.getValue(), optType)) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_UPSCALE_2X, isVip);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_UPSCALE_4X.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_UPSCALE_4X, isVip);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_FINE_TUNING_REMIX.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_FINE_TUNING_REMIX, isVip);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_FINE_TUNING_REMIX_SUBTLE.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_FINE_TUNING_REMIX_SUBTLE, isVip);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_CHANGE_AR.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.Mj_OPT_ZOOM_CHANGE_AR, isVip);
        }
        if (ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_2X.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.GOAPI_OPT_UPSCALE_2X, isVip);
        }
        if (ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_4X.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.GOAPI_OPT_UPSCALE_4X, isVip);
        }
        if (ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_REDRAW.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.LEAPI_OPT_REDRAW, isVip);
        }
        if (ImgOptModelEnum.CONTROL_OPERATE_EDIT_SKETCH_PAINT.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.SDAPI_OPT_SKETCH, isVip);
        }
        if (ImgOptModelEnum.LE_OPERATE_EDIT_SKETCH_PAINT.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.LEAPI_OPT_SKETCH, isVip);
        }
        if (ImgOptModelEnum.CONTROL_OPERATE_EDIT_STRUCTURE_STYLE.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.SDAPI_OPT_STRUCTURE, isVip);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_INPAINT_REPLACE.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.SDAPI_OPT_INPAINT, isVip);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_HS_INPAINTING.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.BYTEAPI_OPT_INPAINTING, isVip);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_OUTPAINT_ZOOM.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.BYTEAPI_OPT_OUTPAINT, isVip);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_FREE_SCALING.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.BYTEAPI_OPT_FREE_SCALING, isVip);
        }
        if (ImgOptModelEnum.GOAPI_OPERATE_EDIT_PHOTO.getValue() == optType) {
            return getDDUseByIsVip(BDDUseNumEnum.GOAPI_OPT_PHOTO, isVip);
        }
        return isVip ? 2d : 4d;//不存在扣10个
    }

    /**
     * 根据是否是会员获取使用数量
     * 
     * @param bddUseNumEnum 使用数量枚举
     * @param isVip         是否是会员
     * @return 使用数量
     */
    public static double getDDUseByIsVip(BDDUseNumEnum bddUseNumEnum, boolean isVip) {
        if (isVip) {
            return bddUseNumEnum.ddVipUseNumDou;
        }
        return bddUseNumEnum.ddUseNumDou;
    }

    public static BDDUseNumEnum getBDDUseNumEnumByOptType(Integer optType) {
        if (ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_2X.getValue() == optType) {
            return BDDUseNumEnum.GOAPI_OPT_UPSCALE_2X;
        }
        if (ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_4X.getValue() == optType) {
            return BDDUseNumEnum.GOAPI_OPT_UPSCALE_4X;
        }
        if (ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_8X.getValue() == optType) {
            return BDDUseNumEnum.GOAPI_OPT_UPSCALE_8X;
        }
        if (ImgOptModelEnum.HIGH_OPERATE_EDIT_UPSCALE_REDRAW.getValue() == optType) {
            return BDDUseNumEnum.LEAPI_OPT_REDRAW;
        }
        if (ImgOptModelEnum.LE_OPERATE_EDIT_SKETCH_PAINT.getValue() == optType) {
            return BDDUseNumEnum.LEAPI_OPT_SKETCH;
        }
        if (ImgOptModelEnum.CONTROL_OPERATE_EDIT_SKETCH_PAINT.getValue() == optType) {
            return BDDUseNumEnum.SDAPI_OPT_SKETCH;
        }
        if (ImgOptModelEnum.CONTROL_OPERATE_EDIT_STRUCTURE_STYLE.getValue() == optType) {
            return BDDUseNumEnum.SDAPI_OPT_STRUCTURE;
        }
        if (ImgOptModelEnum.OPERATE_EDIT_INPAINT_REPLACE.getValue() == optType) {
            return BDDUseNumEnum.SDAPI_OPT_INPAINT;
        }
        if (ImgOptModelEnum.OPERATE_EDIT_HS_INPAINTING.getValue() == optType) {
            return BDDUseNumEnum.BYTEAPI_OPT_INPAINTING;
        }
        if (ImgOptModelEnum.OPERATE_EDIT_OUTPAINT_ZOOM.getValue() == optType) {
            return BDDUseNumEnum.BYTEAPI_OPT_OUTPAINT;
        }
        return null;
    }

    // 视频独立的获取消耗点子数接口
    public static double getBDDUseNumEnumByModel(int modelId, boolean isVip) {
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_SD_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.SD_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_LE_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.LE_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_BYTE_LENS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.BYTE_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_PIKA_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.PIKA_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_DOMO_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.DOMO_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_LUMA_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.LUMA_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY2_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.RUNWAY2_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY3AT_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.RUNWAY3A_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_ZHIPU_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.ZHIPU_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.HAILUO_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_V2_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.HAILUO_V2_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_KLING_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.KLING_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.DREAMFACTORY_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI2_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.DAFENQI2_OPT_VIDEO, isVip);
        }
        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI3_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.DAFENQI3_OPT_VIDEO, isVip);
        }

        if (ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI4_BASICS.getValue() == modelId) {
            return getDDUseByIsVip(BDDUseNumEnum.DAFENQI4_OPT_VIDEO, isVip);
        }
        return 0;
    }

    // 金刚区消耗点子显示到前端
    public static BDDUseNumEnum getOptConsumptionDzString(long functionId) {
        if (BFunctionConfigEnum.FUNCTION_CONFIG_PHOTO.getIntValue() == functionId) {
            return BDDUseNumEnum.GOAPI_OPT_PHOTO; //写真
        }
        if (BFunctionConfigEnum.FUNCTION_CONFIG_PAINT.getIntValue() == functionId) {
            return BDDUseNumEnum.LEAPI_OPT_SKETCH; //上色
        }
        if (BFunctionConfigEnum.FUNCTION_CONFIG_STYLE.getIntValue() == functionId) {
            return BDDUseNumEnum.LEAPI_OPT_SKETCH; //风格
        }
        if (BFunctionConfigEnum.FUNCTION_CONFIG_OUTPAINT.getIntValue() == functionId) {
            return BDDUseNumEnum.BYTEAPI_OPT_OUTPAINT; //拓展
        }
        if (BFunctionConfigEnum.FUNCTION_CONFIG_FREE_SCALING.getIntValue() == functionId) {
            return BDDUseNumEnum.BYTEAPI_OPT_FREE_SCALING; //缩放
        }
        if (BFunctionConfigEnum.FUNCTION_CONFIG_ONECLICK_POSTER.getIntValue() == functionId) {
            return BDDUseNumEnum.BYTEAPI_OPT_ONECLICK_POSTER; //海报
        }
        return null;
    }

}