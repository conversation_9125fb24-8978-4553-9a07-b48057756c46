package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name="用户状态", description="用户状态")
public enum BUserStatusEnum {

    NORMAL_STATUS(0, "用户状态正常"),

    LOCK_STATUS(1, "账号已锁定，请联系客服"),
    ;

    @Schema(description = "账号id")
    private final int status;

    @Schema(description = "描述")
    private final String describe;

    BUserStatusEnum(int status, String describe) {
        this.status = status;
        this.describe = describe;
    }

}
