package com.business.enums.user;

import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * ios应用内支付产品id枚举
 */
@Getter
public enum IOSInAppEnum {
    //会员权益购买
    svip01("ddsjapp_svip_1",1,"829","1680","851","1","无折扣",0),
    svip02("ddsjapp_svip_2",2,"228","420","192","1","无折扣",0),
    svip03("ddsjapp_svip_3",3,"82","140","58","1","无折扣",0),

    vip01("ddsjapp_vip_1",4,"473","840","367","1","无折扣",0),
    vip02("ddsjapp_vip_2",5,"131","210","79","1","无折扣",0),
    vip03("ddsjapp_vip_3",6,"47","70","23","1","无折扣",0),

    //普通用户购买
    JYB_01("ddsjapp_jyb_1",9,"24","24","0","1","无折扣",1),
    JYB_02("ddsjapp_jyb_2",10,"48","48","0","1","无折扣",1),
    JYB_03("ddsjapp_jyb_3",11,"96","96","0","1","无折扣",1),
    JYB_04("ddsjapp_jyb_4",12,"144","144","0","1","无折扣",1),
    JYB_05("ddsjapp_jyb_5",13,"192","192","0","1","无折扣",1),
    JYB_06("ddsjapp_jyb_6",14,"240","240","0","1","无折扣",1),

    //vip购买
    JYB_VIP_01("ddsjapp_jyb_vip_1",9,"22.8","","","0.95","95折",2),
    JYB_VIP_02("ddsjapp_jyb_vip_2",10,"42.8","","","0.9","9折",2),
    JYB_VIP_03("ddsjapp_jyb_vip_3",11,"80.8","","","0.85","85折",2),
    JYB_VIP_04("ddsjapp_jyb_vip_4",12,"114","","","0.8","8折",2),
    JYB_VIP_05("ddsjapp_jyb_vip_5",13,"143","","","0.75","75折",2),
    JYB_VIP_06("ddsjapp_jyb_vip_6",14,"167","","","0.7","7折",2),

    //svip购买
    JYB_SVIP_01("ddsjapp_jyb_svip_1",9,"20.8","","","0.85","85折",3),
    JYB_SVIP_02("ddsjapp_jyb_svip_2",10,"38","","","0.8","8折",3),
    JYB_SVIP_03("ddsjapp_jyb_svip_3",11,"71.8","","","0.75","75折",3),
    JYB_SVIP_04("ddsjapp_jyb_svip_4",12,"99.8","","","0.7","7折",3),
    JYB_SVIP_05("ddsjapp_jyb_svip_5",13,"124","","","0.65","65折",3),
    JYB_SVIP_06("ddsjapp_jyb_svip_6",14,"143","","","0.6","6折",3),



    SVIP_V2_01("ddsjapp_svipv2_1",21,"58.8","100","41.2","1","无折扣",0),
    SVIP_V2_02("ddsjapp_svipv2_2",22,"117","200","83","1","无折扣",0),
    SVIP_V2_03("ddsjapp_svipv2_3",23,"233","400","167","1","无折扣",0),
    SVIP_V2_04("ddsjapp_svipv2_4",24,"290","500","210","1","无折扣",0),
    SVIP_V2_05("ddsjapp_svipv2_5",25,"558","1300","742","1","无折扣",0),
    SVIP_V2_06("ddsjapp_svipv2_6",26,"1148","2600","1452","1","无折扣",0),
    SVIP_V2_07("ddsjapp_svipv2_7",27,"2248","5200","2952","1","无折扣",0),
    SVIP_V2_08("ddsjapp_svipv2_8",28,"2798","6500","3702","1","无折扣",0),
    SVIP_V2_09("ddsjapp_jxhy",29,"998","907","7164","1","无折扣",0),

    JYB_V2_01("ddsjapp_jybv2_1",31,"58.8","100","41.2","1","无折扣",1),
    JYB_V2_02("ddsjapp_jybv2_2",32,"117","200","83","1","无折扣",1),
    JYB_V2_03("ddsjapp_jybv2_3",33,"233","400","167","1","无折扣",1),
    JYB_V2_04("ddsjapp_jybv2_4",34,"290","500","210","1","无折扣",1),
    JYB_V2_05("ddsjapp_jybv2_5",35,"349","600","251","1","无折扣",1),
    JYB_V2_06("ddsjapp_jybv2_6",36,"463","800","337","1","无折扣",1),

    ;
    final String productId;
    final Integer vipConfigId;
    final BigDecimal price;
    final String markPrice;
    final String savePrice;
    final BigDecimal discountRatio;
    final String discountRatioStr;
    final Integer type;

    IOSInAppEnum(String productId, Integer vipConfigId, String price, String markPrice, String savePrice, String discountRatio, String discountRatioStr, Integer type) {
        this.productId = productId;
        this.vipConfigId = vipConfigId;
        this.price = new BigDecimal(price);
        this.markPrice = markPrice;
        this.savePrice = savePrice;
        this.discountRatio = new BigDecimal(discountRatio);
        this.discountRatioStr = discountRatioStr;
        this.type = type;
    }
    public static IOSInAppEnum getIOSInAppEnum(String productId) {
        for (IOSInAppEnum iosInAppEnum : IOSInAppEnum.values()) {
            if (iosInAppEnum.productId.equals(productId)) {
                return iosInAppEnum;
            }
        }
        return null;
    }

    //获取vip购买的价格信息
    public static IOSInAppEnum getIOSInAppEnumVipByVipConfigId(Integer vipConfigId) {
        for (IOSInAppEnum iosInAppEnum : IOSInAppEnum.values()) {
            if (Objects.equals(iosInAppEnum.vipConfigId, vipConfigId) && iosInAppEnum.type == 0) {
                return iosInAppEnum;
            }
        }
        return null;
    }

    //获取加油包购买的价格
    public static IOSInAppEnum getIOSInAppEnumJybByVipConfigId(Integer vipConfigId) {
        for (IOSInAppEnum iosInAppEnum : IOSInAppEnum.values()) {
            if (Objects.equals(iosInAppEnum.vipConfigId, vipConfigId) && Objects.equals(iosInAppEnum.type, 1)) {
                return iosInAppEnum;
            }
        }
        return null;
    }
}
