package com.business.enums;

import cn.hutool.core.thread.ThreadException;
import lombok.Getter;

import java.util.Objects;

/**
 * 通知相关的枚举类
 * @date 2023-12-29
 */
@Getter
public enum BNotificationEnum {

    // 通知状态 0单挑已读 1全部已读
    ONE_READ(0),
    ALL_READ(1),

    // 通知类型 App使用 0系统消息 1绘画任务 2支付消息 3会员到期 4点子到期 5写真任务 6视频任务 7音乐任务 8签到通知 10高清重绘任务 101 通用站内通知 201 活动类消息
    SYS_NOTIF(0, "", ""),
    DRAW_NOTIF(1,"作品已完成", "你的作品已完成, 点击查看"),
    PAY_NOTIF(2,"", ""),
    MEMBER_EXPIRE_NOTIF(3,"", ""),
    DZ_EXPIRE_NOTIF(4,"", ""),

    //PHOTO_NOTIF(5,"写真已完成", "查看您的新写真作品"),
    //VIDEO_NOTIF(6,"任务成功", "你的作品已完成, 点击查看"),
    //AUDIO_NOTIF(7,"音乐创作已完成", "音乐创作已完成"),

    PHOTO_NOTIF(5,"作品已完成", "你的作品已完成, 点击查看"),
    VIDEO_NOTIF(6,"作品已完成", "你的作品已完成, 点击查看"),
    AUDIO_NOTIF(7,"作品已完成", "你的作品已完成, 点击查看"),

    SIGN_NOTIF(8,"", ""),
    HIGH_REDRAW(10,"作品已完成", "你的作品已完成, 点击查看"),

    // 其他通知类消息
    OTHER_NOTIF(101, "", ""),
    // 活动类消息
    ACTIVITY_NOTIF(201, "", ""),
    // 失败统一处理知类消息
    FAILURE_NOTIF(503, "作品生成失败", "你的作品生成失败，点数已返还。"),

    ;


    private final Integer intValue;
    private String strTitle;
    private String strContent;

    BNotificationEnum(Integer intValue) {
        this.intValue = intValue;
    }

    BNotificationEnum(Integer intValue, String strTitle, String strContent) {
        this.intValue = intValue;
        this.strTitle = strTitle;
        this.strContent = strContent;
    }

    public static BNotificationEnum getTitleAndContentByNotType(Integer notType) {
        if (Objects.equals(notType, BNotificationEnum.DRAW_NOTIF.getIntValue())) {
            return BNotificationEnum.DRAW_NOTIF;
        }
        if (Objects.equals(notType, BNotificationEnum.PHOTO_NOTIF.getIntValue())) {
            return BNotificationEnum.PHOTO_NOTIF;
        }
        if (Objects.equals(notType, BNotificationEnum.VIDEO_NOTIF.getIntValue())) {
            return BNotificationEnum.VIDEO_NOTIF;
        }
        if (Objects.equals(notType, BNotificationEnum.AUDIO_NOTIF.getIntValue())) {
            return BNotificationEnum.AUDIO_NOTIF;
        }
        if (Objects.equals(notType, BNotificationEnum.HIGH_REDRAW.getIntValue())) {
            return BNotificationEnum.HIGH_REDRAW;
        }
        return null;
    }



}
