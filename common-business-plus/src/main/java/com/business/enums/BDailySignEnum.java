package com.business.enums;

import lombok.Getter;

@Getter
public enum BDailySignEnum {

    SIGN_MONDAY(1001, "周一"),
    SIGN_TUESDAY(1002, "周二"),
    SIGN_WEDNESDAY(1003, "周三"),
    SIGN_THURSDAY(1004, "周四"),
    SIGN_FRIDAY(1005, "周五"),
    SIGN_SATURDAY(1006, "周六"),
    SIGN_SUNDAY(1007, "周日"),
    ;

    private Integer code;
    private String week;

    BDailySignEnum(Integer code, String week) {
        this.code = code;
        this.week = week;
    }

}
