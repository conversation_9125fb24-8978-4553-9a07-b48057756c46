package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name = "公共错误返回内容枚举信息", description = "公共错误返回内容枚举信息")
public enum BRedisKeyEnum {

    // ===========================================开始==============================================
    DD_USE_RULE("DD_USE_RULE"),// 点子扣除redis key

    DRAW_RECORD_TASK_MJ_LOCK("DRAW_RECORD_TASK:MJ_LOCK"),//任务监听MJ锁:true为可操作；false为禁止操作
    DRAW_RECORD_TASK_MJ_PRO_ACCOUNT("DRAW_RECORD_TASK:PRO_ACCOUNT"),//绘图mj 超级账号：下次任务准备使用的fast账号
    DRAW_RECORD_TASK_MJ_PT_ACCOUNT("DRAW_RECORD_TASK:PT_ACCOUNT"),//绘图mj 一般账号：下次任务准备使用的relaxed账号

    DRAW_RECORD_TASK_MJ_PRO_ACCOUNT_LIST("DRAW_RECORD_TASK:PRO_ACCOUNT_LIST"),// 绘图mj 超级账号：（有效）列表-用于分配账号信息
    DRAW_RECORD_TASK_MJ_PT_ACCOUNT_LIST("DRAW_RECORD_TASK:PT_ACCOUNT_LIST"),// 绘图mj 一般账号：列表


    DRAW_MJ_APP_VERSION("DRAW_MJ:APP_VERSION"),// 绘图mj app版本
    DRAW_MJ_USER_AGENT("DRAW_MJ:USER_AGENT"),// 绘图mj 请求版本
    DRAW_MJ_JOB_ID("DRAW_MJ:JOB_ID"),// 绘图mj 任务缓存
    DRAW_MJ_FADE_IN_JOB_ID("DRAW_MJ:FADE_IN_JOB_ID"),// 绘图mj 渐显图片信息缓存


    //定时任务锁：防止重复执行；防止获取异常
    TASK_LOCK_REDIS_MJ_DRAW_STATE("TASK_LOCK:REDIS_MJ_DRAW_STATE"),// mj 刷新状态锁：不存在或false则执行；存在true则不执行（如果携带id则是校验是否为快速账号）
    TASK_LOCK_REDIS_MJ_DRAW_FAIL_HANDLE("TASK_LOCK:REDIS_MJ_DRAW_FAIL_HANDLE"),// mj 绘图失败处理锁：不存在或false则执行；存在true则不执行
    TASK_LOCK_MJ_DRAW_SUCCESS_SAVE_JOB_ID("TASK_LOCK:MJ_DRAW_SUCCESS_SAVE_JOB_ID"),// mj 保存时使用防止重复数据

    //mj绘图排队问题处理
    TASK_MJ_ACCOUNT_USER_COUNT("TASK_MJ_ACCOUNT:USER_COUNT"),//


    //mj账号刷新使用
    TASK_LOCK_REDIS_MJ_ACCOUNT_REFRESH("TASK_LOCK:REDIS_MJ_ACCOUNT_REFRESH"),// mj账号刷新锁：绘图服务使用
    TASK_MJ_ACCOUNT_REFRESH("TASK_MJ_ACCOUNT:REFRESH"),// 需要刷新时登记，true刷新；false；不刷新
    TASK_MJ_ACCOUNT_COOKIE("TASK_MJ_ACCOUNT:COOKIE"),// token刷新时cookie登记

    AUDIO_TASK_CREATION("AUDIO_TASK:JOB_CREATION"),//音频创作-任务锁
    AUDIO_TASK_STATUS("AUDIO_TASK:JOB_STATUS"),//音频状态-任务锁
    AUDIO_ACCOUNT_TOKEN("AUDIO_ACCOUNT:TOKEN"),//音频token获取
    AUDIO_ACCOUNT_ROUTE_SWITCHING("AUDIO_ACCOUNT:ROUTE_SWITCHING"),//音频路由切换 1是suno音乐、2是海螺minimax 音乐
    AUDIO_ACCOUNT_CLERK_JS_VERSION("AUDIO_ACCOUNT:CLERK_JS_VERSION"),//音频token接口版本
    TASK_LOCK_REDIS_AUDIO_ACCOUNT_REFRESH("TASK_LOCK:REDIS_AUDIO_ACCOUNT_REFRESH"),// AUDIO账号刷新锁：绘图服务使用
    DOWNLOAD_LOCK_REDIS_RUNWAY_IMG("DOWNLOAD_LOCK:REDIS_RUNWAY_IMG"),// 下载runway图片文件锁：视频服务使用
    // ===========================================结束==============================================

    //系统内部使用
    VIP_GRADE_PT("VIP_GRADE:PT"),//普通用户权限
    VIP_GRADE_VIP("VIP_GRADE:VIP"),//vip用户权限
    VIP_GRADE_SVIP("VIP_GRADE:SVIP"),//svip用户权限
    VIP_RIGHTS_CONFIG("VIP_RIGHTS_CONFIG:RIGHTS"),//vip会员权限配置


    INV_CODE("InvCode"),// 邀请码放重复key

    SEND_VERIFY_CODE("code:verify"),// 手机号验证码使用
    SEND_REGISTER_CODE("code:register"),// 账号注册手机号

    REDIS_KEY_PREFIX_TOKEN_APP("APPTOKEN"),// app token
    REDIS_KEY_PREFIX_TOKEN("TOKEN"),// web token

    MJ_WEB_COOKIES("MJ:WEB_COOKIES"),// mj web cookies关联对应mj账号 userId

    MJ_WEB_DRAW_SUBMIT_TASK("MJ:WEB_DRAW_SUBMIT_TASK"),// mj web 提交任务任务

    MJ_ACCOUNT_WEB_COOKIES("MJ:WEB_COOKIES"),// mj web cookies关联对应mj账号 userId
    MJ_ACCOUNT_DRAW_AUTOMATIC_RELAXED("MJ:DRAW:AUTOMATIC_RELAXED"),//账号绘图自动设置慢速
    //不可用账号登记
    MJ_ACCOUNT_ALL_NOT("MJ:ACCOUNT_ALL_NOT"),//mj账号绘图全部不可用
    MJ_ACCOUNT_FAST_NOT("MJ:ACCOUNT_FAST_NOT"),//账号绘图快速不可用
    MJ_ACCOUNT_RELAXED_NOT("MJ:ACCOUNT_RELAXED_NOT"),//账号绘图慢速不可用
    MJ_ACCOUNT_TOKEN_REFRESH_LOCK("MJ:ACCOUNT_TOKEN_REFRESH_LOCK"),// mj账号任务刷新锁
    MJ_ACCOUNT_SPEED_RATIO("MJ:ACCOUNT_SPEED_RATIO"),// mj账号速度分配比例
    MJ_ACCOUNT_RELAXED_OPEN("MJ:ACCOUNT_RELAXED_OPEN"),//慢速开关
    MJ_USER_NOT_USE("MJ:USER_NOT_USE"),//用户禁用mj操作
    MJ_DOWNLOAD_IMAGES_TOKEN("MJ:DOWNLOAD_IMAGES_TOKEN"),//下载图片token

    MJ_WEB_ACCOUNT_INFO_CACHE("MJ:WEB_ACCOUNT_INFO_CACHE"),// mjweb账号信息缓存=
    MJ_WEB_ACCOUNT_INFO_CACHE_TURTLE("MJ:WEB_ACCOUNT_INFO_CACHE_TURTLE"),// mjweb账号信息缓存=龟速

    SECRET_KEY_THIRD_PARTY_KEY_CACHE("SECRET_KEY:THIRD_PARTY_KEY_CACHE"),// 第三方密钥信息缓存

    // TODO 已删除不使用---luma
    REDIS_LUMA_TOKEN_KEY("SECRET_KEY:REDIS_LUMA_TOKEN_KEY"),

    REDIS_PIKA_TOKEN_KEY("SECRET_KEY:REDIS_PIKA_TOKEN_KEY"),// 第三方密钥信息缓存

    REDIS_RUNWAY_TOKEN_KEY("SECRET_KEY:REDIS_RUNWAY_TOKEN_KEY"),// 第三方密钥信息缓存RUNWAY
    REDIS_RUNWAY_ASSET_GROUP_ID("SECRET_KEY:REDIS_RUNWAY_ASSET_GROUP_ID"),// 第三方密钥信息缓存RUNWAY,assetGroupId
    REDIS_RUNWAY_TOKEN_USERNAME_PASS_KEY("SECRET_KEY:REDIS_RUNWAY_USERNAME_PASS_KEY"),// 第三方密钥信息缓存

    //飞书AccessToken
    FEISHU_ACCESSTOKEN_TOKEN("FEISHU_ACCESSTOKEN:TOKEN"),// token刷新


    //运营推广弹窗管理缓存操作
    ACTIVITY_PROMOTION_RULE_CACHE_PT("ACTIVITY:PROMOTION_RULE_CACHE_PT"),// 运营用户弹窗规则信息缓存
    ACTIVITY_PROMOTION_RULE_CACHE_PRO("ACTIVITY:PROMOTION_RULE_CACHE_PRO"),// 运营用户弹窗规则信息缓存
    ACTIVITY_PROMOTION_CONTEXT_CACHE_USERID("ACTIVITY:PROMOTION_CONTEXT_CACHE_USERID"),// 运营用户弹窗控制缓存
    ACTIVITY_PROMOTION_NOT_PUSH_CACHE_USERID("ACTIVITY:PROMOTION_NOT_PUSH_CACHE_USERID"),// 运营用户是否弹窗登记：默认24小时

    //运营推广弹窗到达率统计：每日凌晨定时统计
    ACTIVITY_PROMOTION_PUSH_SUCCESS_CACHE_USERID("ACTIVITY:PROMOTION_PUSH_SUCCESS_CACHE_USERID"),// 运营推送到达
    ACTIVITY_PROMOTION_PUSH_OK_CACHE_USERID("ACTIVITY:PROMOTION_PUSH_OK_CACHE_USERID"),// 运营推送成功点击

    NOTES_NOTICE_SVIP("NOTES_NOTICE:SVIP"),// 通知svip缓存

    MJ_ACCOUNT_LOCK("MJ:ACCOUNT_LOCK"),// mj web 账号锁定一分钟

    /* mj休闲模式限制频率 */
    MJ_CASUAL_LIMITING_FREQUENCY("MJ:CASUAL_LIMITING_FREQUENCY"),

    JIANGXIN_NO("USER:JIANGXIN_NO"),// 匠心会员编号

    REDIS_STYLE_CONFIG_PREFIX("MJ:STYLE:CONFIG:PREFIX:"),//风格码缓存
    ;

    @Schema(description = "redis缓存key")
    private final String key;

    BRedisKeyEnum(String key) {
        this.key = key;
    }

    public static String getAddKeyStr(BRedisKeyEnum bRedisKeyEnum, String addKey) {
        return bRedisKeyEnum.getKey() + ":" + addKey;
    }

}
