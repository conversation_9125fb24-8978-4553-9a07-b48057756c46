package com.business.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Getter
public enum BReportAuditConfigEnum {

    //金刚区 功能列表枚举 1是绘图及每日精选
    WORKS_AUDIT_RESERVE(0, "保留作品"),
    WORKS_AUDIT_DELETE(1, "删除作品"),


    USER_AUDIT_NORMAL(0, "无"),
    USER_AUDIT_FORBIDDEN_1(1, "禁用1天"),
    USER_AUDIT_FORBIDDEN_7(2, "禁用7天"),
    USER_AUDIT_FORBIDDEN_14(3, "禁用14天"),
    USER_AUDIT_FORBIDDEN_30(4, "禁用30天"),
    USER_AUDIT_DISABLED(5, "永久禁用"),

    ;

    public final Integer intValue;
    public final String strName;

    BReportAuditConfigEnum(Integer intValue, String strName) {
        this.intValue = intValue;
        this.strName = strName;
    }

    public static BReportAuditConfigEnum fromIntWorksHandleValue(int intValue) {
        List<BReportAuditConfigEnum> worksHandleEnumList = new ArrayList<>(Arrays.asList(
                BReportAuditConfigEnum.WORKS_AUDIT_RESERVE,
                BReportAuditConfigEnum.WORKS_AUDIT_DELETE
        ));
        for (BReportAuditConfigEnum config : worksHandleEnumList) {
            if (config.intValue.equals(intValue)) {
                return config;
            }
        }
        return null;
    }

    public static BReportAuditConfigEnum fromIntUserHandleValue(int intValue) {
        List<BReportAuditConfigEnum> userHandleEnumList = new ArrayList<>(Arrays.asList(
                BReportAuditConfigEnum.USER_AUDIT_NORMAL,
                BReportAuditConfigEnum.USER_AUDIT_FORBIDDEN_1,
                BReportAuditConfigEnum.USER_AUDIT_FORBIDDEN_7,
                BReportAuditConfigEnum.USER_AUDIT_FORBIDDEN_14,
                BReportAuditConfigEnum.USER_AUDIT_FORBIDDEN_30,
                BReportAuditConfigEnum.USER_AUDIT_DISABLED
        ));
        for (BReportAuditConfigEnum config : userHandleEnumList) {
            if (config.intValue.equals(intValue)) {
                return config;
            }
        }
        return null;
    }


}
