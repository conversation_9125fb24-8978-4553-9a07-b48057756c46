package com.business.enums;

import com.nacos.enums.ImgOptModelEnum;
import lombok.Getter;

@Getter
public enum BFunctionConfigEnum {

    //金刚区 功能列表枚举 1是绘图及每日精选
    FUNCTION_CONFIG_SELECTED(1, "每日精选"),
    FUNCTION_CONFIG_PHOTO(2, "创意写真"),
    FUNCTION_CONFIG_VIDEO(3, "创意视频"),
    FUNCTION_CONFIG_AUDIO(4, "灵感音乐"),

    FUNCTION_CONFIG_UPSCALE(5, "高清重绘"),
    FUNCTION_CONFIG_PAINT(6, "原稿上色"),
    FUNCTION_CONFIG_STYLE(7, "风格迁移"),
    FUNCTION_CONFIG_INPAINT(8, "局部修改"),
    FUNCTION_CONFIG_OUTPAINT(9, "自由拓展"),
    FUNCTION_CONFIG_FREE_SCALING(10, "自由拓展"),
    FUNCTION_CONFIG_ONECLICK_POSTER(201, "一键海报"),

    ;

    public Integer intValue;
    public String strName;

    BFunctionConfigEnum(Integer intValue, String strName) {
        this.intValue = intValue;
        this.strName = strName;
    }

    public static Integer getAttributeEditByIntValue(int intValue) {
        if (intValue == BFunctionConfigEnum.FUNCTION_CONFIG_PAINT.getIntValue()) {
            //return ImgOptModelEnum.CONTROL_OPERATE_EDIT_SKETCH_PAINT.getValue(); SD上色效果不好，暂时不用
            return ImgOptModelEnum.LE_OPERATE_EDIT_SKETCH_PAINT.getValue(); //LE上色正在使用
        }
        if (intValue == BFunctionConfigEnum.FUNCTION_CONFIG_STYLE.getIntValue()) {
            return ImgOptModelEnum.CONTROL_OPERATE_EDIT_STRUCTURE_STYLE.getValue();
        }
        if (intValue == BFunctionConfigEnum.FUNCTION_CONFIG_INPAINT.getIntValue()) {
            return ImgOptModelEnum.OPERATE_EDIT_INPAINT_REPLACE.getValue();
        }
        if (intValue == BFunctionConfigEnum.FUNCTION_CONFIG_OUTPAINT.getIntValue()) {
            return ImgOptModelEnum.OPERATE_EDIT_OUTPAINT_ZOOM.getValue();
        }
        if (intValue == BFunctionConfigEnum.FUNCTION_CONFIG_FREE_SCALING.getIntValue()) {
            return ImgOptModelEnum.OPERATE_EDIT_FREE_SCALING.getValue();
        }
        return null;
    }

}
