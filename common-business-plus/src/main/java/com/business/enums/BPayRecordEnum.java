package com.business.enums;

import lombok.Getter;

@Getter
public enum BPayRecordEnum {
    //1-支付宝，2-微信
    TYPE_ITEM_PAY_ALIPAYAPP(11,"支付宝APP支付"),
    TYPE_ITEM_PAY_ALIPAYNATIVE(12,"支付宝扫码支付"),
    TYPE_ITEM_PAY_ALIPAYH5(13,"支付宝扫码支付"),
    TYPE_ITEM_PAY_WXPAYAPP(21,"微信APP支付"),
    TYPE_ITEM_PAY_WXPAYNATIVE(22,"微信扫码支付"),
    TYPE_ITEM_PAY_WXPAYH5(23,"H5拉起支付"),
    TYPE_ITEM_PAY_IOS_IN_APP(99,"苹果应用内支付"),
    TYPE_ITEM_PAY_ACTIVITY_SVIP(31,"活动赠送SVIP"),

    /* 支付记录状态 0，未支付 1，支付成功 2，支付失败*/
    STATE_PAY_NOT(0,"未支付"),
    STATE_PAY_SUCCESS(1,"支付成功"),
    STATE_PAY_FAIL(2,"支付失败"),

    ORDER_TYPE_JYB(1,"加油包支付"),
    ORDER_TYPE_VIP(2,"VIP会员登陆"),
    ORDER_TYPE_SVIP(3,"SVIP会员登陆"),

    USER_STATE_NOT(0,"未使用"),
    USER_STATE_ING(1,"使用中"),
    USER_STATE_END(2,"已用完"),
    USER_STATE_EXPIRED(3,"已失效"),

    ;
    final Integer intValue;
    final String strValue;
    BPayRecordEnum(Integer intValue, String strValue) {
        this.intValue = intValue;
        this.strValue = strValue;
    }
}
