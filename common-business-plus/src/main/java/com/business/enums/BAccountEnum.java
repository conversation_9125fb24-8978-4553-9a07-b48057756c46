package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name="固定账号枚举", description="固定账号枚举")
public enum BAccountEnum {

    APP_AUDIT_ACCOUNT("***********","1234",null, "app审核专用账号"),

    OFFICIAL_ACCOUNT_ONE("***********","3713",1749996069591289857L, "点点官方账号"),

    ;
    @Schema(description = "手机号")
    private final String mobile;

    @Schema(description = "授权码")
    private final String authCode;

    @Schema(description = "账号id")
    private final Long accountId;

    @Schema(description = "描述")
    private final String describe;

    BAccountEnum(String mobile, String authCode, Long accountId, String describe) {
        this.mobile = mobile;
        this.authCode = authCode;
        this.accountId = accountId;
        this.describe = describe;
    }

    public static BAccountEnum getBAccountEnum(String mobile, String authCode) {
        for (BAccountEnum bAccountEnum : BAccountEnum.values()) {
            if (bAccountEnum.mobile.equals(mobile) && bAccountEnum.authCode.equals(authCode)) {
                return bAccountEnum;
            }
        }
        return null; // 如果找不到匹配的手机号码，则返回null或者抛出异常，具体根据业务需求而定
    }

}
