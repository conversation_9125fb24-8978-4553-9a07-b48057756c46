package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.concurrent.TimeUnit;

@Getter
@Schema(name="缓存有效时间配置", description="缓存有效时间配置")
public enum BRedisExpireTimeEnum {

    TOKEN_EXPIRE_TIME(30,TimeUnit.DAYS),//Token有效时间

    ;
    @Schema(description = "有效时间")
    private final Integer value;

    @Schema(description = "有效时间单位")
    private final TimeUnit timeUnit;

    BRedisExpireTimeEnum(Integer value, TimeUnit timeUnit) {
        this.value = value;
        this.timeUnit = timeUnit;
    }
}
