package com.business.enums;

import lombok.Getter;

/**
 * 通知模板枚举
 */
@Getter
public enum BNotificationTmplEnum {

    // 验证码code 短信模板
    MOBILE_VERIFY_TEMPLATE(1001, "",""),
    // 用户异常操作通知code
    USER_ERROR_TEMPLATE(1002, "", ""),
    // 手机号更换通知code
    MOBILE_CHANGE_TEMPLATE(1003, "",""),
    // 会员到期提醒code
    MEMBER_DUE_TEMPLATE(1004, "会员到期提醒","亲爱的${name}，您的${title}权益即将于${date}到期。为了无缝体验我们的AI绘画工具并继续享受会员权益，请及时进行充值。"),
    MEMBER_DUE_TEMPLATE_YHC(10040, "会员到期提醒","亲爱的${name}，您的${title}权益即将于${date}到期。为了无缝体验我们的AI助理工具并继续享受会员权益，请及时进行充值。"),
    // 加油包充值成功通知code
    DATA_PLUS_TOPUP_TEMPLATE(1005, "下单成功","亲爱的${name},您于${date}已成功购买 ${title}加油包权益 ,支付金额${amount}，现在您可以尽情探索我们的AI绘画工具，释放您的创意潜能。期待您的杰作！"),
    DATA_PLUS_TOPUP_TEMPLATE_YHC(10050, "下单成功","亲爱的${name},您于${date}已成功购买 ${title}加油包权益 ,支付金额${amount}，现在您可以尽情探索我们的AI助理工具。"),
    // 会员充值code
    MEMBER_TOPUP_TEMPLATE(1006, "下单成功","亲爱的${name},您于${date}已成功购买${grade}会员权益 ,支付金额${amount}，现在您可以尽情探索我们的AI绘画工具，释放您的创意潜能。期待您的杰作！"),
    MEMBER_TOPUP_TEMPLATE_YHC(10060, "下单成功","亲爱的${name},您于${date}已成功购买${grade}会员权益 ,支付金额${amount}，现在您可以尽情探索我们的AI助理工具。"),
    // 点数到期
    DS_DUE_TEMPLATE(1007, "点数到期提醒","您的${ddQuantity}点数将于${days}天后到期，请及时使用"),

    // 签到通知 平台自定义模板
    SIGN_NOTIF_TEMPLATE(2001, "美好的一天从这里开始","快来领取你的每日签到奖励"),
    // 好评通知-成功
    GOOD_SUCCESS_NOTIF_TEMPLATE(3001, "首次好评","你的首评奖励审核成功，奖励10点数"),
    // 好评通知-驳回
    GOOD_REFUSAL_NOTIF_TEMPLATE(3002, "首次好评","对不起您提供首评奖励截图不符合要求，请去应用商店评价后截图再上传"),

    ;

    final Integer intValue;
    final String strTitle;
    final String strContent;

    BNotificationTmplEnum(Integer intValue, String strTitle, String strContent) {
        this.intValue = intValue;
        this.strTitle = strTitle;
        this.strContent = strContent;
    }

}
