package com.business.enums;

import lombok.Getter;

/**
 * 短信模板code枚举
 */
@Getter
public enum SMSTmplCodeEnum {

    //验证码code
    MOBILE_VERIFY_TEMPLATECODE(1, "SMS_483455466"),
    //用户异常操作通知code
    USER_ERROR_TEMPLATECODE(2, "SMS_482740340"),
    //手机号更换通知code
    MOBILE_CHANGE_TEMPLATECODE(3, "SMS_482890300"),
    //会员到期提醒code
    MEMBER_DUE_TEMPLATECODE(4, "SMS_482730318"),
    //加油包充值成功通知code
    DATA_PLUS_TOPUP_TEMPLATECODE(5, "SMS_482945287"),
    //会员充值code
    MEMBER_TOPUP_TEMPLATECODE(6, "SMS_482900314"),

    //国际/港澳台-通知短信--验证码
    INTL_MOBILE_VERIFY_TEMPLATECODE(7, "SMS_464726357"),
    //国际/港澳台-通知短信--用户异常操作通知code
    INTL_USER_ERROR_TEMPLATECODE(8, "SMS_464850644"),
    //国际/港澳台-通知短信--手机号更换通知
    INTL_MOBILE_CHANGE_TEMPLATECODE(9, "SMS_464801262"),
    //国际/港澳台-通知短信--会员到期提醒
    INTL_MEMBER_DUE_TEMPLATECODE(10, "SMS_464741331"),
    //国际/港澳台-通知短信--加油包充值成功通知
    INTL_DATA_PLUS_TOPUP_TEMPLATECODE(11, "SMS_464746239"),
    //国际/港澳台-通知短信--会员充值
    INTL_MEMBER_TOPUP_TEMPLATECODE(12, "SMS_464766304");



    final Integer intValue;
    final String strCode;

    SMSTmplCodeEnum(Integer intValue, String strCode) {
        this.intValue = intValue;
        this.strCode = strCode;
    }

}
