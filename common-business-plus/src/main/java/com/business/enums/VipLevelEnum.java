package com.business.enums;


import lombok.Getter;

/**
 * 套餐等级
 * <AUTHOR>
 *
 */
@Getter
public enum VipLevelEnum {


    // 基础
    BASICS_LEVEL(1),

    // 标准
    STANDARD_LEVEL(2),

    // 专业
    PROFESSIONAL_LEVEL(3),

    // 高级
    ADVANCED_LEVEL(4),

    // 永久
    LONG_LEVEL(9);

    final int packageLevel;

    VipLevelEnum(int packageLevel) {
        this.packageLevel = packageLevel;
    }

}
