package com.business.enums;

import lombok.Getter;

@Getter
public enum BOriginalColouringEnum {

    ORIGINAL_COLOURING_INSET(6004, "插画", ""),
    ORIGINAL_COLOURING_FLAT_INSET(6009, "扁平插画","5f3e58d8-7af3-4d5b-92e3-a3d04b9a3414"),
    ORIGINAL_COLOURING_CRYSTAL(6010, "闪耀玻璃","a699f5da-f7f5-4afe-8473-c426b245c145")

    ;

    final int key;
    final String name;
    final String akUUid;

    BOriginalColouringEnum(int key, String name, String akUUid) {
        this.key = key;
        this.name = name;
        this.akUUid = akUUid;
    }

}
