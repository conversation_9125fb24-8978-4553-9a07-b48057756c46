package com.business.enums;

import com.business.audio.model.AudioStyleBO;
import com.business.audio.model.AudioStyleTagBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Schema(name="音频模型枚举", description="音频模型枚举")
public enum BAudioModelEnum {

    VOICE_NATIVE(1001,"原声","原声"),// 声音
    VOICE_ACCOMPANY(1002,"伴奏","伴奏"),// 声音

    ROLE_GIRL(2001,"女声","女声"),// 角色
    ROLE_BOY(2002,"男声","男声"),// 角色

    //内部使用
    TYPE_LG(3001,"灵感","灵感"),
    TYPE_GJ(3002,"高级","高级"),
    TYPE_ZDY(3003,"自定义","自定义"),

    //外部使用
    TYPE_LG2(1,"灵感","灵感"),
    TYPE_GJ2(2,"高级","高级"),
    TYPE_ZDY2(3,"自定义","自定义"),

    ;
    @Schema(description = "固定id")
    private final Integer id;

    @Schema(description = "显示名称")
    private final String name;

    @Schema(description = "使用值")
    private final String useValue;

    BAudioModelEnum(Integer id, String name, String useValue) {
        this.id = id;
        this.name = name;
        this.useValue = useValue;
    }

    public static List<AudioStyleTagBO> getAudioStyleVoice() {
        List<AudioStyleTagBO> voices = new ArrayList<>();
        for (BAudioModelEnum bAudioModelEnum : BAudioModelEnum.values()) {
            if (bAudioModelEnum.getId() > 1000 && bAudioModelEnum.getId() < 2000) {
                AudioStyleTagBO audioStyleTagBO = new AudioStyleTagBO();
                audioStyleTagBO.setTagId(Long.valueOf(bAudioModelEnum.getId()));
                audioStyleTagBO.setTagName(bAudioModelEnum.getName());
                voices.add(audioStyleTagBO);
            }
        }
        return voices;
    }

    public static List<AudioStyleBO> getAudioStyleRoles() {
        List<AudioStyleBO> roles= new ArrayList<>();
        for (BAudioModelEnum bAudioModelEnum : BAudioModelEnum.values()) {
            if (bAudioModelEnum.getId() > 2000 && bAudioModelEnum.getId() < 3000) {
                AudioStyleBO audioStyleBO = new AudioStyleBO();
                audioStyleBO.setId(Long.valueOf(bAudioModelEnum.getId()));
                audioStyleBO.setName(bAudioModelEnum.getName());
                roles.add(audioStyleBO);

            }
        }
        return roles;
    }

    public static BAudioModelEnum getById(Integer id) {
        for (BAudioModelEnum bAudioModelEnum : BAudioModelEnum.values()) {
            if (Objects.equals(bAudioModelEnum.getId(), id)) {
                return bAudioModelEnum;
            }
        }
        return null;
    }
    public static String getAudioStyleString(List<Long> ids) {
        if (Objects.isNull(ids)){
            return "";
        }
        StringBuilder roles = new StringBuilder();
        Map<Long, Long> uniqueNumbersMap = ids.stream()
                .collect(Collectors.toMap(
                        number -> number,  // key mapper
                        number -> number,  // value mapper
                        (existing, replacement) -> existing  // merge function
                ));
        for (BAudioModelEnum bAudioModelEnum : BAudioModelEnum.values()) {
            Long id = uniqueNumbersMap.get(Long.valueOf(bAudioModelEnum.getId()));
            if (Objects.equals(Long.valueOf(bAudioModelEnum.getId()), id)){
                roles.append(",").append(bAudioModelEnum.getName());
            }

        }
        return roles.toString();
    }
}
