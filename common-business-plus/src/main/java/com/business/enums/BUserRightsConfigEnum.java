package com.business.enums;

import lombok.Getter;

@Getter
public enum BUserRightsConfigEnum {

    //金刚区 功能列表枚举 1是绘图及每日精选
    RIGHTS_MODEL_CONSUME(101, "模型消耗50%"),
    RIGHTS_BASICS_INFINITE_DRAW(201, "基础2.0无限次"),
    RIGHTS_CARTOON_INFINITE_DRAW(301, "动漫2.0无限次"),
    RIGHTS_PICKUP_INFINITE_VIDEO(401, "皮卡无限次"),

    RIGHTS_DOMO_INFINITE_VIDEO(501, "哆莫无限次"),
    RIGHTS_MENGGONGCHANG_INFINITE_VIDEO(601, "梦工厂"),
    RIGHTS_MUSIC_GIVE_AWAY(701, "音乐"),
    RIGHTS_FUNCTION(801, "权益功能"),
    RIGHTS_MIND_ASSISTANT(901, "智能助理"),
    RIGHTS_TASK_CONCURRENCY(1001, "任务并发"),
    RIGHTS_HISTORY(1101, "历史记录"),
    RIGHTS_COPYRIGHT(1201, "版权"),
    RIGHTS_ADVERTISING(1301, "广告"),
    RIGHTS_WATERMARK(1401, "水印"),

    ;

    public final Integer intValue;
    public final String strName;

    BUserRightsConfigEnum(Integer intValue, String strName) {
        this.intValue = intValue;
        this.strName = strName;
    }


}
