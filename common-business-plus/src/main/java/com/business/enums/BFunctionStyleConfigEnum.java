package com.business.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum BFunctionStyleConfigEnum {

    FUNCTION_CONFIG_STYLE_DEFAULT(0, "指令"),// 自定义风格
    FUNCTION_CONFIG_STYLE_NATURE(1, "默认"),// 默认风格
    FUNCTION_CONFIG_STYLE_CUSTOM(2, "自然"),// 自然风格

    ;

    Integer intValue;
    String strName;

    BFunctionStyleConfigEnum(Integer intValue, String strName) {
        this.intValue = intValue;
        this.strName = strName;
    }

    public static BFunctionStyleConfigEnum getById(Integer id) {
        for (BFunctionStyleConfigEnum bFunctionStyleConfigEnum : BFunctionStyleConfigEnum.values()) {
            if (Objects.equals(bFunctionStyleConfigEnum.getIntValue(), id)) {
                return bFunctionStyleConfigEnum;
            }
        }
        return null;
    }

}
