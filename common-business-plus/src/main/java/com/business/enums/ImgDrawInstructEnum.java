package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name="指令同步汉化", description="点点官方指令")
public enum ImgDrawInstructEnum {

    MODEL_JICHU("--v 5.2","--基础","绘图模型"),
    MODEL_JICHU_2("--v 5","--基础","绘图模型"),
    MODEL_JICHU_1("--version 5.2","--基础","绘图模型"),
    MODEL_JICHU_V2_1("--v 6.0","--基础V2","绘图模型"),
    MODEL_JICHU_V2("--v 6","--基础V2","绘图模型"),
    MODEL_JICHU_V2_3("--version 6.0","--基础V2","绘图模型"),
    MODEL_JICHU_V2_2("--version 6","--基础V2","绘图模型"),
    MODEL_DONGMAN("--niji 5","--动漫","绘图模型"),
    MODEL_DONGMAN_V2("--niji 6","--动漫V2","绘图模型"),
    MODEL_LINGDONG("DALL·E 3","--灵动","绘图模型"),


    STYLE_ZHENSHI("--style raw","--风格 真实","风格"),
    STYLE_KEAI("--style cute","--风格 可爱","风格"),
    STYLE_CHANGJING("--style scenic","--风格 场景","风格"),
    STYLE_YUANBAN("--style original","--风格 原版","风格"),
    STYLE_FENGFU("--style expressive","--风格 丰富","风格"),


    FENGGEHUA_1("--stylize","--风格化","风格化"),
    FENGGEHUA_2("--s","--风格化","风格化"),

    BILI_1("--ar","--比例","比例"),
    BILI_2("--aspect","--比例","比例"),

    MANSU_ED("--relaxed","--慢速","速度"),
    MANSU("--relax","--慢速","速度"),
    BIAOZHUN("--fast","--标准","速度"),
    KUAISU("--turbo","--快速","速度"),

    ;
    @Schema(description = "mj指令")
    private final String mjValue;

    @Schema(description = "点点官方指令")
    private final String ddValue;

    @Schema(description = "详情")
    private final String details;

    ImgDrawInstructEnum(String mjValue, String ddValue, String details) {
        this.mjValue = mjValue;
        this.ddValue = ddValue;
        this.details = details;
    }

    public static String getDDValueByMJValue(String mjValue) {
        for (ImgDrawInstructEnum item : ImgDrawInstructEnum.values()) {
            if (item.getMjValue().equals(mjValue)) {
                return item.getDdValue();
            }
        }
        return "";
    }

}
