package com.business.enums;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name="mj官网绘图版本号", description="mj官网绘图版本号")
public enum MjParsedVersionEnum {

    MODEL_JICHU_2("5.2", "--基础"),
    MODEL_JICHU_V2_1("6.0", "--基础V2"),

    ;

    @Schema(description = "mj模型")
    private final String mjValue;

    @Schema(description = "mj模型名称")
    private final String mjName;

    MjParsedVersionEnum(String mjValue, String mjName) {
        this.mjValue = mjValue;
        this.mjName = mjName;
    }

    public MjParsedVersionEnum getMjParsedVersionEnum(String mjValue) {
        for (MjParsedVersionEnum enumValue : MjParsedVersionEnum.values()) {
            if (enumValue.mjValue.equals(mjValue)) {
                return enumValue;
            }
        }
        return null; // 或者返回默认枚举值，取决于您的需求
    }

}
