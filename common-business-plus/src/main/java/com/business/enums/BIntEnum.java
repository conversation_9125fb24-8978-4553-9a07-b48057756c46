package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name="公共int枚举信息", description="公共int枚举信息")
public enum BIntEnum {

    /* 绘图是否公开展示 */
    IMG_DRAW_IS_PUBLISH_TRUE(1),
    IMG_DRAW_IS_PUBLISH_FALSE(0),

    //是否
    IS_TRUE(1),//是
    IS_FALSE(0),//否

    ;
    @Schema(description = "值")
    private final Integer intValue;

    BIntEnum(Integer intValue) {
        this.intValue = intValue;
    }
}
