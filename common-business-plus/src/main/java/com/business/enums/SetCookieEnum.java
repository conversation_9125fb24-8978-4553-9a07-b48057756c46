package com.business.enums;


import lombok.Getter;

/**
 * 设置第三方Cookie的类型
 *
 * <AUTHOR>
 */
@Getter
public enum SetCookieEnum {


    // suno类型
    SUNO(1, "suno账号信息"),

    // luma类型
    LUMA(2, "luma账号信息"),

    // pika类型
    PIKA(3, "pika账号信息"),

    // runway类型
    RUNWAY(5, "runway视频转绘");

    final int type;
    final String title;

    SetCookieEnum(int type, String title) {

        this.type = type;
        this.title = title;
    }

}
