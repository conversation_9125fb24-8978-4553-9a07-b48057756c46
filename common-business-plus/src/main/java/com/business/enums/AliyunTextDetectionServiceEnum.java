package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name="aliyun文本检测服务枚举", description="服务枚举")
public enum AliyunTextDetectionServiceEnum {

    IMAGESERVICE("ai_art_detection_02"),

    AUDIOSERVICE("ai_art_detection_01"),
    VIDEOSERVICE("ai_art_detection"),
    SPEECHSERVICE("ai_art_detection_04"),
    TEXTSERVICE("ai_art_detection_03")

    ;
    @Schema(description = "name")
    private final String name;


    AliyunTextDetectionServiceEnum(String name) {
        this.name = name;
    }
}
