package com.business.enums;
import lombok.Getter;

@Getter
public enum DailySignTaskEnum {

    // TODO 是否可点击
    NO_CLICKABLE_BUTTON(0, "不能点击按钮"),
    CLICKABLE_BUTTON(1, "可以点击按钮"),

    // TODO 日期状态
    TODAY(0, "今天"),
    ORMERLY(-1, "过去"),
    FUTURE(1, "将来"),

    // TODO 审核状态
    WAITING_SUBMIT_STATE(0, "去评价", null),
    AUDIT_APPROVE_STATE(1, "已完成", "审核成功"),
    AUDIT_PROGRESS_STATE(2, "待审核", null),
    AUDIT_FAILED_STATE(3, "审核失败", "审核失败"),
    AUDIT_REFUSAL_STATE(4, "已驳回", "审核已驳回"),

    // TODO 每日签到
    DAILY_TASK_INVITE(2, "邀请好友免费领SVIP会员", "每月一次，以领取成功到账为准"),
    DAILY_TASK_GOOG(1, "每日签到", "签到奖励随机，以实际到账为准"),
    DAILY_TASK_COMMUNITY(3, "加入点点社区", "交流学习和不定期获取点数奖励"),

    // TODO 新手任务
    NOVICE_TASK_FIRST_LOGIN(1, "首次登录移动端", "奖励10点数"),
    NOVICE_TASK_FIRST_GOOG(2, "首次好评奖励 ", "奖励10点数"),
    NOVICE_TASK_FIRST_PERSONAL_DATA(3, "首次完善个人资料", "奖励10点数"),

    // TODO 邀请规则说明
    INVITE_RULE_DESCRIPTION(6001, "成功邀请6好友注册奖励月度SVIP"),
    INVITE_GIVE_SVIP_ALL_NUM(6002, "${giveSvipAllNum}个月度SVIP"),

    // TODO 精选类型
    DAILY_SELECTION(0,"每日精选", "top_day"),
    WEEKLY_SELECTION(1, "每周精选", "top_week"),
    MONTHLY_SELECTION(2, "月度精选", "top_month"),

    ;

    private final int code;
    private final String description;
    private final String note;

    DailySignTaskEnum(int code, String description) {
        this.code = code;
        this.description = description;
        this.note = null;
    }

    DailySignTaskEnum(int code, String description, String note) {
        this.code = code;
        this.description = description;
        this.note = note;
    }

    public static DailySignTaskEnum getDailySignTaskEnum(String feed) {
        if (feed.equals(DailySignTaskEnum.DAILY_SELECTION.getNote())) {
            return DailySignTaskEnum.DAILY_SELECTION;
        }
        if (feed.equals(DailySignTaskEnum.WEEKLY_SELECTION.getNote())) {
            return DailySignTaskEnum.WEEKLY_SELECTION;
        }
        if (feed.equals(DailySignTaskEnum.MONTHLY_SELECTION.getNote())) {
            return DailySignTaskEnum.MONTHLY_SELECTION;
        }
        return null;
    }

}
