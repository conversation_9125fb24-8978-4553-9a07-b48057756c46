package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name="账号速度枚举", description="账号速度枚举")
public enum BAccountSpeedEnum {

    ACCOUNT_FAST("fast", "快速账号"),

    ACCOUNT_RELAXED("relaxed", "慢速账号"),

    ;
    @Schema(description = "速度")
    private final String speed;

    @Schema(description = "描述")
    private final String remark;

    BAccountSpeedEnum(String speed, String remark) {
        this.speed = speed;
        this.remark = remark;
    }

}
