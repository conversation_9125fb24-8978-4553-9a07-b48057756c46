package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name = "LE绘图宽高比尺寸", description = "LE绘图宽高比尺寸枚举")
public enum BLEDrowSizeEnum {

    ONE_TO_ONE("1:1", 624, 624),
    TWO_TO_THREE("2:3", 512, 768),
    THREE_TO_TWO("3:2", 768, 512),
    THREE_TO_FOUR("3:4", 544, 720),
    FOUR_TO_THREE("4:3", 720, 544),
    NINE_TO_SIXTEEN("9:16", 512, 904),
    SIXTEEN_TO_NINE("16:9", 904, 512),


    FLUX_ONE_TO_ONE("1:1", 1024, 1024),
    FLUX_TWO_TO_THREE("1:2", 512, 1024),
    FLUX_THREE_TO_TWO("3:2", 768, 512),
    FLUX_THREE_TO_FOUR("3:4", 768, 1024),
    FLUX_NINE_TO_SIXTEEN("9:16", 576, 1024),
    FLUX_SIXTEEN_TO_NINE("16:9", 1024, 576),

    JIMENG_ONE_TO_ONE("1:1", 512, 512),
    JIMENG_TWO_TO_THREE("2:3", 341, 512),
    JIMENG_THREE_TO_TWO("3:2", 512, 341),
    JIMENG_THREE_TO_FOUR("3:4", 384, 512),
    JIMENG_FOUR_TO_THREE("4:3", 512, 384),
    JIMENG_NINE_TO_SIXTEEN("9:16", 288, 512),
    JIMENG_SIXTEEN_TO_NINE("16:9", 512, 288),

    ZHIPU_ONE_TO_ONE("1:1", 1024, 1024),
    ZHIPU_TWO_TO_THREE("2:3", 768, 1344),
    ZHIPU_THREE_TO_TWO("3:2", 1344, 768),
    ZHIPU_THREE_TO_FOUR("3:4", 864, 1152),
    ZHIPU_FOUR_TO_THREE("4:3", 1152, 864),
    ZHIPU_NINE_TO_SIXTEEN("9:16", 720, 1440),
    ZHIPU_SIXTEEN_TO_NINE("16:9", 1440, 720),

    RUNWAY_NINE_TO_SIXTEEN("9:16", 768, 1280),
    RUNWAY_SIXTEEN_TO_NINE("16:9", 1280, 768),


    ;
    @Schema(description = "使用数量展示")
    private final String ratio;

    @Schema(description = "输入宽")
    private final Integer inputWidth;

    @Schema(description = "输入高")
    private final Integer inputHeight;

    BLEDrowSizeEnum(String ratio, Integer inputWidth, Integer inputHeight) {
        this.ratio = ratio;
        this.inputWidth = inputWidth;
        this.inputHeight = inputHeight;
    }

    public static int[] getLeWidthAndHeight(String ratio) {
        if (ONE_TO_ONE.getRatio().equals(ratio)) {
            return new int[]{ONE_TO_ONE.getInputWidth(), ONE_TO_ONE.getInputHeight()};
        }
        if (TWO_TO_THREE.getRatio().equals(ratio)) {
            return new int[]{TWO_TO_THREE.getInputWidth(), TWO_TO_THREE.getInputHeight()};
        }
        if (THREE_TO_TWO.getRatio().equals(ratio)) {
            return new int[]{THREE_TO_TWO.getInputWidth(), THREE_TO_TWO.getInputHeight()};
        }
        if (THREE_TO_FOUR.getRatio().equals(ratio)) {
            return new int[]{THREE_TO_FOUR.getInputWidth(), THREE_TO_FOUR.getInputHeight()};
        }
        if (FOUR_TO_THREE.getRatio().equals(ratio)) {
            return new int[]{FOUR_TO_THREE.getInputWidth(), FOUR_TO_THREE.getInputHeight()};
        }
        if (NINE_TO_SIXTEEN.getRatio().equals(ratio)) {
            return new int[]{NINE_TO_SIXTEEN.getInputWidth(), NINE_TO_SIXTEEN.getInputHeight()};
        }
        if (SIXTEEN_TO_NINE.getRatio().equals(ratio)) {
            return new int[]{SIXTEEN_TO_NINE.getInputWidth(), SIXTEEN_TO_NINE.getInputHeight()};
        }
        return new int[]{1, 1};
    }

    public static int[] getFluxWidthAndHeight(String ratio) {
        if (FLUX_ONE_TO_ONE.getRatio().equals(ratio)) {
            return new int[]{FLUX_ONE_TO_ONE.getInputWidth(), FLUX_ONE_TO_ONE.getInputHeight()};
        }
        if (FLUX_TWO_TO_THREE.getRatio().equals(ratio)) {
            return new int[]{FLUX_TWO_TO_THREE.getInputWidth(), FLUX_TWO_TO_THREE.getInputHeight()};
        }
        if (FLUX_THREE_TO_TWO.getRatio().equals(ratio)) {
            return new int[]{FLUX_THREE_TO_TWO.getInputWidth(), FLUX_THREE_TO_TWO.getInputHeight()};
        }
        if (FLUX_THREE_TO_FOUR.getRatio().equals(ratio)) {
            return new int[]{FLUX_THREE_TO_FOUR.getInputWidth(), FLUX_THREE_TO_FOUR.getInputHeight()};
        }
        if (FLUX_NINE_TO_SIXTEEN.getRatio().equals(ratio)) {
            return new int[]{FLUX_NINE_TO_SIXTEEN.getInputWidth(), FLUX_NINE_TO_SIXTEEN.getInputHeight()};
        }
        if (FLUX_SIXTEEN_TO_NINE.getRatio().equals(ratio)) {
            return new int[]{FLUX_SIXTEEN_TO_NINE.getInputWidth(), FLUX_SIXTEEN_TO_NINE.getInputHeight()};
        }
        return new int[]{1, 1};
    }

    public static int[] getJiMengWidthAndHeight(String ratio) {
        if (JIMENG_ONE_TO_ONE.getRatio().equals(ratio)) {
            return new int[]{JIMENG_ONE_TO_ONE.getInputWidth(), JIMENG_ONE_TO_ONE.getInputHeight()};
        }
        if (JIMENG_TWO_TO_THREE.getRatio().equals(ratio)) {
            return new int[]{JIMENG_TWO_TO_THREE.getInputWidth(), JIMENG_TWO_TO_THREE.getInputHeight()};
        }
        if (JIMENG_THREE_TO_TWO.getRatio().equals(ratio)) {
            return new int[]{JIMENG_THREE_TO_TWO.getInputWidth(), JIMENG_THREE_TO_TWO.getInputHeight()};
        }
        if (JIMENG_THREE_TO_FOUR.getRatio().equals(ratio)) {
            return new int[]{JIMENG_THREE_TO_FOUR.getInputWidth(), JIMENG_THREE_TO_FOUR.getInputHeight()};
        }
        if (JIMENG_FOUR_TO_THREE.getRatio().equals(ratio)) {
            return new int[]{JIMENG_FOUR_TO_THREE.getInputWidth(), JIMENG_FOUR_TO_THREE.getInputHeight()};
        }
        if (JIMENG_NINE_TO_SIXTEEN.getRatio().equals(ratio)) {
            return new int[]{JIMENG_NINE_TO_SIXTEEN.getInputWidth(), JIMENG_NINE_TO_SIXTEEN.getInputHeight()};
        }
        if (JIMENG_SIXTEEN_TO_NINE.getRatio().equals(ratio)) {
            return new int[]{JIMENG_SIXTEEN_TO_NINE.getInputWidth(), JIMENG_SIXTEEN_TO_NINE.getInputHeight()};
        }
        return new int[]{1, 1};
    }

    public static int[] getZhiPuWidthAndHeight(String ratio) {
        if (ZHIPU_ONE_TO_ONE.getRatio().equals(ratio)) {
            return new int[]{ZHIPU_ONE_TO_ONE.getInputWidth(), ZHIPU_ONE_TO_ONE.getInputHeight()};
        }
        if (ZHIPU_TWO_TO_THREE.getRatio().equals(ratio)) {
            return new int[]{ZHIPU_TWO_TO_THREE.getInputWidth(), ZHIPU_TWO_TO_THREE.getInputHeight()};
        }
        if (ZHIPU_THREE_TO_TWO.getRatio().equals(ratio)) {
            return new int[]{ZHIPU_THREE_TO_TWO.getInputWidth(), ZHIPU_THREE_TO_TWO.getInputHeight()};
        }
        if (ZHIPU_THREE_TO_FOUR.getRatio().equals(ratio)) {
            return new int[]{ZHIPU_THREE_TO_FOUR.getInputWidth(), ZHIPU_THREE_TO_FOUR.getInputHeight()};
        }
        if (ZHIPU_FOUR_TO_THREE.getRatio().equals(ratio)) {
            return new int[]{ZHIPU_FOUR_TO_THREE.getInputWidth(), ZHIPU_FOUR_TO_THREE.getInputHeight()};
        }
        if (ZHIPU_NINE_TO_SIXTEEN.getRatio().equals(ratio)) {
            return new int[]{ZHIPU_NINE_TO_SIXTEEN.getInputWidth(), ZHIPU_NINE_TO_SIXTEEN.getInputHeight()};
        }
        if (ZHIPU_SIXTEEN_TO_NINE.getRatio().equals(ratio)) {
            return new int[]{ZHIPU_SIXTEEN_TO_NINE.getInputWidth(), ZHIPU_SIXTEEN_TO_NINE.getInputHeight()};
        }
        return new int[]{1, 1};
    }

    public static String getRunWayWidthAndHeight(String ratio) {
        if (RUNWAY_NINE_TO_SIXTEEN.getRatio().equals(ratio)) {
            return RUNWAY_NINE_TO_SIXTEEN.getInputWidth() + ":" + RUNWAY_NINE_TO_SIXTEEN.getInputHeight();
        }
        if (RUNWAY_SIXTEEN_TO_NINE.getRatio().equals(ratio)) {
            return RUNWAY_SIXTEEN_TO_NINE.getInputWidth() + ":" + RUNWAY_SIXTEEN_TO_NINE.getInputHeight();
        }
        return RUNWAY_NINE_TO_SIXTEEN.getInputWidth() + ":" + RUNWAY_NINE_TO_SIXTEEN.getInputHeight();
    }
}
