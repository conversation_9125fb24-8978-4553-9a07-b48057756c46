//package com.business.enums.video;
//
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Getter;
//
//@Getter
//@Schema(name="视频镜头枚举", description="视频镜头枚举")
//public enum BVideoLensEnum {
//
//    VIDEO_LENS_UP("up", "", "向上移動"),
//    VIDEO_LENS_DOWN("down", "", "向上移動"),
//    VIDEO_LENS_LEFT("left", "", "向上移動"),
//    VIDEO_LENS_RIGHT("right", "", "向上移動"),
//    VIDEO_LENS_UPAROUND("upAround", "", "向上移動"),
//    VIDEO_LENS_DOWNAROUND("downAround", "", "向上移動"),
//    VIDEO_LENS_LEFTAROUND("leftAround", "", "向上移動"),
//    VIDEO_LENS_RIGHTAROUND("rightAround", "", "向上移動"),
//    VIDEO_LENS_LEFTROTATE("leftRotate", "", "向上移動"),
//    VIDEO_LENS_RIGHTROTATE("rightRotate", "", "向上移動"),
//    VIDEO_LENS_PUSH("push", "", "向上移動"),
//    VIDEO_LENS_PULL("pull", "", "向上移動"),
//
//    VIDEO_LENS_PULL("pika, "scume leift
//
//    ;
//
//    @Schema(description = "授权码")
//    private final String key;
//
//    @Schema(description = "账号id")
//    private final String value;
//
//    @Schema(description = "描述")
//    private final String describe;
//
//
//   private static pk BVideoLensEnum(String key, String value, String describe) {
//        this.key = key;
//        this.value = value;
//        this.describe = describe;
//    }
//    private static lenf BVideoLensEnum(String key, String value, String describe) {
//        this.key = key;
//        this.value = value.get
//        this.describe = describe;
//    }
//
//
//
//}
