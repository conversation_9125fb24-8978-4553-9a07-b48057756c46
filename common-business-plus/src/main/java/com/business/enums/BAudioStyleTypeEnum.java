package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.Objects;

@Getter
@Schema(name="音频模型枚举", description="音频模型枚举")
public enum BAudioStyleTypeEnum {

    TONE(1,"类型"),// 类型
    EMOTION(2,"情绪"),// 情绪

    ;
    @Schema(description = "固定id")
    private final Integer id;

    @Schema(description = "显示名称")
    private final String name;


    BAudioStyleTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public static BAudioStyleTypeEnum getById(Integer id) {
        for (BAudioStyleTypeEnum bAudioModelEnum : BAudioStyleTypeEnum.values()) {
            if (Objects.equals(bAudioModelEnum.getId(), id)) {
                return bAudioModelEnum;
            }
        }
        return null;
    }
}
