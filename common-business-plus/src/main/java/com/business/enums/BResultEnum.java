package com.business.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name="公共错误返回内容枚举信息", description="公共错误返回内容枚举信息")
public enum BResultEnum {

    ERROR_FAILED(202,"失败"),// 错误返回：失败
    ERROR_OPT(202,"操作错误"),// 错误返回：操作错误
    ERROR_VIP(202,"仅VIP用户可操作"),// 错误返回：操作错误


    ERROR_AUDIO_CONTENT_NON_COMPLIANCE_TITLE(202,"音频标题不合规"),//
    ERROR_AUDIO_CONTENT_NON_COMPLIANCE(202,"音频歌词不合规"),//
    ERROR_TASK_CONCURRENT(202,"同时操作数量已达上限"),//

    ERROR_BALANCE_DZ(202,"点子余额不足"),// 点子不足异常

    ERROR_API_AUDIO_SUNO(202,"服务维护中,请稍后再试"),// 音频调用接口失败

    ERROR_SMS_PHONE(202,"请输入正确的手机号码！"), //短信手机号错误
    ERROR_SMS_FREQUENT(202,"请求过于频繁，请稍后再试！"),// 短信调用接口失败
    ERROR_SMS_AUTH_CODE(202,"验证码发送失败！"),// 短信验证码发送失败
    ERROR_SMS_AUTH_CODE_INPUT(202,"验证码输入错误！"),// 短信验证码发送失败


    SUCCESS_OPT(200,"操作成功"),// 成功返回：操作成功
    ;
    @Schema(description = "错误码")
    private final Integer code;

    @Schema(description = "错误内容")
    private final String msg;

    BResultEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
