package com.business.kling;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.business.kling.model.KlingImgVideoReqBO;
import com.business.kling.model.KlingTextVideoReqBO;
import com.business.kling.model.KlingTextVideoResBO;
import com.nacos.redis.RedisUtil;
import com.nacos.tool.BrotliInterceptor;
import com.nacos.utils.BFeiShuUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Slf4j
@Schema(title = "可灵视频Api")
public class KlingApis {

    private static final String KLING_DOMAIN = "https://api-beijing.klingai.com";

    @Getter
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    // 文生视频
    public static KlingTextVideoResBO postKLingTextToVideoGenerations(KlingTextVideoReqBO klingTextVideoReqBO, String accessKey, String secretKey, Long recordId) {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(klingTextVideoReqBO, JSONWriter.Feature.WriteMapNullValue));
        log.error("可灵AI请求参数：{}", JSONObject.toJSONString(klingTextVideoReqBO));
        Request request = new Request.Builder()
                .url(KLING_DOMAIN + "/v1/videos/text2video")
                .post(RequestBody.create(jsonObject.toString(), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("Authorization", "Bearer " + sign(accessKey, secretKey))
                .build();
        for (int i = 0; i < 3; i++) {
            try (var response = client.newCall(request).execute()) {
                log.error("可灵AI请求状态码Code：" + response.code());
                assert response.body() != null;
                String responseBody = response.body().string();
                System.out.println("====可灵AI=" + responseBody);

                KlingTextVideoResBO klingResponseBO = JSON.parseObject(responseBody, new TypeReference<KlingTextVideoResBO>() {
                });
                if (klingResponseBO == null) {
                    return null;
                }
                if (response.code() == 429 && (klingResponseBO.getCode() == 1303 || klingResponseBO.getCode() == 1302)) {
                    JSONObject json = new JSONObject();
                    json.put("id", recordId);
                    json.put("type", 0);
                    json.put("klingVideoReqBO", klingTextVideoReqBO);
                    RedisUtil.rPushList(RedisUtil.REDIS_KLING_SUBMIT_TASK_LIST, com.alibaba.fastjson2.JSON.toJSONString(json));
                    klingResponseBO.setCode(1303);
                    return klingResponseBO;
                }

                if (response.code() == 200 && response.isSuccessful()) {
                    return klingResponseBO;
                }
                if (isResourceExhausted(klingResponseBO.getCode())) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "蒙太奇[KLing]，资源包已耗尽", "需要充钱=" + responseBody);
                    return null;
                }
            } catch (Exception e) {
                log.error("可灵AI请求失败：{}", e.getMessage());
                return null;
            }
        }
        return null;
    }

    //图生视频
    public static KlingTextVideoResBO postKLingImageToVideoGenerations(KlingImgVideoReqBO klingImgVideoReqBO, String accessKey, String secretKey, Long recordId) {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(klingImgVideoReqBO, JSONWriter.Feature.WriteMapNullValue));
        log.error("可灵AI请求参数：{}", JSONObject.toJSONString(klingImgVideoReqBO));
        Request request = new Request.Builder()
                .url(KLING_DOMAIN + "/v1/videos/image2video")
                .post(RequestBody.create(jsonObject.toString(), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("Authorization", "Bearer " + sign(accessKey, secretKey))
                .build();
        try (var response = client.newCall(request).execute()) {
            log.info("可灵AI请求状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("====Kling可灵AI=" + responseBody);

            KlingTextVideoResBO klingResponseBO = JSON.parseObject(responseBody, new TypeReference<KlingTextVideoResBO>() {
            });
            if (klingResponseBO == null) {
                return null;
            }
            if (response.code() == 429 && (klingResponseBO.getCode() == 1303 || klingResponseBO.getCode() == 1302)) {
                JSONObject json = new JSONObject();
                json.put("id", recordId);
                json.put("type", 1);
                json.put("klingVideoReqBO", klingImgVideoReqBO);
                RedisUtil.rPushList(RedisUtil.REDIS_KLING_SUBMIT_TASK_LIST, com.alibaba.fastjson2.JSON.toJSONString(json));
                klingResponseBO.setCode(1303);
                return klingResponseBO;
            }

            if (response.code() == 200 && response.isSuccessful()) {
                return klingResponseBO;
            }
            if (klingResponseBO != null && isResourceExhausted(klingResponseBO.getCode())) {
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "蒙太奇[KLing]，资源包已耗尽", "需要充钱=" + responseBody);
                return null;
            }
        } catch (Exception e) {
            log.error("可灵AI请求失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    private static boolean isResourceExhausted(int code) {
        return code == 1102 || code == 1100 || code == 1101 || code == 1103;
    }

    //图生视频拉取
    public static KlingTextVideoResBO getKLingImage2VideoTaskResult(String taskId, String accessKey, String secretKey) {
        Request request = new Request.Builder()
                .url(KLING_DOMAIN + "/v1/videos/image2video/" + taskId)
                .get()
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("Authorization", "Bearer " + sign(accessKey, secretKey))
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("可灵AI获取任务结果状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            System.out.println("====可灵AI获取任务结果=" + responseBody);
            if (response.isSuccessful()) {
                return JSONObject.parseObject(responseBody, KlingTextVideoResBO.class);
            }
        } catch (Exception e) {
            log.error("可灵AI拉取任务失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    //文生视频拉取
    public static KlingTextVideoResBO getKLingVideoTaskResult(String taskId, String accessKey, String secretKey) {
        Request request = new Request.Builder()
                .url(KLING_DOMAIN + "/v1/videos/text2video/" + taskId)
                .get()
                .addHeader("Content-Type", "application/json; charset=utf-8")
                .addHeader("Authorization", "Bearer " + sign(accessKey, secretKey))
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.error("可灵AI获取任务结果状态码Code：" + response.code());
            assert response.body() != null;
            String responseBody = response.body().string();
            System.out.println("====可灵AI获取任务结果=" + responseBody);
            if (response.isSuccessful()) {
                return JSONObject.parseObject(responseBody, KlingTextVideoResBO.class);
            }
        } catch (Exception e) {
            log.error("可灵AI拉取任务失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    static String sign(String ak, String sk) {
        try {
            Date expiredAt = new Date(System.currentTimeMillis() + 1800 * 1000); // 有效时间，此处示例代表当前时间+1800s(30min)
            Date notBefore = new Date(System.currentTimeMillis() - 5 * 1000); //开始生效的时间，此处示例代表当前时间-5秒
            Algorithm algo = Algorithm.HMAC256(sk);
            Map<String, Object> header = new HashMap<String, Object>();
            header.put("alg", "HS256");
            return JWT.create()
                    .withIssuer(ak)
                    .withHeader(header)
                    .withExpiresAt(expiredAt)
                    .withNotBefore(notBefore)
                    .sign(algo);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


}
