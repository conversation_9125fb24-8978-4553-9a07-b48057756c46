package com.business.kling.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

//可灵AI-生成图片响应实体接口参数
@Data
public class KlingTextImageResBO {

    @Schema(title = "错误码")
    @JSONField(name = "code")
    private Integer code;

    @Schema(title = "错误信息")
    @JSONField(name = "message")
    private String message;

    @Schema(title = "任务编号")
    @JSONField(name = "request_id")
    private String requestId;

    @Schema(title = "视频生成结果")
    @JSONField(name = "data")
    private DataObj data;

    @Data
    public static class DataObj {

        @Schema(title = "任务ID，系统生成")
        @JSONField(name = "task_id")
        private String taskId;

        @Schema(title = "任务状态，枚举值：submitted（已提交）、processing（处理中）、succeed（成功）、failed（失败）")
        @JSONField(name = "task_status")
        private String taskStatus;

        @Schema(title = "任务创建时间")
        @JSONField(name = "created_at")
        private Long createdAt;

        @Schema(title = "任务更新时间")
        @JSONField(name = "updated_at")
        private Long updatedAt;

        @Schema(title = "任务状态信息")
        @JSONField(name = "task_status_msg")
        private String taskStatusMsg;

        @Schema(title = "任务更新时间")
        @JSONField(name = "task_result")
        private TaskResultObj taskResult;
    }


    @Data
    public static class TaskResultObj {

        @Schema(title = "任务ID，系统生成")
        @JSONField(name = "images")
        private List<ImagesObj> images;
    }

    @Data
    public static class ImagesObj {

        @JSONField(name = "index")
        private String id;

        @Schema(title = "url")
        @JSONField(name = "url")
        private String url;

    }

}
