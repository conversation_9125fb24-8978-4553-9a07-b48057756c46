package com.business.kling.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

//可灵AI-api视频请求接口参数
@Data
public class KlingImgVideoReqBO {

    @JSONField(name = "model")
    private String model = "kling-v1"; //模型编码

    @JSONField(name = "image")
    private String image; //● 图片格式支持.jpg / .jpeg / .png ● 图片文件大小不能超过10MB，图片分辨率不小于300*300px，图片宽高比要在1:2.5 ~ 2.5:1之间

    @JSONField(name = "image_tail")
    private String imageTail; //● 图片格式支持.jpg / .jpeg / .png ● 图片文件大小不能超过10MB，图片分辨率不小于300*300px

    @JSONField(name = "prompt")
    private String prompt; //正向文本提示词

    @JSONField(name = "negative_prompt")
    private String negativePrompt; //负向文本提示词

    @JSONField(name = "cfg_scale")
    private Float cfgScale = 0.5F; //生成视频的自由度；值越大，模型自由度越小，与用户输入的提示词相关性越强  取值范围：[0, 1]

    @JSONField(name = "mode")
    private String mode = "std"; //生成视频的模式 ● 枚举值：std，pro ● 其中std：标准模式（标准），基础模式，性价比高  其中pro：专家模式（高品质），高表现模式，生成视频质量更佳

    @JSONField(name = "duration")
    private String duration = "5"; //生成视频时长，单位s 枚举值：5，10


}
