package com.business.kling.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

//可灵AI-api文本到图片生成请求接口参数
@Data
public class KlingTextImageReqBO {

    @Schema(description = "模型名称:枚举值：kling-v1")
    @JSONField(name = "model")
    private String model;

    @Schema(description = "正向文本提示词:不能超过500个字符")
    @JSONField(name = "prompt")
    private String prompt;

    @Schema(description = "负向文本提示词:不能超过200个字符 注:图生图（即image字段不为空时）场景下，不支持负向提示词")
    @JSONField(name = "negative_prompt")
    private String negativePrompt;

    @Schema(description = "支持传入图片Base64编码或图片URL（确保可访问） 图片格式支持.jpg / .jpeg / .png 图片文件大小不能超过10MB，图片分辨率不小于300*300px")
    @JSONField(name = "image")
    private String image;

    @Schema(description = "生成过程中对用户上传图片的参考强度●取值范围：[0,1]，数值越大参考强度越大")
    @JSONField(name = "image_fidelity")
    private Float imageFidelity;

    @Schema(description = "生成图片数量 取值范围：[1,9]")
    @JSONField(name = "n")
    private Integer imgQua;

    @Schema(description = "生成图片的画面纵横比（宽:高） 枚举值：16:9, 9:16, 1:1, 4:3, 3:4, 3:2, 2:3")
    @JSONField(name = "aspect_ratio")
    private String aspectRatio;

    @Schema(description = "本次任务结果回调通知地址，如果配置，服务端会在任务状态发生变更时主动通知")
    @JSONField(name = "callback_url")
    private String callbackUrl;

}
