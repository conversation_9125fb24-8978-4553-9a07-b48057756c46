package com.business.kling;

import com.alibaba.fastjson2.JSON;
import com.business.aigc.tusiart.TusiJobStateEnum;
import com.business.db.model.po.ImgDrawDetlPO;
import com.business.kling.model.KlingImgVideoReqBO;
import com.business.kling.model.KlingTextVideoReqBO;
import com.business.kling.model.KlingTextVideoResBO;
import com.business.model.po.ImgDrawRecordPO;
import com.business.utils.BOssUtil;
import com.nacos.config.OssClientConfig;
import com.nacos.utils.BFeiShuUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;


@Slf4j
@Schema(title = "可灵视频 api工具类")
public class KlingApiUtil {
    public static final String STATUS_FAILED = "failed"; // 失败
    public static final String STATUS_SUCCEED = "succeed"; // 成功
    public static final String STATUS_PROCESSING = "processing"; // 处理中
    public static final String STATUS_SUBMITTED = "submitted"; // 已提交

    public static final String STATUS_QUEUING = "queuing"; // 已提交


    /**
     * 可灵视频生成
     * @param klingTextVideoReqBO 文本视频请求参数
     * @param KlingImgRequestBO 图片视频请求参数
     * @param accessKey 访问密钥
     * @param secretKey 密钥
     * @param recordId 记录ID
     * @return 任务ID
     */
    public static String postKlingTextToVideoGenerations(KlingTextVideoReqBO klingTextVideoReqBO, KlingImgVideoReqBO KlingImgRequestBO, String accessKey, String secretKey, Long recordId) {
        KlingTextVideoResBO klingTextVideoResBO = null;
        if (klingTextVideoReqBO != null && KlingImgRequestBO == null) {
            klingTextVideoResBO = KlingApis.postKLingTextToVideoGenerations(klingTextVideoReqBO, accessKey, secretKey, recordId);
        } else {
            klingTextVideoResBO = KlingApis.postKLingImageToVideoGenerations(KlingImgRequestBO, accessKey, secretKey, recordId);
        }
        if (klingTextVideoResBO == null) {
            return null;
        }
        if (klingTextVideoResBO.getCode() == 0) {
            return klingTextVideoResBO.getData().getTaskId();
        }
        if (klingTextVideoResBO.getCode() == 1303) {
            return STATUS_QUEUING;
        }
        return null;
    }

    /**
     * 获取可灵任务结果
     * @param taskId 任务ID
     * @param ak 访问密钥
     * @param sk 密钥
     * @param imgDrawRecordPO 图片视频记录
     * @param imgDrawDetlPO 图片视频详情
     * @return 任务状态
     */
    public static String getKlingVideoTaskResult(String taskId, String ak, String sk, ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO) {
        KlingTextVideoResBO klingTextVideoResBO = null;
        String initImgUrls = imgDrawRecordPO.getInitImgUrls();

        if (StringUtils.isNotBlank(initImgUrls) && initImgUrls.length() > 10) {
            klingTextVideoResBO = KlingApis.getKLingImage2VideoTaskResult(taskId, ak, sk);
        } else {
            klingTextVideoResBO = KlingApis.getKLingVideoTaskResult(taskId, ak, sk);
        }

        if (klingTextVideoResBO == null || klingTextVideoResBO.getData() == null) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "可灵视频拉取任务业务失败", "getZhiPuVideoTaskResult.resp " + JSON.toJSONString(klingTextVideoResBO) + " jobId=" + taskId + " prompt= " + imgDrawRecordPO.getPromptInit());
            return TusiJobStateEnum.FAILED.getState();
        }
        if (Objects.equals(klingTextVideoResBO.getData().getTaskStatus(), STATUS_PROCESSING)
                || Objects.equals(klingTextVideoResBO.getData().getTaskStatus(), STATUS_SUBMITTED)) {
            return TusiJobStateEnum.RUNNING.getState();
        }

        if (Objects.equals(klingTextVideoResBO.getData().getTaskStatus(), STATUS_FAILED)) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "可灵视频拉取任务业务失败", "getZhiPuVideoTaskResult.resp " + JSON.toJSONString(klingTextVideoResBO) + " jobId=" + taskId + " prompt= " + imgDrawRecordPO.getPromptInit());
            return TusiJobStateEnum.FAILED.getState();
        }

        if (Objects.equals(klingTextVideoResBO.getData().getTaskStatus(), STATUS_SUCCEED)) {
            String url = klingTextVideoResBO.getData().getTaskResult().getVideos().get(0).getUrl();
            imgDrawDetlPO.setAudioUrl(url);
            log.info("可灵视频原地址= " + url);
            return uploadLumaVideo(url, taskId, 14, OssClientConfig.FILE_SUFFIX_VIDEO);
        }
        return TusiJobStateEnum.FAILED.getState();
    }


    // TODO 上传视频到oss
    public static String uploadLumaVideo(String fileUrl, String videoJobId, Integer folder, String suffix) {
        if (fileUrl == null) return null;
        int maxRetries = 6; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadURL(fileUrl, videoJobId, folder, suffix);
                if (videoPath != null) {
                    return videoPath;
                }
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                }
                log.error("智谱视频上传失败，重试次数：" + (retryCount + 1) + " videoJobId=" + videoJobId);

            } catch (Exception e) {
                log.error("智谱视频上传视频到OSS发生异常，重试次数：" + (retryCount + 1));
            }
            retryCount++;
        }
        log.error("智谱视频文件上传失败，超过最大重试次数， 视频id=" + videoJobId);
        return null;
    }

}
