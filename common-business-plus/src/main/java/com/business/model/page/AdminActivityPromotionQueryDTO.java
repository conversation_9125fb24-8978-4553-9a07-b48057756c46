package com.business.model.page;

import com.nacos.base.BasePageHelper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(title = "条件查询活动弹窗列表")
public class AdminActivityPromotionQueryDTO extends BasePageHelper implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Deprecated
    @Schema(title = "排序条件")
    private String orderBy;

}
