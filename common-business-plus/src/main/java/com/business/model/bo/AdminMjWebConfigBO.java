package com.business.model.bo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(name = "mjweb账号业务实体", description = "mjweb账号业务实体")
public class AdminMjWebConfigBO {

    @Schema(name = "主键id", type = "Long")
    private Long id;

    @Schema(name = "账号名称", type = "String")
    private String accountName;

    @Schema(name = "账号速度", type = "String")
    private String accountSpeed;

    @Schema(name = "账号唯一id", type = "String")
    private String channelId;

    @Schema(name = "账号秘钥", type = "String")
    private String cookie;

    @Schema(name = "账号优先级等级。 1. 2. 3.", type = "Integer")
    private Integer priorityLevel;

    public AdminMjWebConfigBO() {}

    public AdminMjWebConfigBO(Long id, String accountSpeed, String channelId, String cookie, Integer priorityLevel) {
        this.id = id;
        this.accountSpeed = accountSpeed;
        this.channelId = channelId;
        this.cookie = cookie;
        this.priorityLevel = priorityLevel;
    }
}
