package com.business.model.bo.pika;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Schema(name = "视频业务参数实体", description = "视频业务参数实体")
@Data
public class ImgVideoBO {

    // 皮卡特有的参数
    private String video; //视频地址
    private String style; //风格
    private String image; //图片地址
    private String prompt; //生成视频指令内容
    private String negativeprompt; //负向提示词
    private String pan; //左右
    private String tilt; //上下
    private String rotate; //左右旋转
    private String zoom; //放大缩小
    private String aspectRatio; //比列

    private Integer frameRate;
    private Integer motion;
    private Integer guidanceScale;

    @Override
    public String toString() {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return super.toString();
        }
    }

}
