package com.business.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(name = "绘图指令汉化实体", description = "绘图指令汉化实体")
@Data
public class ImgDrawInstructBO {

    public ImgDrawInstructBO(List<String> instructs, String prompt) {
        this.instructs = instructs;
        this.prompt = prompt;
    }

    @Schema(title = "指令列表：汉化版", type = "List<String>")
    private List<String> instructs;

    @Schema(title = "指令列表：汉化版", type = "List<String>")
    private String prompt;

}
