package com.business.model.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(name = "mj刷新参数实体", description = "mj刷新参数实体")
public class MJRedisRefreshBO {

    @NotBlank(message = "appVersion 不能为空")
    @Schema(name = "mj appVersion 信息", type = "String")
    private String appVersion;

    @NotBlank(message = "appVersion 不能为空")
    @Schema(name = "mj userAgent 信息", type = "String")
    private String userAgent;

    @Schema(name = "mj token 信息", type = "String")
    private String token;

}
