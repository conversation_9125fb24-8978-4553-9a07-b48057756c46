package com.business.model.vo;

import com.alibaba.fastjson2.JSONArray;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Schema(name = "视频循环实体", description = "视频循环实体")
@Data
public class VideoLoopVO {

    @Schema(name = "速度key", type = "Integer")
    private Integer key;

    @Schema(name = "显示名称", type = "String")
    private String name;

    @Schema(name = "数值", type = "boolean")
    private boolean value;

    @Schema(name = "显示图标", type = "String")
    private String imgUrl;


    public static List<VideoLoopVO> videoLoopVOList(String weight) {
        if (weight == null) {
            return null;
        }
        try {
            return JSONArray.parseArray(weight, VideoLoopVO.class);
        } catch (Exception e) {
            log.error("videoLoopVOList {}", weight,e);
            return null;
        }
    }

    public String getImgUrl() {
        if (imgUrl != null && !imgUrl.contains("https") && !imgUrl.contains(";base64")) {
            //装载前缀
            return CommonStrEnum.IMAGE_PREFIX.getValue() + imgUrl;
        }
        return imgUrl;
    }

}
