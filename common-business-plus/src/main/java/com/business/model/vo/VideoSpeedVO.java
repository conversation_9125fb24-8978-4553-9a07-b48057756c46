package com.business.model.vo;

import com.alibaba.fastjson2.JSONArray;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Schema(name = "视频速度展示实体", description = "视频速度展示实体")
@Data
public class VideoSpeedVO {

    @Schema(name = "速度key", type = "Integer")
    private Integer key;

    @Schema(name = "显示名称", type = "String")
    private String name;

    @Schema(name = "数值", type = "String")
    private String value;

    @Schema(name = "显示图标", type = "String")
    private String imgUrl;

    @Schema(name = "用户消耗点子数量", type = "String")
    private String useDDQua;

    @Schema(name = "会员是否可用", type = "Integer")
    private Integer isMemberUse;

    @Schema(name = "会员名称", type = "String")
    private String isMemberUseLabel;

    public static List<VideoSpeedVO> videoSpeedVOList(String weight) {
        if (weight == null) {
            return null;
        }
        try {
            return JSONArray.parseArray(weight, VideoSpeedVO.class);
        } catch (Exception e) {
            log.error("获取videoWeightVOList失败 {}", weight,e);
            return null;
        }
    }

    public String getImgUrl() {
        if (imgUrl != null && !imgUrl.contains("https") && !imgUrl.contains(";base64")) {
            //装载前缀
            return CommonStrEnum.IMAGE_PREFIX.getValue() + imgUrl;
        }
        return imgUrl;
    }

}
