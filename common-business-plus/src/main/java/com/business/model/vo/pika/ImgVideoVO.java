package com.business.model.vo.pika;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name = "视频业务参数实体", description = "视频业务参数实体")
@Data
public class ImgVideoVO {

    private Integer modelId; //模型id
    private String video; //视频地址
    private String image; //图片地址
    private String prompt; //生成视频指令内容
    private String negativeprompt; //负向提示词
    private Integer width;
    private Integer height;

    private Double[] weight; //权重值

    private Integer lensValue; //镜头Key

    private Integer styleKey; //风格key
    private Integer scalesKey; //比列key
    private String videoLens; //镜头数组

    // 哆莫
    private String duration; //时长
    private Integer modelKey; //休闲模式
    private Double videoDuration; //原上传视频时长
    private Integer referModeKey; //视频参考值
    private String videoUrl; //视频url地址

    // 梦工厂
    private String firstHue; //首图主色
    private String lastHue; //尾图主色
    private Boolean loop; //随机or循环
    private ImgDetailDTO imgDetailFirst;
    private ImgDetailDTO imgDetailLast;

    //达芬奇  runway
    private Integer version; //版本
    @Schema(name = "图片是否作为尾帧", type = "Boolean")
    private Boolean imageAsEndFrame = false;

    //镜头
    private String videoLensJson; //镜头数组-new
    private String styleJson; //风格-new

    //wanx 版本参数
    private Integer videoVersionKey;
}
