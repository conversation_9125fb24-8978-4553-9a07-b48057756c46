package com.business.model.vo.pika;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

@Schema(name = "SD视频参数实体", description = "SD视频参数实体")
@Data
@Validated
public class ImgDetailDTO {
        @Schema(description = "宽度", type = "Integer")
        private Integer width;
        @Schema(description = "高度", type = "Integer")
        private Integer height;
}

