package com.business.model.vo;

import com.alibaba.fastjson2.JSONArray;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Schema(name = "视频权重展示实体", description = "视频权重展示实体")
@Data
public class VideoWeightVO {

    @Schema(name = "权重key", type = "Integer")
    private Integer key;

    @Schema(name = "权重名", type = "String")
    private String name;

    @Schema(name = "权重，最大数值", type = "Double")
    private Double maxValue;

    @Schema(name = "权重，最小数值", type = "Double")
    private Double minValue;

    @Schema(name = "权重，叠加数值", type = "Double")
    private Double overlyingValue;

    @Schema(name = "权重，默认数值", type = "Double")
    private Double defaultValue;

    @Schema(name = "权重，显示列表", type = "Double")
    private List<Double> valueList;

    public static List<VideoWeightVO> videoWeightVOList(String weight) {
        if (weight == null) {
            return null;
        }
        try {
            return JSONArray.parseArray(weight, VideoWeightVO.class);
        } catch (Exception e) {
            log.error("获取videoWeightVOList失败 {}", weight,e);
            return null;
        }
    }

}
