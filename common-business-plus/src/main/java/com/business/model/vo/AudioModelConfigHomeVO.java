package com.business.model.vo;

import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "音频模型配置信息显示", description = "音频模型配置信息显示")
public class AudioModelConfigHomeVO {

    @Schema(name = "主键id", type = "Long")
    private Long id;

    @Schema(name = "管理语言id", type = "Integer")
    private Integer languageTagId;

    @Schema(name = "展示名称", type = "String")
    private String nameShow;

    @Schema(name = "web首页显示图片", type = "String")
    private String webHomepageUrl;

    public String getWebHomepageUrl() {
        if (webHomepageUrl != null && !webHomepageUrl.contains("https")) {
            //装载前缀
            return CommonStrEnum.IMAGE_PREFIX.getValue() + webHomepageUrl;
        }
        return webHomepageUrl;
    }
}
