package com.business.model.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Schema(name = "mjweb账号管理配置实体显示使用", description = "mj账号管理配置实体显示使用")
public class AdminMjWebConfigVO {

    @Schema(name = "主键id", type = "Long")
    private Long id;

    @Schema(name = "账号名称", type = "String")
    private String accountName;

    @Schema(name = "账号速度", type = "String")
    private String accountSpeed;

    @Schema(name = "账号唯一id", type = "String")
    private String channelId;

    @Schema(name = "账号秘钥", type = "String")
    private String cookie;

    @Schema(name = "账号状态", type = "Integer")
    private Integer status;

    @Schema(name = "账号优先级等级。 1. 2. 3.", type = "Integer")
    private Integer priorityLevel;

    @Schema(name = "是否启用", type = "Integer")
    private Integer isEnable;

    @Schema(name = "备注", type = "String")
    private String remark;

    @Schema(name = "绘画总数", type = "Long")
    private Long drawTotal;

    @TableField(value = "submit_time", fill = FieldFill.INSERT)
    @Schema(name = "创建时间（默认为创建时服务器时间）", type = "Date")
    @JsonFormat(timezone = "Asia/Shanghai",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
