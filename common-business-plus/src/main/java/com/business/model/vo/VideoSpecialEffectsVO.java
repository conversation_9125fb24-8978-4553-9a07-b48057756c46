package com.business.model.vo;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.nacos.ddimg.model.PromptTemplateDTO;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Data
@Schema(name = "模型配置信息返回实体", description = "视频模型配置信息")
public class VideoSpecialEffectsVO {

    @Schema(name = "主键", type = "Long")
    private Integer key;

    @Schema(name = "父级key", type = "String")
    private String parent;

    @Schema(name = "父级名称", type = "String")
    private String parentName;

    @Schema(name = "镜头名称", type = "String")
    private String name;

    @Schema(name = "英文名称", type = "style")
    private String nameEn;

    @Schema(name = "真正的值", type = "String")
    private String value;

    @Schema(name = "镜头显示url", type = "String")
    private String imgUrl;

    @Schema(name = "镜头显示url", type = "String")
    private String url;

    public String getImgUrl() {
        return CommonStrEnum.IMAGE_PREFIX.getValue() + imgUrl;
    }

    public static List<VideoSpecialEffectsVO> videoSpecialEffectsList(String specialEffects) {
        if (specialEffects == null) {
            return null;
        }
        try {
            return JSONArray.parseArray(specialEffects, VideoSpecialEffectsVO.class);
        } catch (Exception e) {
            log.error("获取videoSpecialEffectsList失败 {}", specialEffects,e);
            return null;
        }
    }

}
