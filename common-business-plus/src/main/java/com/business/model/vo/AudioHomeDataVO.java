package com.business.model.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(name = "音频首页数据展示", description = "音频首页数据")
public class AudioHomeDataVO {

    @Schema(name = "id", type = "Long")
    private Long id;

    @Schema(name = "管理语言id", type = "Integer")
    private Integer languageTagId;

    @Schema(name = "标题", type = "String")
    private String title;

    @Schema(name = "标签", type = "String")
    private String tags;

    @Schema(name = "音乐长度", type = "String")
    private Double duration;

    @Schema(name = "歌词", type = "Integer")
    private String prompt;

    @Schema(name = "大图路径", type = "Integer")
    private String imageLargeUrl;

    @Schema(name = "小图路径", type = "Integer")
    private String imageUrl;

    @Schema(name = "音乐MP3路径", type = "String")
    private String audioUrl;

}
