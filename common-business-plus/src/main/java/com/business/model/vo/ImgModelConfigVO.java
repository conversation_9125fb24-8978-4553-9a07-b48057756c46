package com.business.model.vo;

import com.business.db.model.po.ImgModelConfigPO;
import com.nacos.ddimg.ImageModelUtil;
import com.nacos.ddimg.model.ImgScaleDTO;
import com.nacos.ddimg.model.MJSpeedDTO;
import com.nacos.ddimg.model.MJStyleDTO;
import com.nacos.ddimg.model.PromptTemplateDTO;
import com.nacos.enums.ImgOptModelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Data
@Schema(name = "绘图模型配置信息返回实体", description = "绘图模型配置信息返回实体")
public class ImgModelConfigVO {

    @Schema(name = "主键", type = "Long")
    private Long id;

    @Schema(name = "管理语言id", type = "int")
    private int languageTagId;

    @Schema(name = "排序", type = "int")
    private int sort;

    @Schema(name = "字数长度", type = "int")
    private int wordLength;

    @Schema(name = "基本属性", type = "int")
    private int attribute;

    @Schema(name = "模型名称", type = "String")
    private String modelName;

    @Schema(name = "模型显示url", type = "String")
    private String modelUrl;

    @Schema(name = "模板数组", type = "List<PromptTemplateDTO>")
    private List<PromptTemplateDTO> promptTemplates;

    @Schema(name = "mj风格json", type = "List<MJStyleDTO>")
    private List<MJStyleDTO> mjStyles;

    @Schema(name = "mj绘画速度", type = "List<MJSpeedDTO>")
    private List<MJSpeedDTO> mjSpeeds;

    @Schema(name = "图片尺寸", type = "List<ImgScaleDTO>")
    private List<ImgScaleDTO> imgScales;

    @Schema(name = "默认绘图数量", type = "int")
    private int defaultSize;

    @Schema(name = "最小绘图数量", type = "int")
    private int minSize;

    @Schema(name = "最大绘图数量", type = "int")
    private int maxSize;

    @Schema(name = "是否vip特权使用", type = "int")
    private int isVip;

    @Schema(name = "是否显示", type = "int")
    private int isShow;

    @Schema(name = "是否有垫图功能", type = "int")
    private int isAddImg;

    @Schema(name = "状态", type = "int")
    private int state;

    @Schema(name = "显示标签颜色", type = "String")
    private String tagColor;

    @Schema(name = "vip特权使用显示标签", type = "String")
    private String vipUseTag;

    @Schema(name = "禁止使用时展示内容", type = "String")
    private String stateOffTag;

    public ImgModelConfigVO(ImgModelConfigPO imgModelConfigPO) {
        if (imgModelConfigPO == null){
            return;
        }
        this.id = imgModelConfigPO.getId();
        this.languageTagId = imgModelConfigPO.getLanguageTagId();
        this.sort = imgModelConfigPO.getSort();
        this.wordLength = 1000;
        this.attribute = imgModelConfigPO.getAttribute();
        this.modelName = imgModelConfigPO.getModelName();
        this.modelUrl = imgModelConfigPO.getModelUrl();
        this.promptTemplates = ImageModelUtil.getPromptTemplateDTOs(imgModelConfigPO.getPromptTemplates());
        this.imgScales = ImageModelUtil.getImgScaleDTOs(imgModelConfigPO.getImgScales());
        this.defaultSize = imgModelConfigPO.getDefaultSize();
        this.minSize = imgModelConfigPO.getMinSize();
        this.maxSize = imgModelConfigPO.getMaxSize();
        this.isVip = imgModelConfigPO.getIsVip();
        this.isShow = imgModelConfigPO.getIsShow();
        this.isAddImg = imgModelConfigPO.getIsAddImg();
        this.state = imgModelConfigPO.getState();
        this.tagColor = imgModelConfigPO.getTagColor();
        this.vipUseTag = imgModelConfigPO.getVipUseTag();
        this.stateOffTag = imgModelConfigPO.getStateOffTag();
        //仅mj模型初始化时初始化mj风格json
        if (attribute == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N5.getValue()
                || attribute == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_N6.getValue()
                || attribute == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V5_2.getValue()
                || attribute == ImgOptModelEnum.DRAW_ATTRIBUTE_MJAPP_V6.getValue()
        ){
            this.mjStyles = ImageModelUtil.getMJStyleDTOs(imgModelConfigPO.getMjStyles());
            this.mjSpeeds = ImageModelUtil.getMJSpeedDTOs();
        }
    }
}
