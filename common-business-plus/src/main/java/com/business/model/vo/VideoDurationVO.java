package com.business.model.vo;

import com.alibaba.fastjson2.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Schema(name = "视频时长展示实体", description = "视频时长展示实体")
@Data
public class VideoDurationVO {

    @Schema(name = "时长key", type = "Integer")
    private Integer key;

    @Schema(name = "显示名称", type = "String")
    private String name;

    @Schema(name = "真实值", type = "Integer")
    private String value;

    @Schema(name = "休闲模式是否可用", type = "Boolean")
    private Boolean isLeisure;

    public static List<VideoDurationVO> videoDurationVOList(String duration) {
        if (duration == null) {
            return null;
        }
        try {
            return JSONArray.parseArray(duration, VideoDurationVO.class);
        } catch (Exception e) {
            log.error("获取videoDurationVOList失败 {}", duration,e);
            return null;
        }
    }

}
