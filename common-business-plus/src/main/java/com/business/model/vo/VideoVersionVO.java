package com.business.model.vo;

import com.alibaba.fastjson2.JSONArray;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Schema(name = "视频版本展示实体", description = "视频版本展示实体")
@Data
public class VideoVersionVO {

    @Schema(name = "版本key", type = "Integer")
    private Integer key;

    @Schema(name = "显示版本名称", type = "String")
    private String name;

    @Schema(name = "描述版本", type = "String")
    private String describe;

    @Schema(name = "数值", type = "String")
    private String value;

    @Schema(name = "使用vip点数", type = "String")
    private String ddVipUseNumStr;

    @Schema(name = "使用点数", type = "Boolean")
    private String ddUseNumStr;

    @Schema(name = "显示图标", type = "String")
    private String imgUrl;

    public static List<VideoVersionVO> videoVersionVOList(String version) {
        if (version == null) {
            return null;
        }
        try {
            return JSONArray.parseArray(version, VideoVersionVO.class);
        } catch (Exception e) {
            log.error("获取video版本VOList失败 {}", version, e);
            return null;
        }
    }

    public String getImgUrl() {
        if (imgUrl != null && !imgUrl.contains("https") && !imgUrl.contains(";base64")) {
            //装载前缀
            return CommonStrEnum.IMAGE_PREFIX.getValue() + imgUrl;
        }
        return imgUrl;
    }

}
