package com.business.model.vo;

import com.business.enums.BIntEnum;
import com.business.model.aitask.audio.AudioTaskInfo;
import com.business.model.bo.ImgDrawInstructBO;
import com.business.model.po.ImgDrawDetlPO;
import com.business.model.po.ImgDrawRecordPO;
import com.business.utils.ImgDrawUtil;
import com.business.utils.TaskM2MUtil;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nacos.enums.ImgOptModelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Schema(name = "绘图历史记录返回实体" , description = "绘图历史记录返回实体")
@Data
public class ImgDrawHistoryVO implements Serializable {
    @Schema(name = "主键id", type = "Long")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(name = "用户id", type = "Long")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @Schema(name = "操作属性", type = "int")
    private Integer optAttribute;

    @Schema(name = "模型属性", type = "int")
    private Integer modeAttribute;

    @Schema(name = "功能类型（1绘图；2写真；3视频；4音频）", type = "Integer")
    private Integer funType;

    @Schema(name = "时间标签展示：今天、昨天、时间" , type = "String")
    private String timeTitle;

    @Schema(name = "操作标题一级" , type = "String")
    private String optTitleOne;

    @Schema(name = "操作标题二级" , type = "String")
    private String optTitleTwo;

    @Schema(name = "垫图路径列表" , type = "String")
    private List<String> initImgUrls;

    @Schema(name = "垫图对象：json格式", type = "String")
    private String initImgObject;

    @Schema(name = "绘图指令内容" , type = "String")
    private String prompt;

    @Schema(name = "汉化指令内容" , type = "String")
    private String promptUse;

    @Schema(name = "宽高尺寸", type = "double")
    private Double whDivide;

    @Schema(name = "任务状态", type = "Integer")
    private Integer status;

    @Schema(name = "生成图片个数", type = "Integer")
    private Integer imgQuantity;

    @Schema(name = "任务完成时间", type = "String")
    private String finishTime;

    @Schema(name = "是否发布", type = "int")
    private Integer isPublish;

    @Schema(name = "原始图片id（本次操作父级图片id）", type = "Long")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long originalImgId;

    @Schema(name = "绘图详情信息列表" , type = "List")
    private List<ImgDrawDetlVO> imgDrawDetls;

    @Schema(name = "任务渐显进度", type = "String")
    private String jobRunningSchedule;

    @Schema(title = "指令列表：汉化版", type = "List<String>")
    private List<String> instructs;

    @Schema(name = "备注", type = "String")
    private String remark;

    private byte[] videoData;

    @Schema(name = "音频任务标题", type = "String")
    private String audioTitle;

    @Schema(name = "音频任务歌词", type = "String")
    private String audioLyric;

    @Schema(name = "音频任务风格", type = "String")
    private String audioStyle;

    @Schema(name = "任务id", type = "String")
    private String jobId;

    public String getPromptUse() {
        ImgDrawInstructBO imgInstructBO = ImgDrawUtil.getImgDrawPrompt(promptUse);
        if (imgInstructBO != null) {
            instructs = imgInstructBO.getInstructs();
            return imgInstructBO.getPrompt();
        }
        return null;
    }

    @Schema(name = "音频任务信息", type = "String")
    private AudioTaskInfo audioTaskInfo;

    /**
     * 初始化历史记录
     * @param imgDrawRecordPO
     * @return
     */
    public static ImgDrawHistoryVO intiImgDrawHistoryVO(ImgDrawRecordPO imgDrawRecordPO, List<String> stringList) {
        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
        imgDrawHistoryVO.setFunType(imgDrawRecordPO.getFunType());
        imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawHistoryVO.setPrompt(imgDrawRecordPO.getPromptInit());
        imgDrawHistoryVO.setPromptUse(imgDrawRecordPO.getPromptUse());
        imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
        imgDrawHistoryVO.setInitImgUrls(stringList);
        imgDrawHistoryVO.setImgQuantity(imgDrawRecordPO.getImgQuantity());
        imgDrawHistoryVO.setOriginalImgId(imgDrawRecordPO.getOriginalImgId());
        imgDrawHistoryVO.setTimeTitle(com.nacos.ddimg.ImgDrawUtil.getTimeTitle(imgDrawRecordPO.getCreateTime()));
        imgDrawHistoryVO.setOptTitleOne(ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute()));
        imgDrawHistoryVO.setAudioTitle(imgDrawRecordPO.getAudioTitle());
        imgDrawHistoryVO.setAudioLyric(imgDrawRecordPO.getAudioLyric());
        imgDrawHistoryVO.setAudioStyle(imgDrawRecordPO.getAudioStyle());

        imgDrawHistoryVO.setAudioTaskInfo(TaskM2MUtil.ImgDrawRecordPO2AudioTaskInfo(imgDrawRecordPO));

        return imgDrawHistoryVO;
    }

    public static List<ImgDrawDetlVO> getImgDrawDetlVOS(List<ImgDrawDetlPO> imgDrawDetlPOS, ImgDrawHistoryVO imgDrawHistoryVO) {
        List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();
        for (ImgDrawDetlPO imgDrawDetlPO : imgDrawDetlPOS) {
            ImgDrawDetlVO imgDrawDetlVO = new ImgDrawDetlVO();
            imgDrawDetlVO.setId(imgDrawDetlPO.getId());
            imgDrawDetlVO.setDrawRecordId(imgDrawDetlPO.getDrawRecordId());
            imgDrawDetlVO.setOptAttribute(imgDrawDetlPO.getOptAttribute());
            imgDrawDetlVO.setImgIndex(imgDrawDetlPO.getImgIndex());
            imgDrawDetlVO.setWhDivide(imgDrawDetlPO.getWhDivide());
            imgDrawDetlVO.setImgWidth(imgDrawDetlPO.getImgWidth());
            imgDrawDetlVO.setImgHeight(imgDrawDetlPO.getImgHeight());
            imgDrawDetlVO.setImgHue(imgDrawDetlPO.getImgHue());
            imgDrawDetlVO.setImgSize(imgDrawDetlPO.getImgSize());
            imgDrawDetlVO.setImgSourceUrl(imgDrawDetlPO.getImgSourceUrl());
            imgDrawDetlVO.setImgUrl(imgDrawDetlPO.getImgUrl());
            imgDrawDetlVO.setImgType(imgDrawDetlPO.getImgType());
            imgDrawDetlVO.setIsPublish(imgDrawDetlPO.getIsPublish());
            if (Objects.equals(imgDrawDetlPO.getIsPublish(), BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue())){
                imgDrawHistoryVO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue());
            }
            imgDrawDetlVOS.add(imgDrawDetlVO);
        }
        return imgDrawDetlVOS;
    }
}
