package com.business.model.vo;

import com.alibaba.fastjson2.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Schema(name = "DOMO视频参考展示实体", description = "视频速度展示实体")
@Data
public class VideoReferModeVO {

    @Schema(name = "速度key", type = "Integer")
    private Integer key;

    @Schema(name = "显示名称", type = "String")
    private String name;

    @Schema(name = "数值", type = "Double")
    private String value;

    public static List<VideoReferModeVO> videoReferModeVOList(String videoRefer) {
        if (videoRefer == null) {
            return null;
        }
        try {
            return JSONArray.parseArray(videoRefer, VideoReferModeVO.class);
        } catch (Exception e) {
            log.error("获取videoReferModeList失败 {}", videoRefer,e);
            return null;
        }
    }

}
