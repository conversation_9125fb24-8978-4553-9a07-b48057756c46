package com.business.model.vo;

import com.business.enums.BDDUseNumEnum;
import com.business.model.po.VideoModelConfigPO;
import com.nacos.ddimg.ImageModelUtil;
import com.nacos.ddimg.model.ImgScaleDTO;
import com.nacos.enums.CommonStrEnum;
import com.nacos.enums.ImgOptModelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(name = "视频模型配置信息返回实体", description = "视频模型配置信息")
public class VideoModelConfigVO {

    @Schema(name = "主键", type = "Long")
    private Long id;

    @Schema(name = "管理语言id", type = "int")
    private int languageTagId;

    @Schema(name = "排序", type = "int")
    private int sort;

    @Schema(name = "基本属性-模型编号", type = "int")
    private int attribute;

    @Schema(name = "模型名称", type = "String")
    private String modelName;

    @Schema(name = "模型值", type = "String")
    private String modelValue;

    @Schema(name = "模型显示url", type = "String")
    private String modelUrl;

    @Schema(name = "风格数组", type = "List")
    private List<VideoSpecialEffectsVO> videoStyles;

    @Schema(name = "特效数组", type = "List")
    private List<VideoSpecialEffectsVO> specialEffects;

    @Schema(name = "镜头数组", type = "List")
    private List<VideoSpecialEffectsVO> videoLens;

    @Schema(name = "比列数组", type = "List")
    private List<ImgScaleDTO> videoScales;

    @Schema(name = "视频权重", type = "List")
    private List<VideoWeightVO> weight;

    @Schema(name = "时长json", type = "List")
    private List<VideoDurationVO> duration;

    @Schema(name = "速度json", type = "List")
    private List<VideoLoopVO> loop;

    @Schema(name = "速度json", type = "List")
    private List<VideoSpeedVO> speed;

    @Schema(name = "视频版本", type = "List")
    private List<VideoSpeedVO> videoVersion;

    @Schema(name = "参考json", type = "List")
    private List<VideoReferModeVO> referMode;

    @Schema(name = "是否vip特权使用", type = "int")
    private int isVip;

    @Schema(name = "vip特权使用显示标签", type = "String")
    private String vipUseTag;

    @Schema(name = "是否显示", type = "int")
    private int isShow;

    @Schema(name = "状态", type = "int")
    private int state;

    @Schema(name = "提示词例子", type = "String")
    private String promptEmp;

    @Schema(name = "禁止使用时展示内容", type = "String")
    private String stateOffTag;

    @Schema(name = "普通用户消耗点子数量", type = "int")
    private String useDDQua;

    @Schema(name = "Vip消耗点子数量", type = "int")
    private String vipUseDDQua;

    @Schema(name = "绘图模型列表，为复制指令使用", type = "String")
    private String drawingModels;

    public String getModelUrl() {
        return CommonStrEnum.IMAGE_PREFIX.getValue() + modelUrl;
    }

    public VideoModelConfigVO(VideoModelConfigPO videoModelConfigPO) {
        if (videoModelConfigPO == null) {
            return;
        }
        this.id = videoModelConfigPO.getId();
        this.languageTagId = videoModelConfigPO.getLanguageTagId();
        this.sort = videoModelConfigPO.getSort();
        this.attribute = videoModelConfigPO.getAttribute();
        this.modelName = videoModelConfigPO.getModelName();
        this.modelValue = videoModelConfigPO.getModelValue();
        this.modelUrl = videoModelConfigPO.getModelUrl();
        this.videoStyles = VideoSpecialEffectsVO.videoSpecialEffectsList(videoModelConfigPO.getVideoStyles());
        this.specialEffects = VideoSpecialEffectsVO.videoSpecialEffectsList(videoModelConfigPO.getSpecialEffects());
        this.videoLens = VideoSpecialEffectsVO.videoSpecialEffectsList(videoModelConfigPO.getVideoLens());
        this.videoScales = ImageModelUtil.getImgScaleDTOs(videoModelConfigPO.getVideoScales());
        this.weight = VideoWeightVO.videoWeightVOList(videoModelConfigPO.getWeight());
        this.duration = VideoDurationVO.videoDurationVOList(videoModelConfigPO.getDuration());
        this.loop = VideoLoopVO.videoLoopVOList(videoModelConfigPO.getVideoLoop());
        this.speed = VideoSpeedVO.videoSpeedVOList(videoModelConfigPO.getSpeed());
        this.referMode = VideoReferModeVO.videoReferModeVOList(videoModelConfigPO.getReferMode());
        this.isVip = videoModelConfigPO.getIsVip();
        this.vipUseTag = videoModelConfigPO.getVipUseTag();
        this.isShow = videoModelConfigPO.getIsShow();
        this.state = videoModelConfigPO.getState();
        this.stateOffTag = videoModelConfigPO.getStateOffTag();
        this.videoVersion = VideoSpeedVO.videoSpeedVOList(videoModelConfigPO.getVideoVersion());
        this.promptEmp = videoModelConfigPO.getPromptEmp();
        if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_SD_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.SD_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.SD_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_BYTE_LENS.getValue()) {
            this.useDDQua = BDDUseNumEnum.BYTE_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.BYTE_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_LE_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.LE_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.LE_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_PIKA_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.PIKA_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.PIKA_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_DOMO_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.DOMO_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.DOMO_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_LUMA_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.LUMA_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.LUMA_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY2_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.RUNWAY2_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.RUNWAY2_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_ZHIPU_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.ZHIPU_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.ZHIPU_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.HAILUO_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.HAILUO_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_KLING_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.KLING_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.KLING_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.DREAMFACTORY_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.DREAMFACTORY_OPT_VIDEO.getDdVipUseNumStr();
        } else if (attribute == ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI2_BASICS.getValue()) {
            this.useDDQua = BDDUseNumEnum.DAFENQI2_OPT_VIDEO.getDdUseNumStr();
            this.vipUseDDQua = BDDUseNumEnum.DAFENQI2_OPT_VIDEO.getDdVipUseNumStr();
        } else {
            this.useDDQua = "10";
            this.vipUseDDQua = "10";
        }
    }
}
