package com.business.model.aitask.audio;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name = "音频任务展示实体", description = "音频任务展示实体")
@Data
public class AudioTaskInfo {

    @Schema(name = "音频模型id：默认1", type = "Long")
    private Long modelId;

    @Schema(name = "音频创作风格id", type = "Long")
    private Long styleId;

    @Schema(name = "歌词标题", type = "String")
    private String songLabel;

    @Schema(name = "歌词内容", type = "String")
    private String songLyrics;

    @Schema(name = "是否为纯音乐：v2.3", type = "Boolean")
    private Boolean isPureMusic;

    @Schema(name = "自定义词曲风格（类型3-使用）：v2.3", type = "String")
    private String customStyle;

    @Schema(name = "灵感（类型1-使用）：v2.3", type = "String")
    private String inspiration;

    @Schema(name = "请求类型（1灵感；2高级；3自定义）：v2.3", type = "Integer")
    private Integer type;

    @Schema(name = "上传音频时长音频", type = "Long")
    private Long continueAt;

    @Schema(name = "上传音频原地址URL", type = "String")
    private String musicOssUrl;

}
