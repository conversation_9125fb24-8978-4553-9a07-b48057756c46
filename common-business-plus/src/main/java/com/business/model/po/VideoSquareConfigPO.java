package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName(VideoSquareConfigPO.TABLE_NAME)
@Schema(name = "视频广场配置信息", description = "视频广场配置信息")
public class VideoSquareConfigPO extends BaseEntity {

    public static final String TABLE_NAME = "video_square_config";

    @Schema(name = "视频类型", type = "int")
    private int type;

    @Schema(name = "视频名称", type = "String")
    private String videoName;

    @Schema(name = "视频URL", type = "String")
    private String videoUrl;

    @Schema(name = "视频图片地址", type = "String")
    private String vedeoImgUrl;

    @Schema(name = "高度", type = "Integer")
    private Integer height;

    @Schema(name = "宽度", type = "Integer")
    private Integer width;

    @Schema(name = "底色", type = "String")
    private String backgroundColor;

}
