package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("audio_model_config")
@Schema(name = "音频模型配置信息", description = "音频模型配置信息")
public class AudioModelConfigPO extends BaseEntity {

    @Schema(name = "管理语言id", type = "Integer")
    private Integer languageTagId;

    @Schema(name = "排序", type = "Integer")
    private Integer sort;

    @Schema(name = "展示名称", type = "String")
    private String nameShow;

    @Schema(name = "使用风格", type = "String")
    private String useStyle;

    @Schema(name = "风格显示url", type = "String")
    private String modelUrl;

    @Schema(name = "web首页显示图片", type = "String")
    private String webHomepageUrl;

    @Schema(name = "是否显示在首页", type = "Integer")
    private Integer isHomepageShow;

    @Schema(name = "是否vip特权使用", type = "Integer")
    private Integer isVip;

    @Schema(name = "是否正常：1正常；0异常", type = "Integer")
    private Integer state;

    @Schema(name = "类型", type = "Integer")
    private Integer type;

    @Schema(name = "vip标签展示内容", type = "String")
    private String vipUseTag;

    @Schema(name = "状态禁用展示内容", type = "String")
    private String stateOffTag;

}
