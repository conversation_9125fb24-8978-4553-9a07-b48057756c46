package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import lombok.*;

import java.io.Serializable;

/**
 * @className: com.intelligent.bot.model-> FlowRecord
 * @description: 点子流水记录
 * @author: Admin
 * @createDate: 2023-08-14 14:10
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_flow_record")
public class MjFlowRecordPO extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 记录类型：0-增加、1-消耗
     */
    private Integer recordType;

    /**
     * 点子数量
     */
    //private Double dzQua;
    /**
     * 点子数量
     */
    private Double num;

    /** 备注 */
    private String remark;

}
