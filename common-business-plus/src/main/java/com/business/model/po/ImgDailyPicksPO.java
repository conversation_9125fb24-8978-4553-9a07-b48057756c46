package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.nacos.base.BaseEntity;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName(ImgDailyPicksPO.TABLE_NAME)
@Schema(name = "图片精选实体", description = "图片精选实体")
public class ImgDailyPicksPO extends BaseEntity {

    public static final String TABLE_NAME = "img_daily_picks";

    @Schema(name = "绘图记录 ID", type = "Long")
    private Long drawRecordId;

    @Schema(name = "关联用户ID", type = "Long")
    private Long userId;

    @Schema(name = "精选类型（0 每日精选 1是每周精选 2是每月精选）", type = "String")
    private Integer picksType;


}
