package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName(ImgModelConfigPO.TABLE_NAME)
@Schema(name = "绘图模型配置信息", description = "绘图模型配置信息")
public class ImgModelConfigPO extends BaseEntity {

    public static final String TABLE_NAME = "img_model_config";

    @Schema(name = "管理语言id", type = "int")
    private int languageTagId;

    @Schema(name = "排序", type = "int")
    private int sort;

    @Schema(name = "基本属性", type = "int")
    private int attribute;

    @Schema(name = "模型名称", type = "String")
    private String modelName;

    @Schema(name = "模型值", type = "String")
    private String modelValue;

    @Schema(name = "模型显示url", type = "String")
    private String modelUrl;

    @Schema(name = "模板数组", type = "String")
    private String promptTemplates;

    @Schema(name = "mj风格json", type = "String")
    private String mjStyles;

    @Schema(name = "mj版本json", type = "String")
    private String mjVersion;

    @Schema(name = "图片尺寸", type = "String")
    private String imgScales;

    @Schema(name = "图片张数", type = "String")
    private String imgNumber;

    @Schema(name = "默认绘图数量", type = "int")
    private int defaultSize;

    @Schema(name = "最小数量", type = "int")
    private int minSize;

    @Schema(name = "绘图最大数量", type = "int")
    private int maxSize;

    @Schema(name = "是否vip特权使用", type = "int")
    private int isVip;

    @Schema(name = "是否显示", type = "int")
    private int isShow;

    @Schema(name = "是否有垫图功能", type = "int")
    private int isAddImg;

    @Schema(name = "状态", type = "int")
    private int state;

    @Schema(name = "vip特权使用显示标签", type = "String")
    private String vipUseTag;

    @Schema(name = "禁止使用时展示内容", type = "String")
    private String stateOffTag;

    @Schema(name = "是否支持角色（0 否 1是）", type = "int")
    private int isSupportCref;

    @Schema(name = "是否支持风格（0 否 1是）", type = "int")
    private int isSupportSref;

    public String getModelUrl() {
        return CommonStrEnum.IMAGE_PREFIX.getValue() + modelUrl;
    }
}
