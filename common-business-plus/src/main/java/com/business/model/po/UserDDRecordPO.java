package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import com.nacos.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("user_ddrecord")
@Schema(name = "用户点点数量记录", description = "用户点点数量记录")
public class UserDDRecordPO extends BaseEntity {

    @Schema(name = "用户userid", type = "Long")
    private Long userId;

    @Schema(name = "操作源id", type = "Long")
    private Long sourceId;

    @Schema(name = "类型", type = "Integer")
    private Integer type;

    @Schema(name = "子类型", type = "Integer")
    private Integer typeItem;

    @Schema(name = "总量", type = "BigDecimal")
    private Double total;

    @Schema(name = "总使用量", type = "BigDecimal")
    private Double totalUsage;

    @Schema(name = "到期时间", type = "Date")
    private Date expirationTime;
    public UserDDRecordPO() {
    }

    //初始化新增记录
    public UserDDRecordPO(Long userId, Long sourceId, Integer type, Integer typeItem, Double total) {
        this.userId = userId;
        this.sourceId = sourceId;
        this.type = type;
        this.typeItem = typeItem;
        this.total = total;
        this.totalUsage = (double) 0;
        this.expirationTime = DateUtil.getDateAddMonth(DateUtil.getDateNowShanghai(),1);
    }

    public UserDDRecordPO(Long userId, Long sourceId, Integer type, Integer typeItem, Double total, Date expirationTime) {
        this.userId = userId;
        this.sourceId = sourceId;
        this.type = type;
        this.typeItem = typeItem;
        this.total = total;
        this.totalUsage = (double) 0;
        this.expirationTime = expirationTime;
    }
}
