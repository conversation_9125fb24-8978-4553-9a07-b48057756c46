package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.nacos.base.BaseEntity;
import com.nacos.enums.CommonStrEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName(ImgDrawDetlPO.TABLE_NAME)
@Schema(name = "绘图详情实体", description = "绘图详情实体")
public class ImgDrawDetlPO extends BaseEntity {

    public static final String TABLE_NAME = "img_draw_detl";

    @Schema(name = "绘图记录 ID", type = "Long")
    private Long drawRecordId;

    @Schema(name = "模型属性属性：见枚举", type = "int")
    private Integer modeAttribute;

    @Schema(name = "操作属性：见枚举", type = "int")
    private Integer optAttribute;

    @Schema(name = "关联用户ID", type = "Long")
    private Long userId;

    @Schema(name = "点赞数", type = "int")
    private Integer goodQua;

    @Schema(name = "收藏数", type = "int")
    private Integer collectQua;

    @Schema(name = "图片索引位置", type = "int")
    private Integer imgIndex;

    @Schema(name = "音频id", type = "String")
    private String sunoJobId;

    @Schema(name = "音频Mp3 地址", type = "String")
    private String audioUrl;

    @Schema(name = "音频Mp4 地址", type = "String")
    private String videoUrl;

    @Schema(name = "是否公开", type = "int")
    private Integer isOpen;

    @Schema(name = "是否保存到我的创意", type = "int")
    private Integer isSave;

    @Schema(name = "源图片地址", type = "String")
    private String imgSourceUrl;

    @Schema(name = "本地存储地址", type = "String")
    private String imgUrl;

    @Schema(name = "是否发布", type = "int")
    private Integer isPublish;

    @Schema(name = "宽高尺寸", type = "double")
    private Double whDivide;

    @Schema(name = "图片大小", type = "Long")
    private Long imgSize;

    @Schema(name = "图片宽", type = "int")
    private Integer imgWidth;

    @Schema(name = "图片高", type = "int")
    private Integer imgHeight;

    @Schema(name = "图片类型", type = "String")
    private String imgType;

    @Schema(name = "图片主色调", type = "String")
    private String imgHue;


    public String getImgUrl() {
        if (imgUrl != null && !imgUrl.contains("https")) {
            //装载前缀
            return CommonStrEnum.IMAGE_PREFIX.getValue() + imgUrl;
        }
        return imgUrl;
    }

    public static ImgDrawDetlPO buildImgDrawDetlPO(Long drawRecordId, Integer optAttribute, Integer modeAttribute, Long userId,
                                                   Integer imgIndex, String imgSourceUrl, Double whDivide,
                                                   Long imgSize, Integer imgWidth, Integer imgHeight, String imgType, String imgHue){
        ImgDrawDetlPO impgDrawDetlPO = new ImgDrawDetlPO();
        impgDrawDetlPO.setId(IdWorker.getId());
        impgDrawDetlPO.drawRecordId = drawRecordId;
        impgDrawDetlPO.modeAttribute = modeAttribute;
        impgDrawDetlPO.optAttribute = optAttribute;
        impgDrawDetlPO.userId = userId;
        impgDrawDetlPO.imgIndex = imgIndex;
        impgDrawDetlPO.imgSourceUrl = imgSourceUrl;
        impgDrawDetlPO.whDivide = whDivide;
        impgDrawDetlPO.imgSize = imgSize;
        impgDrawDetlPO.imgWidth = imgWidth;
        impgDrawDetlPO.imgHeight = imgHeight;
        impgDrawDetlPO.imgType = imgType;
        impgDrawDetlPO.imgHue = imgHue;
        return impgDrawDetlPO;
    }

}
