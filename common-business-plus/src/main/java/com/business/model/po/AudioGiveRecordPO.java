package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("audio_give_record")
@Schema(name = "音频赠送记录", description = "音频赠送记录")
public class AudioGiveRecordPO extends BaseEntity {

    @Schema(name = "用户id", type = "Long")
    private Long userId;

    @Schema(name = "赠送数量", type = "Integer")
    private Integer giveCount;

    @Schema(name = "任务id", type = "Long")
    private Long drawRecordId;

    public AudioGiveRecordPO(){}

    public AudioGiveRecordPO(Long userId, Integer giveCount, Long drawRecordId){
        this.userId = userId;
        this.giveCount = giveCount;
        this.drawRecordId = drawRecordId;
    }

}
