package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName(VideoModelConfigPO.TABLE_NAME)
@Schema(name = "视频模型配置信息", description = "视频模型配置信息")
public class VideoModelConfigPO extends BaseEntity {

    public static final String TABLE_NAME = "video_model_config";

    @Schema(name = "管理语言id", type = "int")
    private int languageTagId;

    @Schema(name = "排序", type = "int")
    private int sort;

    @Schema(name = "基本属性-模型编号", type = "int")
    private int attribute;

    @Schema(name = "模型名称", type = "String")
    private String modelName;

    @Schema(name = "模型值", type = "String")
    private String modelValue;

    @Schema(name = "模型显示url", type = "String")
    private String modelUrl;

    @Schema(name = "风格数组", type = "String")
    private String videoStyles;

    @Schema(name = "特效数组", type = "String")
    private String specialEffects;

    @Schema(name = "镜头数组", type = "String")
    private String videoLens;

    @Schema(name = "比列数组", type = "String")
    private String videoScales;

    @Schema(name = "图片到视频比列数组", type = "String")
    private String imgVideoScales;

    @Schema(name = "视频权重", type = "String")
    private String weight;

    @Schema(name = "视频时长数组", type = "String")
    private String duration;

    @Schema(name = "视频速度数组", type = "String")
    private String speed;

    @Schema(name = "视频版本", type = "String")
    private String videoVersion;

    @Schema(name = "视频循环数组", type = "String")
    private String VideoLoop;

    @Schema(name = "视频参考数组", type = "String")
    private String referMode;

    @Schema(name = "是否vip特权使用", type = "int")
    private int isVip;

    @Schema(name = "标题1", type = "String")
    private String title;

    @Schema(name = "标题2", type = "String")
    private String title2;

    @Schema(name = "提示词例子", type = "String")
    private String promptEmp;

    @Schema(name = "提示词例子（图片到视频）", type = "String")
    private String promptEmpImg;

    @Schema(name = "标签颜色", type = "String")
    private String tagColor;

    @Schema(name = "vip特权使用显示标签", type = "String")
    private String vipUseTag;

    @Schema(name = "文生视频", type = "int")
    private int textVideo;

    @Schema(name = "图生视频", type = "int")
    private int imgVideo;

    @Schema(name = "图片数量", type = "int")
    private int picNum;

    @Schema(name = "首尾帧图片是否必填: 0.可首、可尾、可首尾  1.传首必须要传尾 2.传尾必须要传首 3.首尾缺一不可", type = "int")
    private int isFirstLast;

    @Schema(name = "限制文件大小", type = "int")
    private int fileSize;

    @Schema(name = "是否显示", type = "int")
    private int isRatioShow;

    @Schema(name = "是否显示", type = "int")
    private int isShow;

    @Schema(name = "状态", type = "int")
    private int state;

    @Schema(name = "禁止使用时展示内容", type = "String")
    private String stateOffTag;

}
