package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("audio_home_data")
@Schema(name = "音频首页数据", description = "音频首页数据")
public class AudioHomeDataPO extends BaseEntity {

    @Schema(name = "管理语言id", type = "Integer")
    private Integer languageTagId;

    @Schema(name = "风格id", type = "Integer")
    private Long styleId;

    @Schema(name = "标题", type = "String")
    private String title;

    @Schema(name = "标签", type = "String")
    private String tags;

    @Schema(name = "音乐长度", type = "String")
    private Double duration;

    @Schema(name = "歌词", type = "Integer")
    private String prompt;

    @Schema(name = "大图路径", type = "Integer")
    private String imageLargeUrl;

    @Schema(name = "小图路径", type = "Integer")
    private String imageUrl;

    @Schema(name = "音乐MP3路径", type = "String")
    private String audioUrl;

    @Schema(name = "视频MP4路径", type = "String")
    private String videoUrl;

    @Schema(name = "状态：1正常 0不正常", type = "Integer")
    private Integer state;

}
