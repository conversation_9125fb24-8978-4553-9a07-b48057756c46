package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("admin_mjaccount_config")
@Schema(name = "mj账号管理配置实体", description = "mj账号管理配置实体")
public class AdminMjAccountConfigPO extends BaseEntity {

    @Schema(name = "mj用户id", type = "String")
    @TableField(value = "user_id")
    private String userId;

    @Schema(name = "别名", type = "String")
    private String name;

    @Schema(name = "discord账号信息", type = "String")
    private String discordAccount;

    @Schema(name = "discord账号密码", type = "String")
    private String discordPassword;

    @Schema(name = "mj api token", type = "String")
    private String appToken;

    @Schema(name = "个人cookie信息", type = "String")
    private String cookie;

    @Schema(name = "谷歌jwtapi刷新token", type = "String")
    private String gapiRefreshToken;

    @Schema(name = "到期时间", type = "Date")
    private Date expireTime;

    @Schema(name = "剩余绘画时间", type = "int")
    private Integer periodCredits;

    @Schema(name = "总快速绘画时间", type = "int")
    private Integer creditAllocation;

    @Schema(name = "是否使用", type = "int")
    private Integer isUse;

    @Schema(name = "状态", type = "int")
    private Integer state;

    @Schema(name = "类型", type = "int")
    private Integer type;



    @Schema(name = "mjweb接口的请求参数", type = "String")
    private String mjWebBody;

    @Schema(name = "mjweb接口的cookie参数", type = "String")
    private String mjWebLogcookie;

    @Schema(name = "mjweb接口的使用cookie", type = "String")
    private String mjWebUsecookie;







    @Schema(name = "开始时间", type = "String")
    private String timeOn;

    @Schema(name = "结束时间", type = "String")
    private String timeEnd;

    @Schema(name = "高级最大并发数量", type = "int")
    private Integer maxProConcurrent;

    @Schema(name = "一般最大并发数量", type = "int")
    private Integer maxConcurrent;

    @Schema(name = "下次token刷新时间", type = "Date")
    @TableField(value = "token_refresh_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tokenRefreshTime;

    @Schema(name = "下次剩余小时刷新时间", type = "Date")
    @TableField(value = "credits_refresh_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creditsRefreshTime;

}
