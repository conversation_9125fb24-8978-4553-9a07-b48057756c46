package com.business.model.po;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;

/**
 *
 * sys 系统---通知实体类
 * <AUTHOR>
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_notification")
public class SysNotificationPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 326308725675949330L;

    @Schema(name = "用户id" , type = "String")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @Schema(name = "目标用户id" , type = "Long")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long targUserId;

    @Schema(name = "通知类型（0是系统消息 1是绘图任务 2支付 3是会员有关 4点子到期 8签到通知 101 通用站内通知（签到通知） 201 活动类消息）" , type = "String")
    private Integer notifType;

    @Schema(name = "通知标题" , type = "String")
    private String notifTitle;

    @Schema(name = "通知内容" , type = "String")
    private String notifContent;

    @Schema(name = "图片url" , type = "String")
    private String imageUrl;

    @Schema(name = "H5链接地址" , type = "String")
    private String linkAddre;

    @Schema(name = "视频url" , type = "String")
    private String videoUrl;

    @Schema(name = "是否已读（0未读， 1已读）========临时字段" , type = "Integer")
    @JsonIgnore
    private Integer isRead;

    @Schema(name = "是否弹框提示（默认0， 1弹框）" , type = "Integer")
    private Integer isPopup;

    @Schema(name = "任务ID" , type = "Long")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;



    /**
     * 构造器
     * @return SysNotificationPO
     */
    public static SysNotificationPO buildSysNotification(Long userId, Integer notifType, String notifTitle, String notifContent,
                                                         Integer isPopup, Long taskId, String jobImageUrl) {
        SysNotificationPO sysNotificationPO = new SysNotificationPO();
        sysNotificationPO.setId(IdWorker.getId());
        sysNotificationPO.userId = userId;
        sysNotificationPO.notifType = notifType;
        sysNotificationPO.notifTitle = notifTitle;
        sysNotificationPO.notifContent = notifContent;
        sysNotificationPO.isRead = 0;
        sysNotificationPO.isPopup = isPopup;
        sysNotificationPO.taskId = taskId;
        sysNotificationPO.setRemark(jobImageUrl);
        return sysNotificationPO;
    }



}
