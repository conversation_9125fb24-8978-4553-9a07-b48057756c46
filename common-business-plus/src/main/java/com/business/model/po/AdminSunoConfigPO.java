package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("admin_suno_config")
@Schema(name = "音频账号配置实体", description = "音频账号配置实体")
public class AdminSunoConfigPO extends BaseEntity {

    @Schema(name = "cfbm", type = "String")
    private String cfbm;

    @Schema(name = "密钥信息1", type = "String")
    private String client;

    @Schema(name = "密钥信息2", type = "String")
    private String clientUat;

    @Schema(name = "密钥信息3", type = "String")
    private String cfuvid;

    @Schema(name = "密钥信息4", type = "String")
    private String mp;

    @Schema(name = "sessions id", type = "String")
    private String sessions;

    @Schema(name = "是否使用", type = "int")
    private Integer isUse;

    @Schema(name = "状态", type = "int")
    private Integer state;

}
