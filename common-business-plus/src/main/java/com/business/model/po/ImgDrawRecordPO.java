package com.business.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName("img_draw_record")
@Schema(name = "绘图详情实体", description = "绘图详情实体")
public class ImgDrawRecordPO extends BaseEntity {

    @Schema(name = "用户id", type = "Long")
    private Long userId;

    @Schema(name = "上级任务id", type = "Long")
    private Long superId;

    @Schema(name = "操作属性", type = "int")
    private Integer optAttribute;

    @Schema(name = "模型属性", type = "int")
    private Integer modeAttribute;

    @Schema(name = "垫图图片列表", type = "String")
    private String initImgUrls;

    @Schema(name = "垫图对象：json格式", type = "String")
    private String initImgObject;

    @Schema(name = "用户传递关键词", type = "String")
    private String promptInit;

    @Schema(name = "真实传递到接口", type = "String")
    private String promptUse;

    @Schema(name = "任务描述", type = "String")
    private String description;

    @Schema(name = "任务状态", type = "Long")
    private Integer status;

    @Schema(name = "宽高尺寸", type = "double")
    private Double whDivide;

    @Schema(name = "功能类型", type = "int")
    private Integer funType;

    @Schema(name = "图片宽", type = "int")
    private Integer width;

    @Schema(name = "图片高", type = "int")
    private Integer height;

    @Schema(name = "图片数量", type = "int")
    private Integer imgQuantity;

    @Schema(name = "是否发布", type = "int")
    private Integer isPublish;

    @Schema(name = "使用点点数量", type = "double")
    private Double useDdQua;

    @Schema(name = "任务开始时间", type = "Long")
    private Long startTime;

    @Schema(name = "任务提交时间", type = "Long")
    private Long submitTime;

    @Schema(name = "任务完成时间", type = "Long")
    private Long finishTime;

    @Schema(name = "任务失败原因", type = "String")
    private String failReason;

    @Schema(name = "mj任务信息", type = "String")
    private String finalPrompt;

    @Schema(name = "goapi任务id", type = "String")
    private String goTaskId;

    @Schema(name = "le任务id", type = "String")
    private String leJobId;

    @Schema(name = "mj接口jobid", type = "String")
    private String mjJobId;

    @Schema(name = "mj账号速度", type = "int")
    private Integer mjIsRelaxed;

    @Schema(name = "mj是否休闲模式", type = "int")
    private Integer mjIsCasual;

    @Schema(name = "mj账号id", type = "Long")
    private Long mjAccountId;

    @Schema(name = "sd视频任务id", type = "String")
    private String videoJobId;

    @Schema(name = "个人写真场景ID", type = "Long")
    private Long photoSceneId;

    @Schema(name = "原始图片id（本次操作父级图片id）", type = "Long")
    private Long originalImgId;

    @Schema(name = "操作描述", type = "String")
    private String optDescribe;

    //音频相关参数
    @Schema(name = "音频任务id", type = "String")
    private String audioJobId;

    @Schema(name = "suno账号id", type = "Long")
    private Long sunoAccountId;

    @Schema(name = "音频任务标题", type = "String")
    private String audioTitle;

    @Schema(name = "音频任务歌词", type = "String")
    private String audioLyric;

    @Schema(name = "音频任务风格", type = "String")
    private String audioStyle;

}
