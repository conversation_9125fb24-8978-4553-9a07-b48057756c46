package com.business.config.db;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 公共字段
 *  * 注：此处理器优先级高
 */

@Component("myDetaObjectHander")
public class MyDetaObjectHander implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createTime", Date.class, DateUtil.date());
        this.strictInsertFill(metaObject, "operateTime", Date.class, DateUtil.date());
        this.strictInsertFill(metaObject, "creator", Long.class, 1L);
        this.strictInsertFill(metaObject, "operator", Long.class, 1L);

//        this.setFieldValByName("createTime", DateUtil.date(),metaObject);
//        this.setFieldValByName("operateTime",DateUtil.date(),metaObject);
//        this.setFieldValByName("creator", 1L,metaObject);
//        this.setFieldValByName("operator",1L,metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
//        this.setFieldValByName("operateTime",DateUtil.date(),metaObject);
//        this.setFieldValByName("operator",1L,metaObject);
        this.strictUpdateFill(metaObject, "operateTime", DateTime.class, DateUtil.date());
        this.strictUpdateFill(metaObject, "operator", Long.class, 1L);

    }
}
