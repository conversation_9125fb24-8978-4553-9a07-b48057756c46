package com.business.config;

import cn.hutool.crypto.SecureUtil;
import com.nacos.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

@Slf4j
@Aspect
@Component
public class BAspect {

    //重复提交
    @Around("@annotation(noRepeatSubmit)")
    public Object around(ProceedingJoinPoint point, NoRepeatSubmit noRepeatSubmit) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        // 获取类名和方法名
        String className = method.getDeclaringClass().getName();
        String methodName = method.getName();
        // 组装缓存键
        String key = "用户token#" + className + "#" + methodName;
        String keyHashCode = SecureUtil.md5(key);
        // 获取超时时间
        int timeout = noRepeatSubmit.timeout();
        // 获取操作类和方法名称
        String operationName = "重复操作校验";
        // 使用setIfAbsent方法进行原子性判断和设置，避免并发安全问题
        boolean result = RedisUtil.setIfAbsent(keyHashCode, "1", timeout, TimeUnit.MILLISECONDS);
        if (!result) {
            log.info("重复提交: 类名={}, 方法名={}, 操作名称={}", className, methodName, operationName);
            return "重复提交，稍后重试";
        } else {
            log.info("首次提交: 类名={}, 方法名={}, 操作名称={}", className, methodName, operationName);
        }
        try {
            // 执行被标注的方法
            return point.proceed();
        } catch (Throwable throwable) {
            // 异常处理
            log.error("方法执行出错: 类名={}, 方法名={}, 操作名称={}", className, methodName, operationName, throwable);
            throw throwable;
        }
    }


    private long lastRequestTimestamp = 0;

    @Around("@annotation(avoidRepeatRequest)")
    public Object checkRepeatRequest(ProceedingJoinPoint joinPoint, AvoidRepeatRequest avoidRepeatRequest) throws Throwable {
        long currentTime = System.currentTimeMillis();
        if (avoidRepeatRequest == null) {
            throw new IllegalArgumentException("重复请求注解不存在");
        }
        long intervalTime = avoidRepeatRequest.intervalTime() * 1000; // 转换为毫秒

        if (intervalTime <= 0) {
            throw new IllegalArgumentException("间隔时间必需大于0");
        }
        // 检查是否在最小执行间隔内
        if (currentTime - lastRequestTimestamp < intervalTime) {
            throw new IllegalStateException(avoidRepeatRequest.msg());
        }
        // 更新上一次请求时间戳
        lastRequestTimestamp = currentTime;
        // 执行原始方法
        return joinPoint.proceed();
    }

}
