package com.business.config;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SetCookieReq {

    @Schema(title = "类型（1 suno，2 luma）")
    @JSONField(name = "type")
    private int type;

    @Schema(title = "sessionId")
    @JSONField(name = "sessionId")
    private String sessionId;

    @Schema(title = "cookie")
    @JSONField(name = "cookie")
    private String cookie;

    @Schema(title = "版本号")
    @JSONField(name = "clerkJsVersion")
    private String clerkJsVersion;

}
