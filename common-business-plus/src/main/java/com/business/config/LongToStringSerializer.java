package com.business.config;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.writer.ObjectWriter;

import java.lang.reflect.Type;

public class LongToStringSerializer implements ObjectWriter<Long> {
    public static final LongToStringSerializer instance = new LongToStringSerializer();

    @Override
    public void write(JSONWriter jsonWriter, Object o, Object o1, Type type, long l) {
        if(o==null){
            jsonWriter.writeNull();
        }else{
            String strVal = o.toString();
            jsonWriter.writeString(strVal);
        }
    }
}
