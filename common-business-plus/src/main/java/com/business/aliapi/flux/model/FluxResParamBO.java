package com.business.aliapi.flux.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class FluxResParamBO {

    private String code;

    private String message;

    @JSONField(name = "request_id")
    private String requestId;

    private Output output;

    @Data
    public static class Output{

        @JSONField(name = "task_id")
        private String taskId;

        @JSONField(name = "task_status")
        private String taskStatus;
    }


}
