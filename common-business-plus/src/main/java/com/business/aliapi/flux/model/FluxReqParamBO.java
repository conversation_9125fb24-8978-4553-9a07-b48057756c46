package com.business.aliapi.flux.model;

import lombok.Data;

@Data
public class FluxReqParamBO {

    private String model;
    private Input input;
    private Parameters parameters;

    @Data
    public static class Input{
        private String prompt;

        public Input() {}
        public Input(String prompt) {
            this.prompt = prompt;
        }
    }

    @Data
    public static class Parameters {
        private String size;
        private Integer seed;
        private Integer steps;

        public Parameters() {}
        public Parameters(String size) {
            this.size = size;
        }
    }

    public static FluxReqParamBO initFluxReqParamBO(String model, String prompt, String size) {
        FluxReqParamBO sdToSketchBO = new FluxReqParamBO();
        sdToSketchBO.setModel(model);
        sdToSketchBO.setInput(new Input(prompt));
        sdToSketchBO.setParameters(new Parameters(size));
        return sdToSketchBO;
    }

}
