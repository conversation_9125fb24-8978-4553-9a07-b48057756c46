package com.business.aliapi.flux;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson2.JSONObject;
import com.business.aliapi.flux.model.FluxReqParamBO;
import com.business.aliapi.flux.model.FluxResParamBO;
import com.business.aliapi.flux.model.FluxResParamResultBO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

@Slf4j
public class FluxApiSDKUtil {

    private static final int MAX_RETRIES = 3; // 最大重试次数
    private static final long RETRY_DELAY_MS = 2000; // 重试间隔时间（毫秒）

    private static final String API_KEY = "sk-560ad349cb6a4a7eaeb86194ffc77bb9";
    private static final String GET_TASK_URL = "https://dashscope.aliyuncs.com/api/v1/tasks/";
    private static final String GENERATE_IMAGE_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis";

    private static OkHttpClient client;
    public static OkHttpClient getInstance() {
        if (client == null) {
            client = new OkHttpClient.Builder()
                    .readTimeout(15, TimeUnit.MINUTES)
                    .writeTimeout(15, TimeUnit.MINUTES)
                    .build();
        }
        return client;
    }

    public static FluxResParamBO postFluxTextToImage(FluxReqParamBO fluxReqParamBO) throws IOException {
        String jsonString = JSONObject.toJSONString(fluxReqParamBO);
        log.info("阿里百炼生成图片请求参数= " + jsonString);
        Request request = new Request.Builder()
                .url(FluxApiSDKUtil.GENERATE_IMAGE_URL)
                .post(RequestBody.create(jsonString, MediaType.parse("application/json")))
                .addHeader("X-DashScope-Async", "enable")
                .addHeader("Authorization", "Bearer " + API_KEY)
                .addHeader("Content-Type", "application/json")
                .build();
        OkHttpClient client = getInstance();
        for (int attempt = 0; attempt < MAX_RETRIES; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                log.info("阿里百炼生成图片code= " + response.code());
                assert response.body() != null;
                String responseBody = response.body().string();
                log.info("阿里百炼生成图片responseBody= " + responseBody);
                if (response.isSuccessful()) {
                    return JSONObject.parseObject(responseBody, FluxResParamBO.class);
                }
            } catch (Exception e) {
                log.error("阿里百炼生成图片异常= " + e.getMessage());
                if (attempt < MAX_RETRIES - 1) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
        return null;
    }

    public static FluxResParamResultBO getFluxImageTask(String taskId) throws IOException {
        Request request = new Request.Builder()
                .url(FluxApiSDKUtil.GET_TASK_URL + taskId)
                .addHeader("Authorization", "Bearer " + API_KEY)
                .get()
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            ResponseBody responseBody = response.body();
            log.info("阿里百炼执行code= " + response.code());
            if (response.isSuccessful() && responseBody != null) {
                return JSONObject.parseObject(responseBody.string(), FluxResParamResultBO.class);
            } else if (responseBody != null){
                return JSONObject.parseObject(responseBody.string(), FluxResParamResultBO.class);
            }
        } catch (Exception e) {
            log.error("阿里百炼获取任务异常= " + e.getMessage());
            return null;
        }
        return null;
    }

}
