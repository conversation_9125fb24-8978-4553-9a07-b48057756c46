package com.business.aliapi.flux;

import com.business.aliapi.flux.model.FluxReqParamBO;
import com.business.aliapi.flux.model.FluxResParamBO;
import com.business.aliapi.flux.model.FluxResParamResultBO;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
// FLUX API
@Slf4j
public class FluxApiUtil {


    public static FluxResParamBO postFluxTextToImage(String prompt, String size) throws Exception {
        return FluxApiSDKUtil.postFluxTextToImage(FluxReqParamBO.initFluxReqParamBO("flux-dev", prompt,  size));
    }

    public static FluxResParamResultBO getFluxImageTask(String taskId) throws Exception {
        return FluxApiSDKUtil.getFluxImageTask(taskId);
    }

}
