package com.business.aliapi.flux.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class FluxResParamResultBO {

    @JSONField(name = "request_id")
    private String requestId;

    private Output output;

    @Data
    public static class Output{

        @JSONField(name = "task_id")
        private String taskId;

        @JSONField(name = "task_status")
        private String taskStatus;

        private String code;

        private String message;

        private List<Results> results;

        @Data
        public static class Results{

            private String url;

        }

    }
}
