package com.business.aliapi;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.business.sd.model.SDToImageBO;
import okhttp3.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class AliApiUtil {

    private final static String ALI_API_KEY = "sk-560ad349cb6a4a7eaeb86194ffc77bb9";

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .readTimeout(10, TimeUnit.MINUTES)
            .writeTimeout(10, TimeUnit.MINUTES)
            .build();

    // 检测人脸
    public static String postAliFaceDetection() {
        // 构建JSON请求体
        String jsonInputString = "{\n" +
                "  \"model\": \"facechain-facedetect\",\n" +
                "  \"input\": {\n" +
                "    \"images\": [\n" +
                "      \"http://finetune-swap-wulanchabu.oss-cn-wulanchabu.aliyuncs.com/zhicheng/tmp/1E1D5AFA-3C3A-4B6F-ABD6-8742CA983C42.png\",\n" +
                "      \"http://finetune-swap-wulanchabu.oss-cn-wulanchabu.aliyuncs.com/zhicheng/tmp/3.JPG\",\n" +
                "      \"http://finetune-swap-wulanchabu.oss-cn-wulanchabu.aliyuncs.com/zhicheng/tmp/F2EA3984-6EE2-44CD-928F-109B7276BCB6.png\"\n" +
                "    ]\n" +
                "  },\n" +
                "  \"parameters\": {}\n" +
                "}";

        // 创建RequestBody对象
        RequestBody body = RequestBody.create(jsonInputString, MediaType.parse("application/json"));
        Request request = new Request.Builder()
                .url("https://dashscope.aliyuncs.com/api/v1/services/vision/facedetection/detect")
                .post(body)
                .addHeader("Authorization", "Bearer " + ALI_API_KEY)
                .addHeader("Content-Type", "application/json")
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    // 训练
    public static String postAliToFineTunes(List<String> trainingFileUrls) {
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("model", "facechain-finetune");
        jsonBody.put("training_file_ids", trainingFileUrls);

        System.out.println(jsonBody.toString());
        RequestBody body = RequestBody.create(jsonBody.toString(), MediaType.parse("application/json"));
        Request request = new Request.Builder()
                .url("https://dashscope.aliyuncs.com/api/v1/fine-tunes")
                .post(body)
                .addHeader("Authorization", "Bearer " + ALI_API_KEY)
                .addHeader("Content-Type", "application/json")
                .build();
        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            System.out.println(responseBody);
            if (response.isSuccessful()) {
                return responseBody;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // 获取训练结果
    public static String getAliToFineTunesJob(String fineTuneId) {
        Request request = new Request.Builder()
                .url("https://dashscope.aliyuncs.com/api/v1/fine-tunes/" + fineTuneId)
                .get()
                .addHeader("Authorization", "Bearer " + ALI_API_KEY)
                .addHeader("Content-Type", "application/json")
                .build();
        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }



    // 写真
    public static String postAliToGenPotrait(String templatePicUrl, String userPicUrl, String resourceId) {
        // 构建JSON请求体
        String jsonInputString = "{\n" +
                "    \"model\": \"facechain-generation\",\n" +
                "    \"parameters\": {\n" +
                "        \"style\": \"train_free_portrait_url_template\",\n" +
                "        \"n\": 1\n" +
                "    },\n" +
                "    \"input\": {\n" +
                "        \"template_url\": \"" + templatePicUrl + "\",\n" +
                "        \"user_urls\": [\"" + userPicUrl + "\"]\n" +
                "    }\n" +
                "}";

        // 构建JSON请求体
        String jsonInputString1 = "{\n" +
                "    \"model\": \"facechain-generation\",\n" +
                "    \"parameters\": {\n" +
                "        \"style\": \"f_hongkongvintage_female\",\n" +
                "        \"size\": \"768*1024\",\n" +
                "        \"n\": 1\n" +
                "    },\n" +
                "    \"resources\": [\n" +
                "        {\n" +
                "            \"resource_id\": \""+ resourceId +"\",\n" +
                "            \"resource_type\": \"facelora\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        // 构建JSON请求体
        String jsonInputString2 = "{\n" +
                "    \"model\": \"facechain-generation\",\n" +
                "    \"parameters\": {\n" +
                "        \"style\": \"portrait_url_template\",\n" +
                "        \"n\": 1\n" +
                "    },\n" +
                "    \"input\": {\n" +
                "        \"template_url\": \"" + templatePicUrl + "\"\n" +
                "    },\n" +
                "    \"resources\": [\n" +
                "        {\n" +
                "            \"resource_id\": \""+ resourceId +"\",\n" +
                "            \"resource_type\": \"facelora\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";


        // 创建RequestBody对象
        RequestBody body = RequestBody.create(jsonInputString2, MediaType.parse("application/json"));
        Request request = new Request.Builder()
                .url("https://dashscope.aliyuncs.com/api/v1/services/aigc/album/gen_potrait")
                .post(body)
                .addHeader("Authorization", "Bearer " + ALI_API_KEY)
                .addHeader("X-DashScope-Async", "enable")
                .addHeader("Content-Type", "application/json")
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    // 获取结果
    public static String getAliTasksResult(String taskId) {
        Request request = new Request.Builder()
                .url("https://dashscope.aliyuncs.com/api/v1/tasks/" + taskId)
                .get()
                .addHeader("Authorization", "Bearer " + ALI_API_KEY)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage());
        }
        return null;
    }

}
