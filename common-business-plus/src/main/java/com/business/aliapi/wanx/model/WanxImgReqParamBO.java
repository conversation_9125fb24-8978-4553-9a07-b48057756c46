package com.business.aliapi.wanx.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "万相视频图生视频-首帧请求参数")
public class WanxImgReqParamBO {

    @Schema(description = "模型名称", example = "wanx2.1-t2v-turbo")
    @JSONField(name = "model")
    private String model;

    @Schema(description = "输入参数")
    @JSONField(name = "input")
    private Input input;

    @Schema(description = "视频处理参数")
    @JSONField(name = "parameters")
    private Parameters parameters;

    @Data
    @Schema(description = "输入参数")
    public static class Input {

        @Schema(description = "文本提示词，支持中英文，长度不超过800个字符", required = true)
        @JSONField(name = "prompt")
        private String prompt;

        @Schema(description = "图片URL", required = true)
        @JSONField(name = "img_url")
        private String imgUrl;

        public Input(String prompt, String imgUrl) {
            this.prompt = prompt;
            this.imgUrl = imgUrl;
        }

    }

    @Data
    @Schema(description = "视频处理参数")
    public static class Parameters {

        @Schema(description = "是否开启prompt智能改写，默认开启")
        @JSONField(name = "prompt_extend")
        private Boolean promptExtend;

        @Schema(description = "生成视频的时长，默认5秒，当前仅支持5秒固定时长生成")
        @JSONField(name = "duration")
        private Integer duration;

        public Parameters() {
            this.promptExtend = true;
            this.duration = 5;
        }

    }

    public WanxImgReqParamBO(String model, String prompt, String imgUrl) {
        this.model = (model == null || model.isEmpty()) ? "wanx2.1-t2v-turbo" : model;
        this.input = new Input(prompt, imgUrl);
        this.parameters = new Parameters();
    }

}
