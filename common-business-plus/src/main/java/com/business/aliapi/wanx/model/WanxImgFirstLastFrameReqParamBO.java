package com.business.aliapi.wanx.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "万相视频首尾帧生视频请求参数")
public class WanxImgFirstLastFrameReqParamBO {

    @Schema(description = "模型名称", example = "wanx2.1-kf2v-plus")
    @JSONField(name = "model")
    private String model;

    @Schema(description = "输入的基本信息")
    @JSONField(name = "input")
    private Input input;

    @Schema(description = "视频处理参数")
    @JSONField(name = "parameters")
    private Parameters parameters;

    @Data
    @Schema(description = "输入参数")
    public static class Input {
        
        @Schema(description = "文本提示词，支持中英文，长度不超过800个字符", required = true)
        @JSONField(name = "prompt")
        private String prompt;
        
        @Schema(description = "首帧图片URL", required = true)
        @JSONField(name = "first_frame_url")
        private String firstFrameUrl;
        
        @Schema(description = "尾帧图片URL", required = true)
        @JSONField(name = "last_frame_url")
        private String lastFrameUrl;

        // 构造方法
        public Input(String prompt, String firstFrameUrl, String lastFrameUrl) {
            this.prompt = prompt;
            this.firstFrameUrl = firstFrameUrl;
            this.lastFrameUrl = lastFrameUrl;
        }
    }

    @Data
    @Schema(description = "视频处理参数")
    public static class Parameters {
        
        @Schema(description = "生成视频的分辨率档位，默认720P，当前仅支持720P")
        @JSONField(name = "resolution")
        private String resolution = "720P";
        
        @Schema(description = "生成视频的时长，默认5秒，当前仅支持5秒固定时长生成")
        @JSONField(name = "duration")
        private Integer duration = 5;
        
        @Schema(description = "是否开启prompt智能改写，默认开启")
        @JSONField(name = "prompt_extend")
        private Boolean promptExtend = true;
        
        @Schema(description = "随机数种子，用于控制模型生成内容的随机性，取值范围[0, 2147483647]")
        @JSONField(name = "seed")
        private Integer seed;

    }

    // 构造方法
    public WanxImgFirstLastFrameReqParamBO(String model, String prompt, String firstFrameUrl, String lastFrameUrl) {
        this.model = model;
        this.input = new Input(prompt, firstFrameUrl, lastFrameUrl);
    }
} 