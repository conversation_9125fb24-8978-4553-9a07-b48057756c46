package com.business.aliapi.wanx.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "万相视频生成响应参数")
public class WanxResParamBO {

    @Schema(description = "响应码")
    @JSONField(name = "code")
    private String code;

    @Schema(description = "响应消息")
    @JSONField(name = "message")
    private String message;

    @Schema(description = "请求ID")
    @JSONField(name = "request_id")
    private String requestId;

    @Schema(description = "输出结果")
    @JSONField(name = "output")
    private Output output;

    @Data
    @Schema(description = "输出结果")
    public static class Output {

        @Schema(description = "任务ID")
        @JSONField(name = "task_id")
        private String taskId;

        @Schema(description = "任务状态")
        @JSONField(name = "task_status")
        private String taskStatus;
    }


}
