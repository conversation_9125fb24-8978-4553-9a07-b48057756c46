package com.business.aliapi.wanx.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "万相视频文生视频请求参数")
public class WanxReqParamBO {

    @Schema(description = "模型名称", example = "wanx2.1-t2v-turbo")
    @JSONField(name = "model")
    private String model;

    @Schema(description = "输入参数")
    @JSONField(name = "input")
    private Input input;

    @Schema(description = "视频处理参数")
    @JSONField(name = "parameters")
    private Parameters parameters;

    @Data
    @Schema(description = "输入参数")
    public static class Input {

        @Schema(description = "文本提示词，支持中英文，长度不超过800个字符", required = true)
        @JSONField(name = "prompt")
        private String prompt;

        @Schema(description = "是否开启prompt智能改写，默认开启")
        @JSONField(name = "prompt_extend")
        private Boolean promptExtend;

        public Input(String prompt) {
            this.prompt = prompt;
            this.promptExtend = true;
        }

    }

    @Data
    @Schema(description = "视频处理参数")
    public static class Parameters {

        @Schema(description = "生成视频的分辨率档位，默认720P，当前仅支持720P")
        @JSONField(name = "size")
        private String size;

        @Schema(description = "生成视频的时长，默认5秒，当前仅支持5秒固定时长生成")
        @JSONField(name = "duration")
        private Integer duration;

        public Parameters(String size) {
            this.size = size;
            this.duration = 5;
        }

    }

    public WanxReqParamBO(String model, String prompt, String size) {
        this.model = (model == null || model.isEmpty()) ? "wanx2.1-t2v-turbo" : model;
        this.input = new Input(prompt);
        this.parameters = new Parameters(size);
    }

}
