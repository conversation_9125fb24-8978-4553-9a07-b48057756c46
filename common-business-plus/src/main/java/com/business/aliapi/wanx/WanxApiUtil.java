package com.business.aliapi.wanx;

import com.alibaba.fastjson.JSON;
import com.business.aigc.tusiart.TusiJobStateEnum;
import com.business.aliapi.wanx.model.*;
import com.business.db.model.po.ImgDrawDetlPO;
import com.business.model.po.ImgDrawRecordPO;
import com.business.utils.BOssUtil;
import com.nacos.config.OssClientConfig;
import com.nacos.utils.BFeiShuUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 阿里视频生成ApiUtil
 */
@Slf4j
public class WanxApiUtil {

    public static final String STATUS_PENDING = "PENDING"; // 任务排队中
    public static final String STATUS_RUNNING = "RUNNING"; // 任务处理中
    public static final String STATUS_SUSPENDED = "SUSPENDED"; // 任务挂起
    public static final String STATUS_SUCCEEDED = "SUCCEEDED"; // 任务执行成功
    public static final String STATUS_FAILED = "FAILED"; // 任务执行失败
    public static final String STATUS_UNKNOWN = "UNKNOWN"; // 任务不存在或状态未知


    /**
     * 万相视频文生视频
     * @param wanxReqParamBO 请求参数
     * @param wanxImgReqParamBO 请求参数
     * @param apiKey 令牌
     * @return 万相视频文生视频结果
     */
    public static String postWanxVideoGenerations(WanxReqParamBO wanxReqParamBO, WanxImgReqParamBO wanxImgReqParamBO, WanxImgFirstLastFrameReqParamBO wanxImgFirstLastFrameReqParamBO, String apiKey) throws Exception {
        WanxResParamBO wanxResParamBO = null;
        // 文生视频
        if (wanxReqParamBO != null && wanxImgReqParamBO == null) {
            wanxResParamBO = WanxApi.postWanxTextToVideoGenerations(wanxReqParamBO, apiKey);
        }
        // 图生视频-首帧
        else if (wanxReqParamBO == null && wanxImgReqParamBO != null) {
            wanxResParamBO = WanxApi.postWanxImageToVideoGenerations(wanxImgReqParamBO);
        }
        // 图生视频-首尾帧
        else if (wanxReqParamBO == null && wanxImgReqParamBO == null && wanxImgFirstLastFrameReqParamBO != null) {
            wanxResParamBO = WanxApi.postWanxImageToVideoGenerationsByFirstAndLastFrame(wanxImgFirstLastFrameReqParamBO);
        }
        // 判断是否成功
        if (wanxResParamBO == null || wanxResParamBO.getOutput() == null) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "万相境视频提交失败", "postWanxTextToVideoGenerations.resp " + JSON.toJSONString(wanxResParamBO) + " prompt= " + wanxReqParamBO.getInput().getPrompt());
            return null;
        }
        if (wanxResParamBO.getOutput().getTaskId() != null) {
            return wanxResParamBO.getOutput().getTaskId();
        }
        return null;
    }

    /**
     * 万相视频任务结果
     * @param taskId 任务ID
     * @param apiKey 令牌
     * @param imgDrawRecordPO 图片绘制记录
     * @param imgDrawDetlPO 图片绘制详情
     * @return 万相视频任务结果
     */
    public static String getWanxVideoTaskResult(String taskId, String apiKey, ImgDrawRecordPO imgDrawRecordPO, ImgDrawDetlPO imgDrawDetlPO) throws Exception {
        WanxResTaskParamBO wanxResTaskParamBO = WanxApi.getWanxVideoTaskResult(taskId, apiKey);
        if (wanxResTaskParamBO == null || wanxResTaskParamBO.getOutput() == null) {
            return TusiJobStateEnum.FAILED.getState();
        }
        if (Objects.equals(wanxResTaskParamBO.getOutput().getTaskStatus(), STATUS_PENDING)
                || Objects.equals(wanxResTaskParamBO.getOutput().getTaskStatus(), STATUS_RUNNING)
                || Objects.equals(wanxResTaskParamBO.getOutput().getTaskStatus(), STATUS_SUSPENDED)) {
            return TusiJobStateEnum.RUNNING.getState();
        }
        if (Objects.equals(wanxResTaskParamBO.getOutput().getTaskStatus(), STATUS_FAILED)
                || Objects.equals(wanxResTaskParamBO.getOutput().getTaskStatus(), STATUS_UNKNOWN)) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "蒙太奇视频拉取任务业务失败", "getWanxVideoTaskResult.resp " + JSON.toJSONString(wanxResTaskParamBO) + " jobId=" + taskId + " prompt= " + imgDrawRecordPO.getPromptInit());
            return TusiJobStateEnum.FAILED.getState();
        }
        if (Objects.equals(wanxResTaskParamBO.getOutput().getTaskStatus(), STATUS_SUCCEEDED)) {
            String videoUrl = wanxResTaskParamBO.getOutput().getVideoUrl();
            imgDrawDetlPO.setAudioUrl(videoUrl);
            log.info("万相视频原地址= " + videoUrl);
            return uploadLumaVideo(videoUrl, taskId, 14, OssClientConfig.FILE_SUFFIX_VIDEO);
        }
        return TusiJobStateEnum.FAILED.getState();
    }

    /**
     * 上传视频到oss
     * @param fileUrl 视频地址
     * @param videoJobId 视频任务ID
     * @param folder 文件夹
     * @param suffix 文件后缀
     * @return 视频路径
     */
    // TODO 上传视频到oss
    public static String uploadLumaVideo(String fileUrl, String videoJobId, Integer folder, String suffix) {
        if (fileUrl == null) return null;
        int maxRetries = 6; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadURL(fileUrl, videoJobId, folder, suffix);
                if (videoPath != null) {
                    return videoPath;
                }
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                }
                log.error("万相视频上传失败，重试次数：" + (retryCount + 1) + " videoJobId=" + videoJobId);

            } catch (Exception e) {
                log.error("万相视频上传视频到OSS发生异常，重试次数：" + (retryCount + 1));
            }
            retryCount++;
        }
        log.error("万相视频文件上传失败，超过最大重试次数， 视频id=" + videoJobId);
        return null;
    }


    /**
     * 万相视频图像编辑
     * @param imgEraseReqParamBO 请求参数
     * @return 万相视频图像编辑结果
     */
    public static String postImageToImageSynthesis(ImgEraseReqParamBO imgEraseReqParamBO) throws Exception {
        WanxResParamBO wanxResParamBO = WanxApi.postImageToImageSynthesis(imgEraseReqParamBO);
        if (wanxResParamBO == null || wanxResParamBO.getOutput() == null) {
            return null;
        }
        if (wanxResParamBO.getOutput().getTaskId() != null) {
            return wanxResParamBO.getOutput().getTaskId();
        }
        return null;
    }

}
