package com.business.aliapi.wanx.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "万相视频生成任务结果响应参数")
public class WanxResTaskParamBO {

    @Schema(description = "输出结果")
    @JSONField(name = "output")
    private OutputDTO output;

    @Schema(description = "提交时间")
    @JSONField(name = "submit_time")
    private String submitTime;

    @Schema(description = "调度时间")
    @JSONField(name = "scheduled_time")
    private String scheduledTime;

    @Schema(description = "结束时间")
    @JSONField(name = "end_time")
    private String endTime;

    @Schema(description = "视频URL")
    @JSONField(name = "video_url")
    private String videoUrl;

    @Schema(description = "请求失败的错误码")
    @JSONField(name = "code")
    private String code;

    @Schema(description = "请求失败的详细信息")
    @JSONField(name = "message")
    private String message;

    @Schema(description = "使用情况")
    @JSONField(name = "usage")
    private UsageDTO usage;

    @Schema(description = "请求ID")
    @JSONField(name = "request_id")
    private String requestId;

    @Data
    @Schema(description = "输出结果")
    public static class OutputDTO {

        @Schema(description = "任务ID")
        @JSONField(name = "task_id")
        private String taskId;

        @Schema(description = "任务状态")
        @JSONField(name = "task_status")
        private String taskStatus;

        @Schema(description = "提交时间")
        @JSONField(name = "submit_time")
        private String submitTime;

        @Schema(description = "调度时间")
        @JSONField(name = "scheduled_time")
        private String scheduledTime;

        @Schema(description = "结束时间")
        @JSONField(name = "end_time")
        private String endTime;

        @Schema(description = "视频URL")
        @JSONField(name = "video_url")
        private String videoUrl;

        @Schema(description = "输出图片URL")
        @JSONField(name = "output_image_url")
        private String outputImageUrl;

    }

    @Data
    @Schema(description = "使用情况")
    public static class UsageDTO {

        @Schema(description = "视频时长")
        @JSONField(name = "video_duration")
        private Integer videoDuration;

        @Schema(description = "视频比例")
        @JSONField(name = "video_ratio")
        private String videoRatio;

        @Schema(description = "视频数量")
        @JSONField(name = "video_count")
        private Integer videoCount;

    }

}
