package com.business.aliapi.wanx.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "万相视频图像编辑请求参数")
public class ImgEraseReqParamBO {

    @Schema(title = "模型")
    @JSONField(name = "model")
    private String model;

    @Schema(title = "输入")
    @JSONField(name = "input")
    private Input input;

    @Schema(title = "参数")
    @JSONField(name = "parameters")
    private Parameters parameters;

    @Data
    @Schema(title = "输入")
    public static class Input {

        @Schema(title = "图片URL")
        @JSONField(name = "image_url")
        private String imageUrl;

        @Schema(title = "蒙版URL")
        @JSONField(name = "mask_url")
        private String maskUrl;

        @Schema(title = "前景URL")
        @JSONField(name = "foreground_url")
        private String foregroundUrl;

        public Input(String imageUrl, String maskUrl) {
            this.imageUrl = imageUrl;
            this.maskUrl = maskUrl;
        }
    }

    @Data
    @Schema(title = "参数")
    public static class Parameters {

        @Schema(title = "是否快速模式")
        @JSONField(name = "fast_mode")
        private Boolean fastMode;

        @Schema(title = "是否膨胀")
        @JSONField(name = "dilate_flag")
        private Boolean dilateFlag;

        @Schema(title = "是否添加水印")
        @JSONField(name = "add_watermark")
        private Boolean addWatermark;

        public Parameters() {
            this.fastMode = true;
            this.dilateFlag = false;
            this.addWatermark = false;
        }

    }

    public ImgEraseReqParamBO(String prompt, String imgUrl) {
        this.model = "image-erase-completion";
        this.input = new Input(prompt, imgUrl);
        this.parameters = new Parameters();
    }

}
