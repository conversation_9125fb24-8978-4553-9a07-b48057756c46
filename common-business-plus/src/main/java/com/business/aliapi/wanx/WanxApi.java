package com.business.aliapi.wanx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.business.aliapi.wanx.model.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

import java.util.concurrent.TimeUnit;

/**
 * 阿里视频生成API
 */
@Slf4j
public class WanxApi {

    private static final String API_KEY_URL = "https://dashscope.aliyuncs.com";
    private static final String API_KEY = "sk-6079996f48024bffaa395a5751fe6a1b";

    private static OkHttpClient client;

    public static OkHttpClient getInstance() {
        if (client == null) {
            client = new OkHttpClient.Builder()
                    .readTimeout(15, TimeUnit.MINUTES)
                    .writeTimeout(15, TimeUnit.MINUTES)
                    .build();
        }
        return client;
    }

    /**
     * 万相视频文生视频
     * @param wanxReqParamBO 请求参数
     * @param apiKey 令牌
     * @return 万相视频文生视频结果
     */
    public static WanxResParamBO postWanxTextToVideoGenerations(WanxReqParamBO wanxReqParamBO, String apiKey) {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(wanxReqParamBO, JSONWriter.Feature.WriteMapNullValue));
        Request request = new Request.Builder()
                .url(API_KEY_URL + "/api/v1/services/aigc/video-generation/video-synthesis")
                .post(RequestBody.create(jsonObject.toString(), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + (apiKey == null || apiKey.isEmpty() ? API_KEY : apiKey))
                .addHeader("X-DashScope-Async", "enable")
                .build();
        getInstance();
        try (var response = client.newCall(request).execute()) {
            String responseBody = null;
            WanxResParamBO wanxResParamBO = null;
            if (response.body() != null) {
                responseBody = response.body().string();
                log.info("【万相视频文生视频】Wanx_Post_responseBody:{}", responseBody);
                wanxResParamBO = JSON.parseObject(responseBody, new TypeReference<WanxResParamBO>() {
                });
            }
            if (responseBody != null) {
                return wanxResParamBO;
            }
        } catch (Exception e) {
            log.error("【万相视频文生视频】请求失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    /**
     * 获取万相视频任务结果
     * @param taskId 任务ID
     * @param apiKey 令牌
     * @return 万相视频任务结果
     */
    public static WanxResTaskParamBO getWanxVideoTaskResult(String taskId, String apiKey) {
        Request request = new Request.Builder()
                .url(API_KEY_URL + "/api/v1/tasks/" + taskId)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + (apiKey == null || apiKey.isEmpty() ? API_KEY : apiKey))
                .get()
                .build();
        getInstance();
        try (var response = client.newCall(request).execute()) {
            String responseBody = null;
            WanxResTaskParamBO wanxResTaskParamBO = null;
            if (response.body() != null) {
                responseBody = response.body().string();
                log.info("【万相视频任务结果】Wanx_Get_Task_responseBody:{}", responseBody);
                wanxResTaskParamBO = JSON.parseObject(responseBody, new TypeReference<WanxResTaskParamBO>() {
                });
            }
            if (responseBody != null) {
                return wanxResTaskParamBO;
            }
        } catch (Exception e) {
            log.error("【万相视频任务结果】获取失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    /**
     * 万相视频图生视频-基于首帧
     * @param wanxImgReqParamBO 请求参数
     * @return 万相视频图生视频结果
     */
    public static WanxResParamBO postWanxImageToVideoGenerations(WanxImgReqParamBO wanxImgReqParamBO) {
        // 写定模型
        wanxImgReqParamBO.setModel("wanx2.1-i2v-turbo");
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(wanxImgReqParamBO, JSONWriter.Feature.WriteMapNullValue));
        Request request = new Request.Builder()
                .url(API_KEY_URL + "/api/v1/services/aigc/video-generation/video-synthesis")
                .post(RequestBody.create(jsonObject.toString(), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("X-DashScope-Async", "enable")
                .addHeader("Authorization", "Bearer " + API_KEY)
                .addHeader("Content-Type", "application/json")
                .build();
        getInstance();
        // log.info("【万相视频图生视频-基于首帧】Wanx_Post_requestBody:{}", jsonObject);
        try (var response = client.newCall(request).execute()) {
            String responseBody = null;
            WanxResParamBO wanxResParamBO = null;
            if (response.body() != null) {
                responseBody = response.body().string();
                log.info("【万相视频图生视频-基于首帧】Wanx_Post_responseBody:{}", responseBody);
                wanxResParamBO = JSON.parseObject(responseBody, new TypeReference<WanxResParamBO>() {
                });
            }
            if (responseBody != null) {
                return wanxResParamBO;
            }
        } catch (Exception e) {
            log.error("【万相视频图生视频-基于首帧】请求失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    /**
     * 万相视频图生视频-基于首尾帧
     * @param wanxImgReqParamBO 请求参数
     * @return 万相视频图生视频结果
     */
    public static WanxResParamBO postWanxImageToVideoGenerationsByFirstAndLastFrame(WanxImgFirstLastFrameReqParamBO wanxImgFirstLastFrameReqParamBO) {
        // 写定模型
        wanxImgFirstLastFrameReqParamBO.setModel("wanx2.1-kf2v-plus");
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(wanxImgFirstLastFrameReqParamBO, JSONWriter.Feature.WriteMapNullValue));
        Request request = new Request.Builder()
                .url(API_KEY_URL + "/api/v1/services/aigc/image2video/video-synthesis")
                .post(RequestBody.create(jsonObject.toString(), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("X-DashScope-Async", "enable")
                .addHeader("Authorization", "Bearer " + API_KEY)
                .addHeader("Content-Type", "application/json")
                .build();
        getInstance();
        try (var response = client.newCall(request).execute()) {
            String responseBody = null;
            WanxResParamBO wanxResParamBO = null;
            if (response.body() != null) {
                responseBody = response.body().string();
                log.info("【万相视频图生视频-基于首尾帧】Wanx_Post_responseBody:{}", responseBody);
                wanxResParamBO = JSON.parseObject(responseBody, new TypeReference<WanxResParamBO>() {
                });
            }
            if (responseBody != null) {
                return wanxResParamBO;
            }
        } catch (Exception e) {
            log.error("【万相视频图生视频-基于首尾帧】请求失败：{}", e.getMessage());
            return null;
        }
        return null;
    }

    /**
     * 万相视频图像编辑
     * @param imgEraseReqParamBO 请求参数
     * @return 万相视频图像编辑结果
     */
    public static WanxResParamBO postImageToImageSynthesis(ImgEraseReqParamBO imgEraseReqParamBO) {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(imgEraseReqParamBO, JSONWriter.Feature.WriteMapNullValue));
        Request request = new Request.Builder()
                .url(API_KEY_URL + "/api/v1/services/aigc/image2image/image-synthesis")
                .post(RequestBody.create(jsonObject.toString(), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("X-DashScope-Async", "enable")
                .addHeader("Authorization", "Bearer " + API_KEY)
                .addHeader("Content-Type", "application/json")
                .build();
        getInstance();
        try (var response = client.newCall(request).execute()) {
            String responseBody = null;
            WanxResParamBO wanxResParamBO = null;
            if (response.body() != null) {
                responseBody = response.body().string();
                log.info("postImageToImageSynthesis：{}", responseBody);
                wanxResParamBO = JSON.parseObject(responseBody, new TypeReference<WanxResParamBO>() {
                });
            }
            if (responseBody != null) {
                return wanxResParamBO;
            }
        } catch (Exception e) {
            log.error("【万相视频图像编辑】请求失败：{}", e.getMessage());
            return null;
        }
        return null;
    }


}
