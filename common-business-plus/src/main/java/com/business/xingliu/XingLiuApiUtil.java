package com.business.xingliu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.business.aigc.tusiart.TusiJobStateEnum;
import com.business.xingliu.enums.XingLiuEnums;
import com.business.xingliu.model.XingLiuRequestBO;
import com.business.xingliu.model.XingLiuResponseBO;
import com.nacos.utils.BFeiShuUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Schema(title = "智谱视频 api工具类")
public class XingLiuApiUtil {

    public static final int STATUS_WAITING_EXECUTION = 1;
    public static final int STATUS_EXECUTING = 2;
    public static final int STATUS_IMAGE_GENERATED = 3;
    public static final int STATUS_UNDER_REVIEW = 4;
    public static final int STATUS_SUCCESS = 5;// 完成
    public static final int STATUS_FAIL = 6; // 失败


    /**
     * 智谱视频生成
     */
    public static String posXingLiuTextToImageGenerate(XingLiuRequestBO xingLiuRequestBO, String accessKey, String secretKey) {
        String responseBody = XingLiuApis.posXingLiuTextToImageGenerate(xingLiuRequestBO, accessKey, secretKey);
        if (responseBody == null) {
            return null;
        }
        JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(responseBody);
        int code = jsonObject.getIntValue("code");
        if (code == 0) {
            JSONObject dataObject = jsonObject.getJSONObject("data");
            return dataObject.getString("generateUuid");
        }
        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "星流AI生成图片失败：" + XingLiuEnums.getXingLiuGenerateImgErrorMessage(code),
                JSON.toJSONString(xingLiuRequestBO) + "； 错误请求信息： " + responseBody);
        return null;
    }

    /**
     * 智谱视频生成
     * 
     * @param xingLiuRequestBO 请求参数
     * @param accessKey        访问密钥
     * @param secretKey        密钥
     * @return 任务ID
     */
    public static String posXingLiuImgToImageGenerate(XingLiuRequestBO xingLiuRequestBO, String accessKey, String secretKey) {
        String responseBody = XingLiuApis.posXingLiuImageToImageGenerate(xingLiuRequestBO, accessKey, secretKey);
        if (responseBody == null) {
            return null;
        }
        JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(responseBody);
        int code = jsonObject.getIntValue("code");
        if (code == 0) {
            JSONObject dataObject = jsonObject.getJSONObject("data");
            return dataObject.getString("generateUuid");
        }
        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "星流AI生成图片失败：" + XingLiuEnums.getXingLiuGenerateImgErrorMessage(code),
                JSON.toJSONString(xingLiuRequestBO) + "； 错误请求信息： " + responseBody);
        return null;
    }

    /**
     * 获取智谱视频生成任务结果
     * 
     * @param taskId  任务ID
     * @param accessKey 访问密钥
     * @param secretKey 密钥
     * @return 任务结果
     */
    public static String getXingLiuTextToImageTaskResult(String taskId, String accessKey, String secretKey) {
        List<String> imgUrls = new ArrayList<>();
        XingLiuResponseBO xingLiuResponseBO = XingLiuApis.getXingLiuTextToImageTaskResult(taskId, accessKey, secretKey);
        if (xingLiuResponseBO == null || xingLiuResponseBO.getGenerateStatus() == STATUS_FAIL) {
            return TusiJobStateEnum.FAILED.getState();
        }
        if (!Objects.equals(xingLiuResponseBO.getGenerateStatus(), STATUS_SUCCESS)) {
            return TusiJobStateEnum.RUNNING.getState();
        } else {
            List<XingLiuResponseBO.ImagesBO> images = xingLiuResponseBO.getImages();
            for (XingLiuResponseBO.ImagesBO image : images) {
                if (image.getAuditStatus() == 3) {
                    imgUrls.add(image.getImageUrl());
                } else {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "星野视频拉取任务失败", "图片已生成审核不通过，jobId=" + taskId);
                }
            }
            return String.join(",", imgUrls);
        }
    }


}
