package com.business.xingliu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.business.xingliu.enums.XingLiuEnums;
import com.business.xingliu.model.XingLiuRequestBO;
import com.business.xingliu.model.XingLiuResponseBO;
import com.nacos.tool.BrotliInterceptor;
import com.nacos.utils.BFeiShuUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.RandomStringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;


@Slf4j
@Schema(title = "星流视频Api")
public class XingLiuApis {

    private final static String XINGLIU_API_URL = "https://openapi.liblibai.cloud";
    private final static String ACCESS_KEY = "SsS1haLwPrLrT88CrTxxkQ";
    private final static String SECRET_KEY = "Zggl6JlOO0pABGqE_50CQJrSvph3HxVp";

    @Getter
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    public static String posXingLiuTextToImageGenerate(XingLiuRequestBO xingLiuRequestBO, String accessKey, String secretKey) {
        log.error("星流AI请求参数：{}", JSONObject.toJSONString(xingLiuRequestBO));
        System.out.println(JSONObject.toJSONString(xingLiuRequestBO));
        Long timestamp = System.currentTimeMillis();
        String signatureNonce = RandomStringUtils.randomAlphanumeric(10);
        String signature = starMakeSign((secretKey == null ? SECRET_KEY : secretKey), "/api/generate/webui/text2img/ultra", signatureNonce, timestamp);
        String stat3Url = XINGLIU_API_URL + "/api/generate/webui/text2img/ultra?AccessKey=" + (accessKey == null ? ACCESS_KEY : accessKey) +
                "&Signature=" + signature +
                "&Timestamp=" + timestamp +
                "&SignatureNonce=" + signatureNonce;
        Request request = new Request.Builder()
                .url(stat3Url)
                .post(RequestBody.create(JSONObject.toJSONString(xingLiuRequestBO), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("Content-Type", "application/json")
                .build();
        try (var response = client.newCall(request).execute()) {
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("星流AI请求返回：{}", responseBody);
            if (response.isSuccessful()) {
                return responseBody;
            }
        } catch (Exception e) {
            log.error("获取星流任务提交失败 {}：", e.getMessage());
            return null;
        }
        return null;
    }

    public static String posXingLiuImageToImageGenerate(XingLiuRequestBO xingLiuRequestBO, String accessKey, String secretKey) {
        log.error("星流AI请求参数：{}", JSONObject.toJSONString(xingLiuRequestBO));
        Long timestamp = System.currentTimeMillis();
        String signatureNonce = RandomStringUtils.randomAlphanumeric(10);
        String signature = starMakeSign((secretKey == null ? SECRET_KEY : secretKey), "/api/generate/webui/img2img/ultra", signatureNonce, timestamp);
        String stat3Url = XINGLIU_API_URL + "/api/generate/webui/img2img/ultra?AccessKey=" + (accessKey == null ? ACCESS_KEY : accessKey) +
                "&Signature=" + signature +
                "&Timestamp=" + timestamp +
                "&SignatureNonce=" + signatureNonce;
        Request request = new Request.Builder()
                .url(stat3Url)
                .post(RequestBody.create(JSONObject.toJSONString(xingLiuRequestBO), MediaType.parse("application/json; charset=utf-8")))
                .addHeader("Content-Type", "application/json")
                .build();
        try (var response = client.newCall(request).execute()) {
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("星流AI请求返回：{}", responseBody);
            if (response.isSuccessful()) {
                return responseBody;
            }
        } catch (Exception e) {
            log.error("获取星流任务提交失败 {}：", e.getMessage());
            return null;
        }
        return null;
    }

    public static XingLiuResponseBO getXingLiuTextToImageTaskResult(String taskId, String accessKey, String secretKey) {
        Long timestamp = System.currentTimeMillis();
        String signatureNonce = RandomStringUtils.randomAlphanumeric(10);
        String signature = starMakeSign((secretKey == null ? SECRET_KEY : secretKey), "/api/generate/webui/status", signatureNonce, timestamp);
        String stat3Url = XINGLIU_API_URL + "/api/generate/webui/status?AccessKey=" + (accessKey == null ? ACCESS_KEY : accessKey) +
                "&Signature=" + signature +
                "&Timestamp=" + timestamp +
                "&SignatureNonce=" + signatureNonce;
        Request request = new Request.Builder()
                .url(stat3Url)
                .post(RequestBody.create("{\"generateUuid\":\"" + taskId + "\"}",
                        MediaType.parse("application/json; charset=utf-8")))
                .addHeader("Content-Type", "application/json")
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.info("getXingLiuTextToImageTaskResult:{}", response.code());
            if (response.code() == 200 && response.body() != null) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                int code = jsonObject.getInteger("code");
                if (code == 0) {
                    JSONObject dataObject = jsonObject.getJSONObject("data");
                    String dataString = dataObject.toJSONString();
                    System.out.println(dataString);
                    return JSON.parseObject(dataString, new TypeReference<XingLiuResponseBO>() {
                    });
                } else {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "星流AI获取任务结果失败：" + XingLiuEnums.getXingLiuGenerateImgErrorMessage(code), "任务id=" + taskId);
                }
            }
        } catch (Exception e) {
            log.error("获取星流任务失败 {}：", e.getMessage());
            return null;
        }
        return null;
    }

    public static String starMakeSign(String secretKey, String uri, String signatureNonce, Long timestamp) {
        String content = uri + "&" + timestamp + "&" + signatureNonce;
        try {
            SecretKeySpec secret = new SecretKeySpec(secretKey.getBytes(), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(secret);
            return Base64.encodeBase64URLSafeString(mac.doFinal(content.getBytes()));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("no such algorithm");
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e);
        }
    }

}
