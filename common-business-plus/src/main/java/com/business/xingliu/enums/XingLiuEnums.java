package com.business.xingliu.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
public enum XingLiuEnums {

    STATUS_403(403, "访问拒绝,账户积分不足"),
    STATUS_429(429, "QPS超限，发起生图任务接口QPS限制1秒1次"),
    STATUS_100000(100000, "通用参数校验失败"),
    STATUS_100010(100010, "AccessKey过期"),
    STATUS_100021(100021, "用户积分不足"),
    STATUS_100030(100030, "图片地址无法访问，或大小超出限制"),
    STATUS_100031(100031, "图片包含违规内容"),
    STATUS_100032(100032, "图片下载失败"),
    STATUS_100050(100050, "生图参数未通过参数完整度校验"),
    STATUS_100052(100052, "提示词中包含敏感内容，请修改"),
    STATUS_100055(100055, "生图结果中包含敏感内容"),
    STATUS_200000(200000, "内部服务错误,官网系统维护"),
    ;

    @Schema(description = "code")
    private final int code;

    @Schema(description = "错误信息")
    private final String message;


    XingLiuEnums(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getXingLiuGenerateImgErrorMessage(int intValue) {
        if (intValue == STATUS_403.getCode()) {
            return STATUS_403.getMessage();
        }
        if (intValue == STATUS_429.getCode()) {
            return STATUS_429.getMessage();
        }
        if (intValue == STATUS_100000.getCode()) {
            return STATUS_100000.getMessage();
        }
        if (intValue == STATUS_100010.getCode()) {
            return STATUS_100010.getMessage();
        }
        if (intValue == STATUS_100021.getCode()) {
            return STATUS_100021.getMessage();
        }
        if (intValue == STATUS_100030.getCode()) {
            return STATUS_100030.getMessage();
        }
        if (intValue == STATUS_100031.getCode()) {
            return STATUS_100031.getMessage();
        }
        if (intValue == STATUS_100032.getCode()) {
            return STATUS_100032.getMessage();
        }
        if (intValue == STATUS_100050.getCode()) {
            return STATUS_100050.getMessage();
        }
        if (intValue == STATUS_100052.getCode()) {
            return STATUS_100052.getMessage();
        }
        if (intValue == STATUS_100055.getCode()) {
            return STATUS_100055.getMessage();
        }
        if (intValue == STATUS_200000.getCode()) {
            return STATUS_200000.getMessage();
        }
        return null;
    }

}
