package com.business.xingliu.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class XingLiuResponseBO {

    @JSONField(name = "generateUuid")
    private String generateUuid;

    @J<PERSON><PERSON>ield(name = "generateStatus")
    private Integer generateStatus;

    @J<PERSON><PERSON>ield(name = "percentCompleted")
    private Double percentCompleted;

    @JSONField(name = "generateMsg")
    private String generateMsg;

    @JSONField(name = "pointsCost")
    private Integer pointsCost;

    @JSONField(name = "accountBalance")
    private Integer accountBalance;

    @JSONField(name = "images")
    private List<ImagesBO> images;

    @Data
    public static class ImagesBO {

        @JSONField(name = "imageUrl")
        private String imageUrl;

        @JSONField(name = "seed")
        private Long seed;

        @JSONField(name = "auditStatus")
        private Integer auditStatus;

    }

}
