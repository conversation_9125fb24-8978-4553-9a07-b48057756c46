package com.business.xingliu.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class XingLiuRequestBO {

    @JSONField(name = "templateUuid")
    private String templateUuid;

    @J<PERSON>NField(name = "generateParams")
    private GenerateParams generateParams;

    @Data
    public static class GenerateParams {

        @J<PERSON><PERSON>ield(name = "prompt")
        private String prompt;

        @J<PERSON><PERSON>ield(name = "sourceImage")
        private String sourceImage;

        @J<PERSON>NField(name = "aspectRatio")
        private String aspectRatio;

        @JSONField(name = "imageSize")
        private ImageSize imageSize;

        @JSONField(name = "imgCount")
        private int imgCount;

        @JSONField(name = "steps")
        private int steps;

        @J<PERSON><PERSON>ield(name = "controlnet")
        private Controlnet controlnet;

        @Data
        public static class ImageSize {

            @J<PERSON>NField(name = "width")
            private int width;

            @J<PERSON><PERSON>ield(name = "height")
            private int height;
        }

        @Data
        public static class Controlnet {

            @J<PERSON><PERSON>ield(name = "controlType")
            private String controlType;

            @J<PERSON><PERSON>ield(name = "controlImage")
            private String controlImage;
        }

    }

}
