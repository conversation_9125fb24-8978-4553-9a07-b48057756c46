package com.business.aigc.sd;

import com.alibaba.fastjson2.JSONObject;
import com.business.aigc.sd.model.ControlNetMultiRequest;
import com.business.aigc.sd.model.ImgToImgRequest;

public class SM2JsonUtil {

    protected static JSONObject getImg2ImgJsonParams(ImgToImgRequest imgToImgRequest){
        if (imgToImgRequest == null){
            return null;
        }
        JSONObject json = new JSONObject();

        // 使用 put 方法将数据放入 JSONObject 中
        json.put("key", imgToImgRequest.getKey());
        json.put("model_id", imgToImgRequest.getModelId());
        json.put("prompt", imgToImgRequest.getPrompt());
        json.put("negative_prompt", imgToImgRequest.getNegativePrompt());
        json.put("init_image", imgToImgRequest.getInitImage());
        json.put("samples", imgToImgRequest.getSamples());
        json.put("num_inference_steps", imgToImgRequest.getNumInferenceSteps());
        json.put("safety_checker", imgToImgRequest.getSafetyChecker());
        json.put("enhance_prompt",imgToImgRequest.getEnhancePrompt());
        json.put("guidance_scale", imgToImgRequest.getGuidanceScale());
        json.put("strength", imgToImgRequest.getStrength());
        json.put("scheduler", imgToImgRequest.getScheduler());
        json.put("seed", imgToImgRequest.getSeed());
        json.put("lora_model", imgToImgRequest.getLoraModel());
        json.put("tomesd", imgToImgRequest.getTomesd());
        json.put("use_karras_sigmas", imgToImgRequest.getUseKarrasSigmas());
        json.put("vae", imgToImgRequest.getVae());
        json.put("lora_strength", imgToImgRequest.getLoraStrength());
        json.put("embeddings_model", imgToImgRequest.getEmbeddingsModel());
        json.put("webhook", imgToImgRequest.getWebhook());
        json.put("track_id", imgToImgRequest.getTrackId());
        json.put("ip_adapter_id", "ip-adapter_sdxl");
        json.put("ip_adapter_scale", "1");
        json.put("ip_adapter_image", imgToImgRequest.getNumInferenceSteps());
        json.put("clip_skip", "1");
        json.put("base64", "no");
        json.put("temp", "yes");
        return json;
    }


    protected static JSONObject getControlNetMultiRequest2Json(){
        ControlNetMultiRequest request = new ControlNetMultiRequest();
        request.setKey("oGpo4S22uuV8jgRX68ZiXgRx0pTTfq74zpgcb8MEYBU9KD4yLnB18KDHog1p");
        request.setControlnetModel("ipfacev2,sdxl-basemodel-1");
        request.setControlnetType("openpose,depth,canny");

        request.setModelId("albedobase-xl-v21");
//        request.setModelId("crystal-clear-xlv1");
        request.setAutoHint("yes");
        request.setGuessMode("yes");
        request.setPrompt("a portrait of a beautiful @me, claymation, claymation portrait style, beautiful detailed art");
        request.setNegativePrompt("photo, bokeh, photography, nsfw, nude, boobs, oversaturated, ugly, broken, watermark, ugly teeth, bad, scary, hideous");
        request.setInitImage("https://cdn.diandiansheji.com/ai_template/1737080138038439938.webp");
        request.setControlImage(request.getInitImage());
        request.setMaskImage(null);
        request.setWidth("576");
        request.setHeight("1024");
        request.setSamples("1");
        request.setScheduler("UniPCMultistepScheduler");
        request.setNumInferenceSteps("31");//21/31/41
        request.setSafetyChecker("no");
        request.setEnhancePrompt("yes");
        request.setGuidanceScale(7.5);
        request.setControlnetConditioningScale(4.5);
        request.setStrength(0.55);
        request.setLoraModel("claymation-style-for-sdxl");
        request.setLoraStrength("0.7,1");
        request.setClipSkip("2");
        request.setTomesd("yes");
        request.setUseKarrasSigmas("yes");
        request.setVae(null);
        request.setSeed(null);
        request.setEmbeddingsModel(null);
        request.setWebhook(null);
        request.setTrackId(null);

        return JSONObject.from(request);
    }
}
