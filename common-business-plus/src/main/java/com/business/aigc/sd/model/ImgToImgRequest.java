package com.business.aigc.sd.model;

import lombok.Data;

@Data
public class ImgToImgRequest {
    private String key;
    private String modelId;
    private String prompt;
    private String negativePrompt;
    private String initImage;
    private String samples;
    private String numInferenceSteps;
    private String safetyChecker;
    private String enhancePrompt;
    private Double guidanceScale;
    private Double strength;
    private String scheduler;
    private Integer seed;
    private String loraModel;
    private String tomesd;
    private String useKarrasSigmas;
    private String vae;
    private String loraStrength;
    private String embeddingsModel;
    private String webhook;
    private String trackId;
}
