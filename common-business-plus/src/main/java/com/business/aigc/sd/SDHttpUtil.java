package com.business.aigc.sd;

import com.alibaba.fastjson2.JSONObject;
import com.business.aigc.pika.model.VideoGenerationResponse;
import com.business.aigc.sd.model.ImgToImgRequest;
import com.business.aigc.sd.model.TextToImgRequest;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.util.concurrent.TimeUnit;

@Slf4j
public class SDHttpUtil {

    static String mediaTypeJson = "application/json";
    static String authorization = "oGpo4S22uuV8jgRX68ZiXgRx0pTTfq74zpgcb8MEYBU9KD4yLnB18KDHog1p";
    private static OkHttpClient client;

    public static OkHttpClient getInstance() {
        if (client == null) {
            client = new OkHttpClient.Builder()
                    .readTimeout(10, TimeUnit.MINUTES)
                    .writeTimeout(10, TimeUnit.MINUTES)
                    .build();
        }
        return client;
    }

    public static void postReloadModel(String key, String modelId) {
        JSONObject json = new JSONObject();
        json.put("key", key);
        json.put("model_id", modelId);
        RequestBody body = RequestBody.create(json.toString(), MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://modelslab.com/api/v6/images/model_reload")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
//            log.info("response:{}", response);
            if (response.body() != null) {
                String responseBody = response.body().string();
//                log.info("responseBody:{}",responseBody);
//                return JSONObject.parseObject(responseBody, VideoGenerationResponse.class);
                System.out.println(responseBody);
            }
        } catch (Exception e) {
//            log.error(e.getMessage(), e);
        }
    }

    public static VideoGenerationResponse postImgToImg(ImgToImgRequest imgToImgRequest) {
        if (imgToImgRequest == null){
            return null;
        }
        imgToImgRequest.setKey(authorization);
        JSONObject json = SM2JsonUtil.getImg2ImgJsonParams(imgToImgRequest);
        RequestBody body = RequestBody.create(json.toString(), MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://modelslab.com/api/v6/images/img2img")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
//            log.info("response:{}", response);
            if (response.body() != null) {
                String responseBody = response.body().string();
//                log.info("responseBody:{}",responseBody);
//                return JSONObject.parseObject(responseBody, VideoGenerationResponse.class);
                System.out.println(responseBody);
                return null;
            }
        } catch (Exception e) {
//            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static VideoGenerationResponse postControlNetMulti(JSONObject imgToImgRequest) {
        RequestBody body = RequestBody.create(imgToImgRequest.toString(), MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://modelslab.com/api/v6/images/img2img")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
//            log.info("response:{}", response);
            if (response.body() != null) {
                String responseBody = response.body().string();
//                log.info("responseBody:{}",responseBody);
//                return JSONObject.parseObject(responseBody, VideoGenerationResponse.class);
                System.out.println(responseBody);
                return null;
            }
        } catch (Exception e) {
//            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static VideoGenerationResponse postTextToImg(TextToImgRequest textToImgRequest) {
        if (textToImgRequest == null){
            return null;
        }
        textToImgRequest.setKey(authorization);
        JSONObject json = JSONObject.from(textToImgRequest);
        RequestBody body = RequestBody.create(json.toString(), MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://modelslab.com/api/v6/images/text2img")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            log.info("response:{}", response);
            if (response.body() != null) {
                String responseBody = response.body().string();
                log.info("responseBody:{}",responseBody);
                return JSONObject.parseObject(responseBody, VideoGenerationResponse.class);
                // System.out.println(responseBody);
                // return null;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    // 换脸
    public static String postImageToFaceGen() {

        OkHttpClient client = getInstance();
        MediaType mediaType = MediaType.parse("application/json");
        JSONObject json = new JSONObject();
        json.put("key", authorization);
        json.put("prompt", "pretty woman");
        json.put("negative_prompt", "anime, cartoon, drawing, big nose, long nose, fat, ugly, big lips, big mouth, face proportion mismatch, unrealistic, monochrome, lowres, bad anatomy, worst quality, low quality, blurry");
        json.put("face_image", "https://media.allure.com/photos/647f876463cd1ef47aab9c88/3:2/w_2465,h_1643,c_limit/angelina%20jolie%20blonde%20hair%20chloe.jpg");
        json.put("width", 512);
        json.put("height", 512);
        json.put("samples", 1);
        json.put("num_inference_steps", 21);
        json.put("safety_checker", false);
        json.put("base64", false);
        json.put("seed", null);
        json.put("webhook", null);
        json.put("track_id", null);
        RequestBody body = RequestBody.create(mediaType, json.toString());
        Request request = new Request.Builder()
                .url("https://modelslab.com/api/v6/image_editing/face_gen")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try(Response response = client.newCall(request).execute()) {
            System.out.println(response.code());
            if (response.isSuccessful()) {
                System.out.println(response.body().string());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
        return null;
    }

    public static void main(String[] args) {

    }

}
