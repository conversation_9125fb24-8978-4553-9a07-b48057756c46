package com.business.aigc.sd.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class ControlNetMultiRequest {

    @J<PERSON><PERSON>ield(name = "key")
    private String key;
    @J<PERSON><PERSON><PERSON>(name = "model_id")
    private String modelId;
    @J<PERSON><PERSON>ield(name = "controlnet_model")
    private String controlnetModel;
    @JSONField(name = "controlnet_type")
    private String controlnetType;

    @JSONField(name = "auto_hint")
    private String autoHint;

    @J<PERSON><PERSON>ield(name = "guess_mode")
    private String guessMode;

    private String prompt;

    @J<PERSON><PERSON>ield(name = "negative_prompt")
    private String negativePrompt;

    @<PERSON><PERSON><PERSON>ield(name = "init_image")
    private String initImage;

    @J<PERSON><PERSON>ield(name = "control_image")
    private String controlImage;

    @JSONField(name = "mask_image")
    private String maskImage;
    private String width;
    private String height;
    private String samples;
    private String scheduler;
    private String tomesd;
    @JSONField(name = "use_karras_sigmas")
    private String useKarrasSigmas;
    @JSONField(name = "algorithm_type")
    private String algorithmType;

    private String vae;

    @J<PERSON><PERSON>ield(name = "lora_strength")
    private String loraStrength;

    @JSO<PERSON>ield(name = "lora_model")
    private String loraModel;

    @JSONField(name = "num_inference_steps")
    private String numInferenceSteps;

    @JSONField(name = "safety_checker")
    private String safetyChecker;

    @JSONField(name = "embeddings_model")
    private String embeddingsModel;

    @JSONField(name = "ip_adapter_id")
    private String ipAdapterId;

    @JSONField(name = "ip_adapter_scale")
    private float ipAdapterScale;

    @JSONField(name = "ip_adapter_image")
    private String ipAdapterImage;

    @JSONField(name = "enhance_prompt")
    private String enhancePrompt;

    @JSONField(name = "multi_lingual")
    private String multiLingual;

    @JSONField(name = "controlnet_conditioning_scale")
    private Double controlnetConditioningScale;
    private Double strength;
    private Integer seed;
    private String webhook;

    @JSONField(name = "track_id")
    private String trackId;

    @JSONField(name = "upscale")
    private String upscale;

    @JSONField(name = "clip_skip")
    private String clipSkip;

    @JSONField(name = "base64")
    private String base64;

    @JSONField(name = "temp")
    private String temp;

    @JSONField(name = "guidance_scale")
    private Double guidanceScale;

}
