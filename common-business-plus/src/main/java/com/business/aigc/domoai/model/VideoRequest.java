package com.business.aigc.domoai.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

@Data
public class VideoRequest {

    @J<PERSON>NField(name = "video_url")
    private String videoUrl;

    @J<PERSON><PERSON>ield(name = "prompt")
    private String prompt;

    @JSONField(name = "model_version")
    private Integer modelVersion;

    @JSONField(name = "duration")
    private String duration;

    @JSONField(name = "refer_mode")
    private String referMode;

    @JSONField(name = "video_key")
    private String videoKey;

    @JSONField(name = "orientation")
    private String orientation;

    @JSONField(name = "mode")
    private String mode;

    @J<PERSON><PERSON>ield(name = "disable_watermark")
    private Boolean disableWatermark;

    @JSO<PERSON>ield(name = "lip_sync")
    private Boolean lipSync;

    @JSONField(name = "subject_only")
    private Boolean subjectOnly;

    public VideoRequest() {}

//    videoRequest = new VideoRequest();
//    // videoRequest.setVideoUrl("https://img.domoai.app/post-video/2024-06-05_11-07-08.mp4");
//        videoRequest.setVideoUrl("https://cdn.diandiansheji.com/ai_model/sd/video/1804010332212609026.mp4");
//        videoRequest.setPrompt("car");
//        videoRequest.setModelVersion(15023);
//        videoRequest.setDuration("3s");//3s \ 5s \ 10s \20s
//        videoRequest.setReferMode("VIDEO_MORE");//VIDEO_MORE：偏向视频的模型；PROMPT_MORE偏向提示词的模型
//        videoRequest.setVideoKey(null);//null不加主体抠像、"BLUE"蓝色、"GREEN"青色
//        videoRequest.setOrientation("SQUARE");//null 自动、 "SQUARE"：（1:1）、"LANDSCAPE"：16:9 ；"PORTRAIT"：9:16
//        videoRequest.setMode("RELAX");//"RELAX"休闲模式、、"FAST"快速模式
//        videoRequest.setDisableWatermark(true);//是否去水印
//        videoRequest.setLipSync(false);//true 未知、false
//        videoRequest.setSubjectOnly(false);//true 未知、false

    public VideoRequest(String videoUrl, String prompt, Integer modelVersion, String duration,
                        String referMode, String orientation, String mode) {
        this.videoUrl = videoUrl;
        this.prompt = prompt;
        this.modelVersion = modelVersion;
        this.duration = duration;
        this.referMode = referMode;
        this.videoKey = null;
        this.orientation = orientation;
        this.mode = mode;
        this.disableWatermark = true;
        this.lipSync = false;
        this.subjectOnly = false;
    }
}
