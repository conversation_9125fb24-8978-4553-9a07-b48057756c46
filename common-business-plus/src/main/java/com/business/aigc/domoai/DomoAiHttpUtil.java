package com.business.aigc.domoai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.business.aigc.domoai.model.*;
import com.business.utils.BDateUtil;
import com.business.utils.BUrlUtil;
import com.nacos.utils.BFeiShuUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicSessionCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
public class DomoAiHttpUtil {

    static String mediaTypeJson = "application/json";
    private static OkHttpClient client;
    private static OkHttpClient getInstance() {
        if (client == null) {
            client = new OkHttpClient.Builder()
                    .readTimeout(15, TimeUnit.MINUTES)
                    .writeTimeout(15, TimeUnit.MINUTES)
                    .build();
        }
        return client;
    }


    public static String aaaaa() {
        Request request = new Request.Builder()
                .url("https://api.domoai.app/web-post/model/video-models?offset=0&limit=101&locale=zh-Hant")
                .get()
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .addHeader("Authorization", "Bearer 61e0a179-901b-4263-8f4c-b0d09f3e2db3")
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Type", "application/json")
                .addHeader("Domo-Platform", "WEB")
                .addHeader("Host", "api.domoai.app")
                .addHeader("Origin", "https://www.domoai.app")
                .addHeader("Referer", "https://www.domoai.app/")
                .addHeader("Sec-Ch-Ua", "\"Microsoft Edge\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    protected static JsonResponseUploadToken getUploadToken(String token) {
        Request request = new Request.Builder()
                .url("https://api.domoai.app/web-post/upload-token")
                .get()
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .addHeader("Authorization", "Bearer "+token)
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Type", "application/json")
                .addHeader("Domo-Platform", "WEB")
                .addHeader("Host", "api.domoai.app")
                .addHeader("Origin", "https://www.domoai.app")
                .addHeader("Referer", "https://www.domoai.app/")
                .addHeader("Sec-Ch-Ua", "\"Microsoft Edge\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("哆莫视频获取上传文件参数：｛｝", responseBody);
            if (response.isSuccessful()) {
                return JSONObject.parseObject(responseBody, JsonResponseUploadToken.class);
            }else{
                log.info("getUploadToken responseCode="+response.code());
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"哆莫getUploadToken失败","ERRORInfo="+ BUrlUtil.respErrorInfoByCode(response.code()));
            }
        } catch (Exception e) {
            log.info("哆莫视频获取上传文件参数：｛｝", e.getMessage());
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"哆莫getUploadToken失败","ERRORInfo="+ e.getMessage());
            return null;
        }
        return null;
    }

    public static String uploadFileToDomoAi(JsonResponseUploadToken uploadToken, String imgUrl) {
        COSCredentials cred = new BasicSessionCredentials(uploadToken.getTmpSecretId(), uploadToken.getTmpSecretKey(), uploadToken.getSessionToken());
        ClientConfig clientConfig = new ClientConfig(new Region("accelerate"));
        COSClient cosClient = new COSClient(cred, clientConfig);
        InputStream inputStream = null;
        log.info("uploadFileToDomoAi param uploadToken={},imgUrl={}", JSON.toJSONString(uploadToken),imgUrl);
        try {
            URL url = new URL(imgUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            inputStream = connection.getInputStream();

            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            byte[] temp = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(temp)) != -1) {
                buffer.write(temp, 0, bytesRead);
            }
            byte[] data = buffer.toByteArray();
            InputStream inputStreamNew = new ByteArrayInputStream(data);

            int contentLength = connection.getContentLength();
            String key = uploadToken.getObjectKeyPrefix() + BDateUtil.getFileNameUseDate()+".mp4";
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(contentLength);
            PutObjectRequest putObjectRequest = new PutObjectRequest(uploadToken.getBucketName(), key, inputStreamNew, metadata);
            cosClient.putObject(putObjectRequest);
            log.info("哆莫AI上传视频= https://img.domoai.app/" + key);
            return "https://img.domoai.app/" + key;
        } catch (Exception e) {
            log.error("上传图片到哆莫失败..."+ e.getMessage());
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"哆莫uploadFileToDomoAi失败","ERRORInfo="+ e.getMessage());
            return null;
        } finally {
            cosClient.shutdown();
        }
    }


    protected static JsonResponseJobVideo postToGenerateVideo(VideoRequest videoRequest, String token) {
        if (videoRequest == null || videoRequest.getPrompt() == null || videoRequest.getPrompt().isEmpty()
        || videoRequest.getDuration() == null || videoRequest.getDuration().isEmpty() || videoRequest.getModelVersion() == null
                || videoRequest.getReferMode() == null || videoRequest.getReferMode().isEmpty()
                || videoRequest.getVideoUrl() == null || videoRequest.getVideoUrl().isEmpty()
                || videoRequest.getMode() == null || videoRequest.getMode().isEmpty()
        ){
            return null;
        }
        if (!Objects.equals(videoRequest.getDuration(),"3s") && !Objects.equals(videoRequest.getDuration(),"5s")
                && !Objects.equals(videoRequest.getDuration(),"10s") && !Objects.equals(videoRequest.getDuration(),"20s")){
            return null;
        }
        if (!Objects.equals(videoRequest.getReferMode(),"VIDEO_MORE") && !Objects.equals(videoRequest.getReferMode(),"PROMPT_MORE")){
            return null;
        }
        if (videoRequest.getOrientation() == null || videoRequest.getOrientation().isEmpty()) {
            videoRequest.setOrientation(null);
        }
        if (!Objects.equals(videoRequest.getMode(),"RELAX") && !Objects.equals(videoRequest.getMode(),"FAST")){
            return null;
        }
        if (Objects.equals(videoRequest.getDuration(),"20s") && Objects.equals(videoRequest.getMode(),"RELAX")){
            return null;
        }
        /*if (ModelVersionEnum.getByCheckParamNot(videoRequest)){
            return null;
        }*/
        if (videoRequest.getVideoKey() != null && !videoRequest.getVideoKey().isEmpty()) {
            videoRequest.setVideoKey(VideoKeyEnum.getEnum(videoRequest.getVideoKey()).getValue());//重置主体扣像
        }
        //强制慢速
        videoRequest.setMode("RELAX");
        log.info("domo视频postToGenerateVideo param = " + JSON.toJSONString(videoRequest));
        JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(videoRequest, JSONWriter.Feature.WriteMapNullValue));
        String requestBody = json.toString();
        RequestBody body = RequestBody.create(requestBody, MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://api.domoai.app/web-post/create/video?locale=zh-Hant")
                .post(body)
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Length", requestBody)
                .addHeader("Content-Type", "application/json")
                .addHeader("Domo-Platform", "WEB")
                .addHeader("Host", "api.domoai.app")
                .addHeader("Origin", "https://www.domoai.app")
                .addHeader("Referer", "https://www.domoai.app/")
                .addHeader("Sec-Ch-Ua", "\"Microsoft Edge\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("domo视频postToGenerateVideo Code = " + response.code());
            log.info("domo视频postToGenerateVideo resp = "+ responseBody);
            if (response.isSuccessful()) {
                return JSONObject.parseObject(responseBody, JsonResponseJobVideo.class);
            }else{
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"哆莫生成视频失败","code="+ response.code()+", resp="+responseBody);
            }
        } catch (Exception e) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"哆莫生成视频失败","ERRORInfo="+ e.getMessage());
            return null;
        }
        return null;
    }

    //获取任务执行状态
    protected static JsonResponseJobStateVo postsToGenerateJobState(List<String> idList, String token) {
        if (idList == null || idList.isEmpty()){
            return null;
        }
        JSONArray ids = new JSONArray();
        ids.addAll(idList);
        JSONObject json = new JSONObject();
        json.put("posts_id", ids);
        String requestBody = json.toString();
        RequestBody body = RequestBody.create(requestBody, MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://api.domoai.app/web-post/posts-generate-info")
                .post(body)
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .addHeader("Authorization", "Bearer "+token)
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Length", requestBody)
                .addHeader("Content-Type", "application/json")
                .addHeader("Domo-Platform", "WEB")
                .addHeader("Host", "api.domoai.app")
                .addHeader("Origin", "https://www.domoai.app")
                .addHeader("Referer", "https://www.domoai.app/")
                .addHeader("Sec-Ch-Ua", "\"Microsoft Edge\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("domo视频1返回Code = " + response.code());
            log.info("domo视频1返回参数 = "+ responseBody);
            if (response.isSuccessful()) {
                return JSONObject.parseObject(responseBody, JsonResponseJobStateVo.class);
            }
        } catch (Exception e) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"哆莫拉取任务1失败","ERRORInfo="+ e.getMessage());
            return null;
        }
        return null;
    }

    //获取任务执行信息：完成后调用即可
    protected static JsonResponseJobInfoVo postsToGenerateJobInfo(List<String> idList, String token) {
        if (idList == null || idList.isEmpty()){
            return null;
        }
        JSONArray ids = new JSONArray();
        ids.addAll(idList);
        JSONObject json = new JSONObject();
        json.put("posts_id", ids);
        String requestBody = json.toString();
        RequestBody body = RequestBody.create(requestBody, MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://api.domoai.app/web-user/posts")
                .post(body)
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .addHeader("Authorization", "Bearer "+token)
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Length", requestBody)
                .addHeader("Content-Type", "application/json")
                .addHeader("Domo-Platform", "WEB")
                .addHeader("Host", "api.domoai.app")
                .addHeader("Origin", "https://www.domoai.app")
                .addHeader("Referer", "https://www.domoai.app/")
                .addHeader("Sec-Ch-Ua", "\"Microsoft Edge\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("domo视频2返回Code = " + response.code());
            log.info("domo视频2返回参数 = "+ responseBody);
            if (response.isSuccessful()) {
                return JSONObject.parseObject(responseBody, JsonResponseJobInfoVo.class);
            }
        } catch (Exception e) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"哆莫拉取任务2失败","ERRORInfo="+ e.getMessage());
            return null;
        }
        return null;
    }

    protected static boolean postsToDeleteJob(String id, String token) {
        if (id == null || id.isEmpty()){
            return false;
        }
        JSONObject json = new JSONObject();
        json.put("id", id);
        String requestBody = json.toString();
        RequestBody body = RequestBody.create(requestBody, MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://api.domoai.app/user/delete-post")
                .post(body)
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .addHeader("Authorization", "Bearer "+token)
                .addHeader("Connection", "keep-alive")
                .addHeader("Content-Length", requestBody)
                .addHeader("Content-Type", "application/json")
                .addHeader("Domo-Platform", "WEB")
                .addHeader("Host", "api.domoai.app")
                .addHeader("Origin", "https://www.domoai.app")
                .addHeader("Referer", "https://www.domoai.app/")
                .addHeader("Sec-Ch-Ua", "\"Microsoft Edge\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                System.out.println(responseBody);
                return JSONObject.parseObject(responseBody).getInteger("code")==0;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }


}
