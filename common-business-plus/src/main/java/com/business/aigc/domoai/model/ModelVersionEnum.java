package com.business.aigc.domoai.model;

import lombok.Getter;

@Getter
public enum ModelVersionEnum {

    ANIME_V5_2(
            15028,
            "Anime v5.2",
            "日本動漫3.0",
            new Cover("https://imgo.domoai.app/ai-model/4249fe12-2456-4002-a39c-814c260e815c.png", 1024, 1648),
            "--ani v5.2",
            new String[]{"VIDEO_MORE"},
            true,
            300
    ),
    ILLUSTRATION_V13_1(
            15029,
            "Illustration v13.1",
            "黏土卡通風格 3.0",
            new Cover("https://imgo.domoai.app/ai-model/401d4cf6-fa12-40d7-a7cc-ec5982e6dd37.png", 1024, 1648),
            "--illus v13.1",
            new String[]{"VIDEO_MORE"},
            true,
            300
    ),
    ILLUSTRATION_V1_2(
            15030,
            "Illustration v1.2",
            "3D卡通風格3.0",
            new Cover("https://imgo.domoai.app/ai-model/6b8f9c99-60dd-4369-a3f5-8700f3f59704.png", 1024, 1648),
            "--illus v1.2",
            new String[]{"VIDEO_MORE"},
            true,
            300
    ),
    ILLUSTRATION_V14(
            15031,
            "Illustration v14",
            "浮世繪風格",
            new Cover("https://imgo.domoai.app/ai-model/6651151b-6a5b-4d8d-8b6b-660db8c32d5f.png", 1024, 1648),
            "--illus v14",
            new String[]{"VIDEO_MORE"},
            true,
            300
    ),
    ILLUSTRATION_V13(
            15027,
            "Illustration v13",
            "黏土卡通風格",
            new Cover("https://imgo.domoai.app/ai-model/073d2fa2-f678-415b-94fe-1cde9142d37b.png", 1024, 1648),
            "--illus v13",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ILLUSTRATION_V12(
            15026,
            "Illustration v12",
            "樂高風格",
            new Cover("https://imgo.domoai.app/ai-model/6c3064fd-f161-4f88-b1fa-726ab6cc29ec.png", 1024, 1648),
            "--illus v12",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ILLUSTRATION_V11(
            15025,
            "Illustration v11",
            "美國漫畫",
            new Cover("https://imgo.domoai.app/ai-model/c0223c6b-db06-4c09-950c-d0a5019f1c45.png", 1024, 1648),
            "--illus v11",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ANIME_V7(
            15024,
            "Anime v7",
            "色鉛筆風格",
            new Cover("https://imgo.domoai.app/ai-model/3b2629c6-d79e-4b21-b7bf-a5c454476007.png", 1024, 1648),
            "--ani v7",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ILLUSTRATION_V10(
            15023,
            "Illustration v10",
            "PS2遊戲風格",
            new Cover("https://imgo.domoai.app/ai-model/e5394ee2-a0b7-437e-9e3d-1795d5de5125.png", 1024, 1648),
            "--illus v10",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ILLUSTRATION_V9(
            15022,
            "Illustration v9",
            "2.5D插畫風格",
            new Cover("https://imgo.domoai.app/ai-model/8e25862d-428f-4c7c-956e-a70eed5548c8.png", 1024, 1648),
            "--illus v9",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    FUSION_STYLE_V1(
            15014,
            "Fusion Style v1",
            "任何樣式，根據提示(Prompt)",
            new Cover("https://imgo.domoai.app/ai-model/bcb0d19e-f88f-4fc2-85a7-9abf4624525a.png", 1024, 1648),
            "--fs v1",
            new String[]{"PROMPT_MORE"},
            false,
            200
    ),
    ANIME_V6(
            15015,
            "Anime v6",
            "細節豐富的動漫風格2.0",
            new Cover("https://imgo.domoai.app/ai-model/f59317bc-0417-46ab-8fee-72c8fbd3c8eb.png", 1024, 1648),
            "--ani v6",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ANIME_V5_1(
            15016,
            "Anime v5.1",
            "日本動漫2.1",
            new Cover("https://imgo.domoai.app/ai-model/3843c175-b640-4697-a89e-a785583c4a5c.png", 1024, 1648),
            "--ani v5.1",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ANIME_V4_1(
            15018,
            "Anime v4.1",
            "中國墨畫2.0",
            new Cover("https://imgo.domoai.app/ai-model/6db5f48d-8c53-4e88-abac-e4d550b641eb.png", 1024, 1648),
            "--ani v4.1",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ANIME_V1_1(
            15017,
            "Anime v1.1",
            "扁平化色彩動漫風格2.0",
            new Cover("https://imgo.domoai.app/ai-model/5afd5dac-6843-4901-b67f-46f728261070.png", 1024, 1648),
            "--ani v1.1",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ILLUSTRATION_V1_1(
            15019,
            "Illustration v1.1",
            "3D卡通風格2.0",
            new Cover("https://imgo.domoai.app/ai-model/d6d095be-dc29-417e-b5d8-22660fc3df3d.png", 1024, 1648),
            "--illus v1.1",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ILLUSTRATION_V3_1(
            15020,
            "Illustration v3.1",
            "像素風格2.0",
            new Cover("https://imgo.domoai.app/ai-model/1b3b7497-4a89-4eeb-86ec-288f5c2743e0.png", 1024, 1648),
            "--illus v3.1",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ILLUSTRATION_V7_1(
            15021,
            "Illustration v7.1",
            "紙藝風格2.0",
            new Cover("https://imgo.domoai.app/ai-model/475bf829-96b0-4ceb-b3f3-ca555fb12f22.png", 1024, 1648),
            "--illus v7.1",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            200
    ),
    ANIME_V1(
            15001,
            "Anime v1",
            "扁平化色彩動漫風格",
            new Cover("https://imgo.domoai.app/ai-model/2accc87e-0bbd-43c3-8d64-b207747761fb.png", 1024, 1648),
            "--ani v1",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ANIME_V3(
            15002,
            "Anime v3",
            "中國墨畫",
            new Cover("https://imgo.domoai.app/ai-model/6d9122b5-d0d5-4a43-8e70-e1442af51d7e.png", 1024, 1648),
            "--ani v3",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ANIME_V4(
            15003,
            "Anime v4",
            "中國墨畫1.5",
            new Cover("https://imgo.domoai.app/ai-model/d9c670c7-2b76-43db-90e7-63cf170c6f35.png", 1024, 1648),
            "--ani v4",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ANIME_V5(
            15004,
            "Anime v5",
            "日本動漫2.0",
            new Cover("https://imgo.domoai.app/ai-model/1e0b012a-7f97-40fa-93b3-309f00335d0e.png", 1024, 1648),
            "--ani v5",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ANIME_V6_1(
            15005,
            "Anime v6.1",
            "細節豐富的動漫風格2.1",
            new Cover("https://imgo.domoai.app/ai-model/b5ae09c8-d308-439e-9279-f2e5ad51a62d.png", 1024, 1648),
            "--ani v6.1",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ANIME_V2(
            15006,
            "Anime v2",
            "寫實動漫",
            new Cover("https://imgo.domoai.app/ai-model/998d5513-7c93-418e-95d8-e55a009c7fbd.png", 1024, 1648),
            "--ani v2",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ANIME_V4_2(
            15007,
            "Anime v4.2",
            "中國墨畫2.1",
            new Cover("https://imgo.domoai.app/ai-model/6d905306-ef09-4cb4-99ad-768399b8ad5a.png", 1024, 1648),
            "--ani v4.2",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ANIME_V5_3(
            15008,
            "Anime v5.3",
            "日本動漫3.1",
            new Cover("https://imgo.domoai.app/ai-model/00b10e2f-f5c8-40a6-bef5-f6b2dfb43a22.png", 1024, 1648),
            "--ani v5.3",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ANIME_V6_2(
            15009,
            "Anime v6.2",
            "細節豐富的動漫風格2.2",
            new Cover("https://imgo.domoai.app/ai-model/48b7b207-0a91-40af-8d90-0dfb11e9fc36.png", 1024, 1648),
            "--ani v6.2",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ILLUSTRATION_V1(
            15010,
            "Illustration v1",
            "3D卡通風格",
            new Cover("https://imgo.domoai.app/ai-model/5dd05b9c-3627-46ac-bf12-4e0b6d763d09.png", 1024, 1648),
            "--illus v1",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ILLUSTRATION_V2(
            15011,
            "Illustration v2",
            "美國卡通",
            new Cover("https://imgo.domoai.app/ai-model/7415d2de-f59f-44a6-9823-fd4b21d010b0.png", 1024, 1648),
            "--illus v2",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ILLUSTRATION_V3(
            15012,
            "Illustration v3",
            "像素風格",
            new Cover("https://imgo.domoai.app/ai-model/09f34b57-f0e3-44a5-9ed3-1d167cfbaab6.png", 1024, 1648),
            "--illus v3",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),
    ILLUSTRATION_V4(
            15013,
            "Illustration v4",
            "樂高風格2.0",
            new Cover("https://imgo.domoai.app/ai-model/9d69e799-e9cf-4e30-9c29-1d53b798510e.png", 1024, 1648),
            "--illus v4",
            new String[]{"VIDEO_MORE", "PROMPT_MORE"},
            false,
            100
    ),

    ;

    private final int id;
    private final String title;
    private final String description;
    private final Cover cover;
    private final String promptArgs;
    private final String[] allowedReferModes;
    private final boolean allowedLipSync;
    private final int techVersion;

    ModelVersionEnum(int id, String title, String description, Cover cover, String promptArgs, String[] allowedReferModes, boolean allowedLipSync, int techVersion) {
        this.id = id;
        this.title = title;
        this.description = description;
        this.cover = cover;
        this.promptArgs = promptArgs;
        this.allowedReferModes = allowedReferModes;
        this.allowedLipSync = allowedLipSync;
        this.techVersion = techVersion;
    }

    public record Cover(String url, int width, int height) {

    }

    public static Boolean getByCheckParamNot(VideoRequest videoRequest) {
        if (videoRequest == null){
            return true;
        }
        ModelVersionEnum modelVersionEnum = null;
        for (ModelVersionEnum value : ModelVersionEnum.values()) {
            if (value.getId() == videoRequest.getModelVersion()) {
                modelVersionEnum = value;
                break;
            }
        }
        if (modelVersionEnum == null){
            return true;
        }
        //校验是否存在口型同步
        if (!modelVersionEnum.isAllowedLipSync() && videoRequest.getLipSync()){
            return true;
        }
        //校验是否存在其他功能
        for (String allowedReferMode : modelVersionEnum.getAllowedReferModes()) {
            if (videoRequest.getReferMode().equals(allowedReferMode)) {
                return false;
            }
        }
        return true;
    }

}

