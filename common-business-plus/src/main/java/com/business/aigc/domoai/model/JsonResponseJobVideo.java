package com.business.aigc.domoai.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class JsonResponseJobVideo {

    @JSONField(name = "code")
    private int code;

    @J<PERSON><PERSON>ield(name = "data")
    private Data2 data;

    @Data
    public static class Data2 {

        @J<PERSON><PERSON>ield(name = "id")
        private String id;

        @JSONField(name = "category")
        private String category;

        @JSONField(name = "title")
        private String title;

        @JSONField(name = "assets")
        private List<Asset> assets;

        @JSONField(name = "generated_assets")
        private List<Asset> generatedAssets;

        @J<PERSON><PERSON>ield(name = "created_at")
        private String createdAt;

        @JSONField(name = "discord_message_url")
        private String discordMessageUrl;

        @JSONField(name = "video_duration")
        private String videoDuration;

        @JSONField(name = "animate_duration")
        private String animateDuration;

        @JSONField(name = "generate_info")
        private GenerateInfo generateInfo;

        @JSONField(name = "params")
        private Params params;

    }

    @Data
    public static class Asset {

        @J<PERSON><PERSON>ield(name = "url")
        private String url;

        @J<PERSON><PERSON>ield(name = "width")
        private int width;

        @JSONField(name = "height")
        private int height;

        @JSONField(name = "type")
        private String type;

    }

    @Data
    public static class GenerateInfo {

        @JSONField(name = "status")
        private String status;

        @JSONField(name = "prediction_time")
        private String predictionTime;

    }

    @Data
    public static class Params {

        @JSONField(name = "mode")
        private String mode;

        @JSONField(name = "disable_watermark")
        private boolean disableWatermark;

        @JSONField(name = "video_url")
        private String videoUrl;

        @JSONField(name = "prompt")
        private String prompt;

        @JSONField(name = "model_version")
        private int modelVersion;

        @JSONField(name = "duration")
        private String duration;

        @JSONField(name = "refer_mode")
        private String referMode;

        @JSONField(name = "video_key")
        private String videoKey;

        @JSONField(name = "orientation")
        private String orientation;

        @JSONField(name = "lip_sync")
        private boolean lipSync;

    }
}

