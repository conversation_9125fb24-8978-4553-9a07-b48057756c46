package com.business.aigc.domoai.model;

public enum VideoKeyEnum {
    NOTHING(null),
    G<PERSON><PERSON>("GRE<PERSON>"),//绿色
    RED("RED"),//红色
    ORANGE("ORANGE"),//橙色
    YELLOW("YELLOW"),//黄色
    CYAN("CYAN"),//青色
    BLUE("BLUE"),//蓝色
    PINK("PINK"),//粉红色
    BROWN("BROWN"),//棕色
    PURPLE("PURPLE"),//紫色
    MAGENTA("MAGENTA"),//品红色
    WHITE("WHITE"),//白色
    BEIGE("BEIGE"),//米黄色
    GREY("GREY"),//灰色
    NAVY("NAVY"),//藏青色
    BLACK("BLACK"),//黑色


    ;

    private String value;

    VideoKeyEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static VideoKeyEnum getEnum(String value) {
        for (VideoKeyEnum videoKeyEnum : VideoKeyEnum.values()) {
            if (videoKeyEnum.getValue().equals(value)) {
                return videoKeyEnum;
            }
        }
        return NOTHING;
    }
}
