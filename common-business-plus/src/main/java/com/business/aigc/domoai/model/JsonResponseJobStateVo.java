package com.business.aigc.domoai.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

// domoAi job信息状态
@Data
public class JsonResponseJobStateVo {

    @JSONField(name = "code")
    private int code;

    @JSONField(name = "data")
    private Data2 data;

    @Data
    public static class Data2 {

        @JSONField(name = "posts")
        private Map<String, Post> posts;
    }

    @Data
    public static class Post {

        @JSONField(name = "status")
        private String status;

    }
}

