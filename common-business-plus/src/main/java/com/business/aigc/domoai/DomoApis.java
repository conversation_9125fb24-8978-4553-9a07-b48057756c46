package com.business.aigc.domoai;

import com.business.aigc.domoai.model.*;
import com.business.utils.BOssUtil;
import com.nacos.config.OssClientConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class DomoApis {

    public static final String STATUS_PENDING = "PENDING";//挂起、待处理
    public static final String STATUS_RUNNING = "RUNNING";//运行中
    public static final String STATUS_COMPLETION = "COMPLETION";//完成

    private static final String redisKey = "AIGC:DOMOAI:API_TOKEN:1001";

    private static String getAuthToken() {
        String token = null; //RedisUtil.getValue(redisKey);
        return token == null || token.isEmpty() ? "61e0a179-901b-4263-8f4c-b0d09f3e2db3" : token;
    }

    public static String getDomoAiImageUrl(String domoApiKey, String imgUrl) {
        if (Objects.isNull(domoApiKey) || domoApiKey.isEmpty()) {
            domoApiKey = getAuthToken();
        }
        JsonResponseUploadToken uploadToken = DomoAiHttpUtil.getUploadToken(domoApiKey);
        if (uploadToken == null) {
            return null;
        }
        return DomoAiHttpUtil.uploadFileToDomoAi(uploadToken, imgUrl);
    }

    public static JsonResponseJobVideo postToGenerateDomoVideo(String domoApiKey, VideoRequest videoRequest) {
        return DomoAiHttpUtil.postToGenerateVideo(videoRequest, domoApiKey == null ? getAuthToken() : domoApiKey);
    }

    public static String postsToGenerateJobState(List<String> idList, String token) {
        JsonResponseJobStateVo jsonResponseJobStateVo = DomoAiHttpUtil.postsToGenerateJobState(idList, token);
        if (jsonResponseJobStateVo == null) {
            jsonResponseJobStateVo = DomoAiHttpUtil.postsToGenerateJobState(idList, token);
        }
        if (jsonResponseJobStateVo == null || jsonResponseJobStateVo.getData() == null) {
            return null;
        }
        Map<String, JsonResponseJobStateVo.Post> posts = jsonResponseJobStateVo.getData().getPosts();
        JsonResponseJobStateVo.Post post  = posts.get(idList.getFirst());
        if (post == null) {
            return null;
        }
        return switch (post.getStatus()) {
            case STATUS_PENDING -> "running";
            case STATUS_RUNNING -> "running";
            case STATUS_COMPLETION -> postsToGenerateJobInfo(idList, token);
            default -> "running";
        };
    }
    public static String postsToGenerateJobInfo(List<String> idList, String token) {
        JsonResponseJobInfoVo jsonResponseJobStateVo = DomoAiHttpUtil.postsToGenerateJobInfo(idList, token);
        if (jsonResponseJobStateVo == null || jsonResponseJobStateVo.getData() == null || jsonResponseJobStateVo.getData().getFirst().getGeneratedAssets() == null) {
            return null;
        }
        String videoPath = jsonResponseJobStateVo.getData().getFirst().getGeneratedAssets().getFirst().getUrl();
        log.info("哆莫视频地址：" + videoPath);
        return uploadPiKaVideo(videoPath, idList.getFirst(), 14, OssClientConfig.FILE_SUFFIX_VIDEO);
    }

    // TODO 上传视频到oss
    public static String uploadPiKaVideo(String fileUrl, String videoJobId, Integer folder, String suffix) {
        if (fileUrl == null) return null;
        int maxRetries = 4; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadURL(fileUrl, videoJobId, folder, suffix);
                if (videoPath != null) {
                    return videoPath;
                }
                log.error("哆莫视频上传失败，重试次数：" + (retryCount + 1), videoJobId);
            } catch (Exception e) {
                log.error("哆莫上传图片到OSS发生异常，重试次数：" + (retryCount + 1), e);
            }
            retryCount++;
        }
        log.error("哆莫文件上传失败，超过最大重试次数， 视频id=", videoJobId);
        return null;
    }

    public static void main(String[] args) {
        List<String> idList = new ArrayList<String>();
        idList.add("709cbdfb-4400-4fe7-be1c-4ac407e1c25b");
        String token = "61e0a179-901b-4263-8f4c-b0d09f3e2db3";
//        postsToGenerateJobInfo(idList,token);
        postsToGenerateJobState(idList,token);
    }

}
