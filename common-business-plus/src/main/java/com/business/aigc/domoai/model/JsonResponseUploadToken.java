package com.business.aigc.domoai.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class JsonResponseUploadToken {

    @J<PERSON>NField(name = "bucket_name")
    private String bucketName;

    @J<PERSON><PERSON>ield(name = "tmp_secret_id")
    private String tmpSecretId;

    @JSONField(name = "tmp_secret_key")
    private String tmpSecretKey;

    @JSONField(name = "session_token")
    private String sessionToken;

    @JSONField(name = "start_time")
    private String startTime;

    @J<PERSON>NField(name = "expired_time")
    private String expiredTime;

    @J<PERSON><PERSON>ield(name = "object_key_prefix")
    private String objectKeyPrefix;

}

