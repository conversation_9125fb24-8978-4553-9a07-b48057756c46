//package com.business.aigc;
//
//import org.bytedeco.ffmpeg.avcodec.AVCodecParameters;
//import org.bytedeco.ffmpeg.avformat.AVFormatContext;
//import org.bytedeco.ffmpeg.global.avcodec;
//import org.bytedeco.ffmpeg.global.avformat;
//import org.bytedeco.ffmpeg.global.avutil;
//
//public class H264Checker {
//    public static void main(String[] args) {
//        String videoFilePath = "path/to/your/video.mp4";
//        boolean isH264 = checkIfH264(videoFilePath);
//        if (isH264) {
//            System.out.println("The video is encoded in H.264 format.");
//        } else {
//            System.out.println("The video is not encoded in H.264 format.");
//        }
//    }
//
//    public static boolean checkIfH264(String filePath) {
//        AVFormatContext formatContext = new AVFormatContext(null);
//
//        if (avformat.avformat_open_input(formatContext, filePath, null, null) != 0) {
//            System.err.println("Could not open file: " + filePath);
//            return false;
//        }
//
//        if (avformat.avformat_find_stream_info(formatContext,  (org.bytedeco.ffmpeg.avutil.AVDictionary) null) < 0) {
//            System.err.println("Could not find stream information");
//            return false;
//        }
//
//        boolean isH264 = false;
//        for (int i = 0; i < formatContext.nb_streams(); i++) {
//            AVCodecParameters codecParameters = formatContext.streams(i).codecpar();
//            if (codecParameters.codec_type() == avutil.AVMEDIA_TYPE_VIDEO) {
//                if (codecParameters.codec_id() == avcodec.AV_CODEC_ID_H264) {
//                    isH264 = true;
//                    break;
//                }
//            }
//        }
//
//        avformat.avformat_close_input(formatContext);
//        return isH264;
//    }
//}
//
