package com.business.aigc.pika.model.common;

import com.business.aigc.pika.model.req.VideoGenerationRequest;
import lombok.Data;

@Data
public class Options {
    private VideoGenerationRequest.Parameters parameters;//（必传）视频参数
    private Integer frameRate;//（必传）视频帧速率：1 - 24
    private String aspectRatio;//上传视频尺寸："16:9", "9:16", "1:1", "5:2","4:5", "4:3"
    private VideoGenerationRequest.Camera camera;//视频镜头控制

    public String getAspectRatio() {
        return aspectRatio != null && (aspectRatio.equals("16:9")
                || aspectRatio.equals("9:16")
                || aspectRatio.equals("1:1")
                || aspectRatio.equals("5:2")
                || aspectRatio.equals("4:5")
                || aspectRatio.equals("4:3"))? aspectRatio : null;
    }

    public Integer getFrameRate() {
        return frameRate != null && frameRate >= 1 && frameRate <= 24 ? frameRate : null;
    }
}
