package com.business.aigc.pika;

import com.alibaba.fastjson2.JSONObject;
import com.business.aigc.pika.model.AdjustResponse;
import com.business.aigc.pika.model.GetJobResponse;
import com.business.aigc.pika.model.VideoGenerationResponse;
import com.business.aigc.pika.model.common.Options;
import com.business.aigc.pika.model.req.AdjustRequest;
import com.business.aigc.pika.model.req.ExtendRequest;
import com.business.aigc.pika.model.req.VideoGenerationRequest;
import com.business.utils.BUrlUtil;
import com.nacos.utils.BFeiShuUtil;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

//pika http接口工具类
public class PikaHttpUtil {
    private static final Logger log = LoggerFactory.getLogger(PikaHttpUtil.class);

    static String mediaTypeJson = "application/json";

    //pikade 授权放到redis中不再在此维护
//    static String authorization = "Bearer 3e3eaa69-d27d-4cb3-8e85-bf06d6ac3ce3";
    static String  key = "ab316553-2365-43ec-b826-b7db8ea5adc51";
    static String authorization = "Bearer "+key;
    private static OkHttpClient client;

    public static OkHttpClient getInstance() {
        if (client == null) {
            client = new OkHttpClient.Builder()
                    .readTimeout(15, TimeUnit.MINUTES)
                    .writeTimeout(15, TimeUnit.MINUTES)
                    .build();
        }
        return client;
    }

    //文本或可选附件生成视频
    public static VideoGenerationResponse postToGenerate(VideoGenerationRequest videoGenerationRequest,String authorization) {
        if (videoGenerationRequest == null){
            return null;
        }
        Options options = videoGenerationRequest.getOptions();
        if (options == null || options.getParameters() == null || options.getFrameRate() == null){
            return null;
        }
        VideoGenerationRequest.Parameters parameters = videoGenerationRequest.getOptions().getParameters();
        if (parameters == null || parameters.getMotion() == null || parameters.getGuidanceScale() == null){
            return null;
        }
        JSONObject json = JSONObject.from(videoGenerationRequest);
        RequestBody body = RequestBody.create(json.toString(), MediaType.parse(mediaTypeJson));
        for (int attempt = 1; attempt <= 2; attempt++) {
            Request request = new Request.Builder()
                    .url("https://api.pikapikapika.io/web/generate")
                    .addHeader("Accept", "application/json")
                    .addHeader("Authorization", "Bearer " + authorization)
                    .post(body)
                    .build();
            OkHttpClient client = getInstance();
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("response: {}", response);
                log.warn("皮卡视频 code: {}", response.code());
                log.info("皮卡视频 body: {}", responseBody);
                if (response.isSuccessful()) {
                    return JSONObject.parseObject(responseBody, VideoGenerationResponse.class);
                }else {
                    if (attempt == 2) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"pika生成视频失败","ERRORInfo="+ responseBody+" param="+json.toString());
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            }  catch (Exception e) {
                log.error("Exception occurred: {}", e.getMessage(), e);
                if (attempt == 2) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"pika生成视频失败","ERRORInfo="+ e.getMessage());
                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                    return null;
                }
            }
        }
        return null;
    }

    //调整：局部和拓展
    public static AdjustResponse postToAdjust(AdjustRequest adjustRequest,String authorization) {
        if (adjustRequest == null || adjustRequest.getMode() == null){
            return null;
        }
        //局部调整
        if (adjustRequest.getMode().equals("inpainting")){
            if (adjustRequest.getOptions() == null || adjustRequest.getVideo() == null || adjustRequest.getArea() == null) {
                return null;
            }

        }
        //拓展视频
        if (adjustRequest.getMode().equals("outpainting")){
            if (adjustRequest.getPromptText() == null || adjustRequest.getOptions() == null || adjustRequest.getOptions().getAspectRatio() == null || adjustRequest.getVideo() == null || adjustRequest.getArea() == null){
                return null;
            }
            adjustRequest.setStyle(null);
        }
        JSONObject json = JSONObject.from(adjustRequest);
        RequestBody body = RequestBody.create(json.toString(), MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://api.pikapikapika.io/web/adjust")
                .addHeader("Accept", "application/json")
                .addHeader("Authorization", "Bearer "+authorization)
                .post(body)
                .build();
        try {
            OkHttpClient client = getInstance();
            Response response = client.newCall(request).execute();
            log.info("response:{}", response);
            if (response.body() != null) {
                String responseBody = response.body().string();
                log.info("responseBody:{}",responseBody);
                return JSONObject.parseObject(responseBody, AdjustResponse.class);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    //延长视频
    public static VideoGenerationResponse postToExtend(ExtendRequest extendRequest,String authorization) {
        if (extendRequest == null || extendRequest.getOptions() == null || extendRequest.getVideo() == null){
            return null;
        }
        JSONObject json = JSONObject.from(extendRequest);
        RequestBody body = RequestBody.create(json.toString(), MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://api.pikapikapika.io/web/extend")
                .addHeader("Accept", "application/json")
                .addHeader("Authorization","Bearer "+ authorization)
                .post(body)
                .build();
        try {
            OkHttpClient client = getInstance();
            Response response = client.newCall(request).execute();
            log.info("response:{}", response);
            if (response.body() != null) {
                String responseBody = response.body().string();
                System.out.println("==皮卡延迟视频responseBody:"+responseBody);
                return JSONObject.parseObject(responseBody, VideoGenerationResponse.class);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    //获取jobId信息
    public static GetJobResponse getToJobInfo(String jobId ,String authorization) {
        if (jobId == null){
            return null;
        }
        Request request = new Request.Builder()
                .url("https://api.pikapikapika.io/web/jobs/"+jobId)
                .addHeader("Accept", "application/json")
                .addHeader("Authorization","Bearer "+ authorization)
                .build();

        OkHttpClient client = getInstance();
            try (Response response = client.newCall(request).execute()) {
                log.info("response: {}", response);
                log.warn("Unexpected response code: {}", response.code());
                if (response.code() == 200) {
                    String responseBody = response.body().string();
                    System.out.println("皮卡视频任务获取responseBody: " + responseBody);
                    return JSONObject.parseObject(responseBody, GetJobResponse.class);
                }else if(response.code() == 429){
                    GetJobResponse gp = new GetJobResponse();
                    List<GetJobResponse.Video> videos = new ArrayList<>();
                    GetJobResponse.Video video = new GetJobResponse.Video();
                    video.setStatus("queued");
                    videos.add(video);
                    gp.setVideos(videos);
                    return gp;
                }else{
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"pika拉取任务失败","ERRORInfo="+ BUrlUtil.respErrorInfoByCode(response.code())+" jobId="+jobId);
                }
            } catch (Exception e) {
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"pika拉取任务失败","ERRORInfo="+ e.getMessage()+" jobId="+jobId);
                log.error("Exception occurred: {}", e.getMessage(), e);

            }
        return null;
    }

    public static void main(String[] args) {
        String job = "f05f5960-8571-4f70-997e-7e39c0a76f24";
        String token = "9acbb81c-894f-4d2d-a6f5-35e71b4c18dc";

        GetJobResponse toJobInfo = getToJobInfo(job, token);
        log.info("toJobInfo:{}", toJobInfo);
    }

}
