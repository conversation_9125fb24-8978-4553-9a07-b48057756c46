package com.business.aigc.pika.model;

import lombok.Data;

import javax.swing.*;
import java.util.List;

@Data
public class GetJobResponse {

    private Job job;
    private List<Video> videos;

    @Data
    public static class Job {
        private String requestType;
        private Boolean upscaled;
        private Boolean adjusted;
        private String pikaJobId;
        private String id;
        private String accountId;
        private Params params;
        private String promptText;
        private Boolean extended;
        private String createdAt;
    }

    @Data
    public static class Params {
        private Boolean sfx;
        private Spring styleId;
        private Options options;
        private String userId;
        private String promptText;
    }

    @Data
    public static class Options {
        private String aspectRatio;
        private Integer frameRate;
        private Camera camera;
        private Parameters parameters;
        private Boolean extend;
    }

    @Data
    public static class Camera {
        private String rotate;
        private String zoom;
        private String tilt;
        private String pan;
    }

    @Data
    public static class Parameters {
        private Integer motion;
        private Integer guidanceScale;
        private String negativePrompt;
        private Double seed;
    }

    @Data
    public static class Video {
        private String jobId;
        private String pikaVideoId;
        private String accountId;
        private String id;
        private Double duration;
        private Double feedback;
        private Long seed;
        private Double progress;
        private String resultUrl;
        private String imageThumb;
        private String videoPoster;
        private String status;
    }

}
