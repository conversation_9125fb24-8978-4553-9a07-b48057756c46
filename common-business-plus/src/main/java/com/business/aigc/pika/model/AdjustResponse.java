package com.business.aigc.pika.model;

import lombok.Data;

@Data
public class AdjustResponse {

    private Job job;
    private Video video;

    @Data
    public static class Job {
        private String id;
        private Boolean adjusted;
        private String accountId;
        private Double extended;
        private String pikaJobId;
        private Params params;
        private String promptText;
        private String requestType;
        private Boolean upscaled;
        private Object createdAt;
    }

    @Data
    public static class Params {
        private Options options;
        private String userId;
        private String promptText;
        private String video;//视频拓展时返回
    }

    @Data
    public static class Options {
        private String aspectRatio;
        private Double frameRate;
        private Camera camera;
        private Parameters parameters;
        private Boolean extend;
    }

    @Data
    public static class Camera {
        private String rotate;
        private String zoom;
        private String tilt;
        private String pan;
    }

    @Data
    public static class Parameters {
        private Double motion;
        private Double guidanceScale;
        private String negativePrompt;
        private Double seed;
    }

    @Data
    public static class Video {
        private String accountId;
        private String id;
        private String jobId;
        private String status;
        private String pikaVideoId;
    }

}
