package com.business.aigc.pika.model.req;

import com.business.aigc.pika.model.common.Area;
import com.business.aigc.pika.model.common.Options;
import lombok.Data;

@Data
public class AdjustRequest {

    private String promptText;// 视频提示词
    private Options options;//（必传）
    private String video;//视频仅支持：mp4
    private String mode;//图片仅支持：png, jpeg, webp, gif
    private Area area;//是否启用音效
    private String style;//One of 'Anime', 'Moody', '3D', 'Watercolor', 'Natural', 'Claymation', 'Black & white'
    private String ref;//参考内容
    private String webhookOverride;//返回默认的webhook推送接口

    public String getStyle() {
        return style != null && (style.equals("Anime") || style.equals("Moody") || style.equals("3D") || style.equals("Watercolor") || style.equals("Natural") || style.equals("Claymation") || style.equals("Black & white")) ? style : null;
    }

    public String getVideo() {
        return video != null && video.endsWith(".mp4") ? video : null;
    }

    public String getMode() {
        return mode !=null && (mode.equals("inpainting") || mode.equals("outpainting")) ? mode : null;
    }
}
