package com.business.aigc.pika.model.req;

import com.business.aigc.pika.model.common.Options;
import lombok.Data;

@Data
public class VideoGenerationRequest {
    private String promptText;// 视频提示词
    private Options options;//（必传）
    private String video;//视频仅支持：mp4
    private String image;//图片仅支持：png, jpeg, webp, gif
    private Boolean sfx;//是否启用音效
    private String style;//One of 'Anime', 'Moody', '3D', 'Watercolor', 'Natural', 'Claymation', 'Black & white'
    private String ref;//参考内容
    private String webhookOverride;//返回默认的webhook推送接口

    public String getStyle() {
        return style != null && (style.equals("Anime") || style.equals("Moody") || style.equals("3D") || style.equals("Watercolor") || style.equals("Natural") || style.equals("Claymation") || style.equals("Black & white")) ? style : null;
    }

    public Boolean getSfx() {
        return sfx != null && sfx;
    }

    /*public String getImage() {
        return image != null && (image.endsWith(".png") || image.endsWith(".jpeg") || image.endsWith(".webp") || image.endsWith(".gif")) ? image : null;
    }*/

    public String getVideo() {
        return video != null && (video.endsWith(".mp4") || video.endsWith(".MP4")) ? video : null;
    }

    @Data
    public static class Parameters {
        private Integer motion;//（必传）视频的运动强度值：必须在1-4之间。
        private Integer guidanceScale;//（必传）与视频文本值的一致性 5-25
        private String negativePrompt;//反向提示词
        private Long seed;// 视频的种子值

        public Integer getMotion() {
            return motion != null && motion >= 1 && motion <= 4 ? motion : null;
        }

        public Integer getGuidanceScale() {
            return guidanceScale != null && guidanceScale >= 5 && guidanceScale <= 25 ? guidanceScale : null;
        }
    }

    @Data
    public static class Camera {//镜头控制
        private String zoom;//值：in 或 out
        private String pan;// "left" or "right"
        private String tilt;// "up" or "down"
        private String rotate;// "cw" (Clockwise：顺时针) or "ccw" (Counter Clockwise：逆时针)

        public String getZoom() {
            return zoom != null && (zoom.equals("in") || zoom.equals("out")) ? zoom : null;
        }

        public String getPan() {
            return pan != null && (pan.equals("left") || pan.equals("right")) ? pan : null;
        }

        public String getTilt() {
            return tilt != null && (tilt.equals("up") || tilt.equals("down")) ? tilt : null;
        }

        public String getRotate() {
            return rotate != null && (rotate.equals("cw") || rotate.equals("ccw")) ? rotate : null;
        }
    }

}
