package com.business.aigc.tusiart;

import lombok.Getter;

@Getter
public enum TusiJobStateEnum {
    DEFAULT("DEFAULT"),//默认
    CREATED("CREATED"),//已创建
    PENDING("PENDING"),//待执行
    RUNNING("RUNNING"),//执行中
    CANCELED("CANCELED"),//已取消
    SUCCESS("SUCCESS"),//成功
    FAILED("FAILED"),//失败
    WAITING("WAITING"),//等待

    ;

    private final String state;

    TusiJobStateEnum(String state) {
        this.state = state;
    }

}
