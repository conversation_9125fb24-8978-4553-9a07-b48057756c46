package com.business.aigc.tusiart;

import com.alibaba.fastjson2.JSONObject;
import com.business.aigc.tusiart.model.CreateWorkflowDTO;
import com.business.aigc.tusiart.model.JobInfoStateVO;
import com.business.aigc.tusiart.model.JobResultVO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
public class TensorArtHttpUtil {

    static String mediaTypeJson = "application/json";
    private static OkHttpClient client;
    private static OkHttpClient getInstance() {
        if (client == null) {
            client = new OkHttpClient.Builder()
                    .readTimeout(15, TimeUnit.MINUTES)
                    .writeTimeout(15, TimeUnit.MINUTES)
                    .build();
        }
        return client;
    }


    //创建工作流
    protected static boolean postsToGenerateJob(CreateWorkflowDTO createWorkflowDTO, String token) {
        if (Objects.isNull(createWorkflowDTO) || Objects.isNull(token) || Objects.isNull(createWorkflowDTO.getRequestId()) || Objects.isNull(createWorkflowDTO.getParams())){
            return false;
        }
        JSONObject json = JSONObject.from(createWorkflowDTO);
        String requestBody = json.toString();
        RequestBody body = RequestBody.create(requestBody, MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://cn.tensorart.net/v1/jobs/workflow")
                .post(body)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Content-Type", "application/json; charset=UTF-8")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                System.out.println(responseBody);
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                jsonObject = jsonObject.getJSONObject("job");
                return Objects.equals(jsonObject.getString("status"),"CREATED");
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    protected static JobResultVO getToGenerateJobInfo(String jobId, String token) {
        if (jobId == null || jobId.isEmpty()){
            return null;
        }
        Request request = new Request.Builder()
                .url("https://cn.tensorart.net/v1/jobs/" + jobId)
                .get()
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Content-Type", "application/json; charset=UTF-8")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            log.info("response:{}",response);
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                System.out.println(responseBody);
                return JSONObject.parseObject(responseBody, JobResultVO.class);
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }


    //创建工作流
    protected static boolean postsToGenerateJobImgToImg(CreateWorkflowDTO createWorkflowDTO, String token) {
        if (Objects.isNull(createWorkflowDTO) || Objects.isNull(token) || Objects.isNull(createWorkflowDTO.getRequestId()) || Objects.isNull(createWorkflowDTO.getParams())){
            return false;
        }
        JSONObject json = JSONObject.from(createWorkflowDTO);
        String requestBody = json.toString();
        RequestBody body = RequestBody.create(requestBody, MediaType.parse(mediaTypeJson));
        Request request = new Request.Builder()
                .url("https://cn.tensorart.net/v1/jobs/workflow")
                .post(body)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Content-Type", "application/json; charset=UTF-8")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                System.out.println(responseBody);
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                jsonObject = jsonObject.getJSONObject("job");
                return Objects.equals(jsonObject.getString("status"),"CREATED");
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }


    protected static String getResourceId(String imageHttpUrl, String token) {
        try {
            // 获取OkHttpClient实例
            OkHttpClient client = getInstance();
            RequestBody requestImgUrlBody = RequestBody.create(
                    "{\"expireSec\":\"3600\"}",
                    MediaType.parse("application/json; charset=UTF-8")
            );
            // 构建请求
            Request requestImgUrl = new Request.Builder()
                    .url("https://cn.tensorart.net/v1/resource/image")
                    .post(requestImgUrlBody)
                    .addHeader("Authorization", "Bearer "+token)
                    .addHeader("Content-Type", "application/json; charset=UTF-8")
                    .build();
            Response responseImgUrl = client.newCall(requestImgUrl).execute();
            JSONObject jsonObject = JSONObject.parseObject(responseImgUrl.body().string());

            log.info("获取请求信息 {}", jsonObject);

            String resourceId = jsonObject.getString("resourceId");
            String putUrl = jsonObject.getString("putUrl");
            JSONObject headers = jsonObject.getJSONObject("headers");

            byte[] imageBytes = downloadImage(client, imageHttpUrl);
            RequestBody requestBody = RequestBody.create(imageBytes, MediaType.parse("application/octet-stream"));
            Request.Builder requestBuilder = new Request.Builder()
                    .url(putUrl)
                    .put(requestBody);
            for (String key : headers.keySet()) {
                requestBuilder.addHeader(key, headers.getString(key));
            }
            Response response = client.newCall(requestBuilder.build()).execute();
            System.out.println("+111111"+response);
            System.out.println("+222333"+response.body().string());
            if (response.isSuccessful()){
                return resourceId;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    private static byte[] downloadImage(OkHttpClient client, String imageUrl) throws IOException {
        Request request = new Request.Builder().url(imageUrl).build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
            try (InputStream inputStream = response.body().byteStream()) {
                return inputStream.readAllBytes();
            }
        }
    }


    //创建模版工作流
    protected static JobInfoStateVO postsToGenerateJobTemplateImgToImg(JSONObject jsonObjectTemplate, String token) {
        RequestBody body = RequestBody.create(jsonObjectTemplate.toString(), MediaType.parse(mediaTypeJson));
        log.info("请求模版工作流:{}",jsonObjectTemplate);
        Request request = new Request.Builder()
                .url("https://cn.tensorart.net/v1/jobs/workflow/template")
                .post(body)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Content-Type", "application/json; charset=UTF-8")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            log.info("response模版工作流:{}",response);
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                System.out.println(responseBody);
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                jsonObject = jsonObject.getJSONObject("job");
                return JSONObject.parseObject(jsonObject.toString(), JobInfoStateVO.class);
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }




}
