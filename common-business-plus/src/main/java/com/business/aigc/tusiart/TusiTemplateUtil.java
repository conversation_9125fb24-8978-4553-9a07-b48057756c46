package com.business.aigc.tusiart;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.util.Objects;

public class TusiTemplateUtil {
    //装载迪士尼参数信息
    protected static JSONObject getTemplateImg2Img(String requestId, Integer templateId, String imageId) {
        if (!Objects.equals(TusiTemplateEnum.DISHINI_TEMPLATE.getTemplateId(), templateId)
                && !Objects.equals(TusiTemplateEnum.XIANGSU_TEMPLATE.getTemplateId(), templateId)
                && !Objects.equals(TusiTemplateEnum.NIANTU_TEMPLATE.getTemplateId(), templateId)
        ){
            return null;
        }
        // 创建 JSON 数据
        JSONObject json = new JSONObject();
        json.put("requestId", requestId);
        json.put("templateId", TusiTemplateEnum.getTemplateKey(templateId));
        JSONObject field1 = new JSONObject();
        field1.put("nodeId", "22");
        field1.put("fieldName", "image");
        field1.put("fieldValue", imageId); // 设置实际值
        JSONArray fieldAttrs = new JSONArray();
        fieldAttrs.add(field1);
        JSONObject fields = new JSONObject();
        fields.put("fieldAttrs", fieldAttrs);
        json.put("fields", fields);
        return json;
    }

}
