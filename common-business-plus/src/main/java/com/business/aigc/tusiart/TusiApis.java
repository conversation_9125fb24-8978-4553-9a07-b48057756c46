package com.business.aigc.tusiart;

import com.alibaba.fastjson2.JSONObject;
import com.business.aigc.tusiart.model.JobInfoStateVO;
import com.business.aigc.tusiart.model.JobResultVO;
import com.business.aigc.tusiart.model.TemplateWorkflowDTO;

import java.util.Objects;

public class TusiApis {

    //
    private static final String TUSI_API_TOKEN = "50ee9619e9184a31afd63f4470e51dfa";

    // 图片转图片:风格化
    public static JobInfoStateVO postImgToImg(TemplateWorkflowDTO templateWorkflowDTO) {
        if (templateWorkflowDTO == null || Objects.isNull(templateWorkflowDTO.getRequestId()) || Objects.isNull(templateWorkflowDTO.getTemplateId()) || Objects.isNull(templateWorkflowDTO.getImgUrl())){
            return null;
        }
        if (TusiTemplateEnum.getTemplateKey(templateWorkflowDTO.getTemplateId()) == null){
            return null;
        }
        String imageId = TensorArtHttpUtil.getResourceId(templateWorkflowDTO.getImgUrl(),TUSI_API_TOKEN);
        if (Objects.isNull(imageId)){
            return null;
        }
        JSONObject parameters = TusiTemplateUtil.getTemplateImg2Img(templateWorkflowDTO.getRequestId(), templateWorkflowDTO.getTemplateId(), imageId);
        if (parameters == null){
            return null;
        }
        return TensorArtHttpUtil.postsToGenerateJobTemplateImgToImg(parameters, TUSI_API_TOKEN);
    }

    //获取绘图信息
    public static JobResultVO getToGenerateJobInfo(String jobId){
        return TensorArtHttpUtil.getToGenerateJobInfo(jobId, TUSI_API_TOKEN);
    }
}
