package com.business.aigc.tusiart;

import lombok.Getter;

@Getter
public enum TusiTemplateEnum {

    DISHINI_TEMPLATE(1,"737972847162575814","迪士尼风格"),
    NIANTU_TEMPLATE(2,"738009702276986506","粘土风格"),
    XIANGSU_TEMPLATE(3,"737992488048046324","像素风格"),

    ;
    private final Integer templateId;
    private final String templateKey;
    private final String templateName;

    TusiTemplateEnum(Integer templateId, String templateKey, String templateName) {
        this.templateId = templateId;
        this.templateKey = templateKey;
        this.templateName = templateName;
    }

    public static String getTemplateKey(Integer templateId) {
        for (TusiTemplateEnum tusiTemplateEnum : TusiTemplateEnum.values()) {
            if (tusiTemplateEnum.getTemplateId().equals(templateId)) {
                return tusiTemplateEnum.getTemplateKey();
            }
        }
        return null;
    }

}
