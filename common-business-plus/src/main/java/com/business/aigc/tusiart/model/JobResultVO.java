package com.business.aigc.tusiart.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class JobResultVO {

    @JSONField(name = "job")
    private Job job;

    @Data
    public static class Job {

        @J<PERSON>NField(name = "id")
        private String id;

        @JSONField(name = "status")
        private String status;

        @JSONField(name = "credits")
        private double credits;

        @JSONField(name = "successInfo")
        private SuccessInfo successInfo;

    }

    @Data
    public static class SuccessInfo {

        @JSONField(name = "images")
        private List<Image> images;

    }

    @Data
    public static class Image {

        @JSONField(name = "id")
        private String id;

        @JSONField(name = "url")
        private String url;

        @JSO<PERSON>ield(name = "expiredIn")
        private String expiredIn;

    }

}

