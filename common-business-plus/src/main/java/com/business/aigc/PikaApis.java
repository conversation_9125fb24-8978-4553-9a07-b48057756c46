package com.business.aigc;

import com.alibaba.fastjson2.JSON;
import com.business.aigc.pika.PikaHttpUtil;
import com.business.aigc.pika.model.GetJobResponse;
import com.business.aigc.pika.model.VideoGenerationResponse;
import com.business.aigc.pika.model.common.Options;
import com.business.aigc.pika.model.req.VideoGenerationRequest;
import com.business.enums.BRedisKeyEnum;
import com.business.model.bo.pika.ImgVideoBO;
import com.business.model.po.ImgDrawRecordPO;
import com.business.utils.BOssUtil;
import com.google.common.util.concurrent.RateLimiter;
import com.nacos.config.OssClientConfig;
import com.nacos.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class PikaApis {

    private static final RateLimiter rateLimiter = RateLimiter.create(1.0);
    // TODO 反向词过滤-固定值
    private final static String negativePrompt = "bad quality, distorted, deformation, oversaturated, morphing, erratic fluctuation in motion, noisy, poorly drawn, blurry, grainy, resolution, lack of detail, inconsistent lighting";

    public static String postTextAndFileToVideo(ImgVideoBO imgVideoBO) {
        VideoGenerationRequest videoGenerationRequest = new VideoGenerationRequest();
        videoGenerationRequest.setSfx(false);
        videoGenerationRequest.setPromptText(imgVideoBO.getPrompt());
        videoGenerationRequest.setVideo(imgVideoBO.getVideo());
        videoGenerationRequest.setImage(imgVideoBO.getImage() != null ? imgVideoBO.getImage().concat("?x-oss-process=image/format,png") : null);
        videoGenerationRequest.setStyle(imgVideoBO.getStyle());
        Options options = new Options();
        options.setFrameRate(imgVideoBO.getFrameRate());
        VideoGenerationRequest.Parameters parameters = new VideoGenerationRequest.Parameters();
        parameters.setMotion(imgVideoBO.getMotion());
        parameters.setGuidanceScale(imgVideoBO.getGuidanceScale());
        parameters.setNegativePrompt((imgVideoBO.getNegativeprompt() == null ? negativePrompt : imgVideoBO.getNegativeprompt()));
        options.setParameters(parameters);
        options.setAspectRatio(imgVideoBO.getAspectRatio());
        VideoGenerationRequest.Camera camera = new VideoGenerationRequest.Camera();
        camera.setZoom(imgVideoBO.getZoom());
        camera.setPan(imgVideoBO.getPan());
        camera.setTilt(imgVideoBO.getTilt());
        camera.setRotate(imgVideoBO.getRotate());
        options.setCamera(camera);
        videoGenerationRequest.setOptions(options);
        log.info("==**==提交皮卡视频参数= {}", videoGenerationRequest);
        String token = RedisUtil.getValue(BRedisKeyEnum.REDIS_PIKA_TOKEN_KEY.getKey());
        VideoGenerationResponse videoResponse = PikaHttpUtil.postToGenerate(videoGenerationRequest,token);
        if (videoResponse != null && videoResponse.getVideo() != null) {
            return videoResponse.getVideo().getJobId();
        }
        return null;
    }

    public static String getToJobInfo(ImgDrawRecordPO imgDrawRecordPO, String domainName) {
        rateLimiter.acquire();
        String token = RedisUtil.getValue(BRedisKeyEnum.REDIS_PIKA_TOKEN_KEY.getKey());
        // 你的实际逻辑
        try {
            GetJobResponse getJobResponse = PikaHttpUtil.getToJobInfo(imgDrawRecordPO.getVideoJobId(),token);
            log.info("PIKA获取视频: {}" , getJobResponse);
            if (getJobResponse != null && getJobResponse.getVideos() != null ) {
                GetJobResponse.Video video = getJobResponse.getVideos().get(0);
                if (video.getStatus().equalsIgnoreCase("finished")) {
                    List<String> stringList = new ArrayList<>();
                    stringList.add(uploadPiKaVideo(video.getVideoPoster(), imgDrawRecordPO.getVideoJobId(), 13, OssClientConfig.FILE_SUFFIX, domainName));
                    imgDrawRecordPO.setInitImgUrls(JSON.toJSONString(stringList));
                    return uploadPiKaVideo(video.getResultUrl(), imgDrawRecordPO.getVideoJobId(), 14, OssClientConfig.FILE_SUFFIX_VIDEO, domainName);
                } else if (video.getStatus().equalsIgnoreCase("queued")) {
                    return "running";
                } else if (video.getStatus().equalsIgnoreCase("pending")) {
                    return "running";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    // TODO 上传视频到oss
    public static String uploadPiKaVideo(String fileUrl, String videoJobId, Integer folder, String suffix, String domainName) {
        if (fileUrl == null) return null;
        int maxRetries = 4; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String updatedUrl = fileUrl.replace("https://cdn.pika.art", domainName);
                log.info("皮卡修改后的上传域名地址= ｛｝"+ updatedUrl);
                String videoPath = BOssUtil.uploadURL(updatedUrl, videoJobId, folder, suffix);
                if (videoPath != null) {
                    return videoPath;
                }
                log.error("皮卡视频上传失败，重试次数：" + (retryCount + 1) + videoJobId);
            } catch (Exception e) {
                log.error("皮卡上传图片到OSS发生异常，重试次数：" + (retryCount + 1) + e.getMessage());
            }
            retryCount++;
        }
        log.error("皮卡文件上传失败，超过最大重试次数， 视频id=", videoJobId);
        return null;
    }

}
