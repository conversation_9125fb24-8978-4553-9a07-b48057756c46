package com.business.aigc.mj.web.model;


import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class MjJsonParam {

    @SerializedName("channelName")
    private String channelName;

    @SerializedName("flags")
    private Flags flags;

    @SerializedName("jobType")
    private String jobType;

    @SerializedName("parameters")
    private Parameters parameters;

    @SerializedName("prompt")
    private String prompt;

    @SerializedName("isMobile")
    private Boolean isMobile;

    @Data
    public static class Flags {
        @SerializedName("mode")
        private String mode;

        @SerializedName("private")
        private Boolean isPrivate;
    }

    @Data
    public static class Parameters {
        @SerializedName("ar")
        private String ar;

        @SerializedName("version")
        private String version;

        @SerializedName("stylize")
        private Integer stylize;

        @SerializedName("style")
        private String style;
    }

}
