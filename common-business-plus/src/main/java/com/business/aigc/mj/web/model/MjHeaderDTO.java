package com.business.aigc.mj.web.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MjHeaderDTO {

    private String ar;

    private String channelId;

    private String cookie;

    private String speed;

    private String jobId; //web

    private String jobType;

    private Integer index;

    private Integer operate;

    private Integer location;

    public MjHeaderDTO() {}

    public MjHeaderDTO(String channelId, String cookie, String speed) {
        this.channelId = channelId;
        this.cookie = cookie;
        this.jobType = "imagine";
        this.speed = speed;
    }

    public MjHeaderDTO(String channelId, String cookie, String speed, String jobId, String jobType, Integer index) {
        this.channelId = channelId;
        this.cookie = cookie;
        this.speed = speed;
        this.jobId = jobId;
        this.jobType = jobType;
        this.index = index;
    }

    public MjHeaderDTO(String channelId, String cookie, String speed, String jobId, String jobType, Integer index, Integer operate, Integer location) {
        this.channelId = channelId;
        this.cookie = cookie;
        this.speed = speed;
        this.jobId = jobId;
        this.jobType = jobType;
        this.index = index;
        this.operate = operate;
        this.location = location;
    }

    public MjHeaderDTO(String channelId, String cookie, String speed, String jobId, String jobType, Integer index, Integer operate) {
        this.channelId = channelId;
        this.cookie = cookie;
        this.speed = speed;
        this.jobId = jobId;
        this.jobType = jobType;
        this.index = index;
        this.operate = operate;
    }

}
