package com.business.aigc.mj.web;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.ByteString;
import org.jetbrains.annotations.NotNull;

@Slf4j
public class WebSocketClient {

    private final OkHttpClient client = new OkHttpClient();

    public void run() {
        Request request = new Request.Builder()
                .url("https://ws.midjourney.com/ws?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZTEyNjE2ODUtMDAzZi00MGRiLTk1NWEtNmI1ZWExYTE2MDQ4IiwidXNlcm5hbWUiOiJtaWRkMDUiLCJpYXQiOjE3MTg2MDU4NDl9.-VEYiWt2ZN1WWxIOF5PfmfctiTiLF3CnXdmQzlHPwm8")
                .addHeader("Connection", "Upgrade")
                .addHeader("Pragma", "no-cache")
                .addHeader("Cache-Control", "no-cache")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .addHeader("Upgrade", "websocket")
                .addHeader("Origin", "https://www.midjourney.com")
                .addHeader("Sec-WebSocket-Version", "13")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .addHeader("Cookie", "AMP_MKTG_437c42b22c=JTdCJTdE; _ga=GA1.1.1349380607.1718522574; _gcl_au=1.1.1447368201.1718522575; cf_clearance=_Y3PBLevbXiLfacPdPuIshjHf2_ueVZHcFf1tnogAk0-1718604718-*******-nI_.7nSx0roGiMvaSHlSsIbv_xMZEtU56TyR7QHWT2N.yH_IcqBS4RxwerF03hGD2rhesGxakmgVrjI0taGBmw; _ga_Q0DQ5L7K0D=GS1.1.1718605469.6.1.1718606005.0.0.0; __cf_bm=3UvQuDRCJdg9KqjeUrABDHFlTQzNbxpJuV5Vi_yeVP4-1718606664-*******-B3UUc34ogylMIxSer.ORTVewKm3lInPFRfRjd_arcFdF20.rR50Vn2RCcQ7l1PphBrt2qE_ZzBFXhvp85BE2tg; AMP_437c42b22c=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjIyN2JiNGMwZC05Mzk3LTRhYzQtYWI3Ny0xZTExMmM3MWRhYmQlMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjJlMTI2MTY4NS0wMDNmLTQwZGItOTU1YS02YjVlYTFhMTYwNDglMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzE4NjA0NzE3NTk3JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTcxODYwNzA4NTU5MSUyQyUyMmxhc3RFdmVudElkJTIyJTNBMTE3JTdE")
                .addHeader("Sec-WebSocket-Key", "WxnFEl+TAFU/DlbfrXVNSA==")
                .addHeader("Sec-WebSocket-Extensions", "permessage-deflate; client_max_window_bits")
                .build();

        WebSocketListener listener = new WebSocketListener() {
            @Override
            public void onOpen(@NotNull WebSocket webSocket, Response response) {
                System.out.println("WebSocket Opened: " + response.message());
            }

            @Override
            public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
                System.out.println("Received message: " + text);
            }

            @Override
            public void onMessage(@NotNull WebSocket webSocket, ByteString bytes) {
                System.out.println("Received bytes: " + bytes.hex());
            }

            @Override
            public void onClosing(WebSocket webSocket, int code, @NotNull String reason) {
                System.out.println("Closing WebSocket: " + code + " / " + reason);
                webSocket.close(1000, null);
            }

            @Override
            public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, Response response) {
                log.error("WebSocket Failure: {}", t.getMessage(), t);
            }
        };
        client.newWebSocket(request, listener);
        client.dispatcher().executorService().shutdown();
    }

    public static void main(String... args) {
        new WebSocketClient().run();
    }
}
