package com.business.aigc.mj.web.model;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class CookieData {
    private Map<String, String> cookies = new HashMap<>();

    public void addCookie(String name, String value) {
        this.cookies.put(name, value);
    }

    public String getCookie(String name) {
        return this.cookies.get(name);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : cookies.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("; ");
        }
        if (!sb.isEmpty()) {
            sb.setLength(sb.length() - 2);
        }
        return sb.toString();
    }

    public static CookieData fromString(String cookieString) {
        CookieData cookieData = new CookieData();
        String[] cookiePairs = cookieString.split("; ");
        for (String pair : cookiePairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                cookieData.addCookie(keyValue[0], keyValue[1]);
            }
        }
        return cookieData;
    }
}
