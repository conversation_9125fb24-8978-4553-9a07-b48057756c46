package com.business.aigc.mj.web.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

//mj token实体
@Data
public class MjTokenDTO {

    private String type;
    private Cookie cookie;
    private Extra extra;

    @Data
    public static class Cookie {
        private String type;
        private String idToken;
        private String refreshToken;
    }

    @Data
    public static class Extra {
        @JSONField(name = "enrollInWebCreate")
        private boolean enrollInWebCreate;
    }
}
