package com.business.aigc.mj.web.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class MjResponse {
    private List<Success> success;
    private List<Failure> failure;

    @Data
    public static class Success {

        @J<PERSON><PERSON>ield(name = "job_id")
        private String jobId;

        private String prompt;
        @JSO<PERSON>ield(name = "is_queued")
        private boolean isQueued;

        @JSONField(name = "event_type")
        private String eventType;
        private Meta meta;
        private int optimisticJobIndex;

        @Data
        public static class Meta {
            private int height;
            private int width;
            @J<PERSON>NField(name = "batch_size")
            private int batchSize;
        }
    }

    @Data
    public static class Failure {

        @J<PERSON><PERSON>ield(name = "type")
        private String type;

        @J<PERSON><PERSON>ield(name = "message")
        private String message;

        @JSO<PERSON>ield(name = "optimisticJobIndex")
        private Integer optimisticJobIndex;
    }
}
