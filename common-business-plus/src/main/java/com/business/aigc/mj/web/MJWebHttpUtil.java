package com.business.aigc.mj.web;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.business.aigc.mj.web.model.*;
import com.nacos.enums.ImgOptModelEnum;
import com.nacos.mjapi.model.JobStatusBO;
import com.nacos.tool.BrotliInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

//mj web 接口工具
@Slf4j
public class MJWebHttpUtil {
    static final String host = "https://nijijourney.com";
    // static final String host = "https://mj.iworks.cn";
    static final String host_mj = "https://www.midjourney.com";
    static final String host_mj_editor = "https://editor.midjourney.com";
    private static OkHttpClient client;
    private static OkHttpClient getInstance() {
        if (client == null) {
            client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .connectTimeout(60, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS)
                    .cache(null)
                    .build();
        }
        return client;
    }

    public static MjResponse postCreateImage(MjRequest mjRequest, String cookie) {
        // 请求头信息
        Headers headers = new Headers.Builder()
                .add("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Microsoft Edge\";v=\"126\"")
                .add("Content-Type", "application/json")
                .add("x-csrf-protection", "1")
                .add("sec-ch-ua-mobile", "?0")
                .add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .add("sec-ch-ua-platform", "\"Windows\"")
                .add("Accept", "*/*")
                .add("Origin", host)
                .add("Sec-Fetch-Site", "same-origin")
                .add("Sec-Fetch-Mode", "cors")
                .add("Sec-Fetch-Dest", "empty")
                .add("Referer", host + "/")
                .add("Accept-Encoding", "gzip, deflate, br, zstd")
                .add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .add("Cookie", cookie)
                .build();
        String jsonData = JSON.toJSONString(mjRequest);
        RequestBody body = RequestBody.create(
                jsonData, MediaType.parse("application/json; charset=utf-8"));
        Request request = new Request.Builder()
                .url(host + "/api/app/submit-jobs")
                .headers(headers)
                .post(body)
                .build();

        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("Mj-Web请求code：{}", Optional.of(response.code()));
            log.info("Mj-Web请求结果：{}", responseBody);
            if (response.isSuccessful()) {
                return JSONObject.parseObject(responseBody, MjResponse.class);
            } else if (response.code() == 403 || responseBody.contains("Unauthorized")) {
                MjResponse mjResponse = new MjResponse();
                MjResponse.Failure failure = new MjResponse.Failure();
                failure.setType("forbidden_error");
                failure.setMessage("Unauthorized");
                mjResponse.setFailure(List.of(failure));
                return mjResponse;
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    public static MjResponse postCreateImageNew(MjRequest mjRequest, String cookie, Integer operate) {
        System.out.println("==*== 操作类型为= "+operate);
        String jsonObjectParam = null;
        if (operate == null){
            return null;
        }
        else if (operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_DRAW.getValue()) {
            jsonObjectParam = MJWebHttpUtil.textToImageSubmitParam(mjRequest);
        }
        else if (operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_REMIX_SUBTLE.getValue()
            || operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_REMIX.getValue()) { //微调
            jsonObjectParam = MJWebHttpUtil.remixSubmitParam(mjRequest);//微调
        }
        else if (operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_VARY_REGION.getValue()) { //局部修改
            jsonObjectParam = MJWebHttpUtil.repaintSubmitParam(mjRequest);//局部修改
        }
        else if (operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_SUBTLE.getValue()
            || operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_STRONG.getValue()) { //高变化/低变化
            jsonObjectParam = MJWebHttpUtil.subtleOrStrongSubmitParam(mjRequest);//高变化/低变化
        }
        else if (operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_2X.getValue()
            || operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_4X.getValue()
            || operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_2X_SUBTLE.getValue()
            || operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_2X_CREATIVE.getValue()) {
            jsonObjectParam = MJWebHttpUtil.highMagnificationSubmitParam(mjRequest);
        }
        else if (operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_ZOOM_CUSTOM.getValue()) { //缩放
            jsonObjectParam = MJWebHttpUtil.zoomSubmitParam(mjRequest);// 缩放2x--参数
        }
        else if (operate == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_ZOOM_CHANGE_AR.getValue()) { //Change Aspect Ratio改变纵横比
            if (mjRequest.getLocation() != null && mjRequest.getLocation() == 2) { //居中
                jsonObjectParam = MJWebHttpUtil.zoomSubmitParam(mjRequest);// 缩放2x--参数
            } else {
                jsonObjectParam = MJWebHttpUtil.panSubmitParam(mjRequest);// 上下左右平移--参数
            }
        }
        if (jsonObjectParam == null) {
            return null;
        }
        System.out.println("Mj-Web请求参数：" + jsonObjectParam);

        Headers headers = new Headers.Builder()
                .add("Accept", "*/*")
                .add("Accept-Encoding", "gzip, deflate, br, zstd")
                .add("Accept-Language", "zh-CN,zh;q=0.9")
                .add("Content-Type", "application/json")
                .add("Cookie", cookie)
                .add("Origin", host)
                .add("priority", "u=1, i")
                .add("Referer", host + "/imagine")
                .add("Sec-Ch-Ua", "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"138\", \"Chromium\";v=\"138\"")
                .add("Sec-Ch-Ua-Mobile", "?0")
                .add("Sec-Ch-Ua-Platform", "\"Windows\"")
                .add("Sec-Fetch-Dest", "empty")
                .add("Sec-Fetch-Mode", "cors")
                .add("Sec-Fetch-Site", "same-origin")
                .add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .add("X-Csrf-Protection", "1")
                .build();
        Request request = new Request.Builder()
                .url(host+"/api/submit-jobs")
                .headers(headers)
                .post(RequestBody.create(jsonObjectParam, MediaType.parse("application/json; charset=utf-8")))
                .build();

        OkHttpClient client = MJWebHttpUtil.getInstance();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("Mj-Web请求code：{}", Optional.of(response.code()));
            log.info("Mj-Web请求结果：{}", responseBody);
            if (response.isSuccessful()) {
                return JSONObject.parseObject(responseBody, MjResponse.class);
            } else if (response.code() == 403 || responseBody.contains("Unauthorized")) {
                MjResponse mjResponse = new MjResponse();
                MjResponse.Failure failure = new MjResponse.Failure();
                failure.setType("forbidden_error");
                failure.setMessage("Unauthorized");
                mjResponse.setFailure(List.of(failure));
                return mjResponse;
            }
        } catch (Exception e) {
            log.error("Mj-Web请求异常：{}", e.getMessage());
            return null;
        }
        return null;
    }

    //获取任务状态信息
    public static String postJobState(List<String> jobIdList, String cookie) {
        //请求列表
        if (jobIdList == null || jobIdList.isEmpty()){
            return null;
        }
        JSONObject jobObject = new JSONObject();
        JSONArray jobArray = new JSONArray();
        jobArray.addAll(jobIdList);
        jobObject.put("jobIds", jobArray);
        Headers headers = new Headers.Builder()
                .add("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Microsoft Edge\";v=\"126\"")
                .add("Content-Type", "application/json")
                .add("x-csrf-protection", "1")
                .add("sec-ch-ua-mobile", "?0")
                .add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .add("sec-ch-ua-platform", "\"Windows\"")
                .add("Accept", "*/*")
                .add("Origin", host)
                .add("Sec-Fetch-Site", "same-origin")
                .add("Sec-Fetch-Mode", "cors")
                .add("Sec-Fetch-Dest", "empty")
                .add("Referer", host + "/")
                .add("Accept-Encoding", "gzip, deflate, br, zstd")
                .add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .add("Cookie", cookie)
                .build();
        String jsonData = JSON.toJSONString(jobObject);
        RequestBody body = RequestBody.create(
                jsonData, MediaType.parse("application/json; charset=utf-8"));
        Request request = new Request.Builder()
                .url(host + "/api/app/job-status")
                .headers(headers)
                .post(body)
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("getJobStatus responseCode: {}", response.code());
            if (response.isSuccessful() && response.body() != null) {
                log.info("getJobStatus responseBody: {}", responseBody);
                log.info("getJobStatus response: {}", response);
                return responseBody;
            } else if (response.code() == 403 || responseBody.contains("Unauthorized")) {
                return "forbidden_error";
            }
        } catch (Exception e) {
            if (e instanceof SocketTimeoutException){
                log.error("请求超时异常: MJAPIUtil.getJobStatus: {}",e.getMessage());
                return null;
            }
            log.error("MJAPIUtil.getJobStatus: {}",e.getMessage(), e);
            return null;
        }
        return null;
    }

    protected static MjUploadRes mjUploadMaskToGoogle(MjUploadDTO mjUploadDTO, String cookie){
        // 请求头信息
        Headers headers = new Headers.Builder()
                .add("Content-Length", String.valueOf(JSON.toJSONString(mjUploadDTO).getBytes().length))
                .add("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Microsoft Edge\";v=\"126\"")
                .add("Content-Type", "application/json")
                .add("x-csrf-protection", "1")
                .add("sec-ch-ua-mobile", "?0")
                .add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .add("sec-ch-ua-platform", "\"Windows\"")
                .add("Accept", "application/json")
                .add("Origin", "https://editor.midjourney.com")
                .add("Sec-Fetch-Site", "same-origin")
                .add("Sec-Fetch-Mode", "cors")
                .add("Sec-Fetch-Dest", "empty")
                .add("Referer", "https://www.midjourney.com/home")
                .add("Accept-Encoding", "gzip, deflate, br")
                .add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .add("Priority", "u=1, i")
                .add("Cookie", cookie)
                .build();
        // 请求数据
        String jsonData = JSON.toJSONString(mjUploadDTO);
        RequestBody body = RequestBody.create(
                jsonData, MediaType.parse("application/json; charset=utf-8"));
        Request request = new Request.Builder()
                .url(host_mj_editor+"/web/api/upload-mask")
                .headers(headers)
                .post(body)
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("mjUploadMaskToGoogle responseCode: {}", response.code());
            log.info("mjUploadMaskToGoogle responseBody: {}", responseBody);
            if (response.isSuccessful()) {
                return JSONObject.parseObject(responseBody, MjUploadRes.class);
            }
        } catch (Exception e) {
            if (e instanceof SocketTimeoutException){
                log.error("请求超时异常: MJAPIUtil.mjUploadMaskToGoogle: {}",e.getMessage());
            } else {
                log.error("MJAPIUtil.mjUploadMaskToGoogle: {}",e.getMessage(), e);
            }
            return null;
        }
        return null;
    }

    protected static boolean setUserRank(String jsbId, Integer index, String cookie){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("value", 1);
        jsonObject.put("jobId", jsbId);
        jsonObject.put("gridIndex", index);
        jsonObject.put("sr", true);
        Headers headers = new Headers.Builder()
                .add("Accept", "*/*")
                .add("Accept-Encoding", "gzip, deflate, br, zstd")
                .add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .add("Content-Type", "application/json")
                .add("Cookie", cookie)
                .add("Origin", host)
                .add("Priority", "u=1, i")
                .add("Referer", host+"/jobs/"+ jsbId +"?index="+ index)
                .add("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Microsoft Edge\";v=\"126\"")
                .add("Sec-Ch-Ua-Arch", "\"x86\"")
                .add("Sec-Ch-Ua-Bitness", "\"126.0.2592.68\"")
                .add("Sec-Ch-Ua-Full-Version", "\"126.0.2592.68\"")
                .add("Sec-Ch-Ua-Full-Version-List", "\"Not/A)Brand\";v=\"*******\", \"Chromium\";v=\"126.0.6478.114\", \"Microsoft Edge\";v=\"126.0.2592.68\"")
                .add("Sec-Ch-Ua-Mobile", "?0")
                .add("Sec-Ch-Ua-Model", "\"\"")
                .add("Sec-Ch-Ua-Platform", "\"Windows\"")
                .add("Sec-Ch-Ua-Platform-Version", "\"15.0.0\"")
                .add("Sec-Fetch-Dest", "empty")
                .add("Sec-Fetch-Mode", "cors")
                .add("Sec-Fetch-Site", "same-origin")
                .add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .add("X-Csrf-Protection", "1")
                .build();
        RequestBody body = RequestBody.create(JSON.toJSONString(jsonObject), MediaType.parse("application/json; charset=utf-8"));
        Request request = new Request.Builder()
                .url(host+"/api/app/user-rank")
                .headers(headers)
                .post(body)
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            log.info("setUserRank responseCode: {}", response.code());
            if (response.isSuccessful() && response.body() != null) {
                MjUserRankRes mjUserRankRes = JSONObject.parseObject(response.body().string(), MjUserRankRes.class);
                if (mjUserRankRes != null && mjUserRankRes.getSuccess()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("MJAPIUtil.setUserRank: {}",e.getMessage(), e);
            return false;
        }
        return false;
    }

    protected static String getToken(MjTokenDTO mjTokenDTO, String cookie){
        // 请求头信息
        Headers headers = new Headers.Builder()
                .add("Content-Length", "1439")
                .add("sec-ch-ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Microsoft Edge\";v=\"126\"")
                .add("Content-Type", "application/json")
                .add("x-csrf-protection", "1")
                .add("sec-ch-ua-mobile", "?0")
                .add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .add("sec-ch-ua-platform", "\"Windows\"")
                .add("Accept", "*/*")
                .add("Origin", "https://www.midjourney.com")
                .add("Sec-Fetch-Site", "same-origin")
                .add("Sec-Fetch-Mode", "cors")
                .add("Sec-Fetch-Dest", "empty")
                .add("Referer", "https://www.midjourney.com/home")
                .add("Accept-Encoding", "gzip, deflate, br")
                .add("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .add("Priority", "u=1, i")
                .add("Cookie", cookie)
                .build();
        // 请求数据
        String jsonData = JSON.toJSONString(mjTokenDTO);
        RequestBody body = RequestBody.create(
                jsonData, MediaType.parse("application/json; charset=utf-8"));
        Request request = new Request.Builder()
                .url(host+"/api/auth/firebase-login")
                .headers(headers)
                .post(body)
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return response.headers().get("__Host-Midjourney.AuthUserToken");
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    /*public static void main(String[] args) {
        String cookie = "AMP_MKTG_437c42b22c=JTdCJTdE; _ga=GA1.1.1107113351.1718614850; _gcl_au=1.1.1320823074.1718614851; __stripe_mid=6d86acdb-d128-4639-b230-72cf42e2ec0caa0d3b; __cf_bm=rKRwAUPzpEcMDxhVWK9_6DNL_mjdVFb9nqyup8_hNFA-1723447670-*******-QpFHuSnOuWxalCaOioo8kxt0JqvgoyCKxZnGIYq.951rCq6zUiIwdwUcWaC81ZIeU6Q9mr9WjByUVYvgYJHlag; __Host-Midjourney.AuthUserToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KXuvMN7RBuh1Tyn1aVbiJeBtzRajnO-g3DAAZbfWAQM; cf_clearance=m91endGGUyp8._A4Ma0lYvYjhQPYAYQqGA4iXJljIsc-1723447675-*******-FRMpB56uRHktbspIWM8bViUzBksVPDxHlGCoirUDimqwzwAaTk2qRCP0iAsOqmazTD2ggYx_eQIdVRPtPUDdVA; __stripe_sid=a5f2d7de-58c2-4967-930e-a3b43428d6edf767a5; _ga_Q0DQ5L7K0D=GS1.1.1723447673.63.1.1723447733.0.0.0; AMP_437c42b22c=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI2NTJkZmRlNS1kMzdiLTQyNzctOWNiOS04MmFmMzJhMWU5ZTclMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjJkZDE1YWI1ZC05NzkwLTQ2ODMtYjA0NS04ZTNlZGFhZGMwN2QlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzIzNDQ3NjczOTMxJTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTcyMzQ0NzczOTg2OSUyQyUyMmxhc3RFdmVudElkJTIyJTNBMjE4MCU3RA==";
        byte[] tempFile = MJWebHttpUtil.urlToBytes();
        System.out.println(uploadFileToMidjourney(tempFile, cookie));
    }*/

    // 获取图片字节数组
    public static byte[] urlToBytes(String imgUrl) {
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder()
                .url(imgUrl)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                return response.body().bytes();
            }
            log.info("Failed to fetch data: " + response.message());
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    public static String uploadFileToMidjourney(byte[] imageBytes, String cookie) throws Exception{
        try {
            String prefix = UUID.randomUUID().toString().concat(MJWebHttpUtil.fileNameRandomString());
            RequestBody fileBody = RequestBody.create(imageBytes, MediaType.parse("application/octet-stream"));
            RequestBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", prefix.concat(".png"), fileBody) // 修改文件名为实际文件名
                    .build();
            Request request = new Request.Builder()
                    .url(host + "/api/storage/upload_file")
                    .post(requestBody)
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                    .addHeader("Cookie", cookie)
                    .addHeader("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundaryCMGMLw72C6DgzwLA")
                    .addHeader("Content-Length", String.valueOf(requestBody.contentLength()))
                    .addHeader("Origin", host)
                    .addHeader("Referer", host)
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-origin")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .addHeader("X-Csrf-Protection", "1")
                    .build();
            OkHttpClient client = getInstance();
            try (Response response = client.newCall(request).execute()) {
                log.info("uploadFileToMidjourney response code: {}",response.code());
                String responseBody = response.body().string();
                log.info("uploadFileToMidjourney response message: {}",responseBody);
                if (response.isSuccessful() && response.body() != null){
                    return responseBody.replaceAll("^\"|\"$", "");
                }
            } catch (Exception e){
                log.error("mj图生文图片上传失败_2,{}",e.getMessage(),e);
                return null;
            }
        } catch (Exception e){
            log.error("mj图生文图片上传失败_1,{}",e.getMessage(),e);
            return null;
        }
        return null;
    }
    private static String fileNameRandomString() {
        SecureRandom secureRandom = new SecureRandom();
        byte[] randomBytes = new byte[64]; // 64位随机数占用8个字节
        secureRandom.nextBytes(randomBytes);
        StringBuilder stringBuilder = new StringBuilder();
        for (byte b : randomBytes) {
            stringBuilder.append(String.format("%02x", b));
        }
        return stringBuilder.toString();
    }

    /**
     * 初始化JobStatusBO
     * @param jsonObject 参数内容
     */
    public static JobStatusBO createJobStatusBO(JSONObject jsonObject) {
        log.info("针对不同的返回信息进行处理 =====> {}", jsonObject);
        JobStatusBO jobStatusBO = new JobStatusBO();
        jobStatusBO.setUserId(jsonObject.getString("user_id"));
        jobStatusBO.setUsername(jsonObject.getString("username"));
        jobStatusBO.setJobId(jsonObject.getString("id"));
        jobStatusBO.setCurrentStatus(jsonObject.getString("current_status"));
        jobStatusBO.setFullCommand(jsonObject.getString("full_command"));

        List<String> imagePathsList = getStrings(jobStatusBO);
        jobStatusBO.setImagePaths(imagePathsList);

        jobStatusBO.setJobType(jsonObject.getString("job_type"));
        jobStatusBO.setBatchSize(jsonObject.getInteger("batch_size"));
        jobStatusBO.setEventWidth(jsonObject.getInteger("width"));
        jobStatusBO.setEventHeight(jsonObject.getInteger("height"));
        return jobStatusBO;
    }

    private static @NotNull List<String> getStrings(JobStatusBO jobStatusBO) {
        List<String> imagePathsList = new ArrayList<>();
        imagePathsList.add("https://storage.googleapis.com/dream-machines-output/"+ jobStatusBO.getJobId()+"/0_0.png");
        imagePathsList.add("https://storage.googleapis.com/dream-machines-output/"+ jobStatusBO.getJobId()+"/0_1.png");
        imagePathsList.add("https://storage.googleapis.com/dream-machines-output/"+ jobStatusBO.getJobId()+"/0_2.png");
        imagePathsList.add("https://storage.googleapis.com/dream-machines-output/"+ jobStatusBO.getJobId()+"/0_3.png");
        return imagePathsList;
    }

    // 绘图
    public static String textToImageSubmitParam(MjRequest mjRequest) {
        JSONObject jsonObject = new JSONObject();
        if (mjRequest.getFlags() != null) {
            jsonObject.put("f", MJWebHttpUtil.getFObject(mjRequest.getFlags().getMode(), mjRequest.getFlags().isPrivate()));
        }
        jsonObject.put("channelId", mjRequest.getChannelId());
        jsonObject.put("roomId", null);
        if (mjRequest.getMetadata() != null) {
            jsonObject.put("metadata", MJWebHttpUtil.getMetadataObject(mjRequest.getMetadata().getImagePrompts(),
                    mjRequest.getMetadata().getImageReferences(), mjRequest.getMetadata().getCharacterReferences(),
                    mjRequest.getMetadata().getIsMobile(), mjRequest.getMetadata().getDepthReferences(),
                    mjRequest.getMetadata().getLightboxOpen()));
        }
        jsonObject.put("t", mjRequest.getJobType());
        jsonObject.put("prompt", mjRequest.getPrompt());
        return JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteNulls);
    }

    // 上下左右平移--参数
    public static String panSubmitParam(MjRequest mjRequest) {
        JSONObject jsonObject = MJWebHttpUtil.mjPublicSubmitParam(mjRequest);
        jsonObject.put("newPrompt", mjRequest.getNewPrompt());
        jsonObject.put("direction", mjRequest.getPanDirection());
        jsonObject.put("fraction", mjRequest.getPanFraction());
        jsonObject.put("stitch", mjRequest.getStitch());
        return JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteNulls);
    }

    // 缩放2x--参数
    public static String zoomSubmitParam(MjRequest mjRequest) {
        JSONObject jsonObject = MJWebHttpUtil.mjPublicSubmitParam(mjRequest);
        jsonObject.put("newPrompt", mjRequest.getNewPrompt());
        jsonObject.put("zoomFactor", mjRequest.getZoomFactor());
        return JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteNulls);
    }

    // 微调（高/低变化）--参数
    public static String remixSubmitParam(MjRequest mjRequest) {
        JSONObject jsonObject = MJWebHttpUtil.mjPublicSubmitParam(mjRequest);
        jsonObject.put("strong", mjRequest.getStrong());
        jsonObject.put("newPrompt", mjRequest.getNewPrompt());
        return JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteNulls);
    }

    // 局部修改--参数
    public static String repaintSubmitParam(MjRequest mjRequest) {
        if (mjRequest.getMetadata().getImagePrompts() == null) {
            mjRequest.getMetadata().setImagePrompts(0);
        }
        if (mjRequest.getMetadata().getImageReferences() == null) {
            mjRequest.getMetadata().setImageReferences(0);
        }
        if (mjRequest.getMetadata().getCharacterReferences() == null) {
            mjRequest.getMetadata().setCharacterReferences(0);
        }
        JSONObject jsonObject = MJWebHttpUtil.mjPublicSubmitParam(mjRequest);
        jsonObject.put("newPrompt", mjRequest.getNewPrompt());
        if (mjRequest.getFrame() != null) {
            jsonObject.put("frame", MJWebHttpUtil.getFrameObject(mjRequest.getFrame().getWidth(), mjRequest.getFrame().getHeight()));
        }
        if (mjRequest.getParent() != null) {
            jsonObject.put("parent", MJWebHttpUtil.getParentObject(mjRequest.getParent().getWidth(), mjRequest.getParent().getHeight(),
                    mjRequest.getParent().getX(), mjRequest.getParent().getY()));
        }
        jsonObject.put("alphaMask", mjRequest.getAlphaMask());
        return JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteNulls);
    }


    // 高变化/低变化--参数
    public static String subtleOrStrongSubmitParam(MjRequest mjRequest) {
        JSONObject jsonObject = MJWebHttpUtil.mjPublicSubmitParam(mjRequest);
        jsonObject.put("strong", mjRequest.getStrong());
        return JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteNulls);
    }

    // 2X/4X/高清变化/低变化--参数
    public static String highMagnificationSubmitParam(MjRequest mjRequest) {
        JSONObject jsonObject = MJWebHttpUtil.mjPublicSubmitParam(mjRequest);
        jsonObject.put("type", mjRequest.getType());
        return JSON.toJSONString(jsonObject, JSONWriter.Feature.WriteNulls);
    }
    public static JSONObject mjPublicSubmitParam(MjRequest mjRequest) {
        JSONObject jsonObject = new JSONObject();
        if (mjRequest.getFlags() != null) {
            jsonObject.put("f", MJWebHttpUtil.getFObject(mjRequest.getFlags().getMode(), mjRequest.getFlags().isPrivate()));
        }
        jsonObject.put("channelId", mjRequest.getChannelId());
        jsonObject.put("roomId", null);
        if (mjRequest.getMetadata() != null) {
            jsonObject.put("metadata", MJWebHttpUtil.getMetadataObject(mjRequest.getMetadata().getImagePrompts(),
                    mjRequest.getMetadata().getImageReferences(), mjRequest.getMetadata().getCharacterReferences(),
                    mjRequest.getMetadata().getIsMobile(), mjRequest.getMetadata().getDepthReferences(),
                    mjRequest.getMetadata().getLightboxOpen()));
        }
        jsonObject.put("t", mjRequest.getJobType());
        jsonObject.put("id", mjRequest.getId());
        jsonObject.put("index", mjRequest.getIndex());
        return jsonObject;
    }
    public static JSONObject getFObject(String mode, boolean isPrivate) {
        JSONObject fObject = new JSONObject();
        fObject.put("mode", mode);
        fObject.put("private", isPrivate);
        return fObject;
    }
    private static JSONObject getMetadataObject(Integer imagePrompts, Integer imageReferences, Integer characterReferences){
        return getMetadataObject(imagePrompts, imageReferences, characterReferences, null, 0, null);
    }

    private static JSONObject getMetadataObject(Integer imagePrompts, Integer imageReferences, Integer characterReferences,
                                               Boolean isMobile, Integer depthReferences, Boolean lightboxOpen){
        JSONObject metadataObject = new JSONObject();
        metadataObject.put("isMobile", isMobile);
        metadataObject.put("imagePrompts", imagePrompts);
        metadataObject.put("imageReferences", imageReferences);
        metadataObject.put("characterReferences", characterReferences);
        metadataObject.put("depthReferences", depthReferences);
        metadataObject.put("lightboxOpen", lightboxOpen);
        return metadataObject;
    }
    private static JSONObject getFrameObject(Integer width, Integer height){
        JSONObject frameObject = new JSONObject();
        frameObject.put("width", width);
        frameObject.put("height", height);
        return frameObject;
    }
    private static JSONObject getParentObject(Integer width, Integer height, Integer x, Integer y){
        JSONObject parentObject = new JSONObject();
        parentObject.put("width", width);
        parentObject.put("height", height);
        parentObject.put("x", x);
        parentObject.put("y", y);
        return parentObject;
    }


}
