package com.business.aigc.mj.web.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.Map;

@Data
public class MjRequest {

    private String prompt;
    private String newPrompt; //局部修改
    private Frame frame; //局部修改
    private Parent parent; //局部修改
    private String alphaMask; //局部修改

    @JSONField(name = "f")
    private Flags flags;
    @JSONField(name = "t")
    private String jobType;

    private String type; // 2x/4x/低/高

    private Boolean strong;

    private String id;
    private Integer index;
    private String roomId;
    private String channelId;
    private Metadata metadata;
    private Integer zoomFactor;

    @J<PERSON>NField(name = "direction")
    private Integer panDirection;
    @JSONField(name = "fraction")
    private Double panFraction;
    private Boolean stitch;

    private Integer location;

    @Data
    public static class Flags {
        private String mode;
        @JSONField(name = "private")
        private boolean isPrivate;

    }

    @Data
    public static class Metadata {
        private Integer imagePrompts;
        private Integer imageReferences;
        private Integer characterReferences;
        private Boolean isMobile;
        private Integer depthReferences;
        private Boolean lightboxOpen;
    }

    @Data
    public static class Frame {
        private Integer width;
        private Integer height;
    }

    @Data
    public static class Parent {
        private Integer width;
        private Integer height;
        private Integer x;
        private Integer y;
    }

}
