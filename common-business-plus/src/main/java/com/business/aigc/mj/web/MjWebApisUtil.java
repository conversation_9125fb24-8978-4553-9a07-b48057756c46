package com.business.aigc.mj.web;

import com.alibaba.fastjson2.JSONObject;
import com.business.aigc.mj.web.model.*;
import com.nacos.enums.ImgOptModelEnum;
import com.nacos.mjapi.MJParameterEnum;
import com.nacos.mjapi.model.FailureJobInfo;
import com.nacos.mjapi.model.JobInfo;
import com.nacos.mjapi.model.MJAccountHeaderBO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MjWebApisUtil {

    private static final Pattern URL_PATTERN = Pattern.compile("https?://[^\\s]+");

    private static final Map<Integer, String> UPSCALE_MAP = new HashMap<>();

    static {
        UPSCALE_MAP.put(ImgOptModelEnum.OPERATE_EDIT_VARY_SUBTLE.getValue(), "vary");
        UPSCALE_MAP.put(ImgOptModelEnum.OPERATE_EDIT_VARY_STRONG.getValue(), "vary");
        UPSCALE_MAP.put(ImgOptModelEnum.OPERATE_EDIT_VARY_REGION.getValue(), "repaint");
        UPSCALE_MAP.put(ImgOptModelEnum.OPERATE_EDIT_ZOOM.getValue(), "outpaint");
        UPSCALE_MAP.put(ImgOptModelEnum.OPERATE_EDIT_FINE_TUNING_REMIX.getValue(), "remix");
        UPSCALE_MAP.put(ImgOptModelEnum.OPERATE_EDIT_FINE_TUNING_REMIX_SUBTLE.getValue(), "remix");
    }

    private static final List<String> SCALE_LIST = Collections.unmodifiableList(Arrays.asList(
            "1:3", "1:2", "9:16", "2:3", "3:4", "5:6",
            "1:1", "6:5", "4:3", "3:2", "16:9", "2:1", "3:1"
    ));

    public static MjTokenDTO getMjTokenDTO(String tokenText) {
        return JSONObject.parseObject(tokenText, MjTokenDTO.class);
    }

    public static String getUseCookies(String cookieStr, String userToken) {
        CookieData cookieData = CookieData.fromString(cookieStr);
        cookieData.addCookie("__Host-Midjourney.AuthUserToken", userToken);
        return cookieData.toString();
    }

    //获取token信息
    public static String getToken(MjTokenDTO mjTokenDTO, String cookieStr) {
        return MJWebHttpUtil.getToken(mjTokenDTO, "__cf_bm=4AruLHEnVmzN07YPhmtfhHU.WfGDB_F9C4WYFGxuoLY-1718522692-*******-2sp2vNQwHxzEGJrXx0aHpBHp5Oyht0z8FjmRBQwO7.g3gDcWctCgC8eN_jeM6zSzflG.Kh_GEx2h5SKdx4Yybw; AMP_MKTG_437c42b22c=JTdCJTdE; cf_clearance=GqISGPVFnNxGm2TDdf_AE0aY2PrQ3.9yju_h5eXNfJ0-1718522699-*******-jbEXS9UMYAB83HZGgSikefaNJFASLy2DNmGR_0Vrue0zgbxBtVQcUBi.dfAStwbX9U4BWCEftpT36.__SpntLw; _gcl_au=1.1.1852474272.1718522702; _ga=GA1.1.583642145.1718522702; darkMode=disabled; __stripe_mid=c8e13c72-95fc-4d5c-9889-954c44d08915051b46; __stripe_sid=e9d81e99-b86b-4c44-8b68-edd601dd33bb60a793; _ga_Q0DQ5L7K0D=GS1.1.1718522702.1.1.1718523029.0.0.0; AMP_437c42b22c=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjIxOGEyYjJjMS1jYmI2LTQzOTYtOThlNy1mNmJjOTlkYTM4YjklMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjIxODc1NGJmNy0wMTdlLTQzZGMtYTQ0Zi03N2E5MjM4NjEwMTIlMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzE4NTIyNjk3NjA1JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTcxODUyMzAyOTM2OCUyQyUyMmxhc3RFdmVudElkJTIyJTNBMjAlN0Q=");
    }

    public static String processCommands(List<String> commands) {
        List<String> srefCommands = new ArrayList<>();
        List<String> otherCommands = new ArrayList<>();
        for (String command : commands) {
            if (command.startsWith("--sref")) {
                srefCommands.add(command);
            } else {
                otherCommands.add(command);
            }
        }

        List<String> result = new ArrayList<>();
        result.addAll(srefCommands);
        result.addAll(otherCommands);
        return String.join(" ", result);
    }

    private static String initPrompt(String versionNumber, String ar, String mjStyle, int mjStylizeValue, String webPadmapRole, List<String> mjStyleCode, StringBuilder promptStr) {
        // promptStr.append(" ").append(MJParameterEnum.PROMPT_CHAOS.getStrValue()).append(" ");
        if (!ar.contains("F")) {
            promptStr.append(MJParameterEnum.PROMPT_SIZE.getStrValue()).append(" ").append(ar).append(" ");
        }
        if (mjStyle != null) {
            promptStr.append(MJParameterEnum.PROMPT_STYLE.getStrValue()).append(" ").append(mjStyle).append(" ");
        }
        if (webPadmapRole != null && !webPadmapRole.isEmpty()) {
            String roleCommand = (mjStyleCode != null && !mjStyleCode.isEmpty())
                    ? processCommand(webPadmapRole, mjStyleCode).concat(" ")
                    : webPadmapRole;
            promptStr.append(roleCommand);
        } else {
            if (mjStyleCode != null && !mjStyleCode.isEmpty()) {
                promptStr.append(processCommands(mjStyleCode)).append(" ");
            }
        }
        if (mjStylizeValue != 0) {
            promptStr.append(MJParameterEnum.PROMPT_STYLIZE_1.getStrValue()).append(" ").append(mjStylizeValue).append(" ");
        }
        if (versionNumber != null) {
            promptStr.append(versionNumber);
        }
        return promptStr.toString();
    }

    private static String initPromptEditor(String versionNumber, String ar, String newPromptStr) {
        return newPromptStr + " " + MJParameterEnum.PROMPT_SIZE.getStrValue() + " " + ar + " " +
                (versionNumber == null ? "" : versionNumber);
    }

    public static String buildCommand(String baseCommand, List<String> params) {
        StringBuilder commandBuilder = new StringBuilder(baseCommand);
        StringBuilder srefBuilder = new StringBuilder();
        StringBuilder personalizeBuilder = new StringBuilder();

        for (String param : params) {
            if (param.startsWith("--sref")) {
                srefBuilder.append(" ").append(param);
            } else if (param.startsWith("--personalize")) {
                personalizeBuilder.append(" ").append(param);
            }
        }
        commandBuilder.append(srefBuilder).append(personalizeBuilder);
        return commandBuilder.toString();
    }

    public static String processCommand(String baseCommand, List<String> options) {
        if (baseCommand.contains("sref")) {
            Map<String, List<String>> optionValues = new LinkedHashMap<>();
            for (String option : options) {
                String[] tokens = option.trim().split("\\s+");
                if (tokens.length > 1) {
                    String optionName = tokens[0];
                    List<String> values = optionValues.getOrDefault(optionName, new ArrayList<>());
                    values.addAll(Arrays.asList(tokens).subList(1, tokens.length));
                    optionValues.put(optionName, values);
                }
            }

            List<String> baseTokens = new ArrayList<>(Arrays.asList(baseCommand.split("\\s+")));

            int srefIndex = baseTokens.indexOf("--sref");
            if (srefIndex != -1 && optionValues.containsKey("--sref")) {
                List<String> srefIDs = optionValues.get("--sref");
                baseTokens.addAll(srefIndex + 1, srefIDs);
            }

            if (optionValues.containsKey("--personalize")) {
                List<String> personalizeIDs = optionValues.get("--personalize");
                baseTokens.add("--personalize");
                baseTokens.addAll(personalizeIDs);
            }
            return String.join(" ", baseTokens);
        } else {
            return buildCommand(baseCommand, options);
        }
    }

    public static List<String> convertCommands(List<String> commands) {
        Map<String, List<String>> commandMap = new HashMap<>();
        commandMap.put("--words", new ArrayList<>());
        commandMap.put("--sref", new ArrayList<>());
        commandMap.put("--personalize", new ArrayList<>());
        for (String command : commands) {
            for (String part : command.split("&")) {
                Integer styleWords = checkString(part);
                if (styleWords == null) {
                    continue;
                }
                if (styleWords == 2) {
                    commandMap.get("--words").add(part);
                    continue;
                }
                String[] tokens = part.trim().split(" ");
                if (tokens.length > 1 && commandMap.containsKey(tokens[0])) {
                    commandMap.get(tokens[0]).add(tokens[1]);
                }
            }
        }

        List<String> results = new ArrayList<>();
        if (!commandMap.get("--sref").isEmpty()) {
            results.add("--sref " + String.join(" ", commandMap.get("--sref")));
        }
        if (!commandMap.get("--personalize").isEmpty()) {
            results.add("--personalize " + String.join(" ", commandMap.get("--personalize")));
        }

        if (!commandMap.get("--words").isEmpty()) {
            results.add("--words&" + commandMap.get("--words").getFirst());
        }
        return results;
    }

    public static Integer checkString(String part) {
        if (part.startsWith("--sref") || part.startsWith("--personalize")) {
            return 1;
        } else if (part.matches("^[\\u4e00-\\u9fa5a-zA-Z0-9 ,，]+$")) {
            return 2;
        }
        return null;
    }

    // 以前的代码备份
    /*public static List<String> convertCommands(List<String> commands) {
        Map<String, List<String>> commandMap = new HashMap<>();

        for (String command : commands) {
            String[] parts = command.split(" ");
            String key = parts[0];
            String value = parts[1];

            commandMap.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
        }

        List<String> result = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : commandMap.entrySet()) {
            String combinedCommand = entry.getKey() + " " + String.join(" ", entry.getValue());
            result.add(combinedCommand);
        }
        return result;
    }*/

    //创建绘画
    public static MjRequestParam createImageRequestParam(MJAccountHeaderBO mjAccountHeaderBO, MjHeaderDTO mjHeaderDTO, MjRequest request) {
        try {
            MjRequestParam mjRequestParam = MjRequestParam.getMjRequestParamObj();
            if (mjAccountHeaderBO == null || mjAccountHeaderBO.getJsonParameter() == null) {
                return null;
            }
            MjJsonParam mjParams = JSONObject.parseObject(mjAccountHeaderBO.getJsonParameter(), MjJsonParam.class);
            if (mjParams == null) {
                return null;
            }

            Integer imagePrompts = mjAccountHeaderBO.getDefImageQua();
            Integer imageReferences = mjAccountHeaderBO.getSrefImageQua();
            Integer characterReferences = mjAccountHeaderBO.getCrefImageQua();

            String newPromptStr = null;
            StringBuilder promptStr = new StringBuilder();
            StringBuilder promptsStr = new StringBuilder();
            if (mjAccountHeaderBO.getWebPadmapDef() != null && !mjAccountHeaderBO.getWebPadmapDef().isEmpty()) {
                promptsStr.append(mjAccountHeaderBO.getWebPadmapDef())
                        .append(mjAccountHeaderBO.getWebParams());
                //.append(mjAccountHeaderBO.getWebParamsIW() == null ? "" : mjAccountHeaderBO.getWebParamsIW());
            } else {
                promptsStr.append(mjAccountHeaderBO.getWebParams());
            }

            newPromptStr = promptsStr.toString();
            promptStr.append(promptsStr);
            if (mjAccountHeaderBO.getWebPadmapRole() != null && !mjAccountHeaderBO.getWebPadmapRole().isEmpty()) {
                promptStr.append(mjAccountHeaderBO.getWebPadmapRole());
            }

            List<String> mjStyleCodes = (mjAccountHeaderBO.getMjStyleCode() != null && !mjAccountHeaderBO.getMjStyleCode().isEmpty() ? convertCommands(mjAccountHeaderBO.getMjStyleCode()) : null);
            if (mjStyleCodes != null) {
                Iterator<String> iterator = mjStyleCodes.iterator();
                while (iterator.hasNext()) {
                    String strStyleCode = iterator.next();
                    if (strStyleCode.contains("--words")) {
                        promptsStr.append("，").append(strStyleCode.substring(strStyleCode.indexOf('&') + 1));
                        iterator.remove();
                        break;
                    }
                }
            }

            String prompts = initPrompt(mjParams.getParameters().getVersion(), mjParams.getParameters().getAr(), mjParams.getParameters().getStyle(),
                    mjParams.getParameters().getStylize(), mjAccountHeaderBO.getWebPadmapRole(), mjStyleCodes, promptsStr);

            if (request == null) {
                request = new MjRequest();
            }

            // log.info("mj 参数字符串: "+prompts);
            MjRequest.Flags flags = new MjRequest.Flags();
            flags.setPrivate(false);
            flags.setMode(mjHeaderDTO.getSpeed());
            if (mjHeaderDTO.getOperate() != null) {
                int operateValue = mjHeaderDTO.getOperate();
                if (operateValue == ImgOptModelEnum.OPERATE_EDIT_VARY_REGION.getValue()) {
                    String newPrompts = initPromptEditor(mjParams.getParameters().getVersion(), mjParams.getParameters().getAr(), newPromptStr);
                    request.setNewPrompt(newPrompts);
                } else if (operateValue == ImgOptModelEnum.OPERATE_EDIT_CHANGE_AR.getValue()) {
                    request.setPrompt(prompts);
                } else {
                    request.setPrompt(prompts);
                }

                if (mjHeaderDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_ZOOM.getValue()) { // zoom
                    request.setPrompt(null);
                    request.setNewPrompt(null);
                } else if (mjHeaderDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_FINE_TUNING_REMIX_SUBTLE.getValue()) { // 微变化
                    request.setStrong(false);
                    request.setPrompt(null);
                    request.setNewPrompt(prompts);
                } else if (mjHeaderDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_FINE_TUNING_REMIX.getValue()) {
                    request.setStrong(true);
                    request.setPrompt(null);
                    request.setNewPrompt(prompts);
                } else if (mjHeaderDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_SUBTLE.getValue()) {
                    request.setStrong(false);
                    imagePrompts = null;
                    imageReferences = null;
                    characterReferences = null;
                    request.setPrompt(null);
                } else if (mjHeaderDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_VARY_STRONG.getValue()) {
                    request.setStrong(true);
                    imagePrompts = null;
                    imageReferences = null;
                    characterReferences = null;
                    request.setPrompt(null);
                } else if (mjHeaderDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_2X.getValue()
                        || mjHeaderDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_UPSCALE_4X.getValue()
                        || mjHeaderDTO.getOperate() == ImgOptModelEnum.OPERATE_EDIT_CHANGE_AR.getValue()) {
                    imagePrompts = null;
                    imageReferences = null;
                    characterReferences = null;
                    request.setPrompt(null);
                    request.setNewPrompt(null);
                }
            } else {
                request.setPrompt(prompts);
            }

            request.setFlags(flags);
            request.setJobType(mjHeaderDTO.getJobType());
            request.setId(mjHeaderDTO.getJobId());
            request.setIndex(mjHeaderDTO.getIndex());

            request.setRoomId(null);
            request.setChannelId(mjHeaderDTO.getChannelId());
            MjRequest.Metadata metadata = new MjRequest.Metadata();
            metadata.setImagePrompts(imagePrompts);
            metadata.setImageReferences(imageReferences);
            metadata.setCharacterReferences(characterReferences);
            request.setMetadata(metadata);

            String oldPrompt = request.getPrompt();
            if (oldPrompt != null && oldPrompt.contains("https://")) {
                try {
                    System.out.println("MJ旧指令= " + oldPrompt);
                    String resultPrompt = replaceURLs(oldPrompt, mjHeaderDTO.getCookie());
                    System.out.println("MJ新的指令= " + resultPrompt);
                    if (!resultPrompt.isEmpty()) {
                        request.setPrompt(resultPrompt);
                    }
                } catch (Exception e) {
                    log.error("MJ替换图片地址失败" + e.getMessage());
                }
            }
            System.out.println("mj最终结果=" + request.getPrompt());
            request.setLocation(mjHeaderDTO.getLocation());
            mjRequestParam.setCookie(mjHeaderDTO.getCookie());
            mjRequestParam.setRequest(request);
            return mjRequestParam;
        } catch (Exception e) {
            log.error("MJ参数转换异常" + e.getMessage());
            return null;
        }
    }

    public static Object postCreateImage(MjRequest request, String cookie, Integer operate) {
        System.out.println("=========================================");
        System.out.println("mj 请求参数-12: " + JSONObject.toJSONString(request));
        System.out.println("=========================================");
        MjResponse mjResponse = MJWebHttpUtil.postCreateImageNew(request, cookie, operate);
        log.info("mj 响应信息: " + JSONObject.toJSONString(mjResponse));
        if (mjResponse == null) {
            return null;
        }
        if (mjResponse.getSuccess() != null && !mjResponse.getSuccess().isEmpty()) {
            return getJobInfo(mjResponse);
        }
        ;
        if (mjResponse.getFailure() != null && !mjResponse.getFailure().isEmpty()) {
            MjResponse.Failure failure = mjResponse.getFailure().getFirst(); // 获取第一个失败对象
            FailureJobInfo failureJobInfo = new FailureJobInfo();
            failureJobInfo.setType(failure.getType());
            failureJobInfo.setMessage(failure.getMessage());
            return failureJobInfo;
        }
        return null;
    }

    @NotNull
    private static JobInfo getJobInfo(MjResponse mjResponse) {
        MjResponse.Success success = mjResponse.getSuccess().getFirst(); // 获取第一个成功对象
        JobInfo jobInfo = new JobInfo();
        jobInfo.setJob_id(success.getJobId());
        jobInfo.setPrompt(success.getPrompt());
        jobInfo.setJob_type(success.getEventType());
        jobInfo.setIs_queued(success.isQueued());
        if (success.getMeta() != null) {
            jobInfo.setWidth(success.getMeta().getWidth());
            jobInfo.setHeight(success.getMeta().getHeight());
            jobInfo.setBatch_size(success.getMeta().getBatchSize());
        }
        return jobInfo;
    }

    public static String postJobState(List<String> jobIdList, MjHeaderDTO mjHeaderDTO) {
        return MJWebHttpUtil.postJobState(jobIdList, mjHeaderDTO.getCookie());
    }

    public static boolean postSetUserRank(String jsbId, Integer index, String cookie) {
        return MJWebHttpUtil.setUserRank(jsbId, index, cookie);
    }

    public static String postMjUploadMask(String maskB64, String jobId, String cookie) {
        MjUploadDTO mjUploadDTO = new MjUploadDTO();
        mjUploadDTO.setImageNum("0");
        mjUploadDTO.setMaskB64(maskB64);
        mjUploadDTO.setJobId(jobId);
        MjUploadRes mjUploadRes = MJWebHttpUtil.mjUploadMaskToGoogle(mjUploadDTO, cookie);
        if (mjUploadRes == null) {
            return null;
        }
        return mjUploadRes.getMaskUrl();
    }

    /**
     * 高变化/地变化
     *
     * @param upscale 原请求内容
     * @return String
     */
    public static String submitJobUpscaleStrong(int upscale) {
        return UPSCALE_MAP.getOrDefault(upscale, null);
    }

    /**
     * 判断放大2倍还是高清细节
     *
     * @param parentJson 原请求内容
     * @param upscale    上级工作id
     * @return String
     */
    public static String submitJobUpscale(String parentJson, int upscale) {
        if (parentJson == null || parentJson.isEmpty()) {
            return null;
        }

        JSONObject jsonObject = JSONObject.parseObject(parentJson);
        JSONObject parameters = jsonObject.getJSONObject("parameters");
        if (parameters == null) {
            return null;
        }

        String version = parameters.getString("version");
        if (version == null || version.isEmpty()) {
            return null;
        }

        // 提取版本前缀
        String versionPrefix;
        if (version.contains("6.1")) {
            versionPrefix = "v6r1";
        } else if (version.contains("6")) {
            versionPrefix = "v6";
        } else {
            versionPrefix = "v5";
        }

        // 根据 upscale 和版本前缀确定结果
        String result = null;
        if (ImgOptModelEnum.OPERATE_EDIT_UPSCALE_2X.getValue() == upscale) {
            if ("v5".equals(versionPrefix)) {
                result = versionPrefix + "_2x";
            } else {
                result = versionPrefix + "_2x_subtle";
            }
        } else if (ImgOptModelEnum.OPERATE_EDIT_UPSCALE_4X.getValue() == upscale) {
            if ("v5".equals(versionPrefix)) {
                result = versionPrefix + "_4x";
            } else {
                result = versionPrefix + "_2x_creative";
            }
        }
        return result;
    }

    /**
     * 1左上； 2居中； 3右下；
     *
     * @param location 原请求内容
     * @return String
     */
    public static String submitJobUpscalePan(int location) {
        if (location == 2) {
            return "outpaint"; // 居中拓展
        }
        return "pan"; // 左右拓展
    }

    public static List<String> getScaleList() {
        return SCALE_LIST;
    }

    // 模拟上传图片，并返回新 URL
    private static String uploadImage(String imgUrl, String cookie) throws Exception {
        byte[] imageBytes = MJWebHttpUtil.urlToBytes(imgUrl);
        if (imageBytes == null) {
            return null;
        }
        return MJWebHttpUtil.uploadFileToMidjourney(imageBytes, cookie);
    }

    // 替换旧 URL
    private static String replaceURLs(String inputPrompt, String cookie) throws Exception {
        Matcher matcher = URL_PATTERN.matcher(inputPrompt);
        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            String oldUrl = matcher.group();
            String newImgUrl = uploadImage(oldUrl, cookie);
            if (newImgUrl == null) {
                break;
            }
            log.info("替换旧 URL：{} -> {}", oldUrl, newImgUrl);
            matcher.appendReplacement(result, Matcher.quoteReplacement(newImgUrl));
        }
        matcher.appendTail(result);
        return result.toString();
    }

}
