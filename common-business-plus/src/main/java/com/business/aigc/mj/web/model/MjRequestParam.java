package com.business.aigc.mj.web.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MjRequestParam {

    @Schema(name = "绘图任务id", type = "Long")
    private Long imgDrawId;

    private Long accountId;

    private Boolean isFastModel;

    @Schema(name = "绘图任务id", type = "String")
    private String cookie;

    @Schema(name = "绘图任务id", type = "Object")
    private MjRequest request;

    private static MjRequestParam mjRequestParamObj;
    private MjRequestParam() {}

    // 公共静态方法，‌用于获取类的唯一实例
    public static MjRequestParam getMjRequestParamObj() {
        if (mjRequestParamObj == null) {
            mjRequestParamObj = new MjRequestParam();
        }
        return mjRequestParamObj;
    }

}
