package com.business.alibailian;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;

public class Md5Utils {

    private static String getFileMd5(String filePath) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("MD5");
        try (InputStream is = Files.newInputStream(Paths.get(filePath))) {
            byte[] buffer = new byte[1024];
            int read;
            while ((read = is.read(buffer)) > 0) {
                digest.update(buffer, 0, read);
            }
        }
        byte[] md5Bytes = digest.digest();

        StringBuilder md5String = new StringBuilder();
        for (byte b : md5Bytes) {
            md5String.append(String.format("%02x", b));
        }

        return md5String.toString();
    }

    public static String getFileMd5(MultipartFile file) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("MD5");
        try (InputStream is = file.getInputStream()) {
            byte[] buffer = new byte[1024];
            int read;
            while ((read = is.read(buffer)) > 0) {
                digest.update(buffer, 0, read);
            }
        }
        byte[] md5Bytes = digest.digest();

        StringBuilder md5String = new StringBuilder();
        for (byte b : md5Bytes) {
            md5String.append(String.format("%02x", b));
        }

        return md5String.toString();
    }

    public static void main(String[] args) throws Exception {

        String filePath = "请替换为您需要上传文档的实际本地路径，例如/Users/<USER>/Desktop/阿里云百炼系列手机产品介绍.docx";
        String md5 = getFileMd5(filePath);

        System.out.println("文档的MD5值为: " + md5);
    }
}
