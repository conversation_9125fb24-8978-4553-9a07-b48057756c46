package com.business.alibailian;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.bailian20231229.AsyncClient;
import com.aliyun.sdk.service.bailian20231229.models.*;
import com.google.gson.internal.LinkedTreeMap;
import com.nacos.result.Result;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedInputStream;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


@Slf4j
public class BaiLianUtil {

//    public static final String ACCESSKEYID = "LTAI5t9A9sRyNs1a2AsDT7Hp";
//    public static final String SECRETACCESSKEY = "******************************";

    public static final String ACCESSKEYID = "LTAI5t8HksBdSfPC16EShgKw";
    public static final String SECRETACCESSKEY = "******************************";
    public static final String WORKSPACE_ID = "llm-a4tgs3iw46v7yvjw";
    // 在类加载时就初始化（翻译）
    private static final AsyncClient client;

    static {
        try {
            StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                    // Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
                    .accessKeyId(ACCESSKEYID)
                    .accessKeySecret(SECRETACCESSKEY)
                    //.securityToken(System.getenv("ALIBABA_CLOUD_SECURITY_TOKEN")) // use STS token
                    .build());

            // Configure the Client
            client = AsyncClient.builder()
                    .region("cn-beijing") // Region ID
                    //.httpClient(httpClient) // Use the configured HttpClient, otherwise use the default HttpClient (Apache HttpClient)
                    .credentialsProvider(provider)
                    //.serviceConfiguration(Configuration.create()) // Service-level configuration
                    // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                    .overrideConfiguration(
                            ClientOverrideConfiguration.create()
                                    // Endpoint 请参考 https://api.aliyun.com/product/bailian
                                    .setEndpointOverride("bailian.cn-beijing.aliyuncs.com")
                            //.setConnectTimeout(Duration.ofSeconds(30))
                    )
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("初始化 Aliyun Client 失败", e);
        }
    }
    // 提供访问实例的公共方法
    public static AsyncClient getClient() {
        return client;
    }

    public static Result<String> createIndex(String indexName) {
        // Parameter settings for API request
        try {
            CreateIndexRequest createIndexRequest = CreateIndexRequest.builder()
                    .workspaceId(WORKSPACE_ID)
                    .name(indexName)
                    .structureType("unstructured")
                    .sinkType("BUILT_IN")
                    .rerankMinScore(0.7)
                    // Request-level configuration rewrite, can set Http request parameters, etc.
                    // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                    .build();

            // Asynchronously get the return value of the API request
            CompletableFuture<CreateIndexResponse> response = client.createIndex(createIndexRequest);
            // Synchronously get the return value of the API request
            CreateIndexResponse resp = response.get();
            log.info("createIndex response:{}", JSONObject.toJSONString(resp));
            if (resp.getBody().getSuccess()){
                return Result.SUCCESS(resp.getBody().getData().getId());
            } else {
                return Result.ERROR(resp.getBody().getMessage());
            }
        } catch (Exception e) {
            log.error("createIndex失败"+e);
            return Result.ERROR("createIndex失败"+e.getMessage());
        }
    }



    public static void main(String[] args) throws Exception {
        System.out.println("return："+addFile("https://cdn.diandiansheji.com/digital/user/1892418480581099522/kownledge/Screenshot_20250529_172558_com.gongfeng.ddsj_flutter_app.jpg",  "849995b5b7f1507825c72436058a5e09", "111"));
    }

    public static Result<String> addFile(String fileUrl, String md5, String indexId) {
        // Parameter settings for API request
        try {
            if (StringUtils.isBlank(fileUrl)) {
                return Result.ERROR("文件URL为空");
            }
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            ApplyFileUploadLeaseRequest applyFileUploadLeaseRequest = ApplyFileUploadLeaseRequest.builder()
                    .categoryId("default")
                    .workspaceId(WORKSPACE_ID)
                    .fileName(fileName)
                    .md5(md5)
                    .sizeInBytes("1000")
                    // Request-level configuration rewrite, can set Http request parameters, etc.
                    // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                    .build();

            // Asynchronously get the return value of the API request
            CompletableFuture<ApplyFileUploadLeaseResponse> response = client.applyFileUploadLease(applyFileUploadLeaseRequest);
            // Synchronously get the return value of the API request
            ApplyFileUploadLeaseResponse resp = response.get();
            log.info("applyFile response:{}", JSONObject.toJSONString(resp));
            if (resp.getBody().getSuccess()){
                String applyId = resp.getBody().getData().getFileUploadLeaseId();
                String appylyUrl = resp.getBody().getData().getParam().getUrl();
                Map headers = (LinkedTreeMap)resp.getBody().getData().getParam().getHeaders();
                uploadFileLink(appylyUrl, fileUrl, headers);
                //添加至阿里云百炼的应用数据
                return AddFile(applyId);
            } else {
                return Result.ERROR(resp.getBody().getMessage());
            }
        } catch (Exception e) {
            log.error("addFile失败"+e);
            return Result.ERROR("addFile失败"+e.getMessage());
        }
    }

    @SneakyThrows
    private static Result<String> AddFile(String applyId) {
        // Parameter settings for API request
        AddFileRequest addFileRequest = AddFileRequest.builder()
                .leaseId(applyId)
                .parser("DASHSCOPE_DOCMIND")
                .categoryId("default")
                .workspaceId(WORKSPACE_ID)
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<AddFileResponse> response = client.addFile(addFileRequest);
        // Synchronously get the return value of the API request
        AddFileResponse resp = response.get();
        log.info("AddFile response:{}", JSONObject.toJSONString(resp));
        if (!resp.getBody().getSuccess().equals("true")){
            throw new RuntimeException("添加失败"+resp.getBody().getMessage());
        } else {
            return Result.SUCCESS(resp.getBody().getData().getFileId());
        }
    }

    public static void uploadFileLink(String preSignedUrl, String sourceUrlString, Map headers) {
        HttpURLConnection connection = null;
        try {
            // 创建URL对象
            URL url = new URL(preSignedUrl);
            connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法用于文档上传，需与您在上一步中调用ApplyFileUploadLease接口实际返回的Data.Param中Method字段的值一致
            connection.setRequestMethod("PUT");

            // 允许向connection输出，因为这个连接是用于上传文档的
            connection.setDoOutput(true);

//            headers 数据格式如下"X-bailian-extra": "MTExMTgyMzMyNTYyMjcyOA\u003d\u003d","Content-Type": ""
//            解析成对应所需内容

            connection.setRequestProperty("X-bailian-extra", headers.get("X-bailian-extra") != null ? headers.get("X-bailian-extra").toString() : "");
            connection.setRequestProperty("Content-Type", headers.get("Content-Type") != null ? headers.get("Content-Type").toString() : "");

            URL sourceUrl = new URL(sourceUrlString);
            HttpURLConnection sourceConnection = (HttpURLConnection) sourceUrl.openConnection();

            // 设置访问OSS的请求方法为GET
            sourceConnection.setRequestMethod("GET");
            // 获取响应码，200表示请求成功
            int sourceFileResponseCode = sourceConnection.getResponseCode();

            // 从OSS读取文档并通过连接上传
            if (sourceFileResponseCode != HttpURLConnection.HTTP_OK){
                throw new RuntimeException("Failed to get source file.");
            }
            try (DataOutputStream outStream = new DataOutputStream(connection.getOutputStream());
                 InputStream in = new BufferedInputStream(sourceConnection.getInputStream())) {
                byte[] buffer = new byte[4096];
                int bytesRead;

                while ((bytesRead = in.read(buffer)) != -1) {
                    outStream.write(buffer, 0, bytesRead);
                }

                outStream.flush();
            }

            // 检查响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 文档上传成功
                System.out.println("File uploaded successfully.");
            } else {
                // 文档上传失败
                System.out.println("Failed to upload the file. ResponseCode: " + responseCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }


    @SneakyThrows
    public static Result<String> describeFile(String fileId) {
        // Parameter settings for API request
        DescribeFileRequest describeFileRequest = DescribeFileRequest.builder()
                .workspaceId(WORKSPACE_ID)
                .fileId(fileId)
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<DescribeFileResponse> response = client.describeFile(describeFileRequest);
        // Synchronously get the return value of the API request
        DescribeFileResponse resp = response.get();
        log.info("DescribeFile response:{}", JSONObject.toJSONString(resp));
        if (!resp.getBody().getSuccess()){
            throw new RuntimeException("查询失败"+resp.getBody().getMessage());
        } else {
            return Result.SUCCESS(resp.getBody().getData().getStatus());
        }
    }

    @SneakyThrows
    public static Result<String> submitIndexAddDocumentsJob(String indexId, String fileId) {
        // Parameter settings for API request
        SubmitIndexAddDocumentsJobRequest submitIndexAddDocumentsJobRequest = SubmitIndexAddDocumentsJobRequest.builder()
                .workspaceId(WORKSPACE_ID)
                .indexId(indexId)
                .sourceType("DATA_CENTER_FILE")
                .documentIds(java.util.Arrays.asList(
                        fileId
                ))
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<SubmitIndexAddDocumentsJobResponse> response = client.submitIndexAddDocumentsJob(submitIndexAddDocumentsJobRequest);
        // Synchronously get the return value of the API request
        SubmitIndexAddDocumentsJobResponse resp = response.get();
        log.info("SubmitIndexAddDocumentsJob response:{}", JSONObject.toJSONString(resp));
        if (!resp.getBody().getSuccess()){
            throw new RuntimeException("添加失败"+resp.getBody().getMessage());
        } else {
            return Result.SUCCESS(resp.getBody().getData().getId());
        }
    }

    @SneakyThrows
    public static Result<String> getIndexJobStatus(String jobId, String indexId) {
        // Parameter settings for API request
        GetIndexJobStatusRequest getIndexJobStatusRequest = GetIndexJobStatusRequest.builder()
                .jobId(jobId)
                .workspaceId(WORKSPACE_ID)
                .indexId(indexId)
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<GetIndexJobStatusResponse> response = client.getIndexJobStatus(getIndexJobStatusRequest);
        // Synchronously get the return value of the API request
        GetIndexJobStatusResponse resp = response.get();
        log.info("GetIndexJobStatus response:{}", JSONObject.toJSONString(resp));
        if (!resp.getBody().getSuccess()) {
            throw new RuntimeException("查询失败"+resp.getBody().getMessage());
        } else {
            return Result.SUCCESS(resp.getBody().getData().getStatus());
        }
    }

    @SneakyThrows
    public static Result<String> deleteIndexDocument(String indexId, String fileId) {
        // Parameter settings for API request
        DeleteIndexDocumentRequest deleteIndexDocumentRequest = DeleteIndexDocumentRequest.builder()
                .indexId(indexId)
                .workspaceId(WORKSPACE_ID)
                .documentIds(java.util.Arrays.asList(
                        fileId
                ))
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<DeleteIndexDocumentResponse> response = client.deleteIndexDocument(deleteIndexDocumentRequest);
        // Synchronously get the return value of the API request
        DeleteIndexDocumentResponse resp = response.get();
        log.info("DeleteIndexDocument response:{}", JSONObject.toJSONString(resp));
        if (!resp.getBody().getSuccess()){
            throw new RuntimeException("删除失败"+resp.getBody().getMessage());
        } else {
            return Result.SUCCESS("成功");
        }
    }

    @SneakyThrows
    public static Result<String> delSpaceDoc(String fileId) {
        // Parameter settings for API request
        DeleteFileRequest deleteFileRequest = DeleteFileRequest.builder()
                .fileId(fileId)
                .workspaceId(WORKSPACE_ID)
                // Request-level configuration rewrite, can set Http request parameters, etc.
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<DeleteFileResponse> response = client.deleteFile(deleteFileRequest);
        // Synchronously get the return value of the API request
        DeleteFileResponse resp = response.get();
        log.info("DeleteFile response:{}", JSONObject.toJSONString(resp));
        if (!resp.getBody().getSuccess()){
            throw new RuntimeException("删除失败"+resp.getBody().getMessage());
        } else {
            return Result.SUCCESS("成功");
        }
    }
}
