package com.business.le.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class LeonardoBO {

    @Schema(title = "高档风格")
    private String upscalerStyle;

    @Schema(title = "创意”程度, 越高细节就越多")
    private Integer creativityStrength;

    @Schema(title = "必须在1和2之间")
    private Float upscaleMultiplier;

    // le 视频参数
    @Schema(title = "图片id")
    private String imageId;
    @Schema(title = "是否初始化图片")
    private Boolean isInitImage;
    @Schema(title = "运动强度")
    private Integer motionStrength;

    public LeonardoBO() {}

    public LeonardoBO(String imageId, String upscalerStyle, Integer creativityStrength, Float upscaleMultiplier) {
        this.imageId = imageId;
        this.upscalerStyle = (upscalerStyle == null ? "General" : upscalerStyle);
        this.creativityStrength = (creativityStrength == null ? 6 : creativityStrength);
        this.upscaleMultiplier = (upscaleMultiplier == null ? 1f : upscaleMultiplier);
    }

    public LeonardoBO(String imageId, Boolean isInitImage, Integer motionStrength) {
        this.imageId = imageId;
        this.isInitImage = isInitImage;
        this.motionStrength = motionStrength;
    }

}
