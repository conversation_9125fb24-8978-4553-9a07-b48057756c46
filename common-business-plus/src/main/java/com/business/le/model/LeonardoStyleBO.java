package com.business.le.model;

import com.nacos.mjapi.model.MjAddImageBO;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Data
public class LeonardoStyleBO {

    private Integer width;
    private Integer height;
    private String modelId;
    private String prompt;
    private String negativePrompt;

    private Integer numImages;

    private String initImageId;
    private Float initStrength;
    private Boolean controlNet;
    private String controlNetType;

    private Boolean alchemy;
    private String presetStyle;
    private Boolean photoReal;
    private String photoRealVersion;

    private List<Element> elements;

    private List<Controlnets> controlnets;

    public LeonardoStyleBO() {}

    public LeonardoStyleBO(Integer width, Integer height, Float initStrength, String prompt, String initImageId, String modelId, String presetStyle, List<Element> elements) {
        this.modelId = (modelId == null ? "2067ae52-33fd-4a82-bb92-c2c55e7d2786" : modelId);
        this.numImages = 2;
        this.width = (width == null ? 1536 : width);
        this.height = (height == null ? 1536 : height);
        this.prompt = prompt;
        this.initImageId = initImageId;
        if (initStrength == null || initStrength > 0.9) {
            this.initStrength = 0.9F;
        } else if (initStrength < 0.1) {
            this.initStrength = 0.1F;
        } else {
            this.initStrength = initStrength;
        }
        this.controlNet = true;
        this.controlNetType = "CANNY";
        this.alchemy = true;
        this.presetStyle = presetStyle;
        this.photoReal = false;
        this.elements = elements;
    }

    public LeonardoStyleBO(String modelId, Integer width, Integer height, String prompt, List<Element> elements) {
        this.modelId = (modelId == null ? "2067ae52-33fd-4a82-bb92-c2c55e7d2786" : modelId);
        this.numImages = 4;
        this.width = (width == null ? 1536 : width);
        this.height = (height == null ? 1536 : height);
        this.prompt = prompt;
        this.elements = elements;
    }

    @Data
    public static class Element {
        private String akUUID;
        // 值-2到2
        private Float weight;

        public Element() {}

        public Element(String akUUID) {
            this.akUUID = akUUID;
            this.weight = 1f;
        }

        public Element(String akUUID, Float weight) {
            this.akUUID = akUUID;
            this.weight = weight;
        }
    }

    @Data
    public static class Controlnets {
        private String initImageId;

        private String initImageType;

        private Integer preprocessorId;

        private Double weight;

        private Double influence;

        private String strengthType;
    }

    public static Map<String, Object> getLePreprocessorId(String type, double weight){
        Map<String, Object> preprocessorMap = new HashMap<>();
        if ("C".equals(type)) { //角色图片列表
            preprocessorMap.put("preprocessorId", 133);
            preprocessorMap.put("strengthType", mapToRange(mapValue(weight), "T"));
            return preprocessorMap;
        }
        if ("S".equals(type)) { //风格图片列表
            double normalizedValue = weight / 1000;
            preprocessorMap.put("preprocessorId", 67);
            preprocessorMap.put("strengthType", mapToRange(normalizedValue, "F"));
            return preprocessorMap;
        }
        if ("D".equals(type)) { //垫图图片列表
            preprocessorMap.put("preprocessorId", 100);
            preprocessorMap.put("strengthType", mapToRange(weight, "T"));
            return preprocessorMap;
        }
        return null;
    }

    public static String mapToRange(double value, String type) {
        if (value < 0 || value > 1000) {
            throw new IllegalArgumentException("Value must be between 0 and 2");
        }

        double[] thresholds = null;
        String[] labels = null;
        if (type.equals("T")) {
            thresholds = new double[]{0.66, 1.32, 2};
            labels = new String[]{"Low", "Mid", "High"};
        } else {
            thresholds = new double[]{0.4, 0.8, 1.2, 1.6, 2};
            labels = new String[]{"Low", "Mid", "High", "Ultra", "Max"};
        }
        for (int i = 0; i < thresholds.length; i++) {
            if (value <= thresholds[i]) {
                return labels[i];
            }
        }
        throw new IllegalArgumentException("Invalid value");
    }
    public static double mapValue(double value) {
        if (value < 0 || value > 100) {
            throw new IllegalArgumentException("Value must be between 0 and 100");
        }
        return (value / 100.0) * 2.0;
    }

}
