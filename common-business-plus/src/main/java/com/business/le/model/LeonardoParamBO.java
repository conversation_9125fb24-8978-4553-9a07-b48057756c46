package com.business.le.model;

import com.nacos.mjapi.model.MjAddImageBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Data
public class LeonardoParamBO {

    private Integer preprocessorId;

    private String strengthType;

    // 垫图图片id
    private String imageId;

    // 垫图图片地址
    private String imageUrl;

    private Double influence;

    public LeonardoParamBO(){}

    public LeonardoParamBO(Integer preprocessorId, String strengthType, String imageId, Double influence, String imageUrl) {
        this.preprocessorId = preprocessorId;
        this.strengthType = strengthType;
        this.imageId = imageId;
        this.influence = influence;
        this.imageUrl = imageUrl;
    }

}
