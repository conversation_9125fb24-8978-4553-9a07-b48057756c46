package com.business.le;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.business.le.model.LeonardoBO;
import com.business.le.model.LeonardoStyleBO;
import com.business.utils.BFileUtil;
import com.business.utils.BOssUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.enums.DictConfigEnum;
import com.nacos.exception.E;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class LeonardoUtil {

    private static final int MAX_RETRIES = 3; // 最大重试次数
    private static final long RETRY_DELAY_MS = 2000; // 重试间隔时间（毫秒）

    private static final String HOST_LE_GANGPLANK = "https://leonardo.iworks.cn";
    private static final String HOST_LE_AUTHORITY = "https://cloud.leonardo.ai";
    private final static String LE_API_KEY = "b5a7398e-07a2-447f-9c83-b927afe7bc83";  //买的新账号

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .readTimeout(10, TimeUnit.MINUTES)
            .writeTimeout(10, TimeUnit.MINUTES)
            .build();

    /**
     * 获取模型key
     * @return 模型key
     */
    public static String getModelKey() {
        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/platformModels")
                .get()
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + "5f624d0c-4862-4e7f-ae88-51366aeb4b6e")
                .build();
        try(Response response = client.newCall(request).execute()) {
            if (response.code() == 200) {
                return response.body().string();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 获取元素值列表
     * @return 元素值列表
     */
    public static String getElementValueList() {
        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/elements")
                .get()
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + "b5a7398e-07a2-447f-9c83-b927afe7bc83")
                .build();
        try(Response response = client.newCall(request).execute()) {
            if (response.code() == 200) {
                return response.body().string();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * le图片上传
     * @param apiKey 上传apiKey
     * @return 上传结果
     */
    public static Map<String, String> getPresignedUrlUploadingImg(String apiKey) {
        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/init-image")
                .post(RequestBody.create("{\"extension\": \"jpg\"}", MediaType.parse("application/json")))
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + apiKey)
                .build();
        try (Response response = client.newCall(request).execute()){
            log.info("le getPresignedUrlUploadingImg:{}", response.toString());
            if (!response.isSuccessful()) {
                log.info("le getPresignedUrlUploadingImg:{}", response.code());
                return null;
            }
            String responseBody = response.body().string();
            JSONObject jsonObject = JSONObject.parseObject(responseBody);
            JSONObject uploadInitImage = jsonObject.getJSONObject("uploadInitImage");
            String image_id = uploadInitImage.getString("id");
            String url = uploadInitImage.getString("url");
            String fields = uploadInitImage.getString("fields");
            Map<String, String> stringMap = new HashMap<>();
            stringMap.put("url", url);
            stringMap.put("fields", fields);
            stringMap.put("imageId", image_id);
            return stringMap;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * le图片上传
     * @param presignedUrl 上传url
     * @param jsonFields 上传参数
     * @param imageFilePath 上传图片路径
     * @return 上传结果
     */
    public static boolean presignedUrlUploadingImg(String presignedUrl, String jsonFields,String imageFilePath) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> fields = objectMapper.readValue(jsonFields, Map.class);

        File imageFile = new File(imageFilePath);
        MultipartBody.Builder requestBodyBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);
        for (Map.Entry<String, Object> entry : fields.entrySet()) {
            requestBodyBuilder.addFormDataPart(entry.getKey(), String.valueOf(entry.getValue()));
        }
        RequestBody fileBody = RequestBody.create(MediaType.parse("image/*"), imageFile);
        requestBodyBuilder.addFormDataPart("file", imageFile.getName(), fileBody);
        Request request = new Request.Builder()
                .url(presignedUrl)
                .post(requestBodyBuilder.build())
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.info("le presignedUrlUploadingImg:{}", response.toString());
            if (response.isSuccessful() && response.code() == 204) {
               return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        return false;
    }

    /**
     * le图片上传
     * @param presignedUrl 上传url
     * @param jsonFields 上传参数
     * @param imageData 上传图片
     * @return 上传结果
     */
    public static boolean presignedUrlUploadingImg(String presignedUrl, String jsonFields, byte[] imageData) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> fields = objectMapper.readValue(jsonFields, Map.class);

        MultipartBody.Builder requestBodyBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);
        for (Map.Entry<String, Object> entry : fields.entrySet()) {
            requestBodyBuilder.addFormDataPart(entry.getKey(), String.valueOf(entry.getValue()));
        }
        requestBodyBuilder.addFormDataPart("file", "le-to-video.png", RequestBody.create(MediaType.parse("image/png"), imageData));
        Request request = new Request.Builder()
                .url(presignedUrl)
                .post(requestBodyBuilder.build())
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.info("le presignedUrlUploadingImg:{}", response.toString());
            if (response.isSuccessful() && response.code() == 204) {
                return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
        return false;
    }

    /**
     * le图片高清重绘
     * @param leonardoBO 高清重绘参数
     * @param apiKey 高清重绘apiKey
     * @return 高清重绘任务id
     */
    public static String generativeUpscale(LeonardoBO leonardoBO, String apiKey) {
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType,
                "{\"upscalerStyle\": \""+ leonardoBO.getUpscalerStyle() +"\"," +
                        "\"creativityStrength\": "+ leonardoBO.getCreativityStrength() +"," +
                        "\"upscaleMultiplier\": "+ leonardoBO.getUpscaleMultiplier() +"," +
                        "\"initImageId\": \""+ leonardoBO.getImageId() +"\"}");
        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/variations/universal-upscaler")
                .post(body)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + apiKey)
                .build();
        try (Response response = client.newCall(request).execute()){
            log.info("le generativeUpscale:{}", response.toString());
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                JSONObject universalUpscaler = jsonObject.getJSONObject("universalUpscaler");
                return universalUpscaler.getString("id");
            } else {
                log.info("le generativeUpscale:{}", response.code());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    /**
     * 获取高清重绘任务结果
     * @param variationId 高清重绘任务id
     * @param apiKey 高清重绘任务apiKey
     * @return 高清重绘任务结果
     */
    public static String getUpscaledImgVariationId(String variationId, String apiKey) {
        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/variations/" + variationId)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + apiKey)
                .get()
                .build();
        try (Response response = client.newCall(request).execute()){
            log.info("==高清重绘任务结果Response: {}", response.toString());
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("==高清重绘任务结果Body: {}", responseBody);
                return responseBody;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("获取高清重绘任务任务结果失败 Exception: {}", e.getMessage());
            return null;
        }
        return null;
    }

    /**
     * le图片生成视频
     * @param leonardoBO 图片生成视频参数
     * @param absolutePath 图片路径
     * @param apiKey 图片生成视频apiKey
     * @return 图片生成视频任务id
     * @throws Exception 图片生成视频异常
     */
    public static String imageUpscaleRedrawing(LeonardoBO leonardoBO, String absolutePath, String apiKey) throws Exception{
        Map<String, String> stringMap = getPresignedUrlUploadingImg(apiKey);
        if (stringMap == null) {
            return null;
        }
        leonardoBO.setImageId(stringMap.get("imageId"));
        boolean isSuccess = presignedUrlUploadingImg(stringMap.get("url"), stringMap.get("fields"), absolutePath);
        if (!isSuccess) {
            return null;
        }
        String variationId = generativeUpscale(leonardoBO, apiKey);
        if (variationId == null) {
            return null;
        }
        return variationId;
    }

    /**
     * le图片生成视频
     * @param leonardoBO 图片生成视频参数
     * @param apiKey 图片生成视频apiKey
     * @return 图片生成视频任务id
     * @throws InterruptedException 图片生成视频异常
     */
    public static String postLeonardoImageToVideo(LeonardoBO leonardoBO, String apiKey) throws InterruptedException {
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType,
                "{\"imageId\": \""+ leonardoBO.getImageId() +"\"," +
                        "\"isInitImage\": "+ leonardoBO.getIsInitImage() +"," +
                        "\"motionStrength\": "+ leonardoBO.getMotionStrength() +"}");
        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/generations-motion-svd")
                .post(body)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + apiKey)
                .build();
        Thread.sleep(10);
        try (Response response = client.newCall(request).execute()){
            log.info("le generativeUpscale:{}", response.toString());
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                JSONObject universalUpscaler = jsonObject.getJSONObject("motionSvdGenerationJob");
                return universalUpscaler.getString("generationId");
            } else {
                log.info("le generativeUpscale:{}", response.code());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    // 获取Le视频结果/图片上色 结果
    public static String getLeonardoVideo(String generationId, String apiKey) {
        int maxRetries = 3;
        int retryCount = 0;
        long retryDelay = 800; // 2 seconds

        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/generations/" + generationId)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + apiKey)
                .get()
                .build();
        while (retryCount < maxRetries) {
            try (Response response = client.newCall(request).execute()){
                log.info("getLeonardoVideo response: {}", response.toString());
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("==*== responseBody= ｛｝",responseBody);
                    JSONObject jsonObject = JSON.parseObject(responseBody);
                    JSONObject generationsByPk = jsonObject.getJSONObject("generations_by_pk");
                    String status = generationsByPk.getString("status");
                    if ("COMPLETE".equals(status)) {
                        return processCompleteStatus(generationsByPk, generationId);
                    } else if ("PENDING".equals(status)) {
                        return "running";
                    } else if ("FAILED".equals(status)) {
                        retryCount++;
                        return null;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.info("获取Le结果失败 Exception: {}", e.getMessage());
                retryCount++;
                if (retryCount < maxRetries) {
                    try {
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return null;
                    }
                } else {
                    return null;
                }
            }
        }
        return null;
    }

    /**
     * 处理Le视频结果
     * @param generationsByPk 视频结果
     * @param generationId 视频任务id
     * @return 视频结果
     */
    private static String processCompleteStatus(JSONObject generationsByPk, String generationId) {
        JSONArray generatedImages = generationsByPk.getJSONArray("generated_images");
        if (generatedImages == null || generatedImages.isEmpty()) {
            return null;
        }

        if (generatedImages.size() == 1) {
            JSONObject generatedImage = generatedImages.getJSONObject(0);
            String motionMP4URL = generatedImage.getString("motionMP4URL");
            return uploadLEVideo(BFileUtil.imageToBase64(motionMP4URL), generationId);
        }

        List<String> images = new ArrayList<>();
        for (int i = 0; i < generatedImages.size(); i++) {
            JSONObject generatedImage = generatedImages.getJSONObject(i);
            images.add(generatedImage.getString("url"));
        }
        return String.join(", ", images);
    }
    // TODO 上传视频到oss
    public static String uploadLEVideo(byte[] fileBytes, String videoJobId) {
        if (fileBytes == null) return null;
        int maxRetries = 3; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadVideo(fileBytes, videoJobId, 14);
                if (videoPath != null) {
                    return videoPath;
                }
                log.error("uploadImageToOss 图片上传失败，重试次数：" + (retryCount + 1), videoJobId);
            } catch (Exception e) {
                log.error("上传图片到OSS发生异常，重试次数：" + (retryCount + 1), e);
            }
            retryCount++;
        }
        log.error("uploadImageToOss 图片上传失败，超过最大重试次数， 视频id=", videoJobId);
        return null;
    }

    /**
     * 图片上色
     * @param leonardoStyleBO 图片上色参数
     * @param apiKey 图片上色apiKey
     * @return 图片上色任务id
     */
    public static String postLeonardoImageColour(LeonardoStyleBO leonardoStyleBO, String apiKey) {
        JSONObject jsonBody = new JSONObject();

        jsonBody.put("height", leonardoStyleBO.getHeight());
        jsonBody.put("modelId", leonardoStyleBO.getModelId());
        jsonBody.put("prompt", leonardoStyleBO.getPrompt());
        jsonBody.put("negative_prompt", leonardoStyleBO.getNegativePrompt());
        jsonBody.put("num_images", leonardoStyleBO.getNumImages());
        jsonBody.put("width", leonardoStyleBO.getWidth());
        jsonBody.put("elements", leonardoStyleBO.getElements());
        jsonBody.put("alchemy", leonardoStyleBO.getAlchemy());
        jsonBody.put("presetStyle", leonardoStyleBO.getPresetStyle());
        jsonBody.put("init_image_id", leonardoStyleBO.getInitImageId());
        jsonBody.put("init_strength", leonardoStyleBO.getInitStrength());
        jsonBody.put("photoReal", leonardoStyleBO.getPhotoReal());
        jsonBody.put("controlNet", leonardoStyleBO.getControlNet());
        jsonBody.put("controlNetType", leonardoStyleBO.getControlNetType());
        String jsonString = jsonBody.toString();
        System.out.println("==*==上色json参数: " + jsonString);

        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/generations")
                .post(body)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + apiKey)
                .build();
        try (Response response = client.newCall(request).execute()){
            log.info("图片上色:{}", response.toString());
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                JSONObject imageColour = jsonObject.getJSONObject("sdGenerationJob");
                return imageColour.getString("generationId");
            } else {
                log.info("le postLeonardoImageColour:{}", response.code());
                log.info("le postLeonardoImageColour:{}", response.body().string());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    /**
     * le文生图
     * @param leonardoStyleBO 文生图参数
     * @param apiKey 文生图apiKey
     * @return 文生图任务id
     * @throws IBusinessException 文生图异常
     */
    public static String postLeonardoTextToImage(LeonardoStyleBO leonardoStyleBO, String apiKey) throws IBusinessException{
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("height", leonardoStyleBO.getHeight());
        jsonBody.put("width", leonardoStyleBO.getWidth());
        jsonBody.put("modelId", leonardoStyleBO.getModelId());
        jsonBody.put("prompt", leonardoStyleBO.getPrompt());
        jsonBody.put("negative_prompt", leonardoStyleBO.getNegativePrompt());
        jsonBody.put("num_images", leonardoStyleBO.getNumImages());
        jsonBody.put("elements", leonardoStyleBO.getElements());
        jsonBody.put("alchemy", leonardoStyleBO.getAlchemy());
        jsonBody.put("presetStyle", leonardoStyleBO.getPresetStyle());
        jsonBody.put("init_image_id", leonardoStyleBO.getInitImageId());
        jsonBody.put("init_strength", leonardoStyleBO.getInitStrength());
        jsonBody.put("photoReal", leonardoStyleBO.getPhotoReal());
        jsonBody.put("photoRealVersion", leonardoStyleBO.getPhotoRealVersion());
        /*jsonBody.put("controlNet", leonardoStyleBO.getControlNet());
        jsonBody.put("controlNetType", leonardoStyleBO.getControlNetType());*/
        jsonBody.put("sd_version", "v2");
        String jsonString = jsonBody.toString();
        log.info("==*==Le绘图Key: " + apiKey);
        log.info("==*==Le绘图json参数: " + jsonString);
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/generations")
                .post(body)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + apiKey)
                .build();

        for (int attempt = 0; attempt < MAX_RETRIES; attempt++) {
            try (Response response = client.newCall(request).execute()){
                int statusCode = response.code();
                String responseBody = response.body().string();
                log.info("==*==Le绘图编号: " + statusCode);
                log.info("==*==Le绘图结果: " + responseBody);
                if (response.isSuccessful()) {
                    JSONObject jsonObject = JSON.parseObject(responseBody);
                    JSONObject imageColour = jsonObject.getJSONObject("sdGenerationJob");
                    return imageColour.getString("generationId");
                } else {
                    log.warn("Request failed with status code: {}", statusCode);
                    if (attempt < MAX_RETRIES - 1) {
                        log.info("Retrying... attempt {}", attempt + 1);
                        Thread.sleep(RETRY_DELAY_MS);
                    } else {
                        throw new IBusinessException("le画图失败，状态码：" + statusCode);
                    }
                }
            } catch (Exception e) {
                log.error("请求失败: {}", e.getMessage());
                if (attempt < MAX_RETRIES - 1) {
                    log.info("Retrying... attempt {}", attempt + 1);
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IBusinessException("重试中断");
                    }
                } else {
                    throw new IBusinessException("le画图失败");
                }
            }
        }
        throw new IBusinessException("le画图失败");
    }

    /**
     * 获取le文生图结果
     * @param generationId 文生图任务id
     * @param apiKey 文生图apiKey
     * @return 文生图结果
     * @throws Exception 文生图异常
     */
    public static String getLeonardoTextToImageResult(String generationId, String apiKey) throws Exception{
        int maxRetries = 3;
        int retryCount = 0;
        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/generations/" + generationId)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + apiKey)
                .get()
                .build();
        while (retryCount < maxRetries) {
            try (Response response = client.newCall(request).execute()){
                log.info("==*==le文生图response: {}", response.toString());
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    log.info("==*==拉取le文生图片结果: {}",responseBody);
                    JSONObject jsonObject = JSON.parseObject(responseBody);
                    JSONObject generationsByPk = jsonObject.getJSONObject("generations_by_pk");
                    String status = generationsByPk.getString("status");
                    if ("COMPLETE".equals(status)) {
                        return generationsByPk.toJSONString();
                    } else if ("PENDING".equals(status)) {
                        return "running";
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.info("获取Le结果失败 Exception: {}", e.getMessage());
                return null;
            }
        }
        return null;
    }

    /**
     * le文生图测试
     * @param leonardoStyleBO 文生图参数
     * @param apiKey 文生图apiKey
     * @return 文生图任务id
     * @throws IBusinessException 文生图异常
     */
    public static String postLeonardoTextToImageTest(LeonardoStyleBO leonardoStyleBO, String apiKey) throws IBusinessException{
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("height", leonardoStyleBO.getHeight());
        jsonBody.put("width", leonardoStyleBO.getWidth());
        jsonBody.put("modelId", leonardoStyleBO.getModelId());
        jsonBody.put("elements", leonardoStyleBO.getElements());
        jsonBody.put("prompt", leonardoStyleBO.getPrompt());
        jsonBody.put("negative_prompt", leonardoStyleBO.getNegativePrompt());
        jsonBody.put("num_images", leonardoStyleBO.getNumImages());
        jsonBody.put("alchemy", leonardoStyleBO.getAlchemy());
        jsonBody.put("presetStyle", leonardoStyleBO.getPresetStyle());
        jsonBody.put("photoReal", leonardoStyleBO.getPhotoReal());
        jsonBody.put("photoRealVersion", leonardoStyleBO.getPhotoRealVersion());
        jsonBody.put("controlnets", leonardoStyleBO.getControlnets());
        jsonBody.put("sd_version", "v2");

        String jsonString = jsonBody.toString();
        log.info("==*==Le绘图json参数: " + jsonString);
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonString);
        Request request = new Request.Builder()
                .url(HOST_LE_GANGPLANK + "/api/rest/v1/generations")
                .post(body)
                .addHeader("accept", "application/json")
                .addHeader("content-type", "application/json")
                .addHeader("authorization", "Bearer " + apiKey)
                .build();

        for (int attempt = 0; attempt < MAX_RETRIES; attempt++) {
            try (Response response = client.newCall(request).execute()){
                String responseBody = response.body().string();
                log.info("==*==Le绘图结果: {}",responseBody);
                if (response.isSuccessful()) {
                    JSONObject jsonObject = JSON.parseObject(responseBody);
                    JSONObject imageColour = jsonObject.getJSONObject("sdGenerationJob");
                    return imageColour.getString("generationId");
                } else {
                    log.warn("Request failed with status code: {}", response.code());
                    if (attempt < MAX_RETRIES - 1) {
                        log.info("Retrying... attempt {}", attempt + 1);
                        Thread.sleep(RETRY_DELAY_MS);
                    } else {
                        throw new IBusinessException("le画图失败，状态码：" + response.code());
                    }
                }
            } catch (Exception e) {
                log.error("==*==Le绘图失败 Exception: {}", e.getMessage());
                if (attempt < MAX_RETRIES - 1) {
                    log.info("Retrying... attempt {}", attempt + 1);
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IBusinessException("重试中断");
                    }
                } else {
                    throw new IBusinessException("le画图失败");
                }
            }
        }
        throw new IBusinessException("le画图失败");
    }


    public static void main(String[] args) {
        String modle = getModelKey();
        System.out.println(modle);
    }





    public static void getRes() {
        LeonardoStyleBO leonardoStyleBO = new LeonardoStyleBO();
        leonardoStyleBO.setHeight(624);
        leonardoStyleBO.setWidth(624);

        leonardoStyleBO.setModelId("2067ae52-33fd-4a82-bb92-c2c55e7d2786");
        leonardoStyleBO.setPrompt("Children's illustration of a smiling boy standing in front of the yellow bus, showing the entire bus");
        leonardoStyleBO.setNegativePrompt("Disharmonious colorsative Prompt，NSFW,  watermarks, double body, double face, double features, incorrect posture, two heads, two faces, plastic, deformed, blurry, messed up eyes, crossed eyes, disfigured, poorly drawn face, mutation, mutated, ugly, poorly drawn hands, missing limb, blurry, floating limbs, disconnected limbs, malformed hands, out of focus, long neck, long body, long fingers, blender, doll, cropped, low-res, , out of frame, double two heads, blurred, ugly, disfigured, too many fingers, repetitive, grainy, extra limbs, poor anatomy, high pass filter, airbrush, zoomed, soft light, smooth skin, extra limbs, extra fingers, mutated hands, uneven proportions, blind, ugly eyes, dead eyes, blur, out of shot, out of focus");
        leonardoStyleBO.setNumImages(4);
        leonardoStyleBO.setAlchemy(true);
        leonardoStyleBO.setPresetStyle("DYNAMIC");
        leonardoStyleBO.setPhotoReal(false);
        try {
            String leJobId = LeonardoUtil.postLeonardoTextToImage(leonardoStyleBO, null);
            System.out.println(leJobId);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

}
