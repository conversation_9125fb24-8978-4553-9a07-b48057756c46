package com.business.mj;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.business.gpt.GptToTextUtil;
import com.business.mj.model.MJImgToTextBO;
import com.business.mj.model.MjWebRecentJobDTO;
import com.business.mj.model.MjWebResBodyBO;
import com.nacos.tool.BrotliInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MJWebUtil {

    // 装载 AMP
    public static String getAMP_437c42b22c(String deviceId, String userId, Integer lastEventId){
        JSONObject json = new JSONObject();
        json.put("deviceId", deviceId);
        json.put("userId", userId);
        json.put("sessionId", System.currentTimeMillis());
        json.put("optOut", false);
        json.put("lastEventTime", System.currentTimeMillis());
        json.put("lastEventId", lastEventId);
        return json.toJSONString();
    }

    public static String getCookieToBate64(String cookie) {
//        // 加密
//        String encodedString = Base64.getEncoder().encodeToString(originalString.getBytes());
//        System.out.println("Encoded string: " + encodedString);

        // 解密
        byte[] decodedBytes = Base64.getDecoder().decode(cookie);
        String decodedString = new String(decodedBytes);
        log.info("Decoded string: " + decodedString);
        return decodedString;
    }

    public static String getBate64ToURLDecoder(String cookieBate64) {
        try {
            String decodedString = URLDecoder.decode(cookieBate64, StandardCharsets.UTF_8);
            log.info("Decoded string: " + decodedString);
            return decodedString;
        } catch (Exception e) {
            log.error("getCookieToBate64 error",e);
        }
        return null;
    }









    private static final String cookie = "_ga=GA1.1.1865115555.1697472028; __stripe_mid=7eb6ad2c-74a2-45d9-bfbd-a850b2c16d340dcdde; _ga_Q0DQ5L7K0D=GS1.1.1699965312.5.1.1699965313.0.0.0; _ga_1267LWX45K=GS1.1.1702262474.2.1.1702262908.0.0.0; darkMode=disabled; AMP_MKTG_437c42b22c=JTdCJTdE; cf_clearance=r5Sai9e85JY2C6BVzTRLNQqBiu2wSq5ydkFOQrhmObA-1710135628-*******-hrG3bcyrp9yg59OwV.74_QP943r4oFRsYbV.htFDzHe_BpKKJAeVRlgtQe4wj3wFiB7QxidMkTNAPeIWqSQbfg; AMP_437c42b22c=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjIwOGEzZjdlNC04NDYwLTRmZTItODdlMS1kMDY3MWU1OTcyY2ElMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjI5NzM4YmM4Zi0zZmEyLTQxNTEtYmE2OC04MDBlYzkxNTQ2ZjklMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzEwMTQ0NjI5OTcwJTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTcxMDE0NDYyOTk4MyUyQyUyMmxhc3RFdmVudElkJTIyJTNBNjEyJTdE; __cf_bm=82cXCGkFsVmf0eHshp3HCKF.wuCwLOnjqoY7iin73jM-**********-*******-gPg.sbubdp2t9r0YKgpMHcrg1PACPGeVZ5tWikIoScKde4KQ15q9pjrkq5Z6PZf_0j4g86BkJhPxN5pYSZ_lJg; __Host-Midjourney.AuthUserToken=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; __Host-Midjourney.AuthUserToken.sig=NBJkOc-5vbKWzQP0bh-yGqMi4_GL8pMAxzhuwEap5eg; __Host-Midjourney.AuthUser=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

    //mj图生文接口
    public static String getImageToText(String url){
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(new BrotliInterceptor())
                .build();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("image_url", url);
        jsonObject.put("channelId", "picread");
        JSONObject flags = new JSONObject();
        flags.put("private", false);
        flags.put("mode", "fast");
        jsonObject.put("flags", flags);
        String requestBody = jsonObject.toJSONString();
        RequestBody body = RequestBody.create(requestBody,MediaType.parse("application/json"));
        Request request = new Request.Builder()
                .url("https://www.midjourney.com/api/app/picread")
                .post(body)
                .addHeader("Accept", "*/*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Type", "application/json")
                .addHeader("Content-Length", String.valueOf(requestBody.length()))
                .addHeader("Origin", "https://www.midjourney.com")
                .addHeader("Referer", "https://www.midjourney.com/imagine")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .addHeader("X-Csrf-Protection", "1")
                .addHeader("Cookie", cookie)
                .build();
        try {
            Response response = client.newCall(request).execute();
            log.info("response success: " + response);
            if (response.isSuccessful()) {
                if (response.body() != null) {
                    String responseBody = response.body().string();
                    log.info("mj图生文 responseBody: " + responseBody);
                    return responseBody;
                }
            }
        } catch (Exception e){
            log.info("mj图生文解析失败,{}",e.getMessage());
        }
        return null;
    }

    //mj图生文图片上传
    public static String uploadFile(MultipartFile file,String userId){
        OkHttpClient client = new OkHttpClient();
        String name = file.getOriginalFilename();
        if (name == null || name.isEmpty()){
            return null;
        }
        int lastDotIndex = name.lastIndexOf(".");
        if (lastDotIndex == -1 || lastDotIndex == name.length() - 1) {
            return null;
        }

        try {
            String prefix = userId + "/" + fileNameRandomString();
            String suffix = name.substring(lastDotIndex + 1);
            String suffix2 = "."+suffix;

            File tempFile = File.createTempFile("/user/",suffix2);
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(file.getBytes());
            }
            log.info("uploadFile tempFile.getName(): " + tempFile.getName());
            log.info("uploadFile newName: " + prefix+suffix2);
            RequestBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file", prefix+suffix2,
                            RequestBody.create(tempFile,MediaType.parse("image/"+suffix)))
                    .build();
            log.info("cehsi,{}",requestBody.contentType().toString());
            Request request = new Request.Builder()
                    .url("https://www.midjourney.com/api/storage/upload_file")
                    .post(requestBody)
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                    .addHeader("Content-Type", "multipart/form-data")
                    .addHeader("Content-Length", String.valueOf(requestBody.contentLength()))
                    .addHeader("Origin", "https://www.midjourney.com")
                    .addHeader("Referer", "https://www.midjourney.com/imagine")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-origin")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .addHeader("X-Csrf-Protection", "1")
                    .addHeader("Cookie", cookie2)
                    .build();
            Response response = client.newCall(request).execute();
            log.info("response success: " + response);
            if (response.isSuccessful()){
                if (response.body() != null) {
                    String responseBody = response.body().string();
                    log.info("response body: {}",responseBody);
                    return responseBody;
                }
            }
            if (response.body() != null) {
                log.info("response body: " + response.body().string());
            }
            log.info("response message: " + response.message());
        } catch (Exception e){
            log.error("mj图生文图片上传失败,{}",e.getMessage(),e);
        }
        return null;
    }

    private static final String cookie2 = "AMP_MKTG_437c42b22c=JTdCJTdE; darkMode=disabled; __stripe_mid=7596e6c4-ee36-4444-9068-3a44821e9a2c3f1784; cf_clearance=AS20kaYnLzqoPj2ZF03eL1RX9nnvDZOlNCMFW701SUg-1710299200-*******-NnUleO4OP3SsFT8KBCKkILsvtPqDWx60NopLnTty0BaGghMJRC4MEX6UyQjAuY.._4RxWw.wJ0pQ6HGiDUBVqg; AMP_437c42b22c=JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjI4ZWExMTJjMS0zNjVlLTRjOGQtOTI4MC0zZWRkZGJmNWUwN2IlMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjI5NzM4YmM4Zi0zZmEyLTQxNTEtYmE2OC04MDBlYzkxNTQ2ZjklMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzEwMjk5ODg2NTk5JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTcxMDI5OTg4NjYyNSUyQyUyMmxhc3RFdmVudElkJTIyJTNBOTIlN0Q=; __cf_bm=qbN6O8MZduUhl_pI4i4Wb7HQ9Wc9T_6hkLVD9hkwq0Q-**********-*******-uPc0_6l0dWukHCjf6_gvR7rCRzh1MnT66y_pKw7Fh_QozmRrtrDdifmrtbFkEH3ND6dH._Mx9i_iEUAFwN48dw; __Host-Midjourney.AuthUserToken=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; __Host-Midjourney.AuthUserToken.sig=c3HMz9yxYUf9Is4PiJxLeoC_Rfy9GokTuxv0OYcNM-o; __Host-Midjourney.AuthUser=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    private static String fileNameRandomString() {
        SecureRandom secureRandom = new SecureRandom();
        byte[] randomBytes = new byte[64]; // 64位随机数占用8个字节
        secureRandom.nextBytes(randomBytes);
        StringBuilder stringBuilder = new StringBuilder();
        for (byte b : randomBytes) {
            // 使用String.format方法将字节转换为两位16进制表示，并追加到字符串构建器中
            stringBuilder.append(String.format("%02x", b));
        }
        return stringBuilder.toString();
    }


    public static MJImgToTextBO getImgToText(MultipartFile file,String userId){
        try {
//            String url = uploadFile(file,userId);
//            if (url == null){
//                return null;
//            }
//            JSONObject jsonObject = JSON.parseObject(getImageToText(url));
            JSONObject jsonObject = JSON.parseObject("{\n" +
                    "    \"result\": [\n" +
                    "        \"Butterfly\\\", cute pastel background, white and silver butterflies flying in the sky, dreamy light blue gradient color scheme, \\\" but.books \\\", small text at bottom of screen that says \\\" RUN tattoos using word \\\" Butterflies Lancolm for skin texture\\\", simple font design, minimalist style, with words \\\" warp every day is your second change.\\\" , white background, white borders, white shadows, white outline, white stars, white shimmering glass effect, sparkling glitters, glitter in the style of Lancolm.\",\n" +
                    "        \"White background, light blue gradient sky with white clouds and flying butterflies in the center of an iPhone wallpaper design with \\\"Butterfly\\\" written on it, with small sparkles floating around. The overall color scheme is soft pastel. A serene atmosphere surrounds them. It says \\\"cartridge every day\\\", \\\"LINE\\\". There is no text or letters at all. It should have no shadows and be in the style of a minimalist design.\",\n" +
                    "        \"White background, white butterfly illustrations flying in the sky, with text \\\"Butterfly\\\" and \\\"MS every day is another second to,\\\" written on it. The overall color scheme of light blue-green gradient tones creates an atmosphere of tranquility and serenity. This wallpaper can be used as the phone's home screen backdrop or for social media profile pictures. in the style of MS every day is another second to.\",\n" +
                    "        \"White background, a pale blue gradient sky with white clouds, white butterflies flying in the air, a dreamy light effect, a cute cartoon style, \\\"Butterfly\\\" text written at the bottom of the screen, \\\"Third second is another life\\\", soft pastel colors, a minimalist design, a vertical wallpaper, a simple and clean aesthetic, digital art, high resolution, a digital illustration, vectorized, smooth gradients, a detailed background, in the style of a watercolor painting.\"\n" +
                    "    ],\n" +
                    "    \"linked_description\": [\n" +
                    "        \"Butterfly\\\", cute pastel background, white and silver butterflies flying in the sky, dreamy light blue gradient color scheme, \\\" but.books \\\", small text at bottom of screen that says \\\" RUN tattoos using word \\\" Butterflies Lancolm for skin texture\\\", simple font design, minimalist style, with words \\\" warp every day is your second change.\\\" , white background, white borders, white shadows, white outline, white stars, white shimmering glass effect, sparkling glitters, glitter in the style of Lancolm.\",\n" +
                    "        \"White background, light blue gradient sky with white clouds and flying butterflies in the center of an iPhone wallpaper design with \\\"Butterfly\\\" written on it, with small sparkles floating around. The overall color scheme is soft pastel. A serene atmosphere surrounds them. It says \\\"cartridge every day\\\", \\\"LINE\\\". There is no text or letters at all. It should have no shadows and be in the style of a minimalist design.\",\n" +
                    "        \"White background, white butterfly illustrations flying in the sky, with text \\\"Butterfly\\\" and \\\"MS every day is another second to,\\\" written on it. The overall color scheme of light blue-green gradient tones creates an atmosphere of tranquility and serenity. This wallpaper can be used as the phone's home screen backdrop or for social media profile pictures. in the style of MS every day is another second to.\",\n" +
                    "        \"White background, a pale blue gradient sky with white clouds, white butterflies flying in the air, a dreamy light effect, a cute cartoon style, \\\"Butterfly\\\" text written at the bottom of the screen, \\\"Third second is another life\\\", soft pastel colors, a minimalist design, a vertical wallpaper, a simple and clean aesthetic, digital art, high resolution, a digital illustration, vectorized, smooth gradients, a detailed background, in the style of a watercolor painting.\"\n" +
                    "    ],\n" +
                    "    \"aspect_ratio\": [\n" +
                    "        65,\n" +
                    "        128\n" +
                    "    ]\n" +
                    "}");
            StringBuilder textBuilder = new StringBuilder();
            int titleSize = 1;
            if (jsonObject != null) {
                JSONArray texts = JSON.parseArray(jsonObject.getString("result"));
                StringBuilder description = new StringBuilder();
                for (Object text : texts) {
                    if (text != null){
                        String[] parts = text.toString().split(";");
                        for (int i = 0; i < parts.length; i++) {
                            String a = parts[i].trim();
                            if (i == 0 && !description.toString().contains(a)){
                                titleSize ++;
                                description.append(parts[i].trim()).append(";");
                            }
                            if (i != 0 && !description.toString().contains(a)) {
                                description.append(parts[i].trim()).append(";");
                            }
                        }
                    }
                }
                textBuilder.append(description);
            }
            String all = textBuilder.toString();
            if (all.isEmpty()){
                return null;
            }
            all = all.replaceAll("\"", " ").replaceAll("\n", " ");
            log.info("图生文-英文-获取成功,{}",all);
            log.info("图生文-英文-获取成功,{}",all.length());
            String textEn = GptToTextUtil.getTextToEn(all);
            if (textEn == null){
                return null;
            }
            String[] textEns = filterString(textEn).split("[,;.]");
            log.info("图生文-中文-获取成功,{}",textEn);
            return new MJImgToTextBO(textEns,titleSize);
        } catch (Exception e) {
            log.error("图生文获取失败,{}",e.getMessage(),e);
        }
        return null;
    }

    public static String filterString(String input) {
        String regex = "[\\u4e00-\\u9fa5a-zA-Z;,.]+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        StringBuilder result = new StringBuilder();
        while (matcher.find()) {
            result.append(matcher.group());
        }
        return result.toString();
    }

    public static List<MjWebResBodyBO> getNewRecentJob(MjWebRecentJobDTO mjWebRecentJobDTO){
        if (mjWebRecentJobDTO == null || mjWebRecentJobDTO.getPage() == null || mjWebRecentJobDTO.getFeed() == null || mjWebRecentJobDTO.getCookie() == null){
            return null;
        }
        if (!mjWebRecentJobDTO.getFeed().equals("top_day") && !mjWebRecentJobDTO.getFeed().equals("top_week") && !mjWebRecentJobDTO.getFeed().equals("top_month")){
            return null;
        }
        String feedName =mjWebRecentJobDTO.getFeed().equals("top_day") ? "top" : mjWebRecentJobDTO.getFeed();

        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(new BrotliInterceptor())
                .build();

        String url = null;
        String referer = null;
        if (mjWebRecentJobDTO.getType() != null && mjWebRecentJobDTO.getType().equals("niji")){
            url = "https://nijijourney.com/api/app/recent-jobs?amount=" + mjWebRecentJobDTO.getAmount()
                    + "&page=" + mjWebRecentJobDTO.getPage()
                    + "&feed=" + feedName
                    + "&service=niji"
                    + "&_ql=explore";
            referer = "https://nijijourney.com/explore?tab=" + feedName;
        } else {
            url = "https://www.midjourney.com/api/app/recent-jobs?amount=" + mjWebRecentJobDTO.getAmount()
                    + "&page=" + mjWebRecentJobDTO.getPage()
                    + "&feed=" + feedName
                    + "&_ql=explore";
            referer = "https://www.midjourney.com/explore?tab=" + feedName;
        }

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Accept", "*/*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Type", "application/json")
                .addHeader("Referer", referer)
//                .addHeader("Referer", "https://www.midjourney.com/explore?tab=top_day")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .addHeader("X-Csrf-Protection", "1")
                .addHeader("Cookie", mjWebRecentJobDTO.getCookie())
                .get()
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.info("response success: " + response);
            if (response.isSuccessful()) {
                if (response.body() != null) {
                    JSONObject jsonObject = JSONObject.parseObject(response.body().string());
                    if (jsonObject.getBoolean("ok")) {
                        log.info("mj图生文 responseBody: " + jsonObject.getString("jobs"));
                        return JSON.parseArray(jsonObject.getString("jobs"), MjWebResBodyBO.class);
                    }
                    log.info("mj图生文 responseBody: " + response.body().string());
                    return null;
                }
            }
        } catch (Exception e){
            log.info("mj图生文解析失败,{}",e.getMessage());
            return null;
        }
        return null;
    }



}
