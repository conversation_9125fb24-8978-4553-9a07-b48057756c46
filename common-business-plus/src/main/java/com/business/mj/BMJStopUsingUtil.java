package com.business.mj;

import com.alibaba.fastjson2.JSONObject;
import com.business.enums.BNotificationEnum;
import com.business.enums.BRedisKeyEnum;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.business.model.po.SysNotificationPO;
import com.nacos.enums.CommonIntEnum;
import com.nacos.redis.RedisUtil;

import java.util.concurrent.TimeUnit;

//服务停用工具
public class BMJStopUsingUtil {
    //检查是否全部停用
    public static boolean checkAllNot() {
        return RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_ALL_NOT.getKey());
    }

    //处理速度规范
    public static Integer handleSpeed(Integer mjIsRelaxed) {
        //快速、慢速都启动，直接返回错误（null）
        if (RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_FAST_NOT.getKey()) && RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_RELAXED_NOT.getKey())){
            return null;
        }
        //如果仅快速有问题，默认慢速
        if (RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_FAST_NOT.getKey())){
            return CommonIntEnum.IS_TRUE.getIntValue();
        }
        //如果仅慢速有问题，默认快速
        if (RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_RELAXED_NOT.getKey())){
            return CommonIntEnum.IS_FALSE.getIntValue();
        }
        return mjIsRelaxed;
    }

    public static void initAllNot(boolean isAllNot) {
        if (!isAllNot){
            RedisUtil.removeKey(BRedisKeyEnum.MJ_ACCOUNT_ALL_NOT.getKey());
            return;
        }
        if (RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_ALL_NOT.getKey())){
            return;
        }
        RedisUtil.setValue(BRedisKeyEnum.MJ_ACCOUNT_ALL_NOT.getKey(), String.valueOf(true));
    }

    public static void initFastNot(boolean isAllNot) {
        if (!isAllNot){
            RedisUtil.removeKey(BRedisKeyEnum.MJ_ACCOUNT_FAST_NOT.getKey());
            return;
        }
        if (RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_FAST_NOT.getKey())){
            return;
        }
        RedisUtil.setValue(BRedisKeyEnum.MJ_ACCOUNT_FAST_NOT.getKey(), String.valueOf(true));
    }

    public static void initRelaxedNot(boolean isAllNot) {
        if (!isAllNot){
            RedisUtil.removeKey(BRedisKeyEnum.MJ_ACCOUNT_RELAXED_NOT.getKey());
            return;
        }
        if (RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_RELAXED_NOT.getKey())){
            return;
        }
        RedisUtil.setValue(BRedisKeyEnum.MJ_ACCOUNT_RELAXED_NOT.getKey(), String.valueOf(true));
    }

    //用户账号封禁并通知
    public static void prohibitMJDraw(Long userId, String prompt) {
        String value = RedisUtil.getValue(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_USER_NOT_USE,String.valueOf(userId)));
        if (value == null){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("prompt1", prompt);
            //mj封号装载
            RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_USER_NOT_USE,String.valueOf(userId)),jsonObject.toJSONString(),300, TimeUnit.SECONDS);
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(value);
        if (jsonObject == null || jsonObject.getString("prompt2")==null){
            return;
        }
        jsonObject.put("prompt2", prompt);
        RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_USER_NOT_USE,String.valueOf(userId)),jsonObject.toJSONString(),300, TimeUnit.SECONDS);
        SysNotificationPO notificationPO = SysNotificationPO.buildSysNotification(userId,
                BNotificationEnum.OTHER_NOTIF.getIntValue(),
                "违规操作封禁通知", "系统检测到您有频繁的敏感词违规操作,您的操作已被禁止,5分钟后可恢复正常", 0, null, null);
        BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(userId, BMessageSendEnum.NOTIFICATION_PUSH, JSONObject.toJSONString(notificationPO)));
    }

    //检验账号是否被封禁
    public static String isNotUserMJDraw(Long userId) {
        String value = RedisUtil.getValue(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_USER_NOT_USE,String.valueOf(userId)));
        if (value == null){
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(value);
        if (jsonObject == null || jsonObject.getString("prompt2")==null){
            return null;
        }
        long elapsedTime = RedisUtil.getExpire(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_USER_NOT_USE,String.valueOf(userId)),TimeUnit.SECONDS);
        return "您的操作已被封禁"+elapsedTime+"秒,请稍后重试";
    }

    //检验账号速度
    public static Integer isMjAccountSpeed(String accountSpeed) {
       if (accountSpeed.equals("fast")) {
           return 0;
       }
       if (accountSpeed.equals("relaxed")) {
           return 1;
       }
       return null;
    }

}
