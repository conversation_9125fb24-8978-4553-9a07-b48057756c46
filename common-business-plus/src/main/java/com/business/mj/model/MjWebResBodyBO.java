package com.business.mj.model;

import lombok.Data;

@Data
public class MjWebResBodyBO {
    private String id; //数据id
    private int parent_grid; //当前画格
    private String parent_id; //绘图任务id
    private String job_type; //任务类型
    private String event_type; //任务类型
    private String full_command; //指令
    private String enqueue_time; //入队时间
    private int width; //宽
    private int height; //高
    private int batch_size; //大小
    private boolean  published; //是否发布
    private boolean  shown; //是否展示
    private boolean  liked_by_user; //是否点赞
    private String username; //用户名
    private String user_id; //用户id
    private String service; //服务
    private String parsed_version; //模型版本
    private String current_status; //状态

    @Override
    public String toString() {
        return "MjWebResBodyBO{" +
                "id='" + id + '\'' +
                ", parent_grid=" + parent_grid +
                ", parent_id='" + parent_id + '\'' +
                ", job_type='" + job_type + '\'' +
                ", event_type='" + event_type + '\'' +
                ", full_command='" + full_command + '\'' +
                ", enqueue_time='" + enqueue_time + '\'' +
                ", width=" + width +
                ", height=" + height +
                ", batch_size=" + batch_size +
                ", published=" + published +
                ", shown=" + shown +
                ", liked_by_user=" + liked_by_user +
                ", username='" + username + '\'' +
                ", user_id='" + user_id + '\'' +
                ", service='" + service + '\'' +
                ", parsed_version='" + parsed_version + '\'' +
                ", current_status='" + current_status + '\'' +
                '}';
    }
}
