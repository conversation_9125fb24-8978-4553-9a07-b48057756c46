package com.business.mj;

import com.alibaba.fastjson2.JSONObject;
import com.business.enums.BRedisKeyEnum;
import com.business.mj.model.c.MjSpeedRatioBO;
import com.business.model.bo.MJRedisRefreshBO;
import com.nacos.ddimg.model.ImgDrawConcurrencyBO;
import com.nacos.ddimg.model.MJAccountBO;
import com.nacos.enums.CommonIntEnum;
import com.nacos.enums.GlobalRedisKeyEnum;
import com.nacos.mjapi.model.MJAccountHeaderBO;
import com.nacos.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;

//mj账号相关工具
@Slf4j
public class MJAccountUtil {

    private static final int MJ_ACCOUNT_NUM = 10;//排队自动重置数量，谨慎修改

    //快速账号缓存刷新
    public static void refreshAccountPro(List<MJAccountBO> mjAccountBOListPro, MJRedisRefreshBO mjRedisRefreshBO) {
        if (mjAccountBOListPro == null || mjAccountBOListPro.isEmpty()){
            return;
        }
        log.info("pro账号数量：{}",mjAccountBOListPro);
        boolean isRefreshCount = false;
        for (int i = 0; i < mjAccountBOListPro.size(); i++) {
            MJAccountBO mjAccountBO = mjAccountBOListPro.get(i);
            ImgDrawConcurrencyBO imgDrawConcurrencyBOCount = JSONObject.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.TASK_MJ_ACCOUNT_USER_COUNT.getStrKey(), mjAccountBO.getUserId())), ImgDrawConcurrencyBO.class);
            if (imgDrawConcurrencyBOCount == null || imgDrawConcurrencyBOCount.getProRegisterQuantity()==null || imgDrawConcurrencyBOCount.getProRegisterQuantity() <= 0){
                imgDrawConcurrencyBOCount = new ImgDrawConcurrencyBO();
                imgDrawConcurrencyBOCount.setProRegisterQuantity(0);
            }
            if (imgDrawConcurrencyBOCount.getProRegisterQuantity() > MJ_ACCOUNT_NUM){
                isRefreshCount = true;
            }
            mjAccountBO.setResidueProConcurrent(imgDrawConcurrencyBOCount.getProRegisterQuantity());
            mjAccountBOListPro.set(i, mjAccountBO);
            log.info("pro账号数量：{}",mjAccountBO);
            log.info("getResidueProConcurrent：{}",mjAccountBO.getResidueProConcurrent());
        }
        log.info("pro账号数量：{}",mjAccountBOListPro);
        log.info("isRefreshCount：{}",isRefreshCount);
        //大于相应数量时，进行重置
        deleteKeys(isRefreshCount);
        MJAccountHeaderBO mjAccountHeaderBO = initMjAccountHeaderBO(mjRedisRefreshBO, mjAccountBOListPro, Comparator.comparingInt(MJAccountBO::getResidueProConcurrent));
        RedisUtil.setValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PRO_ACCOUNT.getStrKey(), JSONObject.toJSONString(mjAccountHeaderBO));
        initImgDrawConcurrencyBOCount(mjAccountHeaderBO.getUserId(),true);
    }

    //慢速账号缓存刷新
    public static void refreshAccountPt(List<MJAccountBO> mjAccountBOListPt, MJRedisRefreshBO mjRedisRefreshBO) {
        if (mjAccountBOListPt == null || mjAccountBOListPt.isEmpty()){
            return;
        }
        log.info("pt账号数量：{}",mjAccountBOListPt);
        boolean isRefreshCount = false;
        for (int i = 0; i < mjAccountBOListPt.size(); i++) {
            MJAccountBO mjAccountBO = mjAccountBOListPt.get(i);
            ImgDrawConcurrencyBO imgDrawConcurrencyBOCount = JSONObject.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.TASK_MJ_ACCOUNT_USER_COUNT.getStrKey(), mjAccountBO.getUserId())), ImgDrawConcurrencyBO.class);
            if (imgDrawConcurrencyBOCount == null || imgDrawConcurrencyBOCount.getPtRegisterQuantity()==null || imgDrawConcurrencyBOCount.getPtRegisterQuantity() <= 0){
                imgDrawConcurrencyBOCount = new ImgDrawConcurrencyBO();
                imgDrawConcurrencyBOCount.setPtRegisterQuantity(0);
            }
            if (imgDrawConcurrencyBOCount.getPtRegisterQuantity() > MJ_ACCOUNT_NUM){
                isRefreshCount = true;
            }
            mjAccountBO.setResidueConcurrent(imgDrawConcurrencyBOCount.getPtRegisterQuantity());
            mjAccountBOListPt.set(i, mjAccountBO);
            log.info("pt账号数量：{}",mjAccountBO);
            log.info("getResidueConcurrent：{}",mjAccountBO.getResidueConcurrent());

        }
        log.info("pt账号数量：{}",mjAccountBOListPt);
        //大于相应数量时，进行重置
        deleteKeys(isRefreshCount);
        MJAccountHeaderBO mjAccountHeaderBO = initMjAccountHeaderBO(mjRedisRefreshBO, mjAccountBOListPt, Comparator.comparingInt(MJAccountBO::getResidueConcurrent));
        //装载慢速账号
        RedisUtil.setValue(GlobalRedisKeyEnum.DRAW_RECORD_TASK_MJ_PT_ACCOUNT.getStrKey(), JSONObject.toJSONString(mjAccountHeaderBO));
        initImgDrawConcurrencyBOCount(mjAccountHeaderBO.getUserId(),false);
    }

    private static void initImgDrawConcurrencyBOCount(String mjUserId, boolean isPro) {
        ImgDrawConcurrencyBO imgDrawConcurrencyBOCount = JSONObject.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.TASK_MJ_ACCOUNT_USER_COUNT.getStrKey(), mjUserId)), ImgDrawConcurrencyBO.class);
        if (imgDrawConcurrencyBOCount == null){
            imgDrawConcurrencyBOCount = new ImgDrawConcurrencyBO();
        }
        //快速账号数量变动
        if (isPro){
            imgDrawConcurrencyBOCount.setProRegisterQuantity(imgDrawConcurrencyBOCount.getProRegisterQuantity()==null || imgDrawConcurrencyBOCount.getProRegisterQuantity() <= 0 ? 1 : imgDrawConcurrencyBOCount.getProRegisterQuantity()+1);
            log.info("pro imgDrawConcurrencyBOCount：{}",imgDrawConcurrencyBOCount);
        }else {
            imgDrawConcurrencyBOCount.setPtRegisterQuantity(imgDrawConcurrencyBOCount.getPtRegisterQuantity()==null || imgDrawConcurrencyBOCount.getPtRegisterQuantity() <= 0 ? 1 : imgDrawConcurrencyBOCount.getPtRegisterQuantity()+1);
            log.info("pt imgDrawConcurrencyBOCount：{}",imgDrawConcurrencyBOCount);
        }
        RedisUtil.setValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.TASK_MJ_ACCOUNT_USER_COUNT.getStrKey(), mjUserId), JSONObject.toJSONString(imgDrawConcurrencyBOCount));
    }

    //进行批量删除
    private static void deleteKeys(boolean isRefreshCount) {
        if (isRefreshCount){
            Collection<String> keys = RedisUtil.getKeys(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.TASK_MJ_ACCOUNT_USER_COUNT.getStrKey(), "*"));
            if (!keys.isEmpty()){
                RedisUtil.removeKeys(keys);
            }
        }
    }

    //账号排序处理
    private static MJAccountHeaderBO initMjAccountHeaderBO(MJRedisRefreshBO mjRedisRefreshDTO, List<MJAccountBO> mjAccountBOList, Comparator<MJAccountBO> mjAccountBOComparator) {
        //正序排序：由小到大，代表账号使用量
        mjAccountBOList.sort(mjAccountBOComparator);
        log.info("mjAccountBOList,{}", mjAccountBOList.getFirst());
        MJAccountHeaderBO mjAccountHeaderBO = new MJAccountHeaderBO();
        mjAccountHeaderBO.setMjAccountId(mjAccountBOList.getFirst().getId());
        mjAccountHeaderBO.setToken(mjAccountBOList.getFirst().getAppToken());
        mjAccountHeaderBO.setCookie(mjAccountBOList.getFirst().getAppCookies());
        mjAccountHeaderBO.setAppVersion(mjRedisRefreshDTO.getAppVersion());
        mjAccountHeaderBO.setUserAgentVersion(mjRedisRefreshDTO.getUserAgent());
        mjAccountHeaderBO.setUserId(mjAccountBOList.getFirst().getUserId());
        return mjAccountHeaderBO;
    }

    public static boolean isRelaxedOpen() {
        return RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_RELAXED_OPEN.getKey());
    }

    //绘画初始化，获取账号
    public static Integer initIsSpeedRelaxed() {
        //未开启慢速，默认快速
        if (!MJAccountUtil.isRelaxedOpen()){
            return CommonIntEnum.IS_FALSE.getIntValue();//默认快速
        }
        //自动设置快速慢速比率
        if (RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_SPEED_RATIO.getKey())){
            MjSpeedRatioBO mjSpeedRatioBO = JSONObject.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_ACCOUNT_SPEED_RATIO.getKey()), MjSpeedRatioBO.class);
            if (mjSpeedRatioBO != null && mjSpeedRatioBO.getCurrentQuantity() != null && mjSpeedRatioBO.getSettingQuantity() != null && mjSpeedRatioBO.getCurrentQuantity() >= mjSpeedRatioBO.getSettingQuantity()){
                return CommonIntEnum.IS_TRUE.getIntValue();//初始化状态为0时，使用慢速
            }
        }
        return CommonIntEnum.IS_FALSE.getIntValue();//默认快速
    }

    public static void changeSpeedRatio() {
        if (RedisUtil.hasKey(BRedisKeyEnum.MJ_ACCOUNT_SPEED_RATIO.getKey())){
            MjSpeedRatioBO mjSpeedRatioBO = JSONObject.parseObject(RedisUtil.getValue(BRedisKeyEnum.MJ_ACCOUNT_SPEED_RATIO.getKey()), MjSpeedRatioBO.class);
            if (mjSpeedRatioBO != null && mjSpeedRatioBO.getCurrentQuantity() != null && mjSpeedRatioBO.getSettingQuantity() != null){
                if (mjSpeedRatioBO.getCurrentQuantity() >= mjSpeedRatioBO.getSettingQuantity()){
                    mjSpeedRatioBO.setCurrentQuantity(0);
                }else {
                    mjSpeedRatioBO.setCurrentQuantity(mjSpeedRatioBO.getCurrentQuantity()+1);
                }
                RedisUtil.setValue(BRedisKeyEnum.MJ_ACCOUNT_SPEED_RATIO.getKey(), JSONObject.toJSONString(mjSpeedRatioBO));
            }
        }
    }

}
