package com.business.mj;

import com.alibaba.fastjson2.JSON;
import com.business.enums.BRedisKeyEnum;
import com.gargoylesoftware.htmlunit.*;
import com.gargoylesoftware.htmlunit.html.HtmlPage;
import com.gargoylesoftware.htmlunit.javascript.JavaScriptEngine;
import com.gargoylesoftware.htmlunit.util.Cookie;
import com.nacos.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.cache.CacheManager;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

//mj web账号刷新
@Slf4j
public class MjWebAccountUtil {

    public static String getRandom16() {
        Random random = new Random();
        StringBuilder hexRandom = new StringBuilder();
        for (int i = 0; i < 16; i++) {
            int num = random.nextInt(16);
            hexRandom.append(Integer.toHexString(num));
        }
        return hexRandom.toString();
    }

    public static void mai2n() {

        try {



            final WebClient webClient = new WebClient(BrowserVersion.CHROME);
            // 启用JavaScript
//            webClient.getOptions().setJavaScriptEnabled(true);
            // 2. 禁用保留字检查
            webClient.getOptions().setThrowExceptionOnScriptError(false);
            webClient.getOptions().setThrowExceptionOnFailingStatusCode(false);
            webClient.getOptions().setJavaScriptEnabled(true);
            webClient.getOptions().setUseInsecureSSL(true);
            webClient.getOptions().setDownloadImages(false);
            webClient.getOptions().setCssEnabled(false);
            webClient.getOptions().setRedirectEnabled(true);
            webClient.getOptions().setAppletEnabled(false);
            webClient.getOptions().setActiveXNative(false);



            // 获取页面
            HtmlPage page = webClient.getPage("https://www.midjourney.com/cdn-cgi/challenge-platform/scripts/jsd/main.js");

            // 添加 Cookie
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "_ga", "GA1.1.1865115555.1697472028"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "__stripe_mid", "7eb6ad2c-74a2-45d9-bfbd-a850b2c16d340dcdde"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "_ga_Q0DQ5L7K0D", "GS1.1.1699965312.5.1.1699965313.0.0.0"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "_ga_1267LWX45K", "GS1.1.1702262474.2.1.1702262908.0.0.0"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "darkMode", "disabled"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "AMP_MKTG_437c42b22c", "JTdCJTdE"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "__Host-Midjourney.AuthUserToken", "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "__Host-Midjourney.AuthUserToken.sig", "Y3cXxqBrjrZSfgw_ABb6gOgfYp0Ry5DZegjRlDk86Mw"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "__Host-Midjourney.AuthUser", "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "cf_clearance", "m7ceVMSJGxaVyw9.0pPHqco.dzkc80rrK9DxWefj7iA-1710832032-*******-eNb3f5B_.ppdaIr9EwFyLRxc4lktHOPeSxB6521s1Umw8VkZ.S5vc8uQhvuS8NPsdfsyGbwte4O7Wi9HpTy1yQ"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "__cf_bm", "6iaYfg6LDvEe8E2XX9fO7d8Z6TJT7r4nMtd6lqnGN4g-1710850311-*******-id1wrwwQbmwG6BzQKSt5Cy9s.24xHs6J5aozlHRSfcXUCzwYuIj8LiJbchmkzbxJyrINxrnIbUpOH71l9RmuDQ"));
            webClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "AMP_437c42b22c", "JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjIwOGEzZjdlNC04NDYwLTRmZTItODdlMS1kMDY3MWU1OTcyY2ElMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjI5NzM4YmM4Zi0zZmEyLTQxNTEtYmE2OC04MDBlYzkxNTQ2ZjklMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzEwODMxNjExMTI2JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTcxMDgzMjA2MTM5MSUyQyUyMmxhc3RFdmVudElkJTIyJTNBNzMwJTdE"));


            webClient.addRequestHeader("Accept", "*/*");
            webClient.addRequestHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            webClient.addRequestHeader("Accept-Language", "zh-CN,zh;q=0.9");
            webClient.addRequestHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"");
            webClient.addRequestHeader("Sec-Ch-Ua-Mobile", "?0");
            webClient.addRequestHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
            webClient.addRequestHeader("Sec-Fetch-Dest", "script");
            webClient.addRequestHeader("Sec-Fetch-Mode", "no-cors");
            webClient.addRequestHeader("Sec-Fetch-Site", "same-origin");
            webClient.addRequestHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

//            // 获取页面
//            HtmlPage page = webClient.getPage("https://www.midjourney.com/cdn-cgi/challenge-platform/h/b/scripts/jsd/ace796eb5511/main.js");
//https://editor.midjourney.com/cdn-cgi/challenge-platform/h/g/scripts/jsd/956dacbeead0/main.js
//            // 添加 Cookie
//            com.gargoylesoftware.htmlunit.util.Cookie cookie = new Cookie("example.com", "cookie_name", "cookie_value");
//            webClient.getCookieManager().addCookie(cookie);
//            // 添加 Header
//            webClient.addRequestHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36");
//
//            webClient.addRequestHeader("Accept", "*/*");
//                    webClient.addRequestHeader("Accept-Encoding", "gzip, deflate, br, zstd");
//                    webClient.addRequestHeader("Accept-Language", "zh-CN,zh;q=0.9");
//                    webClient.addRequestHeader("Content-Type", "application/json");
////                    webClient.addRequestHeader("Content-Length", String.valueOf(requestBody.length()));
//                    webClient.addRequestHeader("Origin", "https://www.midjourney.com");
//                    webClient.addRequestHeader("Referer", "https://www.midjourney.com/imagine");
//                    webClient.addRequestHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"");
//                    webClient.addRequestHeader("Sec-Ch-Ua-Mobile", "?0");
//                    webClient.addRequestHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
//                    webClient.addRequestHeader("Sec-Fetch-Dest", "empty");
//                    webClient.addRequestHeader("Sec-Fetch-Mode", "cors");
//                    webClient.addRequestHeader("Sec-Fetch-Site", "same-origin");
//                    webClient.addRequestHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//                    webClient.addRequestHeader("X-Csrf-Protection", "1");


            // 获取JavaScript执行结果
//            Object result = page.executeJavaScript("z").getJavaScriptResult();

            int getStatusCode = page.getWebResponse().getStatusCode();
//            String bodyContent = page.getBody().getTextContent();
//            page.executeJavaScript(bodyContent);
//            System.out.println("Body Content: " + bodyContent);
//            log.info("Body Content2: " + bodyContent);
            // 获取请求的URL
            log.info("Request URL: " + page.getUrl());
            log.info("Request getStatusCode: " + page.getWebResponse().getStatusCode());
            log.info("Request getWebResponse: " + page.getWebResponse().getResponseHeaders().toString());

            String cookies = page.getWebResponse().getResponseHeaderValue("Set-Cookie");
            System.out.println("Cookies: " + cookies);
            System.out.println("getWebResponse: " + getStatusCode);

            String bodyContentNew = page.getWebResponse().getResponseHeaderValue("Set-Cookie");
            // 输出结果
            log.info("执行结果：" + bodyContentNew);


            log.info("Request URL2: " + page.getUrl());
            log.info("Request getStatusCode2: " + page.getWebResponse().getStatusCode());
            log.info("Request getWebResponse2: " + page.getWebResponse().getResponseHeaders().toString());

            String js = inputStreamToString(page.getWebResponse().getContentAsStream());
            log.info("Request inputStreamToString: " + js);

            WebClient newClient = new WebClient();
            // 添加 Cookie
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "_ga", "GA1.1.1865115555.1697472028"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "__stripe_mid", "7eb6ad2c-74a2-45d9-bfbd-a850b2c16d340dcdde"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "_ga_Q0DQ5L7K0D", "GS1.1.1699965312.5.1.1699965313.0.0.0"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "_ga_1267LWX45K", "GS1.1.1702262474.2.1.1702262908.0.0.0"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "darkMode", "disabled"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "AMP_MKTG_437c42b22c", "JTdCJTdE"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "__Host-Midjourney.AuthUserToken", "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "__Host-Midjourney.AuthUserToken.sig", "Y3cXxqBrjrZSfgw_ABb6gOgfYp0Ry5DZegjRlDk86Mw"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "__Host-Midjourney.AuthUser", "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "cf_clearance", "m7ceVMSJGxaVyw9.0pPHqco.dzkc80rrK9DxWefj7iA-1710832032-*******-eNb3f5B_.ppdaIr9EwFyLRxc4lktHOPeSxB6521s1Umw8VkZ.S5vc8uQhvuS8NPsdfsyGbwte4O7Wi9HpTy1yQ"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "__cf_bm", "vdOHBUs.2NxS26d8t3kI._BsRn6Y0jSJIQRcVx6eSWM-1710850699-*******-yiW_cADpPgK7jyNw3c_9cdSKM0wtBOEjvz3zT32ijwZnEstgJ0TzoXRzrKO6FB6tzHcgJsrpbMWI5DzlY9mvHA"));
            newClient.getCookieManager().addCookie(new Cookie(".midjourney.com", "AMP_437c42b22c", "JTdCJTIyZGV2aWNlSWQlMjIlM0ElMjIwOGEzZjdlNC04NDYwLTRmZTItODdlMS1kMDY3MWU1OTcyY2ElMjIlMkMlMjJ1c2VySWQlMjIlM0ElMjI5NzM4YmM4Zi0zZmEyLTQxNTEtYmE2OC04MDBlYzkxNTQ2ZjklMjIlMkMlMjJzZXNzaW9uSWQlMjIlM0ExNzEwODMxNjExMTI2JTJDJTIyb3B0T3V0JTIyJTNBZmFsc2UlMkMlMjJsYXN0RXZlbnRUaW1lJTIyJTNBMTcxMDgzMjA2MTM5MSUyQyUyMmxhc3RFdmVudElkJTIyJTNBNzMwJTdE"));


            newClient.addRequestHeader("Accept", "*/*");
            newClient.addRequestHeader("Accept-Encoding", "gzip, deflate, br, zstd");
            newClient.addRequestHeader("Accept-Language", "zh-CN,zh;q=0.9");
            newClient.addRequestHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"");
            newClient.addRequestHeader("Sec-Ch-Ua-Mobile", "?0");
            newClient.addRequestHeader("Sec-Ch-Ua-Platform", "\"macOS\"");
            newClient.addRequestHeader("Sec-Fetch-Dest", "script");
            newClient.addRequestHeader("Sec-Fetch-Mode", "no-cors");
            newClient.addRequestHeader("Sec-Fetch-Site", "same-origin");
            newClient.addRequestHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");



            HtmlPage newPage = newClient.getPage(page.getUrl());
//            Object result = newPage.executeJavaScript(script);



//            Object result = page.executeJavaScript(js);
//            log.info("执行结果11111：" + result.toString());

//            Object result2 = page.executeJavaScript(js).getJavaScriptResult();
//            log.info("执行结果222222：" + result2.toString());

            log.info("Request URL2: " + page.getUrl());
            log.info("Request getStatusCode2: " + page.getWebResponse().getStatusCode());
            String cookies2 = page.getWebResponse().getResponseHeaderValue("Set-Cookie");
            System.out.println("Cookies2: " + cookies2);

            try {
                Thread.sleep(10000); // 10秒等待
                log.info("等待10秒");
                // 在这里执行您的操作
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                // 处理中断异常
            }
            CookieManager cookieManager = webClient.getCookieManager();
            for (Object cookie: cookieManager.getCookies()) {
                log.info("Cookie: " + cookie.toString());
            }

            CookieManager cookieManager2 = newClient.getCookieManager();
            for (Object cookie: cookieManager2.getCookies()) {
                log.info("Cookie2: " + cookie.toString());
            }


        } catch (Exception e) {
            log.error("执行脚本失败", e);
        }

    }

    private static String inputStreamToString(InputStream inputStream) {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String strRead = null;
            StringBuilder sb = new StringBuilder();
            while ((strRead = reader.readLine()) != null) {
                sb.append(strRead);
            }
            return sb.toString();
        } catch (IOException e) {
            log.error("读取流失败", e);
            return null;
        }finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    log.error("关闭流失败", e);
                }
            }
        }
    }




    public static void mai32n(String[] args) {
        OkHttpClient client = new OkHttpClient();
        // 构造请求体
        RequestBody requestBody = RequestBody.create("{}",MediaType.parse("application/json"));
        // 构造请求对象
        Request request = new Request.Builder()
                .url("https://www.midjourney.com/cdn-cgi/challenge-platform/h/b/jsd/r/864b24326b9a0ee4")
                .post(requestBody)
                .addHeader("sec-ch-ua", "\"Microsoft Edge\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"")
                .addHeader("sec-ch-ua-platform", "\"Windows\"")
                .addHeader("sec-ch-ua-mobile", "?0")
                .addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .addHeader("content-type", "application/json")
                .addHeader("accept", "*/*")
                .addHeader("origin", "https://www.midjourney.com")
                .addHeader("sec-fetch-site", "same-origin")
                .addHeader("sec-fetch-mode", "cors")
                .addHeader("sec-fetch-dest", "empty")
                .addHeader("accept-encoding", "gzip, deflate, br, zstd")
                .addHeader("accept-language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .build();
        try {
            // 执行请求
            Response response = client.newCall(request).execute();
            // 获取响应内容
            String responseBody = response.body().string();
            System.out.println("Response: " + responseBody);

            // 关闭响应
            response.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //需要刷新的cookie属性：
    // __cb_bm(接口刷新随机返回)、cf_clearance(固定接口返回刷新)

    public static String getRequestCookies(String userId) {
        try {
            String mapCookies = RedisUtil.getValue(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_COOKIES,userId));
            Map<String, Object> allMap = new HashMap<>(JSON.parseObject(mapCookies));
            return mapToCookieString(allMap);
        } catch (Exception e) {
            log.error("账号信息获取失败",e);
        }
        return null;
    }

    //刷新账号信息
    public static void refreshCookies(Headers headers, String userId) {
        optRefreshCookies(userId,parseCookies(headers));
    }

    // 解析响应中的 Cookies 并存储到 Map 中
    private static Map<String, String> parseCookies(Headers headers) {
        List<String> cookieHeaders = headers.values("Set-Cookie");
        if (cookieHeaders.isEmpty()) {
            return null;
        }
        Map<String, String> cookies = new HashMap<>();
        for (String cookieHeader : cookieHeaders) {
            String[] parts = cookieHeader.split(";");
            String[] cookie = parts[0].split("=");
            String cookieName = cookie[0];
            String cookieValue = cookie[1];
            cookies.put(cookieName, cookieValue);
        }
        return cookies;
    }

    // 将 Map 装载为 Cookie 字符串
    private static String mapToCookieString(Map<String, Object> cookiesMap) {
        StringBuilder cookieBuilder = new StringBuilder();
        for (Map.Entry<String, Object> entry : cookiesMap.entrySet()) {
            if (!cookieBuilder.isEmpty()) {
                cookieBuilder.append("; ");
            }
            cookieBuilder.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return cookieBuilder.toString();
    }

    // 更新账号cookies
    private static void optRefreshCookies(String userId, Map<String, String> newCookies){
        try {
            if (newCookies == null || newCookies.isEmpty()){
                return;
            }
            String mapCookies = RedisUtil.getValue(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_COOKIES,userId));
            Map<String, Object> allMap = replaceValues(newCookies,new HashMap<>(JSON.parseObject(mapCookies)));
            RedisUtil.setValue(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.MJ_WEB_COOKIES,userId),JSON.toJSONString(allMap));
        } catch (Exception e) {
            log.error("获取账号cookies异常",e);
        }
    }

    // 替换旧的值
    private static Map<String, Object> replaceValues(Map<String, String> newMap, Map<String, Object> allMap) {
        for (Map.Entry<String, Object> entry : allMap.entrySet()) {
            String allMapKey = entry.getKey();
            if (newMap.containsKey(allMapKey)) {
                allMap.put(allMapKey, newMap.get(allMapKey));
            }
        }
        return allMap;
    }

}
