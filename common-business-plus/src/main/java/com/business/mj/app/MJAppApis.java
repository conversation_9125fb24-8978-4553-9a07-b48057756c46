package com.business.mj.app;

import com.nacos.mjapi.model.MJAccountHeaderBO;
import com.nacos.mjapi.model.MJGoogleTokenInfoBO;
import com.nacos.mjapi.model.MJUseTimeInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.Calendar;
import java.util.Date;

//mj app 接扣：1.13版本
@Slf4j
public class MJAppApis {

    //获取谷歌api token
    public static MJGoogleTokenInfoBO getMjGoogleApiToken(String token){
        return MjAppApiUtil.getGoogleToken(token);
    }

    //获取mj 账户使用情况
    public static MJUseTimeInfo getMJUseTime(MJAccountHeaderBO mjAccountHeaderBO){
        return MjAppApiUtil.postMJUseTime(
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion(),
                mjAccountHeaderBO.getCookie(),
                mjAccountHeaderBO.getMjAccountId()
        );
    }

    //获取cfbm
    public static String getCfbm(String co) {
        String[] parts = co.split("; ");
        String cfBmValue = null;
        for (String part : parts) {
            if (part.startsWith("__cf_bm=")) {
                cfBmValue = part.substring("__cf_bm=".length());
                break;
            }
        }
        return cfBmValue != null ? "__cf_bm="+cfBmValue : null;
    }

    //获取cfbm中的时间戳
    public static Date getCfbmTime(String cfbm) {
        try {
            String pattern = "-(\\d+)-";
            java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = r.matcher(cfbm);
            if (m.find()) {
                long timestamp = Long.parseLong(m.group(1)) * 1000;  // 转换为毫秒
                Calendar calendar = Calendar.getInstance();
                calendar.setTimeInMillis(timestamp);
                calendar.add(Calendar.MINUTE, 15);
                return calendar.getTime();
            }
        } catch (Exception e) {
            log.error("获取cfbm中的时间戳异常", e);
        }
        return null;
    }
}
