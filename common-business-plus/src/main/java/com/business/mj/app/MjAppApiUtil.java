package com.business.mj.app;

import com.alibaba.fastjson2.JSONObject;
import com.business.feishu.BFeiShuApis;
import com.nacos.mjapi.model.MJGoogleTokenInfoBO;
import com.nacos.mjapi.model.MJUseTimeInfo;
import com.nacos.tool.GzipInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.net.SocketTimeoutException;
import java.util.concurrent.TimeUnit;

@Slf4j
public class MjAppApiUtil {
    private static final String CONTENT_TYPE = "application/json";//请求格式

    private static final String MJAPP_NIJIJOURNEY_URL = "http://************:3333";//mjapp代理路由-腾讯云
//    private static final String MJAPP_NIJIJOURNEY_URL = "http://************:3333";//mjapp代理路由-腾讯云-测试随机
//    private static final String MJAPP_NIJIJOURNEY_URL = "https://nijijourney.com";//mjapp代理路由
    public static MJGoogleTokenInfoBO getGoogleToken(String token) {
        String checkKey = "AIzaSyBzVBIfh_wNNScBDcNeuKd4cyspiR8qrMo";
        String url = "https://ddsa.gongfengbangong.com/mjappgoogleapis/v1/token?key=" + checkKey;//代理路径
//        String url = "https://securetoken.googleapis.com/v1/token?key=" + checkKey;//真实路径
        String responseStr;
        String requestStr;
        Response response = null;
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new GzipInterceptor())
                    .connectTimeout(60, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS)
                    .cache(null)
                    .build();
            JSONObject jsonObjectBody = new JSONObject();
            jsonObjectBody.put("grantType", "refresh_token");
            jsonObjectBody.put("refreshToken", token);
            RequestBody requestBody = RequestBody.create(jsonObjectBody.toJSONString(), MediaType.parse("application/json; charset=utf-8"));
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("content-type", CONTENT_TYPE)
                    .addHeader("X-Android-Package", "com.spellbrush.nijijourney")
                    .addHeader("X-Android-Cert", "435F601C5DB63E9A686186D07350EF4CF0C8B1FA")
                    .addHeader("Accept-Language", "zh-CN, en-US")
                    .addHeader("X-Client-Version", "Android/Fallback/X22003001/FirebaseCore-Android")
                    .addHeader("X-Firebase-GMPID", "1:457164738178:android:84ef79ebf52e564d666e9b")
                    .addHeader("X-Firebase-Client", "H4sIAAAAAAAAAKtWykhNLCpJSk0sKVayio7VUSpLLSrOzM9TslIyUqoFAFyivEQfAAAA")
                    .addHeader("Content-Length", String.valueOf(requestBody.contentLength()))
                    .addHeader("User-Agent", "Dalvik/2.1.0 (Linux; U; Android 12; 2206123SC Build/V417IR)")
                    .addHeader("Host", "securetoken.googleapis.com")
                    .addHeader("Connection", "Keep-Alive")
                    .addHeader("Accept-Encoding", "gzip")
                    .post(requestBody)
                    .build();
            response = client.newCall(request).execute();
            String responseBody = null;
            if (response.body() != null) {
                responseBody = response.body().string();
            }
            String responseCookie = response.header("Set-Cookie");
            requestStr = "token:"+token+";\n checkKey: " + checkKey;
            responseStr = "response: "+response+";\n responseBody: "+ responseBody+";\n responseCookie: "+responseCookie;
            if (response.code()!=200){
                log.error("MjAppApiUtil.getGoogleToken:\nrequestStr, {}\nresponseStr,{}",requestStr,responseStr);
                BFeiShuApis.sedCardErrorFromMonitor(BFeiShuApis.P1,"MJAPP-Token刷新异常","MjAppApiUtil.getGoogleToken 非200异常\nrequestStr" + requestStr +"\nresponseStr" +responseStr,false,"自动关闭账号");
                return null;
            }
            JSONObject responseJson = JSONObject.parseObject(responseBody, JSONObject.class);
            MJGoogleTokenInfoBO mjGoogleTokenInfoBO = new MJGoogleTokenInfoBO();
            mjGoogleTokenInfoBO.setAccess_token(responseJson.getString("access_token"));
            mjGoogleTokenInfoBO.setToken_type(responseJson.getString("token_type"));
            mjGoogleTokenInfoBO.setExpires_in(responseJson.getString("expires_in"));
            mjGoogleTokenInfoBO.setRefresh_token(responseJson.getString("refresh_token"));
            mjGoogleTokenInfoBO.setId_token(responseJson.getString("id_token"));
            mjGoogleTokenInfoBO.setUser_id(responseJson.getString("user_id"));
            mjGoogleTokenInfoBO.setProject_id(responseJson.getString("project_id"));
            return mjGoogleTokenInfoBO;
        } catch (Exception e) {
            log.error("mj刷新谷歌token异常{}",e.getMessage(), e);
            if (e instanceof SocketTimeoutException) {
                MJGoogleTokenInfoBO mjGoogleTokenInfoBO = new MJGoogleTokenInfoBO();
                mjGoogleTokenInfoBO.setHttpState("SocketTimeoutException");
                return mjGoogleTokenInfoBO;
            }
            //未知异常自动关闭账号
            BFeiShuApis.sedCardErrorFromMonitor(BFeiShuApis.P1,"MJAPP获取googleToken失败","Exception : "+e.getMessage(),false,"自动关闭账号");
            return null;
        }finally {
            if (response!=null){
                response.close();
            }
        }
    }


    /**
     * 获取时间（mj-时间接口）
     * @param token 请求头：token
     * @param cookie 请求头 cookie
     * @return 返回时间内容
     */
    public static MJUseTimeInfo postMJUseTime(String token, String appVersion, String userAgent, String cookie,Long mjAccountId) {
        String responseStr;
        String requestStr;
        Response response = null;
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new GzipInterceptor())
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .cache(null)
                    .build();
            RequestBody requestBody = RequestBody.create("{}",MediaType.parse("application/json; charset=utf-8"));
            Request request = new Request.Builder()
                    .url(MJAPP_NIJIJOURNEY_URL+"/api/mobile/session?app_version=" + appVersion)
                    .addHeader("authorization", "Bearer "+ token)
                    .addHeader("x-app-version", appVersion)
                    .addHeader("x-csrf-protection", "1")
                    .addHeader("content-length", "0")
                    .addHeader("accept-encoding", "gzip")
                    .addHeader("User-Agent", userAgent)
                    .addHeader("Host", "nijijourney.com")
                    .addHeader("cookie", cookie)
                    .post(requestBody)
                    .build();
            response = client.newCall(request).execute();

            String responseBody = null;
            if (response.body() != null) {
                responseBody = response.body().string();
            }
            String responseCookie = response.header("Set-Cookie");
            requestStr = "token:"+token+";\n appVersion: "+appVersion+";\n userAgent: "+userAgent+";\n cookie:"+cookie;
            responseStr = "response: "+response+";\n responseBody: "+ responseBody+";\n responseCookie: "+responseCookie;
            log.info("postMJUseTime:\nrequestStr, {}",requestStr);
            log.info("postMJUseTime:\nresponseStr, {}",responseStr);
            MJUseTimeInfo mjUseTimeInfo = new MJUseTimeInfo();
            if (response.code() != 200){
                BFeiShuApis.sedCardWarnFromMonitor(BFeiShuApis.P1,"获取MJ APP 有效时间失败","非200异常\nresponse："+response+"\n账号ID：" + mjAccountId);
                return null;
            }
            JSONObject responseJson = JSONObject.parseObject(responseBody, JSONObject.class);
            JSONObject user = responseJson.getJSONObject("userUsage");
            JSONObject plan = user.getJSONObject("plan");
            mjUseTimeInfo.setPeriodCredits(user.getInteger("period_credits"));
            mjUseTimeInfo.setCreditAllocation(plan.getInteger("credit_allocation"));
            mjUseTimeInfo.setCookie(responseCookie);
            return mjUseTimeInfo;
        } catch (Exception e) {
            log.error("mj刷新快速时间异常{}",e.getMessage(), e);
            if (e instanceof SocketTimeoutException) {
                MJUseTimeInfo mjUseTimeInfo = new MJUseTimeInfo();
                mjUseTimeInfo.setHttpState("SocketTimeoutException");
                return mjUseTimeInfo;
            }
            //未知异常自动关闭账号
            BFeiShuApis.sedCardErrorFromMonitor(BFeiShuApis.P1,"MJAPP获取快速时间失败","Exception : "+e.getMessage(),false,"自动关闭账号");
            return null;
        }finally {
            if (response!=null){
                response.close();
            }
        }
    }

}
