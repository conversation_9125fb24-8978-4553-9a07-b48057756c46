package com.business.audio;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.business.audio.model.*;
import com.business.enums.BAudioModelEnum;
import com.business.utils.BUrlUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.nacos.tool.BrotliInterceptor;
import com.nacos.utils.BFeiShuUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
public class SunoAiApiUtil {

    private static final String submitted = "submitted";//提交状态
    private static final String streaming = "streaming";//创作中
    private static final String complete = "complete";//创作完成

    private static final String CLERK_SUNO_URL = "https://suno.iworks.cn";
    // private static final String CLERK_SUNO_URL = "https://clerk.suno.com";
    private static final String PROD_SUNO_URL = "https://prod.suno.iworks.cn";
    // private static final String PROD_SUNO_URL = "https://studio-api.prod.suno.com";

    private static OkHttpClient okHttpClient;

    public static OkHttpClient getInstance() {
        if (okHttpClient == null) {
            okHttpClient = new OkHttpClient.Builder()
                    .readTimeout(15, TimeUnit.MINUTES)
                    .writeTimeout(15, TimeUnit.MINUTES)
                    .build();
        }
        return okHttpClient;
    }

    //灵感音乐
    public static AudioPostJobVO postToAudioInspiration(AudioPostJobBO audioPostJobBO, String authorization) {
        if (audioPostJobBO == null || audioPostJobBO.getType() == null || audioPostJobBO.getMv() == null) {
            return null;
        }
        JSONObject jsonParams = new JSONObject();

        // TYPE_LG2(1,"灵感","灵感")、TYPE_GJ2(2,"高级","高级")、TYPE_ZDY2(3,"自定义","自定义")
        if (Objects.equals(BAudioModelEnum.TYPE_LG2.getId(), audioPostJobBO.getType())) {
            if (audioPostJobBO.getInspiration() == null) {
                return null;
            }
            /*jsonParams.put("gpt_description_prompt", audioPostJobBO.getInspiration());
            jsonParams.put("make_instrumental", audioPostJobBO.getMake_instrumental());
            jsonParams.put("mv", audioPostJobBO.getMv());
            jsonParams.put("prompt", "");*/

            // 添加键值对
            jsonParams.put("token", null);
            jsonParams.put("gpt_description_prompt", audioPostJobBO.getInspiration());
            jsonParams.put("mv", audioPostJobBO.getMv());
            jsonParams.put("prompt", "");

            JSONObject metadata = new JSONObject();
            metadata.put("lyrics_model", "default");
            jsonParams.put("metadata", metadata);
            jsonParams.put("make_instrumental", audioPostJobBO.getMake_instrumental());

            JSONArray userUploadedImagesB64 = new JSONArray();
            jsonParams.put("user_uploaded_images_b64", userUploadedImagesB64);
            jsonParams.put("generation_type", "TEXT");
        } else {
            if (audioPostJobBO.getTags() == null) {
                return null;
            }
            /*jsonParams.put("prompt", audioPostJobBO.getMake_instrumental()?"":audioPostJobBO.getPrompt());
            jsonParams.put("tags", audioPostJobBO.getTags());
            jsonParams.put("mv", audioPostJobBO.getMv());
            jsonParams.put("title", audioPostJobBO.getTitle());
            jsonParams.put("continue_clip_id", null);
            jsonParams.put("continue_at", null);*/

            jsonParams.put("token", null);
            jsonParams.put("prompt", audioPostJobBO.getMake_instrumental() ? "" : audioPostJobBO.getPrompt());
            jsonParams.put("generation_type", "TEXT");
            jsonParams.put("tags", audioPostJobBO.getTags());
            jsonParams.put("negative_tags", "");
            jsonParams.put("mv", audioPostJobBO.getMv());
            jsonParams.put("title", audioPostJobBO.getTitle());
            // 添加可能为 null 的键
            jsonParams.put("continue_clip_id", null);
            jsonParams.put("continue_at", null);
            jsonParams.put("continued_aligned_prompt", null);
            jsonParams.put("infill_start_s", null);
            jsonParams.put("infill_end_s", null);
            jsonParams.put("task", null);
            jsonParams.put("artist_clip_id", null);
            jsonParams.put("artist_start_s", null);
            jsonParams.put("artist_end_s", null);
            // 创建嵌套的 metadata 对象
            JSONObject metadata = new JSONObject();
            metadata.put("create_session_token", UUID.randomUUID().toString());
            jsonParams.put("metadata", metadata);
        }

        Gson gson = new GsonBuilder().serializeNulls().create();
        String jsonString = gson.toJson(jsonParams);
        System.out.println("================================80e3fa84-6286-4bd6-8a84-34d429d842ed");
        System.out.println(jsonString);
        System.out.println("================================");

        RequestBody body = RequestBody.create(jsonString, MediaType.parse("text/plain;charset=UTF-8"));
        Request request = new Request.Builder()
                // .url("https://studio-api.suno.ai/api/generate/v2/")
                .url("https://studio-api.prod.suno.com/api/generate/v2/")
                .addHeader("Accept", "*/*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Authorization", "Bearer " + authorization)
                .addHeader("Content-Length", "145")
                .addHeader("Content-Type", "text/plain;charset=UTF-8")
                .addHeader("Origin", "https://suno.com")
                .addHeader("Referer", "https://suno.com/")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .post(body)
                .build();
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压
            Response response = client.newCall(request).execute();
            log.info("response:{}", response);
            if (response.body() != null) {
                if (response.code() == 402) {
                    return null;
                }
            }
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("responseBody:{}", responseBody);
                JSONObject jsonObject = JSON.parseObject(responseBody);
                JSONArray jsonArray = jsonObject.getJSONArray("clips");
                List<AudioJobItemBO> clips = new ArrayList<>();
                for (Object o : jsonArray) {
                    AudioJobItemBO audioJobItemBO = getAudioJobItemBO((JSONObject) o);
                    clips.add(audioJobItemBO);
                }
                AudioPostJobVO audioPostJob = new AudioPostJobVO();
                audioPostJob.setId(jsonObject.getString("id"));
                audioPostJob.setClips(clips);
                audioPostJob.setPostJobType(audioPostJobBO.getType());
                return audioPostJob;
            }
        } catch (Exception e) {
            e.printStackTrace();
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "suno提交任务失败", "ERRORInfo=" + e.getMessage() + ", param= " + jsonString);
            log.error(e.getMessage(), e);
        }
        return null;
    }


    public static AudioPostJobVO postToAudio(AudioPostJobBO audioPostJobBO, String authorization) {
        if (audioPostJobBO == null) {
            return null;
        }
        JSONObject jsonParams = new JSONObject();
        jsonParams.put("prompt", audioPostJobBO.getPrompt() == null ? "" : audioPostJobBO.getPrompt());
        jsonParams.put("generation_type", "TEXT");
        jsonParams.put("tags", audioPostJobBO.getTags());
        jsonParams.put("negative_tags", "");
        jsonParams.put("mv", audioPostJobBO.getMv());
        jsonParams.put("title", audioPostJobBO.getTitle());
        jsonParams.put("continue_clip_id", audioPostJobBO.getContinue_clip_id());
        jsonParams.put("continue_at", audioPostJobBO.getContinue_at());
        jsonParams.put("continued_aligned_prompt", null);
        jsonParams.put("infill_start_s", null);
        jsonParams.put("infill_end_s", null);
        jsonParams.put("task", "extend");
        jsonParams.put("artist_clip_id", null);
        jsonParams.put("artist_start_s", null);
        jsonParams.put("artist_end_s", null);

//        jsonParams.put("prompt", audioPostJobBO.getPrompt());
//        jsonParams.put("tags", audioPostJobBO.getTags());
//        jsonParams.put("mv", audioPostJobBO.getMv());
//        jsonParams.put("title", audioPostJobBO.getTitle());
//        jsonParams.put("continue_clip_id", audioPostJobBO.getContinue_clip_id());
//        jsonParams.put("continue_at", audioPostJobBO.getContinue_at());
        System.out.println("==============音乐==============");
        Gson gson = new GsonBuilder().serializeNulls().create();
        String jsonString = gson.toJson(jsonParams);

        RequestBody body = RequestBody.create(jsonString, MediaType.parse("text/plain;charset=UTF-8"));
        Request request = new Request.Builder()
                .url("https://studio-api.suno.ai/api/generate/v2/")
                .addHeader("Accept", "*/*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Authorization", "Bearer " + authorization)
                .addHeader("Content-Length", "145")
                .addHeader("Content-Type", "text/plain;charset=UTF-8")
                .addHeader("Origin", "https://app.suno.ai")
                .addHeader("Referer", "https://app.suno.ai")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .post(body)
                .build();
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压
            Response response = client.newCall(request).execute();
            log.info("response:{}", response);
            if (response.code() == 200 && response.body() != null) {
                String responseBody = response.body().string();
                log.info("responseBody:{}", responseBody);
                JSONObject jsonObject = JSON.parseObject(responseBody);
                JSONArray jsonArray = jsonObject.getJSONArray("clips");
                List<AudioJobItemBO> clips = new ArrayList<>();
                for (Object o : jsonArray) {
                    AudioJobItemBO audioJobItemBO = getAudioJobItemBO((JSONObject) o);
                    clips.add(audioJobItemBO);
                }
                AudioPostJobVO audioPostJob = new AudioPostJobVO();
                audioPostJob.setId(jsonObject.getString("id"));
                audioPostJob.setSunoAccountId(1002L);
                audioPostJob.setClips(clips);
                return audioPostJob;
            } else {
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "suno extend歌曲失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()) + ", param= " + jsonParams.toString());
            }
        } catch (Exception e) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "suno extend歌曲失败", "ERRORInfo=" + e.getMessage() + ", param= " + jsonParams.toString());
            log.error(e.getMessage(), e);
        }
        return null;
    }

    @NotNull
    private static AudioJobItemBO getAudioJobItemBO(JSONObject o) {
        AudioJobItemBO audioJobItemBO = new AudioJobItemBO();
        audioJobItemBO.setId(o.getString("id"));
        audioJobItemBO.setModel_name(o.getString("model_name"));
        audioJobItemBO.setVideo_url(o.getString("video_url"));
        audioJobItemBO.setAudio_url(o.getString("audio_url"));
        audioJobItemBO.setImage_url(o.getString("image_url"));
        audioJobItemBO.setMajor_model_version(o.getString("major_model_version"));
        audioJobItemBO.setTitle(o.getString("title"));
        audioJobItemBO.setUser_id(o.getString("user_id"));
        audioJobItemBO.setStatus(o.getString("status"));
        JSONObject jsonObject2 = o.getJSONObject("metadata");
        audioJobItemBO.setTags(jsonObject2.getString("tags"));
        audioJobItemBO.setPrompt(jsonObject2.getString("prompt"));
        return audioJobItemBO;
    }

    public static List<AudioJobItemBO> getToAudioInfo(List<String> jobIds, String authorization) {
        try {
            if (jobIds == null || jobIds.isEmpty()) {
                return null;
            }
            StringBuilder jobIdsStr = new StringBuilder();
            for (String jobId : jobIds) {
                jobIdsStr.append(jobId).append(",");
            }
            String encodedString = jobIdsStr.toString();
            encodedString = encodedString.replaceAll(",+$", "");//去掉最后一个逗号
            encodedString = URLEncoder.encode(encodedString, StandardCharsets.UTF_8);//进行url转码
            log.info("getToAudioInfo:{}", "https://studio-api.suno.ai/api/feed/v2?ids=" + encodedString + "&page=5000");
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压
            Request request = new Request.Builder()
                    .url("https://studio-api.suno.ai/api/feed/v2?ids=" + encodedString + "&page=5000")
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Authorization", "Bearer " + authorization)
                    .addHeader("Origin", "https://app.suno.ai")
                    .addHeader("Referer", "https://app.suno.ai/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .get()
                    .build();
            Response response = client.newCall(request).execute();
            log.info("response:{}", response);
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("responseBody:{}", responseBody);
                JSONObject jsonObject = JSON.parseObject(responseBody);
                JSONArray jsonArray = jsonObject.getJSONArray("clips");
                List<AudioJobItemBO> audioJobItemBOS = new ArrayList<>();
                for (Object o : jsonArray) {
                    AudioJobItemBO audioJobItemBO = getAudioJobItemBO((JSONObject) o);
                    audioJobItemBOS.add(audioJobItemBO);
                }
                return audioJobItemBOS;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    //优化歌词
    public static String optimizeLyric(String prompt, String authorization) {
        try {
            if (prompt == null) {
                prompt = "";
            }
            String url = "https://studio-api.suno.ai/api/generate/lyrics/";
            log.info("optimizeLyric url:{}", url);
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压

            JSONObject json = new JSONObject();
            json.put("prompt", prompt);
            String jsonBody = json.toString();
            RequestBody body = RequestBody.create(jsonBody, MediaType.parse("text/plain;charset=UTF-8"));
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Authorization", "Bearer " + authorization)
                    .addHeader("Content-Length", String.valueOf(jsonBody.length()))
                    .addHeader("Content-Type", "text/plain;charset=UTF-8")
                    .addHeader("Origin", "https://app.suno.ai")
                    .addHeader("Referer", "https://app.suno.ai")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .post(body)
                    .build();
            Response response = client.newCall(request).execute();
            log.info("response:{}", response);
            if (response.body() != null) {
                String responseBody = response.body().string();
                log.info("responseBody:{}", responseBody);
                JSONObject jsonObject = JSON.parseObject(responseBody);
                return jsonObject.getString("id");

            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    //查询歌词
    public static AudioLyricBO getOptimizeLyric(String id, String authorization) {
        try {
            if (id == null || id.isEmpty()) {
                return null;
            }
            String url = "https://studio-api.suno.ai/api/generate/lyrics/" + id;
            log.info("getOptimizeLyric url:{}", url);
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压
            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Authorization", "Bearer " + authorization)
                    .addHeader("Origin", "https://app.suno.ai")
                    .addHeader("Referer", "https://app.suno.ai/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .get()
                    .build();
            Response response = client.newCall(request).execute();
            log.info("response:{}", response);
            if (response.code() == 200 && response.body() != null) {
                String responseBody = response.body().string();
                log.info("responseBody:{}", responseBody);
                JSONObject jsonObject = JSON.parseObject(responseBody);
                String status = jsonObject.getString("status");
                if (Objects.equals(complete, status)) {
                    //完成状态的歌词
                    AudioLyricBO audioLyricBO = new AudioLyricBO();
                    audioLyricBO.setText(jsonObject.getString("text"));
                    audioLyricBO.setTitle(jsonObject.getString("title"));
                    return audioLyricBO;
                }
                return new AudioLyricBO();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    public static String getApiToken(String sessionId, String cookies) {
        try {
            RequestBody formBody = new FormBody.Builder()
                    .build();
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压
            Request request = new Request.Builder()
                    .url("https://clerk.suno.ai/v1/client/sessions/" + sessionId + "/tokens/api?_clerk_js_version=4.73.3")
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Content-Length", "0")
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Cookie", cookies)
                    .addHeader("Origin", "https://app.suno.ai")
                    .addHeader("Referer", "https://app.suno.ai/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .post(formBody)
                    .build();
            Response response = client.newCall(request).execute();
            log.info("音频获取token response:{}", response);
            if (response.body() != null) {
                String body = response.body().string();
                log.info("音频获取token body:{}", body);
                String responseCookie = response.header("Set-Cookie");
                log.info("音频获取token responseCookie:{}", responseCookie);
                JSONObject jsonObject = JSON.parseObject(body);
                return jsonObject.getString("jwt");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static SunoGetTokenCookBO getApiTwtAndToken(String url, SunoGetTokenCookBO sunoGetTokenCookBOParam) {
        try {
            String getTokenCookies = getTokenCookies(sunoGetTokenCookBOParam);
            if (getTokenCookies == null) {
                return null;
            }
            RequestBody formBody = new FormBody.Builder()
                    .build();
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压
            Request request = new Request.Builder()
                    .url("https://clerk.suno.ai/v1/client/sessions/" + url + "/tokens?_clerk_js_version=4.72.0-snapshot.vc141245")
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Content-Length", "0")
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Cookie", getTokenCookies)
                    .addHeader("Origin", "https://app.suno.ai")
                    .addHeader("Referer", "https://app.suno.ai/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .post(formBody)
                    .build();
            Response response = client.newCall(request).execute();
            log.info("音频获取token response:{}", response);
            if (response.body() != null) {
                String body = response.body().string();
                log.info("音频获取token body:{}", body);
                // 获取 Set-Cookie
                HashMap<String, String> responseCookiesMap = new HashMap<>();
                String setCookieHeader = response.header("Set-Cookie");
                log.info("音频获取token setCookieHeader:{}", setCookieHeader);
                if (setCookieHeader != null) {
                    // 分割多个 Cookie
                    String[] responseCookies = setCookieHeader.split("; ");
                    for (String cookiePair : responseCookies) {
                        // 分割键值对
                        String[] keyValue = cookiePair.split("=", 2);
                        if (keyValue.length == 2) {
                            String key = keyValue[0];
                            String value = keyValue[1];
                            responseCookiesMap.put(key, value);
                        }
                    }
                }
                SunoGetTokenCookBO sunoGetTokenCookBO = new SunoGetTokenCookBO();
                if (response.code() == 200) {
                    responseCookiesMap.forEach(
                            (key, value) -> {
                                if ("__cf_bm".equals(key)) {
                                    sunoGetTokenCookBO.setCfbm(value);
                                } else if ("__client".equals(key)) {
                                    sunoGetTokenCookBO.setClient(value);
                                } else if ("__client_uat".equals(key)) {
                                    sunoGetTokenCookBO.setClientUat(value);
                                } else if ("_cfuvid".equals(key)) {
                                    sunoGetTokenCookBO.setCfuvid(value);
                                } else if ("mp_26ced217328f4737497bd6ba6641ca1c_mixpanel".equals(key)) {
                                    sunoGetTokenCookBO.setMp(value);
                                }
                            }
                    );
                    if (body.contains("jwt")) {
                        JSONObject jsonObject = JSON.parseObject(body);
                        sunoGetTokenCookBO.setJwt((String) jsonObject.get("jwt"));
                        return sunoGetTokenCookBO;
                    }
                } else if (response.code() == 429) {
                    //TODO 429
                    BFeiShuUtil.sedCardWarnFromMonitor(BFeiShuUtil.P4, "SUNO定时刷新JWT失败", "临时测试使用后期去掉：body: " + body + "\nresponse: " + response);
                    return sunoGetTokenCookBO;
                }
                log.error("音频获取token response:{}\nbody:{}", body, response);
                BFeiShuUtil.sedCardWarnFromMonitor(BFeiShuUtil.P4, "SUNO定时刷新JWT失败", "body: " + body + "\nresponse: " + response);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            BFeiShuUtil.sedCardWarnFromMonitor(BFeiShuUtil.P4, "SUNO定时刷新JWT失败", "异常" + e.getMessage());

        }
        return null;
    }

    public static String getTokenCookies(SunoGetTokenCookBO sunoGetTokenCookBO) {
        if (sunoGetTokenCookBO == null || sunoGetTokenCookBO.getCfbm() == null || sunoGetTokenCookBO.getClient() == null || sunoGetTokenCookBO.getCfuvid() == null || sunoGetTokenCookBO.getClientUat() == null || sunoGetTokenCookBO.getMp() == null) {
            return null;
        }
        return "__cf_bm=" + sunoGetTokenCookBO.getCfbm() + "; " +
                "__client=" + sunoGetTokenCookBO.getClient() + "; " +
                "__client_uat=" + sunoGetTokenCookBO.getClientUat() + "; " +
                "_cfuvid=" + sunoGetTokenCookBO.getCfuvid() + "; " +
                "mp_26ced217328f4737497bd6ba6641ca1c_mixpanel=" + sunoGetTokenCookBO.getMp() + "; ";
    }

    // TODO ============================== app接口请求 - 新的接口============================
    // 获取音乐sessionid
    public static String getSunoSessionsId(String token, String clerkJsVersion) {
        Request request = new Request.Builder()
                .url(CLERK_SUNO_URL + "/v1/client?_is_native=true&_clerk_js_version=" + clerkJsVersion)
                .get()
                .addHeader("authorization", token)
                .addHeader("user-agent", "okhttp/4.12.0")
                .addHeader("host", "suno.iworks.cn")
                .addHeader("content-type", "application/x-www-form-urlencoded")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .build();
        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("getSunoSessionsId responseBody:{}", responseBody);
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                JSONObject objectResponse = jsonObject.getJSONObject("response");
                JSONArray sessionArray = objectResponse.getJSONArray("sessions");
                JSONObject objectSession = sessionArray.getJSONObject(0);
                return objectSession.getString("id");
            }
        } catch (Exception e) {
            log.error("suno拉取任务=============================");
            log.error(e.getMessage(), e);
            log.error("suno拉取任务=============================");
            return null;
        }
        return null;
    }

    // 获取音乐token 根据 sessionid
    public static String getSunoTokenBySessionId(String sessionId, String clerkJsVersion, String token) {
        RequestBody formBody = new FormBody.Builder().build();
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(new BrotliInterceptor())
                .build();//手动处理解压
        Request request = new Request.Builder()
                .url(CLERK_SUNO_URL + "/v1/client/sessions/" + sessionId + "/tokens?_is_native=true&_clerk_js_version=" + clerkJsVersion)
                .post(formBody)
                .addHeader("content-type", "application/x-www-form-urlencoded")
                .addHeader("authorization", token)
                .addHeader("content-length", "0")
                .addHeader("accept-encoding", "gzip")
                .addHeader("user-agent", "okhttp/4.12.0")
                .addHeader("host", "suno.iworks.cn")
                .build();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("getSunoTokenBySessionId responseBody:{}", responseBody);
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSON.parseObject(responseBody);
                return jsonObject.getString("jwt");
            }
        } catch (Exception e) {
            log.error("getAudioJobItemBO异常");
            log.error(e.getMessage(), e);
            return null;
        }
        return null;
    }

    // 提交音乐到suno 根据token
    public static AudioPostJobVO submitToSunoGenerateMusic(AudioPostJobBO audioPostJobBO, String jwtToken) {
        if (audioPostJobBO == null || audioPostJobBO.getType() == null || audioPostJobBO.getMv() == null) {
            return null;
        }

        JSONObject jsonObjectReq = new JSONObject();
        if (Objects.equals(audioPostJobBO.getType(), BAudioModelEnum.TYPE_LG2.getId())) { // 1灵感；2高级；3自定义
            // 简单音乐生成
            jsonObjectReq.put("prompt", "");
            jsonObjectReq.put("mv", audioPostJobBO.getMv());
            jsonObjectReq.put("gpt_description_prompt", audioPostJobBO.getInspiration());
            jsonObjectReq.put("make_instrumental", audioPostJobBO.getMake_instrumental());
            jsonObjectReq.put("generation_type", audioPostJobBO.getGeneration_type());
        } else {
            // 自定义音乐生成
            jsonObjectReq.put("prompt", audioPostJobBO.getPrompt());
            jsonObjectReq.put("mv", audioPostJobBO.getMv());
            jsonObjectReq.put("tags", audioPostJobBO.getTags());
            jsonObjectReq.put("title", audioPostJobBO.getTitle());
            jsonObjectReq.put("make_instrumental", audioPostJobBO.getMake_instrumental());
            jsonObjectReq.put("generation_type", audioPostJobBO.getGeneration_type());
        }
        System.out.println(jsonObjectReq.toString());
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), jsonObjectReq.toString());
        Request request = new Request.Builder()
                .url(PROD_SUNO_URL + "/api/generate/v2/") // .url(PROD_SUNO_URL + "/api/generate/v2/")
                .post(body)
                .addHeader("x-suno-client", "Android prerelease-4nt180t 1.0.42")
                .addHeader("authorization", "Bearer " + jwtToken)
                .addHeader("user-agent", "okhttp/4.12.0")
                .addHeader("host", "prod.suno.iworks.cn")
                .addHeader("content-type", "application/json; charset=utf-8")
                .build();

        OkHttpClient client = getInstance();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("submitToSunoGenerateMusic responseBody:{}", responseBody);
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSON.parseObject(responseBody);
                JSONArray jsonArray = jsonObject.getJSONArray("clips");
                List<AudioJobItemBO> clips = new ArrayList<>();
                for (Object o : jsonArray) {
                    AudioJobItemBO audioJobItemBO = getAudioJobItemBO((JSONObject) o);
                    clips.add(audioJobItemBO);
                }
                AudioPostJobVO audioPostJob = new AudioPostJobVO();
                audioPostJob.setId(jsonObject.getString("id"));
                audioPostJob.setClips(clips);
                audioPostJob.setPostJobType(audioPostJobBO.getType());
                return audioPostJob;
            }
        } catch (Exception e) {
            log.error("submitToSunoGenerateMusic异常");
            log.error(e.getMessage(), e);
            return null;
        }
        return null;
    }

    // 获取音乐返回结果
    public static List<AudioJobItemBO> getSunoJobResultByJobIds(List<String> jobIds, String token) {
        if (jobIds == null || jobIds.isEmpty()) {
            return null;
        }
        StringBuilder jobIdsStr = new StringBuilder();
        for (String jobId : jobIds) {
            jobIdsStr.append(jobId).append(",");
        }
        String encodedString = jobIdsStr.toString();
        encodedString = encodedString.replaceAll(",+$", "");//去掉最后一个逗号
        encodedString = URLEncoder.encode(encodedString, StandardCharsets.UTF_8);//进行url转码

        Request request = new Request.Builder()
                .url(PROD_SUNO_URL + "/api/feed/v2?ids=" + encodedString) // .url(PROD_SUNO_URL + "/api/feed/v2?ids=" + encodedString)
                .get()
                .addHeader("content-type", "application/json")
                .addHeader("x-suno-client", "Android prerelease-4nt180t 1.0.42")
                .addHeader("authorization", "Bearer " + token)
                .addHeader("accept-encoding", "gzip")
                .addHeader("user-agent", "okhttp/4.12.0")
                .addHeader("host", "prod.suno.iworks.cn")
                .build();
        OkHttpClient httpClient = new OkHttpClient.Builder()
                .addInterceptor(new BrotliInterceptor())
                .build();
        try (Response response = httpClient.newCall(request).execute();) {
            String responseBody = response.body().string();
            log.info("getSunoJobResultByJobIds responseBody:{}", responseBody);
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSON.parseObject(responseBody);
                JSONArray jsonArray = jsonObject.getJSONArray("clips");
                List<AudioJobItemBO> audioJobItemBOS = new ArrayList<>();
                for (Object o : jsonArray) {
                    AudioJobItemBO audioJobItemBO = getAudioJobItemBO((JSONObject) o);
                    audioJobItemBOS.add(audioJobItemBO);
                }
                return audioJobItemBOS;
            }
        } catch (Exception e) {
            log.error("获取音乐getSunoJobResultByJobIds异常");
            log.error(e.getMessage(), e);
            return null;
        }
        return null;
    }

    // 生成suno 随机歌词
    public static String requestSunoRandomLyrics(String prompt, String token) {
        JSONObject jsonObjectReq = new JSONObject();
        // jsonObjectReq.put("prompt", prompt);
        jsonObjectReq.put("tags", "");
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(mediaType, jsonObjectReq.toString());
        Request request = new Request.Builder()
                .url(PROD_SUNO_URL + "/api/generate/lyrics/")
                .post(body)
                .addHeader("x-suno-client", "Android prerelease-4nt180t 1.0.45")
                .addHeader("authorization", "Bearer " + token)
                .addHeader("user-agent", "okhttp/4.12.0")
                .addHeader("host", "prod.suno.iworks.cn")
                .build();

        OkHttpClient httpClient = new OkHttpClient.Builder()
                .addInterceptor(new BrotliInterceptor())
                .build();//手动处理解压
        try (Response response = httpClient.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("requestSunoRandomLyrics responseBody:{}", responseBody);
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSON.parseObject(responseBody);
                return jsonObject.getString("id");
            }
        } catch (Exception e) {
            log.error("RandomLyrics异常");
            log.error(e.getMessage(), e);
            return null;
        }
        return null;
    }

    // 获取suno 随机歌词
    public static AudioLyricBO getSunoRandomLyrics(String id, String token) {
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(new BrotliInterceptor())
                .build();
        Request request = new Request.Builder()
                .url(PROD_SUNO_URL + "/api/generate/lyrics/" + id)
                .get()
                .addHeader("host", "prod.suno.iworks.cn")
                .addHeader("x-suno-client", "Android prerelease-4nt180t 1.0.45")
                .addHeader("authorization", "Bearer " + token)
                .addHeader("user-agent", "okhttp/4.12.0")
                .addHeader("content-type", "application/json")
                .build();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("requestSunoRandomLyrics responseBody:{}", responseBody);
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSON.parseObject(responseBody);
                String status = jsonObject.getString("status");
                if (Objects.equals(complete, status)) {
                    //完成状态的歌词
                    AudioLyricBO audioLyricBO = new AudioLyricBO();
                    audioLyricBO.setText(jsonObject.getString("text"));
                    audioLyricBO.setTitle(jsonObject.getString("title"));
                    return audioLyricBO;
                }
            }
        } catch (Exception e) {
            log.error("getSunoRandomLyrics异常");
            log.error(e.getMessage(), e);
            return null;
        }
        return null;
    }

}
