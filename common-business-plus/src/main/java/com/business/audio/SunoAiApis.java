package com.business.audio;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.business.audio.model.QueryUploadMusicResp;
import com.business.audio.model.SunoGetTokenCookBO;
import com.business.audio.model.UploadMusic1Resp;
import com.business.sd.SDApisUtil;
import com.nacos.tool.BrotliInterceptor;
import com.nacos.utils.BFeiShuUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.HashMap;

@Slf4j
public class SunoAiApis {

    private static final String submitted = "submitted";//提交状态
    private static final String streaming = "streaming";//创作中
    private static final String complete = "complete";//创作完成
    private static final String SUNO_URL = "https://clerk.suno.com";//suno url
//    private static final String ROUTE_SKIP_URL = "https://chatopenapi.suxiaobaoai.com";//路由跳转url
    private static final String ROUTE_SKIP_URL = "https://suno.iworks.cn";//路由跳转url

    public static SunoGetTokenCookBO getApiTwtAndToken(String version, SunoGetTokenCookBO sunoGetTokenCookBOParam) {
        try {
            String getTokenCookies = getTokenCookies(sunoGetTokenCookBOParam);
            if (getTokenCookies == null){
                return null;
            }
            RequestBody formBody = new FormBody.Builder()
                    .build();
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压
            Request request = new Request.Builder()
                    .url(ROUTE_SKIP_URL + "/v1/client/sessions/"+sunoGetTokenCookBOParam.getSessionId()+"/tokens?__clerk_api_version=2021-02-05&_clerk_js_version="+version)
                    // "https://clerk.suno.com/v1/client/sessions/sess_2oVheJEV1yATg5x4MPCzVhbY7cQ          /tokens?__clerk_api_version=2021-02-05&_clerk_js_version=5.34.3"
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Content-Length", "0")
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Cookie", getTokenCookies)
                    .addHeader("Origin", "https://suno.com")
                    .addHeader("Referer", "https://suno.com/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .post(formBody)
                    .build();
            Response response = client.newCall(request).execute();
            log.info("音频获取token response:{}", response);
            if (response.body() != null) {
                String body = response.body().string();
                log.info("音频获取token body:{}", body);
                // 获取 Set-Cookie
                HashMap<String, String> responseCookiesMap = new HashMap<>();
                String setCookieHeader = response.header("Set-Cookie");
                log.info("音频获取token setCookieHeader:{}", setCookieHeader);
                if (setCookieHeader != null) {
                    // 分割多个 Cookie
                    String[] responseCookies = setCookieHeader.split("; ");
                    for (String cookiePair : responseCookies) {
                        // 分割键值对
                        String[] keyValue = cookiePair.split("=", 2);
                        if (keyValue.length == 2) {
                            String key = keyValue[0];
                            String value = keyValue[1];
                            responseCookiesMap.put(key, value);
                        }
                    }
                }
                SunoGetTokenCookBO sunoGetTokenCookBO = new SunoGetTokenCookBO();
                if (response.code() == 200){
                    responseCookiesMap.forEach(
                            (key, value) -> {
                                if ("__cf_bm".equals(key)) {
                                    sunoGetTokenCookBO.setCfbm(value);
                                } else if ("__client".equals(key)) {
                                    sunoGetTokenCookBO.setClient(value);
                                } else if ("__client_uat".equals(key)) {
                                    sunoGetTokenCookBO.setClientUat(value);
                                } else if ("_cfuvid".equals(key)) {
                                    sunoGetTokenCookBO.setCfuvid(value);
                                }else if ("mp_26ced217328f4737497bd6ba6641ca1c_mixpanel".equals(key)){
                                    sunoGetTokenCookBO.setMp(value);
                                }
                            }
                    );
                    if (body.contains("jwt")){
                        JSONObject jsonObject = JSON.parseObject(body);
                        sunoGetTokenCookBO.setJwt((String) jsonObject.get("jwt"));
                        return sunoGetTokenCookBO;
                    }
                }else if (response.code() == 429){
                    //TODO 429
                    BFeiShuUtil.sedCardWarnFromMonitor(BFeiShuUtil.P4, "SUNO刷新JWT失败", "临时测试使用后期去掉：body: " +body +"\nresponse: "+response);
                    return sunoGetTokenCookBO;
                }
                log.error("音频获取token response:{}\nbody:{}", body,response);
                BFeiShuUtil.sedCardWarnFromMonitor(BFeiShuUtil.P4, "SUNO刷新JWT失败", "body: " +body +"\nresponse: "+response);
            }
        } catch (Exception e) {
            if (e instanceof SocketTimeoutException){
                log.error("超时异常无需处理, {}",e.getMessage());
                return new SunoGetTokenCookBO();
            }
            log.error(e.getMessage(), e);
            BFeiShuUtil.sedCardWarnFromMonitor(BFeiShuUtil.P4, "SUNO刷新JWT失败", "异常:"+e.getMessage());

        }
        return null;
    }

    public static String getTokenCookies(SunoGetTokenCookBO sunoGetTokenCookBO) {
        if (sunoGetTokenCookBO == null || sunoGetTokenCookBO.getCfbm() == null || sunoGetTokenCookBO.getClient() == null || sunoGetTokenCookBO.getCfuvid() == null || sunoGetTokenCookBO.getClientUat() == null || sunoGetTokenCookBO.getMp() == null || sunoGetTokenCookBO.getSessionId() == null){
            return null;
        }
        return "__cf_bm=" + sunoGetTokenCookBO.getCfbm() + "; " +
                "__client=" + sunoGetTokenCookBO.getClient() + "; " +
                "__client_uat=" + sunoGetTokenCookBO.getClientUat() + "; " +
                "_cfuvid=" + sunoGetTokenCookBO.getCfuvid() + "; " +
                "mp_26ced217328f4737497bd6ba6641ca1c_mixpanel=" + sunoGetTokenCookBO.getMp() + "; ";
    }



    //suno文件上传 1/5
    //extension 音乐文件类型
    public static UploadMusic1Resp uploadMusic1(String extension, String authorization) {
        try {
            JSONObject json = new JSONObject();
            json.put("extension", extension);
            String jsonBody = json.toString();
            RequestBody body = RequestBody.create(jsonBody, MediaType.parse("text/plain;charset=UTF-8"));

            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压
            Request request = new Request.Builder()
                    .url("https://studio-api.suno.ai/api/uploads/audio/")
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Authorization", "Bearer " + authorization)
                    .addHeader("Content-Length", "19")
                    .addHeader("Content-Type", "text/plain;charset=UTF-8")
                    .addHeader("Origin", "https://suno.com")
                    .addHeader("Referer", "https://suno.com/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .post(body)
                    .build();

            Response response = client.newCall(request).execute();
            log.info("本地音乐上传uploadMusic1 response:{}", response);
            if ( response.body() != null && response.code() == 200 ) {
                String responseBody = response.body().string();
                log.info("responseBody:{}", responseBody);
                UploadMusic1Resp uploadMusic1Resp = JSONObject.parseObject(responseBody, UploadMusic1Resp.class);
                return uploadMusic1Resp;

            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    //suno文件上传 2/5

    public static String uploadMusic2(MultipartFile mulFile, UploadMusic1Resp uploadMusic1Resp) {
        // 要上传的图片文件路径
        File file = SDApisUtil.convert(mulFile);

        MediaType MEDIA_TYPE = MediaType.parse("audio/mpeg");

        // 创建 RequestBody
        RequestBody fileBody = RequestBody.create(file, MEDIA_TYPE);

        // 创建 MultipartBody
        MultipartBody formBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("Content-Type", uploadMusic1Resp.getFields().getContentType())
                .addFormDataPart("key", uploadMusic1Resp.getFields().getKey())
                .addFormDataPart("AWSAccessKeyId", uploadMusic1Resp.getFields().getAWSAccessKeyId())
                .addFormDataPart("policy", uploadMusic1Resp.getFields().getPolicy())
                .addFormDataPart("signature", uploadMusic1Resp.getFields().getSignature())
                .addFormDataPart("file", file.getName(), fileBody)
                .build();
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor()) // 假设你有一个 BrotliInterceptor 类
                    .build();

            Request request = new Request.Builder()
                    .url("https://suno-uploads.s3.amazonaws.com")
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Content-Length", "1407938")
                    .addHeader("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundary1GTPDJlEBFmcvmIS")
                    .addHeader("Host", "suno-uploads.s3.amazonaws.com")
                    .addHeader("Origin", "https://suno.com")
                    .addHeader("Referer", "https://suno.com/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .post(formBody)
                    .build();

            try (Response response = client.newCall(request).execute()) {
                log.info("本地音乐上传uploadMusic2 response:{}", response);
                if (response.body() != null && (response.code() == 200 || response.code() == 204)) {
                    String responseBody = response.body().string();
                    log.info("responseBody:{}", responseBody);
                    return "success";
                } else {
                    log.error("Unexpected response code: {}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("Error during upload: {}", e.getMessage(), e);
        }
        return "false";
    }

    //suno文件上传 3/5
    //id sunoid
    public static boolean uploadMusic3(String fileName,String id, String authorization) {

        String mediaTypeJson = "application/json";
        JSONObject json = new JSONObject();
        json.put("upload_filename",fileName);
        json.put("upload_type","file_upload");

        RequestBody body = RequestBody.create(json.toString(), MediaType.parse(mediaTypeJson));

        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压
            Request request = new Request.Builder()
                    .url("https://studio-api.suno.ai/api/uploads/audio/"+ id +"/upload-finish/")
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Authorization", "Bearer " + authorization)
                    .addHeader("Content-Length", "19")
                    .addHeader("Content-Type", "text/plain;charset=UTF-8")
                    .addHeader("Origin", "https://suno.com")
                    .addHeader("Referer", "https://suno.com/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .post(body)
                    .build();

            Response response = client.newCall(request).execute();
            log.info("本地音乐上传uploadMusic3 response:{}", response);
            if ( response.body() != null && response.code() == 200 ) {
                String responseBody = response.body().string();
                log.info("responseBody:{}", responseBody);
                return true;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }


    //suno文件上传 4/5
    //id sunoid
    public static QueryUploadMusicResp queryUploadMusicStatus(String id, String authorization) {

        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压

            Request request = new Request.Builder()
                    .url("https://studio-api.suno.ai/api/uploads/audio/"+ id +"/")
                    .addHeader("Accept", "application/json")  // 根据 API 需求设置请求头
                    .addHeader("Authorization", "Bearer " + authorization)
                    .build();

            Response response = client.newCall(request).execute();
            log.info("本地音乐上传queryUploadMusicStatus response:{}", response);
            if ( response.body() != null && response.code() == 200 ) {
                String responseBody = response.body().string();
                log.info("responseBody:{}", responseBody);

                QueryUploadMusicResp queryUploadMusicResp = JSONObject.parseObject(responseBody, QueryUploadMusicResp.class);
                return queryUploadMusicResp;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

  //suno文件上传 5/5
    //id sunoid

    public static String initUploadMusicStatus(String s3Id, String authorization) {

        RequestBody emptyBody = RequestBody.create("", MediaType.parse("application/json; charset=utf-8"));

        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .addInterceptor(new BrotliInterceptor())
                    .build();//手动处理解压
            Request request = new Request.Builder()
                    .url("https://studio-api.suno.ai/api/uploads/audio/"+ s3Id +"/initialize-clip/")
                    .addHeader("Accept", "*/*")
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Authorization", "Bearer " + authorization)
                    .addHeader("Content-Length", "19")
                    .addHeader("Content-Type", "text/plain;charset=UTF-8")
                    .addHeader("Origin", "https://suno.com")
                    .addHeader("Referer", "https://suno.com/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .post(emptyBody)
                    .build();

            Response response = client.newCall(request).execute();
            log.info("本地音乐上传initUploadMusicStatus response:{}", response);
            if ( response.body() != null && response.code() == 200 ) {
                String responseBody = response.body().string();
                JSONObject jsonObject =  JSONObject.parseObject(responseBody);
                String clipId = jsonObject.getString("clip_id");
                log.info("responseBody:{}", responseBody);
                return clipId;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

}
