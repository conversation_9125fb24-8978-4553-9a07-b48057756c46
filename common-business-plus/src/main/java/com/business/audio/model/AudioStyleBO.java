package com.business.audio.model;

import com.business.utils.BUrlUtil;
import lombok.Data;

//音频参数封装实体
@Data
public class AudioStyleBO {
    private Long id;//提交任务id
    private String name;//展示内容
    private String url;//图片展示
    private Integer isVip;//是否vip使用
    private Integer state;//使用状态：禁用、升级中
    private String vipUseTag;//vip标签展示内容
    private String stateOffTag;//状态禁用展示内容

    public String getUrl() {
        return BUrlUtil.getBaseCdnUrl(url);
    }
}
