package com.business.audio.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

//获取音频参数实体
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AudioPostJobBO {
    public AudioPostJobBO(String prompt, String tags, String mv, String title) {
        this.prompt = prompt;
        this.tags = tags;
        this.mv = mv;
        this.title = title;
        this.continue_clip_id = null;
        this.continue_at = null;
        this.generation_type = "TEXT";
        this.make_instrumental = false;//默认非纯音乐
    }

    private String prompt;//歌词
    private String tags;//风格
    private String mv;//模型版本
    private String title;//标题
    private String continue_clip_id;
    private Long continue_at;
    private Boolean make_instrumental;//是否纯音乐
    private Integer type;//请求类型：1灵感；2高级；3自定义
    private String inspiration;//仅灵感时使用
    private String generation_type; //类型
}
