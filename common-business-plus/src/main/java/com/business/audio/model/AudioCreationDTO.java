package com.business.audio.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Schema(name = "音频创作参数实体", description = "音频创作参数实体")
@Data
@Validated
public class AudioCreationDTO {

    @NotNull(message = "音频模型不能为空！")
    @Schema(name = "音频模型id：默认1", type = "Long")
    private Long modelId;

    @Schema(name = "音频创作风格id", type = "Long")
    private Long styleId;

    @Schema(name = "音频创作标签风格id", type = "List<Long>")
    private List<Long> tagIds;

    @Schema(name = "歌词标题", type = "String")
    private String songLabel;

    @Schema(name = "歌词内容", type = "String")
    private String songLyrics;

    @Schema(name = "用户id", type = "String", hidden = true)
    private Long userId;

    @Schema(name = "是否为纯音乐：v2.3", type = "Boolean")
    private Boolean isPureMusic;

    @Schema(name = "自定义词曲风格（类型3-使用）：v2.3", type = "String")
    private String customStyle;

    @Schema(name = "灵感（类型1-使用）：v2.3", type = "String")
    private String inspiration;

    @Schema(name = "请求类型（1灵感；2高级；3自定义）：v2.3", type = "Integer")
    private Integer type;

    public Boolean getPureMusic() {
        return isPureMusic != null && isPureMusic;
    }

    public Integer getType() {
        return type == null ? 2 : type;
    }
}
