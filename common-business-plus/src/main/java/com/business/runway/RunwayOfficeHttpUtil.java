package com.business.runway;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.business.runway.model.RunWayOfficeTaskReq;
import com.business.runway.model.RunWayOfficeTaskResp;
import com.nacos.tool.BrotliInterceptor;
import com.nacos.utils.BFeiShuUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

//Luma工具类
@Slf4j
public class RunwayOfficeHttpUtil {

    //翻墙用的url 原url：https://api.runwayml.com
    private static final String RUNWAY_API_URL = "https://api.dev.runwayml.com";

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();


    /**
     * 提交达芬奇任务
     * @param runWayTaskReq 请求参数
     * @param token 令牌
     * @return 任务ID
     */
    public static String postRunWayToGenerationVideo(RunWayOfficeTaskReq runWayTaskReq, String token) {
        log.info("runway postRunWayToGenerationVideo RequestBody={}", JSONObject.toJSONString(runWayTaskReq));
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/image_to_video")
                .post(RequestBody.create(JSONObject.toJSONString(runWayTaskReq), MediaType.parse("application/json")))
                .addHeader("authorization", "Bearer " + token)
                .addHeader("Content-Type", "application/json")
                .addHeader("X-Runway-Version", "2024-11-06")
                // .addHeader("X-Runway-Version","2024-09-13")
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("RUNWAY视频提交Code：" + response.code() + "。 请求结果为：" + responseBody);
                if (response.code() == 200) {
                    if (null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        return jsonObject.getString("id");
                    }
                } else if (response.code() == 400) {
                    JSONObject jsonObject = JSONObject.parseObject(responseBody);
                    String error = jsonObject.getString("error");
                    if ("You do not have enough credits to run this task.".equals(error)) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + "达芬奇需要充值!");
                        return null;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY视频提交失败：{}", e.getMessage());
                e.printStackTrace();
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + e.getMessage() + ", param= " + JSONObject.toJSONString(runWayTaskReq));
                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                    return null;
                }
            }
        }
        return null;
    }

    /**
     * 获取达芬奇任务状态
     * @param jobId 任务ID
     * @param token 令牌
     * @return 任务状态
     */
    public static RunWayOfficeTaskResp getRunwayVideoJobState(String jobId, String token) {
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/tasks/" + jobId)
                .get()
                .addHeader("authorization", "Bearer " + token)
                .addHeader("X-Runway-Version", "2024-09-13")
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("RUNWAY视频返回Code = " + response.code());
                log.info("RUNWAY视频返回参数 = " + responseBody);
                if (response.isSuccessful()) {
                    if (null != response.body()) {
                        RunWayOfficeTaskResp runWayTaskResp = JSONObject.parseObject(responseBody, RunWayOfficeTaskResp.class);
                        return runWayTaskResp;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY模式获取视频失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY拉取视频失败", "ERRORInfo=" + e.getMessage() + ", jobId= " + jobId);
                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    return null;
                }
            }
        }
        return null;
    }

    // 测试时使用的内容
    public static void main(String[] args) throws NoSuchAlgorithmException, IOException, InterruptedException {
        String token = "key_d5ed9c99e4744c973d379b7fb92ce573eaa3febb1a5090de3f794c7e8bb6104d0bfd20d6a5d48d9b63f1adf6025158a4fcff3f17adf68857ba2df9b75d33744f";
        RunWayOfficeTaskReq req = new RunWayOfficeTaskReq();
        req.setPromptText("大象");

        RunWayOfficeTaskReq.PromptImage promptImage = new RunWayOfficeTaskReq.PromptImage();
        promptImage.setUri("https://image.diandiansheji.com/mj/00004049-b6e7-4243-a3d8-864629515bf1_0_1.webp");
        promptImage.setPosition("first");

        String runWayTaskResp = postRunWayToGenerationVideo(req, token);
        System.out.println(JSON.toJSONString(runWayTaskResp));

        String s = "2a97ce4c-20b6-4a68-98ff-2df263bb393c";
        RunWayOfficeTaskResp runwayVideoJobState = getRunwayVideoJobState(s, token);
        System.out.println(JSON.toJSONString(runwayVideoJobState));


    }


}
