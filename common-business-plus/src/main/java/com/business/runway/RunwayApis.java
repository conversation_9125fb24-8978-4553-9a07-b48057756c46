package com.business.runway;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.business.db.model.dto.runway.RunWayVideoCuttingDTO;
import com.business.enums.BRedisKeyEnum;
import com.business.model.po.ImgDrawRecordPO;
import com.business.runway.model.*;
import com.business.utils.BOssUtil;
import com.business.utils.ImgDrawUtil;
import com.nacos.config.OssClientConfig;
import com.nacos.redis.RedisUtil;
import com.nacos.utils.BFeiShuUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class RunwayApis {

    // runway状态
    public static final String STATUS_FAILED = "FAILED"; // 失败
    public static final String STATUS_THROTTLED = "THROTTLED"; // 限流
    public static final String STATUS_PENDING = "PENDING"; // 待处理
    public static final String STATUS_RUNNING = "RUNNING"; // 进行中
    public static final String STATUS_SUCCEEDED = "SUCCEEDED"; // 成功

    //定时刷新runway的token
    public static void getAndSetToken() {
        String value = RedisUtil.getValue(BRedisKeyEnum.REDIS_RUNWAY_TOKEN_USERNAME_PASS_KEY.getKey());
        RunWayLoginRequest runWayLoginRequest = JSONObject.parseObject(value, RunWayLoginRequest.class);
        RunwayHttpUtil.getToken(runWayLoginRequest);
    }

    //获取账号信息
    public static String getRunwayAccountInfo(String token) {
        return RunwayHttpUtil.getRunwayAccountInfo(token);
    }

    /**
     * 上传图片到runway
     * @param mulFile 图片文件
     * @param token runway token
     * @return 图片url
     * @throws IOException
     */
    public static String uploadImageToRunWay(MultipartFile mulFile, String token) throws IOException {
        String name = mulFile.getOriginalFilename();
        RunWayFileUploadRequest runWayFileUploadRequest = new RunWayFileUploadRequest();
        runWayFileUploadRequest.setFilename(name);

        String returnUrl = null;
        List<String> types = Arrays.asList("DATASET", "DATASET_PREVIEW");
        for (String type : types) {
            runWayFileUploadRequest.setType(type);
            RunWayFileUploadRes runWayFileUploadRes = RunwayHttpUtil.uploadImageToRunWay1(runWayFileUploadRequest, token);
            if (runWayFileUploadRes == null) {
                return null;
            }
            String ext = name.substring(name.lastIndexOf(".") + 1);
            String etag = RunwayHttpUtil.uploadImageToRunWay2(mulFile.getBytes(), ext, runWayFileUploadRes);
            if (StringUtils.isNotBlank(etag)) {
                returnUrl = RunwayHttpUtil.uploadImageToRunWay3(runWayFileUploadRes, etag, token);
            }
        }
        return returnUrl;
    }

    // TODO 视频上传到runway 亚马逊上
    public static String uploadVideoToRunWay(MultipartFile mulFile, String token) throws IOException {
        String name = mulFile.getOriginalFilename();
        RunWayFileUploadRequest runWayFileUploadRequest = new RunWayFileUploadRequest();
        runWayFileUploadRequest.setFilename(name);
        runWayFileUploadRequest.setType("DATASET");
        String videoUrl = uploadVideoToRunWay(name, mulFile, token, runWayFileUploadRequest);
        if (StringUtils.isNotBlank(videoUrl)) {
            return videoUrl;
        }
        return null;
    }

    /**
     * 上传视频到runway
     * @param name 视频文件名
     * @param mulFile 视频文件
     * @param token runway token
     * @param uploadRequest 上传请求
     * @return 视频url
     * @throws IOException
     */
    private static String uploadVideoToRunWay(String name, MultipartFile mulFile, String token, RunWayFileUploadRequest uploadRequest) throws IOException {
        RunWayFileUploadRes runWayFileUploadRes = RunwayHttpUtil.uploadImageToRunWay1(uploadRequest, token);
        if (runWayFileUploadRes == null) {
            return null;
        }
        String ext = name.substring(name.lastIndexOf(".") + 1);
        System.out.println("ext:" + ext);
        String etag = RunwayHttpUtil.uploadImageToRunWay2(mulFile.getBytes(), ext, runWayFileUploadRes);
        if (StringUtils.isNotBlank(etag)) {
            return RunwayHttpUtil.uploadImageToRunWay3(runWayFileUploadRes, etag, token);
        }
        return null;
    }

    // TODO 上传图片到runway 亚马逊
    public static String uploadImageToRunWayByOssUrl(File file, String token) throws IOException {
        String name = file.getName();
        RunWayFileUploadRequest runWayFileUploadRequest = new RunWayFileUploadRequest();
        runWayFileUploadRequest.setFilename(name);

        String returnUrl = null;
        List<String> types = Arrays.asList("DATASET", "DATASET_PREVIEW");
        for (String type : types) {
            runWayFileUploadRequest.setType(type);
            RunWayFileUploadRes runWayFileUploadRes = RunwayHttpUtil.uploadImageToRunWay1(runWayFileUploadRequest, token);
            if (runWayFileUploadRes == null) {
                return null;
            }
            byte[] fileContent = FileUtils.readFileToByteArray(file);
            String ext = name.substring(name.lastIndexOf(".") + 1);
            String etag = RunwayHttpUtil.uploadImageToRunWay2(fileContent, ext, runWayFileUploadRes);
            if (StringUtils.isNotBlank(etag)) {
                returnUrl = RunwayHttpUtil.uploadImageToRunWay3(runWayFileUploadRes, etag, token);
            }
        }
        return returnUrl;
    }

    // TODO 视频裁切-runway 亚马逊上
    public static String runWayVideoCropping(RunWayVideoCuttingDTO runWayVideoCuttingDTO) throws IOException {
        RunWayTaskResp runWayTaskResp = RunwayHttpUtil.runWayVideoCropping(runWayVideoCuttingDTO);
        if (runWayTaskResp != null && StringUtils.isNotBlank(runWayTaskResp.getId())) {
            return runWayTaskResp.getId();
        }
        return null;
    }

    // TODO 获取sessionId-runway
    public static String getRunWaySessionId(Integer asTeamId, String token) throws IOException {
        return RunwayHttpUtil.getRunWaySessionId(asTeamId, token);
    }

    //生成runway视频
    public static String postToGenerateRunwayVideo(RunWayTaskReq runWayTaskReq, String token) {

        RunWayTaskResp runWayTaskResp = RunwayHttpUtil.postRunWayToGenerationVideo(runWayTaskReq, token);
        if (runWayTaskResp != null && StringUtils.isNotBlank(runWayTaskResp.getStatus()) && "429".equals(runWayTaskResp.getStatus())) {
            return runWayTaskResp.getStatus();
        }
        if (runWayTaskResp != null && StringUtils.isNotBlank(runWayTaskResp.getId())) {
            return runWayTaskResp.getId();
        }
        return null;
    }

    //生成runway视频
    public static String postToGenerateRunwayVideo(RunWayVideoTaskReq runWayVideoTaskReq, String token) {
        RunWayTaskResp runWayTaskResp = RunwayHttpUtil.postRunWayToGenerationVideo(runWayVideoTaskReq, token);
        if (runWayTaskResp != null && StringUtils.isNotBlank(runWayTaskResp.getStatus()) && "429".equals(runWayTaskResp.getStatus())) {
            return runWayTaskResp.getStatus();
        }
        if (runWayTaskResp != null && StringUtils.isNotBlank(runWayTaskResp.getId())) {
            return runWayTaskResp.getId();
        }
        return null;
    }

    //TODO 视频转会 == gen3a_turbo
    public static String postRunwayVideoToVideoGenerate(RunWayVideoTransferReq runWayVideoTransferReq, String token) {
        RunWayTaskResp runWayTaskResp = RunwayHttpUtil.postRunwayVideoToVideoGenerate(runWayVideoTransferReq, token);
        if (runWayTaskResp != null && StringUtils.isNotBlank(runWayTaskResp.getStatus()) && "429".equals(runWayTaskResp.getStatus())) {
            return runWayTaskResp.getStatus();
        }
        if (runWayTaskResp != null && StringUtils.isNotBlank(runWayTaskResp.getId())) {
            return runWayTaskResp.getId();
        }
        return null;
    }

    // TODO 生成runway视频角色驱动
    public static String postRunwayVToVGenerateRoleDriven(RunWayVideoRoleTaskReq runWayVideoRoleTaskReq, String token) {
        RunWayTaskResp runWayTaskResp = RunwayHttpUtil.postRunwayVToVGenerateRoleDriven(runWayVideoRoleTaskReq, token);
        if (runWayTaskResp == null) {
            return null;
        }
        if (runWayTaskResp.getStatus().equals("429")) {
            return "429";
        }
        return runWayTaskResp.getId();
    }

    // TODO 获取视频任务结果
    public static String postsToGenerateJobState(String token, ImgDrawRecordPO imgDrawRecordPO) {
        RunWayTaskResp runwayVideoJobState = RunwayHttpUtil.getRunwayVideoJobState(Integer.valueOf(String.valueOf(imgDrawRecordPO.getSunoAccountId())), imgDrawRecordPO.getVideoJobId(), token);
        if (runwayVideoJobState == null) {
            return STATUS_FAILED;
        }
        if (STATUS_FAILED.equals(runwayVideoJobState.getStatus())) {
            if (runwayVideoJobState.getError() != null) {
                RunWayTaskResp.RunWayError runWayError = runwayVideoJobState.getError();
                if (runWayError.getModerationCategory().equals("SEXUALLY_EXPLICIT") || runWayError.getModerationCategory().equals("CHILDREN")) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY拉取任务失败, runway视频检测失败：内容未通过审核" + runWayError.getModerationCategory(), "getRunwayVideoJobState.resp:" + JSON.toJSONString(runwayVideoJobState) + ",taskid= " + runwayVideoJobState.getId());
                    imgDrawRecordPO.setFailReason(runwayVideoJobState.getProgressText());
                } else {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY拉取任务失败：" + JSONObject.toJSONString(runWayError), "getRunwayVideoJobState.resp:" + JSON.toJSONString(runwayVideoJobState) + ",taskid= " + runwayVideoJobState.getId());
                    imgDrawRecordPO.setFailReason(runwayVideoJobState.getProgressText());
                }
            } else {
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY拉取任务业务失败", "getRunwayVideoJobState.resp:" + JSON.toJSONString(runwayVideoJobState) + ",taskid= " + runwayVideoJobState.getId());
                imgDrawRecordPO.setFailReason(runwayVideoJobState.getProgressText());
            }
            return STATUS_FAILED;
        }
        if (STATUS_SUCCEEDED.equals(runwayVideoJobState.getStatus())) {
            imgDrawRecordPO.setWidth(runwayVideoJobState.getArtifacts().getFirst().getMetadata().getSize().getWidth());
            imgDrawRecordPO.setHeight(runwayVideoJobState.getArtifacts().getFirst().getMetadata().getSize().getHeight());
            imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(runwayVideoJobState.getArtifacts().getFirst().getMetadata().getSize().getWidth(), runwayVideoJobState.getArtifacts().getFirst().getMetadata().getSize().getHeight()));
            log.info("runway视频原地址= " + runwayVideoJobState.getArtifacts().getFirst().getUrl());
            return uploadLumaVideo(runwayVideoJobState.getArtifacts().getFirst().getUrl(), imgDrawRecordPO.getVideoJobId(), 17, OssClientConfig.FILE_SUFFIX_VIDEO);
        } else if (STATUS_RUNNING.equals(runwayVideoJobState.getStatus())
                || STATUS_THROTTLED.equals(runwayVideoJobState.getStatus())
                || STATUS_PENDING.equals(runwayVideoJobState.getStatus())) {
            return STATUS_RUNNING;
        }
        return STATUS_RUNNING;
    }


    // TODO 上传视频到oss
    public static String uploadLumaVideo(String fileUrl, String videoJobId, Integer folder, String suffix) {
        if (fileUrl == null) return null;
        int maxRetries = 4; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadURL(fileUrl, videoJobId, folder, suffix);
                if (videoPath != null) {
                    return videoPath;
                }
                log.error("runway视频上传失败，重试次数：" + (retryCount + 1), videoJobId);
            } catch (Exception e) {
                log.error("runway上传图片到OSS发生异常，重试次数：" + (retryCount + 1), e);
            }
            retryCount++;
        }
        log.error("runway模型文件上传失败，超过最大重试次数， 视频id=", videoJobId);
        return null;
    }


    // ============================================================================
    // TODO 上传预设角色图到runway ---- 新接口 2023年6月15日 真他妈乱写的
    public static String uploadPresetRoleImageToRunway(byte[] bytes, String fileName, String token) throws IOException {
        RunWayFileUploadRequest runWayFileUploadRequest = new RunWayFileUploadRequest();
        runWayFileUploadRequest.setFilename(fileName);
        runWayFileUploadRequest.setType("DATASET");
        RunWayFileUploadRes runWayFileUploadRes = RunwayHttpUtil.uploadImageToRunWay1(runWayFileUploadRequest, token);
        if (runWayFileUploadRes == null) {
            return null;
        }
        String ext = fileName.substring(fileName.lastIndexOf(".") + 1);
        String etag = RunwayHttpUtil.uploadImageToRunWay2(bytes, ext, runWayFileUploadRes);

        String returnUrl = null;
        if (StringUtils.isNotBlank(etag)) {
            returnUrl = RunwayHttpUtil.uploadImageToRunWay3(runWayFileUploadRes, etag, token);
        }
        return returnUrl;
    }

}
