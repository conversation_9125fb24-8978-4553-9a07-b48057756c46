package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class RunWayVideoRoleTaskReq {

    @JSONField(name = "taskType")
    private String taskType;

    @JSONField(name = "internal")
    private boolean internal = false;

    @JSONField(name = "asTeamId")
    private String asTeamId;

    @JSONField(name = "sessionId")
    private String sessionId;

    @JSONField(name = "options")
    private OptionsObj options;


    @Data
    public static class OptionsObj {

        @JSONField(name = "seed")
        private Integer seed = 0;

        @JSONField(name = "watermark")
        private boolean watermark = false;

        @JSONField(name = "exploreMode")
        private boolean exploreMode = true;

        @JSONField(name = "motion_multiplier")
        private Integer motionMultiplier;

        @JSONField(name = "seconds")
        private Double seconds = 30D;

        @JSONField(name = "width")
        private Integer width = 1280;
        
        @JSONField(name = "height")
        private Integer height = 768;

        @JSONField(name = "name")
        private String name;

        @JSONField(name = "driving_video")
        private String drivingVideo;

        @JSONField(name = "character_image")
        private String characterimage;

        @JSONField(name = "assetGroupName")
        private String assetGroupName = "Generative Video";

    }
}
