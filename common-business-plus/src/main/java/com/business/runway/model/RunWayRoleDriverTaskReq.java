package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class RunWayRoleDriverTaskReq {

    @J<PERSON>NField(name = "toolId")
    private  String toolId = "generative-video";

    @JSONField(name = "prompt")
    private  String prompt ;

    @JSONField(name = "outputs")
    private  OutputsObj outputs = new OutputsObj();

    @JSONField(name = "settings")
    private  SettingsObj settings;

    @Data
    public static class OutputsObj {

        @JSONField(name = "outputUrls")
        private List<String> outputUrls ;
    }

    @Data
    public static class SettingsObj {

        @J<PERSON>NField(name = "seed")
        private Integer seed = 0;

        @JSONField(name = "watermark")
        private boolean watermark = false;

        @JSONField(name = "exploreMode")
        private boolean exploreMode = true;

        @JSONField(name = "motion_multiplier")
        private Integer motionMultiplier = 3;

        @JSONField(name = "seconds")
        private Double seconds = 30D;

        @J<PERSON><PERSON>ield(name = "height")
        private Integer height = 768;

        @JSONField(name = "width")
        private Integer width = 1280;

        @JSONField(name = "name")
        private String name ;

        @JSONField(name = "driving_video")
        private String drivingVideo ;

        @JSONField(name = "character_image")
        private String vcharacterimage ;

        @JSONField(name = "taskId")
        private String taskId ;

    }

}
