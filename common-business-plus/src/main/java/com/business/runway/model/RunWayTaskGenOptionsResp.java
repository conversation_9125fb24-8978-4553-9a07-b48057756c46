package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class RunWayTaskGenOptionsResp {

    @JSONField(name = "mode")
    private  String mode;

    @JSONField(name = "seed")
    private  String seed;

    @J<PERSON>NField(name = "interpolate")
    private  boolean interpolate;

    @JSONField(name = "upscale")
    private  boolean upscale;

    @JSONField(name = "watermark")
    private  boolean watermark;

    @J<PERSON>NField(name = "motion_score")
    private  Integer motionScore;

    @J<PERSON>NField(name = "motion_vector")
    private  String motionVector;

    @J<PERSON>NField(name = "use_motion_score")
    private  boolean useMotionScore;

    @JSO<PERSON>ield(name = "use_motion_vectors")
    private  boolean useMotionVectors;

    @JSONField(name = "text_prompt")
    private  String textPrompt;

    @<PERSON><PERSON><PERSON>ield(name = "image_prompt")
    private  String imagePrompt;

    @JSONField(name = "init_image")
    private  String initImage;

    @JSONField(name = "driving_video")
    private  Integer drivingVideo;

    @J<PERSON><PERSON>ield(name = "character_image")
    private  Integer characterImage;

}
