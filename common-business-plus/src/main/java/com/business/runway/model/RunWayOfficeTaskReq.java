package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.business.utils.BUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class RunWayOfficeTaskReq {

    // @J<PERSON>NField(name = "promptImage")
    // private String promptImage;

    @JSONField(name = "promptImage")
    private List<PromptImage> promptImages;

    @JSONField(name = "model")
    private String model = "gen3a_turbo";

    @JSONField(name = "seed")
    private Long seed = BUtils.getNineSeed();

    @JSONField(name = "promptText")
    private String promptText;

    @JSONField(name = "watermark")
    private Boolean watermark = false;

    @Schema(description = "视频时长5 10，单位为秒，默认为5秒")
    @JSONField(name = "duration")
    private Integer duration = 5;

    @Schema(description = "默认只有 16:9 和9:16")
    @J<PERSON>NField(name = "ratio")
    private String ratio = "16:9";

    @Data
    public static class PromptImage {
        @J<PERSON><PERSON>ield(name = "uri")
        private String uri;

        @Schema(description = "值：first、last")
        @JSONField(name = "position")
        private String position;

        public PromptImage() {
        }

        public PromptImage(String uri, String position) {
            this.uri = uri;
            this.position = position;
        }

    }

}
