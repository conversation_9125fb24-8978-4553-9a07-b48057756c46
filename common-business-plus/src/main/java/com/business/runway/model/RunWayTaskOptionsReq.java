package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.business.utils.BUtils;
import lombok.Data;

@Data
public class RunWayTaskOptionsReq {

    @JSONField(name = "name")
    private  String name;

    @JSONField(name = "seconds")
    private  Integer seconds = 5;

    @JSONField(name = "exploreMode")
    private  boolean exploreMode = true;

    @JSONField(name = "assetGroupName")
    private  String assetGroupName = "Generative Video";

    //此属性只有类型是gen2使用
    @JSONField(name = "gen2Options")
    private  RunWayTaskOptionsGen2OptionsReq gen2Options;

    //下面的属性是只有类型是gen3a使用
    @JSONField(name = "text_prompt")
    private  String textPrompt;

    @JSONField(name = "seed")
    private  Long seed = BUtils.getSeed();

    @JSONField(name = "watermark")
    private  Boolean watermark = false;

    @JSONField(name = "enhance_prompt")
    private  Boolean enhancePrompt ;

    @JSONField(name = "init_image")
    private  String initImage;

    @J<PERSON><PERSON>ield(name = "resolution")
    private  String resolution = "720p";

    @JSONField(name = "image_as_end_frame")
    private  Boolean imageAsEndFrame = false;

    @JSONField(name = "width")
    private  Integer width ;

    @JSONField(name = "height")
    private  Integer height ;

}
