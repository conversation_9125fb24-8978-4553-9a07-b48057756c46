package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class RunWayOfficeTaskResp {

    @JSONField(name = "id")
    private  String id;

    @JSONField(name = "createdAt")
    private  String createdAt;

    @JSONField(name = "status")
    private  String status;

    @JSONField(name = "failure")
    private  String failure;

    @JSONField(name = "failureCode")
    private  String failureCode;

    @JSONField(name = "output")
    private  List<String> output;

    @JSONField(name = "progress")
    private  Integer progress;

}
