package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

// 视频转绘操作
@Data
public class RunWayVideoTransferOptionsReq {

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "seconds")
    private Integer seconds;

    @JSONField(name = "seed")
    private Long seed;

    @JSONField(name = "exploreMode")
    private Boolean exploreMode;

    @JSONField(name = "watermark")
    private Boolean watermark;

    @JSONField(name = "enhance_prompt")
    private Boolean enhancePrompt;

    @JSONField(name = "video_prompt")
    private String videoPrompt;

    @J<PERSON>NField(name = "text_prompt")
    private String textPrompt;

    @JSONField(name = "width")
    private Integer width;

    @JSO<PERSON>ield(name = "height")
    private Integer height;

    @JSONField(name = "structure_transformation")
    private Float structureTransformation;

    @JSONField(name = "video_prompt_preview_image")
    private String videoPromptPreviewImage;

    @JSONField(name = "flip")
    private Boolean flip;

    @JSONField(name = "assetGroupId")
    private String assetGroupId;


}
