package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class RunWayTaskArtifactsResp {

    @JSONField(name = "id")
    private  String id;

    @JSONField(name = "createdAt")
    private  String createdAt;

    @JSO<PERSON>ield(name = "updatedAt")
    private  String updatedAt;

    @JSONField(name = "userId")
    private  String userId;

    @JSONField(name = "createdBy")
    private  String createdBy;


    @JSONField(name = "taskId")
    private  String taskId;

    @J<PERSON>NField(name = "parentAssetGroupId")
    private String parentAssetGroupId;

    @J<PERSON><PERSON>ield(name = "filename")
    private  String filename;

    @JSO<PERSON>ield(name = "url")
    private  String url;

    @JSO<PERSON>ield(name = "fileSize")
    private  String fileSize;

    @JSONField(name = "isDirectory")
    private  boolean isDirectory;

    @JSONField(name = "previewUrls")
    private  List<String> previewUrls;

    @J<PERSON>NField(name = "private")
    private  boolean privater;

    @J<PERSON><PERSON>ield(name = "privateInTeam")
    private  boolean privateInTeam;

    @J<PERSON><PERSON>ield(name = "deleted")
    private  boolean deleted;

    @JSO<PERSON>ield(name = "reported")
    private  boolean reported;

    @JSONField(name = "favorite")
    private  boolean favorite;

    @JSONField(name = "metadata")
    private  RunWayTaskArtifactsmetadataMetadataResp metadata;


}
