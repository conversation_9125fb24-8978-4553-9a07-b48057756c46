package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.business.utils.BUtils;
import lombok.Data;

@Data
public class    RunWayVideoTaskOptionsReq {

    @JSONField(name = "name")
    private  String name;

    @J<PERSON>NField(name = "seconds")
    private  Integer seconds = 10;

    @J<PERSON><PERSON>ield(name = "exploreMode")
    private  boolean exploreMode = true;

    @JSONField(name = "assetGroupName")
    private  String assetGroupName = "Generative Video";

    //下面的属性是只有类型是gen3a使用
    @JSONField(name = "text_prompt")
    private  String textPrompt;

    @JSONField(name = "seed")
    private  Long seed = BUtils.getSeed();

    @JSONField(name = "watermark")
    private  Boolean watermark = false;

    @JSONField(name = "enhance_prompt")
    private  Boolean enhancePrompt = true;

    @JSONField(name = "video_prompt")
    private  String videoPrompt;

    @JSONField(name = "video_prompt_preview_image")
    private  String videoPromptPreviewImage ;

    @J<PERSON>NField(name = "width")
    private  Integer width =1280 ;

    @J<PERSON>NField(name = "height")
    private  Integer height =768 ;

    @JSONField(name = "structure_transformation")
    private  Double structureTransformation =0.3 ;

}
