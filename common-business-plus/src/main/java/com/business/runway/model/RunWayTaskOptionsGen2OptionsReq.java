package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.business.utils.BUtils;
import lombok.Data;

@Data
public class RunWayTaskOptionsGen2OptionsReq {

    @J<PERSON><PERSON>ield(name = "mode")
    private  String mode;

    @J<PERSON>NField(name = "seed")
    private  Long seed = BUtils.getSeed();;

    @JSONField(name = "interpolate")
    private  boolean interpolate = true;

    @JSONField(name = "upscale")
    private  boolean upscale = true;

    @JSONField(name = "watermark")
    private  boolean watermark = false;

    @JSONField(name = "motion_score")
    private  Integer motionScore ;

    @J<PERSON>NField(name = "motion_vector")
    private  String motionVector;

    @JSONField(name = "use_motion_score")
    private  boolean useMotionScore = false;

    @JSONField(name = "use_motion_vectors")
    private  boolean useMotionVectors = false;

    @JSONField(name = "text_prompt")
    private  String textPrompt;

    @JSONField(name = "image_prompt")
    private  String imagePrompt;

    @J<PERSON><PERSON>ield(name = "init_image")
    private  String initImage;

    @J<PERSON><PERSON>ield(name = "style")
    private  String style = "none";

    @J<PERSON><PERSON>ield(name = "width")
    private  Integer width ;

    @JSONField(name = "height")
    private  Integer height ;

}
