package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class RunWayTaskResp {

    @JSONField(name = "id")
    private String id;

    @J<PERSON><PERSON>ield(name = "name")
    private String name;

    @J<PERSON><PERSON>ield(name = "image")
    private String image;

    @J<PERSON><PERSON>ield(name = "createdAt")
    private String createdAt;

    @JSO<PERSON>ield(name = "updatedAt")
    private String updatedAt;

    @JSONField(name = "taskType")
    private String taskType;

    @JSONField(name = "options")
    private RunWayTaskOptionsResp options;

    @JSONField(name = "status")
    private String status;

    @J<PERSON><PERSON>ield(name = "error")
    private RunWayError error;

    @J<PERSON><PERSON>ield(name = "progressText")
    private String progressText;

    @JSONField(name = "progressRatio")
    private String progressRatio;

    @J<PERSON><PERSON>ield(name = "estimatedTimeToStartSeconds")
    private String estimatedTimeToStartSeconds;

    @JSONField(name = "artifacts")
    private List<RunWayTaskArtifactsResp> artifacts;

    @JSONField(name = "sharedAsset")
    private String sharedAsset;

    @Data
    public static class RunWayError {

        @J<PERSON><PERSON>ield(name = "reason")
        private String reason;

        @JSONField(name = "errorMessage")
        private String errorMessage;

        @JSONField(name = "moderation_category")
        private String moderationCategory;

        @JSONField(name = "tally_asimov")
        private String tallyAsimov;

        @JSONField(name = "message")
        private String message;
    }

}
