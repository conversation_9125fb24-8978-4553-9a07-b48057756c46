package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class RunWayTaskOptionsResp {

    @JSONField(name = "name")
    private  String name;

    @JSONField(name = "seconds")
    private  String seconds;

    @J<PERSON><PERSON>ield(name = "exploreMode")
    private  boolean exploreMode;

    @JSONField(name = "assetGroupName")
    private  String assetGroupName;

    @JSONField(name = "recordingEnabled")
    private  String recordingEnabled;

    @JSONField(name = "gen2Options")
    private RunWayTaskGenOptionsResp gen2Options;

    @JSONField(name = "options")
    private RunWayTaskGenOptionsResp options;

    //下面的属性是只有类型是gen3a使用
    @JSONField(name = "text_prompt")
    private  String textPrompt;

    @JSONField(name = "seed")
    private  String seed;

    @JSONField(name = "watermark")
    private  boolean watermark;

    @<PERSON><PERSON>NField(name = "enhance_prompt")
    private  boolean enhancePrompt;

    @J<PERSON>NField(name = "init_image")
    private  String initImage;

    @J<PERSON>NField(name = "resolution")
    private  String resolution;

    @JSONField(name = "image_as_end_frame")
    private  boolean imageAsEndFrame;

    @JSONField(name = "width")
    private  Integer width;

    @JSONField(name = "height")
    private  Integer height;


}
