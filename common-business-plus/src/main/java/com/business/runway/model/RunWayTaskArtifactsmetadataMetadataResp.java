package com.business.runway.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class RunWayTaskArtifactsmetadataMetadataResp {

    @JSONField(name = "frameRate")
    private  Integer frameRate;

    @JSONField(name = "duration")
    private  Integer duration;

    @JSONField(name = "dimensions")
    private  List<Integer> dimensions;

    @JSONField(name = "size")
    private  RunWayTaskArtifactsmetadataMetadataSizeResp size;

}
