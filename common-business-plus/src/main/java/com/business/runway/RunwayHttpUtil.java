package com.business.runway;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.business.db.model.dto.runway.RunWayVideoCuttingDTO;
import com.business.enums.BRedisKeyEnum;
import com.business.runway.model.*;
import com.business.utils.BDateUtil;
import com.business.utils.BUrlUtil;
import com.business.utils.BUtils;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.nacos.enums.VideoTaskTypeEnum;
import com.nacos.redis.RedisUtil;
import com.nacos.tool.BrotliInterceptor;
import com.nacos.utils.BFeiShuUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

//Luma工具类
@Slf4j
public class RunwayHttpUtil {

    //翻墙用的url
    private static final String RUNWAY_API_URL = "https://runway.iworks.cn";
//    private static final String RUNWAY_API_URL = "https://api.runwayml.com";


    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();


    //定时刷新runway token
    public static void getToken(RunWayLoginRequest req) {
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/login")
                .post(RequestBody.create(JSONObject.toJSONString(req), MediaType.parse("application/json")))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                String token1 = jsonObject.getString("token");
                if (StringUtils.isNotBlank(token1)) {
                    boolean b = RedisUtil.setValue(BRedisKeyEnum.REDIS_RUNWAY_TOKEN_KEY.getKey(), token1);
                    log.info("b:{}", b);
                }
            } else {
                BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY自动获取token失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()) + " param=" + JSONObject.toJSONString(req));
            }
        } catch (Exception e) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY自动获取token失败", "ERRORInfo=" + e.getMessage());
            log.error("RUNWAY获取token失败：{}", e.getMessage());
        }
    }


    //上传图片
    public static RunWayFileUploadRes uploadImageToRunWay1(RunWayFileUploadRequest runWayFileUploadRequest, String token) {

        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/uploads")
                .post(RequestBody.create(JSONObject.toJSONString(runWayFileUploadRequest), MediaType.parse("application/json")))
                .addHeader("accept", "application/json, text/plain, */*")
                .addHeader("content-type", "application/json")
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Length", "2")
                .addHeader("Origin", "https://app.runwayml.com")
                .addHeader("Referer", "https://app.runwayml.com/")
                .addHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String responseBody = response.body().string();
                    RunWayFileUploadRes jsonObject = JSONObject.parseObject(responseBody, RunWayFileUploadRes.class);
                    if (jsonObject.getUploadUrls().get(0).startsWith("https://runway-datasets.s3.us-east-1.amazonaws.com")) {
                        jsonObject.getUploadUrls().set(0, jsonObject.getUploadUrls().get(0).replace("https://runway-datasets.s3.us-east-1.amazonaws.com", "https://runwaydatasets.iworks.cn"));
                    }
                    // log.info("上传图片 responseBody:{}", responseBody);
                    return jsonObject;
                } else {
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY上传文件失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()));
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY上传文件失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY上传文件失败", "ERRORInfo=" + e.getMessage());
                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                    return null;
                }
            }
        }
        return null;
    }


    /**
     * 视频裁切
     * @param runWayVideoCuttingDTO 裁切请求
     * @return 裁切结果
     * @throws UnsupportedEncodingException
     */
    public static RunWayTaskResp runWayVideoCropping(RunWayVideoCuttingDTO runWayVideoCuttingDTO) throws UnsupportedEncodingException {
        JSONObject inputs = new JSONObject();
        inputs.put("video", runWayVideoCuttingDTO.getVideoUrl());
        inputs.put("width", runWayVideoCuttingDTO.getWidth());
        inputs.put("height", runWayVideoCuttingDTO.getHeight());
        inputs.put("offset_x", runWayVideoCuttingDTO.getOffsetX());
        inputs.put("offset_y", runWayVideoCuttingDTO.getOffsetY());
        inputs.put("crop_width", runWayVideoCuttingDTO.getCropWidth());
        inputs.put("crop_height", runWayVideoCuttingDTO.getCropHeight());

        JSONObject videoStreamDescription = new JSONObject();
        videoStreamDescription.put("model_name", "crop_video");
        videoStreamDescription.put("command_name", "crop_video");
        videoStreamDescription.put("inputs", inputs);

        int width = (runWayVideoCuttingDTO.getCropWidth() > runWayVideoCuttingDTO.getCropHeight()) ? 1280 : 768;
        int height = (runWayVideoCuttingDTO.getCropWidth() > runWayVideoCuttingDTO.getCropHeight()) ? 768 : 1280;

        videoStreamDescription.put("output_width", width);
        videoStreamDescription.put("output_height", height);

        JSONObject audioInputs = new JSONObject();
        audioInputs.put("audio", runWayVideoCuttingDTO.getVideoUrl());

        JSONObject audioStreamDescription = new JSONObject();
        audioStreamDescription.put("model_name", "raw_audio");
        audioStreamDescription.put("command_name", "raw_audio");
        audioStreamDescription.put("inputs", audioInputs);

        JSONObject options = new JSONObject();
        options.put("name", "Cropped - runway-" + IdWorker.getId());
        options.put("ignoreAudioStreamFailure", true);
        options.put("videoStreamDescription", videoStreamDescription);
        options.put("audioStreamDescription", audioStreamDescription);
        options.put("videoStart", 0);
        options.put("videoEnd", 20);
        options.put("audioStart", 0);
        options.put("audioEnd", 20);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("taskType", "bake_stream");
        jsonObject.put("internal", false);
        jsonObject.put("options", options);
        jsonObject.put("asTeamId", runWayVideoCuttingDTO.getAsTeamId());

        log.info("runWayVideoCropping请求参数：" + jsonObject.toJSONString());
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/tasks")
                .post(RequestBody.create(jsonObject.toString(), MediaType.parse("application/json")))
                .addHeader("accept", "application/json")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")
                .addHeader("Authorization", "Bearer " + runWayVideoCuttingDTO.getToken())
                .addHeader("Content-Length", "1126")
                .addHeader("Content-Type", "application/json")
                .addHeader("Origin", "https://app.runwayml.com")
                .addHeader("Referer", "https://app.runwayml.com/")
                .addHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
                .build();
        try (Response response = client.newCall(request).execute()) {
            System.out.println("视频裁切 response= " + response.code());
            System.out.println("视频裁切 response:{}" + response);
            if (response.isSuccessful()) {
                if (response.body() == null) {
                    return null;
                }
                String responseBody = response.body().string();
                JSONObject responseObject = JSONObject.parseObject(responseBody);
                String taskObject = responseObject.getString("task");
                return JSONObject.parseObject(taskObject, RunWayTaskResp.class);
            }
        } catch (Exception e) {
            log.error("视频裁切 runWayVideoCropping:\nrequestStr, {}", e.getMessage());
            return null;
        }
        return null;
         /* String json = "{\n" +
                "    \"taskType\": \"bake_stream\",\n" +
                "    \"internal\": false,\n" +
                "    \"options\": {\n" +
                "        \"name\": \"Cropped - VID-20210528-WA0001\",\n" +
                "        \"ignoreAudioStreamFailure\": true,\n" +
                "        \"videoStreamDescription\": {\n" +
                "            \"model_name\": \"crop_video\",\n" +
                "            \"command_name\": \"crop_video\",\n" +
                "            \"inputs\": {\n" +
                "                \"video\": \"https://d2jqrm6oza8nb6.cloudfront.net/datasets/94fbb4a9-ac6b-4cc9-af2a-0d8ceca6dcba.mp4?_jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXlIYXNoIjoiZWZkNDc5MjQ0NGM0MGI5YiIsImJ1Y2tldCI6InJ1bndheS1kYXRhc2V0cyIsInN0YWdlIjoicHJvZCIsImV4cCI6MTczMzUyOTYwMH0.4AkugZtrNYlmbwRHUkgVe6UVtahhzwb0LzeldnPUaro\",\n" +
                "                \"width\": 1280,\n" +
                "                \"height\": 768,\n" +
                "                \"offset_x\": 0,\n" +
                "                \"offset_y\": 24,\n" +
                "                \"crop_width\": 1280,\n" +
                "                \"crop_height\": 720\n" +
                "            },\n" +
                "            \"output_width\": 1280,\n" +
                "            \"output_height\": 768\n" +
                "        },\n" +
                "        \"audioStreamDescription\": {\n" +
                "            \"model_name\": \"raw_audio\",\n" +
                "            \"command_name\": \"raw_audio\",\n" +
                "            \"inputs\": {\n" +
                "                \"audio\": \"https://d2jqrm6oza8nb6.cloudfront.net/datasets/94fbb4a9-ac6b-4cc9-af2a-0d8ceca6dcba.mp4?_jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXlIYXNoIjoiZWZkNDc5MjQ0NGM0MGI5YiIsImJ1Y2tldCI6InJ1bndheS1kYXRhc2V0cyIsInN0YWdlIjoicHJvZCIsImV4cCI6MTczMzUyOTYwMH0.4AkugZtrNYlmbwRHUkgVe6UVtahhzwb0LzeldnPUaro\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"videoStart\": 0,\n" +
                "        \"videoEnd\": 20,\n" +
                "        \"audioStart\": 0,\n" +
                "        \"audioEnd\": 20\n" +
                "    },\n" +
                "    \"asTeamId\": 22778554\n" +
                "}";*/
    }

    /**
     * 上传图片到runway
     * @param data 图片文件字节数组
     * @param ext 图片文件扩展名
     * @param runWayFileUploadRes 上传请求
     * @return 图片url
     * @throws IOException
     */
    public static String uploadImageToRunWay2(byte[] data, String ext, RunWayFileUploadRes runWayFileUploadRes) throws IOException {
        List<String> uploadUrls = runWayFileUploadRes.getUploadUrls();
        if (uploadUrls == null || uploadUrls.size() < 1 || StringUtils.isBlank(uploadUrls.get(0))) {
            return null;
        }
        // 要上传的图片文件路径
        String uploadHeaders = runWayFileUploadRes.getUploadHeaders();
        JSONObject jsonObject = JSONObject.parseObject(uploadHeaders);
        String type = jsonObject.getString("Content-Type");

        RequestBody body = RequestBody.create(data, MediaType.parse(type));

        try {
            Request request = new Request.Builder()
                    .url(uploadUrls.get(0))
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("accept", "*/*")
                    .addHeader("Content-Type", type)
                    .addHeader("Origin", "https://app.runwayml.com")
                    .addHeader("Referer", "https://app.runwayml.com/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .put(body)
                    .build();
            log.info("本地图片上传runway uploadImageToRunWay2 request:{}", request);
            for (int attempt = 1; attempt <= 3; attempt++) {
                try (Response response = client.newCall(request).execute()) {
                    log.info("本地图片上传runway uploadImageToRunWay2 response:{}", response);
                    if (response.body() != null && (response.code() == 200 || response.code() == 204)) {
                        String responseBody = response.body().string();
                        Headers headers = response.headers();
                        String eTag = headers.get("ETag");
                        log.info("responseBody:{}", responseBody);
                        log.info("responseBody eTag:{}", eTag);
                        return eTag;
                    } else {
                        if (attempt == 3) {
                            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY上传文件2失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()));
                            return null;
                        }
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt(); // Restore interrupted status
                            log.info("Thread was interrupted, Failed to complete operation");
                            return null;
                        }
                    }
                } catch (Exception e) {
                    log.error("RUNWAY上传文件2失败：{}", e.getMessage());
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY上传文件2失败", "ERRORInfo=" + e.getMessage());
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error during upload: {}", e.getMessage(), e);
        }
        return null;
    }


    public static String uploadImageToRunWay3(RunWayFileUploadRes runWayFileUploadRes, String etag, String token) throws IOException {
        String id = runWayFileUploadRes.getId();
        if (StringUtils.isBlank(id)) {
            return null;
        }
        UploadCompleteRequest completeReq = new UploadCompleteRequest(Collections.singletonList(new Part(1, etag)));

        try {
            Request request = new Request.Builder()
                    .url(RUNWAY_API_URL + "/v1/uploads/" + runWayFileUploadRes.getId() + "/complete")
                    .addHeader("Accept", "application/json")
                    .addHeader("authorization", "Bearer " + token)
                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Content-Length", "1407938")
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Origin", "https://app.runwayml.com")
                    .addHeader("Referer", "https://app.runwayml.com/")
                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
                    .addHeader("Sec-Fetch-Dest", "empty")
                    .addHeader("Sec-Fetch-Mode", "cors")
                    .addHeader("Sec-Fetch-Site", "same-site")
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .post(RequestBody.create(JSONObject.toJSONString(completeReq), MediaType.parse("application/json")))
                    .build();

            for (int attempt = 1; attempt <= 3; attempt++) {
                try (Response response = client.newCall(request).execute()) {
                    log.info("本地图片上传runway uploadImageToRunWay3 response:{}", response);
                    if (response.body() != null && (response.code() == 200)) {
                        String responseBody = response.body().string();
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        log.info("responseBody:{}", responseBody);
                        return jsonObject.getString("url");
                    } else {
                        if (attempt == 3) {
                            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY上传文件3失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()));
                            return null;
                        }
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt(); // Restore interrupted status
                            log.info("Thread was interrupted, Failed to complete operation");
                            return null;
                        }
                    }
                } catch (Exception e) {
                    log.error("RUNWAY上传文件3失败：{}", e.getMessage());
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY上传文件3失败", "ERRORInfo=" + e.getMessage());
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            }
        } catch (Exception e) {

            log.error("Error during upload: {}", e.getMessage(), e);
        }
        return null;
    }

    //获取账号信息
    public static String getRunwayAccountInfo(String token) {
        try {
            Request request = new Request.Builder()
                    .url(RUNWAY_API_URL + "/v1/profile")
                    .addHeader("authorization", "Bearer " + token)
//                    .addHeader("Accept", "*/*")
//                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
//                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
//                    .addHeader("Content-Length", "1407938")
//                    .addHeader("Content-Type", "application/json")
//                    .addHeader("Origin","https://app.runwayml.com")
//                    .addHeader("Referer", "https://app.runwayml.com/")
//                    .addHeader("Sec-Ch-Ua",  "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"")
//                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
//                    .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
//                    .addHeader("Sec-Fetch-Dest", "empty")
//                    .addHeader("Sec-Fetch-Mode", "cors")
//                    .addHeader("Sec-Fetch-Site", "same-site")
//                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .get()
                    .build();

            for (int attempt = 1; attempt <= 3; attempt++) {
                try (Response response = client.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        String responseBody = response.body().string();
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        JSONObject user = jsonObject.getJSONObject("user");
                        log.info("获取runway账号信息 code:{}", response.code());
                        log.info("获取runway账号信息 responseBody:{}", responseBody);
                        return user.getString("id");
                    } else {
                        if (attempt == 3) {
                            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY获取账号信息失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()));
                            return null;
                        }
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt(); // Restore interrupted status
                            log.info("Thread was interrupted, Failed to complete operation");
                            return null;
                        }
                    }
                } catch (Exception e) {
                    log.error("RUNWAY视频提交失败：{}", e.getMessage());
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY获取账号信息失败", "ERRORInfo=" + e.getMessage());
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error during upload: {}", e.getMessage());
        }
        return null;
    }

    //任务是否可以提交
    public static boolean runwayTaskCanStart(String runwayId, String token) {
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/tasks/can_start?mode=credits&asTeamId=" + runwayId)
//                    .addHeader("Accept", "application/json")
                .addHeader("authorization", "Bearer " + token)
//                    .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
//                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
//                    .addHeader("Content-Length", "1407938")
//                    .addHeader("Content-Type", "application/json")
//                    .addHeader("Origin","https://app.runwayml.com")
//                    .addHeader("Referer", "https://app.runwayml.com/")
//                    .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
//                    .addHeader("Sec-Ch-Ua-Mobile", "?0")
//                    .addHeader("Sec-Ch-Ua-Platform", "\"macOS\"")
//                    .addHeader("Sec-Fetch-Dest", "empty")
//                    .addHeader("Sec-Fetch-Mode", "cors")
//                    .addHeader("Sec-Fetch-Site", "same-site")
//                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .get()
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                log.info("任务是否可以提交 runwayTaskCanStart response:{}", response);
                if (response.body() != null && (response.code() == 200)) {
                    String responseBody = response.body().string();
                    if (StringUtils.isBlank(responseBody)) {
                        return false;
                    }
                    JSONObject jsonObject = JSONObject.parseObject(responseBody);
                    JSONObject canStartNewTask = jsonObject.getJSONObject("canStartNewTask");
                    int currentInProgressTasks = canStartNewTask.getIntValue("currentInProgressTasks");
                    Boolean canStartTask = currentInProgressTasks < 2 ? true : false;
                    log.info("responseBody:{}", responseBody);
                    return canStartTask;
                } else {
                    if (attempt == 3) {
                        return false;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return false;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY视频提交失败：{}", e.getMessage());
                if (attempt == 3) {
                    return false;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 提交runway视频生成任务
     * @param runWayTaskReq 任务请求
     * @param token 令牌
     * @return 任务响应
     */
    public static RunWayTaskResp postRunWayToGenerationVideo(RunWayTaskReq runWayTaskReq, String token) {
        log.info("runway postRunWayToGenerationVideo RequestBody={}", JSONObject.toJSONString(runWayTaskReq));
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/tasks")
                .post(RequestBody.create(JSONObject.toJSONString(runWayTaskReq), MediaType.parse("application/json")))
                .addHeader("authorization", "Bearer " + token)
                .addHeader("Accept", "application/json")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Length", "1407938")
                .addHeader("Content-Type", "application/json")
                .addHeader("Origin", "https://app.runwayml.com")
                .addHeader("Referer", "https://app.runwayml.com/")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("RUNWAY视频提交Code：" + response.code() + "。 请求结果为：" + responseBody);
                if (response.code() == 200) {
                    if (null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String task = jsonObject.getString("task");
                        RunWayTaskResp runWayTaskResp = JSONObject.parseObject(task, RunWayTaskResp.class);
                        return runWayTaskResp;
                    }
                } else {
                    //并发任务满了，所以需要排队处理
                    if (response.code() == 429) {
                        RunWayTaskResp runWayTaskResp = new RunWayTaskResp();
                        runWayTaskResp.setStatus("429");
                        return runWayTaskResp;
                    }
                    if (response.code() == 400 && null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String error = jsonObject.getString("error");
                        //飞书通知点子没有了
                        String key = RedisUtil.REDIS_RUNWAY_ACCOUNT_PREFIX + runWayTaskReq.getAsTeamId() + "-" + BDateUtil.getYearAndMonth();
                        boolean b = RedisUtil.acquireLockByDay(key, 31);
                        if (b) {
                            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "达芬奇Credits已经耗尽", "postRunWayToGenerationVideo.responseStr" + error + " runway account= " + runWayTaskReq.getAsTeamId());
                        }
                        if (error.equals("You do not have enough credits to run this task.")) {
                            //改为无限模式并且重新提交请求
                            if (VideoTaskTypeEnum.TASK_TYPE_GEN2.getValue().equals(runWayTaskReq.getTaskType())) {
                                runWayTaskReq.getOptions().setAssetGroupName("Generative Video");
                                runWayTaskReq.getOptions().setExploreMode(true);
                                runWayTaskReq.getOptions().getGen2Options().setSeed(BUtils.getSeed());
                            }
                            if (VideoTaskTypeEnum.TASK_TYPE_GEN3AT.getValue().equals(runWayTaskReq.getTaskType())) {
                                runWayTaskReq.getOptions().setAssetGroupName("Generative Video");
                                runWayTaskReq.getOptions().setExploreMode(true);
                                runWayTaskReq.getOptions().setSeed(BUtils.getSeed());
                            }
                            return postRunWayToGenerationVideo(runWayTaskReq, token);
                        }
                        if (error.equals("The current plan cannot use explore mode.")) {
                            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY需要充值", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()));
                            return null;
                        }
                    }
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()) + ", param= " + JSONObject.toJSONString(runWayTaskReq));
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY视频提交失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + e.getMessage() + ", param= " + JSONObject.toJSONString(runWayTaskReq));

                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                    return null;
                }
            }
        }
        return null;
    }


    public static RunWayTaskResp postRunWayToGenerationVideo(RunWayVideoTaskReq runWayVideoTaskReq, String token) {
        log.info("runway postRunWayToGenerationVideo RequestBody={}", JSONObject.toJSONString(runWayVideoTaskReq));
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/tasks")
                .post(RequestBody.create(JSONObject.toJSONString(runWayVideoTaskReq), MediaType.parse("application/json")))
                .addHeader("authorization", "Bearer " + token)
                .addHeader("Accept", "application/json")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Length", "1407938")
                .addHeader("Content-Type", "application/json")
                .addHeader("Origin", "https://app.runwayml.com")
                .addHeader("Referer", "https://app.runwayml.com/")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("RUNWAY视频提交Code：" + response.code() + "。 请求结果为：" + responseBody);
                if (response.code() == 200) {
                    if (null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String task = jsonObject.getString("task");
                        RunWayTaskResp runWayTaskResp = JSONObject.parseObject(task, RunWayTaskResp.class);
                        return runWayTaskResp;
                    }
                } else {
                    //并发任务满了，所以需要排队处理
                    if (response.code() == 429) {
                        RunWayTaskResp runWayTaskResp = new RunWayTaskResp();
                        runWayTaskResp.setStatus("429");
                        return runWayTaskResp;
                    }
                    if (response.code() == 400 && null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String error = jsonObject.getString("error");
                        if (error.equals("The current plan cannot use explore mode.")) {
                            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY需要充值", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()));
                            return null;
                        }
                    }
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()) + ", param= " + JSONObject.toJSONString(runWayVideoTaskReq));
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY视频提交失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + e.getMessage() + ", param= " + JSONObject.toJSONString(runWayVideoTaskReq));

                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                    return null;
                }
            }
        }
        return null;
    }

    // 视频到视频 === Gen-1 2024-07-08_19-08-18
    public static RunWayTaskResp postRunwayVideoToVideoGenerate(RunWayVideoTransferReq runWayVideoTransferReq, String token) {
        log.info("视频转会postRunwayVideoToVideoGenerate={}", JSONObject.toJSONString(runWayVideoTransferReq));
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/tasks")
                .post(RequestBody.create(JSONObject.toJSONString(runWayVideoTransferReq), MediaType.parse("application/json")))
                .addHeader("authorization", "Bearer " + token)
                .addHeader("Accept", "application/json")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Length", "1407938")
                .addHeader("Content-Type", "application/json")
                .addHeader("Origin", "https://app.runwayml.com")
                .addHeader("Referer", "https://app.runwayml.com/")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("RUNWAY视频提交Code：" + response.code() + "。 请求结果为：" + responseBody);
                if (response.code() == 200) {
                    if (null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String task = jsonObject.getString("task");
                        RunWayTaskResp runWayTaskResp = JSONObject.parseObject(task, RunWayTaskResp.class);
                        return runWayTaskResp;
                    }
                } else {
                    //并发任务满了，所以需要排队处理
                    if (response.code() == 429) {
                        RunWayTaskResp runWayTaskResp = new RunWayTaskResp();
                        runWayTaskResp.setStatus("429");
                        return runWayTaskResp;
                    }
                    if (response.code() == 400 && null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String error = jsonObject.getString("error");
                        log.info("RUNWAY视频提交失败error：" + error);
                        if (error.equals("The current plan cannot use explore mode.")) {
                            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY需要充值", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()));
                            return null;
                        }
                    }
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()) + ", param= " + JSONObject.toJSONString(runWayVideoTransferReq));
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY视频提交失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + e.getMessage() + ", param= " + JSONObject.toJSONString(runWayVideoTransferReq));

                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                    return null;
                }
            }
        }
        return null;
    }

    // 视频到视频 === gen3a_turbo
    public static RunWayTaskResp postRunwayVideoToVideoGenerate3(RunWayVideoTransferReq runWayVideoTransferReq, String token) {
        log.info("视频转会postRunwayVideoToVideoGenerate={}", JSONObject.toJSONString(runWayVideoTransferReq));
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/tasks")
                .post(RequestBody.create(JSONObject.toJSONString(runWayVideoTransferReq), MediaType.parse("application/json")))
                .addHeader("authorization", "Bearer " + token)
                .addHeader("Accept", "application/json")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Length", "1407938")
                .addHeader("Content-Type", "application/json")
                .addHeader("Origin", "https://app.runwayml.com")
                .addHeader("Referer", "https://app.runwayml.com/")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("RUNWAY视频提交Code：" + response.code() + "。 请求结果为：" + responseBody);
                if (response.code() == 200) {
                    if (null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String task = jsonObject.getString("task");
                        RunWayTaskResp runWayTaskResp = JSONObject.parseObject(task, RunWayTaskResp.class);
                        return runWayTaskResp;
                    }
                } else {
                    //并发任务满了，所以需要排队处理
                    if (response.code() == 429) {
                        RunWayTaskResp runWayTaskResp = new RunWayTaskResp();
                        runWayTaskResp.setStatus("429");
                        return runWayTaskResp;
                    }
                    if (response.code() == 400 && null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String error = jsonObject.getString("error");
                        if (error.equals("The current plan cannot use explore mode.")) {
                            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY需要充值", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()));
                            return null;
                        }
                    }
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()) + ", param= " + JSONObject.toJSONString(runWayVideoTransferReq));
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY视频提交失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + e.getMessage() + ", param= " + JSONObject.toJSONString(runWayVideoTransferReq));

                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                    return null;
                }
            }
        }
        return null;
    }

    public static RunWayTaskResp postRunwayVToVGenerateRoleDriven(RunWayVideoRoleTaskReq runWayTaskReq, String token) {
        log.info("角色驱动postRunWayToGenerationVideo，RequestBody={}", JSONObject.toJSONString(runWayTaskReq));
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/tasks")
                .post(RequestBody.create(JSONObject.toJSONString(runWayTaskReq), MediaType.parse("application/json")))
                .addHeader("authorization", "Bearer " + token)
                .addHeader("Accept", "application/json")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Length", "1407938")
                .addHeader("Content-Type", "application/json")
                .addHeader("Origin", "https://app.runwayml.com")
                .addHeader("Referer", "https://app.runwayml.com/")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("RUNWAY视频提交Code：" + response.code() + "。 请求结果为：" + responseBody);
                if (response.code() == 200) {
                    if (null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String task = jsonObject.getString("task");
                        RunWayTaskResp runWayTaskResp = JSONObject.parseObject(task, RunWayTaskResp.class);
                        return runWayTaskResp;
                    }
                } else {
                    //并发任务满了，所以需要排队处理
                    if (response.code() == 429) {
                        RunWayTaskResp runWayTaskResp = new RunWayTaskResp();
                        runWayTaskResp.setStatus("429");
                        return runWayTaskResp;
                    }
                    if (response.code() == 400 && null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String error = jsonObject.getString("error");
                        if (error.equals("The current plan cannot use explore mode.")) {
                            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY需要充值", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()));
                            return null;
                        }
                    }
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()) + ", param= " + JSONObject.toJSONString(runWayTaskReq));
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY视频提交失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + e.getMessage() + ", param= " + JSONObject.toJSONString(runWayTaskReq));

                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                    return null;
                }
            }
        }
        return null;
    }

    public static JsonObject verifyImageRunWay(String inputVideo, String asTeamId, String token) {
        Request request = new Request.Builder()
                .url("https://streaming-inference.models.runwayml.cloud/streams-server-queue/face_detection_v2/commands/check_faces_v2/result.json?input_video="
                        + inputVideo
                        + "&as_team_id=" + asTeamId
                        + "&hardware=gpu"
                        + "&priority=high"
                        + "&tok=" + token)
                .get()
                .addHeader("Accept", "*/*")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Origin", "https://app.runwayml.com")
                .addHeader("Priority", "u=1, i")
                .addHeader("Referer", "https://app.runwayml.com/")
                .addHeader("Sec-Ch-Ua", "\"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "cross-site")
                .addHeader("Sentry-Trace", "99af00baeae3475cb38acfc9ad08f336-b641980be43694a1-0")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.body() == null) {
                return null;
            }
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                log.info("verifyImageRunWay responseBody, {}", responseBody);
                return JsonParser.parseString(responseBody).getAsJsonObject();
            }
        } catch (Exception e) {
            log.info("verifyImageRunWay erray=" + e.getMessage());
            return null;
        }
        return null;
    }

    public static String postRunWayToGenerationVRoleDriven(RunWayVideoRoleTaskReq runWayTaskReq, String token) {
        log.info("runway postRunWayToGenerationVRoleDriven RequestBody={}", JSONObject.toJSONString(runWayTaskReq));
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/generations")
                .post(RequestBody.create(JSONObject.toJSONString(runWayTaskReq), MediaType.parse("application/json")))
                .addHeader("authorization", "Bearer " + token)
                .addHeader("Accept", "application/json")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Content-Length", "1407938")
                .addHeader("Content-Type", "application/json")
                .addHeader("Origin", "https://app.runwayml.com")
                .addHeader("Referer", "https://app.runwayml.com/")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("RUNWAY视频提交Code：" + response.code() + "。 请求结果为：" + responseBody);
                if (response.code() == 200) {
                    if (null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        return jsonObject.getString("id");
                    }
                } else {
                    //并发任务满了，所以需要排队处理
                    if (response.code() == 429) {
                        return "429";
                    }
                    if (response.code() == 400 && null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        String error = jsonObject.getString("error");
                        if (error.equals("The current plan cannot use explore mode.")) {
                            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY需要充值", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()));
                            return null;
                        }
                    }
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + BUrlUtil.respErrorInfoByCode(response.code()) + ", param= " + JSONObject.toJSONString(runWayTaskReq));
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt(); // Restore interrupted status
                        log.info("Thread was interrupted, Failed to complete operation");
                        return null;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY视频提交失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY提交任务失败", "ERRORInfo=" + e.getMessage() + ", param= " + JSONObject.toJSONString(runWayTaskReq));

                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt(); // Restore interrupted status
                    log.info("Thread was interrupted, Failed to complete operation");
                    return null;
                }
            }
        }
        return null;
    }

    /**
     * 获取runway的任务状态
     * @param runwayId runwayId
     * @param jobId jobId
     * @param token token
     * @return 任务状态
     */
    public static RunWayTaskResp getRunwayVideoJobState(Integer runwayId, String jobId, String token) {
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/tasks/" + jobId + "?asTeamId=" + runwayId)
                .get()
                .addHeader("authorization", "Bearer " + token)
                .build();
        for (int attempt = 1; attempt <= 3; attempt++) {
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body().string();
                log.info("RUNWAY视频返回Code = " + response.code());
                log.info("RUNWAY视频返回参数 = " + responseBody);
                if (response.isSuccessful()) {
                    if (null != response.body()) {
                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                        System.out.println(JSONObject.toJSONString(jsonObject));
                        String task = jsonObject.getString("task");
                        RunWayTaskResp runWayTaskResp = JSONObject.parseObject(task, RunWayTaskResp.class);
                        System.out.println("RUNWAY视频返回参数 = " + runWayTaskResp);
                        return runWayTaskResp;
                    }
                } else {
                    if (attempt == 3) {
                        BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY拉取视频失败", "code=" + response.code() + " ErrorInfo =" + responseBody + ", jobId= " + jobId);
                        return null;
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex) {
                        return null;
                    }
                }
            } catch (Exception e) {
                log.error("RUNWAY模式获取视频失败：{}", e.getMessage());
                if (attempt == 3) {
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "RUNWAY拉取视频失败", "ERRORInfo=" + e.getMessage() + ", jobId= " + jobId);
                    return null;
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    return null;
                }
            }
        }
        return null;
    }

    /**
     * 获取runway的sessionId
     * @param asTeamId asTeamId
     * @param token token
     * @return sessionId
     */
    public static String getRunWaySessionId(Integer asTeamId, String token) {
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("asTeamId", asTeamId);
        jsonParam.put("taskIds", new ArrayList<>());
        log.info("getRunWaySessionId请求参数：" + jsonParam.toString());
        Request request = new Request.Builder()
                .url(RUNWAY_API_URL + "/v1/sessions")
                .post(RequestBody.create(jsonParam.toString(), MediaType.parse("application/json")))
                .addHeader("Accept", "application/json")
                .addHeader("Accept-Encoding", "gzip, deflate, br, zstd")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("Content-Length", "34")
                .addHeader("Content-Type", "application/json")
                .addHeader("Origin", "https://app.runwayml.com")
                .addHeader("Referer", "https://app.runwayml.com/")
                .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                .addHeader("Sec-Ch-Ua-Mobile", "?0")
                .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .build();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            log.info("getRunWaySessionId返回Code = " + response.code());
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                String jsonString = jsonObject.getString("session");
                log.info("getRunWaySessionId=" + jsonString);
                JSONObject sessionObject = JSONObject.parseObject(jsonString);
                return sessionObject.getString("id");
            }
        } catch (Exception e) {
            log.error("getRunWaySessionId报错：{}", e.getMessage());
            return null;
        }
        return null;
    }

    public static void main(String[] args) throws IOException {
        System.out.println("测试 uploadImageToRunWay2 方法...");

        // 1. 准备 RunWayFileUploadRes 对象
        // 在实际场景中，这个对象通常由 uploadImageToRunWay1 方法返回
        RunWayFileUploadRes mockRunWayFileUploadRes = new RunWayFileUploadRes();
        List<String> uploadUrls = new ArrayList<>();
        // 重要提示: 下面的 URL 是一个占位符。
        // 在实际测试中，这里应该是一个有效的、可接受 PUT 请求的预签名 URL，
        // 通常由 RunwayML 的 /v1/uploads 端点（即 uploadImageToRunWay1 的功能）提供。
        // 使用无效 URL 将导致网络错误，但仍可测试方法的部分逻辑。
        uploadUrls.add("https://mock-runway-upload.example.com/some-presigned-url");
        mockRunWayFileUploadRes.setUploadUrls(uploadUrls);

        JSONObject headersJson = new JSONObject();
        // Content-Type 应与实际上传的图片类型匹配
        headersJson.put("Content-Type", "image/jpeg");
        mockRunWayFileUploadRes.setUploadHeaders(headersJson.toJSONString());
        // mockRunWayFileUploadRes.setId("mock-upload-id-123"); // 如果后续测试 uploadImageToRunWay3 会需要

        // 2. 准备图片数据和扩展名
        // 使用一个简单的字符串作为虚拟图片数据
        byte[] imageData;
        try {
            imageData = "这是一个模拟图片文件的字节内容。".getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            System.err.println("无法将字符串转换为字节: " + e.getMessage());
            imageData = new byte[0]; // Fallback to empty byte array
        }
        String imageExtension = "jpg"; // 这个参数在 uploadImageToRunWay2 方法内部目前未被使用

        // 3. 调用被测试的方法
        try {
            // 注意：此调用会尝试发起一个实际的网络请求。
            String eTag = uploadImageToRunWay2(imageData, imageExtension, mockRunWayFileUploadRes);

            if (eTag != null) {
                System.out.println("uploadImageToRunWay2 调用成功。 ETag: " + eTag);
                System.out.println("这意味着图片数据已（模拟）发送到指定的 uploadUrl，并且服务器返回了 ETag。");
                // 在真实的流程中，获取到 eTag 后，通常会调用 uploadImageToRunWay3 来完成上传过程。
                // 例如:
                // String token = "YOUR_VALID_RUNWAY_TOKEN"; // 需要一个有效的 Runway token
                // if (StringUtils.isNotBlank(mockRunWayFileUploadRes.getId()) && StringUtils.isNotBlank(token)) {
                //     String finalUrl = uploadImageToRunWay3(mockRunWayFileUploadRes, eTag, token);
                //     System.out.println("uploadImageToRunWay3 调用成功。 Final URL: " + finalUrl);
                // } else {
                //     System.out.println("跳过 uploadImageToRunWay3 测试，因为缺少 id 或 token。");
                // }
            } else {
                System.out.println("uploadImageToRunWay2 调用失败。未能获取 ETag。");
                System.out.println("请检查网络连接、提供的 uploadUrl 是否有效，以及目标服务器的响应。");
            }
        } catch (IOException e) {
            System.err.println("调用 uploadImageToRunWay2 时发生 IOException: " + e.getMessage());
            // 打印堆Stack跟踪以获取更详细的错误信息，尤其是在网络请求失败时
             e.printStackTrace();
        }
    }


}
