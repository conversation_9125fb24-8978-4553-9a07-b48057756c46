package com.business.runway;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.business.enums.BRedisKeyEnum;
import com.business.model.po.ImgDrawRecordPO;
import com.business.runway.model.*;
import com.business.utils.BOssUtil;
import com.business.utils.ImgDrawUtil;
import com.nacos.config.OssClientConfig;
import com.nacos.redis.RedisUtil;
import com.nacos.utils.BFeiShuUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class RunwayOfficeApis {

    public static final String STATUS_PENDING = "PENDING";//挂起、待处理
    public static final String STATUS_FAILED = "FAILED";//运行中
    public static final String STATUS_RUNNING = "RUNNING";
    public static final String STATUS_SUCCEEDED = "SUCCEEDED";



    //生成runway视频
    public static String postToGenerateRunwayVideo( RunWayOfficeTaskReq runWayTaskReq,String token)  {
        return RunwayOfficeHttpUtil.postRunWayToGenerationVideo(runWayTaskReq, token);
    }

    public static String postsToGenerateJobState( String token, ImgDrawRecordPO imgDrawRecordPO) {
        RunWayOfficeTaskResp runwayVideoJobState = RunwayOfficeHttpUtil.getRunwayVideoJobState( imgDrawRecordPO.getVideoJobId(), token);
        if(runwayVideoJobState == null){
            return STATUS_FAILED;
        }
        if (STATUS_FAILED.equals(runwayVideoJobState.getStatus()) ) {
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1,"RUNWAY拉取任务业务失败","getRunwayVideoJobState.resp:"+ JSON.toJSONString(runwayVideoJobState)+",taskid= "+runwayVideoJobState.getId());
            return STATUS_FAILED;
        }
        if (STATUS_SUCCEEDED.equals(runwayVideoJobState.getStatus())) {
            log.info("runway视频原地址= " + runwayVideoJobState.getOutput());
            return uploadLumaVideo(runwayVideoJobState.getOutput().get(0), imgDrawRecordPO.getVideoJobId(), 17, OssClientConfig.FILE_SUFFIX_VIDEO);
        } else if (STATUS_RUNNING.equals(runwayVideoJobState.getStatus())) {
            return STATUS_RUNNING;
        }
        return STATUS_RUNNING;
    }



    // TODO 上传视频到oss
    public static String uploadLumaVideo(String fileUrl, String videoJobId, Integer folder, String suffix) {
        if (fileUrl == null) return null;
        int maxRetries = 4; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadURL(fileUrl, videoJobId, folder, suffix);
                if (videoPath != null) {
                    return videoPath;
                }
                log.error("runway视频上传失败，重试次数：" + (retryCount + 1), videoJobId);
            } catch (Exception e) {
                log.error("runway上传图片到OSS发生异常，重试次数：" + (retryCount + 1), e);
            }
            retryCount++;
        }
        log.error("runway模型文件上传失败，超过最大重试次数， 视频id=", videoJobId);
        return null;
    }

}
