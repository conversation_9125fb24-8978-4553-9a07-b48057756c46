package com.business.gpt;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.business.gpt.model.GptRequestBody;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionContentPart;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.Arrays;
import java.lang.System;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;


@Slf4j
public class ALiTyqwGPTWebUtil {

    private static final String API_URL = "https://api.openai.com/v1/chat/completions";
    private static final String PROXY_API_URL = "/v1/chat/completions";

    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private static final String fixedPrompt = "。任务说明：\n" +
            "图片描述生成：\n" +
            "当用户传递图片时，生成适合AI绘画工具（如Midjourney, SD等）的描述性语言，请注意识别图片中的艺术风格。\n" +
            "描述语言要简练，并根据图片的文化背景进行编写，突出关键元素和氛围。\n" +
            "文字润色：\n" +
            "当用户输入文字时，对文字进行润色，使其符合AI绘画工具的需求。\n" +
            "保持语言简练，确保描述准确，突出关键细节和意图。\n" +
            "生成2个用于绘画的中文提示词prompt，每个prompt的字数在100字数以上200字数以内，不需要序号，直接输出2个可以直接用于ai绘画prompt内容，每个prompt使用#分割，请注意现在是单向输出，不需要与用户互动。";

    public static String generateCreativityPrompt(GptRequestBody gptRequestBody, String apiKey, String apiUrl) {
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(10, TimeUnit.MINUTES)
                .writeTimeout(10, TimeUnit.MINUTES)
                .build();

        String requestBody = null;
        if (gptRequestBody.getImageUrl() == null || gptRequestBody.getImageUrl().isEmpty()) {
            requestBody = textOutTextReq("输入内容：" + gptRequestBody.getContentSys(),
                    gptRequestBody.getContentUser() + fixedPrompt, gptRequestBody.getModel());
        } else {
            requestBody = textAndImageOutTextReq("输入内容：" + gptRequestBody.getContentUser() + fixedPrompt,
                    gptRequestBody.getImageUrl(), gptRequestBody.getModel());
        }

        RequestBody body = RequestBody.create(requestBody, JSON);
        Request request = new Request.Builder()
                .url(apiUrl.concat(PROXY_API_URL))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + apiKey)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                if (response.body() != null) {
                    String responseBody = response.body().string();
                    log.info("chat图生文responseBody, {}", responseBody);
                    return responseBody;
                }
            }
            log.info("chat图生文 response, {}", response);
        } catch (IOException e) {
            log.error("chat图生文失败, {}", e.getMessage(), e);
            return null;
        }
        return null;
    }

    private static String textAndImageOutTextReq(String text, String url, String model) {
        // 构建消息内容的 JSON 对象
        JSONObject textContent = new JSONObject();
        textContent.put("type", "text");
        textContent.put("text", text);

        JSONObject imageUrlContent = new JSONObject();
        JSONObject imageUrlObj = new JSONObject();
        imageUrlObj.put("url", url);
        imageUrlContent.put("type", "image_url");
        imageUrlContent.put("image_url", imageUrlObj);

        JSONArray contentArray = new JSONArray();
        contentArray.add(textContent);
        contentArray.add(imageUrlContent);

        // 构建 messages 数组
        JSONObject messageObj = new JSONObject();
        messageObj.put("role", "user");
        messageObj.put("content", contentArray);
        JSONArray messagesArray = new JSONArray();
        messagesArray.add(messageObj);

        // 构建整个请求体对象
        JSONObject requestBodyObj = new JSONObject();
        requestBodyObj.put("model", model);
        requestBodyObj.put("messages", messagesArray);
        // requestBodyObj.put("max_tokens", 300);
        String requestBody = requestBodyObj.toString();
        System.out.println("图片输出Body= " + requestBody);
        return requestBody;
    }

    public static String textOutTextReq(String contentSys, String contentUser, String model) {
        // 创建内容对象1：系统消息
        JSONObject systemMessage = new JSONObject();
        systemMessage.put("role", "system");
        systemMessage.put("content", contentSys);
        // 创建内容对象2：用户消息
        JSONObject userMessage = new JSONObject();
        userMessage.put("role", "user");
        userMessage.put("content", contentUser);

        // 创建 messages 数组
        JSONArray messagesArray = new JSONArray();
        messagesArray.add(systemMessage);
        messagesArray.add(userMessage);
        JSONObject requestBodyObj = new JSONObject();
        requestBodyObj.put("model", model);
        requestBodyObj.put("messages", messagesArray);
        String requestBody = requestBodyObj.toString();
        System.out.println("文本输出Body= " + requestBody);
        return requestBody;
    }

    // chat
    static String apiKey = "fa6d1e7c-0387-4fde-ae0a-e9798ab5ab6d";
    static ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
    static Dispatcher dispatcher = new Dispatcher();
    static ArkService service = ArkService.builder().dispatcher(dispatcher).connectionPool(connectionPool)
            .baseUrl("https://ark.cn-beijing.volces.com/api/v3").apiKey(apiKey).build();

    /**
     * 创意提示词
     * 
     * @param gptRequestBody
     * @return
     */
    public static String generateCreativityPromptNew(GptRequestBody gptRequestBody) {
        final List<ChatMessage> messages = new ArrayList<>();
        final ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM)
                .content("输入内容：" + gptRequestBody.getContentSys()).build();
        final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER)
                .content(gptRequestBody.getContentUser() + fixedPrompt).build();
        messages.add(systemMessage);
        messages.add(userMessage);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model("ep-20250407135539-fnxv7")
                .messages(messages)
                .build();
        StringBuilder message = new StringBuilder();
        service.createChatCompletion(chatCompletionRequest).getChoices()
                .forEach(choice -> message.append(choice.getMessage().getContent()));
        service.shutdownExecutor();
        return message.isEmpty() ? null : message.toString();
    }


    @SneakyThrows
    public static String generateCreativityPromptNewFcs(GptRequestBody gptRequestBody)  {
        Generation gen = new Generation();
        Message systemMsg = Message.builder()
                .role(Role.SYSTEM.getValue())
                .content("输入内容：" + gptRequestBody.getContentSys())
                .build();
        Message userMsg = Message.builder()
                .role(Role.USER.getValue())
                .content(gptRequestBody.getContentUser() + fixedPrompt)
                .build();
        GenerationParam param = GenerationParam.builder()
                // 若没有配置环境变量，请用阿里云百炼API Key将下行替换为：.apiKey("sk-xxx")
                .apiKey("sk-560ad349cb6a4a7eaeb86194ffc77bb9")
                // 模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
                .model("qwen-max")
                .messages(Arrays.asList(systemMsg, userMsg))
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .build();
        GenerationResult result = gen.call(param);
        String content =result.getOutput().getChoices().get(0).getMessage().getContent();
        return content.isEmpty() ? null : content;
    }

//    /**
//     * 阿里创意提示词
//     *
//     * @param gptRequestBody
//     * @return
//     */
//    public static String generateCreativityPromptNewFcs(GptRequestBody gptRequestBody) {
//        final List<ChatMessage> messages = new ArrayList<>();
//        final ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM)
//                .content("输入内容：" + gptRequestBody.getContentSys()).build();
//        final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER)
//                .content(gptRequestBody.getContentUser() + fixedPrompt).build();
//        messages.add(systemMessage);
//        messages.add(userMessage);
//
//        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
//                .model("ep-20250407135539-fnxv7")
//                .messages(messages)
//                .build();
//        StringBuilder message = new StringBuilder();
//        service.createChatCompletion(chatCompletionRequest).getChoices()
//                .forEach(choice -> message.append(choice.getMessage().getContent()));
//        service.shutdownExecutor();
//        return message.isEmpty() ? null : message.toString();
//    }

    /**
     * 创意提示词（图片）
     * 
     * @param gptRequestBody
     * @return
     */
    public static String generateCreativityPromptByImgNew(GptRequestBody gptRequestBody) {
        final List<ChatMessage> messages = new ArrayList<>();
        final List<ChatCompletionContentPart> multiParts = new ArrayList<>();
        multiParts.add(ChatCompletionContentPart.builder().type("text").text(
                "输入内容：" + gptRequestBody.getContentUser() + fixedPrompt).build());
        multiParts.add(ChatCompletionContentPart.builder().type("image_url").imageUrl(
                new ChatCompletionContentPart.ChatCompletionContentPartImageURL(
                        gptRequestBody.getImageUrl()))
                .build());
        final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER)
                .multiContent(multiParts).build();
        messages.add(userMessage);

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model("ep-20250407140903-mc66p")
                .messages(messages)
                .build();
        StringBuilder message = new StringBuilder();
        service.createChatCompletion(chatCompletionRequest).getChoices()
                .forEach(choice -> message.append(choice.getMessage().getContent()));
        service.shutdownExecutor();
        return message.isEmpty() ? null : message.toString();
    }

}
