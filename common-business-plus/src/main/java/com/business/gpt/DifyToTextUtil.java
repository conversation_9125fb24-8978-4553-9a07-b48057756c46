package com.business.gpt;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.business.model.dto.GptTextDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Slf4j
public class DifyToTextUtil {

    private static final String API_URL = "https://dify.iworks.cn/v1/chat-messages";
    private static final String API_KEY = "app-ikEYCGuf5QQQ1jbitsGnWNsb";

    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    public static String generatePosterPrompt(GptTextDTO gptTextDTO) {
        CountDownLatch latch = new CountDownLatch(1);
        StringBuilder result = new StringBuilder();
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(10, TimeUnit.MINUTES)
                .writeTimeout(10, TimeUnit.MINUTES)
                .build();

        JSONObject requestBody = new JSONObject();
        requestBody.put("inputs", new JSONObject());
        requestBody.put("query", gptTextDTO.getContent());
        requestBody.put("response_mode", "streaming");
        requestBody.put("conversation_id", "");
        requestBody.put("user", "diandiansheji");
        requestBody.put("files", new JSONArray());

        RequestBody body = RequestBody.create(requestBody.toString(), JSON);
        Request request = new Request.Builder()
                .url(API_URL)
                .addHeader("Authorization", "Bearer " + API_KEY)
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();
        try {
            StringBuilder stringBuilder = new StringBuilder();
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    e.printStackTrace();
                    latch.countDown(); // 请求失败时也释放 latch
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    if (response.isSuccessful()) {
                        // 持续接收流式数据
                        String data;
                        try (ResponseBody responseBody = response.body()) {
                            if (responseBody != null) {
                                BufferedReader reader = new BufferedReader(new InputStreamReader(responseBody.byteStream()));
                                String line;
                                while ((line = reader.readLine()) != null) {
                                    if (line.startsWith("data: ")) {
                                        data = line.substring(6);  // 获取 "data:" 后的部分
                                        ObjectMapper objectMapper = new ObjectMapper();
                                        JsonNode rootNode = objectMapper.readTree(data);
                                        String event = rootNode.path("event").asText();
                                        if (!"message_end".equals(event) && !"agent_thought".equals(event)) {
                                            String answer = rootNode.path("answer").asText(); // 提取 answer 字段
                                            result.append(answer);
                                        }
                                        try {
                                            Thread.sleep(100);  // 模拟逐字显示效果
                                        } catch (InterruptedException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                            }
                        }
                    }
                    latch.countDown();
                }
            });
            latch.await(15, TimeUnit.MINUTES);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("请求失败", e.getMessage());
        }
        String promptInit = result.toString();
        if (promptInit.isEmpty()) {
            return null;
        }
        return promptInit.replaceAll("[\\n\\t\\r]", "")
                .replaceAll("`", "")
                .replaceAll("\\s+", "");
    }

}
