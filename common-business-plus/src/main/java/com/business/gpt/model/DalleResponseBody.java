package com.business.gpt.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "dalle绘图返回实体")
public class DalleResponseBody {

    @Schema(description = "时间戳")
    public Long created;

    @Schema(description = "合并后的人脸图片类型")
    public List<DataObject> data;

    @Data
    @Schema(description = "额外参数实体")
    public static class DataObject {

        @Schema(description = "返回提示次信息")
        public String revised_prompt;

        @Schema(description = "返回图片地址")
        public String url;

    }
}
