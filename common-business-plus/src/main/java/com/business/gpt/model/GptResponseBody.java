package com.business.gpt.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class GptResponseBody {

    public String id;
    public String object;
    public long created;
    public String model;
    public List<Choice> choices;
    public Usage usage;
    @JsonProperty("system_fingerprint")
    public String systemFingerprint;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Choice {
        public int index;
        public Message message;
        public Object logprobs;
        @JsonProperty("finish_reason")
        public String finishReason;

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Message {
            public String role;
            public String content;
        }
    }

    @Data
    public static class Usage {
        @JsonProperty("prompt_tokens")
        public int promptTokens;
        @JsonProperty("completion_tokens")
        public int completionTokens;
        @JsonProperty("total_tokens")
        public int totalTokens;
    }
}
