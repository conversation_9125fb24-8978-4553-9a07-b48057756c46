package com.business.gpt.model;

import lombok.Data;

@Data
public class GptRequestBody {

    private String model;

    private String contentSys;

    private String contentUser;

    private String imageUrl;

    public GptRequestBody() {
    }

    public GptRequestBody(String contentUser) {
        this.model = "gpt-4o";
        this.contentSys = "记住你的身份，精通AI绘画提示词编写，具备较高的艺术素养";
        this.contentUser = contentUser;
    }

    public GptRequestBody(String contentUser, String imageUrl) {
        this.model = "gpt-4o";
        this.contentSys = "记住你的身份，精通AI绘画提示词编写，具备较高的艺术素养";
        this.contentUser = contentUser;
        this.imageUrl = imageUrl;
    }
}
