package com.business.gpt.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Schema(description = "dall3绘图参数")
public class DalleRequestBody {

    @Schema(description = "dall-e-3模型")
    private String model;

    @Schema(description = "绘图提示词")
    private String prompt;

    @Schema(description = "绘图张数，默认是1，支持一张")
    private int n = 1;

    @Schema(description = "绘图分辨率，如：1024x1024")
    private String size;

    @Schema(description = "图片质量")
    private String quality;

    @Schema(description = "返回生成图像格式")
    private String response_format;

    @Schema(description = "生成图像样式")
    private String style;

    @Schema(description = "唯一用户标识")

    private String user;

    public static DalleRequestBody buildDalleRequestBody(String prompt, String size){
        DalleRequestBody dallEDrawRequestBody = new DalleRequestBody();
        dallEDrawRequestBody.model = "dall-e-3";
        dallEDrawRequestBody.prompt = prompt;
        if (size.equals("1:1")) {
            dallEDrawRequestBody.size = "1024x1024";
        } else if (size.equals("4:7")) {
            dallEDrawRequestBody.size = "1024x1792";
        } else {
            dallEDrawRequestBody.size = "1792x1024";
        }
        return dallEDrawRequestBody;
    }
}
