package com.business.gpt;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.business.gpt.model.DalleRequestBody;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Slf4j
public class GptToTextUtil {

    private static final String API_COMPLETIONS_URL = "https://api.openai.com/v1/chat/completions";
    private static final String API_URL1 = "https://api.openai-proxy.com/v1/chat/completions";
    private static final String API_KEY = "***************************************************";

    private static final String API_KEY1 = "https://api.openai-proxy.com/v1/images/generations";

    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    public static void getImageToText(String url) {
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(10, TimeUnit.MINUTES)
                .writeTimeout(10, TimeUnit.MINUTES)
                .build();

        String requestBody = "{" +
                "\"model\": \"gpt-4-vision-preview\"," +
                "\"messages\": [" +
                "    {" +
                "        \"role\": \"user\"," +
                "        \"content\": [" +
                "            {" +
                "                \"type\": \"text\"," +
                "                \"text\": \"这个图片里有啥\"" +
                "            }," +
                "            {" +
                "                \"type\": \"image_url\"," +
                "                \"image_url\": {" +
                "                    \"url\": \"https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg\"" +
                "                }" +
                "            }" +
                "        ]" +
                "    }" +
                "]," +
                "\"max_tokens\": 300" +
                "}";

        RequestBody body = RequestBody.create(requestBody, JSON);

        Request request = new Request.Builder()
                .url(API_COMPLETIONS_URL)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + API_KEY)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                if (response.body() != null) {
                    String responseBody = response.body().string();
                    log.info("chat图生文responseBody, {}",responseBody);
                }
            }
            log.info("chat图生文 response, {}",response);
        } catch (IOException e) {
            log.error("chat图生文, {}",e.getMessage(), e);
        }
    }

    public static String getTextToEn(String text) {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .cache(null)
                .build();
        JSONObject requestBody = getJsonObject(text);
        Request request = new Request.Builder()
                .url("https://api.openai.com/v1/chat/completions")
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + API_KEY)
                .post(RequestBody.create(requestBody.toString(),MediaType.parse("application/json")))
                .build();
        try {
            Response response = client.newCall(request).execute();
            String responseBody = null;
            if (response.body() != null) {
                responseBody = response.body().string();
            }
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                JSONArray jsonArray = null;
                if (jsonObject != null) {
                    jsonArray = jsonObject.getJSONArray("choices");
                }
                JSONObject jsonObject1 = null;
                if (jsonArray != null) {
                    jsonObject1 = jsonArray.getJSONObject(0);
                }
                JSONObject jsonObject2 = null;
                if (jsonObject1 != null) {
                    jsonObject2 = jsonObject1.getJSONObject("message");
                }
                String content = null;
                if (jsonObject2 != null) {
                    content = jsonObject2.getString("content");
                }
                log.info("chat翻译, content, {}",content);
                return content;
            }
            log.info("chat翻译, response, {}",response);
        } catch (Exception e) {
            log.error("chat翻译, Exception{}",e.getMessage(), e);
        }
        return null;
    }

    @NotNull
    private static JSONObject getJsonObject(String mJson) {
        JSONObject system = new JSONObject();
        system.put("role", "system");
        system.put("content", "你是一个中英文翻译助手,并对midjourney指令作出中文优化,尽量简洁,不允许出现重复内容");
        JSONObject user = new JSONObject();
        user.put("role", "user");
        user.put("content", mJson);
        JSONArray messagesArray = new JSONArray();
        messagesArray.add(system);
        messagesArray.add(user);
        JSONObject requestBody = new JSONObject();
        requestBody.put("model", "gpt-4-turbo-preview");
        requestBody.put("messages", messagesArray);
        return requestBody;
    }


    public static String dalleTextGenerationsImage(DalleRequestBody dalleRequestBody, String apiKey, String apiUrl) {
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(10, TimeUnit.MINUTES)
                .writeTimeout(10, TimeUnit.MINUTES)
                .build();

        Request request = new Request.Builder()
                .url(apiUrl.concat("/v1/images/generations"))
                .post(RequestBody.create(MediaType.parse("application/json"), JSONObject.toJSONString(dalleRequestBody)))
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + apiKey)
                .build();

        try (Response response = client.newCall(request).execute()) {
            log.info("dalle绘图, response, {}",response.toString());
            if (response.isSuccessful()) {
                return response.body().string();
            }
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            log.error("dalle绘图, Exception{}",e.getMessage(), e);
            return null;
        }
    }

}
