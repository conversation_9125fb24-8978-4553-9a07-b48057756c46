package com.business.sd.model;

import lombok.Data;

//sd api草图上色接口参数
@Data
public class SDToSketchBO {

    private String prompt; //提示词
    private String negativePrompt;//不希望在输出图像中看到的内容的文本简介。
    private Float controlStrength;//控制力量[ 0 .. 1] Default: 0.7
    private String outputFormat;//Enum: jpeg png webp。 Default: png
    private Integer seed;//随机种子值[ 0 .. 4294967294 ]

    // 拓展需要的参数字段
    private Integer left;
    private Integer right;
    private Integer up;
    private Integer down;
    public static SDToSketchBO initSDToSketch(String prompt, Float controlStrength) {
        SDToSketchBO sdToSketchBO = new SDToSketchBO();
        sdToSketchBO.prompt = prompt;
        if (controlStrength == null) {
            sdToSketchBO.controlStrength = 1F;
        } else {
            sdToSketchBO.controlStrength = controlStrength;
        }
        sdToSketchBO.outputFormat = "webp";
        return sdToSketchBO;
    }

    public static SDToSketchBO initSDInpaint(String prompt) {
        SDToSketchBO sdToSketchBO = new SDToSketchBO();
        sdToSketchBO.prompt = prompt;
        sdToSketchBO.outputFormat = "webp";
        return sdToSketchBO;
    }

    public static SDToSketchBO initSDOutpaint(Integer left, Integer right, Integer up, Integer down) {
        SDToSketchBO sdToSketchBO = new SDToSketchBO();
        sdToSketchBO.left = (null == left ? 0 : left);
        sdToSketchBO.right = (null == right ? 0 : right);
        sdToSketchBO.up = (null == up ? 0 : up);
        sdToSketchBO.down = (null == down ? 0 : down);
        sdToSketchBO.outputFormat = "webp";
        return sdToSketchBO;
    }

}
