package com.business.sd.model;

import lombok.Data;

//sd api生成视频接口参数
@Data
public class SDToVideoBO {
    private Integer seed;//随机种子值[ 0 .. 4294967294 ]
    private Double cfg_scale;//自由度[ 0 .. 10 ]
    private Integer motion_bucket_id;//动态幅度值[ 1 .. 255 ]

    public static SDToVideoBO initSdToVideoBO(Integer seed, Double cfg_scale, Integer motion_bucket_id) {
        SDToVideoBO sdToVideoBO = new SDToVideoBO();
        if (seed == null) {
            sdToVideoBO.seed = 0;
        } else {
            sdToVideoBO.seed = seed;
        }
        if (cfg_scale == null) {
            sdToVideoBO.cfg_scale = 1.8;
        } else {
            sdToVideoBO.cfg_scale = cfg_scale;
        }
        if (motion_bucket_id == null) {
            sdToVideoBO.motion_bucket_id = 127;
        } else {
            sdToVideoBO.motion_bucket_id = motion_bucket_id;
        }
        return sdToVideoBO;
    }
}
