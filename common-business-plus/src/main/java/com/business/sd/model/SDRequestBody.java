package com.business.sd.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Schema(description = "sd绘图参数")
public class SDRequestBody {

    @Schema(description = "sd提示词")
    private String prompt;

    @Schema(description = "sd比例")
    private String aspect_ratio;

    @Schema(description = "方式")
    private String mode;

    @Schema(description = "反向提示词")
    private String negative_prompt;

    @Schema(description = "模型")
    private String model;

    @Schema(description = "输出图片格式")
    private String output_format;

    public SDRequestBody(){}

    public SDRequestBody(String prompt, String aspect_ratio) {
        this.prompt = prompt;
        this.aspect_ratio = aspect_ratio;
        this.mode = "text-to-image";
        this.negative_prompt = "Do not appear deformed, distorted and other images";
        this.model = "sd3";
        this.output_format = "png";
    }
}
