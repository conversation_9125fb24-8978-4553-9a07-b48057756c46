package com.business.sd;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.business.sd.model.SDRequestBody;
import com.business.sd.model.SDToImageBO;
import com.business.sd.model.SDToSketchBO;
import com.business.sd.model.SDToVideoBO;
import com.business.utils.BFileUtil;
import com.business.utils.BOssUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

//SD api工具类
@Slf4j
public class SDApisUtil {

    private static final int MAX_RETRIES = 3; // 最大重试次数
    private static final String SD_API_KEY = "sk-NOTCLaKPTT8BqwhITnS0LJ0UNSRxKL9SyQO2LtxLuia8OBRf";

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .readTimeout(10, TimeUnit.MINUTES)
            .writeTimeout(10, TimeUnit.MINUTES)
            .build();

    public static File convert(MultipartFile multipartFile) {
        try {
            if (multipartFile == null){
                return null;
            }
            File file = new File(Objects.requireNonNull(multipartFile.getOriginalFilename()));
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(multipartFile.getBytes());
            fos.close();
            return file;
        } catch (IOException e) {
            return null;
        }
    }

    public static String postSdToVideo(byte[] imageData, SDToVideoBO sdToVideoBO) {
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("image", "image-to-video.png", RequestBody.create(MediaType.parse("image/png"), imageData))
                .addFormDataPart("seed", sdToVideoBO.getSeed().toString())
                .addFormDataPart("cfg_scale", sdToVideoBO.getCfg_scale().toString())
                .addFormDataPart("motion_bucket_id", sdToVideoBO.getMotion_bucket_id().toString())
                .build();
        Request request = new Request.Builder()
                .url("https://api.stability.ai/v2alpha/generation/image-to-video")
                .addHeader("authorization", "Bearer " + SD_API_KEY)
                .post(requestBody)
                .build();
        for (int i = 0; i < MAX_RETRIES; i++) {
            try (Response response = client.newCall(request).execute()) {
                log.info("视频生成 response:{}",response.toString());
                if (response.isSuccessful()) {
                    String body = response.body().string();
                    log.info("==*==SD视频生成=" + body);
                    JSONObject json = JSONObject.parseObject(body);
                    return json.getString("id");//返回id信息
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                // 打印异常信息
                if (i == MAX_RETRIES - 1) {
                    // 已达到最大重试次数，打印错误日志
                    e.printStackTrace();
                } else {
                    // 尚未达到最大重试次数，继续重试
                    System.out.println("请求超时，正在尝试重新发送...");
                }
            }
        }
        return null;
    }

    public static String getSDVideo(String jobId) throws Exception {
        Request request = new Request.Builder()
                .url("https://api.stability.ai/v2alpha/generation/image-to-video/result/" + jobId)
                .addHeader("authorization", "Bearer " + SD_API_KEY)
                .addHeader("accept", "video/*")
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.info("视频生成{}",response);
            int httpStatus = response.code();
            if (httpStatus == 202) {
                return "running";
            } else if (httpStatus == 200) {
                return uploadSDVideo(response.body().bytes(), jobId);
            } else {
                log.error("视频生成失败");
                return null;
            }
        }
    }

    // TODO 上传视频到oss
    public static String uploadSDVideo(byte[] fileBytes, String videoJobId) {
        int maxRetries = 3; // 最大重试次数
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                String videoPath = BOssUtil.uploadVideo(fileBytes, videoJobId.substring(videoJobId.length() - 32), 14);
                if (videoPath != null) {
                    return videoPath;
                }
                log.error("uploadImageToOss 图片上传失败，重试次数：" + (retryCount + 1), videoJobId);
            } catch (Exception e) {
                log.error("上传图片到OSS发生异常，重试次数：" + (retryCount + 1), e);
            }
            retryCount++;
        }
        log.error("uploadImageToOss 图片上传失败，超过最大重试次数， 视频id=", videoJobId);
        return null;
    }

    // sd 草图-to-精确、精细控制-上色
    public static String sdSketchToFineControl(byte[] imageData, SDToSketchBO sdToSketchBO) {
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("image", "sketch.png", RequestBody.create(MediaType.parse("image/png"), imageData))
                .addFormDataPart("prompt", sdToSketchBO.getPrompt())
                .addFormDataPart("control_strength", String.valueOf(sdToSketchBO.getControlStrength()))
                .addFormDataPart("output_format", sdToSketchBO.getOutputFormat())
                .build();

        Request request = new Request.Builder()
                .url("https://api.stability.ai/v2beta/stable-image/control/sketch")
                .addHeader("Authorization", "Bearer " + SD_API_KEY)
                .addHeader("Accept", "application/json")
                .post(requestBody)
                .build();

        try (Response response = client.newCall(request).execute()){
            log.info("sd草图-to-精确、精细控制-上色 response: {}", response.code());
            if (response.isSuccessful()) {
                return response.body().string();
            }
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            log.error("sd草图-to-精确、精细控制-上色", e);
            return null;
        }
    }

    // sd 原图结构-to-生成图片--风格
    public static String sdStructureToStyleTransfer(byte[] imageData, SDToSketchBO sdToSketchBO) {
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("image", "cat-statue.png", RequestBody.create(MediaType.parse("image/png"), imageData))
                .addFormDataPart("prompt", sdToSketchBO.getPrompt())
                .addFormDataPart("control_strength", String.valueOf(sdToSketchBO.getControlStrength()))
                .addFormDataPart("output_format", sdToSketchBO.getOutputFormat())
                .build();

        Request request = new Request.Builder()
                .url("https://api.stability.ai/v2beta/stable-image/control/structure")
                .addHeader("Authorization", "Bearer " + SD_API_KEY)
                .addHeader("Accept", "application/json")
                .post(requestBody)
                .build();

        try (Response response = client.newCall(request).execute()){
            log.info("sd风格-to-风格迁移 response: {}", response.code());
            if (response.isSuccessful()) {
                String resBody = response.body().string();
                return resBody;
            }
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            log.error("sd风格-to-风格迁移", e);
            return null;
        }
    }

    // sd 稳定的图像油漆--局部修改
    public static String sdStableImageEditInpaint(byte[] imageData, byte[] maskData, SDToSketchBO sdToSketchBO) {
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("image", "dog-wearing-vr-goggles.png", RequestBody.create(MediaType.parse("image/png"), imageData))
                .addFormDataPart("mask", "mask.png", RequestBody.create(MediaType.parse("image/png"), maskData))
                .addFormDataPart("prompt", sdToSketchBO.getPrompt())
                .addFormDataPart("output_format", sdToSketchBO.getOutputFormat())
                .build();

        Request request = new Request.Builder()
                .url("https://api.stability.ai/v2beta/stable-image/edit/inpaint")
                .addHeader("Authorization", "Bearer " + SD_API_KEY)
                .addHeader("Accept", "application/json")
                .post(requestBody)
                .build();

        try (Response response = client.newCall(request).execute()){
            log.info("sd替换=局部修改 response: {}", response.code());
            if (response.isSuccessful()) {
                String resBody = response.body().string();
                return resBody;
            }
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            log.error("sd局部修改", e);
            return null;
        }
    }

    // sd 稳定的图像拓展--自由拓展
    public static String sdStableImageEditOutpaint(byte[] imageData, SDToSketchBO sdToSketchBO) {
         RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("image", "husky-in-a-field.png", RequestBody.create(MediaType.parse("image/png"), imageData))
                .addFormDataPart("left", String.valueOf(sdToSketchBO.getLeft()))
                .addFormDataPart("right", String.valueOf(sdToSketchBO.getRight()))
                .addFormDataPart("up", String.valueOf(sdToSketchBO.getUp()))
                .addFormDataPart("down", String.valueOf(sdToSketchBO.getDown()))
                .addFormDataPart("output_format", String.valueOf(sdToSketchBO.getOutputFormat()))
                .build();

        Request request = new Request.Builder()
                .url("https://api.stability.ai/v2beta/stable-image/edit/outpaint")
                .addHeader("Authorization", "Bearer " + SD_API_KEY)
                .addHeader("Accept", "application/json")
                .post(requestBody)
                .build();

        try (Response response = client.newCall(request).execute()){
            log.info("sd自由拓展 response: {}", response.code());
            if (response.isSuccessful()) {
                return response.body().string();
            }
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            log.error("sd自由拓展", e);
            return null;
        }
    }

    public static String postSdTextToImage(SDRequestBody sdRequestBody) {
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("prompt", sdRequestBody.getPrompt())
                .addFormDataPart("aspect_ratio", sdRequestBody.getAspect_ratio())
                .addFormDataPart("mode", sdRequestBody.getMode())
                .addFormDataPart("negative_prompt", sdRequestBody.getNegative_prompt())
                .addFormDataPart("model", sdRequestBody.getModel()) // sd3 sd3-turbo
                .addFormDataPart("output_format", sdRequestBody.getOutput_format())
                .build();

        Request request = new Request.Builder()
                .url("https://api.stability.ai/v2beta/stable-image/generate/sd3")
                .addHeader("authorization", "Bearer " + SD_API_KEY)
                .addHeader("accept", "application/json")
                .post(requestBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.info("sd绘画状态码="+ response.code());
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }
    // sd3 图像上采样
    public static String imageToImageUpscale(String url) throws IOException, URISyntaxException, InterruptedException {
        File imgFile = BFileUtil.downloadFileFromURL(url, "sd-upscale.png");
        // 调用sd api
        File imageFile = new File(imgFile.getAbsolutePath());

        OkHttpClient client = new OkHttpClient();
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("image", imageFile.getName(), RequestBody.create(imageFile,MediaType.parse("image/png")))
                .build();
        // 构建请求
        Request request = new Request.Builder()
                .url("https://api.stability.ai/v1/generation/esrgan-v1-x2plus/image-to-image/upscale")
                .post(requestBody)
                .addHeader("Authorization", "Bearer " + SD_API_KEY)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response);
            }
            try (ResponseBody responseBody = response.body()) {
                if (responseBody != null) {
                    String jsonString = responseBody.string();

                    JSONObject jsonObject = JSON.parseObject(jsonString);
                    JSONArray artifactsArray = jsonObject.getJSONArray("artifacts");
                    if (artifactsArray != null && artifactsArray.size() > 0) {
                        JSONObject artifact = artifactsArray.getJSONObject(0); // Assuming there is only one artifact
                        String base64Value = artifact.getString("base64");
                        String outputPath = "F:\\Download\\音乐\\output-1.png"; // 更改为你想要存储的文件路径
                        decodeBase64ToFile(base64Value,outputPath);
                        System.out.println("Base64 value: " + base64Value);
                    } else {
                        System.out.println("No artifacts found in the JSON.");
                    }
                    return null; //responseBody.string();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void decodeBase64ToFile(String base64Data, String outputPath) {
        try {
            // 解码Base64数据
            byte[] decodedData = Base64.getDecoder().decode(base64Data);

            // 将数据写入文件
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                fos.write(decodedData);
                System.out.println("数据已成功写入文件：" + outputPath);
                fos.close();
            } catch (IOException e) {
                System.err.println("写入文件时出错：" + e.getMessage());
            }
        } catch (Exception e) {
            System.err.println("Error writing file: " + e.getMessage());
        }
    }

    public static String upscaleCreative(MultipartFile file) throws IOException{
        String apiUrl = "https://api.stability.ai/v2beta/stable-image/upscale/creative";

        OkHttpClient client = new OkHttpClient();

        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("image", file.getOriginalFilename(),
                        RequestBody.create(MediaType.parse("image/png"), file.getBytes()))
                .addFormDataPart("prompt", "cute fluffy white kitten floating in space, pastel colors")
                .addFormDataPart("output_format", "webp")
                .build();

        Request request = new Request.Builder()
                .url(apiUrl)
                .addHeader("Authorization", "Bearer " + SD_API_KEY)
                .addHeader("Accept", "application/json")
                .post(requestBody)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response);
            }
            return response.body().string();

        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


}
