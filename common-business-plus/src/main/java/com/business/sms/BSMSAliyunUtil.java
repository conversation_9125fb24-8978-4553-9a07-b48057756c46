package com.business.sms;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云短信
 * @Data 2024-1-12
 */

@Slf4j
public class BSMSAliyunUtil {
    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId 必填，您的 AccessKey ID
     * @param accessKeySecret  必填，您的 AccessKey Secret
     * @Remote endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
     * @return Client
     * @throws Exception
     */
    public static Client createClient(String endpoint, String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config().setAccessKeyId(accessKeyId).setAccessKeySecret(accessKeySecret);
        config.endpoint = endpoint;
        return new Client(config);
    }

    /**
     * 请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID 和 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
     * 工程代码泄露可能会导致 AccessKey 泄露，并威胁账号下所有资源的安全性。以下代码示例使用环境变量获取 AccessKey 的方式进行调用，仅供参考，建议使用更安全的 STS 方式，
     * 更多鉴权访问方式请参见：https://help.aliyun.com/document_detail/378657.html
     * @param phone
     * @param smsAliyunTmplCodeEnum
     */
    public static void phoneSmsSend(String endpoint, String accessKeyId, String accessKeySecret, String  signName, String phone, SMSAliyunTmplCodeEnum smsAliyunTmplCodeEnum, String templateParam) {
        Client client = null;
        try {
            client = createClient(endpoint,accessKeyId, accessKeySecret);
        } catch (Exception e) {
            log.error("创建SMS aliyun 客户端失败", e);
        }
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phone)
                .setSignName(signName)
                .setTemplateCode(smsAliyunTmplCodeEnum.getStrCode())
                .setTemplateParam(templateParam);
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            if (client != null) {
                client.sendSmsWithOptions(sendSmsRequest, runtime);
            }
        } catch (TeaException error) {
            log.error("发送短信失败1, {}",error.getMessage(), error);
            log.error("诊断地址1, {}",error.getData().get("Recommend"));
            Common.assertAsString(error.message);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            log.error("发送短信失败2, {}",error.getMessage(), error);
            log.error("诊断地址2, {}",error.getData().get("Recommend"));
            Common.assertAsString(error.message);
            Common.assertAsString(error.message);
        }
    }

}
