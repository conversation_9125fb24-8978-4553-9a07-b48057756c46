package com.business.message;

import lombok.Getter;

//消息推送类型
@Getter
public enum BMessageSendEnum {

    //登录
    LOGIN_PUSH(1001, "web端登录"),
    LOGIN_PUSH_APP(1002, "移动端登录"),

    //互动/通知
    INTERACTION_PUSH(1011, "互通消息"),
    NOTIFICATION_PUSH(1012, "通知消息"),

    //绑定
    WX_BIND_PUSH(1021, "绑定微信"),
    WX_PAY_PUSH(1022, "微信付款"),

    //写真
    PHOTO_PUSH(1031, "创意写真"),

    //绘图
    DRAW_JOB_PUSH(2001, "绘画任务"),
    DRAW_JOB_DE_PUSH(2002, "DALLE绘画任务"),

    //视频、高清重绘、草图上色、等等。。。
    VIDEO_JOB_SD_PUSH(3001, "视频生成任务"),

    //音频
    AUDIO_JOB_PUSH(4001, "音乐生成任务"),
    AUDIO_GENERATION_PUSH(4002, "音频生成任务"),

    //弹窗活动推送
    ACTIVITY_PUSH(5001, "弹窗推送"),

    //系统活动推送
    SYSTEM_ACTIVITY_PUSH(5005, "活动推送"),

    //数字人视频任务
    VIDEO_JOB_DIGITAL_PUSH(1101, "数字人视频任务"),

    //视频编辑任务
    VIDEO_EDIT_JOB_PUSH(1102, "视频编辑任务"),

    ;

    private final int type;
    private final String explain;

    BMessageSendEnum(int type, String explain) {
        this.type = type;
        this.explain = explain;
    }

}
