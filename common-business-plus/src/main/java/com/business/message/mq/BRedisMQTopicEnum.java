package com.business.message.mq;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name="redis 通道枚举", description="redis 通道枚举")
public enum BRedisMQTopicEnum {

    TOPIC_GOAPI("TOPIC_GOAPI"),//绘图服务通知
    TOPIC_SYSTEM("TOPIC_SYSTEM"),//运营模块系统消息推送
    TOPIC_OPERATIONS("TOPIC_OPERATIONS"),//运营模块消息推送
    TOPIC_DIGITAL("TOPIC_DIGITAL"),//数字人视频任务通知
    TOPIC_DIGITAL_AUDIO("TOPIC_DIGITAL_AUDIO"),//数字人音频任务通知

    ;
    @Schema(description = "消息通道")
    private final String topic;

    BRedisMQTopicEnum(String topic) {
        this.topic = topic;
    }

    public static BRedisMQTopicEnum getTopic(String topic) {
        for (BRedisMQTopicEnum bRedisMQTopicEnum : BRedisMQTopicEnum.values()) {
            if (bRedisMQTopicEnum.getTopic().equals(topic)) {
                return bRedisMQTopicEnum;
            }
        }
        return null;
    }

}
