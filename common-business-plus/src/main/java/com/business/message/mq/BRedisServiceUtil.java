package com.business.message.mq;


import com.nacos.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BRedisServiceUtil {

    @Retryable(maxAttempts = 5, backoff = @Backoff(delay = 2000))
    public static boolean sendMessage(BRedisMQTopicEnum bRedisMQTopicEnum, String jsonMessage) {
        try {
            RedisUtil.convertAndSend(bRedisMQTopicEnum.getTopic(),jsonMessage);
            return true;
        }catch (Exception e){
            log.error("redis队列消息发送失败= {}", e.getMessage(),e);
            return false;
        }
    }

    // 推送运营消息
    public static boolean sendMessageOperations(String jsonMessage){
        return sendMessage(BRedisMQTopicEnum.TOPIC_OPERATIONS, jsonMessage);
    }

    // 绘图模块任务推送
    public static boolean sendMessageMJ(String jsonMessage){
        return sendMessage(BRedisMQTopicEnum.TOPIC_GOAPI, jsonMessage);
    }

    // 系统消息推送
    public static boolean sendMessageSystem(String jsonMessage){
        return sendMessage(BRedisMQTopicEnum.TOPIC_SYSTEM, jsonMessage);
    }

    // 数字人视频任务推送
    public static boolean sendMessageDigital(String jsonMessage){
        return sendMessage(BRedisMQTopicEnum.TOPIC_DIGITAL, jsonMessage);
    }

    // 数字人音频任务推送
    public static boolean sendMessageDigitalAudio(String jsonMessage){
        return sendMessage(BRedisMQTopicEnum.TOPIC_DIGITAL_AUDIO, jsonMessage);
    }
}
