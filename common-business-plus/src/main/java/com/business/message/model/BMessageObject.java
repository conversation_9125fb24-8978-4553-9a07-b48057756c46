package com.business.message.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(name = "消息推送全局实体", description = "消息推送全局实体")
public class BMessageObject {

    public BMessageObject(Integer type, Object object) {
        this.type = type;
        this.object = object;
    }

    public BMessageObject(Long userId, Integer type, Object object) {
        this.userId = userId;
        this.type = type;
        this.object = object;
    }

    public BMessageObject(String redisId, Integer type, Object object) {
        this.redisId = redisId;
        this.type = type;
        this.object = object;
    }

    public BMessageObject(Long userId, Integer type, Object object, String message) {
        this.userId = userId;
        this.type = type;
        this.object = object;
        this.message = message;
    }

    public BMessageObject() {
    }

    @Schema(name = "用户 ID", type = "Long")
    private Long userId;

    @Schema(name = "用户 ID", type = "Long")
    private String redisId;

    @Schema(name = "消息内容", type = "Long")
    private String message;

    @Schema(name = "消息类型", type = "Integer")
    private Integer type;

    @Schema(name = "消息内容", type = "Object")
    private Object object;


}
