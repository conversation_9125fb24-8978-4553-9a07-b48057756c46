package com.business.message.websocket;

import com.alibaba.fastjson2.JSONObject;
import com.business.message.BMessageSendEnum;
import lombok.extern.slf4j.Slf4j;

//WebSocket发送消息工具
@Slf4j
public class BWebSocketSendUtil {

    public static String getMessageStr(Long userId, BMessageSendEnum bWebSocketEnum, Object object){
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("userId",userId);
            jsonObject.put("type",bWebSocketEnum.getType());
            jsonObject.put("object",JSONObject.toJSONString(object));
            return jsonObject.toJSONString();
        } catch (Exception e) {
            return null;
        }
    }
}
