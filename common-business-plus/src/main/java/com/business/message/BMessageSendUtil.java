package com.business.message;

import com.alibaba.fastjson2.JSONObject;
import com.business.message.model.BMessageObject;
import lombok.extern.slf4j.Slf4j;

//消息发送规范工具
@Slf4j
public class BMessageSendUtil {

    //封装系统消息推送json信息
    public static String getJSONStr(BMessageSendEnum bWebSocketEnum, Object object){
        try {
            return JSONObject.toJSONString(new BMessageObject(bWebSocketEnum.getType(),object));
        } catch (Exception e) {
            return null;
        }
    }

    //封装消息推送json信息
    public static String getJSONStr(Long userId, BMessageSendEnum bWebSocketEnum, Object object){
        try {
            return JSONObject.toJSONString(new BMessageObject(userId,bWebSocketEnum.getType(),object));
        } catch (Exception e) {
            return null;
        }
    }

    public static String getJSONStr(Long userId, BMessageSendEnum bWebSocketEnum, Object object, String message){
        try {
            return JSONObject.toJSONString(new BMessageObject(userId,bWebSocketEnum.getType(),object, message));
        } catch (Exception e) {
            return null;
        }
    }

    public static String getJSONStr(String redisId, BMessageSendEnum bWebSocketEnum, Object object){
        try {
            return JSONObject.toJSONString(new BMessageObject(redisId,bWebSocketEnum.getType(),object));
        } catch (Exception e) {
            return null;
        }
    }

    //解析消息推送json信息
    public static BMessageObject getBMessageObject(String jsonMessage){
        try {
            return JSONObject.parseObject(jsonMessage,BMessageObject.class);
        } catch (Exception e) {
            return null;
        }
    }
}
