package com.business.feishu;

import com.alibaba.fastjson2.JSONArray;
import lombok.extern.slf4j.Slf4j;

//飞书机器人通知
@Slf4j
public class BFeiShuApis {

    public static String P1 = "P1";//高级别
    public static String P2 = "P2";//中级别
    public static String P3 = "P3";//低级别
    public static String P4 = "P4";//最低级别

    public static String admin = ":管理服务";//绘图服务
    public static String draw = ":绘图服务";//绘图服务
    public static String user = ":用户服务";//用户服务
    public static String text = ":文本服务";//文本服务
    public static String monitor = ":账号监听服务";//账号监听服务

    public static void sedCardErrorFromAdmin(String p, String title, String session, boolean isAutomatic, String cycle){
        BFeiShuWebhook.sedCardError(p,title, session, admin,isAutomatic,cycle);
    }

    //绘图服务报警
    public static void sedCardErrorFromDraw(String p, String title, String session, boolean isAutomatic, String cycle){
        BFeiShuWebhook.sedCardError(p,title, session, draw,isAutomatic,cycle);
    }
    public static void sedCardErrorFromUser(String p, String title, String session,boolean isAutomatic, String cycle){
        BFeiShuWebhook.sedCardError(p,title, session, user,isAutomatic,cycle);
    }
    public static void sedCardErrorFromText(String p, String title, String session,boolean isAutomatic, String cycle){
        BFeiShuWebhook.sedCardError(p,title, session, text,isAutomatic,cycle);
    }

    public static void sedCardErrorFromMonitor(String p, String title, String session,boolean isAutomatic, String cycle){
        BFeiShuWebhook.sedCardError(p,title, session, monitor,isAutomatic,cycle);
    }

    public static void sedCardWarnFromMonitor(String p, String title, String session){
        BFeiShuWebhook.sedCardWarn(p,title, session, monitor);
    }

    public static void sedCardWarnFromAdmin(String p, String title, String session){
        BFeiShuWebhook.sedCardWarn(p,title, session, admin);
    }

    public static void sedCardWarnFromDraw(String p, String title, String session){
        BFeiShuWebhook.sedCardWarn(p,title, session, draw);
    }

    public static void sedCardWarnFromUser(String p, String title, String session){
        BFeiShuWebhook.sedCardWarn(p,title, session, user);
    }

    public static void sedCardWarnFromText(String p, String title, String session){
        BFeiShuWebhook.sedCardWarn(p,title, session, text);
    }

    public static void sedCardOkFromAdmin(String title, String session){
        BFeiShuWebhook.sedCardOk(title, session, admin);
    }

    public static void sedCardOkFromDraw(String title, String session){
        BFeiShuWebhook.sedCardOk(title, session, draw);
    }

    public static void sedCardOkFromUser(String title, String session){
        BFeiShuWebhook.sedCardOk(title, session, user);
    }

    public static void sedCardOKFromText(String title, String session){
        BFeiShuWebhook.sedCardOk(title, session, text);
    }

    public static void sedCardText(String title, String sd_num, String dell3_num, String fast_num, String relaxed_num, JSONArray group_table){
        BFeiShuWebhook.sedCardText(title, sd_num, dell3_num, fast_num, relaxed_num, group_table);
    }

    public static void sedCardMjStatus(String title,JSONArray group_table){
        BFeiShuWebhook.sedCardMjStatus(title,group_table);
    }

    public static void sedCardMjCasualMode(String title,JSONArray group_table){
        BFeiShuWebhook.sedCardMjCasualMode(title,group_table);
    }

}
