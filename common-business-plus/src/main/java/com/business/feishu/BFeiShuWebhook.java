package com.business.feishu;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.Date;

//飞书机器人通知
@Slf4j
public class BFeiShuWebhook {
    static String webhookTimedNotifications = "https://open.feishu.cn/open-apis/bot/v2/hook/e6716c63-3ed4-4548-a26e-4e29673cfc19";//定时通知机器人
    static String webhookMonitor = "https://open.feishu.cn/open-apis/bot/v2/hook/1adeb3b6-c0db-45cc-b19e-a1219b742c28";//点点监测机器人
    static String webhookCasual = "https://open.feishu.cn/open-apis/bot/v2/hook/6888ccdf-106d-46f6-a4a7-1626f99d2d3b";

    static String osName = "[ "+System.getProperty("os.name")+" ]";//运行系统名称
    static String activeProfiles = System.getProperty("spring.profiles.active");//运行环境

    private static String getActiveName(){
        return switch (activeProfiles) {
            case "test" -> "线上测试: " + osName;
            case "prod" -> "<font color='green'>线上正式: " + osName + "</font>";
            case null, default -> "开发调试: " + osName;
        };
    }

    static String titleErrorPrefix = ":异常:";
    static String titleWarnPrefix = ":警告:";
    static String titleNoticePrefix = "通知:";
    static String titleStatisticsPrefix = "统计:";

    final static String errorCardId= "ctp_AA1W1PuwfX1J";//异常卡片
    final static String warnCardId = "ctp_AA1oTrX644pt";//告警卡片
    final static String okCardId = "ctp_AAUdKzREJGUm";//处理成功通知
    final static String drawQuantityCardId = "ctp_AAUwcilCZiRa";//绘图统计表单卡片
    final static String mjStatusCardId = "ctp_AAUL6ke8tf1r";//绘图统计表单卡片
    final static String mjCasualModleCardId = "AAqCPa7DHO7OC";

    private static void sendWebhookCardAll(String url, String cardId, JSONObject jsonObject){
        try {
            OkHttpClient client = new OkHttpClient.Builder().cache(null).build();
            String bodyStr = "{\"msg_type\":\"interactive\",\"card\":{\"type\":\"template\",\"data\":{\"template_id\":\""+cardId+"\",\"template_variable\":"+jsonObject.toJSONString()+"}}}";
            RequestBody body = RequestBody.create(
                    bodyStr,
                    MediaType.parse("application/json; charset=utf-8")
            );
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful() && response.code() != 200) {
                log.error("飞书机器人推送失败:response {}",response);
                String json = null;
                if (response.body() != null) {
                    json = response.body().string();
                }
                log.error("飞书机器人推送失败response.body {}",json);
            }
        } catch (IOException e) {
            log.error("飞书机器人推送失败：异常问题：",e);
        }
    }

    //错误通知服务
    protected static void sedCardError(String p,String title, String session, String server, boolean isAutomatic, String cycle){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("title", p+titleErrorPrefix+title);
        jsonObject.put("notification", session);
        jsonObject.put("server", getActiveName() + server);
        jsonObject.put("cycle", cycle);
        jsonObject.put("automate", isAutomatic ? "自动处理" : "人工操作");
        jsonObject.put("date", DateUtils.formatYMDHMS19(new Date()));
        sendWebhookCardAll(webhookMonitor,errorCardId,jsonObject);
    }

    //警告通知服务
    protected static void sedCardWarn(String p,String title, String session, String server){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("title", p+titleWarnPrefix+title);
        jsonObject.put("notification", session);
        jsonObject.put("server", getActiveName() + server);
        jsonObject.put("date", DateUtils.formatYMDHMS19(new Date()));
        sendWebhookCardAll(webhookMonitor,warnCardId,jsonObject);
    }

    protected static void sedCardOk(String title, String session, String server){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("title", titleNoticePrefix+title);
        jsonObject.put("notification", session);
        jsonObject.put("server", getActiveName() + server);
        jsonObject.put("date", DateUtils.formatYMDHMS19(new Date()));
        sendWebhookCardAll(webhookMonitor,okCardId,jsonObject);
    }

    protected static void sedCardText(String title, String sd_num, String dell3_num, String fast_num, String relaxed_num, JSONArray group_table){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("title", "统计:绘图数量:"+title);
        jsonObject.put("remark", getActiveName() + ":" + DateUtils.formatYMDHMS19(new Date()));
        jsonObject.put("sd_num", getFontGreen(sd_num));
        jsonObject.put("dell3_num", getFontGreen(dell3_num));
        jsonObject.put("fast_num", getFontGreen(fast_num));
        jsonObject.put("relaxed_num", getFontRed(relaxed_num));
        jsonObject.put("group_table", group_table);
        sendWebhookCardAll(webhookTimedNotifications,drawQuantityCardId,jsonObject);
    }

    protected static void sedCardMjStatus(String title,JSONArray group_table){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("title", titleNoticePrefix+title);
        jsonObject.put("remark", getActiveName() + ":" + DateUtils.formatYMDHMS19(new Date()));
        jsonObject.put("group_table", group_table);
        sendWebhookCardAll(webhookTimedNotifications,mjStatusCardId,jsonObject);
    }

    protected static void sedCardMjCasualMode(String title,JSONArray group_table){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("title", "统计:休闲模式绘图数量:"+ title);
        jsonObject.put("remark", getActiveName() + ":" + DateUtils.formatYMDHMS19(new Date()));
        jsonObject.put("object_list_1", group_table);
        sendWebhookCardAll(webhookCasual,mjCasualModleCardId,jsonObject);
    }
    public static String getFontRed(String font){
        return "<font color='red'>"+font+"</font>";
    }

    public static String getFontGreen(String font){
        return "<font color='green'>"+font+"</font>";
    }



}
