<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.SceneRecordMapper">

    <!-- 查询场景记录列表 -->
    <select id="querySceneRecordPage" resultType="com.business.db.model.vo.text.SceneRecordQueryVO">
        SELECT
        id,
        scene_id,
        title,
        problem,
        message,
        create_time
        FROM scene_record
        WHERE user_id = #{dto.userId} AND deleted = 0 AND message IS NOT NULL
        <if test="dto.startTime != null">
            AND DATE_FORMAT( create_time, '%Y-%m-%d' ) &gt;= DATE_FORMAT( #{dto.startTime}, '%Y-%m-%d' )
        </if>
        <if test="dto.endTime != null">
            AND DATE_FORMAT( create_time, '%Y-%m-%d' ) &lt;= DATE_FORMAT( #{dto.endTime}, '%Y-%m-%d' )
        </if>
        ORDER BY id DESC
    </select>

    <select id="querySceneRecordsBySceneId" resultType="com.business.db.model.vo.text.SceneRecordQueryVO">
        SELECT
        id,
        scene_id,
        title,
        problem,
        message,
        create_time
        FROM scene_record
        WHERE user_id = #{dto.userId} AND  scene_id = #{dto.sceneId} AND  deleted = 0 AND message IS NOT NULL
        <if test="dto.startTime != null">
            AND DATE_FORMAT( create_time, '%Y-%m-%d' ) &gt;= DATE_FORMAT( #{dto.startTime}, '%Y-%m-%d' )
        </if>
        <if test="dto.endTime != null">
            AND DATE_FORMAT( create_time, '%Y-%m-%d' ) &lt;= DATE_FORMAT( #{dto.endTime}, '%Y-%m-%d' )
        </if>
        ORDER BY id DESC
    </select>

</mapper>
