<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.PhotoTopicSceneMapper">

    <!-- admin 分页批量查询 -->
    <select id="queryPage" resultType="com.business.db.model.vo.PhotoTopicSceneVO">
        SELECT
            photo_topic_scene.id,
            photo_topic_scene.topic_id,
            photo_topic_scene.scene_url,
            photo_topic_scene.img_type,
            photo_topic_scene.img_width,
            photo_topic_scene.img_height,
            photo_topic_scene.img_size,
            photo_topic_scene.img_hue,
            photo_topic_scene.create_time
        FROM photo_topic_scene
        WHERE photo_topic_scene.deleted = 0
    </select>


    <select id="selectPhotoTopicScenes" resultType="com.business.db.model.vo.PhotoTopicSceneVO">
            SELECT
                id,
                topic_id,
                scene_url,
                img_type,
                status,
                sort,
                img_width,
                img_height,
                img_size,
                create_time
            FROM photo_topic_scene
            WHERE topic_id = #{dto.topicId} AND deleted = 0
            ORDER BY sort ASC
    </select>

    <select id="selectPhotoTopicScenesByTopicIds" resultType="com.business.db.model.vo.PhotoTopicSceneVO">
        SELECT
            id,
            topic_id,
            scene_url,
            img_type,
            status,
            sort,
            img_width,
            img_height,
            img_size,
            create_time
        FROM photo_topic_scene
        WHERE deleted = 0 AND topic_id IN
        <foreach item="id" collection="topicIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY sort ASC
    </select>

    <delete id="delPhotoTopicScenesByTopicId">
        DELETE FROM photo_topic_scene WHERE topic_id = #{topicId}
    </delete>

</mapper>
