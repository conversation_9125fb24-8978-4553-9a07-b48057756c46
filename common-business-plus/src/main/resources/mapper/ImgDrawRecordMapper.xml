<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.ImgDrawRecordMapper">

    <update id="updateByIds">
        update img_draw_record set status = 4 where id in (
            <foreach collection="drawIds" item="item" separator="," >
                #{item}
            </foreach>
        )
    </update>

    <!-- app 2.2优化 -->
    <select id="queryHomeList" resultType="com.business.db.model.vo.ImgCommunityVO">
        SELECT
        idr.id,
        idr.prompt_init AS prompt,
        idr.prompt_use AS promptUse,
        tu.id AS painterId,
        tu.avatar AS painterAvatar,
        tu.`name` AS painterName,
        idd.id AS imgId,
        idd.img_url AS imageUrl,
        idd.good_qua AS goodNum,
        idd.collect_qua AS collectNum,
        idd.img_width AS width,
        idd.img_height AS height,
        idd.img_size AS size,
        idd.img_hue AS imageAve,
        idd.img_type AS contentType,
        idd.create_time AS produceTime,
        -- 是否收藏
        CASE WHEN ic.id IS NULL THEN 0 ELSE 1 END collectState,
        -- 是否点赞
        CASE WHEN il.id IS NULL THEN 0 ELSE 1 END likeState,
        -- 创作数量
        CASE WHEN works.worksNum IS NULL THEN 0 ELSE works.worksNum END AS worksNum,
        -- 默认非粉丝
        CASE WHEN focus.focus_user_id IS NULL THEN 0 ELSE 1 END AS focusFlag,
        -- 粉丝数量
        CASE WHEN fans.fansNum IS NULL THEN 0 ELSE fans.fansNum END AS fansNum
        FROM
        img_draw_record idr
        LEFT JOIN t_user tu ON idr.user_id = tu.id
        LEFT JOIN img_draw_detl idd ON idd.draw_record_id = idr.id
        AND idd.deleted = 0
        AND idd.is_publish = 1
        AND idd.img_width IS NOT NULL AND idd.img_url NOT LIKE '%midjourney%'
        LEFT JOIN t_task_img_like il ON il.img_id = idd.id AND il.task_id = idd.draw_record_id AND il.deleted = 0 AND il.user_id = #{dto.userId}
        LEFT JOIN t_task_img_collect ic ON ic.img_id = idd.id AND ic.task_id = idd.draw_record_id AND ic.deleted = 0 AND ic.user_id = #{dto.userId}
        -- 关联查询粉丝数量
        LEFT JOIN ( SELECT COUNT( 1 ) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id ) fans ON tu.id = fans.focus_user_id
        -- 关联查询创作数量
        LEFT JOIN (SELECT COUNT( 1 ) AS worksNum, user_id FROM img_draw_record WHERE deleted = 0
        AND status = 1
        AND fun_type = 1 GROUP BY user_id ) works ON tu.id = works.user_id
        LEFT JOIN t_user_focus focus ON tu.id = focus.focus_user_id AND focus.deleted = 0 AND focus.user_id = #{dto.userId}
        WHERE
        idr.fun_type = 1
        AND idr.`status` = 1
        -- 过滤黑名单
        AND idr.user_id NOT IN (SELECT block_user_id FROM mj_blacklist WHERE user_id = #{dto.userId} AND deleted = 0)
        AND idr.user_id IS NOT NULL AND tu.id IS NOT NULL
        AND idd.id IS NOT NULL
        <if test = "dto.prompt != null and dto.prompt != ''" >
            AND (idr.prompt_init LIKE CONCAT('%',#{dto.prompt},'%') OR idr.prompt_use LIKE CONCAT('%',#{dto.prompt},'%'))
        </if>
        <if test = "dto.painterId != null" >
            AND tu.id = #{dto.painterId}
        </if>
        GROUP BY idr.id
        ORDER BY idr.id DESC
        LIMIT #{dto.limitStart},#{dto.limitEnd};

    </select>

    <select id="queryHomeListTotal" resultType="java.lang.Long">
        SELECT
        IFNULL(COUNT(1),0)
        FROM
        img_draw_record idr
        LEFT JOIN t_user tu ON idr.user_id = tu.id
        LEFT JOIN img_draw_detl idd ON idd.draw_record_id = idr.id
        AND idd.deleted = 0
        AND idd.is_publish = 1
        AND idd.img_width IS NOT NULL AND idd.img_url NOT LIKE '%midjourney%'
        LEFT JOIN t_task_img_like il ON il.img_id = idd.id AND il.task_id = idd.draw_record_id AND il.deleted = 0 AND il.user_id = #{dto.userId}
        LEFT JOIN t_task_img_collect ic ON ic.img_id = idd.id AND ic.task_id = idd.draw_record_id AND ic.deleted = 0 AND ic.user_id = #{dto.userId}
        -- 关联查询粉丝数量
        LEFT JOIN ( SELECT COUNT( 1 ) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id ) fans ON tu.id = fans.focus_user_id
        -- 关联查询创作数量
        LEFT JOIN (SELECT COUNT( 1 ) AS worksNum, user_id FROM img_draw_record WHERE deleted = 0
        AND status = 1
        AND fun_type = 1 GROUP BY user_id ) works ON tu.id = works.user_id
        LEFT JOIN t_user_focus focus ON tu.id = focus.focus_user_id AND focus.deleted = 0 AND focus.user_id = #{dto.userId}
        WHERE
        idr.fun_type = 1
        AND idr.`status` = 1
        -- 过滤黑名单
        AND idr.user_id NOT IN (SELECT block_user_id FROM mj_blacklist WHERE user_id = #{dto.userId} AND deleted = 0)
        AND idr.user_id IS NOT NULL AND tu.id IS NOT NULL
        AND idd.id IS NOT NULL
        <if test = "dto.prompt != null and dto.prompt != ''" >
            AND (idr.prompt_init LIKE CONCAT('%',#{dto.prompt},'%') OR idr.prompt_use LIKE CONCAT('%',#{dto.prompt},'%'))
        </if>
        <if test = "dto.painterId != null" >
            AND tu.id = #{dto.painterId}
        </if>
    </select>






    <select id="guidePageList" resultType="com.business.db.model.vo.GuidePageVO">
        SELECT draw_record_main.id, draw_record_main.prompt_init, draw_record_main.img_url, draw_record_main.img_width, draw_record_main.img_height
        FROM (
             SELECT draw_record.id, draw_record.prompt_init, draw_record.img_url, draw_record.img_width, draw_record.img_height
             FROM (
                  SELECT
                      img_draw_record.id,
                      img_draw_record.prompt_init,
                      img_draw_detl.img_url,
                      img_draw_detl.img_width,
                      img_draw_detl.img_height
                  FROM
                      img_draw_record
                          LEFT JOIN img_draw_detl ON img_draw_record.id = img_draw_detl.draw_record_id
                  WHERE
                      img_draw_record.status = ${@com.nacos.enums.ImgDrawEnum@STATUS_FINISH_SUCCESS.getValue()}
                    AND img_draw_record.fun_type = ${@com.nacos.enums.ImgDrawEnum@FUN_TYPE_DRAW.getValue()}
                    AND img_draw_detl.img_width IS NOT NULL
                    AND img_draw_detl.img_url NOT LIKE '%midjourney%'
                  ORDER BY
                      img_draw_detl.create_time
                      LIMIT 260
                  ) draw_record
             GROUP BY draw_record.prompt_init LIMIT 24
             ) draw_record_main
        ORDER BY RAND();
    </select>

    <!-- admin 查看-->

    <!-- 查询昨日绘画数量统计并通知 -->
    <select id="selectDrawQuantityYesterdayList" resultType="com.business.db.model.bo.EveryDayDrawBO">
        SELECT COUNT(1) quantity, mj_is_relaxed AS mjIsRelaxed , mj_account_id AS mjAccountId, mode_attribute AS modeAttribute
        FROM img_draw_record
        WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
          AND fun_type = 1 AND user_id != "1749996069591289857"
        GROUP BY mj_account_id, mj_is_relaxed,mode_attribute
        ORDER BY mj_is_relaxed
    </select>

    <!-- 查询上周 -->
    <select id="selectDrawQuantityWeekList" resultType="com.business.db.model.bo.EveryDayDrawBO">
        SELECT COUNT(1) quantity, mj_is_relaxed AS mjIsRelaxed , mj_account_id AS mjAccountId, mode_attribute AS modeAttribute
        FROM
            img_draw_record
        WHERE
            create_time BETWEEN CURDATE() - INTERVAL WEEKDAY(
                    CURDATE()) DAY - INTERVAL 1 WEEK
                AND CURDATE() - INTERVAL WEEKDAY(
                    CURDATE()) DAY
          AND fun_type = 1 AND user_id != "1749996069591289857"
        GROUP BY mj_account_id, mj_is_relaxed,mode_attribute
        ORDER BY mj_is_relaxed
    </select>

    <!-- 查询上月 -->
    <select id="selectDrawQuantityMonthList" resultType="com.business.db.model.bo.EveryDayDrawBO">
        SELECT COUNT(1) quantity, mj_is_relaxed AS mjIsRelaxed , mj_account_id AS mjAccountId, mode_attribute AS modeAttribute
        FROM img_draw_record
        WHERE DATE_FORMAT( create_time, '%Y-%m' ) = DATE_FORMAT( CURDATE() - INTERVAL 1 MONTH, '%Y-%m' )
          AND fun_type = 1 AND user_id != "1749996069591289857"
        GROUP BY mj_account_id, mj_is_relaxed,mode_attribute
        ORDER BY mj_is_relaxed
    </select>

    <select id="queryPhotoHistoryPage" resultType="com.business.db.model.vo.PhotoHistoryVO">
        SELECT
        img_draw_record.id,
        img_draw_record.opt_attribute AS action,
        img_draw_record.prompt_init AS prompt,
        img_draw_record.status,
        img_draw_detl.img_type,
        img_draw_detl.img_index,
        img_draw_detl.img_size,
        img_draw_detl.img_width,
        img_draw_detl.img_height,
        img_draw_detl.img_url AS imgUrl,
        img_draw_detl.create_time,
        photo_topic_scene.id AS scene_id,
        photo_topic_scene.scene_url,
        photo_topic.id AS topic_id,
        photo_topic.topic_url,
        photo_topic.topic_title
        FROM img_draw_record
        LEFT JOIN img_draw_detl ON img_draw_record.id = img_draw_detl.draw_record_id
        LEFT JOIN photo_topic_scene ON img_draw_record.photo_scene_id = photo_topic_scene.id
        LEFT JOIN photo_topic ON photo_topic_scene.topic_id = photo_topic.id
        <where>
            <if test="dto.id != null">
                AND img_draw_record.id = #{dto.id}
            </if>
            <if test="dto.userId != null">
                AND img_draw_record.user_id = #{dto.userId}
            </if>
            AND img_draw_record.status != ${@com.nacos.enums.ImgDrawEnum@STATUS_FINISH_FAIL.getValue()}
            AND img_draw_record.fun_type = ${@com.nacos.enums.ImgDrawEnum@FUN_TYPE_PORTRAIT.getValue()} and img_draw_record.deleted=0
        </where>
        ORDER BY img_draw_record.create_time DESC
    </select>

    <select id="selectPhotoTaskOne" resultType="com.business.db.model.vo.PhotoHistoryVO">
        SELECT
            mj_task_new.id,
            mj_task_new.action,
            mj_task_new.prompt_init AS prompt,
            mj_task_new.status,
            mj_task_new.img_type,
            mj_task_new.img_index,
            mj_task_new.img_size,
            mj_task_new.img_width,
            mj_task_new.img_height,
            mj_task_new.main_img_url AS imgUrl,
            mj_task_new.create_time,
            photo_topic_scene.id AS scene_id,
            photo_topic_scene.scene_url,
            photo_topic.id AS topic_id,
            photo_topic.topic_url,
            photo_topic.topic_title
        FROM mj_task_new
                 LEFT JOIN photo_topic_scene ON mj_task_new.message_id = photo_topic_scene.id
                 LEFT JOIN photo_topic ON photo_topic_scene.topic_id = photo_topic.id
        WHERE mj_task_new.id = #{taskId} AND mj_task_new.action = ${@<EMAIL>()}
        ORDER BY mj_task_new.create_time DESC
    </select>

    <select id="selectImgDrawRecordPOList" resultType="com.business.model.po.ImgDrawRecordPO">
        SELECT id,user_id,super_id,opt_attribute,mode_attribute,init_img_urls,init_img_object,prompt_init,
               prompt_use,description,status,wh_divide,fun_type,width,height,img_quantity,is_publish,use_dd_qua,
               start_time,submit_time,finish_time,fail_reason,final_prompt,go_task_id,le_job_id,mj_job_id,mj_is_relaxed,mj_account_id,
               video_job_id,photo_scene_id,original_img_id,opt_describe,audio_job_id,suno_account_id,audio_title,audio_lyric,audio_style,
               data_version,deleted,creator,create_time,operator,operate_time,remark
        FROM img_draw_record WHERE deleted=0 and status=4 and fun_type=1
    </select>

    <select id="selectUserCasualModleStatList" resultType="com.business.db.model.po.admin.AdminUserDrawBO">
        SELECT * FROM (
            SELECT
            DATE(img_draw_record.create_time) AS createTime,
            COUNT(img_draw_record.id) AS totalRecords,
            t_user.`name` AS userName,
            t_user.mobile AS mobile
            FROM
            img_draw_record LEFT JOIN t_user ON img_draw_record.user_id=t_user.id
            WHERE img_draw_record.mj_is_casual=1 AND img_draw_record.`status`=1 AND
            DATE(img_draw_record.create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
            GROUP BY
            img_draw_record.user_id
            ) stat WHERE stat.totalRecords &gt; #{maximum1}
    </select>

    <select id="selectListByDetlId" resultType="com.business.model.po.ImgDrawRecordPO">
        SELECT id,user_id,super_id,opt_attribute,mode_attribute,init_img_urls,init_img_object,prompt_init,
               prompt_use,description,status,wh_divide,fun_type,width,height,img_quantity,is_publish,use_dd_qua,
               start_time,submit_time,finish_time,fail_reason,final_prompt,go_task_id,le_job_id,mj_job_id,mj_is_relaxed,mj_account_id,
               video_job_id,photo_scene_id,original_img_id,opt_describe,audio_job_id,suno_account_id,audio_title,audio_lyric,audio_style,
               data_version,deleted,creator,create_time,operator,operate_time,remark
        FROM img_draw_record WHERE deleted=0 and id in ( select draw_record_id from img_draw_detl where id = #{imgDrawDetlId})
    </select>

</mapper>
