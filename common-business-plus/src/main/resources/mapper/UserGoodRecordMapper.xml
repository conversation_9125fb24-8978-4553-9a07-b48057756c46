<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.UserGoodRecordMapper">

    <select id="selectOne" resultType="com.business.db.model.po.UserGoodRecordPO">
        SELECT
            id,
            user_id,
            state,
            submit_time,
            audit_time,
            data_version,
            deleted,
            creator,
            create_time,
            operator,
            operate_time,
            remark
        FROM user_good_record WHERE user_id = #{userId}
    </select>

</mapper>
