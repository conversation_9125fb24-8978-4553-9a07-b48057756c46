<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.UserMapper">

    <!-- 用户个人信息 -->
    <select id="personalPage" resultType="com.business.db.model.vo.PersonalPageVO">
        SELECT
            u.id,
            u.name,
            u.mobile,
            u.invitation_code ddid,
            u.avatar,
            u.prompt_flag,
            u.prompt_sign,
            u.is_agree_upload_music,
            CASE
                WHEN f1.focusNum IS NULL THEN
                    0 ELSE f1.focusNum
                END AS focusNum,
            CASE
                WHEN f2.fansNum IS NULL THEN
                    0 ELSE f2.fansNum
                END AS fansNum,
            CASE
                WHEN f4.collectNum IS NULL THEN
                    0 ELSE f4.collectNum
                END AS collectNum,
            CASE
                WHEN f6.likeNum IS NULL THEN
                    0 ELSE f6.likeNum
                END AS likeNum,
            CASE
                WHEN f3.focus_user_id IS NULL THEN
                    0 ELSE 1
                END AS focusFlag,
            CASE
                WHEN f5.user_id IS NULL THEN
                    0 ELSE 1
                END AS levelFlag,
            CASE
                WHEN f7.worksNum IS NULL THEN
                    0 ELSE f7.worksNum
                END AS worksNum
        FROM
            t_user u
                LEFT JOIN (SELECT COUNT(1) AS focusNum, user_id FROM t_user_focus WHERE deleted = 0 GROUP BY user_id) f1 ON u.id = f1.user_id
                LEFT JOIN (SELECT COUNT(1) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id) f2 ON u.id = f2.focus_user_id
                LEFT JOIN t_user_focus f3 ON u.id = f3.focus_user_id AND f3.deleted = 0 AND f3.user_id = #{loginUserId}
                LEFT JOIN (SELECT COUNT(1) AS collectNum, user_id FROM t_task_img_collect WHERE deleted = 0 GROUP BY user_id) f4 ON u.id = f4.user_id
                LEFT JOIN (SELECT SUM(img_draw_detl.good_qua) AS likeNum, img_draw_detl.user_id FROM img_draw_detl WHERE img_draw_detl.user_id=#{userId} AND img_draw_detl.deleted = 0) f6 ON u.id = f6.user_id
                LEFT JOIN (SELECT t_pay_info.user_id FROM t_pay_info where t_pay_info.use_state in (0,1) AND t_pay_info.pay_platform in (1,2) AND t_pay_info.platform_status = '支付成功') f5 on f5.user_id=u.id
                LEFT JOIN (SELECT COUNT( 1 ) AS worksNum, user_id FROM img_draw_record WHERE deleted = 0 AND status = 1 AND fun_type = 1 GROUP BY user_id) f7 ON u.id = f7.user_id
        WHERE u.id = #{userId}
        GROUP BY u.id
    </select>

    <select id="getByInvCodeUser" resultType="com.business.db.model.po.UserPO">
        select
            id,
            name,
            linkman,
            mobile,
            password,
            last_login_time,
            type,
            avatar,
            deleted,
            create_time,
            operate_time,
            personal_profile,
            from_user_name,
            wx_union_id,
            is_event,
            invitation_code,
            inviter_code
        from t_user where deleted = 0 and invitation_code=#{invitationCode}
    </select>

    <select id="userInfoById" resultType="com.business.db.model.vo.UserInfoVO">
        SELECT
            t_user.id,
            t_user.`name`,
            t_user.mobile,
            t_user.avatar,
            t_user.address,
            t_user.personal_profile,
            t_user.is_event,
            COUNT(CASE
                      WHEN (t_pay_info.use_state =0 OR t_pay_info.use_state = 1) AND t_pay_info.platform_status = '支付成功'
                          THEN 1 END) AS orderQuantity,
            SUM(CASE
                    WHEN ( t_pay_info.use_state = 0 OR t_pay_info.use_state = 1 ) AND t_pay_info.platform_status = '支付成功' AND ( t_pay_info.pay_platform = 3 OR t_pay_info.pay_platform = 4 )
                        THEN t_pay_info.total
                    WHEN ( t_pay_info.use_state = 0 OR t_pay_info.use_state = 1 ) AND t_pay_info.platform_status = '支付成功' AND ( t_pay_info.pay_platform = 1 OR t_pay_info.pay_platform = 2 )
                        THEN t_pay_info.monthly_num
                    ELSE 0 END ) AS total,
            SUM(CASE
                    WHEN ( t_pay_info.use_state = 0 OR t_pay_info.use_state = 1 ) AND t_pay_info.platform_status = '支付成功' AND ( t_pay_info.pay_platform = 3 OR t_pay_info.pay_platform = 4 )
                        THEN t_pay_info.total_usage
                    WHEN ( t_pay_info.use_state = 0 OR t_pay_info.use_state = 1 ) AND t_pay_info.platform_status = '支付成功' AND ( t_pay_info.pay_platform = 1 OR t_pay_info.pay_platform = 2 )
                        THEN t_pay_info.cycle_usage
                    ELSE 0 END ) AS remainingTimes,
            SUM(CASE
                    WHEN t_pay_info.platform_status = '支付成功'
                        THEN t_pay_info.total_usage ELSE 0 END) AS usedQuantity,
            SUM(CASE
                    WHEN t_pay_info.use_state in (0,1) AND t_pay_info.pay_platform in (1,2) AND t_pay_info.platform_status = '支付成功'
                        THEN 1 ELSE 0 END) AS levelFlag
        FROM
            t_user
                LEFT JOIN t_pay_info ON t_user.id = t_pay_info.user_id
        WHERE t_user.id = #{userId}
        GROUP BY
            t_user.id
    </select>

    <select id="getByUserId" resultType="com.business.db.model.po.UserPO">
        select
            t_user.id,
            t_user.name,
            t_user.linkman,
            t_user.mobile,
            t_user.password,
            t_user.last_login_time,
            t_user.first_login,
            t_user.type,
            t_user.remaining_times,
            t_user.remaining_times_code,
            t_user.email,
            t_user.ip_address,
            t_user.avatar,
            t_user.data_version,
            t_user.deleted,
            t_user.creator,
            t_user.create_time,
            t_user.operator,
            t_user.operate_time,
            t_user.address,
            t_user.personal_profile,
            t_user.from_user_name,
            t_user.wx_union_id,
            t_user.is_event,
            t_user.invitation_code,
            t_user.inviter_code,
            t_user.device_token,
            sem_channel.id channel_id
        from t_user left join sem_channel on sem_channel.user_id=t_user.id
        where t_user.id = #{userId}
    </select>

    <select id="getByFromUser" resultType="com.business.db.model.po.UserPO">
        select
        t_user.id,
        t_user.name,
        t_user.linkman,
        t_user.mobile,
        t_user.password,
        t_user.last_login_time,
        t_user.first_login,
        t_user.type,
        t_user.remaining_times,
        t_user.remaining_times_code,
        t_user.email,
        t_user.ip_address,
        t_user.avatar,
        t_user.data_version,
        t_user.deleted,
        t_user.creator,
        t_user.create_time,
        t_user.operator,
        t_user.operate_time,
        t_user.address,
        t_user.personal_profile,
        t_user.from_user_name,
        t_user.wx_union_id,
        t_user.is_event,
        t_user.invitation_code,
        t_user.inviter_code,
        sem_channel.id channel_id
        from t_user left join sem_channel on sem_channel.user_id=t_user.id
        where t_user.deleted = 0
        <choose>
            <when test="userId != null or (mobile != null and mobile != '') or (openId != null and openId != '') or (unionId != null and unionId != '')">
                <if test="userId != null">
                    and t_user.id = #{userId}
                </if>
                <if test="mobile != null and mobile != ''">
                    and t_user.mobile = #{mobile}
                </if>
                <if test="openId != null and openId != ''">
                    and t_user.from_user_name = #{openId}
                </if>
                <if test="unionId != null and unionId != ''">
                    and t_user.wx_union_id = #{unionId}
                </if>
            </when>
            <otherwise>
                and 1 = 0
            </otherwise>
        </choose>
        order by t_user.mobile desc
        limit 1
    </select>

    <select id="getFansParam" resultType="com.business.db.model.vo.UserParamVO">
        SELECT
            sem_channel.fans_switch,
            sem_channel.fans_give_qua
        FROM (SELECT t_user.id FROM t_user WHERE t_user.invitation_code = (SELECT t_user.inviter_code FROM t_user WHERE t_user.id=#{userId})) t_user
                 LEFT JOIN sem_channel ON sem_channel.user_id=t_user.id
        WHERE sem_channel.deleted = 0
    </select>

    <select id="getSuggestUsers" resultType="com.business.db.model.vo.SuggestUserVO">
        SELECT
            t_user.id userId,
            t_user.`name` nickname,
            t_user.avatar
        FROM t_user
        WHERE deleted = 0 AND t_user.type != -1 AND t_user.mobile IS NOT NULL ORDER BY t_user.create_time DESC LIMIT 45
    </select>

    <!-- 查询所有邀请码进行查重校验 -->
    <select id="getUserInviteCode" resultType="java.lang.String">
        SELECT t_user.invitation_code FROM t_user
    </select>

    <select id="selectFansCount" resultType="java.lang.Long">
        SELECT COUNT(t_user.id) FROM t_user
        WHERE t_user.deleted = 0 AND t_user.inviter_code = (SELECT t_user.invitation_code FROM t_user WHERE t_user.id=#{userId})
          AND DATE_FORMAT(t_user.create_time, '%Y-%m-%d' ) &gt;= #{firstDayOfMonth}
          AND DATE_FORMAT(t_user.create_time, '%Y-%m-%d' ) &lt;= #{lastDayOfMonth}
    </select>

    <select id="queryOpeStatisticsByYesterday" resultType="com.business.db.model.po.admin.OpeStatisticsUserPO">
        SELECT ifnull( addUser.addQuantity, 0 ) AS addQuantity,
               ifnull( share.shareAddQuantity, 0 ) AS shareAddQuantity,
               ifnull( record.payQuantity, 0 ) AS payQuantity
        FROM (SELECT count(1) AS addQuantity FROM `t_user` WHERE deleted=0 AND type!=-1 AND date_format(create_time, '%Y-%m-%d') = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY) AND CONVERT(mobile, SIGNED) > 0) addUser,
             (SELECT COUNT(*) AS shareAddQuantity FROM (SELECT id,invitation_code,inviter_code, create_time FROM t_user where inviter_code IS NOT NULL GROUP BY inviter_code) t_user
              WHERE date_format(t_user.create_time, '%Y-%m-%d') = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)) share,
             (SELECT COUNT(1) AS payQuantity FROM (SELECT user_id FROM pay_record WHERE deleted=0 AND state=1 AND date_format(create_time, '%Y-%m-%d')=DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)) t) record
    </select>

    <select id="queryActiveUserToday" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM sys_login_info WHERE date_format(login_time,'%Y-%m-%d') = #{time}
    </select>

    <select id="queryUserPage" resultType="com.business.db.model.vo.admin.UserQueryPageVO">
        SELECT
        t_user.id,
        t_user.`name`,
        t_user.linkman,
        t_user.mobile,
        t_user.last_login_time,
        t_user.type,
        t_user.email,
        t_user.ip_address,
        t_user.avatar,
        ddrecordTotal.total,
        ddrecordTotal.use_times,
        t_user.create_time,
        t_user.invitation_code
        FROM t_user
        LEFT JOIN (SELECT SUM(total) AS total, SUM(total_usage) AS use_times,user_id FROM user_ddrecord WHERE deleted = 0 GROUP BY user_id) ddrecordTotal ON t_user.id = ddrecordTotal.user_id
        WHERE t_user.deleted = 0 AND CONVERT(t_user.mobile, SIGNED) > 0
        <if test="dto.name != null and dto.name != ''">
            <bind name="name" value="'%'+dto.name+'%'"/>
            AND t_user.name like #{dto.name}
        </if>
        <if test="dto.mobile != null and dto.mobile != ''">
            <bind name="mobile" value="'%'+dto.mobile+'%'"/>
            AND t_user.mobile like #{dto.mobile}
        </if>
        <if test="dto.type != null">
            AND t_user.type = #{dto.type}
        </if>
        ORDER BY t_user.id DESC
    </select>

    <select id="selectUserList" resultType="com.business.db.model.vo.admin.UsersListVO">
        SELECT
            id,
            CONCAT(name, '[', mobile, ']') AS name,
            mobile
        FROM t_user WHERE deleted = 0 AND CONVERT(mobile, SIGNED) > 0
    </select>

    <select id="queryUserStatisticsByToday" resultType="com.business.db.model.vo.admin.UserStatisticsVO">
        SELECT ifnull( addUser.dayUserNumber, 0 ) AS dayUserNumber,
               ifnull( weekUser.weekNumber, 0 ) AS weekNumber,
               ifnull( monthUser.monthNumber, 0 ) AS monthNumber,
               ifnull( totalUser.totalNumber, 0 ) AS totalNumber,
               ifnull( record.totalPayUserNumber, 0 ) AS totalPayUserNumber,
               ifnull( payUser.totalPayPrice, 0 ) AS totalPayPrice
        FROM (SELECT count(1) AS dayUserNumber FROM `t_user` WHERE deleted=0 AND type!=-1 AND date_format(create_time, '%Y-%m-%d') = CURDATE() AND CONVERT(mobile, SIGNED) > 0) addUser,
             (SELECT count(1) AS weekNumber FROM `t_user`
              WHERE deleted=0 AND type!=-1 AND CONVERT(mobile, SIGNED) > 0 AND date_format(create_time, '%Y-%m-%d' ) &gt;= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
                AND date_format(create_time, '%Y-%m-%d') &lt;= DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 6 DAY)) weekUser,
             (SELECT count(1) AS monthNumber FROM `t_user` WHERE type!=-1 AND deleted=0 AND CONVERT(mobile, SIGNED) > 0 AND date_format(create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')) monthUser,
             (SELECT count(1) AS totalNumber FROM `t_user` WHERE type!=-1 AND deleted=0 AND CONVERT(mobile, SIGNED) > 0) totalUser,
             (SELECT COUNT(1) AS totalPayUserNumber FROM (SELECT user_id FROM pay_record WHERE deleted=0 AND state=1 GROUP BY user_id) t) record,
             (SELECT sum(amount) AS totalPayPrice FROM pay_record WHERE deleted=0 AND state=1) payUser
    </select>



    <select id="queryDailyOrder" resultType="com.business.db.model.vo.admin.DailyOrderVO">
        SELECT * FROM (
            SELECT
                count(1) AS orderNumber,
                date_format (create_time, '%Y-%m-%d') as days
            FROM pay_record
            WHERE YEAR (create_time) = YEAR (now()) AND MONTH (create_time) AND state = 1
            GROUP BY date_format(create_time, '%Y-%m-%d')
        ) T WHERE T.days LIKE CONCAT('%', DATE_FORMAT(NOW(), '%Y-%m'), '%') ORDER BY T.days
    </select>

    <select id="queryDailyOrderPrice" resultType="com.business.db.model.vo.admin.DailyOrderPriceVO">
        SELECT * FROM (
            SELECT
                sum(amount) AS price,
                date_format (create_time, '%Y-%m-%d') AS days
            FROM pay_record
            WHERE YEAR (create_time) = YEAR (now()) AND MONTH (create_time) AND state = 1
            GROUP BY date_format(create_time,'%Y-%m-%d')
        ) T WHERE T.days LIKE CONCAT('%', DATE_FORMAT(NOW(), '%Y-%m'), '%') ORDER BY T.days
    </select>

    <select id="queryFansPage" resultType="com.business.db.model.vo.FansOrFollowVO">
        SELECT   t_user.id AS userId,
                 t_user.`name` AS userName,
                 t_user.avatar AS avatar
        FROM t_user_focus
             LEFT JOIN t_user ON t_user.id = t_user_focus.user_id
        WHERE t_user.deleted = 0 AND t_user_focus.focus_user_id = #{dto.userId}
        ORDER BY t_user_focus.create_time DESC
    </select>

    <select id="queryFollowPage" resultType="com.business.db.model.vo.FansOrFollowVO">
        SELECT   t_user.id AS userId,
                 t_user.`name` AS userName,
                 t_user.avatar AS avatar
        FROM t_user_focus
                 LEFT JOIN t_user ON t_user.id = t_user_focus.focus_user_id
        WHERE t_user.deleted = 0 AND t_user_focus.user_id = #{dto.userId}
        ORDER BY t_user_focus.create_time DESC
    </select>

</mapper>
