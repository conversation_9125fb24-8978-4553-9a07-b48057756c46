<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.ImgDrawStyleConfigMapper">

    <select id="selectByIds" parameterType="java.util.List" resultType="java.lang.String">
        SELECT
            style_code
        FROM img_draw_style_config
        WHERE deleted = 0 AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectStyleConfigIdsByClassifyIdAndLabelId"  resultType="com.business.db.model.po.draw.ImgDrawStyleConfigPO">
        SELECT
         distinct config.*
        FROM img_draw_style_config config INNER JOIN  img_draw_style_config_classify  classify on config.id =classify.style_config_id  INNER JOIN img_draw_style_config_label  label on label.style_config_id =  classify.style_config_id
        where classify.deleted = 0
          and config.type in (1,2)
        <if test="classifyId != null">
          and  classify.classify_id = #{classifyId}
        </if>
        and label.deleted = 0
        <if test="labelId != null">
        and  label.label_id = #{labelId}
        </if>
        and config.deleted = 0 and config.is_use = 1
        <if test="isRecommend == 1">
            AND config.is_recommend = 1
        </if>
        order by config.sort desc, config.create_time desc
        <choose>
            <when test="isRecommend == 1">
                LIMIT 50
            </when>
            <otherwise>
                LIMIT 15
            </otherwise>
        </choose>

    </select>

    <update id="batchUpdate">
        UPDATE img_draw_style_config SET use_count = use_count + 1
        WHERE id IN (
            <foreach collection="ids" item="id" separator=",">
                #{id}
            </foreach>
        )
    </update>

</mapper>
