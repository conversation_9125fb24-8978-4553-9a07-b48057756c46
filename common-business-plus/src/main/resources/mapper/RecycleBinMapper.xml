<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.RecycleBinMapper">
    <select id="queryPage" resultType="com.business.db.model.vo.RecycleBinQueryVO">
        SELECT
            t_recycle_bin.id,
            t_recycle_bin.delete_time,
            t_user.id AS user_id,
            t_user.`name` AS user_name,
            t_user.avatar,
            mj_task_detl.id AS image_id,
            mj_task_detl.img_url image_url,
            DATEDIFF(t_recycle_bin.expire_time,NOW()) AS remain_days
        FROM t_recycle_bin LEFT JOIN t_user ON t_recycle_bin.user_id=t_user.id LEFT JOIN mj_task_detl on t_recycle_bin.img_id=mj_task_detl.id
        WHERE t_recycle_bin.deleted=0 AND t_user.id=#{dto.userId} ORDER BY t_recycle_bin.delete_time DESC
    </select>

    <delete id="batchDelRecycle" parameterType="java.util.List">
        DELETE FROM t_recycle_bin WHERE t_recycle_bin.id IN (
                <foreach collection="list" item="item" separator=",">
                    #{item}
                </foreach>
            )
    </delete>

    <insert id="batchInsert" parameterType="com.business.db.model.po.RecycleBinPO">
        INSERT INTO t_recycle_bin (id, user_id, img_id, delete_time, expire_time)
        VALUES
        <foreach collection="list" item="item" separator="," >
            (
            #{item.id},
            #{item.userId},
            #{item.imgId},
            #{item.deleteTime},
            #{item.expireTime}
            )
        </foreach>
    </insert>


    <update id="checkForExpire">
        UPDATE t_recycle_bin SET deleted = 1 WHERE t_recycle_bin.deleted=0 AND DATEDIFF(t_recycle_bin.expire_time,NOW()) = 0;
    </update>

    <select id="getImgIds" resultType="java.lang.Long">
        select t_recycle_bin.img_id from t_recycle_bin where t_recycle_bin.deleted=0 and t_recycle_bin.id in (
        <foreach collection="recycleIds" item="item" separator="," >
            #{item}
        </foreach>
        )
    </select>

</mapper>
