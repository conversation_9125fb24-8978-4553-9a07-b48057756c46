<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.ImgCollectMapper">
    <delete id="deleteCollect">
        DELETE
        FROM
            t_task_img_collect
        WHERE
            img_id = #{imgId}
          AND task_id = #{taskId}
          AND user_id = #{userId}
    </delete>
</mapper>
