<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.ExchangeRecordMapper">

    <!-- 1批量生成 兑换编号-->
    <insert id="insertBatch" parameterType="com.business.db.model.po.ExchangeRecordPO">
        INSERT INTO t_exchange_record (id, code, name, account_num, expiry_time, use_expiry_day)
        VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.id},
            #{item.code},
            #{item.name},
            #{item.accountNum},
            #{item.expiryTime},
            #{item.useExpiryDay})
        </foreach>
    </insert>

</mapper>
