<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.PayInfoMapper">

    <select id="queryMemberCardList" resultType="com.business.db.model.vo.MemberCardVO">
        SELECT t_user.`name`,
               t_user.avatar,
               t_pay_info.level_name,
               t_pay_info.payment_method
        FROM t_pay_info LEFT JOIN t_user ON t_user.id=t_pay_info.user_id
        WHERE t_pay_info.pay_platform in (1,2) and t_pay_info.use_state in (0,1) and t_pay_info.platform_status = '支付成功' ORDER BY t_pay_info.pay_time DESC LIMIT 10
    </select>

    <select id="queryUserConcurrency" resultType="java.lang.Integer">
        SELECT
            IFNULL(MAX(t_member_grade.concurrency), 0)
        FROM t_pay_info LEFT JOIN t_member_grade ON t_pay_info.level_id=t_member_grade.id
        WHERE t_pay_info.user_id=#{userId} AND t_pay_info.platform_status='支付成功' AND t_pay_info.pay_platform IN (1,2)
        ORDER BY t_member_grade.concurrency DESC LIMIT 1
    </select>

    <select id="queryMemberExpireTime" resultType="com.business.db.model.vo.PayInfoVO">
        SELECT
            t_pay_info.id,
            t_pay_info.level_id AS levelId,
            t_pay_info.level_name AS levelName,
            t_pay_info.pay_amount AS payAmount,
            t_pay_info.order_no AS orderNo,
            t_pay_info.platform_status AS platformStatus,
            t_pay_info.pay_platform AS payPlatform,
            t_pay_info.platform_number AS platformNumber,
            t_pay_info.expiration_time AS expirationTime,
            t_pay_info.monthly_num AS monthlyNum,
            t_pay_info.user_id AS userId,
            t_pay_info.order_time AS orderTime,
            t_pay_info.month AS month,
            t_pay_info.pay_time AS payTime,
            t_member_grade.payment_method
        FROM t_pay_info LEFT JOIN t_member_grade ON t_pay_info.level_id=t_member_grade.id
        WHERE user_id=#{userId} and pay_platform in (1,2) and use_state in (0,1) and platform_status = '支付成功' ORDER BY pay_time
    </select>

    <delete id="deletePayInfo">
        delete from t_pay_info where user_id = #{userId}
    </delete>


    <!-- 获取订单有效期 充值专用 : 过滤使用状态、支付状态、等级、用户、有效期倒序-->
    <select id="queryPayExpirationTime" resultType="java.util.Date" parameterType="java.lang.Long">
        SELECT tpi.expiration_time
        FROM t_pay_info tpi LEFT JOIN vip_config vc ON tpi.level_id = vc.id
        WHERE tpi.use_state != ${@com.nacos.enums.PayPlatformEnum@PAY_USE_STATE_EXPIRE.getCode()}
        AND tpi.platform_status = '${@<EMAIL>()}'
        AND expiration_time > NOW()
        AND tpi.user_id = #{userId}
        AND vc.grade = ${@com.nacos.enums.VipGradeEnum@SVIP_MEMBER.getGrade()}
        ORDER BY tpi.expiration_time DESC
        LIMIT 0,1
    </select>

</mapper>
