<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.FlowRecordMapper">
    <select id="selectMyRecord" resultType="com.business.db.model.vo.FlowRecordVO">
        SELECT
            id,
            record_type,
            num,
            remark,
            create_time
        FROM
            t_flow_record
        WHERE
            user_id = #{dto.userId} AND deleted = 0
        <if test="dto.recordType != null">
            AND record_type = #{dto.recordType}
        </if>
        ORDER BY id DESC
    </select>

    <delete id="deleteFlowRecord">
        DELETE FROM t_flow_record where user_id = #{userId}
    </delete>
</mapper>
