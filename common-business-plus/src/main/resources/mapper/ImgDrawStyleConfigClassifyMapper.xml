<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.ImgDrawStyleConfigClassifyMapper">

    <insert id="insertBatch" parameterType="com.business.db.model.po.draw.ImgDrawStyleConfigClassifyPO">
        INSERT INTO img_draw_style_config_classify (id, style_config_id, classify_id)
        VALUES
        <foreach collection="list" item="item" separator="," >
            (
            #{item.id},
            #{item.styleConfigId},
            #{item.classifyId}
            )
        </foreach>
    </insert>

    <select id="selectList" resultType="com.business.db.model.po.draw.ImgDrawStyleClassifyPO">
        SELECT img_draw_style_classify.id, img_draw_style_classify.classify_name
        FROM img_draw_style_config_classify LEFT JOIN img_draw_style_classify ON img_draw_style_config_classify.classify_id = img_draw_style_classify.id
        WHERE img_draw_style_config_classify.style_config_id = #{configId}
    </select>

    <select id="selectStyleConfigIdsByClassifyId" resultType="java.lang.Long">
        SELECT style_config_id FROM img_draw_style_config_classify WHERE classify_id = #{classifyId}
    </select>

    <select id="selectByStyleConfigIds" parameterType="java.util.List" resultType="com.business.db.model.vo.draw.ImgDrawStyleClassifyVO">
        SELECT
        img_draw_style_config_classify.style_config_id,
        img_draw_style_classify.id,
        img_draw_style_classify.classify_name,
        img_draw_style_config_classify.style_config_id AS styleConfigId
        FROM img_draw_style_config_classify
        LEFT JOIN img_draw_style_classify
        ON img_draw_style_config_classify.classify_id = img_draw_style_classify.id
        WHERE img_draw_style_config_classify.style_config_id IN
        <foreach collection="styleConfigIds" item="configId" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </select>

    <delete id="deleteByStyleConfigId" parameterType="java.lang.Long">
        DELETE FROM img_draw_style_config_classify WHERE style_config_id = #{styleConfigId}
    </delete>


    <select id="selectListByIds"  resultType="com.business.db.model.po.draw.ImgDrawStyleClassifyPO">
        select distinct classify.* from img_draw_style_classify classify INNER JOIN img_draw_style_config_classify c_classify
            on classify.id=  c_classify.classify_id
            where classify.deleted=0 and classify.is_use = 1  ORDER BY classify.sort

    </select>

    <select id="selectClassifyLabelLists"  resultType="com.business.db.model.po.draw.ImgDrawStyleClassifyLabelPO">
        SELECT
            cc.classify_id,
            classify.classify_name,
            classify.sort,
            GROUP_CONCAT(
                DISTINCT
        CONCAT(
            '{"labelId":"', cl.label_Id,
            '", "labelName":"', label.label_name,  '"}'
        )
    ) AS label_array
        FROM
            img_draw_style_config_classify cc
                INNER JOIN
            img_draw_style_config_label cl ON cc.style_config_id = cl.style_config_id
                LEFT JOIN
            img_draw_style_classify classify ON cc.classify_id = classify.id
                LEFT JOIN
            img_draw_style_label label ON cl.label_id = label.id
        GROUP BY
            cc.classify_id
        ORDER BY
            classify.sort, label.sort;
    </select>

</mapper>
