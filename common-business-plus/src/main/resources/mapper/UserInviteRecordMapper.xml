<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.UserInviteRecordMapper">

    <select id="selectCountByMonth" resultType="java.lang.Long">
        SELECT COUNT(user_invite_record.id) FROM user_invite_record
        WHERE user_invite_record.deleted = 0 AND user_invite_record.invite_user_id = #{userId}
          AND DATE_FORMAT(user_invite_record.create_time, '%Y-%m-%d' ) &gt;= #{firstDayOfMonth}
          AND DATE_FORMAT(user_invite_record.create_time, '%Y-%m-%d' ) &lt;= #{lastDayOfMonth}
    </select>

    <select id="selectCount" resultType="java.lang.Long">
        SELECT COUNT(user_invite_record.id) FROM user_invite_record
        WHERE user_invite_record.deleted = 0 AND user_invite_record.invite_user_id = #{userId}
          AND DATE_FORMAT(user_invite_record.create_time, '%Y-%m-%d' ) &gt;= #{firstDayOfMonth}
          AND DATE_FORMAT(user_invite_record.create_time, '%Y-%m-%d' ) &lt;= #{lastDayOfMonth}
    </select>

</mapper>
