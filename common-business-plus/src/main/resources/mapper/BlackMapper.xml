<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.BlackMapper">

    <select id="queryPage" resultType="com.business.db.model.vo.BlacklistVO">
        select
            id,
            user_id,
            block_user_id,
            data_version,
            deleted,
            creator,
            create_time,
            operator,
            operate_time,
            remark
        from mj_blacklist
        where deleted = 0
        order by create_time desc
    </select>

</mapper>
