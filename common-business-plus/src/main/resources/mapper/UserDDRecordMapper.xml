<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.UserDDRecordMapper">
    <!-- 查询用户点子即将到期 -->
    <select id="selectDDExpirationByDays" resultType="com.business.db.model.po.DDExpirationPO" parameterType="java.lang.Integer">
        SELECT ddrecord.userId,ddrecord.ddQuantity,ddrecord.days FROM (
                SELECT user_id AS userId, SUM(total-total_usage) AS ddQuantity, #{days} AS days
                FROM user_ddrecord
               WHERE DATE(expiration_time) = DATE(DATE_ADD(NOW(), INTERVAL #{days} DAY))
                     AND DATE(expiration_time) &gt; DATE(NOW())
               GROUP BY user_id) ddrecord WHERE ddrecord.ddQuantity > 0
    </select>


    <!-- 查询音频suno进行中的任务-->
    <select id="selectUserTotal" parameterType="java.lang.Long" resultType="java.lang.Double">
        SELECT IFNULL(SUM(total),0)-IFNULL(SUM(total_usage),0) FROM user_ddrecord
        WHERE user_id = #{userId} AND expiration_time > NOW();
    </select>
</mapper>
