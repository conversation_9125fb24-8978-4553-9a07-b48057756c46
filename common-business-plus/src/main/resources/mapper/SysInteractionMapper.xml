<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.SysInteractionMapper">

    <select id="queryPage" resultType="com.business.db.model.vo.SysInteractionVO">
        SELECT
            sys_interaction.id,
            sys_interaction.user_id,
            sys_interaction.targ_user_id,
            t_user.name,
            t_user.avatar,
            sys_interaction.task_id,
            sys_interaction.img_id,
            img_draw_detl.img_url,
            sys_interaction.is_read,
            sys_interaction.is_focus,
            sys_interaction.interact_title,
            sys_interaction.interact_type,
            sys_interaction.create_time show_time
        FROM sys_interaction LEFT JOIN t_user ON sys_interaction.targ_user_id=t_user.id
                             LEFT JOIN img_draw_detl ON sys_interaction.img_id=img_draw_detl.id
        WHERE sys_interaction.deleted = 0 AND sys_interaction.user_id = #{dto.userId}
        ORDER BY sys_interaction.create_time DESC
    </select>

    <update id="updateRead">
        UPDATE sys_interaction SET is_read = 1 WHERE deleted = 0
    </update>

</mapper>
