<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.UserAwardSvipRecordMapper">

   <select id="selectState" resultType="java.lang.Integer">
        SELECT user_award_svip_record.state FROM user_award_svip_record
        WHERE user_award_svip_record.deleted = 0 AND user_award_svip_record.user_id = #{userId}
          AND DATE_FORMAT(user_award_svip_record.create_time, '%Y-%m-%d' ) &gt;= #{firstDayOfMonth}
          AND DATE_FORMAT(user_award_svip_record.create_time, '%Y-%m-%d' ) &lt;= #{lastDayOfMonth}
    </select>

</mapper>
