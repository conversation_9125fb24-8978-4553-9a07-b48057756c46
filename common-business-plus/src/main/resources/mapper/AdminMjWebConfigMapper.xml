<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.AdminMjWebConfigMapper">

    <select id="selectAdminMjWebConfigBOList" resultType="com.business.model.bo.AdminMjWebConfigBO">
        SELECT
            id,
            account_name,
            account_speed,
            channel_id,
            cookie,
            priority_level
        FROM admin_mjweb_config
        WHERE deleted = 0 AND is_enable = 1 AND status = 1
        ORDER BY create_time ASC
    </select>

    <select id="selectAdminMjWebConfigBO" resultType="com.business.model.bo.AdminMjWebConfigBO">
        SELECT
            id,
            account_name,
            account_speed,
            channel_id,
            cookie,
            priority_level
        FROM admin_mjweb_config
        WHERE id = #{id}
    </select>

    <select id="selectAdminMjWebConfigBORand" resultType="com.business.model.bo.AdminMjWebConfigBO">
        SELECT
            id,
            account_name,
            account_speed,
            channel_id,
            cookie,
            priority_level
        FROM admin_mjweb_config
        WHERE deleted = 0 AND is_enable = 1 AND status = 1
        ORDER BY create_time LIMIT 1
    </select>

</mapper>
