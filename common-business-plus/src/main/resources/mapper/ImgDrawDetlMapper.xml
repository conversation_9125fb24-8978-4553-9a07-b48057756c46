<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.ImgDrawDetlMapper">

    <update id="updateByIds">
        update img_draw_detl set status = 4 where id in (
        <foreach collection="list" item="item" separator="," >
            #{item}
        </foreach>
        )
    </update>

    <update id="updateBatchById">
        UPDATE img_draw_detl
        <set>
            img_url = CASE
            <foreach collection="list" item="item" index="index" separator=" ">
                WHEN id = #{item.id} THEN #{item.imgUrl}
            </foreach>
            END,
            img_width = CASE
            <foreach collection="list" item="item" index="index" separator=" ">
                WHEN id = #{item.id} THEN #{item.imgWidth}
            </foreach>
            END,
            img_height = CASE
            <foreach collection="list" item="item" index="index" separator=" ">
                WHEN id = #{item.id} THEN #{item.imgHeight}
            </foreach>
            END,
            img_size = CASE
            <foreach collection="list" item="item" index="index" separator=" ">
                WHEN id = #{item.id} THEN #{item.imgSize}
            </foreach>
            END,
            img_type = CASE
            <foreach collection="list" item="item" index="index" separator=" ">
                WHEN id = #{item.id} THEN #{item.imgType}
            </foreach>
            END
        </set>
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="selectPhotoTaskOne" resultType="com.business.db.model.vo.PhotoHistoryVO">
        SELECT
            img_draw_record.id,
            img_draw_record.opt_attribute AS action,
            img_draw_record.prompt_init AS prompt,
            img_draw_record.status,
            img_draw_detl.img_type,
            img_draw_detl.img_index,
            img_draw_detl.img_size,
            img_draw_detl.img_width,
            img_draw_detl.img_height,
            img_draw_detl.img_url AS imgUrl,
            img_draw_detl.create_time,
            photo_topic_scene.id AS scene_id,
            photo_topic_scene.scene_url,
            photo_topic.id AS topic_id,
            photo_topic.topic_url,
            photo_topic.topic_title
        FROM img_draw_record
            LEFT JOIN img_draw_detl ON img_draw_record.id = img_draw_detl.draw_record_id
            LEFT JOIN photo_topic_scene ON img_draw_record.photo_scene_id = photo_topic_scene.id
            LEFT JOIN photo_topic ON photo_topic_scene.topic_id = photo_topic.id
        WHERE img_draw_record.id = #{imgDrawId} AND img_draw_record.fun_type = ${@com.nacos.enums.ImgDrawEnum@FUN_TYPE_PORTRAIT.getValue()}
        ORDER BY img_draw_detl.create_time DESC
    </select>


    <!-- app 2.0 我的画廊 关联查询 -->
    <select id="queryGalleryPage" resultType="com.business.db.model.vo.ImgGalleryVO">
        SELECT
            img_draw_record.id,
            img_draw_detl.id AS imgId,
            img_draw_record.opt_attribute,
            img_draw_record.mode_attribute,
            img_draw_record.prompt_init AS prompt,
            img_draw_record.prompt_use AS promptUse,
            img_draw_detl.img_source_url AS imgSourceUrl,
            img_draw_detl.img_url AS imageUrl,
            img_draw_record.width AS width,
            img_draw_record.height AS height,
            img_draw_detl.img_size size,
            img_draw_detl.img_hue imageAve,
            img_draw_detl.img_type contentType,
            t_user.id AS painterId,
            t_user.`name` AS painterName,
            t_user.avatar AS painterAvatar,
            t_user.personal_profile AS painterProfile,
            img_draw_detl.good_qua goodNum,
            img_draw_detl.collect_qua collectNum,
            img_draw_detl.is_publish AS isPublish,
            fans.fansNum,
            works.worksNum,
            CASE
                WHEN focus.focus_user_id IS NULL THEN
                    0 ELSE 1
                END AS focusFlag,
            CASE
                WHEN t_task_img_collect.id IS NULL THEN
                    0 ELSE 1
                END AS collect_state,
            CASE
                WHEN t_task_img_like.id IS NULL THEN
                    0 ELSE 1
                END AS like_state,
            img_draw_record.create_time produce_time
        FROM img_draw_record img_draw_record
                 LEFT JOIN img_draw_detl img_draw_detl ON img_draw_record.id = img_draw_detl.draw_record_id AND img_draw_detl.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
                 LEFT JOIN t_task_img_like t_task_img_like ON t_task_img_like.img_id = img_draw_detl.id
            AND t_task_img_like.task_id = img_draw_detl.draw_record_id
            AND t_task_img_like.deleted = 0
            AND t_task_img_like.user_id = #{dto.userId}
                 LEFT JOIN t_task_img_collect t_task_img_collect ON t_task_img_collect.img_id = img_draw_detl.id
            AND t_task_img_collect.task_id = img_draw_detl.draw_record_id
            AND t_task_img_collect.deleted = 0
            AND t_task_img_collect.user_id = #{dto.userId}
                 LEFT JOIN t_user ON t_user.id = img_draw_record.user_id
                 LEFT JOIN ( SELECT COUNT( 1 ) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id ) fans ON t_user.id = fans.focus_user_id
                 LEFT JOIN ( SELECT COUNT( 1 ) AS worksNum, user_id FROM img_draw_record WHERE deleted = 0 AND STATUS = 5 AND fun_type = 1 GROUP BY user_id ) works ON t_user.id = works.user_id
                 LEFT JOIN t_user_focus focus ON img_draw_record.user_id = focus.focus_user_id
            AND focus.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
            AND focus.user_id = #{dto.userId}
        WHERE img_draw_record.user_id = #{dto.painterId}
        AND img_draw_record.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
        AND img_draw_record.STATUS = ${@com.nacos.enums.ImgDrawEnum@STATUS_FINISH_SUCCESS.getValue()}
        AND img_draw_record.fun_type = ${@com.nacos.enums.ImgDrawEnum@FUN_TYPE_DRAW.getValue()}
        <if test = "dto.isHimself != null and dto.isHimself == 0" >
            AND img_draw_detl.is_publish = ${@com.nacos.enums.CommonIntEnum@SHOW_TRUE.getIntValue()}
        </if>
        AND img_draw_record.width IS NOT NULL AND img_draw_detl.img_url NOT LIKE '%midjourney%'
        ORDER BY img_draw_record.id DESC
    </select>

    <!-- app 2.0 我的画廊 查询单条记录 -->
    <select id="queryGalleryByImgId" resultType="com.business.db.model.vo.ImgCommunityVO">
        SELECT
            img_draw_detl.id imgId,
            img_draw_detl.img_url AS imageUrl,
            img_draw_detl.good_qua goodNum,
            img_draw_detl.collect_qua collectNum,
            img_draw_detl.img_width width,
            img_draw_detl.img_height height,
            img_draw_detl.img_size AS size,
             img_draw_detl.img_hue imageAve,
             img_draw_detl.img_type contentType,
             img_draw_detl.create_time produceTime,
             img_draw_record.id,
             img_draw_record.init_img_object,
             img_draw_record.opt_attribute AS action,
             img_draw_record.prompt_init prompt,
             img_draw_record.prompt_use promptUse,
             t_user.id AS painterId,
             t_user.avatar AS painterAvatar,
             t_user.`name` AS painterName,
             t_user.personal_profile painterProfile,
             CASE WHEN fans.fansNum IS NULL THEN 0 ELSE fans.fansNum END AS fansNum,
             CASE WHEN works.worksNum IS NULL THEN 0 ELSE works.worksNum END AS worksNum,
             CASE WHEN t_task_img_collect.id IS NULL THEN 0 ELSE 1 END collectState,
             CASE WHEN t_task_img_like.id IS NULL THEN 0 ELSE 1 END likeState,
             CASE WHEN focus.focus_user_id IS NULL THEN 0 ELSE 1 END AS focusFlag
         FROM t_user
          INNER JOIN img_draw_record ON img_draw_record.user_id = t_user.id
          LEFT JOIN img_draw_detl img_draw_detl ON img_draw_detl.draw_record_id = img_draw_record.id AND img_draw_detl.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
          LEFT JOIN t_task_img_like ON t_task_img_like.img_id = img_draw_detl.id AND t_task_img_like.task_id = img_draw_detl.draw_record_id
                                                                 AND t_task_img_like.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
                                                                 AND t_task_img_like.user_id = #{dto.userId}
          LEFT JOIN t_task_img_collect ON t_task_img_collect.img_id = img_draw_detl.id AND t_task_img_collect.task_id = img_draw_detl.draw_record_id
                                                                        AND t_task_img_collect.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
                                                                        AND t_task_img_collect.user_id = #{dto.userId}
          LEFT JOIN (SELECT COUNT(1) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
                                                            GROUP BY focus_user_id) fans ON t_user.id = fans.focus_user_id
          LEFT JOIN (SELECT COUNT( 1 ) AS worksNum, user_id FROM img_draw_record WHERE deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
                                        AND status = ${@com.nacos.enums.ImgDrawEnum@STATUS_FINISH_SUCCESS.getValue()}
                                        AND fun_type = ${@com.nacos.enums.ImgDrawEnum@FUN_TYPE_DRAW.getValue()} GROUP BY user_id ) works ON t_user.id = works.user_id
          LEFT JOIN t_user_focus focus ON t_user.id = focus.focus_user_id AND focus.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()} AND focus.user_id = #{dto.userId}
         WHERE img_draw_record.`status` = ${@com.nacos.enums.ImgDrawEnum@STATUS_FINISH_SUCCESS.getValue()} AND img_draw_detl.id = #{dto.imgId}
    </select>

    <!-- 旧的精选查询 -->
    <select id="dailyPicksPage2" resultType="com.business.db.model.vo.DailyPicksVO">
        SELECT
             img_draw_detl.id imgId,
             img_draw_detl.img_url AS imageUrl,
             img_draw_detl.good_qua goodNum,
             img_draw_detl.collect_qua collectNum,
             img_draw_detl.img_width width,
             img_draw_detl.img_height height,
             img_draw_detl.img_size AS size,
             img_draw_detl.img_hue imageAve,
             img_draw_detl.img_type contentType,
             img_draw_detl.create_time produceTime,
             img_draw_record.id,
             img_draw_record.init_img_object,
             img_draw_record.opt_attribute AS action,
             img_draw_record.prompt_init prompt,
             img_draw_record.prompt_use promptUse,
             CASE WHEN t_task_img_collect.id IS NULL THEN 0 ELSE 1 END collectState
         FROM img_draw_record
         LEFT JOIN img_draw_detl img_draw_detl ON img_draw_detl.draw_record_id = img_draw_record.id AND img_draw_detl.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
         LEFT JOIN t_task_img_collect ON t_task_img_collect.img_id = img_draw_detl.id AND t_task_img_collect.task_id = img_draw_detl.draw_record_id
                                                                                      AND t_task_img_collect.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
                                                                                      AND t_task_img_collect.user_id = #{dto.loginUserId}
        WHERE img_draw_record.`status` = ${@com.nacos.enums.ImgDrawEnum@STATUS_FINISH_SUCCESS.getValue()} AND img_draw_record.user_id = #{dto.userId}
        GROUP BY img_draw_detl.draw_record_id
        ORDER BY img_draw_detl.create_time DESC
    </select>

    <select id="dailyPicksPage" resultType="com.business.db.model.vo.DailyPicksVO">
        SELECT
            img_draw_detl.id imgId,
            img_draw_detl.img_url AS imageUrl,
            img_draw_detl.good_qua goodNum,
            img_draw_detl.collect_qua collectNum,
            img_draw_detl.img_width width,
            img_draw_detl.img_height height,
            img_draw_detl.img_size AS size,
             img_draw_detl.img_hue imageAve,
             img_draw_detl.img_type contentType,
             img_draw_detl.create_time produceTime,
             img_draw_record.id,
             img_draw_record.init_img_object,
             img_draw_record.opt_attribute AS action,
             img_draw_record.prompt_init prompt,
             img_draw_record.prompt_use promptUse,
             CASE WHEN t_task_img_collect.id IS NULL THEN 0 ELSE 1 END collectState
        FROM img_daily_picks
        LEFT JOIN img_draw_record ON img_draw_record.id = img_daily_picks.draw_record_id AND img_daily_picks.picks_type = #{dto.picksType}
        LEFT JOIN img_draw_detl img_draw_detl ON img_draw_detl.draw_record_id = img_draw_record.id AND img_draw_detl.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
        LEFT JOIN t_task_img_collect ON t_task_img_collect.img_id = img_draw_detl.id AND t_task_img_collect.task_id = img_draw_detl.draw_record_id
        AND t_task_img_collect.deleted = ${@com.nacos.enums.CommonIntEnum@DELETED_FALSE.getIntValue()}
        AND t_task_img_collect.user_id = #{dto.loginUserId}
        WHERE img_draw_record.`status` = ${@com.nacos.enums.ImgDrawEnum@STATUS_FINISH_SUCCESS.getValue()} AND img_draw_record.user_id = #{dto.userId}
        GROUP BY img_draw_detl.draw_record_id
        ORDER BY img_draw_detl.create_time DESC
    </select>

    <!-- 查询音频suno进行中的任务-->
    <select id="selectAudioOngoingStatus" resultType="com.business.model.bo.AudioOngoingStatusBO">
        SELECT
            idd.id,
            idd.user_id AS userId,
            idd.draw_record_id AS drawRecordId,
            idd.suno_job_id AS sunoJobId,
            idr.`status` ,
            idr.suno_account_id AS sunoAccountId,
            idr.create_time AS submitTime
        FROM
            img_draw_record idr
                LEFT JOIN img_draw_detl idd ON idr.id = idd.draw_record_id
        WHERE
            fun_type = 4
          AND STATUS = 4
          AND img_source_url IS NULL
            LIMIT 100
    </select>

    <select id="selectByDrawDetlId" resultType="com.business.db.model.bo.ImgDrawReportBO">
        SELECT
            img_draw_record.prompt_init,
            img_draw_detl.user_id,
            img_draw_detl.img_url
        FROM img_draw_detl
        LEFT JOIN img_draw_record ON img_draw_detl.draw_record_id = img_draw_record.id
        WHERE img_draw_detl.id = #{imgDrawId}
    </select>

</mapper>
