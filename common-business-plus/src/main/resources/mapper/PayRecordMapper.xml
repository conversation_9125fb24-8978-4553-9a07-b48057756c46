<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.PayRecordMapper">

    <select id="queryUserGrade" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT vc.grade FROM pay_record pr
        LEFT JOIN vip_config vc ON vc.id = pr.vip_config_id
        WHERE pr.user_id = #{userId} AND pr.expiration_time > CURRENT_TIMESTAMP
        AND pr.state = ${@com.nacos.enums.PayRecordEnum@STATE_PAY_SUCCESS.getIntValue()}
        ORDER BY vc.grade DESC
        LIMIT 1
    </select>

    <select id="queryExpirationTime" resultType="com.business.db.model.bo.PayRecordBO">
        SELECT t_user.`name`, t_user.mobile, pay_record.* FROM pay_record LEFT JOIN t_user ON t_user.id = pay_record.user_id
        WHERE state = ${@com.nacos.enums.PayRecordEnum@STATE_PAY_SUCCESS.getIntValue()}
        AND pay_record.expiration_time LIKE CONCAT('%', #{timeFiveBeforeExpiration}, '%') OR pay_record.expiration_time LIKE CONCAT('%', #{timeOneBeforeExpiration}, '%')
        GROUP BY pay_record.user_id
        ORDER BY pay_record.expiration_time DESC

    </select>

    <select id="selectGiveSvipCount" resultType="java.lang.Long">
        SELECT COUNT(pay_record.id) FROM pay_record
        WHERE pay_record.deleted = 0 AND pay_record.user_id = #{userId}
          AND pay_record.state = ${@com.nacos.enums.PayRecordEnum@STATE_PAY_SUCCESS.getIntValue()}
          AND pay_record.type = ${@com.nacos.enums.PayRecordEnum@TYPE_ITEM_PAY_ACTIVITY_SVIP.getIntValue()}
          AND DATE_FORMAT(pay_record.create_time, '%Y-%m-%d' ) &gt;= #{firstDayOfMonth}
          AND DATE_FORMAT(pay_record.create_time, '%Y-%m-%d' ) &lt;= #{lastDayOfMonth}
    </select>

</mapper>
