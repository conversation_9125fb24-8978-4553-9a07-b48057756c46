<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.PhotoTopicMapper">

    <resultMap id="TopicResultMap" type="com.business.db.model.vo.PhotoTopicVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <collection property="topicSceneVOList" column="{topicId = id}"  select="selectByTopicId"/>
    </resultMap>

    <select id="queryPage" resultType="com.business.db.model.vo.PhotoTopicVO">
        SELECT
            photo_topic.id,
            photo_topic.topic_type_id,
            photo_topic.topic_title,
            photo_topic.topic_title_en,
            photo_topic.topic_url,
            photo_topic.is_hot,
            photo_topic.status,
            photo_topic.sort,
            photo_topic.img_type,
            photo_topic.img_hue,
            photo_topic.img_width,
            photo_topic.img_height,
            photo_topic.img_size,
            photo_topic.create_time
        FROM photo_topic
        WHERE photo_topic.deleted = 0
        <if test="dto.topicTypeId != null">
            AND photo_topic.topic_type_id = #{dto.topicTypeId}
        </if>
        <if test="dto.topicTitle != null">
            AND photo_topic.topic_title LIKE CONCAT('%', #{dto.topicTitle}, '%')
        </if>
        <if test="dto.status != null">
            AND photo_topic.status = #{dto.status}
        </if>
        ORDER BY photo_topic.sort ASC

    </select>

    <select id="queryPhotoTopicPage" resultMap="TopicResultMap">
        SELECT
            photo_topic.id,
            photo_topic.topic_type_id,
            CASE
                WHEN #{dto.languageTagId} = 1 THEN topic_title
                WHEN #{dto.languageTagId} = 2 THEN topic_title_en
                ELSE NULL
            END AS topic_title,
            photo_topic.topic_url,
            photo_topic.img_type,
            photo_topic.img_width,
            photo_topic.img_height,
            photo_topic.img_size,
            photo_topic.img_hue,
            photo_topic.create_time
        FROM photo_topic
        WHERE photo_topic.deleted = 0
        <if test="dto.topicTypeId != null">
            AND photo_topic.topic_type_id = #{dto.topicTypeId}
        </if>
        <if test="dto.id != null">
            AND photo_topic.id = #{dto.id}
        </if>
        ORDER BY photo_topic.id,photo_topic.create_time DESC

    </select>

    <select id="selectByTopicId" resultType="com.business.db.model.vo.PhotoTopicSceneVO">
        SELECT
            photo_topic_scene.id,
            photo_topic_scene.topic_id,
            photo_topic_scene.scene_url,
            photo_topic_scene.img_type,
            photo_topic_scene.img_width,
            photo_topic_scene.img_height,
            photo_topic_scene.img_size,
            photo_topic_scene.img_hue,
            photo_topic_scene.create_time
        FROM photo_topic_scene
        WHERE photo_topic_scene.topic_id = #{topicId} AND photo_topic_scene.deleted = 0
        ORDER BY photo_topic_scene.create_time DESC
    </select>

</mapper>
