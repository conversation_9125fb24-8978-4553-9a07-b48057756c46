<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.SysNotificationMapper">

    <!-- <select id="queryPage" resultType="com.business.db.model.vo.SysNotificationVO">
       SELECT * FROM (
             SELECT
                 sys_notification.id,
                 sys_notification.user_id,
                 sys_notification.targ_user_id,
                 sys_notification.notif_type,
                 sys_notification.notif_title,
                 sys_notification.notif_content,
                 sys_notification.is_popup,
                 sys_notification.task_id,
                 sys_notification.create_time AS show_time,
                 COUNT(sys_notification_user.id) isRead
             FROM sys_notification LEFT JOIN sys_notification_user ON sys_notification.id = sys_notification_user.notificat_id AND sys_notification_user.user_id = #{dto.userId}
             WHERE sys_notification.deleted = 0 AND sys_notification.is_send = 1 AND sys_notification.user_id=#{dto.userId} AND sys_notification.notif_type != 0  GROUP BY sys_notification.id
             UNION ALL
             SELECT
                 sys_notification.id,
                 sys_notification.user_id,
                 sys_notification.targ_user_id,
                 sys_notification.notif_type,
                 sys_notification.notif_title,
                 sys_notification.notif_content,
                 sys_notification.is_popup,
                 sys_notification.task_id,
                 sys_notification.create_time AS show_time,
                 COUNT(sys_notification_user.id) isRead
             FROM sys_notification LEFT JOIN sys_notification_user ON sys_notification.id = sys_notification_user.notificat_id AND sys_notification_user.user_id = #{dto.userId}
             WHERE sys_notification.deleted = 0 AND sys_notification.is_send = 1 AND sys_notification.notif_type = 0  GROUP BY sys_notification.id
       ) t ORDER BY t.show_time DESC
    </select> -->

    <select id="queryPage" resultType="com.business.db.model.vo.SysNotificationVO">
        SELECT
          sys_notification.id,
          sys_notification.user_id,
          sys_notification.targ_user_id,
          sys_notification.notif_type,
          sys_notification.notif_title,
          sys_notification.notif_content,
          sys_notification.is_popup,
          sys_notification.task_id,
          sys_notification.video_url,
          sys_notification.create_time AS show_time,
          COUNT(sys_notification_user.id) isRead
        FROM sys_notification LEFT JOIN sys_notification_user ON sys_notification.id = sys_notification_user.notificat_id AND sys_notification_user.user_id = #{dto.userId}
        WHERE sys_notification.deleted = 0
        AND sys_notification.is_send = 1
        AND (sys_notification.notif_type = 0 OR (sys_notification.notif_type NOT IN (0, 201) AND sys_notification.user_id = #{dto.userId}))
        GROUP BY sys_notification.id ORDER BY sys_notification.create_time DESC
    </select>

    <select id="queryActivityPage" resultType="com.business.db.model.vo.SysActivityNotificationVO">
        SELECT
            sys_notification.id,
            sys_notification.notif_type,
            sys_notification.notif_title,
            sys_notification.notif_content,
            sys_notification.image_url,
            sys_notification.link_addre,
            sys_notification.is_popup,
            COUNT(sys_notification_user.id) isRead,
            sys_notification.create_time AS show_time
        FROM sys_notification LEFT JOIN sys_notification_user ON sys_notification.id = sys_notification_user.notificat_id
                                                                     AND sys_notification_user.user_id = #{dto.userId}
        WHERE sys_notification.deleted = 0
          AND sys_notification.is_send = 1
          AND sys_notification.notif_type = 201
        GROUP BY sys_notification.id ORDER BY sys_notification.create_time DESC
    </select>

    <update id="updateRead">
        UPDATE sys_notification SET is_read = 1 WHERE deleted = 0
    </update>

    <select id="selectUnreadNotification" resultType="java.lang.Long">
        SELECT COUNT(*) FROM (
             SELECT sys_notification.id FROM sys_notification WHERE sys_notification.deleted = 0 AND sys_notification.notif_type=0 AND sys_notification.id NOT IN (SELECT sys_notification_user.notificat_id FROM sys_notification_user WHERE sys_notification_user.user_id = #{userId})
             UNION
             SELECT sys_notification.id FROM sys_notification WHERE sys_notification.deleted = 0 AND sys_notification.notif_type!=0 AND sys_notification.user_id=#{userId} AND sys_notification.id NOT IN (SELECT sys_notification_user.notificat_id FROM sys_notification_user WHERE sys_notification_user.user_id = #{userId})
        ) t
    </select>

    <select id="selectUnreadNotificationList" resultType="com.business.db.model.po.SysNotificationPO">
        SELECT
            sys_notification.id,
            sys_notification.user_id,
            sys_notification.targ_user_id,
            sys_notification.notif_type,
            sys_notification.notif_title,
            sys_notification.notif_content,
            sys_notification.is_popup,
            sys_notification.task_id,
            sys_notification.create_time
        FROM sys_notification
        WHERE sys_notification.deleted = 0 AND sys_notification.id NOT IN (SELECT sys_notification_user.notificat_id FROM sys_notification_user WHERE sys_notification_user.user_id = #{userId})
        <if test="messageId != null">
            AND sys_notification.id = #{messageId}
        </if>
    </select>


</mapper>
