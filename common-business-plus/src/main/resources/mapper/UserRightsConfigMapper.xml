<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.UserRightsConfigMapper">

    <select id="selectUserRightsConfigList" resultType="com.business.db.model.vo.UserRightsConfigVO">
        SELECT
            urc.id,
            urc.rights_name,
            urc.not_package,
            urc.not_package_value,
            urc.basic_package,
            urc.basic_package_value,
            urc.series_package,
            urc.series_package_value,
            urc.profe_package,
            urc.profe_package_value,
            urc.high_package,
            urc.high_package_value,
            urc.long_package,
            urc.long_package_value
        FROM
            user_rights_config urc
        WHERE
            urc.deleted = 0
        ORDER BY
            urc.rights_sort ASC
    </select>

</mapper>
