<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.MjTaskMapper">
    <select id="queryPhotoHistoryPage" resultType="com.business.db.model.vo.PhotoHistoryVO">
        SELECT
        img_draw_record.id,
        img_draw_record.opt_attribute AS action,
        img_draw_record.prompt_init AS prompt,
        img_draw_record.status,
        img_draw_detl.img_type,
        img_draw_detl.img_index,
        img_draw_detl.img_size,
        img_draw_detl.img_width,
        img_draw_detl.img_height,
        img_draw_detl.img_url AS imgUrl,
        img_draw_detl.create_time,
        photo_topic_scene.id AS scene_id,
        photo_topic_scene.scene_url,
        photo_topic.id AS topic_id,
        photo_topic.topic_url,
        photo_topic.topic_title
        FROM img_draw_record
        LEFT JOIN img_draw_detl ON img_draw_record.id = img_draw_detl.draw_record_id
        LEFT JOIN photo_topic_scene ON img_draw_record.photo_scene_id = photo_topic_scene.id
        LEFT JOIN photo_topic ON photo_topic_scene.topic_id = photo_topic.id
        <where>
            <if test="dto.id != null">
                AND img_draw_record.id = #{dto.id}
            </if>
            <if test="dto.userId != null">
                AND img_draw_record.user_id = #{dto.userId}
            </if>
            AND img_draw_record.status != ${@com.nacos.enums.ImgDrawEnum@STATUS_FINISH_FAIL.getValue()}
            AND img_draw_record.fun_type = ${@com.nacos.enums.ImgDrawEnum@FUN_TYPE_PORTRAIT.getValue()} and img_draw_record.deleted=0
        </where>
        ORDER BY img_draw_record.create_time DESC
    </select>

    <select id="selectPhotoTaskOne" resultType="com.business.db.model.vo.PhotoHistoryVO">
        SELECT
        mj_task_new.id,
        mj_task_new.action,
        mj_task_new.prompt_init AS prompt,
        mj_task_new.status,
        mj_task_new.img_type,
        mj_task_new.img_index,
        mj_task_new.img_size,
        mj_task_new.img_width,
        mj_task_new.img_height,
        mj_task_new.main_img_url AS imgUrl,
        mj_task_new.create_time,
        photo_topic_scene.id AS scene_id,
        photo_topic_scene.scene_url,
        photo_topic.id AS topic_id,
        photo_topic.topic_url,
        photo_topic.topic_title
        FROM mj_task_new
        LEFT JOIN photo_topic_scene ON mj_task_new.message_id = photo_topic_scene.id
        LEFT JOIN photo_topic ON photo_topic_scene.topic_id = photo_topic.id
        WHERE mj_task_new.id = #{taskId} AND mj_task_new.action = ${@<EMAIL>()}
        ORDER BY mj_task_new.create_time DESC
    </select>

</mapper>
