<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.PhotoTopicTypeMapper">

        <select id="queryPage" resultType="com.business.db.model.vo.PhotoTopicTypeVO">
                SELECT
                        id,
                        language_tag_id,
                        parent_id,
                        type_name,
                        status,
                        sort,
                        create_time
                FROM photo_topic_type
                WHERE deleted = 0 AND parent_id != 0
                ORDER BY sort ASC
        </select>

        <select id="selectPhotoTopicTypes" resultType="com.business.db.model.vo.PhotoTopicTypeVO">
                SELECT
                        id,
                        language_tag_id,
                        parent_id,
                        type_name,
                        status,
                        sort,
                        create_time
                FROM photo_topic_type
                WHERE  language_tag_id = #{languageTagId} AND parent_id = #{parentId} AND deleted = 0
                ORDER BY id,create_time DESC
        </select>

</mapper>
