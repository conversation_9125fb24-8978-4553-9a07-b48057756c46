<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.DrawGalleryMapper">
    <select id="queryPage" resultType="com.business.db.model.vo.GalleryQueryVO">
        SELECT * FROM (
        SELECT * FROM (SELECT
            tti.id imgId,
            CASE
                WHEN SUBSTRING(tti.img_url, 1, 4) = 'http' THEN tti.img_url ELSE CONCAT(#{dto.prefixUrl}, tti.img_url)
            END AS imageUrl,
            tti.good_qua goodNum,
            tti.collect_qua collectNum,
            tti.img_width width,
            tti.img_height height,
            tti.img_size size,
            tti.img_hue imageAve,
            tti.img_type contentType,
            tti.create_time produceTime,
            mt.id,
            mt.action,
            mt.prompt_init,
            mt.prompt_use prompt,
            tu.id AS painterId,
            tu.avatar AS painterAvatar,
            tu.`name` AS painterName,
            tu.personal_profile painterProfile,
            CASE WHEN fans.fansNum IS NULL THEN 0 ELSE fans.fansNum END AS fansNum,
            CASE WHEN works.worksNum IS NULL THEN 0 ELSE works.worksNum END AS worksNum,
            CASE WHEN ttic.id IS NULL THEN 0 ELSE 1 END collectState,
            CASE WHEN ttil.id IS NULL THEN 0 ELSE 1 END likeState,
            CASE WHEN focus.focus_user_id IS NULL THEN 0 ELSE 1 END AS focusFlag
        FROM t_user tu
            INNER JOIN mj_task_new mt ON mt.user_id = tu.id AND mt.is_publish = 1
            INNER JOIN mj_task_detl tti ON tti.task_id = mt.id AND tti.deleted = 0
            LEFT JOIN t_task_img_like ttil ON ttil.img_id = tti.id AND ttil.task_id = tti.task_id AND ttil.deleted = 0 AND ttil.user_id = #{dto.userId}
            LEFT JOIN t_task_img_collect ttic ON ttic.img_id = tti.id AND ttic.task_id = tti.task_id AND ttic.deleted = 0 AND ttic.user_id = #{dto.userId}
            LEFT JOIN (SELECT COUNT(1) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id) fans ON tu.id = fans.focus_user_id
            LEFT JOIN (SELECT COUNT(1) AS worksNum, user_id FROM mj_task_new WHERE deleted = 0 AND status=5 AND action not in (12, 13) GROUP BY user_id) works ON tu.id = works.user_id
            LEFT JOIN t_user_focus focus ON tu.id = focus.focus_user_id AND focus.deleted = 0 AND focus.user_id = #{dto.userId}
            WHERE tu.id = mt.user_id AND mt.action in (2,22) AND mt.`status` = 5 AND tti.img_width IS NOT NULL ORDER BY produceTime DESC limit 100000) gt GROUP BY gt.prompt) t
        WHERE t.painterId NOT IN (SELECT block_user_id FROM mj_blacklist WHERE user_id = #{dto.userId} AND deleted = 0)
        <if test="dto.prompt != null and dto.prompt != ''">
            AND (t.prompt_init LIKE CONCAT('%',#{dto.prompt},'%') OR t.prompt LIKE CONCAT('%',#{dto.prompt},'%'))
        </if>
        <if test="dto.painterId != null">
            AND t.painterId = #{dto.painterId}
        </if>
        ORDER BY RAND()
    </select>
    <select id="queryGalleryByImgId" resultType="com.business.db.model.vo.GalleryQueryVO">
        SELECT * FROM (
            SELECT * FROM (SELECT
                tti.id imgId,
                CASE
                    WHEN SUBSTRING(tti.img_url, 1, 4) = 'http' THEN tti.img_url ELSE CONCAT(#{dto.prefixUrl}, tti.img_url)
                END AS imageUrl,
                tti.good_qua goodNum,
                tti.collect_qua collectNum,
                tti.img_width width,
                tti.img_height height,
                tti.img_size AS size,
                tti.img_hue imageAve,
                tti.img_type contentType,
                tti.create_time produceTime,
                mt.id,
                mt.opt_attribute AS action,
                mt.prompt_use prompt,
                tu.id AS painterId,
                tu.avatar AS painterAvatar,
                tu.`name` AS painterName,
                tu.personal_profile painterProfile,
                CASE WHEN fans.fansNum IS NULL THEN 0 ELSE fans.fansNum END AS fansNum,
                CASE WHEN works.worksNum IS NULL THEN 0 ELSE works.worksNum END AS worksNum,
                CASE WHEN ttic.id IS NULL THEN 0 ELSE 1 END collectState,
                CASE WHEN ttil.id IS NULL THEN 0 ELSE 1 END likeState,
                CASE WHEN focus.focus_user_id IS NULL THEN 0 ELSE 1 END AS focusFlag
            FROM t_user tu
                INNER JOIN img_draw_record mt ON mt.user_id = tu.id
                INNER JOIN img_draw_detl tti ON tti.draw_record_id = mt.id AND tti.deleted = 0
                LEFT JOIN t_task_img_like ttil ON ttil.img_id = tti.id AND ttil.task_id = tti.draw_record_id AND ttil.deleted = 0 AND ttil.user_id = #{dto.userId}
                LEFT JOIN t_task_img_collect ttic ON ttic.img_id = tti.id AND ttic.task_id = tti.draw_record_id AND ttic.deleted = 0 AND ttic.user_id = #{dto.userId}
                LEFT JOIN (SELECT COUNT(1) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id) fans ON tu.id = fans.focus_user_id
                LEFT JOIN (SELECT COUNT(1) AS worksNum, user_id FROM img_draw_detl WHERE deleted = 0 GROUP BY user_id) works ON tu.id = works.user_id
                LEFT JOIN t_user_focus focus ON tu.id = focus.focus_user_id AND focus.deleted = 0 AND focus.user_id = #{dto.userId}
        WHERE tu.id = mt.user_id AND mt.`status` = 1 AND tti.img_width IS NOT NULL ORDER BY produceTime DESC limit 100000) gt GROUP BY gt.prompt) t WHERE t.imgId = #{dto.imgId}
    </select>
    <select id="queryMyCollectPage" resultType="com.business.db.model.vo.GalleryQueryVO">
        SELECT
            tti.id AS imgId,
            CASE
                WHEN SUBSTRING(tti.img_url, 1, 4) = 'http' THEN tti.img_url ELSE CONCAT(#{dto.prefixUrl}, tti.img_url)
            END AS image_url,
            tti.create_time AS produceTime,
            tti.img_width width,
            tti.img_height height,
            tti.img_size size,
            tti.img_hue image_ave,
            tti.img_type content_type,
            mt.id,
            mt.action,
            tti.good_qua good_num,
            tti.collect_qua collect_num,
            1 AS collectState,
            CASE WHEN ttil.id IS NULL THEN 0 ELSE 1 END AS likeState,
            mt.prompt_use AS prompt,
            tu.id AS painterId,
            tu.avatar AS painterAvatar,
            tu.`name` AS painterName,
            tu.personal_profile AS painterProfile,
            CASE WHEN fans.fansNum IS NULL THEN 0 ELSE fans.fansNum END AS fansNum,
            CASE WHEN works.worksNum IS NULL THEN 0 ELSE works.worksNum END AS worksNum,
            CASE WHEN focus.focus_user_id IS NULL THEN 0 ELSE 1 END AS focusFlag
        FROM
            t_task_img_collect ttic
        LEFT JOIN mj_task_detl tti ON tti.id = ttic.img_id
        LEFT JOIN t_task_img_like ttil ON ttil.img_id = ttic.img_id AND ttil.deleted = 0 AND ttil.user_id = #{dto.userId}
        LEFT JOIN mj_task_new mt ON mt.id = ttic.task_id
        LEFT JOIN t_user tu ON tu.id = mt.user_id
        LEFT JOIN (SELECT COUNT(1) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id) fans ON tu.id = fans.focus_user_id
        LEFT JOIN (SELECT COUNT(1) AS worksNum, user_id FROM mj_task_new WHERE deleted = 0 AND status=5 AND action not in (12, 13) GROUP BY user_id) works ON tu.id = works.user_id
        LEFT JOIN t_user_focus focus ON tu.id = focus.focus_user_id AND focus.deleted = 0 AND focus.user_id = #{dto.userId}
        WHERE  ttic.user_id = #{dto.painterId} AND ttic.deleted = 0 AND tti.img_width IS NOT NULL AND mt.user_id NOT IN (SELECT block_user_id FROM mj_blacklist WHERE user_id = #{dto.painterId} AND deleted = 0)
        ORDER BY ttic.id DESC
    </select>

    <select id="queryMyLikePage" resultType="com.business.db.model.vo.GalleryQueryVO">
        SELECT
            tti.id AS imgId,
            CASE
                WHEN SUBSTRING(tti.img_url, 1, 4) = 'http' THEN tti.img_url ELSE CONCAT(#{dto.prefixUrl}, tti.img_url)
            END AS image_url,
            tti.create_time AS produceTime,
            tti.img_width width,
            tti.img_height height,
            tti.img_size size,
            tti.img_hue image_ave,
            tti.img_type content_type,
            mt.id,
            mt.action,
            tti.good_qua good_num,
            tti.collect_qua collect_num,
            1 AS likeState,
            CASE WHEN ttil.id IS NULL THEN 0 ELSE 1 END AS collectState,
            mt.prompt_use AS prompt,
            tu.id AS painterId,
            tu.avatar AS painterAvatar,
            tu.`name` AS painterName,
            tu.personal_profile AS painterProfile,
            CASE WHEN fans.fansNum IS NULL THEN 0 ELSE fans.fansNum END AS fansNum,
            CASE WHEN works.worksNum IS NULL THEN 0 ELSE works.worksNum END AS worksNum,
            CASE WHEN focus.focus_user_id IS NULL THEN 0 ELSE 1 END AS focusFlag
        FROM
            t_task_img_like ttic
        LEFT JOIN mj_task_detl tti ON tti.id = ttic.img_id
        LEFT JOIN t_task_img_collect ttil ON ttil.img_id = ttic.img_id AND ttil.deleted = 0 AND ttil.user_id = #{dto.userId}
        LEFT JOIN mj_task_new mt ON mt.id = ttic.task_id
        LEFT JOIN t_user tu ON tu.id = mt.user_id
        LEFT JOIN (SELECT COUNT(1) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id) fans ON tu.id = fans.focus_user_id
        LEFT JOIN (SELECT COUNT(1) AS worksNum, user_id FROM mj_task_new WHERE deleted = 0 AND status=5 AND action not in (12, 13) GROUP BY user_id) works ON tu.id = works.user_id
        LEFT JOIN t_user_focus focus ON tu.id = focus.focus_user_id AND focus.deleted = 0 AND focus.user_id = #{dto.userId}
        WHERE  ttic.user_id = #{dto.painterId} AND ttic.deleted = 0 AND tti.img_width IS NOT NULL AND mt.user_id NOT IN (SELECT block_user_id FROM mj_blacklist WHERE user_id = #{dto.painterId} AND deleted = 0)
        ORDER BY ttic.id DESC
    </select>

    <!-- AND tti.img_width IS NOT NULL-->
    <select id="queryMyGalleryPage" resultType="com.business.db.model.vo.GalleryQueryVO">
        SELECT
            tti.id AS img_id,
            CASE
                WHEN SUBSTRING(tti.img_url, 1, 4) = 'http' THEN tti.img_url ELSE CONCAT(#{dto.prefixUrl}, tti.img_url)
            END AS image_url,
            tti.good_qua good_num,
            tti.collect_qua collect_num,
            tti.create_time produce_time,
            tti.img_width width,
            tti.img_height height,
            tti.img_size size,
            tti.img_hue image_ave,
            tti.img_type content_type,
            mt.id,
            mt.action,
            mt.prompt_use AS prompt,
            tu.id AS painter_id,
            tu.`name` AS painter_name,
            tu.avatar AS painter_avatar,
            tu.personal_profile AS painter_profile,
            CASE
                WHEN ttic.id IS NULL THEN 0
                ELSE 1
            END AS collect_state,
                CASE
                    WHEN ttil.id IS NULL THEN 0
                    ELSE 1
            END AS like_state
        FROM
            mj_task_detl tti
        LEFT JOIN mj_task_new mt ON mt.id = tti.task_id
        LEFT JOIN t_task_img_like ttil ON ttil.img_id = tti.id AND ttil.task_id = tti.task_id AND ttil.deleted = 0 AND ttil.user_id = #{dto.userId}
        LEFT JOIN t_task_img_collect ttic ON ttic.img_id = tti.id AND ttic.task_id = tti.task_id AND ttic.deleted = 0 AND ttic.user_id = #{dto.userId}
        LEFT JOIN t_user tu ON tu.id = mt.user_id
        WHERE  mt.user_id = #{dto.painterId} AND tti.deleted = 0 AND tti.save_flag = 1 AND mt.action NOT IN (21, 23)
        ORDER BY tti.id DESC
    </select>

    <select id="queryMyGalleryNewPage" resultType="com.business.db.model.vo.GalleryQueryVO">
        SELECT
            mj_task.id,
            mj_task.action,
            tti.id AS img_id,
            mj_task.prompt_use AS prompt,
            CASE
                WHEN SUBSTRING(mj_task.main_img_url, 1, 4) = 'http' THEN mj_task.main_img_url ELSE CONCAT(#{dto.prefixUrl}, mj_task.main_img_url)
            END AS image_url,
            mj_task.img_width width,
            mj_task.img_height height,
            mj_task.img_size size,
            mj_task.img_hue image_ave,
            mj_task.img_type content_type,
            t_user.id AS painter_id,
            t_user.`name` AS painter_name,
            t_user.avatar AS painter_avatar,
            t_user.personal_profile AS painter_profile,
            tti.good_qua good_num,
            tti.collect_qua collect_num,
            fans.fansNum,
            works.worksNum,
            CASE WHEN focus.focus_user_id IS NULL THEN 0 ELSE 1 END AS focusFlag,
            CASE WHEN ttic.id IS NULL THEN 0 ELSE 1 END AS collect_state,
            CASE WHEN ttil.id IS NULL THEN 0 ELSE 1 END AS like_state,
            mj_task.create_time produce_time
        FROM mj_task_new mj_task
        LEFT JOIN mj_task_detl tti ON mj_task.id = tti.task_id
        LEFT JOIN t_task_img_like ttil ON ttil.img_id = tti.id AND ttil.task_id = tti.task_id AND ttil.deleted = 0 AND ttil.user_id = #{dto.userId}
        LEFT JOIN t_task_img_collect ttic ON ttic.img_id = tti.id AND ttic.task_id = tti.task_id AND ttic.deleted = 0 AND ttic.user_id = #{dto.userId}
        LEFT JOIN t_user ON t_user.id = mj_task.user_id
        LEFT JOIN (SELECT COUNT(1) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id) fans ON t_user.id = fans.focus_user_id
        LEFT JOIN (SELECT COUNT(1) AS worksNum, user_id FROM mj_task_new WHERE deleted = 0 AND status=5 AND action NOT IN (12, 13, 23) GROUP BY user_id) works ON t_user.id = works.user_id
        LEFT JOIN t_user_focus focus ON mj_task.user_id = focus.focus_user_id AND focus.deleted = 0 AND focus.user_id = #{dto.userId}
        WHERE mj_task.user_id = #{dto.painterId} AND mj_task.deleted = 0 AND mj_task.status = 5 AND mj_task.action NOT IN (21, 23) AND mj_task.img_width IS NOT NULL GROUP BY mj_task.id ORDER BY mj_task.id DESC
    </select>

    <update id="updateGoodNum">
        UPDATE mj_task_detl SET good_qua = good_qua + ${num} WHERE id = #{imgId}
    </update>

    <update id="updateCollectNum">
        UPDATE mj_task_detl SET collect_qua = collect_qua + ${num} WHERE id = #{imgId}
    </update>

    <update id="batchDelMyGallery">
        update mj_task_detl set deleted = #{deleted} where id in (
            <foreach collection="list" item="item" separator="," >
            #{item}
            </foreach>
        )
    </update>

    <select id="getImgUrlByIds" resultType="java.lang.String">
        select t_task_img.image_url from t_task_img where t_task_img.deleted=0 and t_task_img.id in (
        <foreach collection="imgIdList" item="item" separator="," >
            #{item}
        </foreach>
        )
    </select>


    <!-- app 2.0 我的画廊 -->
    <select id="queryGalleryPage" resultType="com.business.db.model.vo.ImgGalleryVO">
        SELECT
            img_draw_record.id,
            img_draw_detl.id AS imgId,
            img_draw_record.prompt_init AS prompt,
            img_draw_record.prompt_use AS promptUse,
            img_draw_detl.img_source_url AS imgSourceUrl,
            CASE
                WHEN SUBSTRING(img_draw_detl.img_url, 1, 4) = 'http' THEN img_draw_detl.img_url ELSE CONCAT(#{dto.prefixUrl}, img_draw_detl.img_url)
            END AS imageUrl,
            img_draw_record.width AS width,
            img_draw_record.height AS height,
            img_draw_detl.img_size size,
            img_draw_detl.img_hue imageAve,
            img_draw_detl.img_type contentType,
            t_user.id AS painterId,
            t_user.`name` AS painterName,
            t_user.avatar AS painterAvatar,
            t_user.personal_profile AS painterProfile,
            img_draw_detl.good_qua goodNum,
            img_draw_detl.collect_qua collectNum,
            fans.fansNum,
            works.worksNum,
        CASE
            WHEN focus.focus_user_id IS NULL THEN
            0 ELSE 1
            END AS focusFlag,
        CASE
            WHEN t_task_img_collect.id IS NULL THEN
            0 ELSE 1
            END AS collect_state,
        CASE
            WHEN t_task_img_like.id IS NULL THEN
            0 ELSE 1
            END AS like_state,
            img_draw_record.create_time produce_time
        FROM img_draw_record img_draw_record
            LEFT JOIN img_draw_detl img_draw_detl ON img_draw_record.id = img_draw_detl.draw_record_id
            LEFT JOIN t_task_img_like t_task_img_like ON t_task_img_like.img_id = img_draw_detl.id
            AND t_task_img_like.task_id = img_draw_detl.draw_record_id
            AND t_task_img_like.deleted = 0
            AND t_task_img_like.user_id = #{dto.userId}
            LEFT JOIN t_task_img_collect t_task_img_collect ON t_task_img_collect.img_id = img_draw_detl.id
            AND t_task_img_collect.task_id = img_draw_detl.draw_record_id
            AND t_task_img_collect.deleted = 0
            AND t_task_img_collect.user_id = #{dto.userId}
            LEFT JOIN t_user ON t_user.id = img_draw_record.user_id
            LEFT JOIN ( SELECT COUNT( 1 ) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id ) fans ON t_user.id = fans.focus_user_id
            LEFT JOIN ( SELECT COUNT( 1 ) AS worksNum, user_id FROM img_draw_record WHERE deleted = 0 AND STATUS = 5 AND fun_type = 1 GROUP BY user_id ) works ON t_user.id = works.user_id
            LEFT JOIN t_user_focus focus ON img_draw_record.user_id = focus.focus_user_id
            AND focus.deleted = 0
            AND focus.user_id = #{dto.userId}
        WHERE img_draw_record.user_id = #{dto.painterId}
            AND img_draw_record.deleted = 0
            AND img_draw_record.STATUS = 1
            AND img_draw_record.fun_type = 1
            AND img_draw_detl.is_save = 1
            AND img_draw_record.width IS NOT NULL AND img_draw_detl.img_url NOT LIKE '%midjourney%'
        ORDER BY img_draw_record.id DESC
    </select>
    <!-- GROUP BY img_draw_record.id -->

    <!-- app 2.0 我的收藏 -->
    <select id="queryCollectPage" resultType="com.business.db.model.vo.ImgGalleryVO">
        SELECT
            mt.fun_type,
            tti.video_url,
            tti.audio_url,
            tti.id AS imgId,
            CASE
                WHEN SUBSTRING(tti.img_url, 1, 4) = 'http' THEN tti.img_url ELSE CONCAT(#{dto.prefixUrl}, tti.img_url)
            END AS imageUrl,
            tti.create_time AS produceTime,
            tti.img_width width,
            tti.img_height height,
            tti.img_size size,
            tti.img_hue imageAve,
            tti.img_type contentType,
            mt.id,
            tti.good_qua goodNum,
            tti.collect_qua collectNum,
            1 AS collectState,
        CASE
            WHEN ttil.id IS NULL THEN
            0 ELSE 1
            END AS likeState,
            mt.prompt_init AS prompt,
            mt.prompt_use AS promptUse,
            tu.id AS painterId,
            tu.avatar AS painterAvatar,
            tu.`name` AS painterName,
            tu.personal_profile AS painterProfile,
        CASE
            WHEN fans.fansNum IS NULL THEN
            0 ELSE fans.fansNum
            END AS fansNum,
        CASE
            WHEN works.worksNum IS NULL THEN
            0 ELSE works.worksNum
            END AS worksNum,
        CASE
            WHEN focus.focus_user_id IS NULL THEN
            0 ELSE 1
            END AS focusFlag
        FROM
            t_task_img_collect ttic
            LEFT JOIN img_draw_detl tti ON tti.id = ttic.img_id
            LEFT JOIN t_task_img_like ttil ON ttil.img_id = ttic.img_id
            AND ttil.deleted = 0
            AND ttil.user_id = #{dto.userId}
            LEFT JOIN img_draw_record mt ON mt.id = ttic.task_id
            LEFT JOIN t_user tu ON tu.id = mt.user_id
            LEFT JOIN ( SELECT COUNT( 1 ) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id ) fans ON tu.id = fans.focus_user_id
            LEFT JOIN ( SELECT COUNT( 1 ) AS worksNum, user_id FROM img_draw_record WHERE deleted = 0 AND STATUS = 1 AND fun_type = 1 GROUP BY user_id ) works ON tu.id = works.user_id
            LEFT JOIN t_user_focus focus ON tu.id = focus.focus_user_id
            AND focus.deleted = 0
            AND focus.user_id = #{dto.userId}

            WHERE
            ttic.user_id = #{dto.painterId}

            AND ttic.deleted = 0
            AND tti.img_width IS NOT NULL
            AND mt.user_id NOT IN (
            SELECT
            block_user_id
            FROM
            mj_blacklist
            WHERE
            user_id = #{dto.painterId} AND deleted = 0)

            <if test="dto.version == null">
                AND mt.fun_type = 1
            </if>
            ORDER BY
            ttic.id DESC
    </select>

    <!-- app 2.0 我的点赞 -->
    <select id="queryLikePage" resultType="com.business.db.model.vo.ImgGalleryVO">
        SELECT
            tti.id AS imgId,
            CASE
                WHEN SUBSTRING(tti.img_url, 1, 4) = 'http' THEN tti.img_url ELSE CONCAT(#{dto.prefixUrl}, tti.img_url)
            END AS imageUrl,
            tti.create_time AS produceTime,
            tti.img_width width,
            tti.img_height height,
            tti.img_size size,
            tti.img_hue imageAve,
            tti.img_type contentType,
            mt.id,
            tti.good_qua goodNum,
            tti.collect_qua collectNum,
            1 AS likeState,
        CASE
            WHEN ttil.id IS NULL THEN
            0 ELSE 1
            END AS collectState,
            mt.prompt_init AS prompt,
            mt.prompt_use AS promptUse,
            tu.id AS painterId,
            tu.avatar AS painterAvatar,
            tu.`name` AS painterName,
            tu.personal_profile AS painterProfile,
        CASE
            WHEN fans.fansNum IS NULL THEN
            0 ELSE fans.fansNum
            END AS fansNum,
        CASE
            WHEN works.worksNum IS NULL THEN
            0 ELSE works.worksNum
            END AS worksNum,
        CASE
            WHEN focus.focus_user_id IS NULL THEN
            0 ELSE 1
            END AS focusFlag
        FROM
            t_task_img_like ttic
            LEFT JOIN img_draw_detl tti ON tti.id = ttic.img_id
            LEFT JOIN t_task_img_collect ttil ON ttil.img_id = ttic.img_id
            AND ttil.deleted = 0
            AND ttil.user_id = #{dto.userId}
            LEFT JOIN img_draw_record mt ON mt.id = ttic.task_id
            LEFT JOIN t_user tu ON tu.id = mt.user_id
            LEFT JOIN ( SELECT COUNT( 1 ) AS fansNum, focus_user_id FROM t_user_focus WHERE deleted = 0 GROUP BY focus_user_id ) fans ON tu.id = fans.focus_user_id
            LEFT JOIN ( SELECT COUNT( 1 ) AS worksNum, user_id FROM img_draw_record WHERE deleted = 0 AND STATUS = 1 AND fun_type = 1 GROUP BY user_id ) works ON tu.id = works.user_id
            LEFT JOIN t_user_focus focus ON tu.id = focus.focus_user_id
            AND focus.deleted = 0
            AND focus.user_id = #{dto.userId}

            WHERE
            ttic.user_id = #{dto.painterId}

            AND ttic.deleted = 0
            AND tti.img_width IS NOT NULL
            AND mt.user_id NOT IN (
            SELECT
            block_user_id
            FROM
            mj_blacklist
            WHERE
            user_id = #{dto.painterId} AND deleted = 0)

            ORDER BY
            ttic.id DESC
    </select>



</mapper>
