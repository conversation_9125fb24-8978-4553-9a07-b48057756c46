<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.ImgDrawStyleConfigLabelMapper">

    <insert id="insertBatch" parameterType="com.business.db.model.po.draw.ImgDrawStyleConfigLabelPO">
        INSERT INTO img_draw_style_config_label (id, style_config_id, label_id)
        VALUES
        <foreach collection="list" item="item" separator="," >
            (
            #{item.id},
            #{item.styleConfigId},
            #{item.labelId}
            )
        </foreach>
    </insert>

    <select id="selectList" resultType="com.business.db.model.po.draw.ImgDrawStyleLabelPO">
        SELECT img_draw_style_label.id, img_draw_style_label.label_name
        FROM img_draw_style_config_label LEFT JOIN img_draw_style_label ON img_draw_style_config_label.label_id = img_draw_style_label.id
        WHERE img_draw_style_config_label.style_config_id = #{configId}
    </select>

    <select id="selectStyleConfigIdsByLabelId" resultType="java.lang.Long">
        SELECT style_config_id FROM img_draw_style_config_label WHERE label_id = #{labelId}
    </select>

    <select id="selectByStyleConfigIds" parameterType="java.util.List" resultType="com.business.db.model.vo.draw.ImgDrawStyleLabelVO">
        SELECT
        img_draw_style_config_label.style_config_id,
        img_draw_style_label.id,
        img_draw_style_label.label_name,
        img_draw_style_config_label.style_config_id AS styleConfigId
        FROM img_draw_style_config_label
        LEFT JOIN img_draw_style_label
        ON img_draw_style_config_label.label_id = img_draw_style_label.id
        WHERE img_draw_style_config_label.style_config_id IN
        <foreach collection="styleConfigIds" item="configId" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </select>

    <delete id="deleteByStyleConfigId" parameterType="java.lang.Long">
        DELETE FROM img_draw_style_config_label WHERE style_config_id = #{styleConfigId}
    </delete>


    <select id="selectListByIds"  resultType="com.business.db.model.po.draw.ImgDrawStyleLabelPO">
        select distinct label.* from img_draw_style_label label INNER JOIN img_draw_style_config_label c_label
            on label.id=  c_label.label_id
            where label.deleted=0 and label.is_use = 1  ORDER BY label.sort

    </select>

</mapper>
