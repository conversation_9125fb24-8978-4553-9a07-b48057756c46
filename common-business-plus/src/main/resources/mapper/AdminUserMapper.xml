<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.business.db.mapper.AdminUserMapper">

    <select id="queryPage" resultType="com.business.db.model.vo.AdminUserVO">
        SELECT
            photo_topic.id,
            photo_topic.topic_type_id,
            photo_topic.topic_title,
            photo_topic.topic_title_en,
            photo_topic.topic_url,
            photo_topic.is_hot,
            photo_topic.img_type,
            photo_topic.img_hue,
            photo_topic.img_width,
            photo_topic.img_height,
            photo_topic.img_size,
            photo_topic.create_time
        FROM photo_topic
        WHERE photo_topic.deleted = 0 ORDER BY photo_topic.create_time DESC

    </select>

</mapper>
