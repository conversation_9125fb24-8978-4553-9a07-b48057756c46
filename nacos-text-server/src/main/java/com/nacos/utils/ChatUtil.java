package com.nacos.utils;

import cn.hutool.core.util.ObjectUtil;
import com.nacos.model.dto.GptSceneGenerationDTO;

public class ChatUtil {

    public static final String finalChatCompletionsUrl =  "/v1/chat/completions";

    public static String getSceneGenerationPrompt(GptSceneGenerationDTO gptSceneGenerationDTO, String goodPrompt){
        return goodPrompt + ":\n" + gptSceneGenerationDTO.getProblem() + (ObjectUtil.isNotNull(gptSceneGenerationDTO.getOutputLimit()) ? "\n要求最少" + gptSceneGenerationDTO.getOutputLimit() + "字" : "" + "" +
//                "\n请用" + gptSceneGenerationDTO.getIntonat() + "的术语回答" +
                "");
    }

}
