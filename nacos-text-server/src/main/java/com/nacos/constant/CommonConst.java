package com.nacos.constant;


import com.google.common.collect.ImmutableMap;
import org.apache.commons.logging.impl.WeakHashtable;

import java.util.Map;

public class CommonConst {

    /**
     * gpt 3.5 token 最大限制
     */
    public static final Integer GPT_3_5_TURBO_0301_TOKENS = 4096;

    /*
     * 音频任务超时时间(分钟)
     */
    public static final int TASK_TIMEOUT_MINUTES_AUDIO = 20;

    /**
     * 音频任务超时时间(分钟)
     */
    public static final int TASK_TIMEOUT_MINUTES = 15;

    public static final String MODEL_CATEGORY_TEXT = "text";

    public static final int MODEL_ENABLED = 1;

    public static final String MODEL_CATEGORY_TYQW = "tyqw";

    public static final String MODEL_CATEGORY_KOWNLEDGE = "kownledge";

    public static final String MODEL_CATEGORY_gpt = "gpt";

    public static final Map<Integer,String> MODEL_TYPE = ImmutableMap.of(
            1,"qwen-plus-2025-04-28",
            2,"deepseek-r1"
    );
}
