package com.nacos.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nacos.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;


@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("dict_config")
@Schema(name = "字典配置", description = "字典配置表")
public class DictConfigPO extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(name = "类型编码", type = "Long")
    private Long dictType;

    @Schema(name = "子key", type = "Long")
    private Long dictKey;

    @Schema(name = "值", type = "String")
    private String dictValue;

    @Schema(name = "是否使用", type = "Integer")
    private Integer isUse;
}

