package com.nacos.model.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.business.model.vo.pika.ImgDetailDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Schema(name = "视频参数实体", description = "视频参数实体")
@Data
@Validated
public class ImgVideoDTO {

    @NotNull(message = "绘图模型不能为空！")
    @Schema(name = "绘图模型id", type = "Long")
    private Integer modelId;

    @Schema(name = "用户id", type = "Long")
    private Long userId;

    @Schema(name = "垫图图片地址", type = "Array")
    private List<String> initImgUrls;

    @Schema(name = "视频url地址", type = "String")
    private String videoUrl;

    @Schema(description = "宽度", type = "Integer")
    private Integer width;

    @Schema(description = "高度", type = "Integer")
    private Integer height;

    @Schema(name = "生成视频指令内容", type = "String")
    private String prompt;

    @Schema(name = "生成视频指令内容翻译后", type = "String")
    private String promptUse;

    @Schema(name = "生成视频负向指令内容", type = "String")
    private String negativeprompt;

    @Schema(name = "生成视频负向指令内容翻译后", type = "String")
    private String negativepromptUse;

    @Min(value = 0, message = "0！")
    @Max(value = 4294967294L, message = "4294967294L")
    @Schema(title = "种子值 随机性")
    private Integer seed;

    @Min(value = 0, message = "1！")
    @Max(value = 10, message = "10！")
    @Schema(title = "视频与原始图像的粘连程度，使用较高的值可以纠正运动扭曲")
    private Double cfgScale;

    @Min(value = 1, message = "1！")
    @Max(value = 255, message = "255！")
    @Schema(title = "较低的值通常会导致输出视频中的运动较少，而较高的值通常会导致较多的运动")
    private Integer motionBucketId;

    @Schema(title = "视频版本key")
    private Integer videoVersionKey;

    @Schema(title = "视频模型值")
    private String videoModelValue;

    @Schema(name = "镜头参数", type = "Integer")
    private Integer lensValue;

    // 皮卡特有的参数
    @Schema(title = "风格key")
    private Integer styleKey;

    @Schema(title = "比列key")
    private Integer scalesKey;

    @Schema(title = "镜头数组")
    private String videoLens;

    @Schema(title = "权重值")
    private Double[] weight; // 权重值

    // domo视频参数
    @Schema(title = "时长")
    private String duration;

    @Schema(name = "视频是否休闲模式", type = "Integer")
    private Integer modelKey = 2;

    @Schema(name = "视频参考值", type = "Integer")
    private Integer referModeKey;

    @Schema(name = "原上传视频时长", type = "Double")
    private Double videoDuration;

    // 梦工厂
    @Schema(name = "首图主色", type = "Integer")
    private String firstHue;

    @Schema(name = "尾图主色", type = "Integer")
    private String lastHue;

    @Schema(name = "是否循环", type = "boolean")
    private boolean loop = false;

    @Schema(name = "首帧图片详情", type = "ImgDetail")
    private ImgDetailDTO imgDetailFirst;

    @Schema(name = "尾帧图片详情", type = "ImgDetail")
    private ImgDetailDTO imgDetailLast;


    // 达芬奇
    @Schema(name = "版本（基础、高级）", type = "String")
    private Integer version;

    @Schema(name = "图片是否作为尾帧", type = "Boolean")
    private Boolean imageAsEndFrame = false;

    // 新版
    @Schema(name = "风格 === 暂不用 什么垃圾", type = "String")
    private String style;

    @Schema(name = "风格", type = "String")
    private String styleJson;

    @Schema(name = "镜头", type = "String")
    private String videoLensNew;
    
    @Schema(name = "比例", type = "String")
    private String aspectRatio;

    @Schema(name = "首图", type = "String")
    private String firstImg;

    @Schema(name = "尾图", type = "String")
    private String endImg;

    // 任务id新加的任务id
    @Schema(name = "任务id", type = "String")
    private String taskId;

    @Schema(title = "团队号")
    private Integer asTeamId;

    @Schema(title = "视频权重")
    private Integer videoWeight;

    @Data
    public static class VideoLensObj {
        @Schema(title = "向上")
        @JSONField(name = "up")
        private Integer up;

        @Schema(title = "向下")
        @JSONField(name = "down")
        private Integer down;

        @Schema(title = "向左")
        @JSONField(name = "left")
        private Integer left;

        @Schema(title = "向右")
        @JSONField(name = "right")
        private Integer right;

        @Schema(title = "上绕轴")
        @JSONField(name = "upAround")
        private Integer upAround;

        @Schema(title = "下绕轴")
        @JSONField(name = "downAround")
        private Integer downAround;

        @Schema(title = "左绕轴")
        @JSONField(name = "leftAround")
        private Integer leftAround;

        @Schema(title = "右绕轴")
        @JSONField(name = "rightAround")
        private Integer rightAround;

        @Schema(title = "镜头左旋转")
        @JSONField(name = "leftRotate")
        private Integer leftRotate;

        @Schema(title = "镜头右旋转")
        @JSONField(name = "rightRotate")
        private Integer rightRotate;

        @Schema(title = "镜头推")
        @JSONField(name = "push")
        private Integer push;

        @Schema(title = "镜头拉")
        @JSONField(name = "pull")
        private Integer pull;

    }

}
