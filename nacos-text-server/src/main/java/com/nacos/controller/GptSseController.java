package com.nacos.controller;

import com.nacos.result.Result;
import com.nacos.server.SseEmitterServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Slf4j
@RestController
@RequestMapping("/sse")
public class GptSseController {

    @GetMapping(value = "/connect/{userId}", name = "创建连接")
    public SseEmitter connect(@PathVariable String userId) {
        return SseEmitterServer.connect(userId);
    }

    @GetMapping(value = "/close/{userId}", name = "关闭连接")
    public Result<String> close(@PathVariable("userId") String userId) {
        SseEmitterServer.removeUser(userId);
        return Result.SUCCESS("连接关闭");
    }

    @GetMapping(value = "/connectWeb/{userId}", name = "web端创建连接")
    public SseEmitter connectWeb(@PathVariable String userId) {
        return SseEmitterServer.connectWeb(userId);
    }

    @GetMapping(value ="/closeWeb/{userId}", name = "web端关闭连接")
    public Result<String> closeWeb(@PathVariable("userId") String userId) {
        SseEmitterServer.removeUserWeb(userId);
        return Result.SUCCESS("web连接关闭");
    }
}
