package com.nacos.config;

import com.nacos.exception.DzBalanceE;
import com.nacos.result.Result;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Objects;

/**自身服务内使用：全局异常拦截*/
@RestControllerAdvice
@Slf4j
public class ExceptionAdvice {

    /**
     * 异常处理类型
     * @return 返回异常信息
     */
    @ExceptionHandler(value = Exception.class)
    public Result<Object> handleVaildException(Exception e) {
        log.error(e.getMessage(),e);
        // 校验实体参数
        if (e instanceof MethodArgumentNotValidException){
            return Result.ERROR_EXCEPTION(Objects.requireNonNull(((MethodArgumentNotValidException) e).getBindingResult().getFieldError()).getDefaultMessage());
        }
        //校验单个参数
        if (e instanceof ConstraintViolationException){
            return Result.ERROR_EXCEPTION(((ConstraintViolationException) e).getConstraintViolations().stream().map(ConstraintViolation::getMessage).toList().getFirst());
        }
        if (e instanceof MissingServletRequestParameterException){
            return Result.ERROR_EXCEPTION(((MissingServletRequestParameterException) e).getParameterName() + "为必传参数");
        }
        //效验点子不足
        if (e instanceof DzBalanceE dzBalanceE){
            return Result.ERROR_DZ(dzBalanceE.getMessage());
        }
        log.error("系统异常：{}",e.getMessage());
        // return Result.ERROR_EXCEPTION("系统内部异常");
        return Result.ERROR_EXCEPTION("系统繁忙，请稍后再试");
    }
}
