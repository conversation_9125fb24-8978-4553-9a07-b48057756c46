package com.nacos.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.mapper.DictConfigMapper;
import com.business.db.mapper.PayRecordMapper;
import com.business.db.mapper.SceneConfigMapper;
import com.business.db.mapper.SceneRecordMapper;
import com.business.db.model.dto.text.SceneRecordQueryDTO;
import com.business.db.model.po.FlowRecordPO;
import com.business.db.model.po.SceneConfigPO;
import com.business.db.model.po.SceneRecordPO;
import com.business.db.model.vo.text.SceneRecordQueryVO;
import com.business.tengxunyun.BTengXunUtil;
import com.business.utils.BThirdPartyKey;
import com.nacos.auth.JwtNewUtil;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.enums.*;
import com.nacos.exception.E;
import com.nacos.exception.IBusinessException;
import com.nacos.listener.ConsoleStreamListener;
import com.nacos.model.dto.GptSceneGenerationDTO;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.AsyncService;
import com.nacos.service.CheckBalanService;
import com.nacos.service.GptTextService;
import com.nacos.service.IUserService;
import com.nacos.utils.ChatUtil;
import com.nacos.utils.gpt.ChatGptStream;
import com.nacos.utils.gpt.Message;
import com.volcengine.model.maas.api.Api;
import com.volcengine.service.maas.MaasException;
import com.volcengine.service.maas.MaasService;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
@Service
public class GptTextServiceImpl implements GptTextService {

    @Resource
    SceneConfigMapper sceneConfigMapper;

    @Resource
    DictConfigMapper dictConfigMapper;

    @Resource
    SceneRecordMapper sceneRecordMapper;

    @Resource
    IUserService userService;

    @Resource
    CheckBalanService checkBalanService;

    @Resource
    AsyncService asyncService;

    @Resource
    PayRecordMapper payRecordMapper;

    @Override
    public Result<Long> sceneGeneration(GptSceneGenerationDTO gptSceneGenerationDTO) throws IBusinessException {
        if (StringUtils.isEmpty(gptSceneGenerationDTO.getProblem()) || ObjectUtil.isNull(gptSceneGenerationDTO.getSceneId())) {
            throw new E("请输入有效的内容！");
        }

        SceneConfigPO sceneConfigPO = sceneConfigMapper.selectById(gptSceneGenerationDTO.getSceneId());
        if (ObjectUtil.isNull(sceneConfigPO)) {
            return Result.ERROR("该文案场景不存在！");
        }

        //腾讯文本安全检测
        if (BTengXunUtil.textToExamineFailByText(gptSceneGenerationDTO.getProblem(), gptSceneGenerationDTO.getUserId())) {
            return Result.ERROR("您的指令包含敏感词汇，建议修改再试！");
        }

        // 获取chat相关信息
        HashMap<Long, String> dictConfigMap = BThirdPartyKey.getSecretKeyInfo(DictConfigEnum.CHAT_KEY.getDictType());
        if (dictConfigMap == null) {
            return Result.ERROR("模型升级中...");
        }
        //查询对应chat相关信息
        /*List<DictConfigPO> dictConfigPOs = dictConfigMapper.selectList(
                new LambdaQueryWrapper<DictConfigPO>()
                        .eq(DictConfigPO::getDictType, DictConfigEnum.CHAT_KEY.getDictType())
                        .eq(DictConfigPO::getIsUse, DictConfigEnum.getIsUseTrue()));
        if (ObjectUtil.isNull(dictConfigPOs) || dictConfigPOs.isEmpty()){
            return Result.ERROR("CHAT配置不存在");
        }
        HashMap<Long, String> dictConfigMap = new HashMap<>();
        for (DictConfigPO dictConfigPO : dictConfigPOs) {
            dictConfigMap.put(dictConfigPO.getDictKey(), dictConfigPO.getDictValue());
        }*/

        if (gptSceneGenerationDTO.getModel() == GptModelEnum.TOP_SPEED_MODEL.getType()) { //极速
            return sceneGenerationYQ(gptSceneGenerationDTO, sceneConfigPO, dictConfigMap);
        } else {
            /*Integer queryUserGrade = payRecordMapper.queryUserGrade(gptSceneGenerationDTO.getUserId());
            if (queryUserGrade == null || queryUserGrade <= VipGradeEnum.MEMBER_PT.getIntValue()) {
                return Result.ERROR("抱歉此功能只对VIP用户开放");
            }
            return sceneGenerationChat(gptSceneGenerationDTO, sceneConfigPO, dictConfigMap);*/
            return sceneGenerationYQ(gptSceneGenerationDTO, sceneConfigPO, dictConfigMap);
        }
    }

    private Result<Long> sceneGenerationChat(GptSceneGenerationDTO gptSceneGenerationDTO, SceneConfigPO sceneConfigPO, HashMap<Long, String> dictConfigMap) throws IBusinessException {
        //查询场景，拼接用户输入的prompt
        //拼接发送语
        List<Message> messages = new ArrayList<>();
        messages.add(Message.of(ChatUtil.getSceneGenerationPrompt(gptSceneGenerationDTO, sceneConfigPO.getGoodPrompt())));
        messages.add(Message.ofSystem(dictConfigMap.get(DictConfigEnum.CHAT_ROLE_SCENE.getDictKey())));
        long logId = IdWorker.getId();

        //查询扣除点子数量
//        RuleConfigBO ruleConfig = RedisUtil.getCacheObject(CommonConstant.SYS_RULE_CONFIG);
        double chatSceneWriterDeductQua = Double.parseDouble(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DD_USE_RULE.getStrKey(), DDUseRuleEnum.DRAW_AI_COPY_WRITING.getRedisKey())));

        //扣除用户点子数量
        FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ONE.getDtoKey()).remark(FlowRecordEnum.FOUR.getRemark()).build();
        Long payInfoId = checkBalanService.checkUser(gptSceneGenerationDTO.getUserId(), chatSceneWriterDeductQua, flowRecordPO);

        ChatGptStream chatGptStream = ChatGptStream.builder()
                .timeout(600)
                .apiKey(dictConfigMap.get(DictConfigEnum.CHAT_KEY.getDictKey()))
                .apiHost(dictConfigMap.get(DictConfigEnum.CHAT_URL.getDictKey()))
                .build()
                .init();

        ConsoleStreamListener listener = ConsoleStreamListener.builder()
                .type(GptTypeEnum.COPYWRITING.getType())
                .userId(gptSceneGenerationDTO.getUserId())
                .logId(logId)
                .way(payInfoId)
                .userService(userService)
                .asyncService(asyncService)
                .deductions(chatSceneWriterDeductQua)
                .build();
        listener.setOnComplate(msg -> {
            asyncService.endOfAnswer(logId, GptTypeEnum.COPYWRITING.getType(), msg.toString());
        });

        chatGptStream.streamChatCompletion(messages, listener, gptSceneGenerationDTO.getModel());
        //保存文案生成记录
        sceneRecordMapper.insert(SceneRecordPO.buildSceneRecordPO(logId, sceneConfigPO.getSceneName(), gptSceneGenerationDTO.getSceneId()
                , gptSceneGenerationDTO.getUserId(), gptSceneGenerationDTO.getProblem(), gptSceneGenerationDTO.getOutputLimit()));
        return Result.SUCCESS();
    }

    private Result<Long> sceneGenerationYQ(GptSceneGenerationDTO gptSceneGenerationDTO, SceneConfigPO sceneConfigPO, HashMap<Long, String> dictConfigMap) throws IBusinessException {

        //查询场景，拼接用户输入的prompt
        //拼接发送语
        String userMessage = ChatUtil.getSceneGenerationPrompt(gptSceneGenerationDTO, sceneConfigPO.getGoodPrompt());
        String systemMessage = dictConfigMap.get(DictConfigEnum.CHAT_ROLE_SCENE.getDictKey());
        Long userId = gptSceneGenerationDTO.getUserId(); //JwtNewUtil.getUserId();
        long logId = IdWorker.getId();
        //查询扣除点子数量
//        RuleConfigBO ruleConfig = RedisUtil.getCacheObject(CommonConstant.SYS_RULE_CONFIG);
        double chatSceneWriterDeductQua = Double.parseDouble(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DD_USE_RULE.getStrKey(), DDUseRuleEnum.DRAW_AI_COPY_WRITING.getRedisKey())));
        //扣除用户点子数量
        FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ONE.getDtoKey()).remark(FlowRecordEnum.FOUR.getRemark()).build();
        log.info("获取扣点子配置= {}", chatSceneWriterDeductQua);
        checkBalanService.checkUser(userId, chatSceneWriterDeductQua, flowRecordPO);
        asyncService.streamChatCompletion(userId, logId, userMessage, systemMessage);
        //保存文案生成记录
        sceneRecordMapper.insert(SceneRecordPO.buildSceneRecordPO(logId, sceneConfigPO.getSceneName(), gptSceneGenerationDTO.getSceneId()
                , userId, gptSceneGenerationDTO.getProblem(), gptSceneGenerationDTO.getOutputLimit()));
        return Result.SUCCESS();
    }

    @Override
    public Result<Page<SceneRecordQueryVO>> querySceneRecordPage(SceneRecordQueryDTO dto) {
        Page<SceneRecordQueryVO> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        return Result.SUCCESS(sceneRecordMapper.querySceneRecordPage(page, dto));
    }

    @Override
    public Result<Page<SceneRecordQueryVO>> querySceneRecordsBySceneId(SceneRecordQueryDTO dto) {
        Page<SceneRecordQueryVO> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        Page<SceneRecordQueryVO> result = sceneRecordMapper.querySceneRecordsBySceneId(page, dto);
        //result的结果，按照id正向排序
        result.getRecords().sort((o1, o2) -> o1.getId().compareTo(o2.getId()));
        return Result.SUCCESS(result);
    }

    @Override
    public Result<Integer> delSceneRecord(BaseDeleteEntity delete) {
        return Result.SUCCESS(sceneRecordMapper.deleteBatchIds(delete.getIds()));
    }

    @SneakyThrows
    @Override
    public Result<Long> selectSceneTaskCount() {
        log.info("==selectSceneTaskCount userId= {}", JwtNewUtil.getUserId());
        return Result.SUCCESS(sceneRecordMapper.selectCount(new LambdaQueryWrapper<SceneRecordPO>()
                .eq(SceneRecordPO::getUserId, JwtNewUtil.getUserId())
                .isNull(SceneRecordPO::getMessage)));
    }

    //测试
    private static void testStreamChat(MaasService maasService, Api.ChatReq req) {
        Stream<Api.ChatResp> resps = null;
        try {
            resps = maasService.streamChat(req);
        } catch (MaasException e) {
            e.printStackTrace();
        }
        assert resps != null;

        // it is possible that error occurs during response processing
        try {
            resps.forEach(resp -> {
                System.out.println(resp.getChoice().getMessage().getContent());

                // last message, will return full response including usage, role, finish_reason, etc.
                if (resp.getUsage().isInitialized()) {
                    System.out.println(resp.getUsage());
                }
            });
        } catch (RuntimeException e) {
            Throwable cause = e.getCause();
            if (cause instanceof MaasException) {
                System.out.println("code: " + ((MaasException) cause).getCode());
                System.out.println("code_n: " + ((MaasException) cause).getCodeN());
                System.out.println("message: " + ((MaasException) cause).getMsg());
            }
            System.out.println("caught: " + e);
        }
    }

}
