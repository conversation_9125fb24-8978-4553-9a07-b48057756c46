package com.nacos.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.business.db.mapper.ImgDrawDetlMapper;
import com.business.db.mapper.ImgDrawRecordMapper;
import com.business.db.model.po.SysNotificationPO;
import com.business.db.model.vo.ImgDrawHistoryVO;
import com.business.enums.BNotificationEnum;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.business.model.po.ImgDrawRecordPO;
import com.nacos.enums.ImgDrawEnum;
import com.nacos.enums.ImgOptModelEnum;
import com.nacos.service.IHandleVideoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@Transactional(rollbackFor = {Exception.class})
public class HandleVideoServiceImpl implements IHandleVideoService {

    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;
    @Resource
    private ImgDrawDetlMapper imgDrawDetlMapper;

    @Override
    public void videoJobFailedRollbackDz(ImgDrawRecordPO imgDrawRecordPO) throws Exception{
        //删除记录
        imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
        );

        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
        imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_FAIL.getValue());
        // 推送通知
        if(StringUtils.isNotBlank(imgDrawRecordPO.getFailReason())){
            this.taskMessagePush(imgDrawHistoryVO, "");
            this.notificationErrorMessage(imgDrawHistoryVO,imgDrawRecordPO.getFailReason());
        }else{
            this.taskMessagePush(imgDrawHistoryVO, "");
            this.notificationErrorMessage(imgDrawHistoryVO,"任务失败");
        }
    }

    @Override
    public void taskMessagePush(ImgDrawHistoryVO imgDrawHistoryVO, String message) throws Exception{
        if (imgDrawHistoryVO == null) {
            log.warn("imgDrawHistoryVO is null. Unable to push message.");
            return;
        }
        boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(imgDrawHistoryVO.getUserId(), BMessageSendEnum.VIDEO_JOB_SD_PUSH,JSONObject.toJSONString(imgDrawHistoryVO),message));
        log.info("视频任务推送状态: {}", state);
    }

    @Override
    public void notificationMessage(ImgDrawHistoryVO imgDrawHistoryVO, String jobImageUrl)  throws Exception {
        SysNotificationPO notificationPO = SysNotificationPO.buildSysNotification(imgDrawHistoryVO.getUserId(),
                BNotificationEnum.VIDEO_NOTIF.getIntValue(),
                BNotificationEnum.VIDEO_NOTIF.getStrTitle(), BNotificationEnum.VIDEO_NOTIF.getStrContent(), 1, imgDrawHistoryVO.getId(), jobImageUrl);
        boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(imgDrawHistoryVO.getUserId(), BMessageSendEnum.NOTIFICATION_PUSH,JSONObject.toJSONString(notificationPO)));
        log.info("视频消息推送状态", state);
    }
    @Override
    public void notificationErrorMessage(ImgDrawHistoryVO imgDrawHistoryVO, String jobImageUrl)  throws Exception {
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawHistoryVO.getOptAttribute());
        SysNotificationPO notificationPO = SysNotificationPO.buildSysNotification(imgDrawHistoryVO.getUserId(),
                BNotificationEnum.VIDEO_NOTIF.getIntValue(),
                optTitleOne.concat("已失败"), optTitleOne, 1, imgDrawHistoryVO.getId(), null);
        boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(imgDrawHistoryVO.getUserId(), BMessageSendEnum.NOTIFICATION_PUSH,JSONObject.toJSONString(notificationPO)));
        log.info("视频消息推送状态", state);
    }

}
