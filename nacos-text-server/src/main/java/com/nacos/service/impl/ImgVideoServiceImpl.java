package com.nacos.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.business.aigc.PikaApis;
import com.business.aigc.domoai.DomoApis;
import com.business.aigc.domoai.model.JsonResponseJobVideo;
import com.business.aigc.domoai.model.VideoRequest;
import com.business.aigc.tusiart.TusiJobStateEnum;
import com.business.aliapi.wanx.WanxApiUtil;
import com.business.aliapi.wanx.model.WanxImgFirstLastFrameReqParamBO;
import com.business.aliapi.wanx.model.WanxImgReqParamBO;
import com.business.aliapi.wanx.model.WanxReqParamBO;
import com.business.audio.SunoAiApiUtil;
import com.business.audio.model.AudioJobItemBO;
import com.business.db.mapper.*;
import com.business.db.model.po.FlowRecordPO;
import com.business.db.model.po.ImgDrawDetlPO;
import com.business.db.model.po.SysNotificationPO;
import com.business.db.model.po.UserPrivateConfigPO;
import com.business.db.model.vo.ImgDrawDetlVO;
import com.business.db.model.vo.ImgDrawHistoryVO;
import com.business.enums.*;
import com.business.hailuo.HaiLuoApiUtil;
import com.business.hailuo.model.HaiLuoRequestBO;
import com.business.kling.KlingApiUtil;
import com.business.kling.KlingApis;
import com.business.kling.model.KlingImgVideoReqBO;
import com.business.kling.model.KlingTextVideoReqBO;
import com.business.kling.model.KlingTextVideoResBO;
import com.business.le.LeonardoUtil;
import com.business.le.model.LeonardoBO;
import com.business.luma.LumaApis;
import com.business.luma.LumaUploadPicUtil;
import com.business.luma.officeModel.LumaFrames;
import com.business.luma.officeModel.LumaFramesTypeEnum;
import com.business.luma.officeModel.LumaKeyFrames;
import com.business.luma.officeModel.LumaOfficeVideoReq;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.business.model.bo.VideoStyleBO;
import com.business.model.bo.pika.ImgVideoBO;
import com.business.model.po.*;
import com.business.model.vo.VideoReferModeVO;
import com.business.model.vo.VideoSpecialEffectsVO;
import com.business.model.vo.VideoSpeedVO;
import com.business.model.vo.VideoVersionVO;
import com.business.model.vo.pika.ImgVideoVO;
import com.business.runway.RunwayApis;
import com.business.runway.RunwayHttpUtil;
import com.business.runway.RunwayOfficeApis;
import com.business.runway.model.*;
import com.business.sd.SDApisUtil;
import com.business.sd.model.SDToVideoBO;
import com.business.tengxunyun.BAliYunUtil;
import com.business.tengxunyun.BTengXunUtil;
import com.business.utils.*;
import com.business.zhipu.ZhiPuApiUtil;
import com.business.zhipu.model.ZhiPuRequestBO;
import com.nacos.auth.JwtNewUtil;
import com.nacos.config.OssClientConfig;
import com.nacos.config.PiKaConfig;
import com.nacos.constant.CommonConst;
import com.nacos.ddimg.ImageModelUtil;
import com.nacos.ddimg.ImgDrawUtil;
import com.nacos.ddimg.model.ImgScaleDTO;
import com.nacos.ddimg.model.ImgVideoScaleDTO;
import com.nacos.enums.*;
import com.nacos.exception.E;
import com.nacos.exception.IBusinessException;
import com.nacos.model.dto.ImgVideoDTO;
import com.nacos.model.dto.ImgVideoOptDTO;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.AsyncService;
import com.nacos.service.CheckBalanService;
import com.nacos.service.IHandleVideoService;
import com.nacos.service.IImgVideoService;
import com.nacos.service.mp.IUserDDRecordService;
import com.nacos.utils.OSSUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.business.tywx.TongYiWanXiangApiUtill;
import com.business.tywx.model.TongYiWanXiangVideoRequestBO;
import java.util.Collections;

@Service
@Slf4j
public class ImgVideoServiceImpl implements IImgVideoService {

    @Resource
    private CheckBalanService checkBalanService;
    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;
    @Resource
    private ImgDrawDetlMapper imgDrawDetlMapper;
    @Resource
    private VideoModelConfigMapper videoModelConfigMapper;
    @Resource
    private AsyncService asyncService;
    @Resource
    private IHandleVideoService handleVideoService;
    @Resource
    private IUserDDRecordService userDDRecordService;
    @Resource
    private AudioModelConfigMapper audioModelConfigMapper;
    @Resource
    private AudioHomeDataMapper audioHomeDataMapper;
    @Resource
    private UserPrivateConfigMapper userPrivateConfigMapper;
    @Resource
    private AudioGiveRecordMapper audioGiveRecordMapper;

    @Value("${dreamfactory.pull-task-switch}")
    public Boolean pullTaskSwitch = false;

    /**
     * 视频生成接口 - 主要入口方法
     * <p>
     * 根据传入的模型ID选择相应的视频生成实现：
     * - SD视频生成
     * - Leonardo视频生成
     * - Byte视频生成
     * - Domo视频生成
     * - Runway视频生成
     * - Luma视频生成
     * - Pika视频生成
     * <p>
     * 处理流程：
     * 1. 参数校验
     * 2. 提示词处理和翻译
     * 3. 获取模型配置
     * 4. 根据VIP级别和模型判断是否需要扣除点数
     * 5. 根据模型类型分发到不同的处理方法
     * 
     * @param imgVideoDTO 视频生成请求参数对象
     * @return 包含生成任务信息的结果对象
     * @throws Throwable 异常情况
     */
    @Override
    @Transactional(rollbackFor = { Exception.class, E.class })
    public Result<Object> videoGeneration(ImgVideoDTO imgVideoDTO) throws Throwable {
        if (imgVideoDTO == null) {
            return Result.ERROR("参数不能为空");
        }
        if (imgVideoDTO.getUserId() == null) {
            imgVideoDTO.setUserId(JwtNewUtil.getUserId());
        }
        if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_SD_BASICS.getValue()
                || imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_LE_BASICS.getValue()
                || imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_BYTE_LENS.getValue()) {
            // 校验是有图片
            if (imgVideoDTO.getInitImgUrls() == null || imgVideoDTO.getInitImgUrls().isEmpty()) {
                return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
            }
            if (imgVideoDTO.getWidth() == null || imgVideoDTO.getHeight() == null) {
                return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
            }
        }
        if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DOMO_BASICS.getValue()) {
            // 校验是有视频
            if (imgVideoDTO.getVideoUrl() == null || imgVideoDTO.getVideoUrl().isEmpty()) {
                return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
            }
        }
        // 兼容老的达芬奇
        if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY2_BASICS.getValue()
                && imgVideoDTO.getPrompt() == null) {
            return Result.ERROR("请升级新版本使用此模型！");
        }
        // 如果提示词不为null或空则校验合法性
        if (imgVideoDTO.getPrompt() != null) {
            if (!BStringUtil.isStringWithinLimitVideo(imgVideoDTO.getPrompt())) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            }
            if (BTengXunUtil.textToExamineFailByVideo(imgVideoDTO.getPrompt(), imgVideoDTO.getUserId())) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            }
            // if(BAliYunUtil.videoDetection(imgVideoDTO.getPrompt())){
            // return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            // }
            if (BStringUtil.isChinese(imgVideoDTO.getPrompt())) {
                imgVideoDTO.setPromptUse(BAliYunUtil.textToEnglish(imgVideoDTO.getPrompt()));
            }
            if (StringUtils.isBlank(imgVideoDTO.getPromptUse())) {
                imgVideoDTO.setPromptUse(imgVideoDTO.getPrompt());
            }
        }
        if (imgVideoDTO.getNegativeprompt() != null) {
            if (!BStringUtil.isStringWithinLimitVideo(imgVideoDTO.getNegativeprompt())) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            }
            if (BTengXunUtil.textToExamineFailByVideo(imgVideoDTO.getNegativeprompt(), imgVideoDTO.getUserId())) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            }
            if (BStringUtil.isChinese(imgVideoDTO.getNegativeprompt())) {
                imgVideoDTO
                        .setNegativepromptUse(BTengXunUtil.textToEnglish(imgVideoDTO.getNegativeprompt(), "zh", "en"));
            }
        }

        // 效验模型跳转不同的执行接口
        VideoModelConfigPO videoModelConfigPO = videoModelConfigMapper
                .selectOne(new LambdaQueryWrapper<VideoModelConfigPO>()
                        .eq(VideoModelConfigPO::getAttribute, imgVideoDTO.getModelId())
                        .eq(VideoModelConfigPO::getIsShow, CommonIntEnum.SHOW_TRUE.getIntValue()));
        if (videoModelConfigPO == null) {
            return Result.ERROR("未找到匹配的模型");
        }

        // 校验用户是否为VIP用户
        // boolean isVip = userDDRecordService.getUserIsVip();
        Integer memberLevel = userDDRecordService.getUserVipInfo();
        boolean isVip = (memberLevel != null && memberLevel > 0);
        VideoSpeedVO videoSpeed = getVideoSpeed(imgVideoDTO.getModelKey(), videoModelConfigPO.getSpeed());
        // 24小时视频失效时间的计算
        // imgVideoDTO.setPrompt(BStringUtil.getRemainingTime(DateUtil.date()));
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecord(imgVideoDTO);
        // 初始化图片URL
        if (imgVideoDTO.getInitImgUrls() != null && imgVideoDTO.getInitImgUrls().size() > 1) {
            List<String> initImgUrls = imgVideoDTO.getInitImgUrls();
            if (initImgUrls.size() > 1) {
                String even = IntStream.range(0, initImgUrls.size())
                        .filter(i -> i % 2 == 0) // 保留偶数索引 (从0开始)
                        .mapToObj(initImgUrls::get)
                        .filter(value -> value != null && !value.isEmpty()) // 过滤掉空值和空字符串
                        .findFirst().orElse("");
                String odd = IntStream.range(0, initImgUrls.size())
                        .filter(i -> i % 2 == 1) // 保留奇数数索引 (从0开始)
                        .mapToObj(initImgUrls::get)
                        .filter(value -> value != null && !value.isEmpty()) // 过滤掉空值和空字符串
                        .findFirst().orElse("");
                List<String> initImgUrlsList = new ArrayList<>();
                if (StringUtils.isNotBlank(even)) {
                    URL url = new URL(even);
                    String resBody = OSSUtils.getImageInfo(OssClientConfig.ENDPOINT, OssClientConfig.ACCESSKEYID,
                            OssClientConfig.SECRETACCESSKEY, OssClientConfig.BUCKET_NAME,
                            url.getPath().substring(1, url.getPath().length()));
                    if (resBody != null) {
                        JSONObject jsonObject = JSONObject.parseObject(resBody);
                        imgVideoDTO.setWidth(jsonObject.getJSONObject("ImageWidth").getInteger("value"));
                        imgVideoDTO.setHeight(jsonObject.getJSONObject("ImageHeight").getInteger("value"));
                        imgDrawRecordPO.setWidth(imgVideoDTO.getWidth());
                        imgDrawRecordPO.setHeight(imgVideoDTO.getHeight());
                        imgDrawRecordPO
                                .setWhDivide(ImgDrawUtil.getWhDivide(imgVideoDTO.getWidth(), imgVideoDTO.getHeight()));
                    }
                } else if (StringUtils.isBlank(even) && StringUtils.isNotBlank(odd)) {
                    URL url = new URL(odd);
                    String resBody = OSSUtils.getImageInfo(OssClientConfig.ENDPOINT, OssClientConfig.ACCESSKEYID,
                            OssClientConfig.SECRETACCESSKEY, OssClientConfig.BUCKET_NAME,
                            url.getPath().substring(1, url.getPath().length()));
                    if (resBody != null) {
                        JSONObject jsonObject = JSONObject.parseObject(resBody);
                        imgVideoDTO.setWidth(jsonObject.getJSONObject("ImageWidth").getInteger("value"));
                        imgVideoDTO.setHeight(jsonObject.getJSONObject("ImageHeight").getInteger("value"));
                        imgDrawRecordPO.setWidth(imgVideoDTO.getWidth());
                        imgDrawRecordPO.setHeight(imgVideoDTO.getHeight());
                        imgDrawRecordPO
                                .setWhDivide(ImgDrawUtil.getWhDivide(imgVideoDTO.getWidth(), imgVideoDTO.getHeight()));
                    }
                }
                initImgUrlsList.add(even);
                initImgUrlsList.add(odd);
                imgVideoDTO.setInitImgUrls(initImgUrlsList);
            }
        }
        imgDrawRecordPO.setInitImgUrls(
                imgVideoDTO.getInitImgUrls() != null ? JSONArray.toJSONString(imgVideoDTO.getInitImgUrls()) : null);

        // 扣除用户点子 ===== 查询点子规则数据
        double dzQuantity = BDDUseNumEnum.getBDDUseNumEnumByModel(imgVideoDTO.getModelId(), isVip);
        int optAttribute = ImgOptModelEnum.getOptAttributeByModelId(imgVideoDTO.getModelId());// 获取操作
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(optAttribute);
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ONE.getDtoKey())
                .remark(optTitleOne).build();
        // 校验模型是否可以使用休闲模式
        boolean canUseDomoBasics = false;
        boolean canUseDomoApi = false;
        boolean canUseRunwayApi = false;

        // 判断是否为休闲模式相关的模型(PIKA/DOMO/RUNWAY)
        if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_PIKA_BASICS.getValue() ||
                imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DOMO_BASICS.getValue() ||
                imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY2_BASICS.getValue()) {

            // 校验速度模式是否正确
            if (videoSpeed == null) {
                return Result.ERROR("休闲模式参数错误，请联系管理员");
            }

            // 判断具体是哪种模型
            boolean isPikaBasics = imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_PIKA_BASICS.getValue();
            boolean isDomoBasics = imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DOMO_BASICS.getValue();
            boolean isRunwayBasics = imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY2_BASICS
                    .getValue();

            // PIKA模型：标准会员及以上可以免费使用休闲模式
            boolean canUsePikaBasics = isPikaBasics &&
                    memberLevel >= VipGradeEnum.MEMBER_NORM.getIntValue() &&
                    videoSpeed.getValue().equals(BImgModelsEnum.MJ_MODEL_RELAX.getStrValue());

            // DOMO模型：专业会员及以上可以免费使用休闲模式
            canUseDomoBasics = isDomoBasics &&
                    (memberLevel >= VipGradeEnum.MEMBER_PROFE.getIntValue() &&
                            videoSpeed.getValue().equals(BImgModelsEnum.MJ_MODEL_RELAX.getStrValue()));

            // DOMO API：专业会员可以使用休闲模式，或任何人都可以使用快速模式(需扣点)
            canUseDomoApi = isDomoBasics &&
                    ((memberLevel >= VipGradeEnum.MEMBER_PROFE.getIntValue() &&
                            videoSpeed.getValue().equals(BImgModelsEnum.MJ_MODEL_RELAX.getStrValue()) ||
                            videoSpeed.getValue().equals(BImgModelsEnum.MJ_MODEL_FAST.getStrValue())));

            // RUNWAY模型：专业会员可以免费使用休闲模式
            canUseRunwayApi = isRunwayBasics &&
                    ((memberLevel >= VipGradeEnum.MEMBER_PROFE.getIntValue() &&
                            videoSpeed.getValue().equals(BImgModelsEnum.MJ_MODEL_RELAX.getStrValue())));

            // 如果满足任一免费条件(对应会员等级+休闲模式)，则不扣除点数
            if (canUsePikaBasics || canUseDomoBasics || canUseRunwayApi) {
                dzQuantity = 0;
                checkBalanService.checkUserNew(imgDrawRecordPO.getUserId(), dzQuantity, flowRecordSub);
            } else {
                // 不满足免费条件，需要扣除相应点数
                checkBalanService.checkUser(imgDrawRecordPO.getUserId(), dzQuantity, flowRecordSub);
            }
        }
        // 非休闲模式模型(Luma)
        else if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_LUMA_BASICS.getValue()) {
            // if (!isVip) {
            // return Result.ERROR(CommonResultEnum.VIDEO_API_SVIP_ERROR.getValue());
            // }
            checkBalanService.checkUser(imgDrawRecordPO.getUserId(), dzQuantity, flowRecordSub);
        }
        // 非休闲模式模型(SD/LE/Byte)
        else {
            checkBalanService.checkUser(imgDrawRecordPO.getUserId(), dzQuantity, flowRecordSub);
        }
        imgDrawRecordPO.setUseDdQua(dzQuantity);
        imgDrawRecordPO.setOptAttribute(optAttribute);
        try {
            // TODO 新版的不用SD视频模型 以后弃用
            // SD视频模型
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_SD_BASICS.getValue()) {
                imgDrawRecordPO.setDescription("sd-video " + imgVideoDTO.getPrompt());
                return sdVideoGeneration(imgVideoDTO, imgDrawRecordPO);
            }
            // LE视频模型
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_LE_BASICS.getValue()) {
                HashMap<Long, String> dictConfigMap = BThirdPartyKey
                        .getSecretKeyInfo(DictConfigEnum.LE_DRAW_KEY.getDictType());
                if (dictConfigMap == null) {
                    log.info("LE没有可用的key: {}", "请立即更换");
                    return Result.ERROR("模型升级维护中...");
                }
                imgDrawRecordPO.setDescription("le-video " + imgVideoDTO.getPrompt());
                return leVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO, dictConfigMap);
            }
            // Byte视频模型
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_BYTE_LENS.getValue()) {
                imgDrawRecordPO.setDescription("byte-video " + imgVideoDTO.getPrompt());
                return byteVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO);
            }
            // Pika视频模型
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_PIKA_BASICS.getValue()) {
                imgDrawRecordPO.setDescription("pika-video " + imgVideoDTO.getPrompt());
                if (imgVideoDTO.getInitImgUrls() == null && imgVideoDTO.getPrompt() == null
                        && imgVideoDTO.getVideoUrl() == null) {
                    return Result.ERROR("参数不能为空，请重新生成");
                }
                if (imgVideoDTO.getVideoUrl() != null) {
                    if (!BFileUtil.isMp4Format(imgVideoDTO.getVideoUrl())) {
                        return Result.ERROR("视频格式不是MP4，请重新上传");
                    }
                    if (BFileUtil.getVideoSize(imgVideoDTO.getVideoUrl()) > 10) {
                        return Result.ERROR("视频文件不能超过10MB，请重新上传");
                    }
                }
                return pikaVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO);
            }
            // 哆莫视频模型
            if ((imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DOMO_BASICS.getValue())) {
                if (canUseDomoApi) {
                    imgDrawRecordPO.setDescription("domo-video " + imgVideoDTO.getPrompt());
                    return domoVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO);
                } else {
                    return Result.ERROR("哆莫模型休闲模式只能开专业会员以上权限！");
                }
            }
            // Luma视频模型
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_LUMA_BASICS.getValue()) {
                if (StringUtils.isNotBlank(imgVideoDTO.getPromptUse()) && imgVideoDTO.getPromptUse().length() < 3) {
                    return Result.ERROR(CommonResultEnum.VIDEO_PROMPT_ERROR.getValue());
                }
                imgDrawRecordPO.setDescription("luma-video " + imgVideoDTO.getPrompt());
                return lumaVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO);
            }
            // Runway视频模型
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY2_BASICS.getValue()) {
                imgDrawRecordPO.setDescription("runway " + imgVideoDTO.getPrompt());
                // 如果是高级 则是真3at模型
                // if(2 == imgVideoDTO.getVersion().intValue()) {
                // imgVideoDTO.setModelId(ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY3AT_BASICS.getValue());
                //// if(imgVideoDTO.getInitImgUrls() == null ||
                // imgVideoDTO.getInitImgUrls().isEmpty()){
                //// return Result.ERROR("达芬奇高级模式必须传图片！");
                //// }
                // }
                if ((imgVideoDTO.getInitImgUrls() == null || imgVideoDTO.getInitImgUrls().isEmpty())
                        && StringUtils.isBlank(imgVideoDTO.getPrompt())) {
                    return Result.ERROR("达芬奇必须传指令或图片！");
                }
                return runwayVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO, videoSpeed);
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 将用户使用点子数返回
            FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ZERO.getDtoKey())
                    .remark(optTitleOne).build();
            asyncService.updateRemainingTimes(imgDrawRecordPO.getUserId(), imgDrawRecordPO.getUseDdQua(), flowRecordPO);
            log.error("==*==生成视频失败：", e.getMessage(), e);
            return Result.ERROR("视频生成失败，请重试");
        }
        return Result.ERROR(CommonResultEnum.NOT_ERROR.getValue());
    }

    /**
     * SD模型视频生成方法
     * <p>
     * 使用SD模型将图片转换为视频，处理步骤：
     * 1. 调整图片尺寸以适应SD模型
     * 2. 下载并处理图片数据
     * 3. 调用SD API提交视频生成任务
     * 4. 保存任务记录并返回任务信息
     * 
     * @param imgVideoDTO     视频生成请求参数
     * @param imgDrawRecordPO 图片绘制记录对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> sdVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO)
            throws Exception {
        try {
            // 校验跳转视频接口执行img-video
            Map<String, Integer> integerMap = BStringUtil.getSimilarWidthHeight(imgVideoDTO.getWidth(),
                    imgVideoDTO.getHeight());
            log.info("sd视频尺寸：width ｛｝, height ｛｝", integerMap.get("width"), integerMap.get("height"));
            int x = (imgVideoDTO.getWidth() - integerMap.get("width")) / 2;
            int y = (imgVideoDTO.getHeight() - integerMap.get("height")) / 2;
            String suffix = "/crop,x_" + x + ",y_" + y + ",w_" + integerMap.get("width") + ",h_"
                    + integerMap.get("height");
            String url = imgVideoDTO.getInitImgUrls().get(0) + "?x-oss-process=image/format,png" + suffix;
            byte[] imageData = BFileUtil.downloadImageGetBytePlayVideo(url);
            String videoJobId = SDApisUtil.postSdToVideo(imageData, SDToVideoBO.initSdToVideoBO(imgVideoDTO.getSeed(),
                    imgVideoDTO.getCfgScale(), imgVideoDTO.getMotionBucketId()));
            log.info("==*==SD视频任务id= ｛｝", videoJobId);
            if (videoJobId == null) {
                throw new E("提交视频失败，请重试");
            }
            imgDrawRecordPO.setVideoJobId(videoJobId);
        } catch (BFileUtil.FileSizeException e) {
            log.error("sd文件过大：｛｝", e.getMessage());
            throw new E("图片过大，请重新上传");
        }
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());
            ;
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E(CommonResultEnum.getSystemErrorMsg());
    }

    private Result<Object> leVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO, Map<Long, String> dictConfigMap) throws Exception {
        int[] integerMap = BStringUtil.getCompressedImageWidthAndHeight(imgVideoDTO.getWidth(),
                imgVideoDTO.getHeight());
        String suffix = "/resize,w_" + integerMap[0]; // +",h_"+ integerMap[1]; 高暂时不用
        String url = imgVideoDTO.getInitImgUrls().get(0) + "?x-oss-process=image/format,png" + suffix;
        byte[] imageData = BFileUtil.downloadImageGetBytePlayVideo(url);
        Map<String, String> stringMap = LeonardoUtil
                .getPresignedUrlUploadingImg(dictConfigMap.get(DictConfigEnum.LE_DRAW_KEY.getDictKey()));
        if (stringMap == null) {
            throw new E("视频初始化失败，请重试");
        }
        boolean isSuccess = LeonardoUtil.presignedUrlUploadingImg(stringMap.get("url"), stringMap.get("fields"),
                imageData);
        if (!isSuccess) {
            throw new E("上传视频图片失败，请重试");
        }
        int[] intArray = Arrays.stream(imgVideoDTO.getWeight()).mapToInt(Double::intValue).toArray();

        String videoJobId = LeonardoUtil.postLeonardoImageToVideo(
                new LeonardoBO(stringMap.get("imageId"), true, intArray[0]),
                dictConfigMap.get(DictConfigEnum.LE_DRAW_KEY.getDictKey()));
        imgDrawRecordPO.setLeJobId(videoJobId);
        log.info("==*==LE视频任务id= ｛｝", videoJobId);
        if (videoJobId == null) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        imgDrawRecordPO.setVideoJobId(videoJobId);

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setImage(imgVideoDTO.getInitImgUrls() == null ? null : imgVideoDTO.getInitImgUrls().get(0));
        imgVideoVO.setWidth(imgVideoDTO.getWidth());
        imgVideoVO.setHeight(imgVideoDTO.getHeight());
        imgVideoVO.setWeight(imgVideoDTO.getWeight());
        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName() + " 权重:" + intArray[0]);

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());
            ;
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("生成视频失败，请重试");
    }

    private Result<Object> byteVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {
        // 装载返回实体对象
        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
        imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setImage(imgVideoDTO.getInitImgUrls() == null ? null : imgVideoDTO.getInitImgUrls().get(0));
        imgVideoVO.setWidth(imgVideoDTO.getWidth());
        imgVideoVO.setHeight(imgVideoDTO.getHeight());
        imgVideoVO.setWeight(imgVideoDTO.getWeight());
        int[] intArray = Arrays.stream(imgVideoDTO.getWeight()).mapToInt(Double::intValue).toArray();
        int frameNum = intArray[0];
        int fps = intArray[1];
        int mode = 0;

        if (videoModelConfigPO.getSpecialEffects() != null) {
            List<VideoSpecialEffectsVO> specialEffectsList = VideoSpecialEffectsVO
                    .videoSpecialEffectsList(videoModelConfigPO.getSpecialEffects());
            Optional<VideoSpecialEffectsVO> matchingSpecialEffects = specialEffectsList.stream()
                    .filter(specialEffect -> specialEffect.getKey() == imgVideoDTO.getLensValue().intValue())
                    .findFirst();
            if (matchingSpecialEffects.isPresent()) {
                mode = Integer.valueOf(matchingSpecialEffects.get().getValue());
                imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName() + " 特效:"
                        + matchingSpecialEffects.get().getName() + " 帧数:" + frameNum + " 帧率:" + fps);
            }
        }

        imgVideoVO.setLensValue(imgVideoDTO.getLensValue());
        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));

        int[] integerMap = BStringUtil.getCompressedImageWidthAndHeight(imgVideoDTO.getWidth(),
                imgVideoDTO.getHeight());
        String suffix = "/resize,w_" + integerMap[0]; // +",h_"+ integerMap[1]; 高暂时不用
        String url = imgVideoDTO.getInitImgUrls().get(0) + "?x-oss-process=image/format,jpg" + suffix;
        byte[] imageData = BFileUtil.downloadImageGetBytePlayVideo(url);

        asyncService.byteVideoGeneration(Base64.getEncoder().encodeToString(imageData), imgDrawRecordPO,
                imgDrawHistoryVO, mode, frameNum, fps);
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E(CommonResultEnum.getSystemErrorMsg());
    }

    private Result<Object> domoVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {
        // 获取配置
        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                .getSecretKeyInfo(DictConfigEnum.DOMO_API_KEY.getDictType());
        if (dictConfigMap == null) {
            log.info("DOMO没有可用的key: {}", "请立即更换");
            throw new E("DOMO视频模型升级维护中...");
        }
        String modelType = null;
        String referMode = null;
        Integer styleValue = null;
        String scalesValue = null;
        StringBuffer prompt = new StringBuffer();

        if (videoModelConfigPO.getVideoStyles() != null) {
            List<VideoSpecialEffectsVO> specialEffectsList = VideoSpecialEffectsVO
                    .videoSpecialEffectsList(videoModelConfigPO.getVideoStyles());
            Optional<VideoSpecialEffectsVO> matchingSpecialEffects = specialEffectsList.stream()
                    .filter(specialEffect -> specialEffect.getKey() == imgVideoDTO.getStyleKey().intValue())
                    .findFirst();
            if (matchingSpecialEffects.isPresent()) {
                styleValue = Integer.valueOf(matchingSpecialEffects.get().getValue());
                prompt.append(videoModelConfigPO.getModelName()).append(" ")
                        .append(matchingSpecialEffects.get().getName()).append(" ");
            }
        }
        if (videoModelConfigPO.getSpeed() != null) {
            List<VideoSpeedVO> videoSpeedVOList = VideoSpeedVO.videoSpeedVOList(videoModelConfigPO.getSpeed());
            Optional<VideoSpeedVO> matchingVideoSpeeds = videoSpeedVOList.stream()
                    .filter(speedVO -> speedVO.getKey() == imgVideoDTO.getModelKey().intValue()).findFirst();
            if (matchingVideoSpeeds.isPresent()) {
                modelType = matchingVideoSpeeds.get().getValue();
                prompt.append(matchingVideoSpeeds.get().getName()).append(" ");
            }
        }
        if (videoModelConfigPO.getVideoScales() != null) {
            List<ImgScaleDTO> videoImgScaleList = ImageModelUtil.getImgScaleDTOs(videoModelConfigPO.getVideoScales());
            assert videoImgScaleList != null;
            Optional<ImgScaleDTO> matchingImgScales = videoImgScaleList.stream()
                    .filter(scaleDTO -> scaleDTO.getKey() == imgVideoDTO.getScalesKey().intValue()).findFirst();
            if (matchingImgScales.isPresent()) {
                if (matchingImgScales.get().getValue() == null || matchingImgScales.get().getValue().isEmpty()) {
                    imgDrawRecordPO
                            .setWhDivide(ImgDrawUtil.getWhDivide(imgVideoDTO.getWidth(), imgVideoDTO.getHeight()));
                } else {
                    imgDrawRecordPO
                            .setWhDivide(ImgDrawUtil.getWhDivide(Integer.valueOf(matchingImgScales.get().getWidth()),
                                    Integer.valueOf(matchingImgScales.get().getHeight())));
                }
                scalesValue = matchingImgScales.get().getValue();
                prompt.append(matchingImgScales.get().getWidth()).append(":")
                        .append(matchingImgScales.get().getHeight());
            }
        }
        if (videoModelConfigPO.getReferMode() != null) {
            List<VideoReferModeVO> videoReferModeVOList = VideoReferModeVO
                    .videoReferModeVOList(videoModelConfigPO.getReferMode());
            Optional<VideoReferModeVO> matchingReferModes = videoReferModeVOList.stream()
                    .filter(referModeVO -> referModeVO.getKey() == imgVideoDTO.getReferModeKey().intValue())
                    .findFirst();
            if (matchingReferModes.isPresent()) {
                referMode = matchingReferModes.get().getValue();
            }
        }
        if (modelType == null || styleValue == null || referMode == null || scalesValue == null) {
            throw new E("选择的风格不存在，请联系客服");
        }
        imgDrawRecordPO.setPromptInit(prompt.toString());

        String domoAiImageUrl = DomoApis.getDomoAiImageUrl(dictConfigMap.get(DictConfigEnum.DOMO_API_KEY.getDictKey()),
                imgVideoDTO.getVideoUrl());
        if (domoAiImageUrl == null) {
            throw new E("上传失败，请重试");
        }
        VideoRequest videoRequest = new VideoRequest(domoAiImageUrl, imgVideoDTO.getPromptUse(), styleValue,
                imgVideoDTO.getDuration(), referMode, scalesValue, modelType);
        if (StringUtils.isBlank(videoRequest.getPrompt())) {
            videoRequest.setPrompt(imgVideoDTO.getPrompt());
        }
        JsonResponseJobVideo jsonResponseJobVideo = DomoApis.postToGenerateDomoVideo(
                dictConfigMap.get(DictConfigEnum.DOMO_API_KEY.getDictKey()),
                videoRequest);
        if (jsonResponseJobVideo == null || jsonResponseJobVideo.getData() == null
                || jsonResponseJobVideo.getData().getId() == null) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        imgDrawRecordPO.setVideoJobId(jsonResponseJobVideo.getData().getId());

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setStyleKey(imgVideoDTO.getStyleKey());
        imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        imgVideoVO.setDuration(imgVideoDTO.getDuration());
        imgVideoVO.setModelKey(imgVideoDTO.getModelKey());
        imgVideoVO.setVideoDuration(imgVideoDTO.getVideoDuration());
        imgVideoVO.setReferModeKey(imgVideoDTO.getReferModeKey());
        imgVideoVO.setVideoUrl(imgVideoDTO.getVideoUrl());
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());

        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName() + " " + imgVideoDTO.getPrompt());
        imgDrawRecordPO.setRemark("平均生成时间：" + jsonResponseJobVideo.getData().getGenerateInfo().getPredictionTime());
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * Runway视频生成方法
     * <p>
     * 使用Runway模型将图片转换为视频，处理步骤：
     * 1. 获取Runway API令牌
     * 2. 处理图片URL
     * 3. 调用Runway API提交视频生成任务
     * 4. 保存任务记录并返回任务信息
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置
     * @param videoSpeed         视频速度
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> runwayVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO, VideoSpeedVO videoSpeed) throws Exception {
        // 获取Runway API令牌
        String token = RedisUtil.getValue(BRedisKeyEnum.REDIS_RUNWAY_TOKEN_KEY.getKey());
        String downPicUrl = null;
        if (imgVideoDTO.getInitImgUrls() != null && !imgVideoDTO.getInitImgUrls().isEmpty()) {
            List<String> initImgUrls = imgVideoDTO.getInitImgUrls();
            if (initImgUrls.size() > 1) {
                // 判断上传的是不是亚马逊的图片地址，如果是则不需要再上传亚马逊
                String last = initImgUrls.getLast();
                if (StringUtils.isNotBlank(last) && last.contains("previews")) {

                } else {
                    if (StringUtils.isNotBlank(initImgUrls.getFirst())) {
                        downPicUrl = initImgUrls.getFirst();
                        initImgUrls.clear();
                        initImgUrls.add(downPicUrl);
                    } else if (StringUtils.isNotBlank(initImgUrls.getLast())) {
                        downPicUrl = initImgUrls.getLast();
                        initImgUrls.clear();
                        initImgUrls.add(downPicUrl);
                    }
                }
            } else if (initImgUrls.size() == 1) {
                if (StringUtils.isNotBlank(initImgUrls.getFirst())) {
                    downPicUrl = initImgUrls.getFirst();
                    initImgUrls.clear();
                    initImgUrls.add(downPicUrl);
                } else {
                    downPicUrl = initImgUrls.getLast();
                    initImgUrls.clear();
                    initImgUrls.add(downPicUrl);
                }
                imgVideoDTO.setInitImgUrls(initImgUrls);
            }
        }

        if (token == null) {
            log.info("runway没有可用的key: {}", "请立即更换");
            throw new E("runway视频模型升级维护中...");
        }

        if (StringUtils.isNotBlank(downPicUrl)) {
            try {
                boolean downloadImgLock = RedisUtil
                        .acquireLock(GlobalRedisKeyEnum.DOWNLOAD_LOCK_REDIS_RUNWAY_IMG.getStrKey(), 120);
                if (downloadImgLock) {
                    // 将图片下载到本地
                    File imgFile = BFileUtil.downloadFileFromURL(downPicUrl, "runway-download.png");
                    // 将图片上传到亚马逊云存储
                    String amazonUrl = RunwayApis.uploadImageToRunWayByOssUrl(imgFile, token);
                    if (StringUtils.isNotBlank(amazonUrl)) {
                        List<String> initImgUrls = imgVideoDTO.getInitImgUrls();
                        initImgUrls.add(amazonUrl);
                    }
                }
            } finally {
                RedisUtil.releaseLock(GlobalRedisKeyEnum.DOWNLOAD_LOCK_REDIS_RUNWAY_IMG.getStrKey());
            }
        }

        String videoJobId = null;
        RunWayTaskReq runWayTaskReq = new RunWayTaskReq();
        String runwayAccountInfo = RunwayHttpUtil.getRunwayAccountInfo(token);
        if (StringUtils.isBlank(runwayAccountInfo)) {
            log.info("postToGenerateRunwayVideo  getRunwayAccountInfo response={}", runwayAccountInfo);
            throw new E("runway账号信息获取失败，请重试" + ", token=" + token);
        }

        imgDrawRecordPO.setWidth(imgVideoDTO.getWidth());
        imgDrawRecordPO.setHeight(imgVideoDTO.getHeight());
        imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(imgVideoDTO.getWidth(), imgVideoDTO.getHeight()));

        // 判断Credits是否耗尽，如果耗尽直接走无限模式
        String key = RedisUtil.REDIS_RUNWAY_ACCOUNT_PREFIX + runwayAccountInfo + "-" + BDateUtil.getYearAndMonth();
        String value = RedisUtil.getValue(key);
        boolean flag = true;
        if ("true".equals(value)) {
            flag = false;
        }
        // gen3a方式生成视频根据modelId和loop判断
        if (imgVideoDTO.getVersion().intValue() == 2) {
            RunWayTaskOptionsReq runWayTaskOptionsReq = new RunWayTaskOptionsReq();
            // 有图片
            if (imgVideoDTO.getInitImgUrls() != null && !imgVideoDTO.getInitImgUrls().isEmpty()
                    && imgVideoDTO.getInitImgUrls().size() == 2) {

                runWayTaskOptionsReq
                        .setName(VideoTaskTypeEnum.TASK_TYPE_GEN3A.getValue() + ", " + imgVideoDTO.getPromptUse());
                runWayTaskOptionsReq.setTextPrompt(imgVideoDTO.getPromptUse());
                runWayTaskOptionsReq.setEnhancePrompt(true);
                runWayTaskOptionsReq.setInitImage(imgVideoDTO.getInitImgUrls().getLast());
                if (imgVideoDTO.getImageAsEndFrame()) {
                    runWayTaskOptionsReq.setImageAsEndFrame(true);
                }
                imgDrawRecordPO.setHeight(768);
                imgDrawRecordPO.setWidth(1366);
                imgDrawRecordPO
                        .setWhDivide(ImgDrawUtil.getWhDivide(imgDrawRecordPO.getWidth(), imgDrawRecordPO.getHeight()));
                List<String> list = new ArrayList<>();
                list.add(imgVideoDTO.getInitImgUrls().getFirst());
                imgDrawRecordPO.setInitImgUrls(JSONObject.toJSONString(list));
            } else {
                // 无图片
                runWayTaskOptionsReq
                        .setName(VideoTaskTypeEnum.TASK_TYPE_GEN3A.getValue() + ", " + imgVideoDTO.getPromptUse());
                runWayTaskOptionsReq.setTextPrompt(imgVideoDTO.getPromptUse());
                runWayTaskOptionsReq.setEnhancePrompt(true);
                runWayTaskOptionsReq.setWidth(1366);
                runWayTaskOptionsReq.setHeight(768);
                imgDrawRecordPO.setHeight(768);
                imgDrawRecordPO.setWidth(1366);
                imgDrawRecordPO
                        .setWhDivide(ImgDrawUtil.getWhDivide(imgDrawRecordPO.getWidth(), imgDrawRecordPO.getHeight()));
            }
            // 快速模式使用runway的credits点
            if (videoSpeed.getValue().equals(BImgModelsEnum.MJ_MODEL_FAST.getStrValue()) && flag) {
                runWayTaskOptionsReq.setExploreMode(false);
                // runWayTaskOptionsReq.setAssetGroupName("CreditsMode");
            }
            runWayTaskReq.setTaskType(VideoTaskTypeEnum.TASK_TYPE_GEN3A.getValue());
            runWayTaskReq.setAsTeamId(runwayAccountInfo);
            runWayTaskReq.setOptions(runWayTaskOptionsReq);

            videoJobId = RunwayApis.postToGenerateRunwayVideo(runWayTaskReq, token);
        } else if (imgVideoDTO.getVersion().intValue() == 1) {
            // gen2方式生成视频
            RunWayTaskOptionsGen2OptionsReq req = new RunWayTaskOptionsGen2OptionsReq();
            if (videoModelConfigPO.getVideoScales() != null) {
                List<ImgScaleDTO> videoImgScaleList = ImageModelUtil
                        .getImgScaleDTOs(videoModelConfigPO.getVideoScales());
                assert videoImgScaleList != null;
                ImgScaleDTO matchingImgScales = videoImgScaleList.stream()
                        .filter(scaleDTO -> scaleDTO.getKey() == imgVideoDTO.getScalesKey().intValue()).findFirst()
                        .orElse(null);
                if (matchingImgScales != null) {
                    imgVideoDTO.setHeight(Integer.valueOf(matchingImgScales.getHeight()));
                    imgVideoDTO.setWidth(Integer.valueOf(matchingImgScales.getWidth()));
                }
            }
            // 无图片无设置镜头参数
            if ((imgVideoDTO.getInitImgUrls() == null || imgVideoDTO.getInitImgUrls().isEmpty()
                    || imgVideoDTO.getInitImgUrls().size() < 1) && StringUtils.isBlank(imgVideoDTO.getVideoLens())) {
                req.setMode(VideoTaskTypeEnum.TASK_TYPE_GEN2.getValue());
                req.setMotionScore(22);
                req.setUseMotionScore(true);
                req.setTextPrompt(imgVideoDTO.getPromptUse());
                req.setWidth(imgVideoDTO.getWidth());
                req.setHeight(imgVideoDTO.getHeight());
                imgDrawRecordPO.setHeight(imgVideoDTO.getHeight());
                imgDrawRecordPO.setWidth(imgVideoDTO.getWidth());
                imgDrawRecordPO
                        .setWhDivide(ImgDrawUtil.getWhDivide(imgDrawRecordPO.getWidth(), imgDrawRecordPO.getHeight()));
                // 无图片有设置镜头参数
            } else if ((imgVideoDTO.getInitImgUrls() == null || imgVideoDTO.getInitImgUrls().isEmpty()
                    || imgVideoDTO.getInitImgUrls().size() < 1) && StringUtils.isNotBlank(imgVideoDTO.getVideoLens())) {
                req.setMode(VideoTaskTypeEnum.TASK_TYPE_GEN2.getValue());
                req.setTextPrompt(imgVideoDTO.getPromptUse());
                req.setWidth(imgVideoDTO.getWidth());
                req.setHeight(imgVideoDTO.getHeight());
                imgDrawRecordPO.setHeight(imgVideoDTO.getHeight());
                imgDrawRecordPO.setWidth(imgVideoDTO.getWidth());
                imgDrawRecordPO
                        .setWhDivide(ImgDrawUtil.getWhDivide(imgDrawRecordPO.getWidth(), imgDrawRecordPO.getHeight()));
                Map<String, Integer> resultMap = BStringUtil.parseStringByColon(imgVideoDTO.getVideoLens());
                // 将 Map 转换为 JSON 字符串
                String jsonString = JSONObject.toJSONString(resultMap);
                // 将 JSON 字符串转换为 JSONObject
                JSONObject jsonObject = JSONObject.parseObject(jsonString);
                req.setMotionVector(jsonObject.toString());
                // 有图片无设置镜头参数
            } else if ((imgVideoDTO.getInitImgUrls() != null && imgVideoDTO.getInitImgUrls().size() == 2)
                    && StringUtils.isBlank(imgVideoDTO.getVideoLens())) {
                req.setMode(VideoTaskTypeEnum.TASK_TYPE_GEN2.getValue());
                req.setMotionScore(22);
                req.setUseMotionScore(true);
                req.setTextPrompt(imgVideoDTO.getPromptUse());
                req.setInitImage(imgVideoDTO.getInitImgUrls().getLast());
                req.setImagePrompt(imgVideoDTO.getInitImgUrls().getLast());
                List<String> list = new ArrayList<>();
                list.add(imgVideoDTO.getInitImgUrls().getFirst());
                imgDrawRecordPO.setInitImgUrls(JSONObject.toJSONString(list));
                // 有图片有设置镜头参数
            } else if ((imgVideoDTO.getInitImgUrls() != null && imgVideoDTO.getInitImgUrls().size() == 2)
                    && StringUtils.isNotBlank(imgVideoDTO.getVideoLens())) {
                req.setMode(VideoTaskTypeEnum.TASK_TYPE_GEN2.getValue());
                req.setTextPrompt(imgVideoDTO.getPromptUse());
                req.setInitImage(imgVideoDTO.getInitImgUrls().getLast());
                req.setImagePrompt(imgVideoDTO.getInitImgUrls().getLast());
                req.setUseMotionVectors(true);
                Map<String, Integer> resultMap = BStringUtil.parseStringByColon(imgVideoDTO.getVideoLens());
                // 将 Map 转换为 JSON 字符串
                String jsonString = JSONObject.toJSONString(resultMap);
                // 将 JSON 字符串转换为 JSONObject
                JSONObject jsonObject = JSONObject.parseObject(jsonString);
                req.setMotionVector(jsonObject.toString());
                List<String> list = new ArrayList<>();
                list.add(imgVideoDTO.getInitImgUrls().getFirst());
                imgDrawRecordPO.setInitImgUrls(JSONObject.toJSONString(list));
            }
            RunWayTaskOptionsReq runWayTaskOptionsReq = new RunWayTaskOptionsReq();
            runWayTaskOptionsReq.setName(VideoTaskTypeEnum.TASK_TYPE_GEN2.getValue() + " " + imgVideoDTO.getPromptUse()
                    + ", " + req.getStyle());
            // runWayTaskOptionsReq.setExploreMode(false);
            runWayTaskOptionsReq.setSeconds(4);
            runWayTaskOptionsReq.setAssetGroupName("Generative Video");
            runWayTaskOptionsReq.setSeed(null);
            runWayTaskOptionsReq.setImageAsEndFrame(null);
            runWayTaskOptionsReq.setResolution(null);
            runWayTaskOptionsReq.setWatermark(null);
            runWayTaskOptionsReq.setEnhancePrompt(null);
            runWayTaskOptionsReq.setGen2Options(req);
            // 快速模式使用runway的credits点
            if (videoSpeed.getValue().equals(BImgModelsEnum.MJ_MODEL_FAST.getStrValue()) && flag) {
                runWayTaskOptionsReq.setExploreMode(false);
                // runWayTaskOptionsReq.setAssetGroupName("CreditsMode");
            }
            runWayTaskReq.setTaskType(VideoTaskTypeEnum.TASK_TYPE_GEN2.getValue());
            runWayTaskReq.setInternal(false);
            runWayTaskReq.setAsTeamId(runwayAccountInfo);
            runWayTaskReq.setOptions(runWayTaskOptionsReq);

            videoJobId = RunwayApis.postToGenerateRunwayVideo(runWayTaskReq, token);
            imgDrawRecordPO.setSunoAccountId(Long.valueOf(runwayAccountInfo));
        }

        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        if (StringUtils.isNotBlank(videoJobId) && "429".equals(videoJobId)) {
        } else {
            imgDrawRecordPO.setVideoJobId(videoJobId);
        }

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setImage(imgVideoDTO.getInitImgUrls() != null ? imgVideoDTO.getInitImgUrls().getFirst() : null);
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setVersion(imgVideoDTO.getVersion());
        imgVideoVO.setVideoLens(imgVideoDTO.getVideoLens());
        imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        imgVideoVO.setImageAsEndFrame(imgVideoDTO.getImageAsEndFrame());

        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName() + " "
                + (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setRemark("平均生成时间：5-10分钟");
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            if (imgVideoDTO.getInitImgUrls() != null && !imgVideoDTO.getInitImgUrls().isEmpty()) {
                imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls().subList(0, 1));
                ;
            }
            // 将去runway生成不成功的放到队列里，定时器去处理
            if (StringUtils.isNotBlank(videoJobId) && "429".equals(videoJobId)) {
                JSONObject json = new JSONObject();
                json.put("id", imgDrawRecordPO.getId());
                json.put("runWayTaskReq", runWayTaskReq);
                boolean b = RedisUtil.rPushList(RedisUtil.REDIS_RUNWAY_SUBMIT_TASK_LIST, JSON.toJSONString(json));
            }
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * runway视频生成方法(新)
     * <p>
     * 使用runway模型生成视频
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> runwayVideoGenerationTransfer(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {
        String token = RedisUtil.getValue(BRedisKeyEnum.REDIS_RUNWAY_TOKEN_KEY.getKey());
        String assetGroupId = RedisUtil.getValue(BRedisKeyEnum.REDIS_RUNWAY_ASSET_GROUP_ID.getKey());
        if (token == null || assetGroupId == null) {
            log.info("runway没有可用的key: {}", "请立即更换");
            throw new E("runway视频模型升级维护中...");
        }
        Map<String, String> stringMap = getRunwayTaskCanStart(imgVideoDTO.getAsTeamId(), imgVideoDTO.getTaskId(),
                token);
        System.out.println("videoUrl:" + stringMap.get("videoUrl") + "=====" + stringMap.get("firstFrame"));
        imgVideoDTO.setVideoUrl(stringMap.get("videoUrl"));

        // 新的判断模型逻辑
        VideoSpecialEffectsVO videoStyle = null;
        if (imgVideoDTO.getStyleKey() != null) {

            videoStyle = getTextToVideoStyle(videoModelConfigPO.getVideoStyles(), imgVideoDTO.getStyleKey());
        }
        if (videoStyle == null) {
            log.info("未找到对应的视频风格");
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        StringBuilder textPrompt = new StringBuilder();
        if (imgVideoDTO.getPromptUse() == null || imgVideoDTO.getPromptUse().isEmpty()) {
            textPrompt.append(videoStyle.getNameEn());
        } else {
            textPrompt.append(imgVideoDTO.getPromptUse()).append(", ").append(videoStyle.getNameEn());
        }

        String videoJobId = null;
        RunWayVideoTransferReq runWayVideoTransferReq = new RunWayVideoTransferReq();
        runWayVideoTransferReq.setTaskType(VideoTaskTypeEnum.TASK_TYPE_GEN3AT.getValue());
        runWayVideoTransferReq.setInternal(false);
        String sessionId = RunwayApis.getRunWaySessionId(imgVideoDTO.getAsTeamId(), token);
        if (sessionId == null) {
            log.info("获取runway sessionid出错");
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        log.info("视频时长= " + imgVideoDTO.getVideoDuration());

        long seed = BUtils.getSeed();
        RunWayVideoTransferOptionsReq options = new RunWayVideoTransferOptionsReq();
        if (StringUtils.isNotBlank(imgVideoDTO.getVideoUrl())) {
            options.setName("Gen-3 Alpha Turbo " + seed + ", "
                    + textPrompt.substring(0, Math.min(20, textPrompt.length())) + " , M 5");
            options.setSeed(seed);
            options.setExploreMode(true);
            options.setWatermark(false);
            options.setEnhancePrompt(true);
            int seconds = (int) Math.round(imgVideoDTO.getVideoDuration());
            options.setSeconds(Math.min(seconds, 20));
            options.setVideoPrompt(imgVideoDTO.getVideoUrl());
            options.setTextPrompt(textPrompt.toString());
            options.setWidth(imgVideoDTO.getWidth());
            options.setHeight(imgVideoDTO.getHeight());
            float cfgScale = imgVideoDTO.getVideoWeight() == null ? (float) Integer.parseInt(videoStyle.getValue()) / 10
                    : (float) imgVideoDTO.getVideoWeight() / 10;
            options.setStructureTransformation(cfgScale); // 转会风格权重，0-10 传参数的时候是0.1，0.2，0.3，0.4，0.5，0.6，0.7，0.8，0.9，1
            options.setVideoPromptPreviewImage(stringMap.get("firstFrame"));
            if (imgVideoDTO.getWidth() < imgVideoDTO.getHeight()) {
                options.setFlip(true);
            }
            options.setAssetGroupId(assetGroupId);
        }
        runWayVideoTransferReq.setOptions(options);
        runWayVideoTransferReq.setAsTeamId(imgVideoDTO.getAsTeamId());
        runWayVideoTransferReq.setSessionId(sessionId);

        // videoJobId = RunwayApis.postToGenerateRunwayVideo(runWayVideoTaskReq, token);
        videoJobId = RunwayApis.postRunwayVideoToVideoGenerate(runWayVideoTransferReq, token);
        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorInfo());
        }
        if (StringUtils.isNotBlank(videoJobId) && "429".equals(videoJobId)) {
        } else {
            imgDrawRecordPO.setVideoJobId(videoJobId);
        }

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setImage(imgVideoDTO.getInitImgUrls() != null ? imgVideoDTO.getInitImgUrls().getFirst() : null);
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setVersion(imgVideoDTO.getVersion());
        imgVideoVO.setVideoLens(imgVideoDTO.getVideoLens());
        imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        imgVideoVO.setImageAsEndFrame(imgVideoDTO.getImageAsEndFrame());
        imgVideoVO.setStyleJson(imgVideoDTO.getStyleJson());

        imgDrawRecordPO.setSunoAccountId(Long.valueOf(imgVideoDTO.getAsTeamId()));
        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoStyle == null ? "" : videoStyle.getName());
        // imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName() + " "+
        // (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setRemark("平均生成时间：10-20分钟");
        // imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());
        imgDrawRecordPO.setInitImgUrls(null);

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            if (imgVideoDTO.getInitImgUrls() != null && !imgVideoDTO.getInitImgUrls().isEmpty()) {
                imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls().subList(0, 1));
                ;
            }
            // 将去runway生成不成功的放到队列里，定时器去处理
            if (StringUtils.isNotBlank(videoJobId) && "429".equals(videoJobId)) {

                JSONObject json = new JSONObject();
                json.put("id", imgDrawRecordPO.getId());
                json.put("runWayVideoTaskReq", runWayVideoTransferReq);
                boolean b = RedisUtil.rPushList(RedisUtil.REDIS_RUNWAY_SUBMIT_TASK_VIDEO_LIST, JSON.toJSONString(json));
            }
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * 获取可用的Runway任务
     * <p>
     * 获取可用的Runway任务，并返回任务状态和视频URL
     * 
     * @param asTeamId 团队ID
     * @param taskId   任务ID
     * @param token    令牌
     * @return 包含任务状态和视频URL的Map
     */
    private static Map<String, String> getRunwayTaskCanStart(Integer asTeamId, String taskId, String token) {
        while (true) {
            // 调用接口获取任务状态
            RunWayTaskResp runWayTaskResp = RunwayHttpUtil.getRunwayVideoJobState(asTeamId, taskId, token);
            if (runWayTaskResp != null && runWayTaskResp.getStatus().equals("SUCCEEDED")) {
                Map<String, String> stringMap = new HashMap<>();
                RunWayTaskArtifactsResp artifactsResp = runWayTaskResp.getArtifacts().getFirst();
                stringMap.put("videoUrl", artifactsResp.getUrl());
                stringMap.put("firstFrame", artifactsResp.getPreviewUrls().getFirst());
                System.out.println("runWayTaskResp:" + JSONObject.toJSONString(runWayTaskResp));
                return stringMap;
            } else {
                try {
                    Thread.sleep(2000); // 暂停2秒再尝试
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 角色驱动视频生成方法
     * <p>
     * 使用角色驱动视频生成方法生成视频
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> runwayVideoGenerationRoleDriven(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {
        if (imgVideoDTO.getAsTeamId() == null) {
            throw new E(CommonResultEnum.getSystemErrorInfo());
        }
        String token = RedisUtil.getValue(BRedisKeyEnum.REDIS_RUNWAY_TOKEN_KEY.getKey());
        if (token == null) {
            log.info("runway没有可用的key: {}", "请立即更换");
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }

        /*
         * String videoUrl =getRunwayTaskCanStart(imgVideoDTO.getAsTeamId(),
         * imgVideoDTO.getTaskId(), token);
         * System.out.println("videoUrl:"+videoUrl);
         * imgVideoDTO.setVideoUrl(videoUrl);
         */

        // 新的判断模型逻辑
        VideoSpecialEffectsVO videoStyle = null;
        if (imgVideoDTO.getStyleKey() == null) {
            throw new E("驱动风格不能为空");
        }
        if (imgVideoDTO.getStyleKey() == 999) { // 自定义驱动图片
            videoStyle = new VideoSpecialEffectsVO();
            if (imgVideoDTO.getStyle().contains("diandiansheji")) {
                String characterimage = uploadPresetRoleImageToRunway(imgVideoDTO.getStyle(), token);
                if (characterimage == null) {
                    throw new E("驱动角色异常");
                }
                videoStyle.setUrl(characterimage);
            } else {
                videoStyle.setUrl(imgVideoDTO.getStyle());
            }
        } else {
            videoStyle = getTextToVideoStyle(videoModelConfigPO.getVideoStyles(), imgVideoDTO.getStyleKey());
            if (videoStyle == null || videoStyle.getUrl() == null) {
                throw new E("驱动风格不能为空");
            }
            String characterimage = uploadPresetRoleImageToRunway(videoStyle.getUrl(), token);
            if (characterimage == null) {
                throw new E("驱动角色异常");
            }
            videoStyle.setUrl(characterimage);
        }
        String sessionId = RunwayApis.getRunWaySessionId(imgVideoDTO.getAsTeamId(), token);
        if (sessionId == null) {
            log.info("获取runway sessionid出错");
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }

        String videoJobId = null;
        RunWayVideoRoleTaskReq runWayVideoTaskReq = new RunWayVideoRoleTaskReq();
        if (StringUtils.isNotBlank(imgVideoDTO.getVideoUrl())) {
            RunWayVideoRoleTaskReq.OptionsObj options = new RunWayVideoRoleTaskReq.OptionsObj();
            options.setName("Gen-3 Alpha Turbo Act-One " + imgVideoDTO.getPromptUse() + " 4110564999");
            options.setDrivingVideo(imgVideoDTO.getVideoUrl());
            options.setMotionMultiplier(imgVideoDTO.getVideoWeight());
            options.setCharacterimage(videoStyle.getUrl());
            double duration = imgVideoDTO.getVideoDuration() == null ? Double.parseDouble(imgVideoDTO.getDuration())
                    : imgVideoDTO.getVideoDuration();
            options.setSeconds(Math.min(duration, 30));
            runWayVideoTaskReq.setOptions(options);
            runWayVideoTaskReq.setSessionId(sessionId);
            runWayVideoTaskReq.setTaskType(VideoTaskTypeEnum.TASK_TYPE_GEN3AT.getValue());
            runWayVideoTaskReq.setAsTeamId(String.valueOf(imgVideoDTO.getAsTeamId()));

            videoJobId = RunwayApis.postRunwayVToVGenerateRoleDriven(runWayVideoTaskReq, token);
        }

        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorInfo());
        }
        if (!videoJobId.equals("429")) {
            imgDrawRecordPO.setVideoJobId(videoJobId);
        }

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setImage(imgVideoDTO.getInitImgUrls() != null ? imgVideoDTO.getInitImgUrls().getFirst() : null);
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setVersion(imgVideoDTO.getVersion());
        imgVideoVO.setVideoLens(imgVideoDTO.getVideoLens());
        imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        imgVideoVO.setImageAsEndFrame(imgVideoDTO.getImageAsEndFrame());
        imgVideoVO.setStyleJson(imgVideoDTO.getStyleJson());

        imgDrawRecordPO.setSunoAccountId(Long.valueOf(imgVideoDTO.getAsTeamId()));
        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        // imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName() + " "+
        // (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setPromptInit(videoStyle.getName() == null ? "" : videoStyle.getName());
        imgDrawRecordPO.setRemark("平均生成时间：10-20分钟");
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());
        imgDrawRecordPO.setInitImgUrls(null);
        Integer width = runWayVideoTaskReq.getOptions().getWidth();
        Integer height = runWayVideoTaskReq.getOptions().getHeight();
        imgDrawRecordPO.setWidth(width);
        imgDrawRecordPO.setHeight(height);
        imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(width, height));

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            if (imgVideoDTO.getInitImgUrls() != null && !imgVideoDTO.getInitImgUrls().isEmpty()) {
                imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls().subList(0, 1));
                ;
            }
            // 将去runway生成不成功的放到队列里，定时器去处理
            if (StringUtils.isNotBlank(videoJobId) && "429".equals(videoJobId)) {
                JSONObject json = new JSONObject();
                json.put("id", imgDrawRecordPO.getId());
                json.put("runWayVideoTaskReq", runWayVideoTaskReq);
                boolean b = RedisUtil.rPushList(RedisUtil.REDIS_RUNWAY_SUBMIT_TASK_VIDEO_ROLE_LIST,
                        JSON.toJSONString(json));
            }
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    // 角色驱动预设风格图上传
    private static String uploadPresetRoleImageToRunway(String imgUrl, String token) {
        try {
            // 使用正则表达式匹配最后的文件名
            String fileName = null;
            Pattern pattern = Pattern.compile(".*/([^/]+)$");
            Matcher matcher = pattern.matcher(imgUrl);
            if (matcher.find()) {
                fileName = matcher.group(1);
            }

            boolean downloadImgLock = RedisUtil
                    .acquireLock(GlobalRedisKeyEnum.DOWNLOAD_LOCK_REDIS_RUNWAY_IMG.getStrKey(), 60);
            if (downloadImgLock) {
                byte[] imgFile = BFileUtil.downloadImageGetByte(imgUrl);
                return RunwayApis.uploadPresetRoleImageToRunway(imgFile, fileName, token);
            }
        } catch (Exception e) {
            log.error("角色驱动预设风格图上传失败，请重试" + e.getMessage());
            return null;
        } finally {
            RedisUtil.releaseLock(GlobalRedisKeyEnum.DOWNLOAD_LOCK_REDIS_RUNWAY_IMG.getStrKey());
        }
        return null;
    }

    /**
     * 梦工厂视频生成方法
     * <p>
     * 使用梦工厂AI模型生成视频
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> runwayVideoGenerationOld(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {

        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                .getSecretKeyInfo(DictConfigEnum.RUNWAY_API_KEY.getDictType());
        String token = dictConfigMap.get(DictConfigEnum.RUNWAY_API_KEY.getDictKey());
        if (token == null) {
            log.info("runway没有可用的key: {}", "请立即更换");
            throw new E("runway视频模型升级维护中...");
        }

        String videoJobId = null;

        imgDrawRecordPO.setWidth(imgVideoDTO.getWidth());
        imgDrawRecordPO.setHeight(imgVideoDTO.getHeight());
        imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(imgVideoDTO.getWidth(), imgVideoDTO.getHeight()));

        List<String> initImgUrls = imgVideoDTO.getInitImgUrls();

        RunWayOfficeTaskReq runWayTaskReq = new RunWayOfficeTaskReq();
        if (null != initImgUrls && initImgUrls.size() > 0) {
            RunWayOfficeTaskReq.PromptImage promptImage = new RunWayOfficeTaskReq.PromptImage();
            promptImage.setUri(initImgUrls.get(0));
            promptImage.setPosition("first");
            List<RunWayOfficeTaskReq.PromptImage> promptImageList = new ArrayList<>();
            promptImageList.add(promptImage);
            runWayTaskReq.setPromptImages(promptImageList);
            // runWayTaskReq.setPromptImage(initImgUrls.get(0));
        }
        runWayTaskReq.setPromptText(imgVideoDTO.getPromptUse());
        videoJobId = RunwayOfficeApis.postToGenerateRunwayVideo(runWayTaskReq, token);

        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setImage(imgVideoDTO.getInitImgUrls() != null ? imgVideoDTO.getInitImgUrls().getFirst() : null);
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setVersion(imgVideoDTO.getVersion());
        imgVideoVO.setVideoLens(imgVideoDTO.getVideoLens());
        imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        imgVideoVO.setImageAsEndFrame(imgVideoDTO.getImageAsEndFrame());

        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName() + " "
                + (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setRemark("平均生成时间：5-10分钟");
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());
        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg()) || StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
            List<String> list = new ArrayList<>();
            list.add(imgVideoDTO.getFirstImg());
            list.add(imgVideoDTO.getEndImg());
            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(list));
        }

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            if (imgVideoDTO.getInitImgUrls() != null && !imgVideoDTO.getInitImgUrls().isEmpty()) {
                imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls().subList(0, 1));
                ;
            }
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * 梦工厂视频生成方法
     * <p>
     * 使用梦工厂AI模型生成视频
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> runwayVideoGenerationNew(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {

        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                .getSecretKeyInfo(DictConfigEnum.RUNWAY_API_KEY.getDictType());
        String token = dictConfigMap.get(DictConfigEnum.RUNWAY_API_KEY.getDictKey());
        if (token == null) {
            log.info("runway没有可用的key: {}", "请立即更换");
            throw new E("runway视频模型升级维护中...");
        }
        ImgVideoDTO.VideoLensObj videoLensObj = new ImgVideoDTO.VideoLensObj();
        String videoLensByModel = null;
        if (StringUtils.isNotBlank(imgVideoDTO.getVideoLensNew())) {
            videoLensObj = JSONObject.parseObject(imgVideoDTO.getVideoLensNew(), ImgVideoDTO.VideoLensObj.class);
            videoLensByModel = getVideoLensByModel(videoLensObj, false);
        }

        String videoJobId = null;

        RunWayOfficeTaskReq runWayTaskReq = new RunWayOfficeTaskReq();
        if (StringUtils.isNotBlank(imgVideoDTO.getDuration())) {
            runWayTaskReq.setDuration(Integer.parseInt(imgVideoDTO.getDuration()));
        }
        if (imgVideoDTO.getScalesKey() != null) {
            // List<ImgScaleDTO> scaleList =
            // ImageModelUtil.getImgScaleDTOs(videoModelConfigPO.getVideoScales());
            List<ImgVideoScaleDTO> scaleList = ImageModelUtil
                    .getImgVideoScaleDTOs(videoModelConfigPO.getImgVideoScales());
            Optional<ImgVideoScaleDTO> matchingVideoScale = scaleList.stream()
                    .filter(scale -> scale.getKey() == imgVideoDTO.getScalesKey().intValue()).findFirst();
            if (matchingVideoScale.isPresent()) {
                ImgVideoScaleDTO imgScale = matchingVideoScale.get();
                // imgVideoBO.setAspectRatio(imgScale.getWidth() +":"+ imgScale.getHeight());
                // //比列key
                imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(imgScale.getWidth(), imgScale.getHeight()));
                runWayTaskReq.setRatio(
                        BLEDrowSizeEnum.getRunWayWidthAndHeight(imgScale.getWidth() + ":" + imgScale.getHeight()));
            }
        }
        List<RunWayOfficeTaskReq.PromptImage> promptImageList = new ArrayList<>();
        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg())) {
            promptImageList.add(new RunWayOfficeTaskReq.PromptImage(imgVideoDTO.getFirstImg(), "first"));
        }
        if (StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
            promptImageList.add(new RunWayOfficeTaskReq.PromptImage(imgVideoDTO.getEndImg(), "last"));
        }
        runWayTaskReq.setPromptImages(promptImageList);

        if (StringUtils.isNotBlank(videoLensByModel)) {
            runWayTaskReq.setPromptText(imgVideoDTO.getPromptUse() + videoLensByModel);
        } else {
            runWayTaskReq.setPromptText(imgVideoDTO.getPromptUse());
        }
        if (StringUtils.isBlank(imgVideoDTO.getStyle()) || "随机".equals(imgVideoDTO.getStyle())) {
        } else {
            runWayTaskReq.setPromptText(runWayTaskReq.getPromptText() + " " + imgVideoDTO.getStyle());
        }

        videoJobId = RunwayOfficeApis.postToGenerateRunwayVideo(runWayTaskReq, token);

        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setImage(imgVideoDTO.getInitImgUrls() != null ? imgVideoDTO.getInitImgUrls().getFirst() : null);
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setVersion(imgVideoDTO.getVersion());
        imgVideoVO.setVideoLens(imgVideoDTO.getVideoLens());
        imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        imgVideoVO.setImageAsEndFrame(imgVideoDTO.getImageAsEndFrame());
        imgVideoVO.setStyleJson(imgVideoDTO.getStyleJson());
        imgVideoVO.setVideoLensJson(imgVideoDTO.getVideoLensNew());

        imgDrawRecordPO.setVideoJobId(videoJobId);
        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName() + " "
                + (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setRemark("平均生成时间：5-10分钟");
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());
        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg()) || StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
            List<String> list = new ArrayList<>();
            if (null != imgVideoDTO.getFirstImg()) {
                list.add(imgVideoDTO.getFirstImg());
            }
            if (null != imgVideoDTO.getEndImg()) {
                list.add(imgVideoDTO.getEndImg());
            }
            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(list));
        }

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            if (imgVideoDTO.getInitImgUrls() != null && !imgVideoDTO.getInitImgUrls().isEmpty()) {
                imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls().subList(0, 1));
                ;
            }
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * 梦工厂视频生成方法
     * <p>
     * 使用梦工厂AI模型生成视频
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> lumaVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {

        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                .getSecretKeyInfo(DictConfigEnum.DREAMFACTORY_API_KEY.getDictType());
        String key = dictConfigMap.get(DictConfigEnum.DREAMFACTORY_API_KEY.getDictKey());
        if (dictConfigMap == null || StringUtils.isBlank(key)) {
            log.info("梦工厂没有可用的key: {}", "请立即更换");
            throw new E("梦工厂视频模型升级维护中...");
        }

        String videoJobId = null;
        if (imgVideoDTO.getInitImgUrls() != null && !imgVideoDTO.getInitImgUrls().isEmpty()) {
            List<String> initImgUrls = imgVideoDTO.getInitImgUrls();
            if (initImgUrls.size() >= 1) {
                LumaKeyFrames keyframes = new LumaKeyFrames();
                LumaFrames frame = new LumaFrames();
                if (StringUtils.isNotBlank(initImgUrls.get(0))) {
                    frame.setType(LumaFramesTypeEnum.IMAGE.getType());
                    // String url = initImgUrls.get(0).replace("cdn", "image");
                    String lumaUploadImage = LumaUploadPicUtil.getLumaUploadImage(initImgUrls.get(0));
                    if (StringUtils.isNotBlank(lumaUploadImage)) {
                        frame.setUrl(lumaUploadImage);
                    } else {
                        frame.setUrl(initImgUrls.get(0));
                    }
                    keyframes.setFrame0(frame);
                }
                if (initImgUrls.size() > 1 && StringUtils.isNotBlank(initImgUrls.get(1))) {
                    LumaFrames frame1 = new LumaFrames();
                    frame1.setType(LumaFramesTypeEnum.IMAGE.getType());
                    // String url = initImgUrls.get(1).replace("cdn", "image");
                    String lumaUploadImage = LumaUploadPicUtil.getLumaUploadImage(initImgUrls.get(1));
                    if (StringUtils.isNotBlank(lumaUploadImage)) {
                        frame1.setUrl(lumaUploadImage);
                    } else {
                        frame1.setUrl(initImgUrls.get(1));
                    }
                    keyframes.setFrame1(frame1);
                    imgVideoDTO.setLoop(false);
                }
                videoJobId = LumaApis.postToGenerateLumaOfficeVideo(
                        new LumaOfficeVideoReq(imgVideoDTO.getPromptUse(), imgVideoDTO.isLoop(), keyframes), key);

            }
        } else {
            imgDrawRecordPO.setWidth(16);
            imgDrawRecordPO.setHeight(9);
            imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(16, 9));
            videoJobId = LumaApis.postToGenerateLumaOfficeVideo(
                    new LumaOfficeVideoReq(imgVideoDTO.getPromptUse(), imgVideoDTO.isLoop()), key);
        }
        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        imgDrawRecordPO.setVideoJobId(videoJobId);

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        if (imgVideoDTO.getInitImgUrls() != null) {
            List<String> initImgUrlList = imgVideoDTO.getInitImgUrls();
            List<String> collect = initImgUrlList.stream().filter(m -> StringUtils.isNotBlank(m))
                    .collect(Collectors.toList());

            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(collect));
            imgVideoVO
                    .setImage(initImgUrlList.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
        }
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setFirstHue(imgVideoDTO.getFirstHue());
        imgVideoVO.setLastHue(imgVideoDTO.getLastHue());
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setLoop(imgVideoDTO.isLoop());
        imgVideoVO.setImgDetailFirst(imgVideoDTO.getImgDetailFirst());
        imgVideoVO.setImgDetailLast(imgVideoDTO.getImgDetailLast());

        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName() + " "
                + (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setRemark("平均生成时间：1-5分钟");
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());
        if (imgVideoDTO.getInitImgUrls() != null) {
            List<String> initImgUrlList = imgVideoDTO.getInitImgUrls();
            List<String> collect = initImgUrlList.stream().filter(m -> StringUtils.isNotBlank(m))
                    .collect(Collectors.toList());
            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(collect));
        }

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());
            ;
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * 梦工厂视频生成方法
     * <p>
     * 使用梦工厂AI模型生成视频
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> lumaVideoGenerationNew(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {

        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                .getSecretKeyInfo(DictConfigEnum.DREAMFACTORY_API_KEY.getDictType());
        String key = dictConfigMap.get(DictConfigEnum.DREAMFACTORY_API_KEY.getDictKey());
        if (StringUtils.isBlank(key)) {
            log.info("梦工厂没有可用的key: {}", "请立即更换");
            throw new E("梦工厂视频模型升级维护中...");
        }

        ImgVideoDTO.VideoLensObj videoLensObj = new ImgVideoDTO.VideoLensObj();
        String videoLensByModel = null;
        if (StringUtils.isNotBlank(imgVideoDTO.getVideoLensNew())) {
            videoLensObj = JSONObject.parseObject(imgVideoDTO.getVideoLensNew(), ImgVideoDTO.VideoLensObj.class);
            videoLensByModel = getVideoLensByModel(videoLensObj, false);
        }
        LumaOfficeVideoReq lumaOfficeVideoReq = new LumaOfficeVideoReq();
        if (StringUtils.isNotBlank(videoLensByModel)) {
            lumaOfficeVideoReq.setPrompt(imgVideoDTO.getPromptUse() + videoLensByModel);
        } else {
            lumaOfficeVideoReq.setPrompt(imgVideoDTO.getPromptUse());
        }
        lumaOfficeVideoReq.setLoop(imgVideoDTO.isLoop());

        // 新的判断模型逻辑
        VideoSpecialEffectsVO videoStyle = null;
        if (imgVideoDTO.getStyleKey() != null) {
            videoStyle = getTextToVideoStyle(videoModelConfigPO.getVideoStyles(), imgVideoDTO.getStyleKey());
        }
        if (videoStyle != null) {
            lumaOfficeVideoReq.setPrompt(videoStyle.getValue() + " " + lumaOfficeVideoReq.getPrompt());
        }

        if (imgVideoDTO.getScalesKey() != null) {
            List<ImgScaleDTO> scaleList = ImageModelUtil.getImgScaleDTOs(videoModelConfigPO.getVideoScales());
            Optional<ImgScaleDTO> matchingVideoScale = scaleList.stream()
                    .filter(scale -> scale.getKey() == imgVideoDTO.getScalesKey().intValue()).findFirst();
            if (matchingVideoScale.isPresent()) {
                ImgScaleDTO imgScale = matchingVideoScale.get();
                // imgVideoBO.setAspectRatio(imgScale.getWidth() +":"+ imgScale.getHeight());
                // //比列key
                imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(Integer.valueOf(imgScale.getWidth()),
                        Integer.valueOf(imgScale.getHeight())));
                lumaOfficeVideoReq.setAspectRatio(imgScale.getTitle());
            }
        }
        String videoJobId = null;
        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg()) || StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
            if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg())) {
                LumaKeyFrames keyframes = new LumaKeyFrames();
                LumaFrames frame = new LumaFrames();
                if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg())) {
                    frame.setType(LumaFramesTypeEnum.IMAGE.getType());
                    String lumaUploadImage = LumaUploadPicUtil.getLumaUploadImage(imgVideoDTO.getFirstImg());
                    if (StringUtils.isNotBlank(lumaUploadImage)) {
                        frame.setUrl(lumaUploadImage);
                    } else {
                        frame.setUrl(imgVideoDTO.getFirstImg());
                    }
                    keyframes.setFrame0(frame);
                }
                if (StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
                    LumaFrames frame1 = new LumaFrames();
                    frame1.setType(LumaFramesTypeEnum.IMAGE.getType());
                    String lumaUploadImage = LumaUploadPicUtil.getLumaUploadImage(imgVideoDTO.getEndImg());
                    if (StringUtils.isNotBlank(lumaUploadImage)) {
                        frame1.setUrl(lumaUploadImage);
                    } else {
                        frame1.setUrl(imgVideoDTO.getEndImg());
                    }
                    keyframes.setFrame1(frame1);
                    imgVideoDTO.setLoop(false);
                }
                lumaOfficeVideoReq.setKeyframes(keyframes);
                videoJobId = LumaApis.postToGenerateLumaOfficeVideo(lumaOfficeVideoReq, key);

            }
        } else {
            videoJobId = LumaApis.postToGenerateLumaOfficeVideo(lumaOfficeVideoReq, key);
        }
        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        imgDrawRecordPO.setVideoJobId(videoJobId);

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        if (imgVideoDTO.getInitImgUrls() != null) {
            List<String> initImgUrlList = imgVideoDTO.getInitImgUrls();
            List<String> collect = initImgUrlList.stream().filter(m -> StringUtils.isNotBlank(m))
                    .collect(Collectors.toList());

            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(collect));
            imgVideoVO
                    .setImage(initImgUrlList.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
        }
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setFirstHue(imgVideoDTO.getFirstHue());
        imgVideoVO.setLastHue(imgVideoDTO.getLastHue());
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setLoop(imgVideoDTO.isLoop());
        imgVideoVO.setImgDetailFirst(imgVideoDTO.getImgDetailFirst());
        imgVideoVO.setImgDetailLast(imgVideoDTO.getImgDetailLast());
        imgVideoVO.setStyleJson(imgVideoDTO.getStyleJson());
        imgVideoVO.setVideoLensJson(imgVideoDTO.getVideoLensNew());

        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName()
                + " " + (videoStyle == null ? "" : videoStyle.getName() == null ? "" : videoStyle.getName())
                + " " + (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setRemark("平均生成时间：1-5分钟");
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());

        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg()) || StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
            List<String> list = new ArrayList<>();
            list.add(imgVideoDTO.getFirstImg());
            list.add(imgVideoDTO.getEndImg());
            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(list));

            // 有图片的时候处理默认宽高比
            imgDrawRecordPO.setWidth(imgVideoDTO.getWidth());
            imgDrawRecordPO.setHeight(imgVideoDTO.getHeight());
            imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(imgVideoDTO.getWidth(), imgVideoDTO.getHeight()));
        }

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());
            ;
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * 万相视频生成
     * 
     * @param imgVideoDTO        图片视频DTO
     * @param imgDrawRecordPO    图片视频记录PO
     * @param videoModelConfigPO 视频模型配置PO
     * @return 结果
     * @throws Exception 异常
     */
    private Result<Object> wanxVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {
        // 获取通义系列视频生成key
        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                .getSecretKeyInfo(DictConfigEnum.WANX_API_KEY.getDictType());
        String apiKey = dictConfigMap.get(DictConfigEnum.WANX_API_KEY.getDictKey());
        if (StringUtils.isBlank(apiKey)) {
            log.info("通义系列视频生成没有可用的key: {}", "请立即更换");
            throw new E("通义系列视频模型升级维护中...");
        }
        // 获取视频镜头
        String videoLensByModel = null;
        ImgVideoDTO.VideoLensObj videoLensObj = new ImgVideoDTO.VideoLensObj();
        if (StringUtils.isNotBlank(imgVideoDTO.getVideoLensNew())) {
            videoLensObj = JSONObject.parseObject(imgVideoDTO.getVideoLensNew(), ImgVideoDTO.VideoLensObj.class);
            videoLensByModel = getVideoLensByModel(videoLensObj, true);
        }
        // 获取视频比例
        String size = null;
        if (imgVideoDTO.getScalesKey() != null) {
            List<ImgScaleDTO> scaleList = ImageModelUtil.getImgScaleDTOs(videoModelConfigPO.getVideoScales());
            Optional<ImgScaleDTO> matchingVideoScale = scaleList.stream()
                    .filter(scale -> scale.getKey() == imgVideoDTO.getScalesKey().intValue()).findFirst();
            if (matchingVideoScale.isPresent()) {
                ImgScaleDTO imgScale = matchingVideoScale.get();
                imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(Integer.valueOf(imgScale.getWidth()),
                        Integer.valueOf(imgScale.getHeight())));
                size = imgScale.getWidth() + "*" + imgScale.getHeight();
            }
        }
        // 获取提示词
        String prompt = null;
        if (StringUtils.isNotBlank(videoLensByModel)) {
            prompt = imgVideoDTO.getPrompt() + videoLensByModel;
        } else {
            prompt = imgVideoDTO.getPrompt();
        }

        // 新的判断模型逻辑
        VideoSpecialEffectsVO videoStyle = null;
        // 获取视频风格
        if (imgVideoDTO.getStyleKey() != null) {
            videoStyle = getTextToVideoStyle(videoModelConfigPO.getVideoStyles(), imgVideoDTO.getStyleKey());
        }
        // 获取用户提示词
        String promptUse = null;
        if (videoStyle != null) {
            promptUse = videoStyle.getValue() + " " + prompt;
        } else {
            promptUse = prompt;
        }
        // 提交视频任务
        String videoJobId = null;
        // 图生视频-首尾帧
        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg()) && StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
            List<ImgVideoScaleDTO> imgVideoScales = ImageModelUtil
                    .getImgVideoScaleDTOs(videoModelConfigPO.getImgVideoScales());
            ImgVideoScaleDTO imgVideoScale = imgVideoScales.stream()
                    .filter(scales -> Objects.equals(scales.getKey(), imgVideoDTO.getScalesKey())).findFirst()
                    .orElse(null);
            if (imgVideoScale != null) {
                imgDrawRecordPO
                        .setWhDivide(ImgDrawUtil.getWhDivide(imgVideoScale.getWidth(), imgVideoScale.getHeight()));
            }
            videoJobId = WanxApiUtil.postWanxVideoGenerations(
                    null,
                    null,
                    new WanxImgFirstLastFrameReqParamBO(
                            imgVideoDTO.getVideoModelValue(), // 模型
                            promptUse, // 提示词
                            imgVideoDTO.getFirstImg(), // 首帧图片
                            imgVideoDTO.getEndImg() // 尾帧图片
                    ),
                    apiKey);
        }
        // 图生视频-首帧
        else if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg())) {
            videoJobId = WanxApiUtil.postWanxVideoGenerations(
                    null,
                    new WanxImgReqParamBO(imgVideoDTO.getVideoModelValue(), promptUse, imgVideoDTO.getFirstImg()),
                    null,
                    apiKey);
        }
        // 文生视频
        else {
            videoJobId = WanxApiUtil.postWanxVideoGenerations(
                    new WanxReqParamBO(imgVideoDTO.getVideoModelValue(), promptUse, size == null ? "960*960" : size),
                    null,
                    null,
                    apiKey);
        }
        // 判断视频任务是否提交成功
        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        imgDrawRecordPO.setVideoJobId(videoJobId);

        // 设置图片视频VO
        ImgVideoVO imgVideoVO = new ImgVideoVO();
        imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setFirstHue(imgVideoDTO.getFirstHue());
        imgVideoVO.setLastHue(imgVideoDTO.getLastHue());
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setLoop(imgVideoDTO.isLoop());
        imgVideoVO.setImgDetailFirst(imgVideoDTO.getImgDetailFirst());
        imgVideoVO.setImgDetailLast(imgVideoDTO.getImgDetailLast());
        imgVideoVO.setStyleJson(imgVideoDTO.getStyleJson());
        imgVideoVO.setVideoLensJson(imgVideoDTO.getVideoLensNew());
        imgVideoVO.setVideoVersionKey(imgVideoDTO.getVideoVersionKey());

        // 设置图片视频记录PO
        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName()
                + " " + (videoStyle == null ? "" : videoStyle.getName() == null ? "" : videoStyle.getName())
                + " " + (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setRemark("平均生成时间：1-10分钟");
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());

        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg()) || StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
            List<String> list = new ArrayList<>();
            list.add(imgVideoDTO.getFirstImg());
            list.add(imgVideoDTO.getEndImg());
            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(list));
        }

        // 插入图片视频记录
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());
            ;
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * 智谱视频生成方法
     * <p>
     * 使用智谱AI模型生成视频
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> zhiPuVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {

        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                .getSecretKeyInfo(DictConfigEnum.ZHIPU_API_KEY.getDictType());
        String token = dictConfigMap.get(DictConfigEnum.ZHIPU_API_KEY.getDictKey());
        if (dictConfigMap == null || StringUtils.isBlank(token)) {
            log.info("智谱视频没有可用的key: {}", "请立即更换");
            throw new E("智谱视频模型升级维护中...");
        }
        String videoLensByModel = null;
        ImgVideoDTO.VideoLensObj videoLensObj = new ImgVideoDTO.VideoLensObj();
        if (StringUtils.isNotBlank(imgVideoDTO.getVideoLensNew())) {
            videoLensObj = JSONObject.parseObject(imgVideoDTO.getVideoLensNew(), ImgVideoDTO.VideoLensObj.class);
            videoLensByModel = getVideoLensByModel(videoLensObj, true);
        }
        ZhiPuRequestBO zhiPuRequestBO = new ZhiPuRequestBO();
        zhiPuRequestBO.setModel("cogvideox-flash"); // cogvideox
        if (StringUtils.isNotBlank(videoLensByModel)) {
            zhiPuRequestBO.setPrompt(imgVideoDTO.getPrompt() + videoLensByModel);
        } else {
            zhiPuRequestBO.setPrompt(imgVideoDTO.getPrompt());
        }

        // 新的判断模型逻辑
        VideoSpecialEffectsVO videoStyle = null;
        if (imgVideoDTO.getStyleKey() != null) {
            videoStyle = getTextToVideoStyle(videoModelConfigPO.getVideoStyles(), imgVideoDTO.getStyleKey());
        }
        if (videoStyle != null) {
            zhiPuRequestBO.setPrompt(videoStyle.getValue() + " " + zhiPuRequestBO.getPrompt());
        }

        String videoJobId = null;
        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg())) {
            List<ImgVideoScaleDTO> imgVideoScales = ImageModelUtil
                    .getImgVideoScaleDTOs(videoModelConfigPO.getImgVideoScales());
            ImgVideoScaleDTO imgVideoScale = imgVideoScales.stream()
                    .filter(scales -> Objects.equals(scales.getKey(), imgVideoDTO.getScalesKey())).findFirst()
                    .orElse(null);
            int width = 0;
            if (imgVideoScale != null) {
                imgDrawRecordPO
                        .setWhDivide(ImgDrawUtil.getWhDivide(imgVideoScale.getWidth(), imgVideoScale.getHeight()));
                width = getRefeImgWidth(imgVideoScale.getWidth() + ":" + imgVideoScale.getHeight());
            }
            // String imgUrl = imgVideoDTO.getFirstImg() +
            // "?x-oss-process=image/format,avif/resize,w_" + width;
            // System.out.println(imgUrl); // + "?x-oss-process=image/format,avif/resize,w_"
            // + width);
            // String imgUrlBase64 = ImageUtil.getImgUrlToBate64(imgUrl);
            zhiPuRequestBO.setImageUrl(imgVideoDTO.getFirstImg());// ("data:image/jpg;base64," + imgUrlBase64);
            zhiPuRequestBO.setPrompt((zhiPuRequestBO.getPrompt() == null || zhiPuRequestBO.getPrompt().isEmpty()) ? null
                    : zhiPuRequestBO.getPrompt());
            videoJobId = ZhiPuApiUtil.postZhiPuTextToVideoGenerations(zhiPuRequestBO, token);
        } else {
            imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(3, 2));
            videoJobId = ZhiPuApiUtil.postZhiPuTextToVideoGenerations(zhiPuRequestBO, token);
        }
        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        imgDrawRecordPO.setVideoJobId(videoJobId);

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        if (imgVideoDTO.getInitImgUrls() != null) {
            List<String> initImgUrlList = imgVideoDTO.getInitImgUrls();
            List<String> collect = initImgUrlList.stream().filter(m -> StringUtils.isNotBlank(m))
                    .collect(Collectors.toList());

            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(collect));
            imgVideoVO
                    .setImage(initImgUrlList.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
        }
        imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        imgVideoVO.setStyleJson(imgVideoDTO.getStyleJson());
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setFirstHue(imgVideoDTO.getFirstHue());
        imgVideoVO.setLastHue(imgVideoDTO.getLastHue());
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setLoop(imgVideoDTO.isLoop());
        imgVideoVO.setImgDetailFirst(imgVideoDTO.getImgDetailFirst());
        imgVideoVO.setImgDetailLast(imgVideoDTO.getImgDetailLast());
        imgVideoVO.setVideoLensJson(imgVideoDTO.getVideoLensNew());

        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName()
                + " " + (videoStyle == null ? "" : videoStyle.getName() == null ? "" : videoStyle.getName())
                + " " + (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setRemark("平均生成时间：1-5分钟");
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());
        if (imgVideoDTO.getInitImgUrls() != null) {
            List<String> initImgUrlList = imgVideoDTO.getInitImgUrls();
            List<String> collect = initImgUrlList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(collect));
        }

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());
            ;
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * 获取参考图片宽度
     * <p>
     * 根据图片比例获取参考图片宽度
     * 
     * @param imgScales 图片比例
     * @return 参考图片宽度
     */
    public static int getRefeImgWidth(String imgScales) {
        if (imgScales == null) {
            if (imgScales.equals(BLEDrowSizeEnum.ZHIPU_ONE_TO_ONE.getRatio())) {
                return BLEDrowSizeEnum.ZHIPU_ONE_TO_ONE.getInputWidth();
            }
            if (imgScales.equals(BLEDrowSizeEnum.ZHIPU_THREE_TO_FOUR.getRatio())) {
                return BLEDrowSizeEnum.ZHIPU_THREE_TO_FOUR.getInputWidth();
            }
            if (imgScales.equals(BLEDrowSizeEnum.ZHIPU_FOUR_TO_THREE.getRatio())) {
                return BLEDrowSizeEnum.ZHIPU_FOUR_TO_THREE.getInputWidth();
            }
            if (imgScales.equals(BLEDrowSizeEnum.ZHIPU_NINE_TO_SIXTEEN.getRatio())) {
                return BLEDrowSizeEnum.ZHIPU_NINE_TO_SIXTEEN.getInputWidth();
            }
            if (imgScales.equals(BLEDrowSizeEnum.ZHIPU_SIXTEEN_TO_NINE.getRatio())) {
                return BLEDrowSizeEnum.ZHIPU_SIXTEEN_TO_NINE.getInputWidth();
            }
        }
        return 1024;
    }

    /**
     * 海螺视频生成方法
     * <p>
     * 使用海螺AI模型生成视频
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置对象
     * @param haiLuoRequestBO    海螺请求对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> hailuoVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO, HaiLuoRequestBO haiLuoRequestBO) throws Exception {
        // String base64 = ImgHandleUtil.initMinimaxImage(imgVideoDTO.getFirstImg());
        // if (base64 == null) {
        // throw new E("图片不符合要求，请重新上传");
        // }
        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                .getSecretKeyInfo(DictConfigEnum.HAILUO_API_KEY.getDictType());
        String key = dictConfigMap.get(DictConfigEnum.HAILUO_API_KEY.getDictKey());
        if (dictConfigMap == null || StringUtils.isBlank(key)) {
            log.info("海螺视频没有可用的key: {}", "请立即更换");
            throw new E("海螺视频模型升级维护中...");
        }

        ImgVideoDTO.VideoLensObj videoLensObj = new ImgVideoDTO.VideoLensObj();
        String videoLensByModel = null;
        if (StringUtils.isNotBlank(imgVideoDTO.getVideoLensNew())) {
            videoLensObj = JSONObject.parseObject(imgVideoDTO.getVideoLensNew(), ImgVideoDTO.VideoLensObj.class);
            videoLensByModel = getVideoLensByModel(videoLensObj, true);
        }

        // 新的判断模型逻辑
        StringBuilder prompts = new StringBuilder();
        VideoSpecialEffectsVO videoStyle = null;
        if (imgVideoDTO.getStyleKey() != null) {
            videoStyle = getTextToVideoStyle(videoModelConfigPO.getVideoStyles(), imgVideoDTO.getStyleKey());
        }
        if (videoStyle != null) {
            prompts.append(videoStyle.getValue());
        }
        prompts.append(imgVideoDTO.getPrompt());
        if (StringUtils.isNotBlank(videoLensByModel)) {
            prompts.append(videoLensByModel);
        }
        haiLuoRequestBO.setPrompt(prompts.toString());

        String videoJobId = null;
        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg())) {
            List<ImgVideoScaleDTO> imgVideoScales = ImageModelUtil
                    .getImgVideoScaleDTOs(videoModelConfigPO.getImgVideoScales());
            ImgVideoScaleDTO imgVideoScale = imgVideoScales.stream()
                    .filter(scales -> Objects.equals(scales.getKey(), imgVideoDTO.getScalesKey())).findFirst()
                    .orElse(null);
            int width = 0;
            if (imgVideoScale != null) {
                imgDrawRecordPO
                        .setWhDivide(ImgDrawUtil.getWhDivide(imgVideoScale.getWidth(), imgVideoScale.getHeight()));
                width = getRefeImgWidth(imgVideoScale.getWidth() + ":" + imgVideoScale.getHeight());
            }

            haiLuoRequestBO.setFirstFrameImage(imgVideoDTO.getFirstImg()); // +
                                                                           // "?x-oss-process=image/format,avif/resize,w_"
                                                                           // + width);
            // haiLuoRequestBO.setFirstFrameImage(base64);
            videoJobId = HaiLuoApiUtil.postHaiLuoTextToVideoGenerations(haiLuoRequestBO, key);
            if ("2013".equals(videoJobId)) {
                throw new E("图片不符合要求，换一张图片试试");
            }
        } else {
            imgDrawRecordPO.setWidth(16);
            imgDrawRecordPO.setHeight(9);
            imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(16, 9));
            videoJobId = HaiLuoApiUtil.postHaiLuoTextToVideoGenerations(haiLuoRequestBO, key);
        }
        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        if (StringUtils.isNotBlank(videoJobId) && "429".equals(videoJobId)) {
            log.info("海螺视频限流");
        } else {
            imgDrawRecordPO.setVideoJobId(videoJobId);
        }

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        if (imgVideoDTO.getInitImgUrls() != null) {
            List<String> initImgUrlList = imgVideoDTO.getInitImgUrls();
            List<String> collect = initImgUrlList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());

            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(collect));
            imgVideoVO
                    .setImage(initImgUrlList.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
        }
        imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        imgVideoVO.setStyleJson(imgVideoDTO.getStyleJson());
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setFirstHue(imgVideoDTO.getFirstHue());
        imgVideoVO.setLastHue(imgVideoDTO.getLastHue());
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setLoop(imgVideoDTO.isLoop());
        imgVideoVO.setImgDetailFirst(imgVideoDTO.getImgDetailFirst());
        imgVideoVO.setImgDetailLast(imgVideoDTO.getImgDetailLast());
        imgVideoVO.setVideoLensJson(imgVideoDTO.getVideoLensNew());

        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName()
                + " " + (videoStyle == null ? "" : videoStyle.getName() == null ? "" : videoStyle.getName())
                + " " + (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setRemark("平均生成时间：1-5分钟");
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());
        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg()) || StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
            List<String> list = new ArrayList<>();
            list.add(imgVideoDTO.getFirstImg());
            list.add(imgVideoDTO.getEndImg());
            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(list));
        }

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());

            // 将去runway生成不成功的放到队列里，定时器去处理
            if (StringUtils.isNotBlank(videoJobId) && "429".equals(videoJobId)) {
                JSONObject json = new JSONObject();
                json.put("id", imgDrawRecordPO.getId());
                json.put("haiLuoRequestBO", haiLuoRequestBO);
                boolean b = RedisUtil.rPushList(RedisUtil.REDIS_HAILUO_SUBMIT_TASK_LIST, JSON.toJSONString(json));
            }
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * 可灵视频生成方法
     * <p>
     * 使用可灵AI模型生成视频
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> klingVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {
        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                .getSecretKeyInfo(DictConfigEnum.KLING_API_KEY.getDictType());
        if (dictConfigMap == null) {
            throw new E("可灵视频模型升级维护中...");
        }
        String klingApiKey = dictConfigMap.get(DictConfigEnum.KLING_API_KEY.getDictKey());
        if (StringUtils.isBlank(klingApiKey)) {
            log.info("可灵视频没有可用的key: {}", "请立即更换");
            throw new E("可灵视频模型升级维护中...");
        }

        ImgVideoDTO.VideoLensObj videoLensObj = new ImgVideoDTO.VideoLensObj();
        String videoLensByModel = null;
        if (StringUtils.isNotBlank(imgVideoDTO.getVideoLensNew())) {
            videoLensObj = JSONObject.parseObject(imgVideoDTO.getVideoLensNew(), ImgVideoDTO.VideoLensObj.class);
            videoLensByModel = getVideoLensByModel(videoLensObj, true);
        }

        String videoJobId = null;
        String[] split = klingApiKey.split(",");
        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg()) || StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
            KlingImgVideoReqBO klingImgVideoReqBO = new KlingImgVideoReqBO();
            int width = 0;
            if (imgVideoDTO.getScalesKey() != null) {
                ImageModelUtil.getImgVideoScaleDTOs(videoModelConfigPO.getImgVideoScales());
                List<ImgVideoScaleDTO> scaleList = ImageModelUtil
                        .getImgVideoScaleDTOs(videoModelConfigPO.getImgVideoScales());
                Optional<ImgVideoScaleDTO> matchingVideoScale = scaleList.stream()
                        .filter(scale -> scale.getKey() == imgVideoDTO.getScalesKey().intValue()).findFirst();
                if (matchingVideoScale.isPresent()) {
                    ImgVideoScaleDTO imgScale = matchingVideoScale.get();
                    // imgVideoBO.setAspectRatio(imgScale.getWidth() +":"+ imgScale.getHeight());
                    // //比列key
                    imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(Integer.valueOf(imgScale.getWidth()),
                            Integer.valueOf(imgScale.getHeight())));
                    width = getRefeImgWidth(imgScale.getWidth() + ":" + imgScale.getHeight());
                }
            }

            /*
             * String firstImg = imgVideoDTO.getFirstImg();
             * String objectName =
             * firstImg.substring(CommonStrEnum.IMAGE_PREFIX.getValue().concat("/").length()
             * );
             * // String objectName =
             * OssClientConfig.getPath(14).concat(fileName.concat(OssClientConfig.
             * FILE_SUFFIX));
             * try {
             * String resBody = OSSUtils.getImageInfo(OssClientConfig.ENDPOINT,
             * OssClientConfig.ACCESSKEYID, OssClientConfig.SECRETACCESSKEY,
             * OssClientConfig.BUCKET_NAME, objectName);
             * if (resBody != null) {
             * JsonNode jsonNode = new ObjectMapper().readTree(resBody);
             * Integer width = jsonNode.get("ImageWidth").get("value").asInt();
             * Integer height = jsonNode.get("ImageHeight").get("value").asInt();
             * imgDrawRecordPO.setWidth(width);
             * imgDrawRecordPO.setHeight(height);
             * imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(width, height));
             * }
             * } catch (Throwable e) {
             * throw new E("请换一张图片重试");
             * }
             */

            if (StringUtils.isNotBlank(videoLensByModel)) {
                klingImgVideoReqBO.setPrompt(imgVideoDTO.getPrompt() + videoLensByModel);
            } else {
                klingImgVideoReqBO.setPrompt(imgVideoDTO.getPrompt());
            }
            klingImgVideoReqBO.setNegativePrompt(imgVideoDTO.getNegativeprompt());
            klingImgVideoReqBO.setImage(imgVideoDTO.getFirstImg()); // + "?x-oss-process=image/format,avif/resize,w_" +
                                                                    // width);
            if (StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
                klingImgVideoReqBO.setImageTail(imgVideoDTO.getEndImg());
            }

            // 新的判断模型逻辑
            VideoSpecialEffectsVO videoStyle = null;
            if (imgVideoDTO.getStyleKey() != null) {
                videoStyle = getTextToVideoStyle(videoModelConfigPO.getVideoStyles(), imgVideoDTO.getStyleKey());
            }
            if (videoStyle != null) {
                klingImgVideoReqBO.setPrompt(videoStyle.getValue() + " " + klingImgVideoReqBO.getPrompt());
            }

            if (queryConcurrency() > 3) {
                JSONObject json = new JSONObject();
                json.put("id", imgDrawRecordPO.getId());
                json.put("type", 1);
                json.put("klingVideoReqBO", klingImgVideoReqBO);
                RedisUtil.rPushList(RedisUtil.REDIS_KLING_SUBMIT_TASK_LIST,
                        com.alibaba.fastjson2.JSON.toJSONString(json));
                videoJobId = KlingApiUtil.STATUS_QUEUING;
            } else {
                videoJobId = KlingApiUtil.postKlingTextToVideoGenerations(null, klingImgVideoReqBO, split[0], split[1],
                        imgDrawRecordPO.getId());
            }
        } else {
            KlingTextVideoReqBO klingTextVideoReqBO = new KlingTextVideoReqBO();
            if (imgVideoDTO.getScalesKey() != null) {
                List<ImgScaleDTO> scaleList = ImageModelUtil.getImgScaleDTOs(videoModelConfigPO.getVideoScales());
                Optional<ImgScaleDTO> matchingVideoScale = scaleList.stream()
                        .filter(scale -> scale.getKey() == imgVideoDTO.getScalesKey().intValue()).findFirst();
                if (matchingVideoScale.isPresent()) {
                    ImgScaleDTO imgScale = matchingVideoScale.get();
                    // imgVideoBO.setAspectRatio(imgScale.getWidth() +":"+ imgScale.getHeight());
                    // //比列key
                    imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(Integer.valueOf(imgScale.getWidth()),
                            Integer.valueOf(imgScale.getHeight())));
                    klingTextVideoReqBO.setAspectRatio(imgScale.getTitle());
                }
            }
            if (StringUtils.isNotBlank(videoLensByModel)) {
                klingTextVideoReqBO.setPrompt(imgVideoDTO.getPrompt() + videoLensByModel);
            } else {
                klingTextVideoReqBO.setPrompt(imgVideoDTO.getPrompt());
            }
            klingTextVideoReqBO.setNegativePrompt(imgVideoDTO.getNegativeprompt());
            if (queryConcurrency() > 3) {
                JSONObject json = new JSONObject();
                json.put("id", imgDrawRecordPO.getId());
                json.put("type", 0);
                json.put("klingVideoReqBO", klingTextVideoReqBO);
                RedisUtil.rPushList(RedisUtil.REDIS_KLING_SUBMIT_TASK_LIST,
                        com.alibaba.fastjson2.JSON.toJSONString(json));
                videoJobId = KlingApiUtil.STATUS_QUEUING;
            } else {
                videoJobId = KlingApiUtil.postKlingTextToVideoGenerations(klingTextVideoReqBO, null, split[0], split[1],
                        imgDrawRecordPO.getId());
            }
        }
        if (videoJobId == null || videoJobId.isEmpty()) {
            throw new E(CommonResultEnum.getSystemErrorMsg());
        }
        if (KlingApiUtil.STATUS_QUEUING.equalsIgnoreCase(videoJobId)) {
            imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_QUEUING.getValue());
        } else {
            imgDrawRecordPO.setVideoJobId(videoJobId);
        }

        ImgVideoVO imgVideoVO = new ImgVideoVO();
        if (imgVideoDTO.getInitImgUrls() != null) {
            List<String> initImgUrlList = imgVideoDTO.getInitImgUrls();
            List<String> collect = initImgUrlList.stream().filter(m -> StringUtils.isNotBlank(m))
                    .collect(Collectors.toList());

            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(collect));
            imgVideoVO
                    .setImage(initImgUrlList.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(",")));
        }
        imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        imgVideoVO.setVideoLensJson(imgVideoDTO.getVideoLensNew());
        imgVideoVO.setStyleJson(imgVideoDTO.getStyleJson());
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setFirstHue(imgVideoDTO.getFirstHue());
        imgVideoVO.setLastHue(imgVideoDTO.getLastHue());
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setLoop(imgVideoDTO.isLoop());
        imgVideoVO.setImgDetailFirst(imgVideoDTO.getImgDetailFirst());
        imgVideoVO.setImgDetailLast(imgVideoDTO.getImgDetailLast());

        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setPromptInit(videoModelConfigPO.getModelName()
                + " " + (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());

        if (StringUtils.isNotBlank(imgVideoDTO.getFirstImg()) || StringUtils.isNotBlank(imgVideoDTO.getEndImg())) {
            List<String> list = new ArrayList<>();
            list.add(imgVideoDTO.getFirstImg());
            list.add(imgVideoDTO.getEndImg());
            imgDrawRecordPO.setInitImgUrls(JSONArray.toJSONString(list));
        }

        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());
            ;
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("提交失败，请重试");
    }

    /**
     * 查询并发数
     * <p>
     * 查询并发数，包括处理并发数
     * 
     * @return 并发数
     */
    private Long queryConcurrency() {
        return imgDrawRecordMapper.selectCount(new LambdaQueryWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getModeAttribute, ImgOptModelEnum.VIDEO_ATTRIBUTE_KLING_BASICS.getValue())
                .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue()));

    }

    /**
     * 视频操作
     * <p>
     * 处理视频操作，包括处理视频生成请求
     * 
     * @param imgVideoOptDTO 视频生成请求参数
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    @Override
    @Transactional(rollbackFor = { Exception.class, E.class })
    public Result<Object> videoOpt(ImgVideoOptDTO imgVideoOptDTO) throws Exception {
        if (imgVideoOptDTO == null) {
            return Result.ERROR("参数不能为空");
        }
        if (imgVideoOptDTO.getInitImgUrls() == null || imgVideoOptDTO.getInitImgUrls().isEmpty()) {
            return Result.ERROR("延长图片不能为空");
        }
        imgVideoOptDTO.setInitImgUrls(null);

        Integer memberLevel = userDDRecordService.getUserVipInfo();
        boolean isVip = (memberLevel != null && memberLevel > 0);
        if (!isVip) {
            return Result.ERROR("VIP专属，请充值会员");
        }

        // 1、校验操作详情是否存在
        ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectById(imgVideoOptDTO.getImgDrawDetlId());
        if (imgDrawDetlPO == null) {
            return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
        }
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectById(imgDrawDetlPO.getDrawRecordId());
        if (imgDrawRecordPO == null) {
            return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
        }
        imgDrawRecordPO.setCreateTime(DateUtil.date());
        imgDrawRecordPO.setOperateTime(DateUtil.date());
        imgDrawRecordPO.setSuperId(imgDrawRecordPO.getId());
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());
        imgDrawRecordPO.setOptAttribute(ImgOptModelEnum.LUMA_OPT_ATTRIBUTE_VIDEO_EXTEND.getValue());
        imgDrawRecordPO.setId(IdWorker.getId());

        // 如果提示词不为null或空则校验合法性
        if (imgVideoOptDTO.getPrompt() != null) {
            if (!BStringUtil.isStringWithinLimitVideo(imgVideoOptDTO.getPrompt())) {
                return Result.ERROR("您的指令违反社区规定，请修改后重试");
            }
            // if(BTengXunUtil.textToExamineFailByVideo(imgVideoOptDTO.getPrompt(),
            // JwtNewUtil.getUserId())){
            // return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            // }
            if (BAliYunUtil.videoDetection(imgVideoOptDTO.getPrompt())) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            }
            if (BStringUtil.isChinese(imgVideoOptDTO.getPrompt())) {
                imgVideoOptDTO.setPromptUse(BAliYunUtil.textToEnglish(imgVideoOptDTO.getPrompt()));
            }
            if (StringUtils.isNotBlank(imgVideoOptDTO.getPromptUse()) && imgVideoOptDTO.getPromptUse().length() < 3) {
                return Result.ERROR(CommonResultEnum.VIDEO_PROMPT_ERROR.getValue());
            }
        }

        double dzQuantity = BDDUseNumEnum.getBDDUseNumEnumByModel(imgDrawRecordPO.getModeAttribute(), isVip);
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(ImgOptModelEnum.LUMA_OPT_ATTRIBUTE_VIDEO_EXTEND.getValue());
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ONE.getDtoKey())
                .remark(optTitleOne).build();
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), dzQuantity, flowRecordSub);
        imgDrawRecordPO.setUseDdQua(dzQuantity);
        try {
            // 1、梦工厂属性并且可以进行梦工厂原生操作
            if (imgDrawDetlPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_LUMA_BASICS.getValue()) {
                HashMap<Long, String> dictConfigMap = BThirdPartyKey
                        .getSecretKeyInfo(DictConfigEnum.DREAMFACTORY_API_KEY.getDictType());
                String key = dictConfigMap.get(DictConfigEnum.DREAMFACTORY_API_KEY.getDictKey());
                if (dictConfigMap == null || StringUtils.isBlank(key)) {
                    log.info("梦工厂没有可用的key: {}", "请立即更换");
                    throw new E("梦工厂视频模型升级维护中...");
                }
                LumaKeyFrames keyframes = new LumaKeyFrames();
                LumaFrames frame = new LumaFrames();
                frame.setType(LumaFramesTypeEnum.GENERATION.getType());
                frame.setId(imgDrawRecordPO.getVideoJobId());
                keyframes.setFrame0(frame);
                LumaOfficeVideoReq lumaOfficeVideoReq = new LumaOfficeVideoReq(imgVideoOptDTO.getPromptUse(), false,
                        keyframes);
                lumaOfficeVideoReq.setLoop(null);
                lumaOfficeVideoReq.setAspectRatio(null);

                String videoJobId = LumaApis.postToGenerateLumaOfficeVideo(lumaOfficeVideoReq, key);
                if (videoJobId == null || videoJobId.isEmpty()) {
                    throw new E("延长失败，请重试");
                }
                imgDrawRecordPO.setVideoJobId(videoJobId);

                ImgVideoVO imgVideoVO = new ImgVideoVO();
                imgVideoVO.setModelId(imgDrawRecordPO.getModeAttribute());
                imgVideoVO.setImage(
                        imgVideoOptDTO.getInitImgUrls() != null ? String.join(",", imgVideoOptDTO.getInitImgUrls())
                                : null);
                imgVideoVO.setPrompt(imgVideoOptDTO.getPrompt());

                imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
                imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
                imgDrawRecordPO.setPromptInit(
                        "梦工厂 延长 " + (imgVideoOptDTO.getPrompt() == null ? "" : imgVideoOptDTO.getPrompt()));
                imgDrawRecordPO.setInitImgUrls(imgVideoOptDTO.getInitImgUrls() != null
                        ? JSONArray.toJSONString(imgVideoOptDTO.getInitImgUrls())
                        : null);

                if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
                    ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
                    imgDrawHistoryVO.setInitImgUrls(imgVideoOptDTO.getInitImgUrls());
                    ;
                    return Result.SUCCESS(imgDrawHistoryVO);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 将用户使用点子数返回
            FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ZERO.getDtoKey())
                    .remark(optTitleOne).build();
            asyncService.updateRemainingTimes(imgDrawRecordPO.getUserId(), imgDrawRecordPO.getUseDdQua(), flowRecordPO);
            log.error("==*==延长视频失败：", e.getMessage(), e);
            return Result.ERROR("延长失败，请重试");
        }
        return Result.ERROR("延长失败，请重试");
    }

    /**
     * 处理初始图片URL列表
     * <p>
     * 处理并验证图片URL列表
     * 
     * @param initImgUrls 初始图片URL列表
     * @param token       认证令牌
     * @return 处理后的图片URL列表
     */
    private List<String> processInitImgUrls(List<String> initImgUrls, String token) {
        List<String> lumaAiImgUrls = new LinkedList<>();
        for (String imgUrl : initImgUrls) {
            try {
                String lumaAiImageUrl = LumaApis
                        .getLumaAiImageUrl(BFileUtil.downloadFileFromURL(imgUrl, "luma-upload-01.png"), token);
                if (lumaAiImageUrl != null) {
                    lumaAiImgUrls.add(lumaAiImageUrl);
                }
            } catch (IOException e) {
                log.error("梦工厂上传图片失败，请重试", e);
                throw new E("梦工厂上传图片失败，请重试");
            }
        }
        return lumaAiImgUrls;
    }

    /**
     * Pika视频生成方法
     * <p>
     * 使用Pika AI模型生成视频
     * 
     * @param imgVideoDTO        视频生成请求参数
     * @param imgDrawRecordPO    图片绘制记录对象
     * @param videoModelConfigPO 视频模型配置对象
     * @return 包含任务信息的结果对象
     * @throws Exception 处理异常
     */
    private Result<Object> pikaVideoGeneration(ImgVideoDTO imgVideoDTO, ImgDrawRecordPO imgDrawRecordPO,
            VideoModelConfigPO videoModelConfigPO) throws Exception {
        ImgVideoBO imgVideoBO = new ImgVideoBO();
        if (imgVideoDTO.getInitImgUrls() != null) {
            imgVideoBO.setImage(imgVideoDTO.getInitImgUrls().get(0));
        }
        this.getParms(imgDrawRecordPO, videoModelConfigPO, imgVideoDTO, imgVideoBO);
        imgVideoBO.setPrompt(imgDrawRecordPO.getPromptUse());
        imgVideoBO.setNegativeprompt(imgVideoDTO.getNegativepromptUse());
        imgVideoBO.setVideo(imgVideoDTO.getVideoUrl());
        String videoJobId = PikaApis.postTextAndFileToVideo(imgVideoBO);
        log.info("==*==PiKa视频任务id= ｛｝", videoJobId);
        if (videoJobId == null) {
            throw new E("提交视频失败，请重试");
        }
        imgDrawRecordPO.setVideoJobId(videoJobId);
        if (imgDrawRecordMapper.insert(imgDrawRecordPO) > 0) {
            ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, null);
            imgDrawHistoryVO.setInitImgUrls(imgVideoDTO.getInitImgUrls());
            ;
            return Result.SUCCESS(imgDrawHistoryVO);
        }
        throw new E("生成视频失败，请重试");
    }

    public static void getParms(ImgDrawRecordPO imgDrawRecordPO, VideoModelConfigPO videoModelConfigPO,
            ImgVideoDTO imgVideoDTO, ImgVideoBO imgVideoBO) {
        ImgVideoVO imgVideoVO = new ImgVideoVO();
        imgVideoVO.setModelId(imgVideoDTO.getModelId());
        imgVideoVO.setVideo(imgVideoDTO.getVideoUrl());
        imgDrawRecordPO.setPromptUse(imgVideoDTO.getPromptUse());
        StringBuffer promptJoint = new StringBuffer();
        promptJoint.append(videoModelConfigPO.getModelName() + " ");
        if (imgVideoDTO.getVideoUrl() != null
                && (imgVideoDTO.getInitImgUrls() == null || imgVideoDTO.getInitImgUrls().isEmpty())) {
            imgVideoVO.setImage(imgVideoDTO.getVideoUrl() + "?x-oss-process=video/snapshot,t_0,f_jpg");
        } else {
            imgVideoVO.setImage(imgVideoBO.getImage());
        }
        imgVideoVO.setPrompt(imgVideoDTO.getPrompt());
        imgVideoVO.setWidth(imgVideoDTO.getWidth());
        imgVideoVO.setHeight(imgVideoDTO.getHeight());
        imgVideoVO.setWeight(imgVideoDTO.getWeight());
        imgVideoVO.setNegativeprompt(imgVideoDTO.getNegativeprompt());

        if (imgVideoDTO.getStyleKey() != null) {
            List<VideoStyleBO> videoStyleList = VideoStyleBO
                    .getVideoStyleByJsonString(videoModelConfigPO.getVideoStyles());
            Optional<VideoStyleBO> matchingVideoStyle = videoStyleList.stream()
                    .filter(style -> style.getKey() == imgVideoDTO.getStyleKey().intValue()).findFirst();
            if (matchingVideoStyle.isPresent()) {
                imgVideoBO.setStyle(matchingVideoStyle.get().getNameEn());
                String prompt = matchingVideoStyle.get().getValue()
                        + (imgVideoDTO.getPromptUse() == null ? "" : imgVideoDTO.getPromptUse());
                imgDrawRecordPO.setPromptUse(prompt);
                promptJoint.append(matchingVideoStyle.get().getName() + " ");
            }
            imgVideoVO.setStyleKey(imgVideoDTO.getStyleKey());
        }
        if (imgVideoDTO.getWeight() != null) {
            int[] intArray = Arrays.stream(imgVideoDTO.getWeight()).mapToInt(Double::intValue).toArray();
            imgVideoBO.setFrameRate(intArray[0]);
            imgVideoBO.setMotion(intArray[1]);
            imgVideoBO.setGuidanceScale(intArray[2]);
            promptJoint.append("帧率:" + intArray[0] + " 动幅:" + intArray[1] + " 权重:" + intArray[2] + " ");
        }
        if (imgVideoDTO.getScalesKey() != null) {
            List<ImgScaleDTO> scaleList = ImageModelUtil.getImgScaleDTOs(videoModelConfigPO.getVideoScales());
            Optional<ImgScaleDTO> matchingVideoScale = scaleList.stream()
                    .filter(scale -> scale.getKey() == imgVideoDTO.getScalesKey().intValue()).findFirst();
            if (matchingVideoScale.isPresent()) {
                ImgScaleDTO imgScale = matchingVideoScale.get();
                imgVideoBO.setAspectRatio(imgScale.getWidth() + ":" + imgScale.getHeight()); // 比列key
                imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(Integer.valueOf(imgScale.getWidth()),
                        Integer.valueOf(imgScale.getHeight())));
            }
            imgVideoVO.setScalesKey(imgVideoDTO.getScalesKey());
        }
        if (imgVideoDTO.getVideoLens() != null) {
            Map<String, Integer> resultMap = BStringUtil.parseString(imgVideoDTO.getVideoLens());
            List<VideoStyleBO> videoLensList = VideoStyleBO
                    .getVideoStyleByJsonString(videoModelConfigPO.getVideoLens());
            if (videoLensList != null && videoLensList != null) {
                for (VideoStyleBO videoStyleBO : videoLensList) {
                    for (Map.Entry<String, Integer> entry : resultMap.entrySet()) {
                        if ("tilt".equalsIgnoreCase(entry.getKey())
                                && videoStyleBO.getKey() == entry.getValue().intValue()) {
                            imgVideoBO.setTilt(videoStyleBO.getValue());
                            continue;
                        }
                        if ("pan".equalsIgnoreCase(entry.getKey())
                                && videoStyleBO.getKey() == entry.getValue().intValue()) {
                            imgVideoBO.setPan(videoStyleBO.getValue());
                            continue;
                        }
                        if ("rotate".equalsIgnoreCase(entry.getKey())
                                && videoStyleBO.getKey() == entry.getValue().intValue()) {
                            imgVideoBO.setRotate(videoStyleBO.getValue());
                            continue;
                        }
                        if ("zoom".equalsIgnoreCase(entry.getKey())
                                && videoStyleBO.getKey() == entry.getValue().intValue()) {
                            imgVideoBO.setZoom(videoStyleBO.getValue());
                            continue;
                        }
                    }
                }
            }
            imgVideoVO.setVideoLens(imgVideoDTO.getVideoLens());
        }
        imgDrawRecordPO.setPromptInit(
                promptJoint.toString() + (imgVideoDTO.getPrompt() == null ? "" : imgVideoDTO.getPrompt()));
        imgDrawRecordPO.setInitImgObject(JSONObject.toJSONString(imgVideoVO));
        imgDrawRecordPO.setDescription(JSONObject.toJSONString(imgVideoVO));
    }

    public static Integer getSpecialEffects(String specialEffects, Integer key) {
        if (specialEffects == null || key == null) {
            return null;
        }
        List<VideoSpecialEffectsVO> specialEffectsVOS = VideoSpecialEffectsVO.videoSpecialEffectsList(specialEffects);
        Optional<VideoSpecialEffectsVO> matchingVideoSpecial = specialEffectsVOS.stream()
                .filter(style -> style.getKey().intValue() == key.intValue()).findFirst();
        if (matchingVideoSpecial.isPresent()) {
            return Integer.valueOf(matchingVideoSpecial.get().getValue());
        }
        log.info("未找到镜头相关的特效= ｛｝", key);
        return null;
    }

    // 新版会员模式参数
    private VideoSpeedVO getVideoSpeed(Integer modelKey, String speed) {
        List<VideoSpeedVO> videoSpeedVOList = VideoSpeedVO.videoSpeedVOList(speed);
        if (videoSpeedVOList == null || videoSpeedVOList.isEmpty()) {
            return null;
        }
        Optional<VideoSpeedVO> result = videoSpeedVOList.stream()
                .filter(model -> model.getKey().intValue() == modelKey.intValue()).findFirst();
        return result.orElse(null);
    }

    /**
     * 初始化任务视频
     *
     * @return
     * @throws IBusinessException
     */
    private ImgDrawRecordPO initImgDrawRecord(ImgVideoDTO imgVideoDTO) throws IBusinessException {
        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
        imgDrawRecordPO.setId(IdWorker.getId());
        imgDrawRecordPO.setModeAttribute(imgVideoDTO.getModelId());
        imgDrawRecordPO.setUserId(imgVideoDTO.getUserId());
        imgDrawRecordPO.setSubmitTime(System.currentTimeMillis());// 装载提交时间
        imgDrawRecordPO.setSuperId((long) ImgDrawEnum.SUPER_ID_DRAW.getValue());// 装载上级任务id
        imgDrawRecordPO.setStartTime(System.currentTimeMillis());// 装载开始时间
        imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());// 装载任务状态
        imgDrawRecordPO.setImgQuantity(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setIsPublish(ImgDrawEnum.IMG_NUMBER_ONE.getValue());
        imgDrawRecordPO.setFunType(ImgDrawEnum.FUN_TYPE_VIDEO.getValue());
        imgDrawRecordPO.setWidth(imgVideoDTO.getWidth());
        imgDrawRecordPO.setHeight(imgVideoDTO.getHeight());
        imgDrawRecordPO.setWhDivide(ImgDrawUtil.getWhDivide(imgVideoDTO.getWidth(), imgVideoDTO.getHeight()));
        imgDrawRecordPO.setCreateTime(DateUtil.date());
        imgDrawRecordPO.setOperateTime(DateUtil.date());
        return imgDrawRecordPO;
    }

    /**
     * 初始化历史记录
     *
     * @param imgDrawRecordPO
     * @return
     */
    private static ImgDrawHistoryVO intiImgDrawHistoryVO(ImgDrawRecordPO imgDrawRecordPO, List<String> stringList) {
        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
        imgDrawHistoryVO.setFunType(imgDrawRecordPO.getFunType());
        imgDrawHistoryVO.setUserId(imgDrawRecordPO.getUserId());
        imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawHistoryVO.setPrompt(imgDrawRecordPO.getPromptInit());
        imgDrawHistoryVO.setPromptUse(imgDrawRecordPO.getPromptUse());
        imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
        imgDrawHistoryVO.setInitImgUrls(stringList);
        imgDrawHistoryVO.setImgQuantity(imgDrawRecordPO.getImgQuantity());
        imgDrawHistoryVO.setOriginalImgId(imgDrawRecordPO.getOriginalImgId());
        imgDrawHistoryVO.setTimeTitle(ImgDrawUtil.getTimeTitle(imgDrawRecordPO.getCreateTime()));
        imgDrawHistoryVO.setOptTitleOne(ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute()));
        return imgDrawHistoryVO;
    }

    /**
     * 拉取多莫视频运行中的任务
     * <p>
     * 拉取多莫视频运行中的任务，包括处理视频生成请求
     * 
     * @throws IOException 输入输出异常
     */
    public void pullVideoOngoingTaskDuomo() throws IOException {
        List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper
                .selectList(new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_VIDEO.getValue())
                        .isNotNull(ImgDrawRecordPO::getVideoJobId)
                        .eq(ImgDrawRecordPO::getModeAttribute, ImgOptModelEnum.VIDEO_ATTRIBUTE_DOMO_BASICS.getValue()));
        log.info("拉取多莫视频运行中的任务，在线任务数为= " + imgDrawRecordPOList.size());
        if (imgDrawRecordPOList.isEmpty()) {
            return;
        }
        LambdaQueryWrapper<UserPrivateConfigPO> lamb = new LambdaQueryWrapper<UserPrivateConfigPO>();
        lamb.eq(UserPrivateConfigPO::getUserId, imgDrawRecordPOList.get(0).getUserId());
        lamb.eq(UserPrivateConfigPO::getFunType, 3);
        UserPrivateConfigPO userPrivateConfigPO = userPrivateConfigMapper.selectOne(lamb);

        for (ImgDrawRecordPO imgDrawRecordPO : imgDrawRecordPOList) {
            try {
                String videoPath = null;
                ImgDrawDetlPO imgDrawDetlPO = ImgDrawDetlPO.buildImgDrawDetlPO(imgDrawRecordPO.getId(),
                        imgDrawRecordPO.getOptAttribute(), imgDrawRecordPO.getModeAttribute(),
                        imgDrawRecordPO.getUserId(), 0, null, imgDrawRecordPO.getWhDivide(),
                        null, imgDrawRecordPO.getWidth(), imgDrawRecordPO.getHeight(), "webp", null);
                if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DOMO_BASICS.getValue()) {
                    HashMap<Long, String> dictConfigMap = BThirdPartyKey
                            .getSecretKeyInfo(DictConfigEnum.DOMO_API_KEY.getDictType());
                    if (dictConfigMap == null) {
                        log.info("DOMO没有可用的key: {}", "请立即更换");
                        continue;
                    }
                    videoPath = DomoApis.postsToGenerateJobState(
                            Collections.singletonList(imgDrawRecordPO.getVideoJobId()),
                            dictConfigMap.get(DictConfigEnum.DOMO_API_KEY.getDictKey()));
                }
                log.info("pullVideoOngoingTaskDuomo videoPath= {}", videoPath);
                if (null == videoPath) {
                    continue;
                }
                if (LumaApis.STATUS_FAILED.equalsIgnoreCase(videoPath)) {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                    continue;
                }
                if (LumaApis.STATUS_RUNNING.equalsIgnoreCase(videoPath)) {
                    continue;
                }

                // 更新ImgDrawRecordPO
                if (imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getFinishTime, System.currentTimeMillis())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue())) < 1) {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                    continue;
                }

                // 构建 ImgDrawDetlPO
                List<String> stringList = null;
                if (imgDrawRecordPO.getInitImgUrls() != null) {
                    stringList = ImgDrawUtil.getInitImgUrlsList(imgDrawRecordPO.getInitImgUrls());
                }

                String videoImgUrl = null;
                String videoUrl = BUrlUtil.getBaseCdnUrl(videoPath);
                if (stringList == null || stringList.isEmpty() || stringList.getFirst() == null
                        || stringList.getFirst().isEmpty()) {
                    videoImgUrl = videoUrl.concat("?x-oss-process=video/snapshot,t_300,m_fast");
                } else {
                    videoImgUrl = stringList.getFirst();
                }

                imgDrawDetlPO.setImgSourceUrl(videoImgUrl);
                imgDrawDetlPO.setImgUrl(videoImgUrl);
                imgDrawDetlPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
                imgDrawDetlPO.setIsSave(CommonIntEnum.SHOW_FALSE.getIntValue());
                imgDrawDetlPO.setIsOpen(CommonIntEnum.SHOW_FALSE.getIntValue());
                imgDrawDetlPO.setVideoUrl(videoUrl);

                if (userPrivateConfigPO != null && 1 == userPrivateConfigPO.getIsPrivate().intValue()) {
                    imgDrawDetlPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
                }

                // 插入 ImgDrawDetlPO
                if (imgDrawDetlMapper.insert(imgDrawDetlPO) <= 0) {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                    continue;
                }

                // 构建 ImgDrawHistoryVO
                ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, stringList);
                imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());
                imgDrawHistoryVO.setImgDrawDetls(getImgDrawDetlVOS(imgDrawDetlPO, imgDrawHistoryVO));

                // 推送通知
                handleVideoService.taskMessagePush(imgDrawHistoryVO, "任务已完成");
                handleVideoService.notificationMessage(imgDrawHistoryVO, imgDrawDetlPO.getImgUrl());
            } catch (Exception e) {
                log.error("拉取视频运行中的任务... 失败", e);
                try {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                } catch (Exception exception) {
                    e.printStackTrace();
                    log.error("视频生成回滚失败", exception.getMessage());
                    continue;
                }
                continue;
            }
        }
    }

    /**
     * 拉取视频运行中的任务
     * <p>
     * 拉取视频运行中的任务，包括处理视频生成请求
     * 
     * @throws IOException 输入输出异常
     */
    public void pullVideoOngoingTask() throws IOException {
        List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper
                .selectList(new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_VIDEO.getValue())
                        .isNotNull(ImgDrawRecordPO::getVideoJobId)
                        .in(ImgDrawRecordPO::getModeAttribute,
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_SD_BASICS.getValue(),
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_BYTE_LENS.getValue(),
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_LE_BASICS.getValue(),
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_LUMA_BASICS.getValue(),
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY2_BASICS.getValue(),
                                // ImgOptModelEnum.VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS.getValue(), 下架
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI2_BASICS.getValue(),
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI3_BASICS.getValue(),
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI4_BASICS.getValue()));
        log.info("pullVideoOngoingTask拉取任务，在线任务数为= " + imgDrawRecordPOList.size());
        if (imgDrawRecordPOList.isEmpty()) {
            return;
        }

        LambdaQueryWrapper<UserPrivateConfigPO> lamb = new LambdaQueryWrapper<UserPrivateConfigPO>();
        lamb.eq(UserPrivateConfigPO::getUserId, imgDrawRecordPOList.get(0).getUserId());
        lamb.eq(UserPrivateConfigPO::getFunType, 3);
        UserPrivateConfigPO userPrivateConfigPO = userPrivateConfigMapper.selectOne(lamb);

        for (ImgDrawRecordPO imgDrawRecordPO : imgDrawRecordPOList) {
            try {
                String videoPath = null;
                ImgDrawDetlPO imgDrawDetlPO = ImgDrawDetlPO.buildImgDrawDetlPO(imgDrawRecordPO.getId(),
                        imgDrawRecordPO.getOptAttribute(), imgDrawRecordPO.getModeAttribute(),
                        imgDrawRecordPO.getUserId(), 0, null, imgDrawRecordPO.getWhDivide(),
                        null, imgDrawRecordPO.getWidth(), imgDrawRecordPO.getHeight(), "webp", null);
                if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_SD_BASICS.getValue()) {
                    videoPath = SDApisUtil.getSDVideo(imgDrawRecordPO.getVideoJobId());
                } else if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_LE_BASICS.getValue()) {
                    HashMap<Long, String> dictConfigMap = BThirdPartyKey
                            .getSecretKeyInfo(DictConfigEnum.LE_DRAW_KEY.getDictType());
                    if (dictConfigMap == null) {
                        log.info("LE没有可用的key: {}", "请立即更换");
                        continue;
                    }
                    videoPath = LeonardoUtil.getLeonardoVideo(imgDrawRecordPO.getVideoJobId(),
                            dictConfigMap.get(DictConfigEnum.LE_DRAW_KEY.getDictKey()));
                } else if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_PIKA_BASICS
                        .getValue()) {
                    videoPath = PikaApis.getToJobInfo(imgDrawRecordPO, PiKaConfig.domainName);
                } else if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_LUMA_BASICS
                        .getValue()) {
                    // imgDrawRecordPO.getModeAttribute() ==
                    // ImgOptModelEnum.VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS.getValue() luma 下架不用
                    if (pullTaskSwitch) {
                        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                                .getSecretKeyInfo(DictConfigEnum.DREAMFACTORY_API_KEY.getDictType());
                        String key = dictConfigMap.get(DictConfigEnum.DREAMFACTORY_API_KEY.getDictKey());
                        if (dictConfigMap == null || StringUtils.isBlank(key)) {
                            log.info("梦工厂没有可用的key: {}", "请立即更换");
                            throw new E("梦工厂视频模型升级维护中...");
                        }
                        videoPath = LumaApis.postsToGenerateJobState(imgDrawRecordPO.getVideoJobId(), key,
                                imgDrawRecordPO, imgDrawDetlPO);
                    } else {
                        videoPath = "running";
                    }
                } else if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI2_BASICS
                        .getValue()) {
                    HashMap<Long, String> dictConfigMap = BThirdPartyKey
                            .getSecretKeyInfo(DictConfigEnum.RUNWAY_API_KEY.getDictType());
                    String token = dictConfigMap.get(DictConfigEnum.RUNWAY_API_KEY.getDictKey());
                    if (token == null) {
                        log.info("RUNWAY没有可用的key: {}", "请立即更换");
                        throw new E("RUNWAY视频模型升级维护中...");
                    }
                    // videoPath = RunwayApis.postsToGenerateJobState(token, imgDrawRecordPO);
                    videoPath = RunwayOfficeApis.postsToGenerateJobState(token, imgDrawRecordPO);
                } else if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY2_BASICS
                        .getValue()
                        || imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY3AT_BASICS
                                .getValue()
                        || imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI3_BASICS
                                .getValue()
                        || imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI4_BASICS
                                .getValue()) {
                    String token = RedisUtil.getValue(BRedisKeyEnum.REDIS_RUNWAY_TOKEN_KEY.getKey());
                    if (token == null) {
                        log.info("RUNWAY没有可用的key: {}", "请立即更换");
                        throw new E("RUNWAY视频模型升级维护中...");
                    }
                    videoPath = RunwayApis.postsToGenerateJobState(token, imgDrawRecordPO);
                }
                log.info("pullVideoOngoingTask videoPath= {}", videoPath);
                if (LumaApis.STATUS_FAILED.equalsIgnoreCase(videoPath)) {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                    continue;
                }
                if (LumaApis.STATUS_RUNNING.equalsIgnoreCase(videoPath)) {
                    continue;
                }

                // 更新ImgDrawRecordPO
                if (imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getFinishTime, System.currentTimeMillis())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue())) < 1) {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                    continue;
                }

                // 构建 ImgDrawDetlPO
                List<String> stringList = null;
                if (imgDrawRecordPO.getInitImgUrls() != null) {
                    stringList = ImgDrawUtil.getInitImgUrlsList(imgDrawRecordPO.getInitImgUrls());
                }

                String videoImgUrl = null;
                String videoUrl = BUrlUtil.getBaseCdnUrl(videoPath);
                if (stringList == null || stringList.isEmpty() || stringList.getFirst() == null
                        || stringList.getFirst().isEmpty()) {
                    videoImgUrl = videoUrl.concat("?x-oss-process=video/snapshot,t_300,m_fast");
                } else {
                    videoImgUrl = stringList.getFirst();
                }

                imgDrawDetlPO.setImgSourceUrl(videoImgUrl);
                imgDrawDetlPO.setImgUrl(videoImgUrl);
                imgDrawDetlPO.setIsSave(CommonIntEnum.SHOW_FALSE.getIntValue());
                imgDrawDetlPO.setIsOpen(CommonIntEnum.SHOW_FALSE.getIntValue());
                imgDrawDetlPO.setVideoUrl(videoUrl);

                if (userPrivateConfigPO != null && 1 == userPrivateConfigPO.getIsPrivate().intValue()) {
                    imgDrawDetlPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
                }

                // 插入 ImgDrawDetlPO
                if (imgDrawDetlMapper.insert(imgDrawDetlPO) <= 0) {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                    continue;
                }

                // 构建 ImgDrawHistoryVO
                ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, stringList);
                imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());
                imgDrawHistoryVO.setImgDrawDetls(getImgDrawDetlVOS(imgDrawDetlPO, imgDrawHistoryVO));

                // 推送通知
                handleVideoService.taskMessagePush(imgDrawHistoryVO, "任务已完成");
                handleVideoService.notificationMessage(imgDrawHistoryVO, imgDrawDetlPO.getImgUrl());
            } catch (Exception e) {
                log.error("拉取视频运行中的任务... 失败", e);
                try {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                } catch (Exception exception) {
                    e.printStackTrace();
                    log.error("视频生成回滚失败", exception.getMessage());
                }
            }
        }
    }

    /**
     * 拉取视频任务
     * <p>
     * 拉取视频任务，包括处理视频生成请求
     * 
     * @throws Exception 处理异常
     */
    @Override
    public void pullVideoTaskInChina() {
        List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper
                .selectList(new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_VIDEO.getValue())
                        .isNotNull(ImgDrawRecordPO::getVideoJobId)
                        .in(ImgDrawRecordPO::getModeAttribute, ImgOptModelEnum.VIDEO_ATTRIBUTE_ZHIPU_BASICS.getValue(),
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_BASICS.getValue(),
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_V2_BASICS.getValue(),
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_KLING_BASICS.getValue(),
                                ImgOptModelEnum.VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS.getValue()));
        log.info("pullVideoTaskInChina拉取任务，在线任务数为= " + imgDrawRecordPOList.size());
        if (imgDrawRecordPOList.isEmpty()) {
            return;
        }

        LambdaQueryWrapper<UserPrivateConfigPO> lamb = new LambdaQueryWrapper<UserPrivateConfigPO>();
        lamb.eq(UserPrivateConfigPO::getUserId, imgDrawRecordPOList.get(0).getUserId());
        lamb.eq(UserPrivateConfigPO::getFunType, 3);
        UserPrivateConfigPO userPrivateConfigPO = userPrivateConfigMapper.selectOne(lamb);

        for (ImgDrawRecordPO imgDrawRecordPO : imgDrawRecordPOList) {
            try {
                String videoPath = null;
                ImgDrawDetlPO imgDrawDetlPO = ImgDrawDetlPO.buildImgDrawDetlPO(imgDrawRecordPO.getId(),
                        imgDrawRecordPO.getOptAttribute(), imgDrawRecordPO.getModeAttribute(),
                        imgDrawRecordPO.getUserId(), 0, null, imgDrawRecordPO.getWhDivide(),
                        null, imgDrawRecordPO.getWidth(), imgDrawRecordPO.getHeight(), "webp", null);
                // 智谱视频
                if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_ZHIPU_BASICS.getValue()) {
                    HashMap<Long, String> dictConfigMap = BThirdPartyKey
                            .getSecretKeyInfo(DictConfigEnum.ZHIPU_API_KEY.getDictType());
                    String key = dictConfigMap.get(DictConfigEnum.ZHIPU_API_KEY.getDictKey());
                    if (StringUtils.isBlank(key)) {
                        log.info("智谱视频没有可用的key: {}", "请立即更换");
                        throw new E("智谱视频模型升级维护中...");
                    }
                    videoPath = ZhiPuApiUtil.getZhiPuVideoTaskResult(imgDrawRecordPO.getVideoJobId(), key,
                            imgDrawRecordPO, imgDrawDetlPO);
                }
                // 海螺视频
                else if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_BASICS.getValue()
                        || imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_V2_BASICS
                                .getValue()) {
                    HashMap<Long, String> dictConfigMap = BThirdPartyKey
                            .getSecretKeyInfo(DictConfigEnum.HAILUO_API_KEY.getDictType());
                    String key = dictConfigMap.get(DictConfigEnum.HAILUO_API_KEY.getDictKey());
                    if (StringUtils.isBlank(key)) {
                        log.info("海螺视频没有可用的key: {}", "请立即更换");
                        throw new E("海螺视频模型升级维护中...");
                    }
                    videoPath = HaiLuoApiUtil.getHaiLuoVideoTaskResult(imgDrawRecordPO.getVideoJobId(), key,
                            imgDrawRecordPO, imgDrawDetlPO);
                }
                // 可灵视频
                else if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_KLING_BASICS
                        .getValue()) {
                    HashMap<Long, String> dictConfigMap = BThirdPartyKey
                            .getSecretKeyInfo(DictConfigEnum.KLING_API_KEY.getDictType());
                    String key = dictConfigMap.get(DictConfigEnum.KLING_API_KEY.getDictKey());
                    if (StringUtils.isBlank(key)) {
                        log.info("可灵视频没有可用的key: {}", "请立即更换");
                        throw new E("可灵视频模型升级维护中...");
                    }
                    String[] split = key.split(",");
                    videoPath = KlingApiUtil.getKlingVideoTaskResult(imgDrawRecordPO.getVideoJobId(), split[0],
                            split[1], imgDrawRecordPO, imgDrawDetlPO);
                }
                // 万相视频
                else if (imgDrawRecordPO.getModeAttribute() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS
                        .getValue()) {
                    HashMap<Long, String> dictConfigMap = BThirdPartyKey
                            .getSecretKeyInfo(DictConfigEnum.WANX_API_KEY.getDictType());
                    String key = dictConfigMap.get(DictConfigEnum.WANX_API_KEY.getDictKey());
                    if (StringUtils.isBlank(key)) {
                        log.info("万相视频没有可用的key: {}", "请立即更换");
                        throw new E("万相视频模型升级维护中...");
                    }
                    videoPath = WanxApiUtil.getWanxVideoTaskResult(imgDrawRecordPO.getVideoJobId(), key,
                            imgDrawRecordPO, imgDrawDetlPO);
                }
                log.info("pullVideoOngoingTask videoPath= {}", videoPath);
                if (Objects.equals(TusiJobStateEnum.FAILED.getState(), videoPath)) {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                    continue;
                }
                if (Objects.equals(TusiJobStateEnum.RUNNING.getState(), videoPath)) {
                    continue;
                }

                // 更新ImgDrawRecordPO
                if (imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getFinishTime, System.currentTimeMillis())
                        .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue())) < 1) {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                    continue;
                }

                // 构建 ImgDrawDetlPO
                List<String> stringList = null;
                if (imgDrawRecordPO.getInitImgUrls() != null) {
                    stringList = ImgDrawUtil.getInitImgUrlsList(imgDrawRecordPO.getInitImgUrls());
                }

                String videoImgUrl = null;
                String videoUrl = BUrlUtil.getBaseCdnUrl(videoPath);
                if (stringList == null || stringList.isEmpty() || stringList.getFirst() == null
                        || stringList.getFirst().isEmpty()) {
                    videoImgUrl = videoUrl.concat("?x-oss-process=video/snapshot,t_300,m_fast");
                } else {
                    videoImgUrl = stringList.getFirst();
                }

                imgDrawDetlPO.setImgSourceUrl(videoImgUrl);
                imgDrawDetlPO.setImgUrl(videoImgUrl);
                imgDrawDetlPO.setIsSave(CommonIntEnum.SHOW_FALSE.getIntValue());
                imgDrawDetlPO.setIsOpen(CommonIntEnum.SHOW_FALSE.getIntValue());
                imgDrawDetlPO.setVideoUrl(videoUrl);

                if (userPrivateConfigPO != null && 1 == userPrivateConfigPO.getIsPrivate().intValue()) {
                    imgDrawDetlPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
                }

                // 插入 ImgDrawDetlPO
                if (imgDrawDetlMapper.insert(imgDrawDetlPO) <= 0) {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                    continue;
                }

                // 构建 ImgDrawHistoryVO
                ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO, stringList);
                imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());
                imgDrawHistoryVO.setImgDrawDetls(getImgDrawDetlVOS(imgDrawDetlPO, imgDrawHistoryVO));

                // 推送通知
                handleVideoService.taskMessagePush(imgDrawHistoryVO, "任务已完成");
                handleVideoService.notificationMessage(imgDrawHistoryVO, imgDrawDetlPO.getImgUrl());
            } catch (Exception e) {
                log.error("拉取视频运行中的任务... 失败", e);
                try {
                    this.videoJobFailedRollbackDz(imgDrawRecordPO);
                } catch (Exception exception) {
                    e.printStackTrace();
                    log.error("视频生成回滚失败", exception.getMessage());
                }
            }
        }
    }

    private static VideoSpecialEffectsVO getTextToVideoStyle(String videoStyles, Integer styleKey) throws Exception {
        return VideoSpecialEffectsVO.videoSpecialEffectsList(videoStyles).stream()
                .filter(item -> Objects.equals(item.getKey(), styleKey))
                .findFirst()
                .orElse(null);
    }

    private void videoJobFailedRollbackDz(ImgDrawRecordPO imgDrawRecordPO) throws Exception {
        // 将用户使用点子数返回
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute());
        FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ZERO.getDtoKey())
                .remark(optTitleOne).build();
        asyncService.updateRemainingTimes(imgDrawRecordPO.getUserId(), imgDrawRecordPO.getUseDdQua(), flowRecordPO);
        handleVideoService.videoJobFailedRollbackDz(imgDrawRecordPO);
    }

    @Override
    public Result<Object> getVideoInfo(Long recordId) throws IOException {
        if (recordId == null) {
            return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
        }
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectOne(new LambdaQueryWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getId, recordId));
        if (imgDrawRecordPO == null || imgDrawRecordPO.getVideoJobId() == null) {
            return Result.ERROR(CommonResultEnum.PARAMETER_ERROR.getValue());
        }
        String videoPath = null;
        try {
            videoPath = SDApisUtil.getSDVideo(imgDrawRecordPO.getVideoJobId());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("getVideoInfo= {}", e.getMessage(), e);
            return Result.ERROR("视频已失效");
        }
        if (videoPath == null) {
            return Result.ERROR("视频已失效");
        }

        ImgDrawHistoryVO imgDrawHistoryVO = intiImgDrawHistoryVO(imgDrawRecordPO,
                ImgDrawUtil.getInitImgUrlsList(imgDrawRecordPO.getInitImgUrls()));
        imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());

        LambdaQueryWrapper<ImgDrawDetlPO> queryWrapper = new LambdaQueryWrapper<ImgDrawDetlPO>()
                .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawRecordPO.getId());
        List<ImgDrawDetlPO> imgDrawDetlPOList = imgDrawDetlMapper.selectList(queryWrapper);
        imgDrawHistoryVO.setImgDrawDetls(getImgDrawDetlVOS(
                imgDrawDetlPOList != null && imgDrawDetlPOList.size() > 0 ? imgDrawDetlPOList.get(0) : null,
                imgDrawHistoryVO));
        return Result.SUCCESS(imgDrawHistoryVO);
    }

    /**
     * 获取图片绘制详情列表
     * <p>
     * 获取图片绘制详情列表，包括处理图片绘制详情列表
     * 
     * @param imgDrawDetlPO    图片绘制详情对象
     * @param imgDrawHistoryVO 图片绘制历史记录视图对象
     * @return 图片绘制详情列表
     */
    @NotNull
    private static List<ImgDrawDetlVO> getImgDrawDetlVOS(ImgDrawDetlPO imgDrawDetlPO,
            ImgDrawHistoryVO imgDrawHistoryVO) {
        List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();
        ImgDrawDetlVO imgDrawDetlVO = new ImgDrawDetlVO();
        imgDrawDetlVO.setId(imgDrawDetlPO.getId());
        imgDrawDetlVO.setDrawRecordId(imgDrawDetlPO.getDrawRecordId());
        imgDrawDetlVO.setOptAttribute(imgDrawDetlPO.getOptAttribute());
        imgDrawDetlVO.setImgIndex(imgDrawDetlPO.getImgIndex());
        imgDrawDetlVO.setWhDivide(imgDrawDetlPO.getWhDivide());
        imgDrawDetlVO.setImgWidth(imgDrawDetlPO.getImgWidth());
        imgDrawDetlVO.setImgHeight(imgDrawDetlPO.getImgHeight());
        imgDrawDetlVO.setImgHue(imgDrawDetlPO.getImgHue());
        imgDrawDetlVO.setImgSize(imgDrawDetlPO.getImgSize());
        imgDrawDetlVO.setImgSourceUrl(imgDrawDetlPO.getImgSourceUrl());
        imgDrawDetlVO.setImgUrl(imgDrawDetlPO.getImgUrl());
        imgDrawDetlVO.setImgType(imgDrawDetlPO.getImgType());
        imgDrawDetlVO.setIsPublish(imgDrawDetlPO.getIsPublish());
        imgDrawDetlVO.setVideoUrl(imgDrawDetlPO.getVideoUrl());
        if (Objects.equals(imgDrawDetlPO.getIsPublish(), BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue())) {
            imgDrawHistoryVO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue());
        }
        imgDrawDetlVOS.add(imgDrawDetlVO);
        return imgDrawDetlVOS;
    }

    /**
     * 根据Suno获取图片绘制详情列表
     * <p>
     * 根据Suno获取图片绘制详情列表，包括处理图片绘制详情列表
     * 
     * @param imgDrawDetlPOS   图片绘制详情列表
     * @param imgDrawHistoryVO 图片绘制历史记录视图对象
     * @return 图片绘制详情列表
     */
    public static List<ImgDrawDetlVO> getImgDrawDetlVOSBySuno(List<ImgDrawDetlPO> imgDrawDetlPOS,
            ImgDrawHistoryVO imgDrawHistoryVO) {
        List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();
        for (ImgDrawDetlPO imgDrawDetlPO : imgDrawDetlPOS) {
            ImgDrawDetlVO imgDrawDetlVO = new ImgDrawDetlVO();
            imgDrawDetlVO.setId(imgDrawDetlPO.getId());
            imgDrawDetlVO.setDrawRecordId(imgDrawDetlPO.getDrawRecordId());
            imgDrawDetlVO.setOptAttribute(imgDrawDetlPO.getOptAttribute());
            imgDrawDetlVO.setImgIndex(imgDrawDetlPO.getImgIndex());
            imgDrawDetlVO.setWhDivide(imgDrawDetlPO.getWhDivide());
            imgDrawDetlVO.setImgWidth(imgDrawDetlPO.getImgWidth());
            imgDrawDetlVO.setImgHeight(imgDrawDetlPO.getImgHeight());
            imgDrawDetlVO.setImgHue(imgDrawDetlPO.getImgHue());
            imgDrawDetlVO.setImgSize(imgDrawDetlPO.getImgSize());
            imgDrawDetlVO.setImgUrl(imgDrawDetlPO.getImgUrl());
            imgDrawDetlVO.setImgSourceUrl(imgDrawDetlPO.getImgSourceUrl());
            imgDrawDetlVO.setImgType(imgDrawDetlPO.getImgType());
            imgDrawDetlVO.setIsPublish(imgDrawDetlPO.getIsPublish());
            imgDrawDetlVO.setAudioUrl(imgDrawDetlPO.getAudioUrl());
            imgDrawDetlVO.setVideoUrl(imgDrawDetlPO.getVideoUrl());
            if (Objects.equals(imgDrawDetlPO.getIsPublish(), BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue())) {
                imgDrawHistoryVO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue());
            }
            imgDrawDetlVOS.add(imgDrawDetlVO);
        }
        return imgDrawDetlVOS;
    }

    // TODO 监听24小时视频失效任务
    public void listenVideoExpiredTask() {
        List<ImgDrawRecordPO> imgDrawRecordList = imgDrawRecordMapper
                .selectList(new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_VIDEO.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue()));
        if (imgDrawRecordList == null || imgDrawRecordList.isEmpty()) {
            return;
        }
        for (ImgDrawRecordPO imgDrawRecordPO : imgDrawRecordList) {
            if (BStringUtil.getRemainingTime(imgDrawRecordPO.getCreateTime()) == null) {
                String prompt = BStringUtil.getRemainingTime(imgDrawRecordPO.getCreateTime());
                imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                        .set(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
                        .set(ImgDrawRecordPO::getPromptInit, prompt)
                        .set(ImgDrawRecordPO::getPromptUse, prompt));
            }
        }
    }

    @Override
    public void doVideoByRunwaySubmitTask() throws InterruptedException {
        List<String> runwayTaskList = RedisUtil.lGet(RedisUtil.REDIS_RUNWAY_SUBMIT_TASK_LIST, 0, 9);
        if (runwayTaskList != null && !runwayTaskList.isEmpty()) {
            for (String s : runwayTaskList) {
                if (StringUtils.isNotBlank(s)) {
                    JSONObject jsonObject = JSONObject.parseObject(s);
                    Long id = jsonObject.getLong("id");
                    RunWayTaskReq runWayTaskReq = jsonObject.getObject("runWayTaskReq", RunWayTaskReq.class);
                    if (runWayTaskReq != null) {
                        if (runWayTaskReq.getOptions().getSeed() != null) {
                            runWayTaskReq.getOptions().setSeed(BUtils.getSeed());
                        }
                        if (runWayTaskReq.getOptions().getGen2Options() != null) {
                            runWayTaskReq.getOptions().getGen2Options().setSeed(BUtils.getSeed());
                            runWayTaskReq.getOptions().setResolution(null);
                            runWayTaskReq.getOptions().setImageAsEndFrame(null);
                            runWayTaskReq.getOptions().setSeed(null);
                            runWayTaskReq.getOptions().setWatermark(null);
                        }
                        String token = RedisUtil.getValue(BRedisKeyEnum.REDIS_RUNWAY_TOKEN_KEY.getKey());
                        String videoJobId = RunwayApis.postToGenerateRunwayVideo(runWayTaskReq, token);
                        // 将去runway生成不成功的放到队列里，定时器去处理
                        if (StringUtils.isNotBlank(videoJobId) && "429".equals(videoJobId)) {
                        } else if (StringUtils.isNotBlank(videoJobId) && !"429".equals(videoJobId)) {
                            // 更新imgrecord表
                            ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
                            imgDrawRecordPO.setId(id);
                            imgDrawRecordPO.setVideoJobId(videoJobId);
                            imgDrawRecordMapper.updateById(imgDrawRecordPO);
                            RedisUtil.remove(RedisUtil.REDIS_RUNWAY_SUBMIT_TASK_LIST, 0, s);
                        }
                    }
                    Thread.sleep(500);
                }
            }
        }
    }

    /**
     * 提交Runway视频任务
     * <p>
     * 提交Runway视频任务，包括处理视频生成请求
     * 
     * @throws InterruptedException 中断异常
     */
    @Override
    public void doVideoByRunwaySubmitVideoTask() throws InterruptedException {
        List<String> runwayTaskList = RedisUtil.lGet(RedisUtil.REDIS_RUNWAY_SUBMIT_TASK_VIDEO_LIST, 0, 9);
        if (runwayTaskList != null && !runwayTaskList.isEmpty()) {
            for (String s : runwayTaskList) {
                if (StringUtils.isNotBlank(s)) {
                    JSONObject jsonObject = JSONObject.parseObject(s);
                    Long id = jsonObject.getLong("id");
                    RunWayVideoTaskReq runWayTaskReq = jsonObject.getObject("runWayVideoTaskReq",
                            RunWayVideoTaskReq.class);
                    if (runWayTaskReq != null) {
                        if (runWayTaskReq.getOptions().getSeed() != null) {
                            runWayTaskReq.getOptions().setSeed(BUtils.getSeed());
                        }

                        String token = RedisUtil.getValue(BRedisKeyEnum.REDIS_RUNWAY_TOKEN_KEY.getKey());
                        String videoJobId = RunwayApis.postToGenerateRunwayVideo(runWayTaskReq, token);
                        // 将去runway生成不成功的放到队列里，定时器去处理
                        if (StringUtils.isNotBlank(videoJobId) && "429".equals(videoJobId)) {
                        } else if (StringUtils.isNotBlank(videoJobId) && !"429".equals(videoJobId)) {
                            // 更新imgrecord表
                            ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
                            imgDrawRecordPO.setId(id);
                            imgDrawRecordPO.setVideoJobId(videoJobId);
                            imgDrawRecordMapper.updateById(imgDrawRecordPO);
                            RedisUtil.remove(RedisUtil.REDIS_RUNWAY_SUBMIT_TASK_VIDEO_LIST, 0, s);
                        }
                    }
                    Thread.sleep(500);
                }
            }
        }
    }

    /**
     * 提交Runway角色驱动任务
     * <p>
     * 提交Runway角色驱动任务，包括处理角色驱动视频生成请求
     * 
     * @throws InterruptedException 中断异常
     */
    @Override
    public void doVideoByRunwaySubmitRoleTask() throws InterruptedException {
        List<String> runwayTaskList = RedisUtil.lGet(RedisUtil.REDIS_RUNWAY_SUBMIT_TASK_VIDEO_ROLE_LIST, 0, 9);
        if (runwayTaskList != null && !runwayTaskList.isEmpty()) {
            for (String videoParam : runwayTaskList) {
                if (StringUtils.isNotBlank(videoParam)) {
                    JSONObject jsonObject = JSONObject.parseObject(videoParam);
                    Long id = jsonObject.getLong("id");
                    RunWayVideoRoleTaskReq runWayTaskReq = jsonObject.getObject("runWayVideoTaskReq",
                            RunWayVideoRoleTaskReq.class);
                    if (runWayTaskReq != null) {
                        String token = RedisUtil.getValue(BRedisKeyEnum.REDIS_RUNWAY_TOKEN_KEY.getKey());
                        String videoJobId = RunwayApis.postRunwayVToVGenerateRoleDriven(runWayTaskReq, token);
                        // 将去runway生成不成功的放到队列里，定时器去处理
                        if (videoJobId == null) {
                            RedisUtil.remove(RedisUtil.REDIS_RUNWAY_SUBMIT_TASK_VIDEO_ROLE_LIST, 0, videoParam);
                            continue;
                        }
                        if (videoJobId.equals("429")) {
                            continue;
                        }
                        // 更新imgrecord表
                        ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
                        imgDrawRecordPO.setId(id);
                        imgDrawRecordPO.setVideoJobId(videoJobId);
                        imgDrawRecordMapper.updateById(imgDrawRecordPO);
                        RedisUtil.remove(RedisUtil.REDIS_RUNWAY_SUBMIT_TASK_VIDEO_ROLE_LIST, 0, videoParam);
                    }
                    Thread.sleep(400);
                }
            }
        }
    }

    /**
     * 提交海螺任务
     * <p>
     * 提交海螺任务，包括处理海螺视频生成请求
     * 
     * @throws InterruptedException 中断异常
     */
    @Override
    public void doVideoByHailuoSubmitTask() throws InterruptedException {
        List<String> runwayTaskList = RedisUtil.lGet(RedisUtil.REDIS_HAILUO_SUBMIT_TASK_LIST, 0, 9);
        if (runwayTaskList != null && !runwayTaskList.isEmpty()) {
            for (String s : runwayTaskList) {
                if (StringUtils.isNotBlank(s)) {
                    JSONObject jsonObject = JSONObject.parseObject(s);
                    Long id = jsonObject.getLong("id");
                    ImgDrawRecordPO recordPO = imgDrawRecordMapper.selectById(id);
                    Date createTime = recordPO.getCreateTime();
                    Date currentTime = new Date();

                    long differenceInMillis = currentTime.getTime() - createTime.getTime();
                    long differenceInMinutes = TimeUnit.MILLISECONDS.toMinutes(differenceInMillis);
                    if (differenceInMinutes > 20) {
                        RedisUtil.remove(RedisUtil.REDIS_HAILUO_SUBMIT_TASK_LIST, 0, s);
                    }

                    HaiLuoRequestBO runWayTaskReq = jsonObject.getObject("haiLuoRequestBO", HaiLuoRequestBO.class);
                    if (runWayTaskReq != null) {

                        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                                .getSecretKeyInfo(DictConfigEnum.HAILUO_API_KEY.getDictType());
                        String key = dictConfigMap.get(DictConfigEnum.HAILUO_API_KEY.getDictKey());
                        if (dictConfigMap == null || StringUtils.isBlank(key)) {
                            log.info("海螺视频没有可用的key: {}", "请立即更换");
                            throw new E("海螺视频模型升级维护中...");
                        }
                        String videoJobId = HaiLuoApiUtil.postHaiLuoTextToVideoGenerations(runWayTaskReq, key);

                        // 将去runway生成不成功的放到队列里，定时器去处理
                        if (StringUtils.isNotBlank(videoJobId) && "429".equals(videoJobId)) {
                        } else if (StringUtils.isNotBlank(videoJobId) && !"429".equals(videoJobId)) {
                            // 更新imgrecord表
                            ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
                            imgDrawRecordPO.setId(id);
                            imgDrawRecordPO.setVideoJobId(videoJobId);
                            imgDrawRecordMapper.updateById(imgDrawRecordPO);
                            RedisUtil.remove(RedisUtil.REDIS_HAILUO_SUBMIT_TASK_LIST, 0, s);
                        }
                    }
                    Thread.sleep(500);
                }
            }
        }
    }

    /**
     * 提交Kling任务
     * <p>
     * 提交Kling任务，包括处理Kling视频生成请求
     * 
     * @throws InterruptedException 中断异常
     */
    @Override
    public void doVideoByKlingSubmitTask() throws InterruptedException {
        log.info("提交kling排队中的任务。。。。。");
        List<String> klingTaskList = RedisUtil.lGet(RedisUtil.REDIS_KLING_SUBMIT_TASK_LIST, 0, 9);
        if (klingTaskList == null || klingTaskList.isEmpty()) {
            return;
        }
        HashMap<Long, String> dictConfigMap = BThirdPartyKey
                .getSecretKeyInfo(DictConfigEnum.KLING_API_KEY.getDictType());
        if (dictConfigMap == null) {
            throw new E("可灵没有账号...");
        }
        String klingApiKey = dictConfigMap.get(DictConfigEnum.KLING_API_KEY.getDictKey());
        if (StringUtils.isBlank(klingApiKey)) {
            throw new E("可灵没有账号...");
        }
        String[] split = klingApiKey.split(",");
        for (String klingTask : klingTaskList) {
            if (StringUtils.isBlank(klingTask)) {
                continue;
            }
            JSONObject jsonObject = JSONObject.parseObject(klingTask);
            Long imgDrawRecordId = jsonObject.getLong("id");
            Integer klingDrawType = jsonObject.getInteger("type");
            ImgDrawRecordPO recordPO = imgDrawRecordMapper.selectById(imgDrawRecordId);
            Date createTime = recordPO.getCreateTime();
            Date currentTime = new Date();

            long differenceInMillis = currentTime.getTime() - createTime.getTime();
            long differenceInMinutes = TimeUnit.MILLISECONDS.toMinutes(differenceInMillis);
            if (differenceInMinutes > 20) {
                RedisUtil.remove(RedisUtil.REDIS_KLING_SUBMIT_TASK_LIST, 0, klingTask);
            }

            if (klingDrawType == 1) {
                KlingImgVideoReqBO textVideoReq = jsonObject.getObject("klingVideoReqBO", KlingImgVideoReqBO.class);
                if (textVideoReq == null) {
                    return;
                }
                KlingTextVideoResBO klingTextVideoResBO = KlingApis.postKLingImageToVideoGenerations(textVideoReq,
                        split[0], split[1], recordPO.getId());
                if (klingTextVideoResBO == null) {
                    return;
                }
                if (klingTextVideoResBO.getCode() == 0) {
                    ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
                    imgDrawRecordPO.setId(recordPO.getId());
                    imgDrawRecordPO.setVideoJobId(klingTextVideoResBO.getData().getTaskId());
                    imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());
                    imgDrawRecordMapper.updateById(imgDrawRecordPO);
                    RedisUtil.remove(RedisUtil.REDIS_KLING_SUBMIT_TASK_LIST, 0, klingTask);
                }
            } else {
                KlingTextVideoReqBO imgVideoReq = jsonObject.getObject("klingVideoReqBO", KlingTextVideoReqBO.class);
                if (imgVideoReq == null) {
                    return;
                }
                KlingTextVideoResBO klingTextVideoResBO = KlingApis.postKLingTextToVideoGenerations(imgVideoReq,
                        split[0], split[1], recordPO.getId());
                if (klingTextVideoResBO == null) {
                    return;
                }
                if (klingTextVideoResBO.getCode() == 0) {
                    ImgDrawRecordPO imgDrawRecordPO = new ImgDrawRecordPO();
                    imgDrawRecordPO.setId(recordPO.getId());
                    imgDrawRecordPO.setVideoJobId(klingTextVideoResBO.getData().getTaskId());
                    imgDrawRecordPO.setStatus(ImgDrawEnum.STATUS_IN_PROGRESS.getValue());
                    imgDrawRecordMapper.updateById(imgDrawRecordPO);
                    RedisUtil.remove(RedisUtil.REDIS_KLING_SUBMIT_TASK_LIST, 0, klingTask);
                }
            }
            Thread.sleep(100);
        }
    }

    /**
     * 获取音频任务状态
     * <p>
     * 获取音频任务状态，包括查询音频状态为进行中的任务，并进行处理
     * 
     * @throws Exception 处理异常
     */
    @Override
    public void getJobStatus() {
        try {
            // 获取音频查询信息状态
            if (!RedisUtil.acquireLock(GlobalRedisKeyEnum.AUDIO_TASK_STATUS.getStrKey(), 120)) {
                return;
            }
            // 1、查询音频状态为进行中的任务
            List<ImgDrawRecordPO> imgDrawRecordPOList = imgDrawRecordMapper
                    .selectList(new LambdaQueryWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
                            .eq(ImgDrawRecordPO::getFunType, ImgDrawEnum.FUN_TYPE_AUDIO.getValue())
                            .isNotNull(ImgDrawRecordPO::getAudioJobId)
                            .orderByAsc(ImgDrawRecordPO::getCreateTime)
                            .last("LIMIT 0,5"));

            if (imgDrawRecordPOList == null || imgDrawRecordPOList.isEmpty()) {
                return;
            }
            log.info("音频任务状态查询开始: 数量：{}; \naudioOngoingStatusBOS: {}", imgDrawRecordPOList.size(), imgDrawRecordPOList);
            // 查询所有的suno任务
            /*
             * SunoGetTokenCookBO sunoGetTokenCookBO =
             * JSON.parseObject(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(
             * GlobalRedisKeyEnum.AUDIO_ACCOUNT_TOKEN.getStrKey(), "1001")),
             * SunoGetTokenCookBO.class);
             * if (sunoGetTokenCookBO == null || sunoGetTokenCookBO.getJwt() == null) {
             * return;
             * }
             * SunoGetTokenCookBO sunoGetTokenCookBO2 =
             * SunoAiApis.getApiTwtAndToken(version, sunoGetTokenCookBO);
             * if (sunoGetTokenCookBO2 == null || sunoGetTokenCookBO2.getJwt() == null) {
             * return;
             * }
             */

            String token = RedisUtil.getValue(
                    GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.AUDIO_ACCOUNT_TOKEN.getStrKey(), "1002"));
            if (token == null || token.isEmpty()) {
                return;
            }
            String version = RedisUtil.getValue(GlobalRedisKeyEnum.AUDIO_ACCOUNT_CLERK_JS_VERSION.getStrKey());
            String sessionId = RedisUtil.getValue(GlobalRedisKeyEnum.AUDIO_ACCOUNT_SESS_SESSION_ID.getStrKey());
            String jwtToken = SunoAiApiUtil.getSunoTokenBySessionId(sessionId, version, token);
            if (jwtToken == null || jwtToken.isEmpty()) {
                return;
            }

            List<Long> idList = imgDrawRecordPOList.stream().map(m -> m.getId()).toList();
            List<ImgDrawDetlPO> imgDrawDetlPOS = imgDrawDetlMapper.selectList(new LambdaQueryWrapper<ImgDrawDetlPO>()
                    .in(ImgDrawDetlPO::getDrawRecordId, idList));
            if (imgDrawDetlPOS == null || imgDrawDetlPOS.isEmpty()) {
                return;
            }
            List<String> audioJobIds = imgDrawDetlPOS.stream().map(ImgDrawDetlPO::getSunoJobId).toList();

            // 调用api接口
            // List<AudioJobItemBO> audioJobItemBOS =
            // SunoAiApiUtil.getToAudioInfo(audioJobIds, sunoGetTokenCookBO2.getJwt());
            List<AudioJobItemBO> audioJobItemBOS = SunoAiApiUtil.getSunoJobResultByJobIds(audioJobIds, jwtToken);
            if (audioJobItemBOS == null || audioJobItemBOS.isEmpty()) {
                return;
            }
            // 进行账号分组
            Map<Long, List<ImgDrawRecordPO>> audioJobIdMap = imgDrawRecordPOList.stream()
                    .collect(Collectors.groupingBy(ImgDrawRecordPO::getSunoAccountId));
            // 查询分组map
            audioJobIdMap.forEach((k, v) -> {
                if (k != null && k >= 0L && v != null && !v.isEmpty()) {
                    try {
                        audioJobIdMapHandler(k, v, audioJobItemBOS);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            });
        } finally {
            RedisUtil.releaseLock(GlobalRedisKeyEnum.AUDIO_TASK_STATUS.getStrKey());
        }
    }

    /**
     * 音频任务处理
     * <p>
     * 处理音频任务，包括推送消息、更新记录、保存音乐等步骤
     * 
     * @param changeKey           账号ID
     * @param imgDrawRecordPOList 图片绘制记录列表
     * @param audioJobItemBOS     音频任务列表
     * @throws Exception 处理异常
     */
    // 单个账号进行处理
    private void audioJobIdMapHandler(Long changeKey, List<ImgDrawRecordPO> imgDrawRecordPOList,
            List<AudioJobItemBO> audioJobItemBOS) throws Exception {
        log.info("audioJobIdMapHandler changeKey：{}", changeKey);
        if (imgDrawRecordPOList.isEmpty()) {
            return;
        }

        LambdaQueryWrapper<UserPrivateConfigPO> lamb = new LambdaQueryWrapper<UserPrivateConfigPO>();
        lamb.eq(UserPrivateConfigPO::getUserId, imgDrawRecordPOList.get(0).getUserId());
        lamb.eq(UserPrivateConfigPO::getFunType, 4);
        UserPrivateConfigPO userPrivateConfigPO = userPrivateConfigMapper.selectOne(lamb);
        for (ImgDrawRecordPO imgDrawRecordPO : imgDrawRecordPOList) {
            try {
                // 推送对象构建
                ImgDrawHistoryVO imgDrawHistoryVO = ImgDrawHistoryVO.intiImgDrawHistoryVO(imgDrawRecordPO, null);
                imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());

                List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();

                List<ImgDrawDetlPO> imgDrawDetlPOS = imgDrawDetlMapper
                        .selectList(new LambdaQueryWrapper<ImgDrawDetlPO>()
                                .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawRecordPO.getId()));
                if (imgDrawDetlPOS == null || imgDrawDetlPOS.isEmpty()) {
                    return;
                }
                List<String> audioJobIds = imgDrawDetlPOS.stream().map(ImgDrawDetlPO::getSunoJobId).toList();
                // 是否两个任务都完成标志位
                int taskFlag = 0;
                // 是否已经生成流信息标志位
                int flowFlag = 0;
                AudioJobItemBO audioJobItemBO1 = null;
                for (String audioJobId : audioJobIds) {
                    AudioJobItemBO audioJobItemBO = audioJobItemBOS.stream().filter(bo -> audioJobId.equals(bo.getId()))
                            .findFirst().orElse(null);
                    audioJobItemBO1 = audioJobItemBO;
                    if (audioJobItemBO == null) {
                        continue;
                    }
                    ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlPOS.stream()
                            .filter(m -> audioJobItemBO.getId().equals(m.getSunoJobId())).findFirst().orElse(null);

                    if (audioJobItemBO.getStatus().equals("complete")) {
                        String audioUrl = null;
                        String imgUrl = null;
                        if (StringUtils.isNotBlank(audioJobItemBO.getAudio_url())) {
                            String ossUrl = RunwayApis.uploadLumaVideo(audioJobItemBO.getAudio_url(),
                                    String.valueOf(audioJobItemBO.getId()), 48, OssClientConfig.FILE_SUFFIX_AUDIO);
                            audioUrl = BUrlUtil.getBaseCdnUrl(ossUrl);
                        }
                        if (StringUtils.isNotBlank(audioJobItemBO.getImage_url())) {
                            String ossUrl = RunwayApis.uploadLumaVideo(audioJobItemBO.getImage_url(),
                                    String.valueOf(audioJobItemBO.getId()), 49, OssClientConfig.FILE_SUFFIX);
                            imgUrl = BUrlUtil.getBaseCdnUrl(ossUrl);
                        }

                        LambdaUpdateWrapper<ImgDrawDetlPO> lambda = new LambdaUpdateWrapper<ImgDrawDetlPO>();
                        lambda.eq(ImgDrawDetlPO::getId, imgDrawDetlPO.getId());
                        lambda.set(ImgDrawDetlPO::getVideoUrl, audioJobItemBO.getVideo_url());
                        lambda.set(ImgDrawDetlPO::getImgUrl, imgUrl);
                        lambda.set(ImgDrawDetlPO::getImgSourceUrl,
                                audioJobItemBO.getImage_url().replace("image", "image_large"));
                        lambda.set(ImgDrawDetlPO::getWhDivide, 1);
                        lambda.set(ImgDrawDetlPO::getImgWidth, 1);
                        lambda.set(ImgDrawDetlPO::getImgHeight, 1);
                        lambda.set(ImgDrawDetlPO::getRemark, audioJobItemBO.getAudio_url());
                        lambda.set(ImgDrawDetlPO::getImgType, "image/png");
                        lambda.set(ImgDrawDetlPO::getAudioUrl, audioUrl);
                        if (userPrivateConfigPO != null && 1 == userPrivateConfigPO.getIsPrivate().intValue()) {
                            lambda.set(ImgDrawDetlPO::getIsPublish, CommonIntEnum.SHOW_FALSE.getIntValue());
                        }
                        int update = imgDrawDetlMapper.update(null, lambda);

                        int recordIndex = imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                                .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                                .set(ImgDrawRecordPO::getAudioLyric, audioJobItemBO.getPrompt())
                                .set(ImgDrawRecordPO::getAudioTitle, audioJobItemBO.getTitle())
                                .set(ImgDrawRecordPO::getPromptUse, audioJobItemBO.getTags())
                                .set(ImgDrawRecordPO::getWhDivide, 1)
                                .set(ImgDrawRecordPO::getWidth, 1)
                                .set(ImgDrawRecordPO::getHeight, 1));
                        if (recordIndex >= 1 && update >= 1) {
                            taskFlag++;
                            // 4推送给user
                            imgDrawHistoryVO.setAudioLyric(audioJobItemBO.getPrompt());
                            imgDrawDetlPO.setImgUrl(audioJobItemBO.getImage_url());
                            imgDrawDetlPO
                                    .setImgSourceUrl(audioJobItemBO.getImage_url().replace("image", "image_large"));
                            imgDrawDetlPO.setAudioUrl(audioJobItemBO.getAudio_url());
                            List<ImgDrawDetlPO> imgDrawDetlPOS1 = new ArrayList<>();
                            ImgDrawDetlPO imgDrawDetlPO2 = imgDrawDetlMapper.selectById(imgDrawDetlPO.getId());
                            imgDrawDetlPOS1.add(imgDrawDetlPO2);
                            imgDrawDetlVOS.addAll(getImgDrawDetlVOSBySuno(imgDrawDetlPOS1, imgDrawHistoryVO));
                        }
                    } else if (StringUtils.isNotBlank(audioJobItemBO.getAudio_url())
                            && !audioJobItemBO.getStatus().equals("complete")) {
                        log.info("其中一个音频任务生成audioId，推送详情到页面... imgDrawDetlPOid={}", imgDrawDetlPO.getId());
                        // 4推送给user
                        imgDrawHistoryVO.setAudioLyric(audioJobItemBO.getPrompt());
                        imgDrawDetlPO.setImgUrl(audioJobItemBO.getImage_url());
                        imgDrawDetlPO.setImgSourceUrl(audioJobItemBO.getImage_url().replace("image", "image_large"));
                        imgDrawDetlPO.setAudioUrl(audioJobItemBO.getAudio_url());
                        List<ImgDrawDetlPO> imgDrawDetlPOS1 = new ArrayList<>();
                        imgDrawDetlPOS1.add(imgDrawDetlPO);
                        List<ImgDrawDetlVO> imgDrawDetlVOSBySuno = getImgDrawDetlVOSBySuno(imgDrawDetlPOS1,
                                imgDrawHistoryVO);
                        imgDrawDetlVOS.addAll(imgDrawDetlVOSBySuno);
                        flowFlag++;
                    }
                }
                if (taskFlag == 2) {
                    // 更新imgrecord表数据
                    log.info("音频任务全部完成，推送详情到页面...ImgDrawRecordPOId={}", imgDrawRecordPO.getId());
                    imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                            .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue())
                            .set(ImgDrawRecordPO::getFinishTime, System.currentTimeMillis()));
                    imgDrawHistoryVO.setImgDrawDetls(imgDrawDetlVOS);
                    taskMessagePush(imgDrawHistoryVO, "任务已完成");
                    notificationMessage(imgDrawHistoryVO, null);

                    // 将音乐保存到首页
                    // ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlPOList.getFirst();
                    AudioHomeDataPO audioHomeDataPO = new AudioHomeDataPO();
                    audioHomeDataPO.setId(IdWorker.getId());
                    audioHomeDataPO.setLanguageTagId(1);
                    Long styleId = BStringUtil.audioMatcherStyle(imgDrawRecordPO.getAudioStyle());
                    styleId = styleId == null ? 12 : styleId;
                    audioHomeDataPO.setStyleId(styleId);
                    audioHomeDataPO.setTitle(audioJobItemBO1.getTitle());
                    AudioModelConfigPO audioModelConfigPO = audioModelConfigMapper
                            .selectOne(new LambdaQueryWrapper<AudioModelConfigPO>()
                                    .eq(AudioModelConfigPO::getId, styleId));
                    if (audioModelConfigPO != null) {
                        audioHomeDataPO.setTags(
                                audioModelConfigPO.getNameShow().concat(",").concat(audioModelConfigPO.getUseStyle()));
                    }
                    audioHomeDataPO.setDuration(210.000000);
                    audioHomeDataPO.setPrompt(imgDrawRecordPO.getAudioLyric());
                    audioHomeDataPO.setImageLargeUrl(audioJobItemBO1.getImage_url().replace("image", "image_large"));
                    audioHomeDataPO.setImageUrl(audioJobItemBO1.getImage_url());
                    audioHomeDataPO.setAudioUrl(audioJobItemBO1.getAudio_url());
                    audioHomeDataPO.setVideoUrl(audioJobItemBO1.getVideo_url());
                    audioHomeDataPO.setState(1);
                    audioHomeDataMapper.insert(audioHomeDataPO);

                } else if (flowFlag >= 1) {
                    // 有音乐流生成时的推送。
                    imgDrawHistoryVO.setImgDrawDetls(imgDrawDetlVOS);
                    taskMessagePush(imgDrawHistoryVO, "");
                }

                // 判断时间差是否超过了30分钟,关闭任务
                if (BDateUtil
                        .differenceMinutes(imgDrawRecordPO.getCreateTime()) > CommonConst.TASK_TIMEOUT_MINUTES_AUDIO) {
                    log.info("suno判断是否超过了20分钟,关闭任务 imgDrawRecordPO Id={}", imgDrawRecordPO.getId());
                    // int detailIndex = imgDrawDetlMapper.deleteById(imgDrawDetlPO.getId());
                    int recordIndex = imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getId, imgDrawRecordPO.getId())
                            .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
                            .set(ImgDrawRecordPO::getFinishTime, System.currentTimeMillis()));
                    if (recordIndex > 0) {
                        audioGiveRecordMapper.delete(new LambdaUpdateWrapper<AudioGiveRecordPO>()
                                .eq(AudioGiveRecordPO::getDrawRecordId, imgDrawRecordPO.getId()));
                        imgDrawRecordPO.setFailReason("任务超时，关闭任务！");
                        // 退回点子
                        this.videoJobFailedRollbackDz(imgDrawRecordPO);
                    }
                }
            } catch (Exception e) {
                log.info("audioJobIdMapHandler error={}", e.getMessage());
                this.videoJobFailedRollbackDz(imgDrawRecordPO);
            }
        }
    }

    /**
     * 任务消息推送
     * <p>
     * 推送任务完成的消息
     * 
     * @param imgDrawHistoryVO 图片绘制历史记录视图对象
     * @param message          消息内容
     */
    private void taskMessagePush(ImgDrawHistoryVO imgDrawHistoryVO, String message) {
        if (imgDrawHistoryVO == null) {
            log.warn("imgDrawHistoryVO is null. Unable to push message.");
            return;
        }
        boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(imgDrawHistoryVO.getUserId(),
                BMessageSendEnum.AUDIO_JOB_PUSH, JSONObject.toJSONString(imgDrawHistoryVO), message));
        log.info("Message push status: {}", state);
    }

    /**
     * 音乐任务推送消息
     * <p>
     * 推送音乐任务完成的消息
     * 
     * @param imgDrawHistoryVO 图片绘制历史记录视图对象
     * @param jobImageUrl      任务图片URL
     */
    public void notificationMessage(ImgDrawHistoryVO imgDrawHistoryVO, String jobImageUrl) {
        try {
            String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgDrawHistoryVO.getOptAttribute());
            SysNotificationPO notificationPO = null;
            if (optTitleOne != null) {
                notificationPO = SysNotificationPO.buildSysNotification(imgDrawHistoryVO.getUserId(),
                        BNotificationEnum.AUDIO_NOTIF.getIntValue(),
                        optTitleOne.concat("已完成"), optTitleOne, 1, imgDrawHistoryVO.getId(), jobImageUrl);
            }
            boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(imgDrawHistoryVO.getUserId(),
                    BMessageSendEnum.NOTIFICATION_PUSH, JSONObject.toJSONString(notificationPO)));
            log.info("音乐任务推送消息:{}", state);
        } catch (Exception e) {
            log.error("音乐失败推送消息= {}", e.getMessage(), e);
        }
    }

    /**
     * 令牌桶算法限制请求频率
     */
    private final RateLimiter rateLimiter = RateLimiter.create(1.0); // 1 req per second

    /**
     * 发送请求
     * <p>
     * 使用令牌桶算法限制请求频率
     * 
     * @throws Exception 处理异常
     */
    public void sendRequest() {
        rateLimiter.acquire(); // 请求前先获取许可
        try {
            // 调用第三方 API 的逻辑
            System.out.println("Sending API request...");
            // Example: YourApiService.sendRequest();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据模型获取视频镜头描述
     * <p>
     * 根据视频镜头对象和是否为中国地区，获取视频镜头的描述
     * 
     * @param videoLensObj 视频镜头对象
     * @param isChina      是否为中国地区
     * @return 视频镜头描述
     */
    private String getVideoLensByModel(ImgVideoDTO.VideoLensObj videoLensObj, boolean isChina) {
        String result = "";
        if (null == videoLensObj) {
            return null;
        }
        if (isChina) {
            if (null != videoLensObj.getUp() && videoLensObj.getUp() >= 0) {
                if (videoLensObj.getUp() >= 0 && videoLensObj.getUp() < 50) {
                    result += " 镜头往上轻微移动";
                } else {
                    result += " 镜头往上移动";
                }
            }
            if (null != videoLensObj.getDown() && videoLensObj.getDown() >= 0) {
                if (videoLensObj.getDown() >= 0 && videoLensObj.getDown() < 50) {
                    result += " 镜头往下轻微移动";
                } else {
                    result += " 镜头往下移动";
                }
            }
            if (null != videoLensObj.getLeft() && videoLensObj.getLeft() >= 0) {
                if (videoLensObj.getLeft() >= 0 && videoLensObj.getLeft() < 50) {
                    result += " 镜头往左轻微移动";
                } else {
                    result += " 镜头往左移动";
                }
            }
            if (null != videoLensObj.getRight() && videoLensObj.getRight() >= 0) {
                if (videoLensObj.getRight() >= 0 && videoLensObj.getRight() < 50) {
                    result += " 镜头往右轻微移动";
                } else {
                    result += " 镜头往右移动";
                }
            }
            if (null != videoLensObj.getUpAround() && videoLensObj.getUpAround() >= 0) {
                if (videoLensObj.getUpAround() >= 0 && videoLensObj.getUpAround() < 50) {
                    result += " 相机往上轻微摇镜";
                } else {
                    result += " 相机往上摇镜";
                }
            }
            if (null != videoLensObj.getDownAround() && videoLensObj.getDownAround() >= 0) {
                if (videoLensObj.getDownAround() >= 0 && videoLensObj.getDownAround() < 50) {
                    result += " 相机往下轻微摇镜";
                } else {
                    result += " 相机往下摇镜";
                }
            }
            if (null != videoLensObj.getLeftAround() && videoLensObj.getLeftAround() >= 0) {
                if (videoLensObj.getLeftAround() >= 0 && videoLensObj.getLeftAround() < 50) {
                    result += " 相机往左轻微摇镜";
                } else {
                    result += " 相机往左摇镜";
                }
            }
            if (null != videoLensObj.getRightAround() && videoLensObj.getRightAround() >= 0) {
                if (videoLensObj.getRightAround() >= 0 && videoLensObj.getRightAround() < 50) {
                    result += " 相机往右轻微摇镜";
                } else {
                    result += " 相机往右摇镜";
                }
            }
            if (null != videoLensObj.getLeftRotate() && videoLensObj.getLeftRotate() >= 0) {
                if (videoLensObj.getLeftRotate() >= 0 && videoLensObj.getLeftRotate() < 50) {
                    result += " 相机往左轻微旋转";
                } else {
                    result += " 相机往左旋转";
                }
            }
            if (null != videoLensObj.getRightRotate() && videoLensObj.getRightRotate() >= 0) {
                if (videoLensObj.getRightRotate() >= 0 && videoLensObj.getRightRotate() < 50) {
                    result += " 相机往右轻微旋转";
                } else {
                    result += " 相机往右旋转";
                }
            }
            if (null != videoLensObj.getPull() && videoLensObj.getPull() >= 0) {
                if (videoLensObj.getPull() >= 0 && videoLensObj.getPull() < 50) {
                    result += " 相机往后轻微拉镜";
                } else {
                    result += " 相机往后拉镜";
                }
            }
            if (null != videoLensObj.getPush() && videoLensObj.getPush() >= 0) {
                if (videoLensObj.getPush() >= 0 && videoLensObj.getPush() < 50) {
                    result += " 相机往前轻微推镜";
                } else {
                    result += " 相机往前推镜";
                }
            }
        } else {
            if (null != videoLensObj.getUp() && videoLensObj.getUp() >= 0) {
                if (videoLensObj.getUp() >= 0 && videoLensObj.getUp() < 50) {
                    result += " The camera moves slightly upward";
                } else {
                    result += " camera move up";
                }
            }
            if (null != videoLensObj.getDown() && videoLensObj.getDown() >= 0) {
                if (videoLensObj.getDown() >= 0 && videoLensObj.getDown() < 50) {
                    result += " The camera moves slightly downwards";
                } else {
                    result += " camera move down";
                }
            }
            if (null != videoLensObj.getLeft() && videoLensObj.getLeft() >= 0) {
                if (videoLensObj.getLeft() >= 0 && videoLensObj.getLeft() < 50) {
                    result += " The camera moves slightly to the left";
                } else {
                    result += " camera move left";
                }
            }
            if (null != videoLensObj.getRight() && videoLensObj.getRight() >= 0) {
                if (videoLensObj.getRight() >= 0 && videoLensObj.getRight() < 50) {
                    result += " The camera moves slightly to the right";
                } else {
                    result += " camera move right";
                }
            }
            if (null != videoLensObj.getUpAround() && videoLensObj.getUpAround() >= 0) {
                if (videoLensObj.getUpAround() >= 0 && videoLensObj.getUpAround() < 50) {
                    result += " camera pan up";
                } else {
                    result += " camera pan up";
                }
            }
            if (null != videoLensObj.getDownAround() && videoLensObj.getDownAround() >= 0) {
                if (videoLensObj.getDownAround() >= 0 && videoLensObj.getDownAround() < 50) {
                    result += " Camera pan down";
                } else {
                    result += " Camera pan down";
                }
            }
            if (null != videoLensObj.getLeftAround() && videoLensObj.getLeftAround() >= 0) {
                if (videoLensObj.getLeftAround() >= 0 && videoLensObj.getLeftAround() < 50) {
                    result += " camera pan left";
                } else {
                    result += " camera pan left";
                }
            }
            if (null != videoLensObj.getRightAround() && videoLensObj.getRightAround() >= 0) {
                if (videoLensObj.getRightAround() >= 0 && videoLensObj.getRightAround() < 50) {
                    result += " camera pan right";
                } else {
                    result += " camera pan right";
                }
            }
            if (null != videoLensObj.getLeftRotate() && videoLensObj.getLeftRotate() >= 0) {
                if (videoLensObj.getLeftRotate() >= 0 && videoLensObj.getLeftRotate() < 50) {
                    result += " camera rotate left";
                } else {
                    result += " camera rotate left";
                }
            }
            if (null != videoLensObj.getRightRotate() && videoLensObj.getRightRotate() >= 0) {
                if (videoLensObj.getRightRotate() >= 0 && videoLensObj.getRightRotate() < 50) {
                    result += " camera rotate right";
                } else {
                    result += " camera rotate right";
                }
            }
            if (null != videoLensObj.getPull() && videoLensObj.getPull() >= 0) {
                if (videoLensObj.getPull() >= 0 && videoLensObj.getPull() < 50) {
                    result += " Camera pulls back";
                } else {
                    result += " Camera pulls back";
                }
            }
            if (null != videoLensObj.getPush() && videoLensObj.getPush() >= 0) {
                if (videoLensObj.getPush() >= 0 && videoLensObj.getPush() < 50) {
                    result += " camera moves forward";
                } else {
                    result += " camera moves forward";
                }
            }
        }
        return result;
    }

    /**
     * 视频生成新方法（在用）
     * 2025-04-28 新增万相视频-首尾帧
     * <p>
     * 处理视频生成请求，包括参数校验、模型选择、用户权限检查、点数扣除、任务记录初始化等步骤
     * 
     * @param imgVideoDTO 视频生成请求参数
     * @return 包含任务信息的结果对象
     * @throws Throwable 处理异常
     */
    @Override
    public Result<Object> videoGenerationNew(ImgVideoDTO imgVideoDTO) throws Throwable {
        if (imgVideoDTO == null) {
            return Result.ERROR("参数不能为空");
        }
        if (imgVideoDTO.getUserId() == null) {
            imgVideoDTO.setUserId(JwtNewUtil.getUserId());
        }
        // 如果提示词不为null或空则校验合法性
        if (imgVideoDTO.getPrompt() != null) {
            // 校验提示词是否在限制范围内
            if (!BStringUtil.isStringWithinLimitVideo(imgVideoDTO.getPrompt())) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            }
            // 校验提示词是否违规
            if (BTengXunUtil.textToExamineFailByVideo(imgVideoDTO.getPrompt(), imgVideoDTO.getUserId())) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            }
            // 如果提示词为中文则不翻译
            if (BStringUtil.isChinese(imgVideoDTO.getPrompt())) {
                // 如果模型为智谱、miniMax、可灵、万相则不翻译
                if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_ZHIPU_BASICS.getValue()
                        || imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_BASICS.getValue()
                        || imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_KLING_BASICS.getValue()
                        || imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS
                                .getValue()) {
                    imgVideoDTO.setPromptUse(imgVideoDTO.getPrompt());
                } else {
                    imgVideoDTO.setPromptUse(BAliYunUtil.textToEnglish(imgVideoDTO.getPrompt()));
                }
            }
            // 如果提示词为空则使用原提示词
            if (StringUtils.isBlank(imgVideoDTO.getPromptUse())) {
                imgVideoDTO.setPromptUse(imgVideoDTO.getPrompt());
            }
        }
        // 校验负向提示词
        if (imgVideoDTO.getNegativeprompt() != null) {
            // 校验负向提示词是否在限制范围内
            if (!BStringUtil.isStringWithinLimitVideo(imgVideoDTO.getNegativeprompt())) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            }
            // 校验负向提示词是否违规
            if (BTengXunUtil.textToExamineFailByVideo(imgVideoDTO.getNegativeprompt(), imgVideoDTO.getUserId())) {
                return Result.ERROR(CommonResultEnum.DRAW_MJ_API_PROMPT_ERROR.getValue());
            }
            // 如果负向提示词为中文则不翻译
            if (BStringUtil.isChinese(imgVideoDTO.getNegativeprompt())) {
                imgVideoDTO
                        .setNegativepromptUse(BTengXunUtil.textToEnglish(imgVideoDTO.getNegativeprompt(), "zh", "en"));
            }
        }
        // 效验模型跳转不同的执行接口
        VideoModelConfigPO videoModelConfigPO = videoModelConfigMapper
                .selectOne(new LambdaQueryWrapper<VideoModelConfigPO>()
                        .eq(VideoModelConfigPO::getAttribute, imgVideoDTO.getModelId())
                        .eq(VideoModelConfigPO::getIsShow, CommonIntEnum.SHOW_TRUE.getIntValue()));
        if (videoModelConfigPO == null) {
            return Result.ERROR("未找到匹配的模型");
        }

        // 校验用户是否为VIP用户
        Integer memberLevel = userDDRecordService.getUserVipInfo();
        boolean isVip = (memberLevel != null && memberLevel > 0);
        if (!isVip && videoModelConfigPO.getIsVip() == 1) {
            return Result.ERROR_HY(CommonResultEnum.HY_BALANCE_ERROR.getValue());
        }
        // 24小时视频失效时间的计算
        ImgDrawRecordPO imgDrawRecordPO = initImgDrawRecord(imgVideoDTO);
        List<String> initImgUrlsList = new ArrayList<>();
        initImgUrlsList.add(imgVideoDTO.getFirstImg());
        initImgUrlsList.add(imgVideoDTO.getEndImg());
        imgVideoDTO.setInitImgUrls(initImgUrlsList);
        imgDrawRecordPO.setInitImgUrls(
                imgVideoDTO.getInitImgUrls() != null ? JSONArray.toJSONString(imgVideoDTO.getInitImgUrls()) : null);

        // 扣除用户点子 ===== 查询点子规则数据
        double dzQuantity = BDDUseNumEnum.getBDDUseNumEnumByModel(imgVideoDTO.getModelId(), isVip);
        int optAttribute = ImgOptModelEnum.getOptAttributeByModelId(imgVideoDTO.getModelId());// 获取操作
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(optAttribute);
        FlowRecordPO flowRecordSub = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ONE.getDtoKey())
                .remark(optTitleOne).build();

        // 根据模型获取视频速度-智谱
        if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_ZHIPU_BASICS.getValue()) {
            VideoSpeedVO videoSpeed = getVideoSpeed(imgVideoDTO.getModelKey(), videoModelConfigPO.getSpeed());
            if (videoSpeed == null) {
                log.info("获取视频速度失败, 休闲模式失败");
                return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            }
            if (videoSpeed.getValue().equals("RELAX")) {
                dzQuantity = 0;
            }
        }
        // 根据模型获取视频速度-达芬奇
        else if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI3_BASICS.getValue()
                || imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI4_BASICS.getValue()) {
            double duration = imgVideoDTO.getVideoDuration() == null ? Double.parseDouble(imgVideoDTO.getDuration())
                    : imgVideoDTO.getVideoDuration();
            int seconds = (int) Math.round(duration);
            dzQuantity = dzQuantity * Math.min(seconds, 20);
        }
        // 根据模型获取视频速度-梦工厂
        else if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS.getValue()) {
            if (imgVideoDTO.getVideoVersionKey() != null && imgVideoDTO.getVideoVersionKey() != 0) {
                List<VideoVersionVO> versionVOList = VideoVersionVO
                        .videoVersionVOList(videoModelConfigPO.getVideoVersion());
                Optional<VideoVersionVO> matchingVideoVersion = versionVOList.stream()
                        .filter(version -> version.getKey() == imgVideoDTO.getVideoVersionKey().intValue()).findFirst();
                if (matchingVideoVersion.isPresent()) {
                    VideoVersionVO videoVersion = matchingVideoVersion.get();
                    imgVideoDTO.setVideoModelValue(videoVersion.getValue());
                    dzQuantity = isVip ? Double.parseDouble(videoVersion.getDdVipUseNumStr())
                            : Double.parseDouble(videoVersion.getDdUseNumStr());
                }
            }
        }
        // 根据模型获取视频速度-好莱坞
        else if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_BASICS.getValue()) {
            if (imgVideoDTO.getVideoVersionKey() != null && imgVideoDTO.getVideoVersionKey() != 0) {
                List<VideoVersionVO> versionVOList = VideoVersionVO
                        .videoVersionVOList(videoModelConfigPO.getVideoVersion());
                Optional<VideoVersionVO> matchingVideoVersion = versionVOList.stream()
                        .filter(version -> version.getKey() == imgVideoDTO.getVideoVersionKey().intValue()).findFirst();
                if (matchingVideoVersion.isPresent()) {
                    VideoVersionVO videoVersion = matchingVideoVersion.get();
                    imgVideoDTO.setVideoModelValue(videoVersion.getValue());
                    dzQuantity = isVip ? Double.parseDouble(videoVersion.getDdVipUseNumStr())
                            : Double.parseDouble(videoVersion.getDdUseNumStr());
                }
            }
        }

        log.info("视频扣除点子数：{}", dzQuantity);
        checkBalanService.checkUser(imgDrawRecordPO.getUserId(), dzQuantity, flowRecordSub);
        imgDrawRecordPO.setUseDdQua(dzQuantity);
        imgDrawRecordPO.setOptAttribute(optAttribute);

        try {
            // 达芬奇 1.5 == Gen-3模型
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI2_BASICS.getValue()) {
                imgDrawRecordPO.setDescription("runway " + imgVideoDTO.getPrompt());

                if ((imgVideoDTO.getInitImgUrls() == null || imgVideoDTO.getInitImgUrls().isEmpty())
                        && StringUtils.isBlank(imgVideoDTO.getPrompt())) {
                    return Result.ERROR("达芬奇必须传指令或图片！");
                }
                return runwayVideoGenerationNew(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO);
            }
            // 达芬奇 1.5 == Gen-3模型-视频转绘
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI3_BASICS.getValue()) {
                imgDrawRecordPO.setDescription("runway " + imgVideoDTO.getPrompt());
                if (imgVideoDTO.getVideoUrl() == null) {
                    return Result.ERROR("请上传视频转绘视频");
                }
                return runwayVideoGenerationTransfer(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO);
            }
            // 达芬奇 1.5 == Gen-3模型-角色驱动
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI4_BASICS.getValue()) {
                imgDrawRecordPO.setDescription("runway " + imgVideoDTO.getPrompt());
                if (imgVideoDTO.getVideoUrl() == null) {
                    return Result.ERROR("请上传角色驱动视频");
                }
                return runwayVideoGenerationRoleDriven(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO);
            }
            // 智谱 - 皮克斯 1.0
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_ZHIPU_BASICS.getValue()) {
                imgDrawRecordPO.setDescription("智谱 " + imgVideoDTO.getPrompt());
                if ((imgVideoDTO.getInitImgUrls() == null || imgVideoDTO.getInitImgUrls().isEmpty())
                        && StringUtils.isBlank(imgVideoDTO.getPrompt())) {
                    return Result.ERROR("皮克斯视频必须传指令或图片！");
                }
                return zhiPuVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO);
            }
            // 好莱坞 - minimax
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_BASICS.getValue()) {
                imgDrawRecordPO.setDescription("海螺 " + imgVideoDTO.getPrompt());
                if ((imgVideoDTO.getInitImgUrls() == null || imgVideoDTO.getInitImgUrls().isEmpty())
                        && StringUtils.isBlank(imgVideoDTO.getPrompt())) {
                    return Result.ERROR("好莱坞指令或图片不能为空");
                }
                HaiLuoRequestBO haiLuoRequestBO = new HaiLuoRequestBO();
                haiLuoRequestBO.setModel("video-01");
                return hailuoVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO, haiLuoRequestBO);
            }
            // 天像境V2 - minimax
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_V2_BASICS.getValue()) {
                imgDrawRecordPO.setDescription("海螺v2 " + imgVideoDTO.getPrompt());
                if ((imgVideoDTO.getInitImgUrls() == null || imgVideoDTO.getInitImgUrls().isEmpty())
                        && StringUtils.isBlank(imgVideoDTO.getPrompt())) {
                    return Result.ERROR("天像境图片不能为空");
                }
                HaiLuoRequestBO haiLuoRequestBO = new HaiLuoRequestBO();
                haiLuoRequestBO.setModel("video-01-live2d");
                return hailuoVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO, haiLuoRequestBO);
            }
            // 万相境 - 通义万相
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS.getValue()) {
                if (StringUtils.isNotBlank(imgVideoDTO.getPromptUse()) && imgVideoDTO.getPromptUse().length() < 2) {
                    return Result.ERROR(CommonResultEnum.VIDEO_PROMPT_ERROR.getValue());
                }
                imgDrawRecordPO.setDescription("通义万相：" + imgVideoDTO.getPrompt());
                return wanxVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO);
            }
            // 可灵 == 蒙太奇 1.5
            if (imgVideoDTO.getModelId() == ImgOptModelEnum.VIDEO_ATTRIBUTE_KLING_BASICS.getValue()) {
                imgDrawRecordPO.setDescription("可灵 " + imgVideoDTO.getPrompt());
                if ((imgVideoDTO.getInitImgUrls() == null || imgVideoDTO.getInitImgUrls().isEmpty())
                        && StringUtils.isBlank(imgVideoDTO.getPrompt())) {
                    return Result.ERROR("蒙太奇视频必须传指令或图片！");
                }
                return klingVideoGeneration(imgVideoDTO, imgDrawRecordPO, videoModelConfigPO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 将用户使用点子数返回
            FlowRecordPO flowRecordPO = FlowRecordPO.builder().recordType(DDUseRuleEnum.COMM_ZERO.getDtoKey())
                    .remark(optTitleOne).build();
            asyncService.updateRemainingTimes(imgDrawRecordPO.getUserId(), imgDrawRecordPO.getUseDdQua(), flowRecordPO);
            log.error("==*==生成视频失败：", e.getMessage(), e);
            // return Result.ERROR("视频生成失败，请重试");
            // return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
            return Result.ERROR(e.getMessage());
        }
        log.error("==*==生成视频失败：逻辑不存在");
        return Result.ERROR(CommonResultEnum.getSystemErrorMsg());
    }
}
