package com.nacos.service;

import cn.hutool.core.util.ObjectUtil;
import com.business.db.model.po.FlowRecordPO;
import com.nacos.exception.E;
import com.nacos.result.Result;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("CheckService")
@Transactional(rollbackFor = E.class)
public class CheckBalanService {

    @Resource
    private CheckService checkService;

    @Resource
    private IFlowRecordService flowRecordService;

    /**
     * 新的扣点子规则
     *
     * @param userId
     * @param dzNumber
     * @param flowRecordPO
     * @return
     */
    public Long checkUser(Long userId, Double dzNumber, FlowRecordPO flowRecordPO) {
        if (ObjectUtil.isNull(dzNumber)) {
            throw new E("扣点子失败！");
        }
        if (0 == Double.doubleToLongBits(dzNumber)) {
            flowRecordPO.setUserId(userId);
            flowRecordPO.setNum(dzNumber);
            flowRecordService.save(flowRecordPO);
            return null;
        }
        Result<Boolean> result = checkService.getResidueDDDeduct(userId, dzNumber);
        if (result.getData().booleanValue()) {
            //保存点子消耗记录
            flowRecordPO.setUserId(userId);
            flowRecordPO.setNum(dzNumber);
            flowRecordService.save(flowRecordPO);
            return null;
        }
        throw new E("扣点子失败！");
    }

    /**
     * 新的扣点子规则
     */
    public void checkUserNew(Long userId, Double dzNumber, FlowRecordPO flowRecordPO) {
        // 保存点子消耗记录
        flowRecordPO.setUserId(userId);
        flowRecordPO.setNum(dzNumber);
        flowRecordService.save(flowRecordPO);
    }

}
