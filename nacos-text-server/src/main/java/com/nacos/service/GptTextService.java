package com.nacos.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.text.SceneRecordQueryDTO;
import com.business.db.model.vo.text.SceneRecordQueryVO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.exception.IBusinessException;
import com.nacos.model.dto.GptSceneGenerationDTO;
import com.nacos.result.Result;

public interface GptTextService {

    Result<Long> sceneGeneration(GptSceneGenerationDTO gptSceneGenerationDTO) throws IBusinessException;

    Result<Page<SceneRecordQueryVO>> querySceneRecordPage(SceneRecordQueryDTO dto);

    Result<Integer> delSceneRecord(BaseDeleteEntity delete);

    Result<Long> selectSceneTaskCount();

    Result<Page<SceneRecordQueryVO>> querySceneRecordsBySceneId(SceneRecordQueryDTO dto);
}
