package com.nacos.utils;


import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContextBuilder;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.conn.ssl.X509HostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLException;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocket;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLEncoder;
import java.security.GeneralSecurityException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;

@Slf4j
public class HttpUtil {

    public static final String DEF_CHATSET = "UTF-8";
    public static final int DEF_CONN_TIMEOUT = 30000;
    public static final int DEF_READ_TIMEOUT = 30000;
    public static final String USERAGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36";

//    public static JSONObject readData(HttpServletRequest request) {
//        JSONObject result = null;
//        StringBuilder sb = new StringBuilder();
//        try (BufferedReader reader = request.getReader();) {
//            char[] buff = new char[1024];
//            int len;
//            while ((len = reader.read(buff)) != -1) {
//                sb.append(buff, 0, len);
//            }
//            result = JSONUtil.parseObj(sb.toString());
//        } catch (IOException e) {
//        }
//        return result;
//    }

    /**
     * @param strUrl 请求地址
     * @param params 请求参数
     * @param method 请求方法
     * @return 网络请求字符串
     * @throws Exception
     */
    public static String net(String strUrl, Map<String,String> headerMap, Map params, String method) throws Exception {
        HttpURLConnection conn = null;
        BufferedReader reader = null;
        String rs = null;
        try {
            StringBuffer sb = new StringBuffer();
            if ((method == null || method.equals("GET")) && MapUtil.isNotEmpty(params)) {
                strUrl = strUrl + "?" + urlencode(params);
            }
            URL url = new URL(strUrl);
            conn = (HttpURLConnection) url.openConnection();
            if (method == null || method.equals("GET")) {
                conn.setRequestMethod("GET");
            } else {
                conn.setRequestMethod("POST");
                conn.setDoOutput(true);
            }
            conn.setRequestProperty("User-agent", USERAGENT);
            conn.setUseCaches(false);
            conn.setConnectTimeout(DEF_CONN_TIMEOUT);
            conn.setReadTimeout(DEF_READ_TIMEOUT);
            conn.setInstanceFollowRedirects(false);
            if(MapUtil.isNotEmpty(headerMap)){
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    conn.setRequestProperty(entry.getKey(),entry.getValue());
                }
            }
            conn.connect();
            if (MapUtil.isNotEmpty(params) && method.equals("POST")) {
                try {
                    DataOutputStream out = new DataOutputStream(conn.getOutputStream());
                    out.writeBytes(urlencode(params));
                } catch (Exception e) {
                    // TODO: handle exception
                }
            }
            InputStream is = conn.getInputStream();
            reader = new BufferedReader(new InputStreamReader(is, DEF_CHATSET));
            String strRead = null;
            while ((strRead = reader.readLine()) != null) {
                sb.append(strRead);
            }
            rs = sb.toString();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                reader.close();
            }
            if (conn != null) {
                conn.disconnect();
            }
        }
        return rs;
    }

    // 将map型转为请求参数型
    public static String urlencode(Map<String, Object> data) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry i : data.entrySet()) {
            try {
                sb.append(i.getKey()).append("=").append(URLEncoder.encode(i.getValue() + "", "UTF-8")).append("&");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return sb.toString();
    }

    /**
     * post请求（用于请求xml格式的参数）
     *
     * @param url
     * @param params
     * @return
     */
    public static String doPost(String url, String params) throws Exception {

        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建httpPost
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Content-Type", "application/xml");
        String charSet = "UTF-8";
        StringEntity entity = new StringEntity(params, charSet);
        httpPost.setEntity(entity);
        CloseableHttpResponse response = null;

        try {

            response = httpclient.execute(httpPost);
            StatusLine status = response.getStatusLine();
            int state = status.getStatusCode();
            if (state == HttpStatus.SC_OK) {
                HttpEntity responseEntity = response.getEntity();
                String jsonString = EntityUtils.toString(responseEntity, charSet);
                return jsonString;
            } else {

            }
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 带证书请求
     *
     * @param
     * @param
     * @return
     * @throws Exception
     */
//    public static String doRefund(String url, String data, WxPayProperties wxPayProperties) throws Exception {
//        /**
//         * PKCS12证书 是从微信商户平台-》账户设置-》 API安全 中下载的
//         */
//        KeyStore keyStore = KeyStore.getInstance("PKCS12");
//        InputStream instream = HttpUtils.class.getClassLoader().getResourceAsStream(wxPayProperties.getCertName());
//        if (instream == null) {
//            log.error("证书未找到，请保证项目根目录下存在名为【{}】的证书文件", wxPayProperties.getCertName());
//        }
//        try {
//            keyStore.load(instream, wxPayProperties.getMchId().toCharArray());
//        } finally {
//            instream.close();
//        }
//        SSLContext sslcontext = SSLContexts.custom()
//                .loadKeyMaterial(keyStore, wxPayProperties.getMchId().toCharArray())
//                .build();
//        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
//                sslcontext,
//                new String[]{"TLSv1"},
//                null,
//                SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
//        CloseableHttpClient httpclient = HttpClients.custom()
//                .setSSLSocketFactory(sslsf)
//                .build();
//        try {
//            HttpPost httpost = new HttpPost(url);
//            httpost.setEntity(new StringEntity(data, "UTF-8"));
//            CloseableHttpResponse response = httpclient.execute(httpost);
//            try {
//                HttpEntity entity = response.getEntity();
//                String jsonStr = EntityUtils.toString(response.getEntity(), "UTF-8");
//                EntityUtils.consume(entity);
//                return jsonStr;
//            } finally {
//                response.close();
//            }
//        } finally {
//            httpclient.close();
//        }
//    }

    public static String post(String requestUrl, String accessToken, String params)
            throws Exception {
        String contentType = "application/x-www-form-urlencoded";
        return HttpUtil.post(requestUrl, accessToken, contentType, params);
    }

    public static String post(String requestUrl, String accessToken, String contentType, String params)
            throws Exception {
        String encoding = "UTF-8";
        if (requestUrl.contains("nlp")) {
            encoding = "GBK";
        }
        return HttpUtil.post(requestUrl, accessToken, contentType, params, encoding);
    }

    public static String post(String requestUrl, String accessToken, String contentType, String params, String encoding)
            throws Exception {
        String url = requestUrl + "?access_token=" + accessToken;
        return HttpUtil.postGeneralUrl(url, contentType, params, encoding);
    }

    public static String postGeneralUrl(String generalUrl, String contentType, String params, String encoding)
            throws Exception {
        URL url = new URL(generalUrl);
        // 打开和URL之间的连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        // 设置通用的请求属性
        connection.setRequestProperty("Content-Type", contentType);
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setUseCaches(false);
        connection.setDoOutput(true);
        connection.setDoInput(true);

        // 得到请求的输出流对象
        DataOutputStream out = new DataOutputStream(connection.getOutputStream());
        out.write(params.getBytes(encoding));
        out.flush();
        out.close();

        // 建立实际的连接
        connection.connect();
        // 获取所有响应头字段
        Map<String, List<String>> headers = connection.getHeaderFields();
        // 遍历所有的响应头字段
        for (String key : headers.keySet()) {
            System.err.println(key + "--->" + headers.get(key));
        }
        // 定义 BufferedReader输入流来读取URL的响应
        BufferedReader in = null;
        in = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), encoding));
        String result = "";
        String getLine;
        while ((getLine = in.readLine()) != null) {
            result += getLine;
        }
        in.close();
        System.err.println("result:" + result);
        return result;
    }


    /********************************************************************************************/
    //微信扫码
//    public static final int connTimeout=10000;
//    public static final int readTimeout=10000;
//    public static final String charset="UTF-8";
//    private static HttpClient client = null;
//    static {
//        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
//        cm.setMaxTotal(128);
//        cm.setDefaultMaxPerRoute(128);
//        client = HttpClients.custom().setConnectionManager(cm).build();
//    }
//    public static Map<String,Object> getReq(String url) throws Exception {
//        return get(url, charset, null, null);
//    }
//
//    public static Map<String,Object> getReq(String url, String charset) throws Exception {
//        return get(url, charset, connTimeout, readTimeout);
//    }
    /**
     * 发送一个 GET 请求
     *
     * @param url
     * @param charset
     * @param connTimeout  建立链接超时时间,毫秒.
     * @param readTimeout  响应超时时间,毫秒.
     * @return
     * @throws ConnectTimeoutException   建立链接超时
     * @throws SocketTimeoutException   响应超时
     * @throws Exception
     */
//    public static Map<String,Object> get(String url, String charset, Integer connTimeout,Integer readTimeout)
//            throws ConnectTimeoutException,SocketTimeoutException, Exception {
//        Gson gson = new Gson();
//        Map<String,Object> map = new HashMap<>();
//        HttpClient client = null;
//        HttpGet get = new HttpGet(url);
//        String result = "";
//        try {
//            // 设置参数
//            RequestConfig.Builder customReqConf = RequestConfig.custom();
//            if (connTimeout != null) {
//                customReqConf.setConnectTimeout(connTimeout);
//            }
//            if (readTimeout != null) {
//                customReqConf.setSocketTimeout(readTimeout);
//            }
//            get.setConfig(customReqConf.build());
//
//            HttpResponse res = null;
//
//            if (url.startsWith("https")) {
//                // 执行 Https 请求.
//                client = createSSLInsecureClient();
//                res = client.execute(get);
//            } else {
//                // 执行 Http 请求.
//                client = HttpUtils.client;
//                res = client.execute(get);
//            }
//
//            result = IOUtils.toString(res.getEntity().getContent(), charset);
//        } finally {
//            get.releaseConnection();
//            if (url.startsWith("https") && client != null && client instanceof CloseableHttpClient) {
//                ((CloseableHttpClient) client).close();
//            }
//        }
//        return gson.fromJson(result,map.getClass());
//    }
    /**
     * 发送一个 Post 请求, 使用指定的字符集编码.
     *
     * @param url
     * @param body RequestBody
     * @param mimeType 例如 application/xml "application/x-www-form-urlencoded" a=1&b=2&c=3
     * @param charset 编码
     * @param connTimeout 建立链接超时时间,毫秒.
     * @param readTimeout 响应超时时间,毫秒.
     * @return ResponseBody, 使用指定的字符集编码.
     * @throws ConnectTimeoutException 建立链接超时异常
     * @throws SocketTimeoutException  响应超时
     * @throws Exception
     */
//    public static String post(String url, String body, String mimeType,String charset, Integer connTimeout, Integer readTimeout)
//            throws ConnectTimeoutException, SocketTimeoutException, Exception {
//        HttpClient client = null;
//        HttpPost post = new HttpPost(url);
//        String result = "";
//        try {
//            if (StringUtils.isNotBlank(body)) {
//                HttpEntity entity = new StringEntity(body, ContentType.create(mimeType, charset));
//                post.setEntity(entity);
//            }
//            // 设置参数
//            RequestConfig.Builder customReqConf = RequestConfig.custom();
//            if (connTimeout != null) {
//                customReqConf.setConnectTimeout(connTimeout);
//            }
//            if (readTimeout != null) {
//                customReqConf.setSocketTimeout(readTimeout);
//            }
//            post.setConfig(customReqConf.build());
//            HttpResponse res;
//            if (url.startsWith("https")) {
//                // 执行 Https 请求.
//                client = createSSLInsecureClient();
//                res = client.execute(post);
//            } else {
//                // 执行 Http 请求.
//                client = HttpUtils.client;
//                res = client.execute(post);
//            }
//            result = IOUtils.toString(res.getEntity().getContent(), charset);
//        } finally {
//            post.releaseConnection();
//            if (url.startsWith("https") && client != null&& client instanceof CloseableHttpClient) {
//                ((CloseableHttpClient) client).close();
//            }
//        }
//        return result;
//    }
    /**
     * 创建 SSL连接
     * @return
     * @throws GeneralSecurityException
     */
    private static CloseableHttpClient createSSLInsecureClient() throws GeneralSecurityException {
        try {
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            }).build();

            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, new X509HostnameVerifier() {

                @Override
                public boolean verify(String arg0, SSLSession arg1) {
                    return true;
                }

                @Override
                public void verify(String host, SSLSocket ssl)
                        throws IOException {
                }

                @Override
                public void verify(String host, X509Certificate cert)
                        throws SSLException {
                }

                @Override
                public void verify(String host, String[] cns,
                                   String[] subjectAlts) throws SSLException {
                }

            });

            return HttpClients.custom().setSSLSocketFactory(sslsf).build();

        } catch (GeneralSecurityException e) {
            throw e;
        }
    }
}
