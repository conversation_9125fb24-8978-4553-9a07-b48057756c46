user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log  main;
    sendfile        on;
    client_max_body_size 500m;
    keepalive_timeout 1800s; #指定 KeepAlive 的超时时间（timeout）。指定每个 TCP 连接最多可以保持多长时间。Nginx 的默认值是 75 秒，有些浏览器最多只保持 60 秒，所以可以设定为 60 秒。若将它设置为 0，就禁止了 keepalive 连接。
    proxy_connect_timeout 1800s; #nginx跟后端服务器连接超时时间(代理连接超时)
    proxy_send_timeout 1800s; #后端服务器数据回传时间(代理发送超时)
    proxy_read_timeout 1800s; #连接成功后，后端服务器响应时间(代理接收超时)
    fastcgi_connect_timeout 1800s; #指定nginx与后端fastcgi server连接超时时间
    fastcgi_send_timeout 1800s; #指定nginx向后端传送请求超时时间（指已完成两次握手后向fastcgi传送请求超时时间）
    fastcgi_read_timeout 1800s; #指定nginx向后端传送响应超时时间（指已完成两次握手后向fastcgi传送响应超时时间）

    # 网关服务上游配置
    upstream gateway_server {
        server **************:8801; 
        keepalive 32;
    }

    # h5 跳转
    server {
        listen     80;
        charset utf-8;
        server_name m.diandiansheji.com;
        return 307 https://$server_name$request_uri;
    }

    # 跳转 h5 登陆注册
    server {
        listen  443 ssl;
        server_name  m.diandiansheji.com;
        ssl_certificate_key /etc/nginx/ssl/m.diandiansheji.com.key;
        ssl_certificate /etc/nginx/ssl/m.diandiansheji.com.pem;
        ssl_session_timeout 30m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
        ssl_prefer_server_ciphers on;
        # h5注册登陆
        location / {
            root /web/h5/;
            try_files $uri $uri/ /index.html;
            index index.html;
        }
        # h5注册登陆
        location /pay {
            root /web/h5/;
            try_files $uri $uri/ /pay.html;
            index index.html;
        }

        # 跳转微信支付
#         location /MP_verify_Vz6IpjCkOdxxtz3s.txt {
#             alias /etc/nginx/ssl/;
#             try_files $uri $uri/ /MP_verify_Vz6IpjCkOdxxtz3s.txt
#             index index.html;
#         }
        location = /MP_verify_Vz6IpjCkOdxxtz3s.txt {
            root /etc/nginx/ssl;
        }


    }

    # 主域名跳转--web
    server {
        listen     80;
        charset utf-8;
        server_name diandiansheji.com;
        server_name www.diandiansheji.com;
        return 307 https://www.diandiansheji.com$request_uri;
    }

    # 跳转 443 www域名
    server {
        listen  443 ssl;
        server_name  diandiansheji.com;
        ssl_certificate_key /etc/nginx/ssl/diandiansheji.com.key;
        ssl_certificate /etc/nginx/ssl/diandiansheji.com.pem;
        ssl_session_timeout 30m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
        ssl_prefer_server_ciphers on;
        return 307 https://www.diandiansheji.com$request_uri;
    }

    # 跳转web页面-user
    server {
        listen  443 ssl;
        server_name  www.diandiansheji.com;
        ssl_certificate_key /etc/nginx/ssl/diandiansheji.com.key;
        ssl_certificate /etc/nginx/ssl/diandiansheji.com.pem;
        ssl_session_timeout 30m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
        ssl_prefer_server_ciphers on;
        # 404页面
        location /404.html {
            alias /web/404.html;
        }
        # react项目
        location / {
            root /web/user/;
            try_files $uri $uri/ /index.html;
            index index.html;
        }
        # 跳转微信支付
        location /MP_verify_Vz6IpjCkOdxxtz3s.txt {
            alias /etc/nginx/ssl/;
            try_files $uri $uri/ /MP_verify_Vz6IpjCkOdxxtz3s.txt
            index index.html;
        }

        # 跳转微信支付
        location /WW_verify_FPG19EcbmQr5k8n2.txt {
            alias /etc/nginx/ssl/;
            try_files $uri $uri/ /WW_verify_FPG19EcbmQr5k8n2.txt
            index index.html;
        }
        # 跳转apple 通用链接
        location /.well-known/apple-app-site-association {
            absolute_redirect on;
            alias /etc/nginx/ssl/apple-app-site-association;
            add_header Content-Type application/json;
        }

        error_page 404 /web/404.html;

    }

    # 跳转管理员页面-admin
    server {
        listen  443 ssl;
        server_name  admin.diandiansheji.com;
        ssl_certificate_key /etc/nginx/ssl/admin.diandiansheji.com.key;
        ssl_certificate /etc/nginx/ssl/admin.diandiansheji.com.pem;
        ssl_session_timeout 30m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
        ssl_prefer_server_ciphers on;

        location / {
            root /web/admin;
            try_files $uri $uri/ /index.html;
            index index.html;
        }

          # 管理平台
        location /web/adminconfig {
            root /;
#             alias /web/admin;
            try_files $uri $uri/ /web/adminconfig/index.html;
            index index.html;
        }
        error_page 404 /web/404.html;
    }

    # 跳转渠道
    server {
        listen  443 ssl;
        server_name  channel.diandiansheji.com;
        ssl_certificate_key /etc/nginx/ssl/channel.diandiansheji.com.key;
        ssl_certificate /etc/nginx/ssl/channel.diandiansheji.com.pem;
        ssl_session_timeout 30m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
        ssl_prefer_server_ciphers on;

        location / {
            root /web/channel/;
            try_files $uri $uri/ /index.html;
            index index.html;
        }
        error_page 404 /web/404.html;
    }

    # 跳转后端api服务
    server {
        listen  443 ssl;
        server_name  aiapi.diandiansheji.com;
        ssl_certificate_key /etc/nginx/ssl/aiapi.diandiansheji.com.key;
        ssl_certificate /etc/nginx/ssl/aiapi.diandiansheji.com.pem;
        ssl_session_timeout 30m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
        ssl_prefer_server_ciphers on;

        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_ssl_name "api.midjourneyapi.xyz";
        proxy_ssl_server_name on;

        # 跳转-众和服务-阿里云测试回调服务器
        location /callback-gf/cncn/ {
            proxy_buffering off;
            proxy_read_timeout 1800s;
            proxy_pass http://**************:8811/;
        }

        # 跳转-众和服务-阿里云测试回调服务器
        location /callback-gf/ {
            proxy_buffering off;
            proxy_read_timeout 1800s;
            proxy_pass http://***********$request_uri;
        }

        # WebSocket支持 - 保持直连
        location /user/api/websocket {
          #proxy_pass http://**************:8811;
          proxy_pass http://**************:8811;
          proxy_http_version 1.1;
          proxy_set_header Upgrade "websocket";
          proxy_set_header Connection "upgrade";
          proxy_read_timeout 600s;
        }
        
        # 用户子服务
        location /user {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            #proxy_pass http://**************:8811;
            # proxy_pass http://**************:8811; # 直连
            proxy_pass http://gateway_server/user; # 网关
        }

        # 旧短信接口禁用
        location /user/send/sms {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            #proxy_pass http://**************:8811;
            # proxy_pass http://**************:8811; # 直连
            # proxy_pass http://gateway_server/user/send/sms; # 网关
        }

        # fcs用户子服务
        location /fcsuser {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://**************:8833; # 网关
        }
		
		# digital子服务
        location /digital {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            # proxy_pass http://**************:8818; # 直连
            proxy_pass http://gateway_server/digital; # 网关
        }

        # fcs WebSocket支持 - 保持直连
        location /fcsuser/api/websocket {
          proxy_pass http://**************:8833;
          proxy_http_version 1.1;
          proxy_set_header Upgrade "websocket";
          proxy_set_header Connection "upgrade";
          proxy_read_timeout 600s;
        }

        # 管理子服务
        location /admin {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            #proxy_pass http://**************:8812;
            # proxy_pass http://**************:8812; #直连
            proxy_pass http://gateway_server/admin; # 网关
        }

        # 绘图子服务
        location /text {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            # proxy_pass http://**************:8821; # 直连
			#proxy_pass http://************:8821; # 跳转腾讯服务器，保证连接问题
            proxy_pass http://gateway_server/text; # 网关
        }

        # 绘图子服务
        # location /draw {
        #     proxy_http_version 1.1;
        #     proxy_set_header Connection "";
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #     proxy_read_timeout 86400s;
        #     proxy_buffering off;
        #     proxy_cache off;
        #     #proxy_pass http://**************:8822;
        #     proxy_pass http://************:8825; # 跳转腾讯服务器，保证连接问题
        # }
		
        # 绘图子服务-阿里云服务器
        location /draw {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            # proxy_pass http://**************:8825; # 直连
            proxy_pass http://gateway_server/draw; # 网关
        }

        # 绘图子服务-阿里云服务器 (旧版本路径兼容)
        location /goapi {
        # 将 /goapi/... 的请求重写为 /draw/... 然后由上面的 /draw location 处理
        rewrite ^/goapi(/.*)$ /draw$1 last;
    }

        # 课程服务-阿里云服务器
        location /course {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            # proxy_pass http://**************:8817; # 直连
            proxy_pass http://gateway_server/course; # 网关
        }

        # 跳转旧子服务
        location / {
            proxy_buffering off;
            proxy_read_timeout 1800s;
            proxy_pass http://**************:8081;
        }

    }

    # 跳转api
    server {
        listen  443 ssl;
        server_name  aiapi.diandiansheji.cn;
        ssl_certificate_key /etc/nginx/ssl/aiapi.diandiansheji.cn.key;
        ssl_certificate /etc/nginx/ssl/aiapi.diandiansheji.cn.pem;
        ssl_session_timeout 30m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
        ssl_prefer_server_ciphers on;

        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_ssl_name "api.midjourneyapi.xyz";
        proxy_ssl_server_name on;

        # 管理子服务
        location /admin {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            # proxy_pass http://**************:8812; # 直连
            proxy_pass http://gateway_server/admin; # 网关
        }

        # 绘图子服务-跳转腾讯服务器
        # location /draw {
        #     proxy_http_version 1.1;
        #     proxy_set_header Connection "";
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #     proxy_read_timeout 86400s;
        #     proxy_buffering off;
        #     proxy_cache off;
        #     proxy_pass http://************:8825; # 跳转腾讯服务器，保证连接问题
        # }

        # 绘图子服务-阿里云服务器
        location /draw {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://**************:8825;
            
        }

        # 绘图子服务-阿里云服务器
        location /goapi {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://**************:8826;
            
        }
		
        # 视频子服务
        location /text {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://**************:8821;
			#proxy_pass http://************:8821; # 跳转腾讯服务器，保证连接问题

        }

        # 用户子服务
        location /user {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://**************:8811;
        }

        # # WebSocket支持 - 保持直连
        location /user/api/websocket {
          proxy_pass http://**************:8811;
          proxy_http_version 1.1;
          proxy_set_header Upgrade "websocket";
          proxy_set_header Connection "upgrade";
          proxy_read_timeout 600s;
        }
		
		# 用户子服务
        location /fcsuser {
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_read_timeout 86400s;
            proxy_buffering off;
            proxy_cache off;
            proxy_pass http://**************:8833;
        }

        # # WebSocket支持 - 保持直连
        location /fcsuser/api/websocket {
          proxy_pass http://**************:8833;
          proxy_http_version 1.1;
          proxy_set_header Upgrade "websocket";
          proxy_set_header Connection "upgrade";
          proxy_read_timeout 600s;
        }



        # 跳转点点设计路由
        location / {
            proxy_buffering off;
            proxy_read_timeout 1800s;
            proxy_pass http://**************:8801;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Origin "";
        }
    }
    error_page 404 /web/404.html;

}
