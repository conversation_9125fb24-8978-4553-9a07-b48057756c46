version: "3.1"
services:
  ddsjgateway:
    image: tabatad/jdk21
    container_name: ddsjgateway
    working_dir: /server
    restart: always
    privileged: true
    ports:
      - 8801:8801
    volumes:
      - ./appfile:/appfile
      - ./logs:/usr/local/logs
      - ./conf:/usr/local/conf
    command: java -Xms2048M -Xmx4096M -Xss1M -Duser.timezone=GMT+8 -Dspring.profiles.active=test -Dspring.config.additional-location=/usr/local/conf/ -jar -Dfile.encoding=utf-8 /appfile/nacos-gateway-server-1.0.0-SNAPSHOT.jar
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
    networks:
      - ddsj-network

networks:
  ddsj-network:
    external: true
