package com.nacos.config;

import feign.Logger;
import feign.codec.Decoder;
import feign.codec.Encoder;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

@Configuration
public class FeignConfig {
    
    // 配置消息转换器
    @Bean
    public HttpMessageConverters httpMessageConverters() {
        return new HttpMessageConverters(new MappingJackson2HttpMessageConverter());
    }

    // 配置Feign日志级别
    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    // 配置Feign编码器
    @Bean
    public Encoder feignEncoder() {
        return new SpringEncoder(() -> httpMessageConverters());
    }

    // 配置Feign解码器
    @Bean
    public Decoder feignDecoder() {
        return new SpringDecoder(() -> httpMessageConverters());
    }
} 