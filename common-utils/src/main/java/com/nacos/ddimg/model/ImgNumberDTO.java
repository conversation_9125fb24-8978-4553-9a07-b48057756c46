package com.nacos.ddimg.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name = "绘图张数展示实体", description = "绘图张数展示实体")
@Data
public class ImgNumberDTO {

    @Schema(name = "主键", type = "Integer")
    private Integer key;

    @Schema(name = "值", type = "String")
    private String value;

    @Schema(name = "使用vip点数", type = "String")
    private String ddVipUseNumStr;

    @Schema(name = "使用点数", type = "Boolean")
    private String ddUseNumStr;

}
