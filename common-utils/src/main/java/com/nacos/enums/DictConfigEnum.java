package com.nacos.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name = "配置字典表枚举", description = "配置字典表枚举")
public enum DictConfigEnum {

    //赠送活动枚举使用
    DD_ADD_TASK_GIVE_INVITE(10000L, 1L, "邀请赠送点子数量", "DD_ADD_TASK_GIVE_INVITE"),
    DD_ADD_TASK_GIVE_INVITEE(10000L, 2L, "被邀请赠送点子数量", "DD_ADD_TASK_GIVE_INVITEE"),
    DD_ADD_TASK_GIVE_FIRST_APP(10000L, 3L, "APP首次登陆赠送点子数量", "DD_ADD_TASK_GIVE_FIRST_APP"),
    DD_ADD_TASK_GIVE_FIRST_WEB(10000L, 4L, "WEB首次登陆赠送点子数量", "DD_ADD_TASK_GIVE_FIRST_WEB"),
    DD_ADD_TASK_GIVE_FIRST_PASSWORD(10000L, 5L, "任务显示最大口令数量", "DD_ADD_TASK_GIVE_FIRST_PASSWORD"),

    //邀请码链接

    //app下载字典枚举
    ANDROID_APP_DOWNLOAD_URL(10001L, 1L, "Android 下载链接", "ANDROID_APP_DOWNLOAD_URL"),
    IOS_APP_DOWNLOAD_URL(10001L, 2L, "IOS 下载链接", "IOS_APP_DOWNLOAD_URL"),
    ALL_APP_DOWNLOAD_URL(10001L, 3L, "IOS Android 合并下载链接", "ALL_APP_DOWNLOAD_URL"),

    /* 服务客户 */
    DD_FANS_WX_CROWD(10002L, 1L, "点点粉丝群显示", "DD_FANS_WX_CROWD"),
    DD_CUSTOM_SERVICE_URL(10002L, 2L, "联系客服:二维码", "DD_CUSTOM_SERVICE_URL"),
    DD_WEB_URL(10002L, 3L, "点点设计网站地址:APP展示", "DD_WEB_URL"),

    /* 邀请链接地址 */
    DD_INVITE_URL(10003L, 1L, "邀请链接地址", "DD_INVITE_URL"),
    /* */
    AURORA_PRI_KEY(10004L, 1L, "极光推送密钥", "AURORA_PRI_KEY"),

    /* 默认绘图速度 */
    DEFAULT_DRAW_SPEED(10005L, 1L, "默认绘图速度", "DEFAULT_DRAW_SPEED"),

    /* 文案语调 */
    COPYWRITING_TONE(10010L, 1L, "文案语调", "COPYWRITING_TONE"),

    /* 点击通知跳转路由 */
    NOTIFICATION_ROUTE_TO_MESSAGE(10020L, 1L, "跳到通知页面", "NOTIFICATION_ROUTE_TO_MESSAGE"),
    NOTIFICATION_ROUTE_TO_ALBUM(10020L, 2L, "跳到写真页面", "NOTIFICATION_ROUTE_TO_ALBUM"),
    NOTIFICATION_ROUTE_TO_RECORD(10020L, 3L, "跳到绘画记录页面", "NOTIFICATION_ROUTE_TO_RECORD"),
    NOTIFICATION_ROUTE_TO_ACTIVITY(10020L, 4L, "跳到通知活动页面", "NOTIFICATION_ROUTE_TO_ACTIVITY"),

    /* chat 对接*/
    CHAT_KEY(20001L, 1L, "chat key 信息", "CHAT_KEY"),
    CHAT_URL(20001L, 2L, "chat url 信息", "CHAT_URL"),
    CHAT_ROLE_SCENE(20001L, 3L, "chat 角色信息-场景角色", "CHAT_ROLE_SCENE"),

    /* 苹果审核支付方式枚举 */
    APPLE_AUDIT_PRIVATE_VERSION(30001L, 1L, "苹果审核=支付类型参数", "APPLE_AUDIT_PRIVATE_VERSION"),

    /* APP审核备案信息 */
    APP_AUDIT_FILING_INFO(40000L, 1L, "备案信心参数", "APP_AUDIT_FILING_INFO"),

    /* A华为审核build号控制 */
    HUAWEI_AUDIT_BUILD_NUMBER(41111L, 1L, "华为审核build号控制", "HUAWEI_AUDIT_BUILD_NUMBER"),

    /* LE绘图key */
    LE_DRAW_KEY(20111L, 1L, "le key 信息", "LE_DRAW_KEY"),

    /* DOMO视频key */
    DOMO_API_KEY(20222L, 1L, "domo key 信息", "DOMO_API_KEY"),    /* DOMO视频key */

    /* 梦工厂视频key */
    DREAMFACTORY_API_KEY(20333L, 1L, "梦工厂key", "DOMO_API_KEY"),    /* 梦工厂视频key */

    /* runway视频key */
    RUNWAY_API_KEY(20666L, 1L, "runwaykey", "RUNWAY_API_KEY"),    /* runway视频key */

    /* 智谱视频key */
    ZHIPU_API_KEY(20444L, 1L, "智谱key", "ZHIPU_API_KEY"),    /* 智谱视频key */

    /* 海螺视频key */
    HAILUO_API_KEY(20555L, 1L, "海螺key", "HAILUO_API_KEY"),    /* 海螺视频key */

    /* 可灵视频key */
    KLING_API_KEY(20670L, 1L, "可灵key", "KELING_API_KEY"),
    /* 可灵图片key */
    KLING_IMG_API_KEY(20671L, 1L, "可灵key", "KLING_IMG_API_KEY"),

    /* 万相视频key */
    WANX_API_KEY(20680L, 1L, "万相key", "WANX_API_KEY"),

    /* 星野AI——key */
    XINGYE_API_KEY(20684L, 1L, "星野key", "XINGYE_API_KEY"),

    /* 火山引擎(即梦绘图)海报key */
    VOLCANO_POSTER_API_KEY(20777L, 1L, "火山引擎海报(即梦绘图)key", "VOLCANO_POSTER_API_KEY"),

    /* 火山引擎(即梦绘图)文案生成key */
    VOLCANO_CHAT_API_KEY(20778L, 1L, "火山引擎文案生成key", "VOLCANO_CHAT_API_KEY"),

    /* 腾讯云数字人视频key */
    TENCENT_VIDEO_API_KEY(20685L, 1L, "腾讯云数字人视频key", "TENCENT_VIDEO_API_KEY"),

    /* 禅境API key */
    CHANJING_API_KEY(20686L, 1L, "禅境API key", "CHANJING_API_KEY"),

    /* Minimax API Key */
    MINIMAX_API_KEY(20687L, 1L, "Minimax API Key", "MINIMAX_API_KEY"),

    /* ElevenLabs API Key */
    ELEVENLABS_API_KEY(20688L, 1L, "ElevenLabs API Key", "ELEVENLABS_API_KEY"),

    ;

    @Schema(description = "所属类code")
    private final Long dictType;

    @Schema(description = "所属子类键值")
    private final Long dictKey;

    @Schema(description = "redis缓存存储key")
    private final String redisKey;

    @Schema(description = "描述")
    private final String description;

    DictConfigEnum(Long dictType, Long dictKey, String description, String redisKey) {
        this.dictType = dictType;
        this.dictKey = dictKey;
        this.description = description;
        this.redisKey = redisKey;
    }

    /**
     * 获取点子相关数量
     *
     * @param value 具体数值
     * @return double 类型
     */
    public static Double getDDValueDouble(String value) {
        return value == null ? 0 : Double.parseDouble(value);
    }

    /* 使用 */
    public static int getIsUseTrue() {
        return 1;
    }

    /* 禁用 */
    public static int getIsUseFalse() {
        return 0;
    }
}
