package com.nacos.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

@Getter
@Schema(name = "公共错误返回内容枚举信息", description = "公共错误返回内容枚举信息")
public enum CommonResultEnum {

    // =================新版用户提示标题===============
    PROMPT_WORD_ERROR("提示词包含敏感信息,请按照社区规则调整后重试"),// 违规提示词
    IMG_NOT_MATCH_ERROR("图片不符合社区规定，请修改重试"),// 图片违规
    IMG_NUMBER_ERROR("图片数量选择错误"),// 图片违规
    SYSTEM_DISTRACTION_ERROR("对不起系统走神了，请稍后重试"),// 系统异常 * 随机
    SYSTEM_HOT_ERROR("服务器好烫，先降降温稍后再试"),// 系统异常 * 随机

    DZ_BALANCE_ERROR("点数不足，请购买点数"),// 点子不足异常

    HY_BALANCE_ERROR("请开通会员，才能使用此模型"),

    LE_DRAW_CREF_ERROR("通用模型角色图只能上传一张"),//
    LE_DRAW_SREF_ERROR("通用模型参考图只能上传一张"),//
    LE_DRAW_CREFANDSREF_ERROR("通用模型角色图和参考图不能同时选择"),//

    DRAW_MJ_CREF_ERROR("绘图模型不支持角色参考"),// 提示词问题
    DRAW_MJ_SREF_ERROR("绘图模型不支持风格参考"),// 提示词问题

    TASK_CONCURRENT_COUNT("任务数量已达到上限"),
    TASK_PROGRESS_ERROR("作品创作中不能取消"),
    TASK_CANCEL_FAIL_ERROR("作品取消失败"),
    TASK_CANCEL_SUCCESS_ERROR("作品取消成功"),

    VARY_REGION_PROMPT_ERROR("请绘制要修改的选区"),

    HISTORY_DELETION_ERROR("作品删除失败"),
    HISTORY_PROGRESS_ERROR("作品创作中不能删除"),

    CREATIVE_PROMPT_ERROR("生成创意指令失败,不扣点数"),// prompt words

    // =======================================以下内容需要改动========================================
    DRAW_MJ_API_ERROR("当前排队人数较多,请稍后重试"),// mj api 调用错误
    DRAW_MJ_API_BANNED_PROMPT_DETECTED("提示词包含敏感信息,请按照社区规则调整后重试"),// mj api 调用错误
    DRAW_MJ_API_INVALID_LINK("存在不支持的图片,请换张图片试试"),// mj api 调用错误
    DRAW_MJ_API_UNKNOWN_ERROR("网络波动严重，请稍后再试"),// mj api 未知错误
    DRAW_MJ_API_MODEL_CONFIG_ERROR("模型配置不存在，请联系客服"),// mj api 未知错误
    DRAW_MJ_API_RELAXED_ERROR("慢速当前排队中"),// mj api 慢速提示
    DRAW_MJ_API_RELAXED_NEW_ERROR("当前排队人数还有1人"),// mj api 慢速提示
    DRAW_MJ_API_QUEUE_FULL_ERROR("作业队列已满，稍后重试"),// mj api 慢速提示
    DRAW_MJ_API_INVALID_LINK_ERROR("图片验证不通过，请换张图片重试"),// mj api 慢速提示
    DRAW_MJ_API_PROMPT_ERROR("指令不合规"),// 提示词问题
    DRAW_MJ_API_INVALID_PARAMETER("指令不合法，请删除指令--version在重试"),// 提示词问题

    VIDEO_API_SIZE_ERROR("图片大小不能超过5MB"),// mj api 未知错误
    VIDEO_API_SVIP_ERROR("请开通会员，体验梦工厂模型"),// 提示词问题
    VIDEO_PROMPT_ERROR("提示词太短，最小长度为3个字符"),// 提示词问题

    SYSTEM_INVALID_PARAMETER("对不起系统走神了，请稍后重试！"),// 系统异常
    SYSTEM_INVALID_PARAMETER1("服务器好烫，先降降温稍后再试！"),// 系统异常


    PARAMETER_ERROR("参数无效"),// 参数错误
    NOT_ERROR("逻辑不存在"),// 逻辑不可能错误
    JOB_CANCEL_FAIL_ERROR("作品取消失败。"),// 参数错误
    JOB_CANCEL_ONGOING_ERROR("作品创作中不能取消。"),// 参数错误
    ;
    @Schema(description = "值")
    private final String value;

    CommonResultEnum(String value) {
        this.value = value;
    }

    public static String getSystemErrorMsg() {
        return getRandomItem();
    }

    public static String getRandomItem() {
        List<String> prefixList = Arrays.asList(SYSTEM_DISTRACTION_ERROR.getValue(), SYSTEM_HOT_ERROR.getValue());
        return prefixList.get(ThreadLocalRandom.current().nextInt(prefixList.size()));
    }

    public static String getSystemErrorInfo() {
        String[] ERROR_MESSAGES = {
                CommonResultEnum.SYSTEM_INVALID_PARAMETER.getValue(),  // 错误提示 1
                CommonResultEnum.SYSTEM_INVALID_PARAMETER1.getValue()   // 错误提示 2
        };

        final Random RANDOM = new Random();
        return ERROR_MESSAGES[RANDOM.nextInt(ERROR_MESSAGES.length)];
    }

    public static void main(String[] args) {
        for (int i = 0; i < 100; i++) {
            System.out.println(getSystemErrorInfo());
        }
    }

}
