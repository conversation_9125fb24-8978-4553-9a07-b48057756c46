package com.nacos.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Getter
@Schema(name="公共字符串枚举信息", description="公共字符串枚举信息")
public enum CommonStrEnum {

    MIDJOURNEY("https://cdn.midjourney.com"),// mj官网地址
    IMAGE_PREFIX("https://cdn.diandiansheji.com"),// 内部图片前缀
    IMAGE_SCALES_PREFIX("https://cdn.diandiansheji.com/global/all/scales/"),// 尺寸图前缀：svg图标
    NOTIFICATION_DEFAULT_ICON("https://cdn.diandiansheji.com/global/all/notice/notice.png"),// 通知默认图标 不能删
    ;
    @Schema(description = "值")
    private final String value;

    CommonStrEnum(String value) {
        this.value = value;
    }

}
