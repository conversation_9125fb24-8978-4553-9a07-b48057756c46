package com.nacos.enums;

import lombok.Getter;

/**
 * socket推送类型
 * <AUTHOR>
 * @date  2023-12-29
 */
@Getter
public enum WebSocketEnum {

    //登录
    LOGIN_PUSH(1001, "web端登录"),
    LOGIN_PUSH_APP(1002, "移动端登录"),

    //互动/通知
    INTERACTION_PUSH(1011, "互通消息"),
    NOTIFICATION_PUSH(1012, "通知消息"),


    //绑定
    WX_BIND_PUSH(1021, "绑定微信"),
    WX_PAY_PUSH(1022, "微信付款"),

    //写真
    PHOTO_PUSH(1031, "创意写真"),

    //绘图
    DRAW_JOB_PUSH(2001, "绘画任务"),
    DRAW_JOB_DE_PUSH(2002, "DALLE绘画任务"),

    VIDEO_JOB_SD_PUSH(3001, "视频生成任务"),

    AUDIO_JOB_PUSH(4001, "音乐生成任务"),

    //活动
    ACTIVITY_PUSH(5001, "活动推送"),

    //支付成功
    PAY_OK_PUSH(6001, "支付成功"),

    ;

    private final int pushType;
    private final String typeName;

    WebSocketEnum(int pushType, String typeName) {
        this.pushType = pushType;
        this.typeName = typeName;
    }

}
