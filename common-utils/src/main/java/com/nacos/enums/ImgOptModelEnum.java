package com.nacos.enums;

import lombok.Getter;


@Getter
public enum ImgOptModelEnum {

    //mj 模型开始
    DRAW_ATTRIBUTE_MJAPP_N5(1101), // MJ-N5模型
    DRAW_ATTRIBUTE_MJAPP_N6(1102), // MJ-N6模型
    DRAW_ATTRIBUTE_MJAPP_V5_2(1201), // MJ-V5.2模型
    DRAW_ATTRIBUTE_MJAPP_V6(1202), // MJ-V6模型
    DRAW_ATTRIBUTE_MJAPP_V6_1(1203), // MJ-V6.1模型
    DRAW_ATTRIBUTE_MJAPP_GENERIC_VERSION(1301), // MJ通用模型

    DRAW_ATTRIBUTE_DE(2001),// DE模型
    DRAW_ATTRIBUTE_SD(3001),// SD模型 - 绘画
    DRAW_ATTRIBUTE_LE(4001),// LE模型 - 绘画
    DRAW_ATTRIBUTE_FLUX(5001),// FLUX模型 - 绘画
    DRAW_ATTRIBUTE_KLING(5004),// KLING模型 - 绘画
    DRAW_ATTRIBUTE_ZHIPU(5003),// 智谱模型 - 绘画
    DRAW_ATTRIBUTE_JIMENG(5006),// JIMENG即梦海报模型 - 绘画
    DRAW_ATTRIBUTE_XINGYE(5010),// 星野

    // 视频模型
    VIDEO_ATTRIBUTE_SD_BASICS(13001),// SD 视频 这个模型变化失误性的
    VIDEO_ATTRIBUTE_BYTE_LENS(14001),// byte 镜头-视频
    VIDEO_ATTRIBUTE_LE_BASICS(15001),// le 模型-视频
    VIDEO_ATTRIBUTE_PIKA_BASICS(16001),// pika 模型-视频
    VIDEO_ATTRIBUTE_DOMO_BASICS(17001),// domoAPI 模型-视频
    VIDEO_ATTRIBUTE_LUMA_BASICS(18001),// luma梦工厂API 模型-视频
    VIDEO_ATTRIBUTE_DIGITALMAN_BASICS(19001),// 数字人
    VIDEO_ATTRIBUTE_RUNWAY2_BASICS(20001),// 达芬奇RUNWAY gen2模型-视频
    VIDEO_ATTRIBUTE_RUNWAY3AT_BASICS(20002),// 达芬奇RUNWAY gen3a模型-视频
    VIDEO_ATTRIBUTE_ZHIPU_BASICS(20003),// 智谱 = 皮克斯
    VIDEO_ATTRIBUTE_HAILUO_BASICS(20004),// MiNiMax == 好莱坞
    VIDEO_ATTRIBUTE_KLING_BASICS(20006),// KLing 模型 == 梦工厂2.0 
    VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS(20005),// 通义万相 == 万相境
    VIDEO_ATTRIBUTE_DAFENQI2_BASICS(20007),// 达芬奇RUNWAY == Gen-3模型
    VIDEO_ATTRIBUTE_DAFENQI3_BASICS(20008),// 达芬奇RUNWAY == 视频转绘
    VIDEO_ATTRIBUTE_DAFENQI4_BASICS(20009),// 达芬奇RUNWAY == 角色驱动
    VIDEO_ATTRIBUTE_HAILUO_V2_BASICS(20010),// MiNiMax == 好莱坞 - v2


    DRAW_ATTRIBUTE_VARIANT(9001),// 变体模型

    //音频模型
    AUDIO_ATTRIBUTE_SUNO(91111),// 音频suno模型
    AUDIO_ATTRIBUTE_HAILUO(91112),// 音频海螺minimax模型
    //mj 模型结束

    MJ_OPT_ATTRIBUTE_DRAW(10011), // MJ 绘图 生成
    MJ_OPT_ATTRIBUTE_SUBTLE(10021), // MJ 绘图 低变化
    MJ_OPT_ATTRIBUTE_STRONG(10022), // MJ 绘图 强变化
    MJ_OPT_ATTRIBUTE_REMIX(10025), // MJ 绘图 微调gao变化
    MJ_OPT_ATTRIBUTE_REMIX_SUBTLE(10026), // MJ 绘图 微调di变化
    MJ_OPT_ATTRIBUTE_VARY_REGION(10031),// MJ 绘图 局部修改
    MJ_OPT_ATTRIBUTE_UPSCALE_2X(10041),// MJ 绘图 2倍放大（v5、n5）
    MJ_OPT_ATTRIBUTE_UPSCALE_4X(10042),// MJ 绘图 4倍放大（v5、n5）
    MJ_OPT_ATTRIBUTE_UPSCALE_2X_SUBTLE(10043),// MJ 绘图 2倍放大-低变化（v6、n6）
    MJ_OPT_ATTRIBUTE_UPSCALE_2X_CREATIVE(10044),// MJ 绘图 4倍放大-高变化（v6、n6）

    MJ_OPT_ATTRIBUTE_ZOOM_TOP(10051),// MJ 绘图 上拓展
    MJ_OPT_ATTRIBUTE_ZOOM_BOTTOM(10052),// MJ 绘图 下拓展
    MJ_OPT_ATTRIBUTE_ZOOM_LEFT(10053),// MJ 绘图 左拓展
    MJ_OPT_ATTRIBUTE_ZOOM_RIGHT(10054),// MJ 绘图 右拓展
    MJ_OPT_ATTRIBUTE_ZOOM_MAKE(10055),// MJ 绘图 方形拓展
    MJ_OPT_ATTRIBUTE_ZOOM_CUSTOM(10056),// MJ 绘图 自定义拓展
    MJ_OPT_ATTRIBUTE_ZOOM_CHANGE_AR(10057),// MJ 绘图 Change Aspect Ratio自由拓展


    DALLE_OPT_ATTRIBUTE_DRAW(20011), // DE/SD/LE/FLUX/可伶/即梦（绘图 生成）/zhipu
    CC_OPT_ATTRIBUTE_FACE_FUSION(90011), // CC 公共 脸部融合

    // SD_OPT_ATTRIBUTE_DRAW(30011), // SD 绘图 生成

    // 创意视频
    SD_OPT_ATTRIBUTE_VIDEO(130011),// SD视频 生成
    BYTE_OPT_ATTRIBUTE_VIDEO(140011),// byte视频 生成
    LE_OPT_ATTRIBUTE_VIDEO(150011),// le视频 生成
    PIKA_OPT_ATTRIBUTE_VIDEO(160011),// 皮卡视频 生成
    DOMO_OPT_ATTRIBUTE_VIDEO(170011),// 哆莫视频 生成
    LUMA_OPT_ATTRIBUTE_VIDEO(180011),// 梦工厂视频 生成
    LUMA_OPT_ATTRIBUTE_VIDEO_EXTEND(180012),// 梦工厂视频延长 生成
    RUNWAY_OPT_ATTRIBUTE_VIDEO(200011),// 达芬奇runway视频 生成
    RUNWAY3A_OPT_ATTRIBUTE_VIDEO(200021),// 达芬奇runway3A视频 生成
    ZHIPU_OPT_ATTRIBUTE_VIDEO(200031),// 智谱 生成
    HAILUO_OPT_ATTRIBUTE_VIDEO(200041),// 海螺 生成 MiNiMax
    HAILUO_V2_OPT_ATTRIBUTE_VIDEO(200042),// 海螺 生成 v2 MiNiMax
    KLING_OPT_ATTRIBUTE_VIDEO(200051),// 可聆 生成
    FREAMFACTORY_OPT_ATTRIBUTE_VIDEO(200061),// 梦工厂2.0 生成
    DAFENQI_OPT_ATTRIBUTE_VIDEO(200071),// 达芬奇1.5 生成
    DAFENQI3_OPT_ATTRIBUTE_VIDEO(200081),// 达芬奇1.5 生成 ===视频转绘
    DAFENQI4_OPT_ATTRIBUTE_VIDEO(200091),// 达芬奇1.5 生成 === 角色驱动

    //金刚区高清重绘--操作
    GOAPI_OPT_ATTRIBUTE_UPSCALE_2X(40011),// 高清 2x
    GOAPI_OPT_ATTRIBUTE_UPSCALE_4X(40012),// 高清 4x
    GOAPI_OPT_ATTRIBUTE_UPSCALE_8X(40013),// 高清 8x
    LE_OPT_ATTRIBUTE_UPSCALE_REDRAW(40014),// 重绘
    LE_OPT_ATTRIBUTE_SKETCH_PAINT(40015),// LE控制草图上色

    SD_OPT_ATTRIBUTE_SKETCH_PAINT(40020),// SD控制草图上色
    SD_OPT_ATTRIBUTE_STRUCTURE_STYLE(40021),// SD控制图像风格迁移
    SD_OPT_ATTRIBUTE_INPAINT_REPLACE(40022),// SD局部修改
    BYTE_OPT_ATTRIBUTE_INPAINTING(40023),// byte 抹除-去除
    SD_OPT_ATTRIBUTE_OUTPAINT_ZOOM(40030),// SD图片拓展

    BYTE_OPT_ATTRIBUTE_FREE_SCALING(40031),// 字节图片缩放

    SUNO_OPT_ATTRIBUTE_AUDIO(92221),// SUNO音频 生成
    HAILUO_OPT_ATTRIBUTE_AUDIO(92222),// MINIMAX音频 生成

    //操作模型
    OPT_ATTRIBUTE_END(10001),//操作禁用编辑

    //操作内容：一级
    PARENT_OPERATE_ALL(0),//全局
    OPERATE_RETURN(1),//重绘
    OPERATE_EDIT(2),//编辑
    OPERATE_SAVE(3),//保存下载
    OPERATE_DELETE(4),//删除

    //2级展示内容
    OPERATE_EDIT_VARY_REGION(2001),//局部修改
    OPERATE_EDIT_VARY(2002),//变化
    OPERATE_EDIT_PAN_REGION(2003),//拓展
    OPERATE_EDIT_ZOOM(2004),//缩放
    OPERATE_EDIT_FACE_FUSION(2005),//脸部融合
    OPERATE_EDIT_UPSCALE(2006),//放大
    OPERATE_EDIT_CHANGE_AR(2007),//Change Aspect Ratio改变纵横比
    OPERATE_EDIT_FINE_TUNING(2008),//微调

    OPERATE_EDIT_VARY_SUBTLE(3001),//变化低
    OPERATE_EDIT_VARY_STRONG(3002),//变化高

    OPERATE_EDIT_FINE_TUNING_REMIX_SUBTLE(3004),//低变化=微调
    OPERATE_EDIT_FINE_TUNING_REMIX(3005),//高变化=微调

    OPERATE_EDIT_PAN_TOP(3011),//上拓展
    OPERATE_EDIT_PAN_BOTTOM(3012),//下拓展
    OPERATE_EDIT_PAN_LEFT(3013),//左拓展
    OPERATE_EDIT_PAN_RIGHT(3014),//右拓展
    OPERATE_EDIT_ZOOM_MAKE(3015),//方形拓展
    OPERATE_EDIT_ZOOM_CUSTOM(3016),//自定义缩放

    OPERATE_EDIT_UPSCALE_2X(3021),//放大2倍
    OPERATE_EDIT_UPSCALE_4X(3022),//放大4倍


    //金刚区高清重绘编辑属性值----开始----
    GOAPI_OPERATE_EDIT_PHOTO(3999),// goapi个人写真

    HIGH_OPERATE_EDIT_UPSCALE_2X(4001),// goapi高清2x
    HIGH_OPERATE_EDIT_UPSCALE_4X(4002),// goapi高清4x
    HIGH_OPERATE_EDIT_UPSCALE_8X(4003),// goapi高清8x
    HIGH_OPERATE_EDIT_UPSCALE_REDRAW(4011),// 重绘
    LE_OPERATE_EDIT_SKETCH_PAINT(4012), // le控制草图上色

    CONTROL_OPERATE_EDIT_SKETCH_PAINT(4020),// sd控制草图上色=原稿上色
    CONTROL_OPERATE_EDIT_STRUCTURE_STYLE(4021),// 控制图像风格迁移

    OPERATE_EDIT_INPAINT_REPLACE(4022),   // 控制图片局部修改=替换
    OPERATE_EDIT_HS_INPAINTING(4023),// 控制图片抹除=去掉不适合的
    OPERATE_EDIT_HS_INPAINTING_new_daidieng(4024),// 控制图片智能=智能抠图

    OPERATE_EDIT_OUTPAINT_ZOOM(4030),// 控制图片自由拓展
    OPERATE_EDIT_FREE_SCALING(4031),// 控制图片自由缩放
    //金刚区高清重绘操作属性值----结束----


    MJ_PADDING_REFERENCE(1), // 垫图参考
    MJ_PADDING_STYLE(2),// 垫图风格
    MJ_PADDING_ROLE(3), // 垫图角色

    ;

    final int value;

    ImgOptModelEnum(int value) {
        this.value = value;
    }

    // 获取操作标题一级
    public static String getOptTitleOne(int value) {
        if (MJ_OPT_ATTRIBUTE_VARY_REGION.value == value) {
            return "局部修改";
        }
        if (MJ_OPT_ATTRIBUTE_SUBTLE.value == value || MJ_OPT_ATTRIBUTE_STRONG.value == value) {
            return "变化";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_TOP.value == value
                || MJ_OPT_ATTRIBUTE_ZOOM_BOTTOM.value == value
                || MJ_OPT_ATTRIBUTE_ZOOM_LEFT.value == value
                || MJ_OPT_ATTRIBUTE_ZOOM_RIGHT.value == value
                || MJ_OPT_ATTRIBUTE_ZOOM_MAKE.value == value
        ) {
            return "拓展";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_CHANGE_AR.value == value) {
            return "自由拓展";
        }
        if (MJ_OPT_ATTRIBUTE_UPSCALE_2X.value == value || MJ_OPT_ATTRIBUTE_UPSCALE_4X.value == value || MJ_OPT_ATTRIBUTE_UPSCALE_2X_SUBTLE.value == value || MJ_OPT_ATTRIBUTE_UPSCALE_2X_CREATIVE.value == value) {
            return "放大";
        }
        if (CC_OPT_ATTRIBUTE_FACE_FUSION.value == value) {
            return "脸部融合";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_CUSTOM.value == value) {
            return "缩放";
        }
        if (MJ_OPT_ATTRIBUTE_DRAW.value == value || DALLE_OPT_ATTRIBUTE_DRAW.value == value) {
            return "灵感绘画";
        }
        if (SD_OPT_ATTRIBUTE_VIDEO.value == value || BYTE_OPT_ATTRIBUTE_VIDEO.value == value || LE_OPT_ATTRIBUTE_VIDEO.value == value
                || PIKA_OPT_ATTRIBUTE_VIDEO.value == value || DOMO_OPT_ATTRIBUTE_VIDEO.value == value || LUMA_OPT_ATTRIBUTE_VIDEO.value == value
                || RUNWAY_OPT_ATTRIBUTE_VIDEO.value == value || RUNWAY3A_OPT_ATTRIBUTE_VIDEO.value == value || ZHIPU_OPT_ATTRIBUTE_VIDEO.value == value
                || HAILUO_OPT_ATTRIBUTE_VIDEO.value == value || KLING_OPT_ATTRIBUTE_VIDEO.value == value || FREAMFACTORY_OPT_ATTRIBUTE_VIDEO.value == value
                || DAFENQI_OPT_ATTRIBUTE_VIDEO.value == value || HAILUO_V2_OPT_ATTRIBUTE_VIDEO.value == value) {
            return "创意视频";
        }
        if (DAFENQI3_OPT_ATTRIBUTE_VIDEO.value == value) {
            return "视频转绘";
        }
        if (DAFENQI4_OPT_ATTRIBUTE_VIDEO.value == value) {
            return "角色驱动";
        }
        if (SUNO_OPT_ATTRIBUTE_AUDIO.value == value) {
            return "灵感音乐";
        }
        if (GOAPI_OPT_ATTRIBUTE_UPSCALE_2X.value == value || GOAPI_OPT_ATTRIBUTE_UPSCALE_4X.value == value
                || GOAPI_OPT_ATTRIBUTE_UPSCALE_8X.value == value || LE_OPT_ATTRIBUTE_UPSCALE_REDRAW.value == value) {
            return "高清重绘";
        }
        if (SD_OPT_ATTRIBUTE_SKETCH_PAINT.value == value || LE_OPT_ATTRIBUTE_SKETCH_PAINT.value == value) {
            return "原稿上色";
        }
        if (SD_OPT_ATTRIBUTE_STRUCTURE_STYLE.value == value) {
            return "风格迁移";
        }
        if (SD_OPT_ATTRIBUTE_INPAINT_REPLACE.value == value) {
            return "图像替换";
        }
        if (BYTE_OPT_ATTRIBUTE_INPAINTING.value == value) {
            return "图像抹除";
        }
        if (SD_OPT_ATTRIBUTE_OUTPAINT_ZOOM.value == value) {
            return "智能拓图";
        }
        if (BYTE_OPT_ATTRIBUTE_FREE_SCALING.value == value) {
            return "智能缩放";
        }
        if (LUMA_OPT_ATTRIBUTE_VIDEO_EXTEND.value == value) {
            return "视频延长";
        }
        if (MJ_OPT_ATTRIBUTE_REMIX.value == value || MJ_OPT_ATTRIBUTE_REMIX_SUBTLE.value == value) {
            return "微调";
        }
        return null;
    }

    // 获取操作标题二级
    public static String getOptTitleTwo(int value) {

        if (MJ_OPT_ATTRIBUTE_SUBTLE.value == value) {
            return "低";
        }
        if (MJ_OPT_ATTRIBUTE_STRONG.value == value) {
            return "高";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_TOP.value == value) {
            return "向上";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_BOTTOM.value == value) {
            return "向下";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_LEFT.value == value) {
            return "向左";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_RIGHT.value == value) {
            return "向右";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_MAKE.value == value) {
            return "方形";
        }
        if (MJ_OPT_ATTRIBUTE_UPSCALE_2X.value == value) {
            return "2倍";
        }
        if (MJ_OPT_ATTRIBUTE_UPSCALE_4X.value == value) {
            return "4倍";
        }
        if (MJ_OPT_ATTRIBUTE_UPSCALE_2X_SUBTLE.value == value) {
            return "2倍-低变化";
        }
        if (MJ_OPT_ATTRIBUTE_UPSCALE_2X_CREATIVE.value == value) {
            return "2倍-高变化";
        }
        if (GOAPI_OPT_ATTRIBUTE_UPSCALE_2X.value == value) {
            return "高清2X";
        }
        if (GOAPI_OPT_ATTRIBUTE_UPSCALE_4X.value == value) {
            return "高清4X";
        }
        if (GOAPI_OPT_ATTRIBUTE_UPSCALE_8X.value == value) {
            return "高清8X";
        }
        if (LE_OPT_ATTRIBUTE_UPSCALE_REDRAW.value == value) {
            return "重绘";
        }
        if (MJ_OPT_ATTRIBUTE_REMIX.value == value) {
            return "低变化";
        }
        if (MJ_OPT_ATTRIBUTE_REMIX_SUBTLE.value == value) {
            return "高变化";
        }
        return null;
    }

    public static String getOptTitleAll(int value) {
        if (MJ_OPT_ATTRIBUTE_VARY_REGION.value == value) {
            return "局部修改";
        }
        if (CC_OPT_ATTRIBUTE_FACE_FUSION.value == value) {
            return "脸部融合";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_CUSTOM.value == value) {
            return "缩放";
        }
        if (MJ_OPT_ATTRIBUTE_DRAW.value == value || DALLE_OPT_ATTRIBUTE_DRAW.value == value) {
            return "灵感绘画";
        }
        if (MJ_OPT_ATTRIBUTE_SUBTLE.value == value) {
            return "低变化";
        }
        if (MJ_OPT_ATTRIBUTE_STRONG.value == value) {
            return "高变化";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_TOP.value == value) {
            return "向上拓展";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_BOTTOM.value == value) {
            return "向下拓展";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_LEFT.value == value) {
            return "向左拓展";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_RIGHT.value == value) {
            return "向右拓展";
        }
        if (MJ_OPT_ATTRIBUTE_ZOOM_MAKE.value == value) {
            return "方形拓展";
        }
        if (MJ_OPT_ATTRIBUTE_UPSCALE_2X.value == value) {
            return "放大2倍";
        }
        if (MJ_OPT_ATTRIBUTE_UPSCALE_4X.value == value) {
            return "放大4倍";
        }
        if (MJ_OPT_ATTRIBUTE_UPSCALE_2X_SUBTLE.value == value) {
            return "2倍-低变化";
        }
        if (MJ_OPT_ATTRIBUTE_UPSCALE_2X_CREATIVE.value == value) {
            return "2倍-高变化";
        }
        return null;
    }

    // 视频
    public static Integer getOptAttributeByModelId(int modelId) {
        if (VIDEO_ATTRIBUTE_SD_BASICS.value == modelId) {
            return SD_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_BYTE_LENS.value == modelId) {
            return BYTE_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_LE_BASICS.value == modelId) {
            return LE_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_PIKA_BASICS.value == modelId) {
            return PIKA_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_DOMO_BASICS.value == modelId) {
            return DOMO_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_LUMA_BASICS.value == modelId) {
            return LUMA_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_RUNWAY2_BASICS.value == modelId) {
            return RUNWAY_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_RUNWAY3AT_BASICS.value == modelId) {
            return RUNWAY3A_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_ZHIPU_BASICS.value == modelId) {
            return ZHIPU_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_HAILUO_BASICS.value == modelId) {
            return HAILUO_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_HAILUO_V2_BASICS.value == modelId) {
            return HAILUO_V2_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_KLING_BASICS.value == modelId) {
            return KLING_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS.value == modelId) {
            return FREAMFACTORY_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_DAFENQI2_BASICS.value == modelId) {
            return DAFENQI_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_DAFENQI3_BASICS.value == modelId) {
            return DAFENQI3_OPT_ATTRIBUTE_VIDEO.value;
        }
        if (VIDEO_ATTRIBUTE_DAFENQI4_BASICS.value == modelId) {
            return DAFENQI4_OPT_ATTRIBUTE_VIDEO.value;
        }
        return 0;
    }

}
