package com.nacos.enums;

import lombok.Getter;

@Getter
public enum UserDDrecordEnum {
    //0:回退；1付款类型的记录；2任务类型的记录；3活动类型的记录；
    TYPE_RETURN(0),
    TYPE_PAY(1),
    TYPE_TASK(2),
    TYPE_ACTIVITY(3),
    TYPE_CHANNEL(4),

    TYPE_RETURN_ALL(0,"用户返还点子数量登记"),

    //1付款类型的记录；
    TYPE_ITEM_PAY_JYB(1,"加油包支付"),
    TYPE_ITEM_PAY_VIP(2,"VIP会员支付"),
    TYPE_ITEM_PAY_SVIP(3,"SVIP会员支付"),

    //2任务类型的记录；
    TYPE_ITEM_TASK_GIVE_APP(2001,"APP首次登陆赠送点子"),
    TYPE_ITEM_TASK_GIVE_WEB(2002,"WEB首次登陆赠送点子"),
    TYPE_ITEM_TASK_GIVE_INVITE_NEW_USER(2003,"邀请新用户赠送点子"),
    TYPE_ITEM_TASK_GIVE_INVITED_BY_FRIENDS(2004,"被好友邀请赠送点子"),
    TYPE_ITEM_TASK_GIVE_DAILY_SIGN(2005,"每日签到赠送点子"),
    TYPE_ITEM_TASK_GIVE_FIRST_GOOG(2006,"首次好评赠送点子"),


    //3活动类型的记录；
    TYPE_ITEM_ACTIVITY_REDEMPTION_CODE(3001,"兑换码兑换点子"),
    TYPE_ITEM_ACTIVITY_PASSWORD(3002,"口令兑换点子"),

    //特殊渠道商
    TYPE_ITEM_CHANNEL_INVITED(4001,"渠道商邀请赠送点子"),

    SOURCE_ID_INVALID(0,"无价值id防止空"),


    STATE_USEED(0,"已用完"),
    STATE_INUSE(1,"使用中"),
    STATE_EXPIRE(2,"已过期"),

    ;
    final Integer intValue;

    final String strValue;
    UserDDrecordEnum(Integer intValue) {
        this.intValue = intValue;
        this.strValue = null;
    }

    UserDDrecordEnum(Integer intValue, String strValue) {
        this.intValue = intValue;
        this.strValue = strValue;
    }
}
