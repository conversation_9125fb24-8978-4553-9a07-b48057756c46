package com.nacos.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * redis工具
 * redis 5种数据类型：
 * opsForValue（字符串）
 * opsForList（列表）
 * opsForHash（集合）
 * opsForSet（有序集合）
 * OpsForZSet（散列表）
 */
@Slf4j
@Component
public class RedisUtil {

    public static String REDIS_SUNO_AUDIO_STATUS = "redis-suno-audio_status";
    public static String REDIS_USER_DIGITAL_MAN_APPLY = "USER:DIGITAL_MAN_APPLY";
    public static String REDIS_GLOBAL_CONFIG_CRAFTSMANSHIP = "CONFIG:REDIS_GLOBAL_CONFIG_CRAFTSMANSHIP";
    public static String REDIS_RUNWAY_SUBMIT_TASK_LIST = "VIDEO:REDIS_RUNWAY_SUBMIT_TASK_LIST";
    public static String REDIS_RUNWAY_SUBMIT_TASK_VIDEO_LIST = "VIDEO:REDIS_RUNWAY_SUBMIT_TASK_VIDEO_LIST";
    public static String REDIS_RUNWAY_SUBMIT_TASK_VIDEO_ROLE_LIST = "VIDEO:REDIS_RUNWAY_SUBMIT_TASK_VIDEO_ROLE_LIST";
    public static String REDIS_HAILUO_SUBMIT_TASK_LIST = "VIDEO:REDIS_HAILUO_SUBMIT_TASK_LIST";
    public static String REDIS_KLING_SUBMIT_TASK_LIST = "VIDEO:REDIS_KLING_SUBMIT_TASK_LIST";
    public static String REDIS_RUNWAY_ACCOUNT_PREFIX = "RUNWAY:ACCOUNT:";
    public static String REDIS_LUMA_ACCOUNT_PREFIX = "LUMA:ACCOUNT:";
    public static String REDIS_HAOLUO_ACCOUNT_PREFIX = "HAILUO:ACCOUNT:";
    public static String REDIS_DIGITAL_AUDIO_STATUS = "DIGITAL_AUDIO_STATUS:";//数字人音频任务状态

    private static StringRedisTemplate staticStringRedisTemplate;

    private static RedisTemplate<String, Object> redisTemplate;

    public static void convertAndSend(String channel, Object message) {
        staticStringRedisTemplate.convertAndSend(channel, message);
    }

    /**
     * 判断key是否存在
     *
     * @param key
     * @return
     */
    public static Boolean hasKey(final String key) {
        return staticStringRedisTemplate.hasKey(key);
    }

    public static boolean setIfAbsent(String key, String value, long timeout, TimeUnit unit) {
        return Boolean.TRUE.equals(staticStringRedisTemplate.opsForValue().setIfAbsent(key, value, timeout, unit));
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public static Set<String> getKeys(final String pattern) {
        return staticStringRedisTemplate.keys(pattern);
    }


    /**
     * 获得某个key剩余时间
     *
     * @param key key
     */
    public static Long getExpire(final String key, TimeUnit timeUnit) {
        return staticStringRedisTemplate.getExpire(key, timeUnit);
    }

    public RedisUtil(StringRedisTemplate stringRedisTemplate, RedisTemplate<String, Object> redisTemplate) {
        RedisUtil.staticStringRedisTemplate = stringRedisTemplate;
        RedisUtil.redisTemplate = redisTemplate;
    }

    public static boolean setKeyExpire(final String key, final long timeout, final TimeUnit unit) {
        return Boolean.TRUE.equals(staticStringRedisTemplate.expire(key, timeout, unit));
    }

    /**
     * 删除
     *
     * @param key 键
     */
    public static boolean removeKey(String key) {
        return Boolean.TRUE.equals(staticStringRedisTemplate.delete(key));
    }

    //批量删除
    public static void removeKeys(Collection<String> keys) {
        staticStringRedisTemplate.delete(keys);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public static boolean setValue(String key, String value) {
        try {
            staticStringRedisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
    }

    public static void setValueSeconds(String key, String value, long seconds, TimeUnit timeUnit) {
        staticStringRedisTemplate.opsForValue().set(key, value, seconds, timeUnit);
    }

    /**
     * 存储集合
     *
     * @param key
     * @param values
     */
    public static void addToSet(String key, Set<String> values) {
        staticStringRedisTemplate.opsForSet().add(key, values.toArray(new String[0]));
    }

    /**
     * 获取集合
     *
     * @param key
     * @return
     */
    public static Set<String> getMembers(String key) {
        return staticStringRedisTemplate.opsForSet().members(key);
    }


    /**
     * 获取字符串信息
     *
     * @param key 键
     */
    public static String getValue(String key) {
        return staticStringRedisTemplate.opsForValue().get(key);
    }

    /**
     * 获取字符串信息
     *
     * @param lockKey             锁的键值
     * @param expireTimeInSeconds 锁的超时时间
     */
    public static boolean acquireLock(String lockKey, long expireTimeInSeconds) {
        return Boolean.TRUE.equals(staticStringRedisTemplate.opsForValue().setIfAbsent(lockKey, "true", expireTimeInSeconds, TimeUnit.SECONDS));
    }

    /**
     * 获取字符串信息
     *
     * @param lockKey             锁的键值
     * @param expireTimeInSeconds 锁的超时时间
     */
    public static boolean acquireLockByDay(String lockKey, long expireTimeInSeconds) {
        return Boolean.TRUE.equals(staticStringRedisTemplate.opsForValue().setIfAbsent(lockKey, "true", expireTimeInSeconds, TimeUnit.DAYS));
    }

    /**
     * 释放锁
     *
     * @param lockKey 锁的键值
     */
    public static void releaseLock(String lockKey) {
        staticStringRedisTemplate.delete(lockKey);
    }

    public static Collection<String> keysCounts(final String pattern) {
        return staticStringRedisTemplate.opsForValue().getOperations().keys(pattern);
    }


    public static boolean joinTheQueue(String key, String value) {
        try {
            staticStringRedisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
    }

    public static boolean outOfQueue(String key) {
        try {
            staticStringRedisTemplate.opsForValue().get(key);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
    }


    // ===============================list=================================

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束 0 到 -1代表所有值
     */
    public static List<String> lGet(String key, long start, long end) {
        try {
            List<String> range = staticStringRedisTemplate.opsForList().range(key, start, end);
            return range;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 获取list缓存的长度
     *
     * @param key 键
     */
    public long lGetListSize(String key) {
        try {
            return staticStringRedisTemplate.opsForList().size(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }


    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     */
    public Object lGetIndex(String key, long index) {
        try {
            return staticStringRedisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     */
    public static String lGetIndexFromList(String key, long index) {
        try {
            return staticStringRedisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     */
    public boolean lSet(String key, String value) {
        try {
            staticStringRedisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 将list放入缓存从左边放
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public static boolean lpushList(String key, String value) {
        try {
            staticStringRedisTemplate.opsForList().leftPush(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

    /**
     * 将list放入缓存从右边放
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public static boolean rPushList(String key, String value) {
        try {
            staticStringRedisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

    /**
     * 将数据从右边放进去
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public static boolean rPushListByCount(String key, String value) {
        try {
            staticStringRedisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

    /**
     * 从list中取出数据 从右边取
     *
     * @param key 键
     * @return
     */
    public static String rPopList(String key) {
        try {
            String s = staticStringRedisTemplate.opsForList().rightPop(key);
            return s;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    /**
     * 从list中删除元素
     *
     * @param key 键
     * @return
     */
    public static Long remove(String key, long count, Object value) {
        try {
            Long remove = staticStringRedisTemplate.opsForList().remove(key, count, value);
            return remove;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    /**
     * 从list中取出数据 从右边取
     *
     * @param key 键
     * @return
     */
    public static List<String> rPopListByCount(String key, Long count) {
        try {
            List<String> strings = staticStringRedisTemplate.opsForList().rightPop(key, count);
            return strings;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    /**
     * Set集合添加
     *
     * @param key
     * @param value
     */

    public static boolean addSet(String key, String value) {
        try {
            SetOperations<String, String> set = staticStringRedisTemplate.opsForSet();
            set.add(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Set集合中元素的个数
     *
     * @param key
     */

    public static Long countSet(String key) {
        try {
            SetOperations<String, String> set = staticStringRedisTemplate.opsForSet();
            Long size = set.size(key);
            return size;
        } catch (Exception e) {
            e.printStackTrace();
            return 0L;
        }
    }

    /**
     * Set集合取出元素
     *
     * @param key
     */

    public static String popSet(String key) {
        try {
            SetOperations<String, String> set = staticStringRedisTemplate.opsForSet();
            String pop = set.pop(key);
            return pop;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


}
