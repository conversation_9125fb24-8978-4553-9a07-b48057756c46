package com.nacos.mjapi;

import com.alibaba.fastjson2.JSONObject;
import com.nacos.enums.ImgOptModelEnum;
import com.nacos.mjapi.model.*;
import com.nacos.result.Result;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MJApis {


    public static String submitJobDrawParam(String mode, String ar, int stylize, String version, String prompt, String style){
        return MJAPIUtil.requestSubmitJobsBodyJsonImagine(mode,ar,stylize,version,prompt,style);
    }

    public static JobInfo submitJobDraw(MJAccountHeaderBO mjAccountHeaderBO){
        if (mjAccountHeaderBO == null || mjAccountHeaderBO.getCookie() == null || mjAccountHeaderBO.getToken() == null || mjAccountHeaderBO.getJsonParameter() == null){
            return null;
        }
        return MJAPIUtil.postSubmitJobs(
                mjAccountHeaderBO.getJsonParameter(),
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getCookie(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion()
        );
    }
    /**
     * 放大接口
     * @param requestBodyJson 原请求内容
     * @param parentJboId 上级工作id
     * @param index 图片序号
     * @return JobInfo 任务信息
     */
    public static JobInfo submitJobUpscale(String requestBodyJson, int upscale, String parentJboId, int index, MJAccountHeaderBO mjAccountHeaderBO){
        if (index < 0 || index > 3) {
            return null;
        }
        ++index;
        String requestSubmitJobsBodyJson = MJAPIUtil.requestSubmitJobsBodyJsonUpscale(requestBodyJson, parentJboId, index,upscale);
        if (requestSubmitJobsBodyJson == null){
            return null;
        }
        return MJAPIUtil.postSubmitJobs(
                requestSubmitJobsBodyJson,
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getCookie(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion()
        );
    }

    /**
     * 平移接口
     * @param requestBodyJson 原请求内容
     * @param parentJboId 上级工作id
     * @param index 图片序号
     * @return JobInfo
     */
    public static JobInfo submitPan(String requestBodyJson, int zoom, String parentJboId, int index, MJAccountHeaderBO mjAccountHeaderBO){
        if (index < 0 || index > 3) {
            return null;
        }
        ++index;
        String requestSubmitJobsBodyJson = null;
        if (ImgOptModelEnum.OPERATE_EDIT_PAN_TOP.getValue() == zoom){
            requestSubmitJobsBodyJson = MJAPIUtil.requestSubmitJobsBodyJsonPanTop(requestBodyJson, parentJboId, index);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_PAN_BOTTOM.getValue() == zoom){
            requestSubmitJobsBodyJson = MJAPIUtil.requestSubmitJobsBodyJsonPanBottom(requestBodyJson, parentJboId, index);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_PAN_LEFT.getValue() == zoom){
            requestSubmitJobsBodyJson = MJAPIUtil.requestSubmitJobsBodyJsonPanLeft(requestBodyJson, parentJboId, index);
        }
        if (ImgOptModelEnum.OPERATE_EDIT_PAN_RIGHT.getValue() == zoom){
            requestSubmitJobsBodyJson = MJAPIUtil.requestSubmitJobsBodyJsonPanRight(requestBodyJson, parentJboId, index);
        }
        if (requestSubmitJobsBodyJson == null){
            return null;
        }
        return MJAPIUtil.postSubmitJobs(
                requestSubmitJobsBodyJson,
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getCookie(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion()
        );
    }

    /**
     * 方形拓展
     * @param requestBodyJson 请求参数实体
     * @param parentJboId 父级id
     * @param index 图片序号：默认1
     * @return
     */
    public static JobInfo submitSquare(String requestBodyJson, String parentJboId, int index, MJAccountHeaderBO mjAccountHeaderBO){
        if (index < 0 || index > 3) {
            return null;
        }
        ++index;
        return MJAPIUtil.postSubmitJobs(
                MJAPIUtil.requestSubmitJobsBodyJsonSquare(requestBodyJson, parentJboId, index),
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getCookie(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion()
        );
    }

    /**
     * 缩放
     * @param requestBodyJson 请求参数实体
     * @param parentJboId 父级id
     * @param index 图片序号：默认1
     * @param zoomFactorStr 缩放比例：1.0 - 2.0; 梯度：0.1
     * @return
     */
    public static JobInfo submitZoom(String requestBodyJson, String parentJboId, int index, String zoomFactorStr, MJAccountHeaderBO mjAccountHeaderBO){
        if (index < 0 || index > 3) {
            return null;
        }
        ++index;
        BigDecimal zoomFactor = new BigDecimal(zoomFactorStr).subtract(new BigDecimal("1"));
        zoomFactor = zoomFactor.multiply(new BigDecimal("50"));
        zoomFactor = new BigDecimal("100").subtract(zoomFactor);
        if (zoomFactor.intValue() < 50 || zoomFactor.intValue() > 100){
            return null;
        }
        return MJAPIUtil.postSubmitJobs(
                MJAPIUtil.requestSubmitJobsBodyJsonZoom(requestBodyJson, parentJboId, index,zoomFactor.intValue()),
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getCookie(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion()
        );
    }

    /**
     * 变化：弱变化；强变化
     * @param requestBodyJson 请求参数实体
     * @param parentJboId 父级id
     * @param index 图片序号：默认1
     * @param varyType 变化：低变化：3001；强变化：3002
     * @return
     */
    public static JobInfo submitVary(String requestBodyJson, int varyType, String parentJboId, int index, MJAccountHeaderBO mjAccountHeaderBO){
        if (index < 0 || index > 3) {
            return null;
        }
        ++index;
        if (ImgOptModelEnum.OPERATE_EDIT_VARY_STRONG.getValue() != varyType && ImgOptModelEnum.OPERATE_EDIT_VARY_SUBTLE.getValue() != varyType){
            return null;
        }
        return MJAPIUtil.postSubmitJobs(
                MJAPIUtil.requestSubmitJobsBodyJsonVary(requestBodyJson, parentJboId, index,varyType),
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getCookie(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion()
        );
    }

    /**
     * 局部修改
     * @param requestBodyJson 请求参数实体
     * @param parentJboId 父级id
     * @param index 图片序号：默认1
     * @param prompt 提示指令
     * @param maskBate64 蒙板图片bate64格式
     * @return
     */
    public static JobInfo submitVaryRegion(String requestBodyJson, String parentJboId, int index, String prompt, String maskBate64, MJAccountHeaderBO mjAccountHeaderBO){
        if (index < 0 || index > 3) {
            return null;
        }
        ++index;
        if (maskBate64 == null){
            return null;
        }
        if (prompt != null){
            prompt = filterPrompt(prompt);
        }
        return MJAPIUtil.postSubmitJobs(
                MJAPIUtil.requestSubmitJobsBodyJsonVaryRegion(requestBodyJson, parentJboId, index,prompt,maskBate64),
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getCookie(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion()
        );
    }

    /**
     * 放大接口
     * @param requestBodyJson 原请求内容
     * @param parentJobId 上级工作id
     * @param index 图片序号
     * @return JobInfo 任务信息
     */
    public static JobInfo submitJobRemix(String requestBodyJson, boolean remix, String prompt, String parentJobId, int index, MJAccountHeaderBO mjAccountHeaderBO){
        if (index < 0 || index > 3) {
            return null;
        }
        if (prompt != null){
            prompt = filterPrompt(prompt);
        }
        ++index;
        String requestSubmitJobsBodyJson = MJAPIUtil.requestSubmitJobsBodyJsonRemix(requestBodyJson, parentJobId, index,remix,prompt);
        if (requestSubmitJobsBodyJson == null){
            return null;
        }
        return MJAPIUtil.postSubmitJobs(
                requestSubmitJobsBodyJson,
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getCookie(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion()
        );
    }

    /**
     * TODO 自由变化接口：新版
     * @param oldPrompt 提示词（全）
     * @param requestBodyJson 原请求内容
     * @param parentJobId 上级工作id
     * @param index 图片序号
     * @param location 位置：1左上； 2居中； 3右下；
     * @return JobInfo 任务信息
     */
    public static JobInfo submitJobZoomNew(String oldPrompt, String requestBodyJson, Integer location, String parentJobId, int index,String scale, MJAccountHeaderBO mjAccountHeaderBO){
        List<String> scaleList = getScaleList();
        if (location == null || location < 0 || location > 3 || !scaleList.contains(scale) || index < 0 || index > 3) {
            log.error("参数错误\nrequestBodyJson{}\nlocation{}\nparentJobId{}\nindex{}\nscale{}",requestBodyJson,location,parentJobId,index,scale);
            return null;
        }
        ++index;
        //居中情况调用缩放接口
        if (location == 2){
            String requestSubmitJobsBodyJson = MJAPIUtil.requestSubmitJobsBodyJsonZoomNew(requestBodyJson, parentJobId, index,100,scale);
            if (requestSubmitJobsBodyJson == null){
                return null;
            }
            return MJAPIUtil.postSubmitJobs(
                    requestSubmitJobsBodyJson,
                    mjAccountHeaderBO.getToken(),
                    mjAccountHeaderBO.getCookie(),
                    mjAccountHeaderBO.getAppVersion(),
                    mjAccountHeaderBO.getUserAgentVersion()
            );
        }
        JSONObject jsonObject = JSONObject.parseObject(requestBodyJson);
        JSONObject parameters = jsonObject.getJSONObject("parameters");
        String ar = null;
        log.info("promptUse:{}",oldPrompt);
        Pattern pattern = Pattern.compile("--ar\\s(\\d+:\\d+)");
        Matcher matcher = pattern.matcher(oldPrompt);
        if (matcher.find()) {
            ar = matcher.group(1);
            parameters.put("ar", ar);
            jsonObject.put("parameters", parameters);
            requestBodyJson = jsonObject.toJSONString();
        }
        if (ar == null){
            return null;
        }
        log.info("ar:{}",ar);
        log.info("scale:{}",scale);
        Double fractionEnum = BMJZoomEnum.getBMJZoomEnum(ar,scale);

        String[] arParts = ar.split(":");
        int defaultW = Integer.parseInt(arParts[0]);
        int defaultH = Integer.parseInt(arParts[1]);
        String[] scaleParts = scale.split(":");
        int scaleW = Integer.parseInt(scaleParts[0]);
        int scaleH = Integer.parseInt(scaleParts[1]);
        String requestSubmitJobsBodyJson;
        int ok = 17;
        RoundingMode roundingMode = RoundingMode.DOWN;
        //上下：0为下拓展；2为上拓展；
        if (scaleW < scaleH){
            int newRatio = defaultW * scaleH;
            double newRatio2 = new BigDecimal(String.valueOf(newRatio)).divide(new BigDecimal(String.valueOf(scaleW)),ok, roundingMode).subtract(new BigDecimal(String.valueOf(defaultH))).doubleValue();
            double fraction = new BigDecimal(String.valueOf(newRatio2)).divide(new BigDecimal(String.valueOf(defaultH)),ok, roundingMode).doubleValue();
            fraction = fractionEnum == null?fraction:fractionEnum;
            if (fraction <= 0){
                return null;
            }
            requestSubmitJobsBodyJson = MJAPIUtil.requestSubmitJobsBodyJsonPanNew(requestBodyJson, parentJobId, index,(location == 1?0:2),fraction);
            log.info("上下拓展比例：{}",requestSubmitJobsBodyJson);
        }else {
            //左右：1为右拓展；3为左拓展
            int newRatio = scaleW * defaultH;
            double newRatio2 = new BigDecimal(String.valueOf(newRatio)).divide(new BigDecimal(String.valueOf(scaleH)),ok, roundingMode).subtract(new BigDecimal(String.valueOf(defaultW))).doubleValue();
            double fraction = new BigDecimal(String.valueOf(newRatio2)).divide(new BigDecimal(String.valueOf(defaultW)),ok, roundingMode).doubleValue();
//            fraction = fractionEnum == null?fraction:fractionEnum;
            if (fraction <= 0){
                return null;
            }
            requestSubmitJobsBodyJson = MJAPIUtil.requestSubmitJobsBodyJsonPanNew(requestBodyJson, parentJobId, index,(location == 1?1:3),fraction);
            log.info("左右拓展比例：{}",requestSubmitJobsBodyJson);
        }
        return MJAPIUtil.postSubmitJobs(
                requestSubmitJobsBodyJson,
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getCookie(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion()
        );
    }

    public static List<String> getScaleList() {
        List<String> stringList = new ArrayList<>();
        stringList.add("1:3");
        stringList.add("1:2");
        stringList.add("9:16");
        stringList.add("2:3");
        stringList.add("3:4");
        stringList.add("5:6");
        stringList.add("1:1");
        stringList.add("6:5");
        stringList.add("4:3");
        stringList.add("3:2");
        stringList.add("16:9");
        stringList.add("2:1");
        stringList.add("3:1");
        return stringList;
    }

    public static Result<List<JobStatusBO>> getJobStatus(List<String> jobIds, MJAccountHeaderBO mjAccountHeaderBO){
        return MJAPIUtil.getJobStatus(jobIds,mjAccountHeaderBO.getToken(), mjAccountHeaderBO.getCookie(), mjAccountHeaderBO.getAppVersion(), mjAccountHeaderBO.getUserAgentVersion());
    }

    //刷新mjAppToken操作
    public static String getMJAppToken(MJAccountHeaderBO mjAccountHeaderBO){
        return MJAPIUtil.getMJAppToken(
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion(),
                mjAccountHeaderBO.getCookie()
        );
    }

    public static MJUseTimeInfo getMJUseTime(MJAccountHeaderBO mjAccountHeaderBO){
        return MJAPIUtil.postMJUseTime(
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion(),
                mjAccountHeaderBO.getCookie()
        );
    }

    public static boolean getMJJobsCancel(MJAccountHeaderBO mjAccountHeaderBO, String jobId){
        return MJAPIUtil.getMJJobsCancel(
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion(),
                mjAccountHeaderBO.getCookie(),
                jobId
        );
    }

    public static boolean getMJJobsInfo(MJAccountHeaderBO mjAccountHeaderBO, JobNewInfoStatusDTO jobNewInfoStatusDTO){
        return MJAPIUtil.getMJJobsInfo(
                mjAccountHeaderBO.getToken(),
                mjAccountHeaderBO.getAppVersion(),
                mjAccountHeaderBO.getUserAgentVersion(),
                mjAccountHeaderBO.getCookie(),
                jobNewInfoStatusDTO
        );
    }

    public static MJGoogleTokenInfoBO getMjGoogleApiToken(String token){
        return MJAPIUtil.postTokenVersion1_13(token);
    }

    public static MJAccountHeaderBO getTextHeader(){
        MJAccountHeaderBO mjAccountHeaderBO = new MJAccountHeaderBO();
        mjAccountHeaderBO.setToken("eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..dBrVUtL0wlWsJrpT.wgo0gXZ6T0RQXxVL6lspxfFFPYCMHWYXP41fSuPbB1NdOJ_olTUhJ0IHhgwOQwlQkDyHX8PLgPoKd-X03W72cm7Lvi_2MBF59jWUYg2uJeTINhddo5s02ZeadFXRWvK-CHV1iHGj6wdJqVjle-D0MRkJp5JyUpp634pV7_io9nWG5mHbzl2dHoLL0N9kXll3clwW8tZiAv-XlZG2svq5SWa__fAE6wNMy3zT1SwblxKJUEEQDBOuwyFJcdi3ctVc1H1IUuqjFVNjdZ2ELptb7PAbVZfXyXEUv0ayLIM7Birn8Ye-lY9941Im4poOz3V7iVXSBDfhYOSDdjX62zylKGqKi2lEf9hjyZ4bst9pg2kwSrCouTB5y5thsftAUppZHDUOi2ts2-pejpWa6ljv1AdQcrtIO3OkqXdgG1gKEzLN2KZFX8rRqAp4CSqJ4v_wj0Hbz1yeUZJZbGEm-LoaVA8Ak5enbLaet_6Lodcx9erQcuh0r8RnnxHe6ZFAFFB5Iuy1el-aWmDOh5Dkj_HQoUk6847CGvN3XgspUSdf1Pw4nZ1Z6SXPTnG0oOABpIgQsx_-yDsDmPmhnXIT2PjN8cb5wOpNKaPMOu8xQGD4ikK7jADmG_VYQM53BvKRBbwXWtrB-SgcRn9nZlf87x4GmdT_2_efgJo-i7I563DdM6IC0dCgvr-NKKFCGDHt6NhsBM3JJOEMQltF2OZpJ7Z83x7IBqq0-jnZH-qaPwjkW2y7WKCHS-Xg8LSpFZRK2Wo0ZQN8GbP9EqYmP5nfwfz9s4oeC2eNfAEJjQcnAFnTcUQHAqYQWcH8Xp5P-z7iuVg5yHA9Ccl0_IJx3Y33-7S8ufMH7pMck7DL8ErzDfP8hJPZJPEeoXnBTLuV6M_7DqFt3PyL_BDAQGghGs8K7-gW1z8BU7gGqHUzXKbhH-CmGMJZDXXfOsPB2zdWjGtq8bv8REMYplrVLDBNnYSp4pVUWhfgE2aN_N8VUxQR0eJ3_VpsGNdVC-sD4Mg4W6q-D2R1Ggnzrm-IXbAdvgXzQmnJBqzlxNAjyR_1xGaBSRisLvl4QhK-O6tivOrSiHSa-pKB2rV5SDw2DbBsI1hWOtyBFhLFFOAUcSwY--Pjb0Ct7yP20JJCefgJXg5nz_9d7QqylUIJA8U.gH-1pCP7vTlU_quEgtQGrw");
        mjAccountHeaderBO.setAppVersion("1.13.3");
        mjAccountHeaderBO.setUserAgentVersion("okhttp/4.9.2");
        mjAccountHeaderBO.setCookie("__cf_bm=V32dUImBO0MVZK9XQfEeUuhJfhjAIwxSCSY00OV.Jp0-**********-1.0.1.1-6bPc6am0g2_QXFCrSqsMKU6R9ltoJojUeM.S7OQuXDwhb45PTlEAxDqWeVgStAJQNIHHrG3VkJxNvYIza0bNMg");
        return mjAccountHeaderBO;
    }

    public static String filterPrompt(String text) {
        // 定义正则表达式，匹配中文、英文大小写、中英文标点符号,空格
        String regex = "[\\u4e00-\\u9fa5a-zA-Z0-9',.，。？！；：、‘’“”《》（）【】 ]";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        // 使用StringBuilder存储过滤后的文本
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            sb.append(matcher.group());
        }
        // 将双引号替换为单引号
        return sb.toString().replaceAll("[《》（）【】\"]", "'").trim();
    }

    public static String getCfbm(String co) {
        // 使用 split 方法和 indexOf 方法提取 __cf_bm 的值
        String[] parts = co.split("; ");
        String cfBmValue = null;

        for (String part : parts) {
            if (part.startsWith("__cf_bm=")) {
                cfBmValue = part.substring("__cf_bm=".length());
                break;
            }
        }
        return cfBmValue != null ? "__cf_bm="+cfBmValue : null;
    }

}
