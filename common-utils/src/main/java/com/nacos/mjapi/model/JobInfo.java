package com.nacos.mjapi.model;


import lombok.Data;

/**
 * &#064;description:  mj操作任务信息
 */
@Data
public class JobInfo {
    private String job_id;//任务id
    private String prompt;//提示词
    private Boolean is_queued;//是否排队中
    private String job_type;// 任务类型
    private String flagsMode;//绘画速度:turbo :fast :relaxed
    private String cookie;//同步返回：cookie

    //meta 实体
    private String full_command;//提示词
    private int height;//尺寸高
    private int width;//尺寸宽
    private int batch_size;//图片数量

    private Boolean isCreditsExhausted;//时间是否已用完：true/false
    private Boolean isBannedPromptDetected;//提示词是否敏感：true/false
    private Boolean isTokenExhausted;//token 是否失效：true/false
    private Boolean isPendingModMessage;//挂起通知：1、封号通知 是否：true/false
    private Boolean isInvalidLink;//无效链接：1、通知用户 是否：true/false

    public Boolean getIsCreditsExhausted() {
        return isCreditsExhausted != null && isCreditsExhausted;
    }

    public Boolean getIsBannedPromptDetected() {
        return isBannedPromptDetected != null && isBannedPromptDetected;
    }

    public Boolean getIsTokenExhausted() {
        return isTokenExhausted != null && isTokenExhausted;
    }

    public Boolean getIsPendingModMessage() {
        return isPendingModMessage != null && isPendingModMessage;
    }

    public Boolean getIsInvalidLink() {
        return isInvalidLink != null && isInvalidLink;
    }
}
