package com.nacos.mjapi.model;


import lombok.Data;

import java.util.List;

/**
 * &#064;description:  mj操作任务信息
 */
@Data
public class JobStatusBO {
    private String jobId; //图片任务id
    private String userId; //mj用户id
    private String username; //mj用户名称
    private String currentStatus; // 运行状态
    private List<String> imagePaths; // 图片路径
    private String fullCommand; // 完整命令
    private String jobType; // 操作类型
    private Integer eventWidth; // 图片宽度
    private Integer eventHeight; // 图片高度
    private Integer batchSize; // 图片数量
    private List<String> jobStatusRunningImgs; // 渐显图片信息
    private String jobRunningSchedule;//渐显图片进度%
}
