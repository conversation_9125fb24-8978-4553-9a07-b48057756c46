package com.nacos.utils;

import com.aliyun.oss.*;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.PutObjectRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.config.OssClientConfig;
import com.nacos.model.OssParamBO;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * oss工具类
 */
@Slf4j
public class OSSUtils {

    private static OSS oss;
    public static OSS initClient(String endpoint, String accessKeyId, String secretAccessKey) {
        if (oss == null){
            return new OSSClientBuilder().build(endpoint, accessKeyId, secretAccessKey);
        }
        return oss;
    }
    public static void shutdown() {
        if (oss != null) {
            oss.shutdown(); // 关闭OSSClient。
            oss = null;
        }
    }

    /**
     * 上传base64图片到oss
     * @param endpoint 文件上传地址
     * @param accessKeyId ossKey
     * @param secretAccessKey  ossSecretKey
     * @param imageBate64  bate64文件
     * @param folder  文件存储文件夹序号
     * @return
     */
    public static String uploadBase64(String endpoint, String accessKeyId, String secretAccessKey,
                                      String bucketName, String accessPath, String imageBate64, String fileName, Integer folder) {
        String imgUrl = fileName.concat(OssClientConfig.FILE_SUFFIX);
        String objectName = OssClientConfig.getPath(folder).concat(imgUrl);
        try {
            byte[] imageBytes = Base64.getDecoder().decode(imageBate64); // 将Base64编码的图片数据转为字节数组
            InputStream inputStream = new ByteArrayInputStream(imageBytes); // 将字节数组转为输入流
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName,objectName, inputStream);
            OSSUtils.initClient(endpoint, accessKeyId, secretAccessKey).putObject(putObjectRequest); // 上传文件。
            return accessPath.concat(objectName);
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            OSSUtils.shutdown();
        }
        return null;
    }

    /**
     * inputStream流上传
     * @param endpoint 文件上传地址
     * @param accessKeyId ossKey
     * @param secretAccessKey ossSecretKey
     * @param inputStream bate64文件
     * @param folder 文件存储文件夹序号
     * @return
     * @throws Exception
     */
    public static OssParamBO uploadStream(String endpoint, String accessKeyId, String secretAccessKey,
                                      String bucketName, String accessPath, InputStream inputStream, String fileName, Integer folder) throws Exception{
        OssParamBO ossParamBO = new OssParamBO();
        String imgUrl = fileName.concat(OssClientConfig.FILE_SUFFIX);
        String objectName = OssClientConfig.getPath(folder).concat(imgUrl);
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName,objectName, inputStream);
            OSSUtils.initClient(endpoint, accessKeyId, secretAccessKey).putObject(putObjectRequest); // 上传文件。
            ossParamBO.setImageUrl(accessPath.concat(objectName));
            long startTime = System.currentTimeMillis();
            String resBody = OSSUtils.getImageInfo(endpoint, accessKeyId, secretAccessKey, bucketName, objectName);
            long endTime = System.currentTimeMillis();
            log.info("获取图片宽高耗时时长= " + (endTime - startTime));
            if (resBody != null || StringUtils.isNotEmpty(resBody)) {
                JsonNode jsonNode = new ObjectMapper().readTree(resBody);
                ossParamBO.setImageWidth(jsonNode.get("ImageWidth").get("value").asText());
                ossParamBO.setImageHeight(jsonNode.get("ImageHeight").get("value").asText());
                ossParamBO.setFileSize(jsonNode.get("FileSize").get("value").asText());
            }
            return ossParamBO;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        } catch (Throwable te) {
            log.error("Author:\n" +
                    "Josh Bloch (Added exception chaining and programmatic access to stack trace in 1.4.)\n" +
                    "jls\n" +
                    "11.2 Compile-Time Checking of Exceptions");
            log.error("Error Message:" + te.getMessage());
        } finally {
            OSSUtils.shutdown();
        }
        return null;
    }

    /**
     * 网络url上传OSS
     * @param
     * @return
     * @throws Exception
     */
    public static OssParamBO uploadURL(String endpoint, String accessKeyId, String secretAccessKey,
                                   String bucketName, String imageUrl, String fileName, Integer folder) {
        OssParamBO ossParamBO = new OssParamBO();
        String objectName = OssClientConfig.getPath(folder).concat(fileName.concat(OssClientConfig.FILE_SUFFIX));
        try {
            InputStream inputStream = new URL(imageUrl).openStream();
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName,objectName, inputStream);
            OSSUtils.initClient(endpoint, accessKeyId, secretAccessKey).putObject(putObjectRequest); // 上传文件。
            ossParamBO.setImageUrl("/".concat(objectName));
            long startTime = System.currentTimeMillis();
            String resBody = OSSUtils.getImageInfo(endpoint, accessKeyId, secretAccessKey, bucketName, objectName);
            long endTime = System.currentTimeMillis();
            log.info("获取图片宽高耗时时长= " + (endTime - startTime));
            if (resBody != null || StringUtils.isNotEmpty(resBody)) {
                JsonNode jsonNode = new ObjectMapper().readTree(resBody);
                ossParamBO.setImageWidth(jsonNode.get("ImageWidth").get("value").asText());
                ossParamBO.setImageHeight(jsonNode.get("ImageHeight").get("value").asText());
                ossParamBO.setFileSize(jsonNode.get("FileSize").get("value").asText());
            }
            return ossParamBO;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (Throwable ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
            log.error("！！！！！！！！Error StackTrace:" + ce.getStackTrace());
        } finally {
            OSSUtils.shutdown();
        }
        return null;
    }

    /**
     * SDK获取图片宽高信息
     * @param endpoint  文件上传地址
     * @param accessKeyId  ossKey
     * @param secretAccessKey  ossSecretKey
     * @param bucketName  oss桶名称
     * @param objectName 文件名称（列如 /mj/xxx.png）
     * @return
     * @throws Throwable
     */
    public static String getImageInfo(String endpoint, String accessKeyId, String secretAccessKey,
                                    String bucketName, String objectName) throws Throwable {
        OSS ossClient = OSSUtils.initClient(endpoint, accessKeyId, secretAccessKey);
        try {
            String style = "image/info";  // 获取图片信息。
            Date expiration = new Date(new Date().getTime() + 1000 * 60 * 10 );    // 指定签名URL过期时间为10分钟。
            GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(bucketName, objectName, HttpMethod.GET);
            req.setExpiration(expiration);
            req.setProcess(style);
            URL signedUrl = ossClient.generatePresignedUrl(req);
            HttpURLConnection connection = (HttpURLConnection) signedUrl.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("获取图片宽高信息 Response Code: ", responseCode);
            StringBuilder response = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                log.info("获取图片宽高信息 Response Body: {}", response.toString());
            }
            connection.disconnect();
            return response != null ? response.toString() : null;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }

    /**
     * 上传base64图片到oss
     * @param endpoint 文件上传地址
     * @param accessKeyId ossKey
     * @param secretAccessKey  ossSecretKey
     * @param imageBate64  bate64文件
     * @param folder  文件存储文件夹序号
     * @return
     */
    public static OssParamBO uploadBase64(String endpoint, String accessKeyId, String secretAccessKey,
                                      String bucketName, String imageBate64, String fileName, Integer folder) {
        OssParamBO ossParamBO = new OssParamBO();
        String objectName = OssClientConfig.getPath(folder).concat(fileName.concat(OssClientConfig.FILE_SUFFIX));
        try {
            byte[] imageBytes = Base64.getDecoder().decode(imageBate64); // 将Base64编码的图片数据转为字节数组
            InputStream inputStream = new ByteArrayInputStream(imageBytes); // 将字节数组转为输入流
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName,objectName, inputStream);
            OSSUtils.initClient(endpoint, accessKeyId, secretAccessKey).putObject(putObjectRequest); // 上传文件。
            ossParamBO.setImageUrl("/".concat(objectName));
            long startTime = System.currentTimeMillis();
            String resBody = OSSUtils.getImageInfo(endpoint, accessKeyId, secretAccessKey, bucketName, objectName);
            long endTime = System.currentTimeMillis();
            log.info("获取图片宽高耗时时长= " + (endTime - startTime));
            if (resBody != null || StringUtils.isNotEmpty(resBody)) {
                JsonNode jsonNode = new ObjectMapper().readTree(resBody);
                ossParamBO.setImageWidth(jsonNode.get("ImageWidth").get("value").asText());
                ossParamBO.setImageHeight(jsonNode.get("ImageHeight").get("value").asText());
                ossParamBO.setFileSize(jsonNode.get("FileSize").get("value").asText());
            }
            return ossParamBO;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        } finally {
            OSSUtils.shutdown();
        }
        return null;
    }


    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    // 这里只是以ocr为例，其他能力请使用相应类目的包下面的Client类
    public static com.aliyun.imageenhan20190930.Client  createClient(String endpoint,String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        config.endpoint = endpoint;
        return new com.aliyun.imageenhan20190930.Client(config);
    }


}
