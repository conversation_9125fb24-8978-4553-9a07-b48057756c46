<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- 强制继承父项目 -->
    <parent>
        <groupId>com.nacos</groupId>
        <artifactId>ddsj-server-nacos</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>nacos-user-server</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>nacos-user-server</name>
    <description>用户浏览器微服务</description>
    <packaging>jar</packaging>

    <properties>
        <fastjson.version>2.0.23</fastjson.version>
    </properties>

    <dependencies>
        <!-- yaml 工具包-->
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>

        <!-- 注册中心客户端 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <!-- 排除漏洞内容版本 -->
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 配置中心客户端 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.nacos</groupId>
            <artifactId>common-business-plus</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

       <dependency>
           <groupId>com.nacos</groupId>
           <artifactId>common-utils</artifactId>
           <exclusions>
               <exclusion>
                   <groupId>org.json</groupId>
                   <artifactId>json</artifactId>
               </exclusion>
           </exclusions>
       </dependency>

        <!-- 日志配置 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.4.14</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>1.4.14</version>
        </dependency>
        <!-- 日志配置结束 -->

        <!-- 二维码生成工具 -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.1</version>
        </dependency>

        <!-- openapi 核心组件每个子服务都要有-->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-webmvc-core</artifactId>
            <version>1.7.0</version>
        </dependency>

        <!-- socket wss 使用开始 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <!-- socket wss 使用结束 -->

        <!-- 微信支付 v3 使用开始-->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
            <version>0.2.12</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
        </dependency>
        <!-- 微信支付 v3 使用结束-->

        <!-- 消息推送 -->
        <dependency>
            <groupId>cn.bestwu</groupId>
            <artifactId>umeng-push</artifactId>
            <version>1.5.5</version>
            <type>pom</type>
        </dependency>

        <!-- 手机号验证开始 -->
        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>8.12.41</version> <!-- 请根据最新版本进行替换 -->
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
            <scope>compile</scope>
        </dependency>
        <!-- 手机号验证结束 -->

        <!-- 支付宝 对接-->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-easysdk</artifactId>
            <version>2.2.3</version>
        </dependency>

        <!-- 苹果支付-->
        <dependency>
            <groupId>com.apple.itunes.storekit</groupId>
            <artifactId>app-store-server-library</artifactId>
            <version>2.0.0</version>
        </dependency>


        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-mock</artifactId>
            <version>2.0.8</version>
        </dependency>

        <!-- 阿里云验证码SDK -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>captcha20230305</artifactId>
            <version>1.1.3</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
