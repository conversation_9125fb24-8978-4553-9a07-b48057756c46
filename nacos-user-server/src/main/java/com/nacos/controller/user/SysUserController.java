package com.nacos.controller.user;


import com.business.db.model.dto.SysReportRecordDTO;
import com.business.db.model.po.SysReportTypePO;
import com.nacos.result.Result;
import com.nacos.service.SysUserService;
import com.nacos.utils.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;


@Tag(name = "系统用户操作接口", description = "用户注销、用户举报")
@RestController
@RequestMapping("/sysUser")
@Log4j2
public class SysUserController {

    @Resource
    private SysUserService sysUserService;

    @Operation(summary = "用户注销")
    @PostMapping(value = "/logoff",name = "用户注销，手机号验证")
    public Result<Object> logoff(HashMap<String, Object> map) {
        String verificationCode = (String) map.get("verificationCode");
        if (verificationCode == null || verificationCode.isEmpty()) {
            return Result.ERROR("验证码不能为空");
        }
        return sysUserService.logoff(JwtUtil.getUserId(), verificationCode);
    }

    @Operation(summary = "用户举报类型")
    @GetMapping(value = "/reportTypes/{languageTagId}", name = "用户举报类型")
    public Result<List<SysReportTypePO>> selectReportTypeList(@PathVariable Long languageTagId) {
        return sysUserService.reportTypeList(languageTagId);
    }

    @Operation(summary = "用户举报")
    @PostMapping(value = "/report",name = "用户举报")
    public Result<Object> reportSubmit(@Validated @RequestBody SysReportRecordDTO sysReportRecordDTO) {
        return sysUserService.reportSubmit(sysReportRecordDTO);
    }

}
