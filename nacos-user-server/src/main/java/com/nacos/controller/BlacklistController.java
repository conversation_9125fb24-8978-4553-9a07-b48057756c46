package com.nacos.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.BlackQueryDTO;
import com.business.db.model.vo.BlacklistVO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;
import com.nacos.service.IBlackService;
import io.swagger.v3.oas.annotations.Operation;
import com.business.db.model.po.BlacklistPO;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@Tag(name = "黑名单API", description = "黑名单")
@RestController
@RequestMapping("/block")
public class BlacklistController {

    @Resource
    private IBlackService blackService;

    /**
     * 黑名单分页查询
     * @param dto
     * @return
     */
    @Operation(summary = "查询黑名单列表分页")
    @RequestMapping(value = "/queryPage",name = "查询黑名单列表分页", method = RequestMethod.POST)
    public Result<Page<BlacklistVO>>  queryPage(@RequestBody BlackQueryDTO dto) {
        return blackService.queryPage(dto);
    }

    /**
     * 黑名单新增
     * @param blacklistPO
     * @return
     */
    @Operation(summary = "新增黑名单")
    @RequestMapping(value = "/add",name = "新增黑名单", method = RequestMethod.POST)
    public Result<?> add(@Validated @RequestBody BlacklistPO blacklistPO) {
        return blackService.add(blacklistPO);
    }

    /**
     * 黑名单修改
     * @param blacklistPO
     * @return
     */
    @Operation(summary = "编辑黑名单")
    @RequestMapping(value = "/update",name = "编辑黑名单", method = RequestMethod.POST)
    public Result<?>  update(@Validated @RequestBody BlacklistPO blacklistPO) {
        return blackService.update(blacklistPO);
    }

    /**
     * 黑名单删除
     * @param params
     * @return
     */
    @Operation(summary = "删除黑名单")
    @RequestMapping(value = "/delete",name = "删除黑名单", method = RequestMethod.POST)
    public Result<?>  delete(@Validated @RequestBody BaseDeleteEntity params) {
        return blackService.delete(params);
    }
}
