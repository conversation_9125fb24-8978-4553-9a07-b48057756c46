package com.nacos.controller;

import com.nacos.result.Result;
import com.nacos.service.RateLimitConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 限流配置管理接口
 */
@Tag(name = "限流配置API", description = "限流配置管理")
@RestController
@RequestMapping("/ratelimit/config")
@Slf4j
public class RateLimitConfigController {
    
    @Resource
    private RateLimitConfigService configService;
    
    /**
     * 获取所有限流配置
     * @return 配置Map
     */
    @Operation(summary = "获取所有限流配置")
    @GetMapping("/all")
    public Result<Map<String, Integer>> getAllConfigs() {
        Map<String, Integer> configs = configService.getAllConfigs();
        return Result.SUCCESS(configs);
    }
    
    /**
     * 获取指定配置值
     * @param key 配置键
     * @return 配置值
     */
    @Operation(summary = "获取指定配置值")
    @GetMapping("/get")
    public Result<Integer> getConfigValue(@RequestParam String key) {
        if (key == null || key.isEmpty()) {
            return Result.ERROR("配置键不能为空");
        }
        
        Integer value = configService.getConfigValue(key);
        if (value == null) {
            return Result.ERROR("配置不存在");
        }
        
        return Result.SUCCESS(value);
    }
    
    /**
     * 更新配置值
     * @param key 配置键
     * @param value 配置值
     * @return 更新结果
     */
    @Operation(summary = "更新配置值")
    @PostMapping("/update")
    public Result<Boolean> updateConfigValue(@RequestParam String key, @RequestParam Integer value) {
        if (key == null || key.isEmpty()) {
            return Result.ERROR("配置键不能为空");
        }
        
        if (value == null || value < 0) {
            return Result.ERROR("配置值无效");
        }
        
        boolean updated = configService.updateConfigValue(key, value);
        return Result.SUCCESS(updated);
    }
    
    /**
     * 刷新配置缓存
     * @return 结果
     */
    @Operation(summary = "刷新配置缓存")
    @PostMapping("/refresh")
    public Result<Void> refreshConfigCache() {
        configService.refreshConfigCache();
        return Result.SUCCESS();
    }
} 