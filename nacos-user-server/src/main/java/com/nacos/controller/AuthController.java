package com.nacos.controller;

import com.business.config.AvoidRepeatRequest;
import com.business.db.model.LoginToken;
import com.business.db.model.bo.UVerifyResBO;
import com.business.db.model.dto.*;
import com.business.db.model.vo.UserAuthVO;
import com.business.enums.BAccountEnum;
import com.business.enums.BRedisKeyEnum;
import com.nacos.enums.CodeTypeEnum;
import com.nacos.enums.UserInfoEnum;
import com.nacos.exception.E;
import com.nacos.exception.IBusinessException;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.AuthService;
import com.nacos.utils.JGuangUtil;
import com.nacos.utils.JwtUtil;
import com.nacos.utils.wx.WXLoginUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/auth")
@Schema(title = "登录Api", description = "无需鉴权")
@Transactional(rollbackFor = E.class)
public class AuthController {

    private Instant lastRequestTime;
    @Resource
    private AuthService authService;

    //+++++++++++++++++++++++++++++++++++++PC登录相关接口===开始++++++++++++++++++++++++++++++++++++++++++++++++
    //@AvoidRepeatRequest( msg = "请勿短时间连续登录")
    @Operation(summary = "pc手机验证码登录/注册")
    @PostMapping(value = "/login", name = "用户登录注册")
    public Result<UserAuthVO> userPhoneLogin(@RequestBody UserLoginDTO userLogin) throws Exception {
        AuthController requestHandler = new AuthController();
        if(!requestHandler.canProcessRequest()) {
            return Result.ERROR("请求过于频繁，请稍后再试！");
        }
        // 校验是否官网账号
        BAccountEnum accountEnum = BAccountEnum.getBAccountEnum(userLogin.getMobile(), userLogin.getVerificationCode());
        if (accountEnum != null) {
            userLogin.setVerificationCode(accountEnum.getAuthCode());
            //设置十分钟
            RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.SEND_REGISTER_CODE,userLogin.getMobile()), accountEnum.getAuthCode(), 10, TimeUnit.MINUTES);
        }
        return authService.userPhoneLogin(userLogin);
    }

    @Operation(summary = "pc微信扫码二维码参数")
    @PostMapping(value = "/wxParams", name = "二维码参数接口")
    public Result<?> getWxParams(@RequestBody WxLoginDTO dto) {
        log.info("wxParams: {}", dto);
        //登陆调用
        if (CodeTypeEnum.LOGIN.getType().equals(dto.getType())){
            return Result.SUCCESS(WXLoginUtil.getWxCodeMap(dto.getType(),null));
        }
        //绑定调用
        if (CodeTypeEnum.BIND.getType().equals(dto.getType())){
            return Result.SUCCESS(WXLoginUtil.getWxCodeMap(dto.getType(),String.valueOf(JwtUtil.getUserId())));
        }
        return Result.ERROR("登录类型错误");
    }

    @Operation(summary = "pc微信扫码登录回调方法")
    @GetMapping("/login/callback")
    @ResponseBody
    public void callBack(String code, String state, String type, Long userId, HttpServletRequest request) throws IBusinessException {
        log.info("扫码回调接口 callBack: {}",request);
        if (CodeTypeEnum.LOGIN.getType().equals(type)){
            //微信扫码登陆操作
            authService.wxLoginCallBack(code, state, type, userId, request);
            return;
        }
        //用户绑定微信
        if(CodeTypeEnum.BIND.getType().equals(type)) {
            authService.bindWxLogin(code, state, type, userId, request);
        }
    }

    @Operation(summary = "pc微信扫码之后绑定手机号", description = "pc微信扫码之后绑定手机号")
    @PostMapping(value = "/pcBind", name = "微信扫码注册关注后绑定手机号")
    public Result<UserAuthVO> bindPhone(@Validated @RequestBody UserBindMobileDTO dto) throws IBusinessException {
        dto.setAaaaaaa("pclogin");
        //dto.setLoginType(UserInfoEnum.LOGIN_TYPE_WEB.getIntValue());
        return authService.bindPhone(dto);
    }
    //+++++++++++++++++++++++++++++++++++++PC登录相关接口===结束++++++++++++++++++++++++++++++++++++++++++++++++
    //+++++++++++++++++++++++++++++++++++++APP登录相关接口===开始++++++++++++++++++++++++++++++++++++++++++++++++

    //@AvoidRepeatRequest( msg = "请勿短时间连续登录")
    @Operation(summary = "App手机短信验证码登录")
    @PostMapping(value = "/smsLogin", name = "用户登录注册")
    public Result<UserAuthVO> smsLogin(@RequestBody UserLoginDTO userLogin) throws Exception{
        AuthController requestHandler = new AuthController();
        if(!requestHandler.canProcessRequest()) {
            return Result.ERROR("请求过于频繁，请稍后再试！");
        }
        // 校验是否官网账号
        BAccountEnum accountEnum = BAccountEnum.getBAccountEnum(userLogin.getMobile(), userLogin.getVerificationCode());
        if (accountEnum != null) {
            userLogin.setVerificationCode(accountEnum.getAuthCode());
            //设置十分钟
            RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.SEND_REGISTER_CODE,userLogin.getMobile()), accountEnum.getAuthCode(), 10, TimeUnit.MINUTES);
        }
        userLogin.setLoginType(UserInfoEnum.LOGIN_TYPE_APP.getIntValue());
        return authService.userPhoneLogin(userLogin);
    }

    @Operation(summary = "App微信授权登录")
    @GetMapping("/wxLogin")
    public Result<UserAuthVO> wxLogin(@RequestParam("code") String code,@RequestParam("deviceToken") String deviceToken) throws IBusinessException {
        return authService.wxAppLogin(code, deviceToken);
    }

    @Operation(summary = "app登录成功绑定手机号", description = "app登录成功绑定手机号")
    @PostMapping(value = "/bind", name = "微信扫码注册关注后绑定手机号")
    public Result<UserAuthVO> bind(@Validated @RequestBody UserBindMobileDTO dto, HttpServletRequest request) throws IBusinessException {
        dto.setAaaaaaa("applogin");
        dto.setLoginType(UserInfoEnum.LOGIN_TYPE_APP.getIntValue());
        return authService.bindPhone(dto);
    }
    //+++++++++++++++++++++++++++++++++++++APP登录相关接口===结束++++++++++++++++++++++++++++++++++++++++++++++++

    @Operation(summary = "谷歌授权登录")
    @PostMapping("/googleLogin")
    public Result<UserAuthVO> googleLogin(@RequestBody GgAndApLoginDTO googleLoginDTO) throws IBusinessException {
        return authService.googleLogin(googleLoginDTO);
    }

    @Operation(summary = "苹果授权登录")
    @PostMapping("/appleLogin")
    public Result<UserAuthVO> appleLogin(@RequestBody GgAndApLoginDTO googleLoginDTO) throws IBusinessException {
        return authService.appleLogin(googleLoginDTO);
    }

    @Operation(summary = "退出登录", description = "退出登录")
    @PostMapping(value = "/loginOut", name = "退出登录")
    public Result<Object> loginOut(){
        return authService.loginOut();
    }

    @Operation(summary = "PC/APP 变更手机号", description = "PC/APP 变更手机号")
    @PostMapping(value = "/changeMobile", name = "变更手机号")
    public Result<Object> alterPhone(@Validated @RequestBody UserBindMobileDTO dto){
        return authService.alterPhone(dto);
    }

    @Operation(summary = "解绑微信API", description = "解绑微信API")
    @PostMapping(value = "/wxUnbind", name = "解除微信绑定")
    public Result<Object> wxUnbind(){
        return authService.wxUnbind();
    }

    @Operation(summary = "校验手机号是否存在", description = "校验手机号是否存在")
    @PostMapping(value = "/verifyMobile", name = "校验手机号是否存在")
    public Result<Object> verifyPhone(@RequestBody UserLoginDTO userLoginDTO){
        return authService.verifyPhone(userLoginDTO.getMobile());
    }

    @Operation(summary = "app友盟一键登陆", description = "友盟一键登陆接口")
    @PostMapping(value = "/uVerifyLogin", name = "友盟一键登陆Api")
    public Result<UserAuthVO> findTokenByMobileNo(@Validated @RequestBody UVerifyLoginDTO uVerifyLoginDTO) throws InvalidKeyException, NoSuchAlgorithmException, IBusinessException {
        UVerifyResBO uVerifyResBO = authService.findTokenByMobileNo(uVerifyLoginDTO);
        if (uVerifyResBO == null || !uVerifyResBO.getSuccess() || uVerifyResBO.getData() == null) {
            return Result.ERROR("一键登陆失败");
        }
        UserLoginDTO userLogin = new UserLoginDTO();
        userLogin.setMobile(uVerifyResBO.getData().getMobile());
        userLogin.setDeviceToken(uVerifyLoginDTO.getDeviceToken());
        userLogin.setVerificationCode("1234");
        userLogin.setLoginType(uVerifyLoginDTO.getLoginType());
        RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.SEND_REGISTER_CODE,userLogin.getMobile()), "1234", 10,TimeUnit.MINUTES);
        return authService.userPhoneLogin(userLogin);
    }

    @Operation(summary = "app极光一键登陆号登录", description = "App极光一键获取手机号登录")
    @AvoidRepeatRequest( msg = "请勿短时间连续发送")
    @PostMapping(value = "/auroraLogin", name = "极光一键登录返回手机号")
    public Result<UserAuthVO> getAuroraGetPhone(@Validated @RequestBody LoginToken loginToken) throws Exception {
        String phoneNo = JGuangUtil.loginTokenVerify(loginToken.getLoginToken());
        if (phoneNo == null || phoneNo.isEmpty()) {
            return Result.ERROR_LOGIN("获取一键登录的手机号码失败");
        }
        System.out.println("手机号："+phoneNo);

        UserLoginDTO userLogin = new UserLoginDTO();
        userLogin.setMobile(phoneNo);
        userLogin.setDeviceToken(loginToken.getDeviceToken());
        userLogin.setVerificationCode("1234");
        userLogin.setLoginType(loginToken.getLoginType());
        RedisUtil.setValueSeconds(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.SEND_REGISTER_CODE,userLogin.getMobile()), "1234", 10,TimeUnit.MINUTES);
        return authService.userPhoneLogin(userLogin);
    }

    @Operation(summary = "苹果审核专用开关")
    @GetMapping("/appleAuditSwitch")
    public Result<Boolean> appleAuditSwitch(String version) throws IBusinessException {
        return authService.appleAuditSwitch(version);
    }

    // 重复提交的处理
    public boolean canProcessRequest() {
        Instant currentTime = Instant.now();
        // 检查上次请求时间是否为空或与当前时间相隔超过30秒
        if (lastRequestTime == null || Duration.between(lastRequestTime, currentTime).getSeconds() > 30) {
            // 允许处理请求
            lastRequestTime = currentTime;
            return true;
        } else {
            // 请求间隔不足30秒，不允许处理请求
            return false;
        }
    }
}
