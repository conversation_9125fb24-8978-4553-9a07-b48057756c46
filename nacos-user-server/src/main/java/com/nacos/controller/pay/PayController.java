package com.nacos.controller.pay;


import com.alibaba.fastjson2.JSONObject;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;
import com.nacos.service.IPayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "支付相关接口", description = "支付相关接口")
@RestController
@RequestMapping("/pay")
@Slf4j(topic = "PayController")
public class PayController {

    /* 支付服务 */
    @Resource
    private IPayService payService;

    @PostMapping(value = "/iosTourist/check", name = "ios游客充值验证")
    @Operation(summary = "ios应用内更新验证", description = "ios游客充值验证")
    public Result<Object> iosTouristCheck(@RequestBody JSONObject jsonObject) throws IBusinessException {
        log.info("ios应用内更新验证:{}", jsonObject);
        return payService.iosTouristCheck(jsonObject);
    }

    @PostMapping(value = "/iosIap/check", name = "ios应用内更新验证")
    @Operation(summary = "ios应用内更新验证", description = "ios应用内更新验证")
    public Result<Object> iosIapCheck(@RequestBody JSONObject jsonObject) throws IBusinessException {
        log.info("ios应用内更新验证:{}", jsonObject);
        return payService.iosIapCheck(jsonObject);
    }

    @PostMapping(value = "/iosIapCheckTouristPay", name = "ios应用内游客刷新接口验证")
    @Operation(summary = "ios应用内游客刷新接口验证", description = "ios应用内游客刷新接口验证")
    public Result<Object> iosIapCheckTouristPay(@RequestBody JSONObject jsonObject) throws IBusinessException {
        log.info("ios应用内游客刷新接口验证:{}", jsonObject);
        return payService.iosIapCheckTouristPay(jsonObject);
    }

    @PostMapping(value = "/iosIapCheckLogs", name = "ios应用内游客刷新接口验证")
    @Operation(summary = "ios应用内游客刷新接口验证", description = "ios应用内游客刷新接口验证")
    public Result<Object> iosIapCheckLogs(@RequestBody JSONObject jsonObject) {
        log.info("苹果支付登记：iosIapCheckLogs:{}",jsonObject);
        return Result.SUCCESS();
    }

    /**
     * @param :TransactionID订单号
     * @param :receipt订单加密收据
     * @Description: Ios客户端内购支付
     */
    @RequestMapping("/applePay")
    public void doIosAppRequestV2(HttpServletResponse response, HttpServletRequest request) {
//    public void doIosAppRequestV2(@RequestParam("receipt") String receipt, @RequestParam("TransactionID") String TransactionID) {
        payService.doIosAppRequestV2(response, request);
    }



}
