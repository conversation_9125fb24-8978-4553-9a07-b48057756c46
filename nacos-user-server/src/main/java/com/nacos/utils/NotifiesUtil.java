package com.nacos.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.business.db.model.po.SysNotificationPO;
import com.nacos.config.message.YouMengMessage;
import com.nacos.service.IUserService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NotifiesUtil {

    public static void sendNotification(int pushType, Long userId, SysNotificationPO notificationPO, IUserService userService) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", pushType);
        jsonObject.put("userId", userId);
        jsonObject.put("object", JSON.toJSONString(notificationPO));
        try {
            YouMengMessage.pushMessage(jsonObject.toString(), userService);
        } catch (Exception e) {
            log.error("NotifiesUtil 消息推送失败", e);
        }
    }

}
