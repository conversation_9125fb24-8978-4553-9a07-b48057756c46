package com.nacos.utils.wxpay;

import com.baomidou.mybatisplus.core.toolkit.SystemClock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 业务流水号生成器，固定为23个数字字符，如：22070908371818823233395
 * <p>
 * 15个数字字符时间戳+8个数字字符标识[26位bit(3位数据中心位(最大7个)+11位机器标识位数(最大2047)+12位序列号(最大4095))
 * </p>
 * 新的毫秒"sequence"等于1到1000的随机数,4095-1000=3095,
 * 故单机排除机器其他性能因数，理论上一秒钟可生成3091905(3095*999)个流水号
 * 同一个数据中心同一个业务服务不可超过最大2047个业务服务实例数.
 */
public class SequenceNoUtils {
    private static final Logger logger = LoggerFactory.getLogger(SequenceNoUtils.class);
    /**
     * 时间搓格式化
     */
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyMMddHHmmssSSS");

    /**
     * 数据中心识位数
     */
    private final long dataCenterIdBits = 3L;

    /**
     * 机器标识位数
     */
    private final long workerIdBits = 11L;

    private long sequence = 0L;

    private long lastTimestamp = -1L;

    /**
     * 当前数据中心
     */
    private final long dataCenterId;

    /**
     * 当前机器节点
     */
    private final long workerId;

    private SequenceNoUtils() {
        // 最大数据中心数
        long maxDataCenterId = ~(-1L << dataCenterIdBits);
        this.dataCenterId = getDataCenterId(maxDataCenterId);

        // 最大机器节点数
        long maxWorkerId = ~(-1L << workerIdBits);
        this.workerId = getMaxWorkerId(dataCenterId, maxWorkerId);
    }

    /**
     * 获取流水号
     *
     * @return code
     */
    public static String generateNo() {
        return getSingleton().nextCode();
    }

    /**
     * 获取流水号
     *
     * @return code
     */
    public static String generateNo(String businessCode) {
        return getSingleton().nextBusinessCode(businessCode);
    }

    /**
     * 单例模式-双重校验锁
     *
     * @return sq
     */
    private static SequenceNoUtils getSingleton() {
        return SingletonHolder.SINGLETON;
    }

    private static final class SingletonHolder {
        static final SequenceNoUtils SINGLETON = new SequenceNoUtils();
    }

    /**
     * next code
     *
     * @return code
     */
    private synchronized String nextCode() {
        long timestamp = timeGen();
        //闰秒
        if (timestamp < lastTimestamp) {
            long offset = lastTimestamp - timestamp;
            if (offset <= 5) {
                try {
                    wait(offset << 1);
                    timestamp = timeGen();
                    if (timestamp < lastTimestamp) {
                        throw new RuntimeException(String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", offset));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else {
                throw new RuntimeException(String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", offset));
            }
        }

        /**
         * 毫秒内自增位
         */
        /**
         * 数字标识位数
         */
        long numberBitsMast = 26L;
        long sequenceBits = numberBitsMast - dataCenterIdBits - workerIdBits;
        if (lastTimestamp == timestamp) {
            // 相同毫秒内，序列号自增
            /**
             * 最大序列号
             */
            long sequenceMask = ~(-1L << sequenceBits);
            sequence = (sequence + 1) & sequenceMask;
            if (sequence == 0) {
                // 同一毫秒的序列数已经达到最大
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            // 新增的毫秒，序列号置为 1 - 1000 随机数
            sequence = ThreadLocalRandom.current().nextLong(1, 1000);
        }

        lastTimestamp = timestamp;

        // 时间戳格式化 + (数据中心标识 | 机器标识部分 | 序列号部分)
        long dataCenterIdShift = sequenceBits + workerIdBits;
        /**
         * 8个数字字符标识段左补齐
         */
        String numberFullLeft = "%08d";
        return TIME_FORMATTER.format(LocalDateTime.ofInstant(new Date(timestamp).toInstant(), ZoneId.systemDefault()))
                // 左补齐
                + String.format(numberFullLeft, ((dataCenterId << dataCenterIdShift)
                | (workerId << sequenceBits)
                | sequence));
    }

    /**
     * next code
     *
     * @return code
     */
    private synchronized String nextBusinessCode(String businessCode) {
        long timestamp = timeGen();
        //闰秒
        if (timestamp < lastTimestamp) {
            long offset = lastTimestamp - timestamp;
            if (offset <= 5) {
                try {
                    wait(offset << 1);
                    timestamp = timeGen();
                    if (timestamp < lastTimestamp) {
                        throw new RuntimeException(String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", offset));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else {
                throw new RuntimeException(String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", offset));
            }
        }

        /**
         * 毫秒内自增位
         */
        /**
         * 数字标识位数
         */
        long numberBitsMast = 26L;
        long sequenceBits = numberBitsMast - dataCenterIdBits - workerIdBits;
        if (lastTimestamp == timestamp) {
            // 相同毫秒内，序列号自增
            /**
             * 最大序列号
             */
            long sequenceMask = ~(-1L << sequenceBits);
            sequence = (sequence + 1) & sequenceMask;
            if (sequence == 0) {
                // 同一毫秒的序列数已经达到最大
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            // 新增的毫秒，序列号置为 1 - 1000 随机数
            sequence = ThreadLocalRandom.current().nextLong(1, 1000);
        }

        lastTimestamp = timestamp;

        // 时间戳格式化 + (数据中心标识 | 机器标识部分 | 序列号部分)
        long dataCenterIdShift = sequenceBits + workerIdBits;
        /**
         * 6个数字字符标识段左补齐
         */
        String numberFullLeft = "%06d";
        return TIME_FORMATTER.format(LocalDateTime.ofInstant(new Date(timestamp).toInstant(), ZoneId.systemDefault()))
                // 左补齐
                + businessCode // 插入2位业务标识
                + String.format(numberFullLeft, ((dataCenterId << dataCenterIdShift)
                | (workerId << sequenceBits)
                | sequence));
    }

    /**
     * 数据标识id部分
     */
    private static long getDataCenterId(long maxDataCenterId) {
        long id = 0L;
        try {
            InetAddress ip = InetAddress.getLocalHost();
            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            if (network == null) {
                id = 1L;
            } else {
                byte[] mac = network.getHardwareAddress();
                if (null != mac) {
                    id = ((0x000000FF & (long) mac[mac.length - 2]) | (0x0000FF00 & (((long) mac[mac.length - 1]) << 8))) >> 6;
                    id = id % (maxDataCenterId + 1);
                }
            }
        } catch (Exception e) {
            logger.warn(" getDataCenterId: " + e.getMessage());
        }
        return id;
    }

    /**
     * 获取 maxWorkerId
     */
    private static long getMaxWorkerId(long maxDataCenterId, long maxWorkerId) {
        StringBuilder mpId = new StringBuilder();
        mpId.append(maxDataCenterId);
        String name = ManagementFactory.getRuntimeMXBean().getName();
        if (null != name && !name.trim().isEmpty()) {
            /*
             * GET jvmPid
             */
            mpId.append(name.split("@")[0]);
        }
        /*
         * MAC + PID 的 hashcode 获取16个低位
         */
        return (mpId.toString().hashCode() & 0xffff) % (maxWorkerId + 1);
    }

    /**
     * tilNextMillis
     *
     * @param lastTimestamp stamp
     * @return l
     */
    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 获取毫秒
     *
     * @return l
     */
    private long timeGen() {
        return SystemClock.now();
    }

}