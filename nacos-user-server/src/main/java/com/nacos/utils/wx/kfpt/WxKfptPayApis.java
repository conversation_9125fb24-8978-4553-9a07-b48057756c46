package com.nacos.utils.wx.kfpt;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.app.AppServiceExtension;
import com.wechat.pay.java.service.payments.app.model.Amount;
import com.wechat.pay.java.service.payments.app.model.PrepayRequest;
import com.wechat.pay.java.service.payments.app.model.PrepayWithRequestPaymentResponse;
import lombok.extern.log4j.Log4j2;

import java.math.BigDecimal;

/**
 * &#064;Description: 微信支付接口
 */
@Log4j2
public class WxKfptPayApis {

    /**
     * 微信支付 app支付
     * @param orderNo 支付单号
     * @return 返回App接口信息
     */
    public static PrepayWithRequestPaymentResponse postAppPlaceOrder(String orderNo, BigDecimal actualPayment, String description){
        AppServiceExtension appServiceExtension = new AppServiceExtension.Builder().config(config).build();
        PrepayRequest request = new PrepayRequest();
        Amount amount = new Amount();
//        amount.setTotal(1);
        amount.setTotal(actualPayment.multiply(new BigDecimal("100")).intValue());
        request.setAmount(amount);
        request.setAppid(WxKfptProperties.wxkfptAppId);
        request.setMchid(WxKfptProperties.wxkfptMchId);
//        点点{用户ID}-购买{商品名称}权益
        request.setDescription(description);
        request.setNotifyUrl(WxKfptProperties.wxkfptPayNotifyUrl);
        request.setOutTradeNo(orderNo);
        PrepayWithRequestPaymentResponse response = appServiceExtension.prepayWithRequestPayment(request);
        log.info("微信支付app支付返回结果：{}", response);
        return response;
    }

    /** 微信支付公共配置信息 */
    private static final Config config = new RSAAutoCertificateConfig.Builder()
            .merchantId(WxKfptProperties.wxkfptMchId)
            .privateKeyFromPath(WxKfptProperties.wxkfptPrivateKeyPath)
            .merchantSerialNumber(WxKfptProperties.wxkfptMerchantSerialNumber)
            .apiV3Key(WxKfptProperties.wxkfptApiV3Key)
            .build();

}