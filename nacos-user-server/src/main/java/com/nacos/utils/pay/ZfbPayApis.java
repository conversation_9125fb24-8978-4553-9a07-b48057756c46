package com.nacos.utils.pay;

import com.alipay.easysdk.factory.Factory;
import com.alipay.easysdk.kernel.Config;
import com.alipay.easysdk.kernel.util.ResponseChecker;
import com.alipay.easysdk.payment.app.models.AlipayTradeAppPayResponse;
import com.alipay.easysdk.payment.wap.models.AlipayTradeWapPayResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.PrintWriter;
import java.math.BigDecimal;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

//支付宝支付
@Slf4j
public class ZfbPayApis {
    private static boolean hasMethodBeenCalled = false;//执行标记

    private static void initFactory() {
        if (!hasMethodBeenCalled) {
            // 1. 设置参数（全局只需设置一次）
            Factory.setOptions(getOptions());
            hasMethodBeenCalled = true;
        }
    }

    private static Config getOptions() {
        Config config = new Config();
        config.appId = ZfbPayProperties.zfbAppId;
        config.gatewayHost = "openapi.alipay.com";
        config.protocol = "https";
        config.signType = "RSA2";
        config.merchantPrivateKey = ZfbPayProperties.zfbMerchantPrivateKey;
//        config.merchantCertPath = "<-- 请填写您的应用公钥证书文件路径，例如：/foo/appCertPublicKey_2019051064521003.crt -->";
//        config.alipayCertPath = "<-- 请填写您的支付宝公钥证书文件路径，例如：/foo/alipayCertPublicKey_RSA2.crt -->";
//        config.alipayRootCertPath = "<-- 请填写您的支付宝根证书文件路径，例如：/foo/alipayRootCert.crt -->";
        //注：如果采用非证书模式，则无需赋值上面的三个证书路径，改为赋值如下的支付宝公钥字符串即可
         config.alipayPublicKey = ZfbPayProperties.zfbAlipayPublicKey;
        //可设置异步通知接收服务地址（可选）
        config.notifyUrl = ZfbPayProperties.zfbNotifyUrl;
        return config;
    }

    //获取支付宝app拉起信息
    public static AlipayTradeAppPayResponse getZfbAppPayResponse(String outTradeNo, BigDecimal actualPayment, String subject) {
        try {
            initFactory();
//            String yuan = "0.01";
            String yuan = String.format("%.2f", actualPayment);
            AlipayTradeAppPayResponse response = Factory.Payment.App().pay(subject, outTradeNo, yuan);
            if (ResponseChecker.success(response)) {
                return response;
            }
            log.error("支付宝APP支付调用失败，原因：response: {}，\nresponse.getBody(){}", response, response.getBody());
        } catch (Exception e) {
            log.error("支付宝APP支付调用遭遇异常，原因：{}", e.getMessage(), e);
        }
        return null;
    }

    //支付宝扫码
    public static AlipayTradeWapPayResponse getZfbWapPayResponse(String outTradeNo, BigDecimal actualPayment, String subject, String quitUrl, String returnUrl) {
        try {
            initFactory();
//            String yuan = "0.01";
            String yuan = String.format("%.2f", actualPayment);
            AlipayTradeWapPayResponse response = Factory.Payment.Wap()
                    .pay(subject, outTradeNo, yuan,quitUrl,returnUrl);
            if (ResponseChecker.success(response)) {
                return response;
            }
            log.error("支付宝手机网页支付失败，原因：response: {}，\nresponse.getBody(){}", response, response.getBody());
        } catch (Exception e) {
            log.error("支付宝手机网页支付异常，原因：{}", e.getMessage(), e);
        }
        return null;
    }

    //支付宝支付回调调用接收处理：验证签名：返回单号：返回订单号
    public static Map<String, String> callBackOutTradeNo(HttpServletRequest request){
        try {
            Map<String, String> parameters = new HashMap<>();
            Enumeration<String> parameterNames = request.getParameterNames();
            while (parameterNames.hasMoreElements()) {
                String paramName = parameterNames.nextElement();
                String paramValue = request.getParameter(paramName);
                parameters.put(paramName, paramValue);
            }
            log.info("支付宝支付回调参数：{}", parameters);
            //调用验证签名方法
//            initFactory();
//            if (Factory.Payment.Common().verifyNotify(parameters)){
//                return parameters;
//            }
            return parameters;
        } catch (Exception e) {
            log.error("支付宝支付回调异常，原因：{}", e.getMessage(), e);
        }
        return null;
    }

    //回调返回成功
    public static void callBackReturnSuccess(HttpServletResponse response) {
        log.info("支付宝回调处理成功");
        response.setStatus(200);
        try (PrintWriter out = response.getWriter()) {
            out.println("success");
        } catch (Exception e) {
            log.error("支付宝回调处理成功返回异常：{}", e.getMessage());
        }
    }

    //回调返回失败
    public static void callBackReturnFile(HttpServletResponse response) {
        log.info("支付宝回调处理失败");
        response.setStatus(200);
        try (PrintWriter out = response.getWriter()) {
            out.println("fail");
        } catch (Exception e) {
            log.error("支付宝回调处理失败返回异常：{}", e.getMessage());
        }
    }

}
