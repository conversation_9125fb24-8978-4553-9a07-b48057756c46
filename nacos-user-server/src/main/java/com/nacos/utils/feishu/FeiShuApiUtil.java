package com.nacos.utils.feishu;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;

@Log4j2
public class FeiShuApiUtil {

    private static final String ACCESS_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal";
    private static final String ADD_RECORDS_URL = "https://open.feishu.cn/open-apis/bitable/v1/apps/${app_token}/tables/${table_id}/records";
    private static final String UPLOAD_ALL_URL = "https://open.feishu.cn/open-apis/drive/v1/medias/upload_all";

    public static String getFeiShuAccessToken() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("app_id", FeiShuProperties.appId);
        jsonObject.put("app_secret", FeiShuProperties.appSecret);

        OkHttpClient client = new OkHttpClient().newBuilder().build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonObject.toJSONString());
        Request request = new Request.Builder()
                .url(ACCESS_TOKEN_URL)
                .addHeader("Content-Type", "application/json")
                .post(body)
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    // 准备需要上传的文件
    public static String getFileToken(MultipartFile fileToUpload, String accessToken) {
        OkHttpClient client = new OkHttpClient();
        try {
            RequestBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("file_name", fileToUpload.getOriginalFilename())
                    .addFormDataPart("parent_type", FeiShuProperties.parentType)
                    .addFormDataPart("parent_node", FeiShuProperties.parentNode)
                    .addFormDataPart("size", String.valueOf(fileToUpload.getSize()))
                    .addFormDataPart("file", fileToUpload.getOriginalFilename(),
                            RequestBody.create(MediaType.parse("application/octet-stream"), fileToUpload.getBytes()))
                    .build();
            Request request = new Request.Builder()
                    .url(UPLOAD_ALL_URL)
                    .addHeader("Authorization", "Bearer " + accessToken)
                    .post(requestBody)
                    .build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().string();
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.error("getFileToken: {}",e);
            return null;
        }
        return null;
    }

    //添加好评接口
    public static String addTableRecords(Long userId, String userName, String accessToken, String fileToken) {
        long currentTime = System.currentTimeMillis();
        OkHttpClient client = new OkHttpClient().newBuilder().build();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), "{\"fields\":{\"用户名\":\""+userName+"\",\"提交日期\":"+currentTime+",\"用户id\":\""+userId+"\",\"好评截图\":[{\"file_token\":\""+fileToken+"\"}],\"状态\":\"待审核\"}}");
        Request request = new Request.Builder()
                .url(ADD_RECORDS_URL.replace("${app_token}", FeiShuProperties.appToken).replace("${table_id}", FeiShuProperties.tableId))
                .addHeader("Authorization", "Bearer " + accessToken)
                .addHeader("Content-Type", "application/json")
                .post(requestBody)
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string(); // 保存响应体内容
                log.info("飞书多维表格添加 addTableRecords=" + responseBody);
                return responseBody;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("addTableRecords: {}",e);
            return null;
        }
        return null;
    }

}
