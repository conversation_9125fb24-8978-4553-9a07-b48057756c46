package com.nacos.utils.captcha;

import com.aliyun.captcha20230305.Client;
import com.aliyun.captcha20230305.models.VerifyIntelligentCaptchaRequest;
import com.aliyun.captcha20230305.models.VerifyIntelligentCaptchaResponse;
import com.aliyun.teaopenapi.models.Config;
import com.nacos.constant.CommonPara;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云验证码工具类
 */
@Slf4j
public class CaptchaAliyunUtil {

    private static Client client;

    static {
        try {
            Config config = new Config()
                    .setAccessKeyId(CommonPara.captchaAccessKeyId)
                    .setAccessKeySecret(CommonPara.captchaAccessKeySecret);
            // 验证码服务接入点
            config.endpoint = CommonPara.captchaEndpoint;
            // 根据官方示例，添加超时配置
            config.connectTimeout = 5000;
            config.readTimeout = 5000;
            client = new Client(config);
        } catch (Exception e) {
            log.error("初始化阿里云验证码客户端失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 智能验证码校验
     *
     * @param captchaVerifyParam 验证参数 (前端传来的)
     * @param sceneId            场景ID
     * @return 验证结果，true表示验证通过，false表示验证失败或出现异常时根据策略决定
     */
    public static boolean verifyIntelligentCaptcha(String captchaVerifyParam, String sceneId) {
        if (client == null) {
            log.error("阿里云验证码客户端未初始化，验证跳过并返回失败。");
            return false;
        }

        boolean verificationResult = false;

        try {
            // 1. 创建请求
            VerifyIntelligentCaptchaRequest verifyIntelligentCaptchaRequest = new VerifyIntelligentCaptchaRequest(); 
            // 2. 设置验证参数
            verifyIntelligentCaptchaRequest.setCaptchaVerifyParam(captchaVerifyParam);
            if (sceneId != null && !sceneId.isEmpty()) {
                verifyIntelligentCaptchaRequest.setSceneId(sceneId);
                log.debug("智能验证码请求参数 sceneId: {}", sceneId);
            } else {
                log.info("智能验证码请求参数 sceneId 未提供或为空。");
            }
            log.debug("智能验证码请求参数 captchaVerifyParam: {}", captchaVerifyParam);

            // 3. 发送验证请求
            VerifyIntelligentCaptchaResponse response = client
                    .verifyIntelligentCaptcha(verifyIntelligentCaptchaRequest);

            // 4. 记录响应信息
            if (response != null && response.getBody() != null) {
                String responseVerifyCodeStr = "N/A";
                AliyunCaptchaVerifyCode responseVerifyCodeEnum = AliyunCaptchaVerifyCode.UNKNOWN;
                // 安全地获取 VerifyCode 及其枚举，即使 getResult() 为 null
                if (response.getBody().getResult() != null) {
                    responseVerifyCodeStr = response.getBody().getResult().getVerifyCode();
                    if (responseVerifyCodeStr != null && !responseVerifyCodeStr.isEmpty()) {
                         responseVerifyCodeEnum = AliyunCaptchaVerifyCode.fromCode(responseVerifyCodeStr);
                    }
                }
                
                Object sdkVerifyResult = "N/A";
                if (response.getBody().getResult() != null) {
                    sdkVerifyResult = response.getBody().getResult().getVerifyResult();
                }

                log.debug("智能验证码响应: code: {}, message: {}, requestId: {}, result.verifyCode: {} ({}), result.verifyResult: {}, success: {}",
                        response.getBody().getCode(),
                        response.getBody().getMessage(),
                        response.getBody().getRequestId(),
                        responseVerifyCodeStr,
                        responseVerifyCodeEnum.getDescription(),
                        sdkVerifyResult,
                        response.getBody().getSuccess());

                // 5. 获取验证结果
                if (Boolean.TRUE.equals(response.getBody().getSuccess()) && response.getBody().getResult() != null) {
                    // 直接根据SDK的VerifyResult确定业务验证结果
                    verificationResult = Boolean.TRUE.equals(response.getBody().getResult().getVerifyResult());

                    // 获取VerifyCode及其描述用于日志记录
                    // String verifyCodeStrForLog = response.getBody().getResult().getVerifyCode(); // 已在上面获取为 responseVerifyCodeStr
                    // AliyunCaptchaVerifyCode verifyCodeEnumForLog = AliyunCaptchaVerifyCode.fromCode(verifyCodeStrForLog); // 已在上面获取为 responseVerifyCodeEnum

                    if (verificationResult) {
                        log.info("智能验证码验证通过 (基于SDK VerifyResult)。阿里云响应码: {} ({})。",
                                 responseVerifyCodeEnum.getCode(), // 使用从上面获取的 responseVerifyCodeEnum
                                 responseVerifyCodeEnum.getDescription());
                    } else {
                        log.warn("智能验证码验证失败 (基于SDK VerifyResult)。阿里云响应码: {} ({})。",
                                 responseVerifyCodeEnum.getCode(), // 使用从上面获取的 responseVerifyCodeEnum
                                 responseVerifyCodeEnum.getDescription());
                    }
                } else {
                    log.warn("智能验证码API请求未成功或响应结果为空，验证失败。Success: {}, ResultIsNull: {}",
                            response.getBody().getSuccess(), response.getBody().getResult() == null);
                    verificationResult = false; // 确保在这种情况下 verificationResult 为 false
                }
            } else {
                log.warn("智能验证码响应异常或结果为空，默认验证失败。Response: {}", response);
                verificationResult = false;
            }

        } catch (com.aliyun.tea.TeaException error) {
            log.error("智能验证码验证失败 (TeaException): Code={}, Message={}, RequestId={}. 验证不通过。",
                    error.getCode(), error.getMessage(), error.getData() != null ? error.getData().get("RequestId") : "N/A", error);
            verificationResult = false;
        } catch (Exception e) {
            log.error("智能验证码验证失败 (Exception): {}. 验证不通过。", e.getMessage(), e);
            verificationResult = false;
        }
        return verificationResult;
    }
}