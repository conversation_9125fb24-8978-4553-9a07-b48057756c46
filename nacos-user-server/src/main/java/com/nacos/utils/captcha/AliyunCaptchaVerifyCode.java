package com.nacos.utils.captcha;

import java.util.HashMap;
import java.util.Map;

public enum AliyunCaptchaVerifyCode {
    T001("T001", "验证通过。"),
    T005("T005", "控制台开启测试模式，且配置了验证通过。"), // 阿里云层面认为是配置通过

    F001("F001", "疑似攻击请求，风险策略不通过。"),
    F002("F002", "传入的CaptchaVerifyParam参数为空。客户端参数需要完整传递。"),
    F003("F003", "传入的CaptchaVerifyParam格式不合法。客户端参数需禁止修改。"),
    F004("F004", "控制台开启测试模式，且配置了验证不通过。"),
    F005("F005", "CaptchaVerifyParam中的场景ID（sceneId）不合法。请检查客户端传递的参数。"),
    F006("F006", "CaptchaVerifyParam中的场景ID（sceneId）与控制台创建的不符。请检查前端集成配置。"),
    F008("F008", "验证数据重复提交。同一笔验证码请求只允许提交一次。"),
    F009("F009", "检测到虚拟设备环境（如虚拟机、模拟器等）。如需放行请调整控制台策略。"),
    F010("F010", "同IP访问频率超出限制。如需调整请配置控制台策略。"),
    F011("F011", "同设备访问频率超出限制。如需调整请配置控制台策略。"),
    F012("F012", "服务端传入的SceneID与前端配置的SceneId不一致。"),
    F013("F013", "传入的CaptchaVerifyParam缺少必要参数。"),
    F014("F014", "无初始化记录。可能原因：1.验证请求与初始化间隔超20分钟；2.未初始化即请求验证。"),
    F015("F015", "验证交互不通过（如拼图错误、答案选择错误等）。可刷新重试。"),
    F016("F016", "控制台自定义策略配置的URL验证导致不通过。请检查控制台URL策略配置。"),
    F017("F017", "疑似攻击请求，协议或参数异常不通过。"),

    UNKNOWN("UNKNOWN", "未知的验证码响应代码。");

    private final String code;
    private final String description;

    private static final Map<String, AliyunCaptchaVerifyCode> CODE_MAP = new HashMap<>();

    static {
        for (AliyunCaptchaVerifyCode verifyCode : values()) {
            CODE_MAP.put(verifyCode.getCode(), verifyCode);
        }
    }

    AliyunCaptchaVerifyCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据字符串代码获取对应的枚举常量。
     * @param code 阿里云返回的VerifyCode字符串
     * @return 对应的枚举常量，如果未找到则返回 UNKNOWN。
     */
    public static AliyunCaptchaVerifyCode fromCode(String code) {
        if (code == null || code.isEmpty()) {
            return UNKNOWN;
        }
        return CODE_MAP.getOrDefault(code, UNKNOWN);
    }

    /**
     * 判断此响应码在阿里云定义中是否表示一种通过状态 (主要用于理解和日志)。
     * 注意：业务逻辑可能仅接受 T001 作为真正的通过。
     * @return true 如果是T001或T005，否则false。
     */
    public boolean isConsideredPassByAliyun() {
        return this == T001 || this == T005;
    }
} 