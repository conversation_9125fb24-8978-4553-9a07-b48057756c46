package com.nacos.enums;

import lombok.Getter;

/**
 * 二维码类型
 */
@Getter
public enum LoginTypeEnum {

    LOGIN("L"), // L 登录
    SHARE("F"), // F分享
    BIND("B"),  // B 绑定

    WX_LOGIN(1), //微信登录
    GOOGLE_LOGIN(2), //谷歌登录
    APPLE_LOGIN(3), //苹果登录
    ;

    //操作类型
    String type;
    //登录类型
    Integer intValue;
    LoginTypeEnum(String type) {
        this.type = type;
    }

    LoginTypeEnum(Integer intValue) {
        this.intValue = intValue;
    }
}

