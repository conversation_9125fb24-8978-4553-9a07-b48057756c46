package com.nacos.config.message.model;

import com.nacos.constant.CommonPara;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 安卓实体
 * <AUTHOR>
 * @date 2024-01-05
 */
@Data
@Schema(title = "安卓实体", description = "接收消息实体")
public class AndroidEntity {

    @Schema(title = "appkey")
    private String appkey;

    @Schema(title = "appvalue")
    private String appMasterSecret;

    @Schema(title = "设备token")
    private String deviceToken;

    @Schema(title = "提示文字")
    private String ticker;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "内容")
    private String text;

    @Schema(title = "消息类型 1是消息 2通知")
    private int displayType;

    @Schema(title = "自定义键")
    private String extraFieldKey;

    @Schema(title = "自定义值")
    private String extraFieldValue;

    @Schema(title = "自定义")
    private String channelActivity;

    @Schema(title = "自定义")
    private ChannelEntity channelProperties;

    @Schema(title = "图标")
    private String setImg;


    /**
     * 消息类型
     * @param deviceToken
     * @param ticker
     * @param title
     * @param text
     * @param channelActivity
     * @param channelProperties
     * @return
     */
    public static AndroidEntity buildAndroidEntityMessage(String deviceToken, String ticker, String title, String text, String extraFieldKey, String extraFieldValue, String channelActivity, ChannelEntity channelProperties, String setImg) {
        AndroidEntity androidEntity = new AndroidEntity();
        androidEntity.appkey = CommonPara.umAppId;
        androidEntity.appMasterSecret = CommonPara.umMasterSecret;
        androidEntity.deviceToken = deviceToken;
        androidEntity.ticker = ticker;
        androidEntity.title = title;
        androidEntity.setImg = setImg;
        androidEntity.text = text;
        androidEntity.displayType = 1;
        androidEntity.extraFieldKey = extraFieldKey;
        androidEntity.extraFieldValue = extraFieldValue;
        androidEntity.channelActivity = channelActivity;
        androidEntity.channelProperties = channelProperties;
        return androidEntity;
    }

    /**
     * 通知类型
     * @param deviceToken
     * @param ticker
     * @param title
     * @param text
     * @param channelActivity
     * @param channelProperties
     * @return
     */
    public static AndroidEntity buildAndroidEntityNotice(String deviceToken, String ticker, String title, String text, String extraFieldKey, String extraFieldValue, String channelActivity, ChannelEntity channelProperties, String setImg) {
        AndroidEntity androidEntity = new AndroidEntity();
        androidEntity.appkey = CommonPara.umAppId;
        androidEntity.appMasterSecret = CommonPara.umMasterSecret;
        androidEntity.deviceToken = deviceToken;
        androidEntity.ticker = ticker;
        androidEntity.title = title;
        androidEntity.setImg = setImg;
        androidEntity.text = text;
        androidEntity.displayType = 2;
        androidEntity.extraFieldKey = extraFieldKey;
        androidEntity.extraFieldValue = extraFieldValue;
        androidEntity.channelActivity = channelActivity;
        androidEntity.channelProperties = channelProperties;
        return androidEntity;
    }

    /**
     * 广播模式消息通知
     */
    public static AndroidEntity buildAndroidBroadcastEntityNotice(String ticker, String title, String text, String extraFieldKey, String extraFieldValue, String channelActivity, ChannelEntity channelProperties, String setImg) {
        AndroidEntity androidEntity = new AndroidEntity();
        androidEntity.appkey = CommonPara.umAppId;
        androidEntity.appMasterSecret = CommonPara.umMasterSecret;
        androidEntity.ticker = ticker;
        androidEntity.title = title;
        androidEntity.setImg = setImg;
        androidEntity.text = text;
        androidEntity.displayType = 2;
        androidEntity.extraFieldKey = extraFieldKey;
        androidEntity.extraFieldValue = extraFieldValue;
        androidEntity.channelActivity = channelActivity;
        androidEntity.channelProperties = channelProperties;
        return androidEntity;
    }

}
