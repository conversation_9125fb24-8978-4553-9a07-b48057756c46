package com.nacos.config.message.mq;

import com.alibaba.fastjson2.JSONObject;
import com.business.db.mapper.ImgDrawRecordMapper;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.model.BMessageObject;
import com.business.message.mq.BRedisMQTopicEnum;
import com.nacos.config.message.YouMengMessage;
import com.nacos.enums.ImgDrawEnum;
import com.nacos.enums.WebSocketEnum;
import com.nacos.handler.WebSocketHandler;
import com.nacos.service.IUserService;
import com.nacos.service.senior.MessageSubscriberService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class RedisMessageListenerAdapter implements MessageListener {

    @Resource
    private MessageSubscriberService messageSubscriberService;

    @Resource
    private IUserService userService;
    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;

    //消息监听器
    @Override
    public void onMessage(@NotNull Message message, byte[] pattern) {
        BRedisMQTopicEnum topic = BRedisMQTopicEnum.getTopic(new String(pattern));
        if (topic != null) {
            processMessage(message, topic);
        }
    }

    //区分通道处理
    public void processMessage(Message message, BRedisMQTopicEnum topic) {
        String jsonMessage = new String(message.getBody());
        switch (topic) {
            //绘图服务
            case TOPIC_GOAPI:
                handleMessageDraw(message);
                break;
            //运营消息
            case TOPIC_OPERATIONS:
                log.info("handleMessageOperation:运营消息订阅, {}", jsonMessage);
                BMessageObject bMessageObject = BMessageSendUtil.getBMessageObject(jsonMessage);
                if (bMessageObject == null || bMessageObject.getUserId() == null || bMessageObject.getType() == null){
                    break;
                }
                handleMessageOperation(bMessageObject);
                break;
            //系统消息
            case TOPIC_SYSTEM:
                BMessageObject sysBMessageObject= BMessageSendUtil.getBMessageObject(jsonMessage);
                if (sysBMessageObject == null || sysBMessageObject.getObject() == null || sysBMessageObject.getType() == null){
                    break;
                }
                handleSystemMessageOperation(sysBMessageObject);
                break;
            //数字人视频任务
            case TOPIC_DIGITAL:
                handleMessageDigital(message);
                break;
            default:
                log.error("未知的通道 topic: {}", topic);
                break;
        }
    }

    //绘图服务消息订阅
    private void handleMessageDraw(Message message) {
        String jsonMessage = new String(message.getBody());
        BMessageObject bMessageObject = BMessageSendUtil.getBMessageObject(jsonMessage);
        if (bMessageObject == null || bMessageObject.getType() == null){
            return;
        }
        log.info("handleMessageDraw:绘图服务消息订阅接收成功, {}", bMessageObject.getType());
        try {
            if (bMessageObject.getType() == WebSocketEnum.DRAW_JOB_PUSH.getPushType()){
                messageSubscriberService.pushMessageDrawMJ(bMessageObject);
                return;
            }
            if (bMessageObject.getUserId() != null){
                // TODO 其他消息推送 移动端推送
                log.info("其他消息推送任务.........");
                YouMengMessage.pushMessage(jsonMessage, userService);
                JSONObject jsonObject = JSONObject.parseObject(jsonMessage);
                if(jsonObject != null && jsonObject.containsKey("object") ) {
                    String object = jsonObject.getString("object");
                    JSONObject jsonObject1 = JSONObject.parseObject(object);
                    if("绘图任务失败".equals(jsonObject1.getString("notifTitle"))) {
                        Long taskId = jsonObject1.getLong("taskId");
                        com.business.model.vo.ImgDrawHistoryVO imgDrawHistoryVO = new com.business.model.vo.ImgDrawHistoryVO();
                        imgDrawHistoryVO.setId(taskId);
                        imgDrawHistoryVO.setUserId(bMessageObject.getUserId());
                        imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_FAIL.getValue());
                        // 推送通知
                        messageSubscriberService.pushDrawTaskRealTime(imgDrawHistoryVO);
                    }
                }
                //校验是否存在websocket实例管理
                if (WebSocketHandler.getSessionNull(String.valueOf(bMessageObject.getUserId()))){
                    return;
                }
                // TODO 其他消息推送 web推送
                WebSocketHandler.sendMessage(bMessageObject.getUserId(), jsonMessage);
            }

        } catch (Exception e) {
            log.error("绘图消息订阅处理异常,{}",e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    //数字人视频任务消息订阅
    private void handleMessageDigital(Message message) {
        String jsonMessage = new String(message.getBody());
        BMessageObject bMessageObject = BMessageSendUtil.getBMessageObject(jsonMessage);
        if (bMessageObject == null || bMessageObject.getType() == null){
            return;
        }
        log.info("handleMessageDigital:数字人视频任务消息订阅接收成功, {}", bMessageObject.getType());
        try {
            if (bMessageObject.getUserId() != null){
                // 移动端推送
                YouMengMessage.pushMessage(jsonMessage, userService);
                
                // Web端推送
                if (!WebSocketHandler.getSessionNull(String.valueOf(bMessageObject.getUserId()))){
                    WebSocketHandler.sendMessage(bMessageObject.getUserId(), jsonMessage);
                }
            }
        } catch (Exception e) {
            log.error("数字人视频任务消息订阅处理异常,{}",e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    //运营弹窗消息推送
    private void handleMessageOperation(BMessageObject bMessageObject) {
        if(BMessageSendEnum.ACTIVITY_PUSH.getType() == bMessageObject.getType()){
            messageSubscriberService.pushMessageActivityPromotion(bMessageObject);
        }
    }

    //运营活动系统消息推送
    private void handleSystemMessageOperation(BMessageObject sysBMessageObject) {
        if(BMessageSendEnum.SYSTEM_ACTIVITY_PUSH.getType() == sysBMessageObject.getType()){
            messageSubscriberService.pushSystemMessageActivityPromotion(sysBMessageObject);
        }
    }
}
