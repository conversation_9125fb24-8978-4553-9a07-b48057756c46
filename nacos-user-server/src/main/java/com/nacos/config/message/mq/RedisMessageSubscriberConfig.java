package com.nacos.config.message.mq;

import com.business.message.mq.BRedisMQTopicEnum;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

//1 进行消息订阅配置
@Configuration
@AutoConfigureAfter({RedisMessageListenerAdapter.class})
public class RedisMessageSubscriberConfig {

    @Bean
    RedisMessageListenerContainer redisContainer(RedisConnectionFactory connectionFactory, MessageListenerAdapter listenerAdapter) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        //绘图推送
        container.addMessageListener(listenerAdapter, new PatternTopic(BRedisMQTopicEnum.TOPIC_GOAPI.getTopic()));
        //系统推送
        container.addMessageListener(listenerAdapter, new PatternTopic(BRedisMQTopicEnum.TOPIC_SYSTEM.getTopic()));
        //运营推送
        container.addMessageListener(listenerAdapter, new PatternTopic(BRedisMQTopicEnum.TOPIC_OPERATIONS.getTopic()));
        //数字人视频任务推送这个服务再fcsuser里
//        container.addMessageListener(listenerAdapter, new PatternTopic(BRedisMQTopicEnum.TOPIC_DIGITAL.getTopic()));
        return container;
    }

    @Bean
    MessageListenerAdapter listenerAdapter(RedisMessageListenerAdapter redisMessageListenerAdapter) {
        return new MessageListenerAdapter(redisMessageListenerAdapter, "onMessage");
    }

}

