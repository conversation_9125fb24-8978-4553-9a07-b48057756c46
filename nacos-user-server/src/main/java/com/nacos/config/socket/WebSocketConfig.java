package com.nacos.config.socket;

import com.nacos.handler.WebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

/**
 * 告诉SpringBoot这是一个配置类，让SpringBoot加载配置
 *  用于开启注解接收和发送消息
 * <AUTHOR>
 * @date 2024/01/11
 */

@Slf4j
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new WebSocketHandler(new WebSocketSessionManager()), "/api/websocket")//设置处理类和连接路径
                .setAllowedOrigins("*") //设置作用域
                .addInterceptors(new MyWebSocketInterceptor());//设置拦截器
    }
    //自定义拦截器拦截WebSocket请求
    static class MyWebSocketInterceptor implements HandshakeInterceptor {
        @Override
        public boolean beforeHandshake(@NotNull ServerHttpRequest request, @NotNull ServerHttpResponse response,
                                       @NotNull org.springframework.web.socket.WebSocketHandler wsHandler, @NotNull Map<String, Object> attributes) throws Exception {
            if (request instanceof ServletServerHttpRequest serverHttpRequest) {
                // 获取请求路径携带的参数
                String userId = serverHttpRequest.getServletRequest().getParameter("userId");
                attributes.put("userId", userId);
                return true;
            } else {
                return false;
            }
        }
        // 握手成功后的处理，可以进行一些初始化工作
        @Override
        public void afterHandshake(ServerHttpRequest request, @NotNull ServerHttpResponse response,
                                   @NotNull org.springframework.web.socket.WebSocketHandler wsHandler, Exception exception) {
            String userId = request.getURI().getQuery();
            log.info("WebSocket 连接成功 userId:{}", userId);

        }
    }
}
