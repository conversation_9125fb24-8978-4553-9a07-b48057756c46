package com.nacos.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.business.db.model.dto.AttentionDTO;
import com.business.db.model.dto.FlowRecordDTO;
import com.business.db.model.dto.UpdateUserInfoDTO;
import com.business.db.model.po.FlowRecordPO;
import com.business.db.model.po.UserPO;
import com.business.db.model.vo.UserParamVO;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;


public interface IUserService extends IService<UserPO> {

    Result<?> settingsPage(Long languageTagId);

    Result<?> personalPage(Long userId, Long loginUserId);

    Result<Boolean> isCourseMember(Long userId);

    UserPO getByInvCodeUser(String invitationCode);

    Result<?> userInfo(Long userId);

    Result<?> getSuggestUsers();

    Result<?> toggleFollow(AttentionDTO dto);

    UserPO getByFromUser(String unionId, String openId, String mobile, Long userId);

    UserParamVO getFansParam(Long userId);

    Result<?> updateUserInfo(UpdateUserInfoDTO dto);

    /**
     * 微信扫码新用户使用
     *
     * @return
     */
    UserPO getByUserId(Long userId);

    /**
     * 查询用户点子消费记录
     *
     * @param flowRecordDTO 条件查询
     * @return 返回查询结果
     */

    @Deprecated
    Result<Page<FlowRecordPO>> selectDDFlowRecord2(FlowRecordDTO flowRecordDTO);

    Result<Page<FlowRecordPO>> selectDDFlowRecord(FlowRecordDTO flowRecordDTO);

    Result<Integer> optIsPromptFlag() throws IBusinessException;

    Result<Object> digitalManApply(Long userId);

    Result<Object> queryDigitalManApplySize();
}
