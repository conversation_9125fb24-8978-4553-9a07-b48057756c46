package com.nacos.service;


import com.business.db.model.dto.pay.WxH5PayCourseDTO;
import com.business.db.model.dto.pay.WxH5PayDTO;
import com.business.db.model.dto.pay.ZfbH5PayCourseDTO;
import com.business.db.model.dto.pay.ZfbH5PayDTO;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;


public interface ZfbAndWxPayService {

    /**
     * app支付
     *
     * @param rechargeId 充值信息表id
     * @param userId     用户id
     * @param payType    支付调用类型：app、扫码
     * @return 支付信息
     */
    Result<Object> appPay(Long rechargeId, Long userId, Integer payType);

    //微信h5支付
    Result<Object> wxH5Pay(WxH5PayDTO wxH5PayDTO);

    //支付宝h5支付
    Result<Object> zfbH5Pay(ZfbH5PayDTO zfbH5PayDTO);

    boolean callBackAll(String outTradeNo) throws IBusinessException;

    Result<Object> wxH5PayCourse(WxH5PayCourseDTO wxH5PayCourseDTO);

    Result<Object> zfbH5PayCourse(ZfbH5PayCourseDTO zfbH5PayCourseDTO);

    Result<Object> memberPayCourse(Long coursesId);

    boolean callBackCourse(String outTradeNo) throws IBusinessException;

}
