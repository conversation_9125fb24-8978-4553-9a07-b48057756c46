package com.nacos.service.sys;


import com.baomidou.mybatisplus.extension.service.IService;
import com.business.db.model.po.SysNotificationUserPO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;

/**
 * 通知消息用户关联
 * @className: ISysNotificationUserService
 * @author: Myl
 * @createDate: 2023-12-21互动消息
 *
 */
public interface ISysNotificationUserService extends IService<SysNotificationUserPO> {

    Result<Boolean> add(SysNotificationUserPO notificationUserPO);

    Result<Boolean>  update(SysNotificationUserPO notificationUserPO);

    Result<Boolean> delete(BaseDeleteEntity params);


}
