package com.nacos.service.sys;


import com.baomidou.mybatisplus.extension.service.IService;
import com.business.db.model.po.SysLoginInfoPO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;
import org.springframework.web.socket.WebSocketSession;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 登录信息
 * @className: ISysLoginInfoService
 * @author: Myl互动消息
 * @createDate: 2023-12-21互动消息
 *
 */
public interface ISysLoginInfoService extends IService<SysLoginInfoPO> {

    Result<Boolean> add(SysLoginInfoPO loginInfoPO);

    Result<Boolean> activeUser(List<String> userIds);

    Result<Boolean>  update(SysLoginInfoPO loginInfoPO);

    Result<Boolean>  delete(BaseDeleteEntity params);


}
