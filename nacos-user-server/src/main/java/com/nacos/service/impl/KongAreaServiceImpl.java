package com.nacos.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.mapper.FunctionOptConfigMapper;
import com.business.db.mapper.ImgDrawDetlMapper;
import com.business.db.mapper.ImgModelConfigMapper;
import com.business.db.mapper.VideoModelConfigMapper;
import com.business.db.model.dto.DailyPicksDTO;
import com.business.db.model.po.FunctionOptConfigPO;
import com.business.db.model.po.ImgModelConfigPO;
import com.business.db.model.vo.DailyPicksVO;
import com.business.db.model.vo.FunctionOptConfigVO;
import com.business.db.model.vo.ImgModelConfigVO;
import com.business.db.model.vo.ImgModelsVO;
import com.business.enums.BAccountEnum;
import com.business.enums.BDDUseNumEnum;
import com.business.model.po.VideoModelConfigPO;
import com.business.model.vo.VideoModelConfigGradeVO;
import com.business.model.vo.VideoModelConfigVO;
import com.business.utils.BDateUtil;
import com.nacos.ddimg.ImageModelUtil;
import com.nacos.enums.CommonIntEnum;
import com.nacos.enums.DailySignTaskEnum;
import com.nacos.enums.ImgOptModelEnum;
import com.nacos.model.DailyPicksOptVO;
import com.nacos.result.Result;
import com.nacos.service.IKongAreaService;
import com.nacos.utils.DateUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Service
@Slf4j
public class KongAreaServiceImpl implements IKongAreaService {

    @Resource
    private ImgDrawDetlMapper imgDrawDetlMapper;
    @Resource
    private FunctionOptConfigMapper functionOptConfigMapper;
    @Resource
    private VideoModelConfigMapper videoModelConfigMapper;
    @Resource
    private ImgModelConfigMapper imgModelConfigMapper;

    @Value("${draw.models}")
    private String drawModel;

    @Override
    public Result<Page<DailyPicksVO>> dailyPicksPage(DailyPicksDTO dailyPicksDTO) {
        if (dailyPicksDTO.getPicksType() == null) {
            dailyPicksDTO.setPicksType(DailySignTaskEnum.DAILY_SELECTION.getCode());
        }
        dailyPicksDTO.setCreateDate(DateUtil.getDateNowShanghai());
        Page<DailyPicksVO> page = new Page<>(dailyPicksDTO.getPageNumber(), dailyPicksDTO.getPageSize());
        dailyPicksDTO.setUserId(BAccountEnum.OFFICIAL_ACCOUNT_ONE.getAccountId());
        return Result.SUCCESS(imgDrawDetlMapper.dailyPicksPage(page, dailyPicksDTO));
    }

    @Override
    public Result<List<DailyPicksOptVO>> dailyPicksOptConfigEditList(Long languageTagId) {
        List<DailyPicksOptVO> dailySignTaskDTOList = new ArrayList<>();
        dailySignTaskDTOList.add(new DailyPicksOptVO(
                "今日精选",
                DailySignTaskEnum.DAILY_SELECTION.getDescription(),
                BDateUtil.getStrTodayDate(),
                DailySignTaskEnum.DAILY_SELECTION.getCode())
        );
        dailySignTaskDTOList.add(new DailyPicksOptVO(
                "本周精选",
                DailySignTaskEnum.WEEKLY_SELECTION.getDescription(),
                BDateUtil.getStrWeekCount(),
                DailySignTaskEnum.WEEKLY_SELECTION.getCode())
        );
        dailySignTaskDTOList.add(new DailyPicksOptVO(
                "本月精选",
                DailySignTaskEnum.MONTHLY_SELECTION.getDescription(),
                BDateUtil.getStrMonthDate(),
                DailySignTaskEnum.MONTHLY_SELECTION.getCode())
        );
        return Result.SUCCESS(dailySignTaskDTOList);
    }

    @Override
    public Result<List<FunctionOptConfigVO>> functionOptConfigList(Long languageTagId, Long functionConfigId) {
        List<FunctionOptConfigPO> functionOptConfigPOS = functionOptConfigMapper.selectList(new LambdaQueryWrapper<FunctionOptConfigPO>()
                .eq(FunctionOptConfigPO::getLanguageTagId, languageTagId)
                .eq(FunctionOptConfigPO::getParentOperate, functionConfigId)
                .eq(FunctionOptConfigPO::getIsUse, CommonIntEnum.SHOW_TRUE.getIntValue())
                .eq(FunctionOptConfigPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                .orderByAsc(FunctionOptConfigPO::getSort));

        if (functionOptConfigPOS == null || functionOptConfigPOS.isEmpty()) {
            return Result.SUCCESS(new ArrayList<>());
        }
        List<FunctionOptConfigVO> functionOptConfigVOS = new ArrayList<>();
        for (FunctionOptConfigPO functionOptConfigPO : functionOptConfigPOS) {
            FunctionOptConfigVO functionOptConfigVO = BeanUtil.copyProperties(functionOptConfigPO, FunctionOptConfigVO.class);
            BDDUseNumEnum bddUseNumEnum = BDDUseNumEnum.getBDDUseNumEnumByOptType(functionOptConfigPO.getOperate());
            functionOptConfigVO.setExpendDdQua(bddUseNumEnum.getDdUseNumStr());
            functionOptConfigVO.setVipExpendDdQua(bddUseNumEnum.getDdVipUseNumStr());
            functionOptConfigVOS.add(functionOptConfigVO);
        }
        return Result.SUCCESS(functionOptConfigVOS);
    }

    @Override
    public Result<Object> videoModelConfigList(Long languageTagId, String version) {
        System.out.println("drawModel:=================================================");
        System.out.println("languageTagId:" + languageTagId);
        System.out.println("version:" + version);
        System.out.println("drawModel:=================================================");
        List<VideoModelConfigPO> videoModelConfigPOList = videoModelConfigMapper.selectList(
                new LambdaQueryWrapper<VideoModelConfigPO>()
                        .eq(VideoModelConfigPO::getLanguageTagId, languageTagId)
                        .eq(VideoModelConfigPO::getIsShow, CommonIntEnum.SHOW_TRUE.getIntValue())
                        .eq(VideoModelConfigPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .orderByAsc(VideoModelConfigPO::getSort)
        );
        if (videoModelConfigPOList == null || videoModelConfigPOList.isEmpty()) {
            return Result.SUCCESS(new ArrayList<>());
        }
        if (StringUtils.isNotBlank(version)) {
            //新版本模型
            List<VideoModelConfigPO> resultList = videoModelConfigPOList.stream()
                    .filter(item -> Arrays.asList(ImgOptModelEnum.VIDEO_ATTRIBUTE_KLING_BASICS.getValue(), ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_BASICS.getValue(), ImgOptModelEnum.VIDEO_ATTRIBUTE_ZHIPU_BASICS.getValue(),
                            ImgOptModelEnum.VIDEO_ATTRIBUTE_DREAMFACTORY2_BASICS.getValue(), ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI2_BASICS.getValue(), ImgOptModelEnum.VIDEO_ATTRIBUTE_DOMO_BASICS.getValue(),
                            ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI3_BASICS.getValue(), ImgOptModelEnum.VIDEO_ATTRIBUTE_DAFENQI4_BASICS.getValue(), ImgOptModelEnum.VIDEO_ATTRIBUTE_HAILUO_V2_BASICS.getValue()).contains(item.getAttribute()))
                    .toList();
            List<VideoModelConfigGradeVO> videoModelConfigGradeVOList = new ArrayList<>();
            for (VideoModelConfigPO videoModelConfigPO : resultList) {
                VideoModelConfigGradeVO videoModelConfigGradeVO = new VideoModelConfigGradeVO(videoModelConfigPO);
                videoModelConfigGradeVO.setDrawingModels(drawModel);
                videoModelConfigGradeVOList.add(videoModelConfigGradeVO);
            }
            System.out.println("1========新版参数==============");
            return Result.SUCCESS(videoModelConfigGradeVOList);
        } else {
            //老版本模型
            List<VideoModelConfigPO> resultList = videoModelConfigPOList.stream()
                    .filter(item -> Arrays.asList(ImgOptModelEnum.VIDEO_ATTRIBUTE_BYTE_LENS.getValue(),
                            ImgOptModelEnum.VIDEO_ATTRIBUTE_LUMA_BASICS.getValue(),
                            ImgOptModelEnum.VIDEO_ATTRIBUTE_DOMO_BASICS.getValue(),
                            ImgOptModelEnum.VIDEO_ATTRIBUTE_RUNWAY2_BASICS.getValue(),
                            ImgOptModelEnum.VIDEO_ATTRIBUTE_DIGITALMAN_BASICS.getValue()).contains(item.getAttribute()))
                    .toList();
            List<VideoModelConfigVO> videoModelConfigVOList = new ArrayList<>();
            for (VideoModelConfigPO videoModelConfigPO : resultList) {
                VideoModelConfigVO videoModelConfigVO = new VideoModelConfigVO(videoModelConfigPO);
                videoModelConfigVO.setDrawingModels(drawModel);
                videoModelConfigVOList.add(videoModelConfigVO);
            }
            System.out.println("2========旧版参数==============");
            return Result.SUCCESS(videoModelConfigVOList);
        }
    }

    @Override
    public Result<List<ImgModelConfigVO>> imageModelConfigPoster(Long languageTagId, String version) {
        List<ImgModelConfigPO> imgModelConfigPOList = imgModelConfigMapper.selectList(
                new LambdaQueryWrapper<ImgModelConfigPO>()
                        .eq(ImgModelConfigPO::getLanguageTagId, languageTagId)
                        .eq(ImgModelConfigPO::getAttribute, ImgOptModelEnum.DRAW_ATTRIBUTE_JIMENG.getValue())
                        .eq(ImgModelConfigPO::getIsShow, CommonIntEnum.SHOW_TRUE.getIntValue())
                        .eq(ImgModelConfigPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .orderByAsc(ImgModelConfigPO::getSort)
        );
        if (imgModelConfigPOList == null || imgModelConfigPOList.isEmpty()) {
            return Result.SUCCESS(new ArrayList<>());
        }
        List<ImgModelConfigVO> imgModelConfigVOList = new ArrayList<>();
        for (ImgModelConfigPO imgModelConfigPO : imgModelConfigPOList) {
            if (imgModelConfigPO == null) {
                continue;
            }
            imgModelConfigVOList.add(buildImgModelConfigVO(imgModelConfigPO));
        }
        return Result.SUCCESS(imgModelConfigVOList);
    }

    private ImgModelConfigVO buildImgModelConfigVO(ImgModelConfigPO imgModelConfigPO) {
        ImgModelConfigVO imgModelConfigVO = new ImgModelConfigVO();
        imgModelConfigVO.setId(imgModelConfigPO.getId());
        imgModelConfigVO.setDeUseDDQua(2d);
        imgModelConfigVO.setLanguageTagId(imgModelConfigPO.getLanguageTagId());
        imgModelConfigVO.setSort(imgModelConfigPO.getSort());
        imgModelConfigVO.setAttribute(imgModelConfigPO.getAttribute());
        imgModelConfigVO.setModelName(imgModelConfigPO.getModelName());
        imgModelConfigVO.setModelValue(imgModelConfigPO.getModelValue());
        ;
        imgModelConfigVO.setModelUrl(imgModelConfigPO.getModelUrl());
        imgModelConfigVO.setMjModels(ImgModelsVO.imgSpeedVOList(imgModelConfigPO.getMjModels()));
        imgModelConfigVO.setPromptTemplates(ImageModelUtil.getPromptTemplateDTOs(imgModelConfigPO.getPromptTemplates()));
        String imgScales = "[{\"key\":3,\"title\":\"电商详情页、朋友圈图片\",\"width\":\"3\",\"height\":\"4\",\"urlPrefix\":\"https://cdn.diandiansheji.com/global/all/video/scales/\",\"isTrue\":true,\"isFixed\":false},{\"key\":4,\"title\":\"教育海报、复古风内容\",\"width\":\"4\",\"height\":\"3\",\"urlPrefix\":\"https://cdn.diandiansheji.com/global/all/video/scales/\",\"isTrue\":true,\"isFixed\":false},{\"key\":5,\"title\":\"小红书、微博、朋友圈电商推广\",\"width\":\"1\",\"height\":\"1\",\"urlPrefix\":\"https://cdn.diandiansheji.com/global/all/video/scales/\",\"isTrue\":true,\"isFixed\":false},{\"key\":1,\"title\":\"抖音、小红书封面、朋友圈广告\",\"width\":\"9\",\"height\":\"16\",\"urlPrefix\":\"https://cdn.diandiansheji.com/global/all/video/scales/\",\"isTrue\":true,\"isFixed\":false},{\"key\":2,\"title\":\"微信公众号头图、微博横幅\",\"width\":\"16\",\"height\":\"9\",\"urlPrefix\":\"https://cdn.diandiansheji.com/global/all/video/scales/\",\"isTrue\":true,\"isFixed\":false}]";
        imgModelConfigVO.setImgScales(ImageModelUtil.getImgScaleDTOs(imgScales));
        imgModelConfigVO.setDefaultSize(imgModelConfigPO.getDefaultSize());
        imgModelConfigVO.setMinSize(imgModelConfigPO.getMinSize());
        imgModelConfigVO.setMaxSize(imgModelConfigPO.getMaxSize());
        imgModelConfigVO.setIsVip(imgModelConfigPO.getIsVip());
        imgModelConfigVO.setIsShow(imgModelConfigPO.getIsShow());
        imgModelConfigVO.setIsAddImg(imgModelConfigPO.getIsAddImg());
        imgModelConfigVO.setTagColor(imgModelConfigPO.getTagColor());
        imgModelConfigVO.setState(imgModelConfigPO.getState());
        imgModelConfigVO.setVipUseTag(imgModelConfigPO.getVipUseTag());
        imgModelConfigVO.setStateOffTag(imgModelConfigPO.getStateOffTag());
        imgModelConfigVO.setIsSupportCref(imgModelConfigPO.getIsSupportCref());
        imgModelConfigVO.setIsSupportSref(imgModelConfigPO.getIsSupportSref());
        imgModelConfigVO.setMjUseDDQua(BDDUseNumEnum.BYTEAPI_OPT_ONECLICK_POSTER.getDdUseNumStr());
        imgModelConfigVO.setMjVipUseDDQua(BDDUseNumEnum.BYTEAPI_OPT_ONECLICK_POSTER.getDdVipUseNumStr());
        imgModelConfigVO.setMjStyles(ImageModelUtil.getImgStyleDTOs(imgModelConfigPO.getMjStyles()));
        imgModelConfigVO.setWordLength(500);
        return imgModelConfigVO;
    }

}
