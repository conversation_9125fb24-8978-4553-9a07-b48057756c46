package com.nacos.service.impl;

import com.business.db.mapper.BlacklistIPMapper;
import com.business.db.model.po.user.BlacklistIPPO;
import com.nacos.service.BlacklistService;
import com.nacos.service.RateLimitConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 黑名单服务实现类
 */
@Slf4j
@Service
public class BlacklistServiceImpl implements BlacklistService {

    // IP黑名单前缀
    private static final String IP_BLACKLIST_PREFIX = "blacklist:ip:";
    // IP违规记录前缀
    private static final String IP_VIOLATIONS_PREFIX = "violations:ip:";
    
    // 黑名单阈值配置键
    private static final String BLACKLIST_THRESHOLD_KEY = "rate-limit.blacklist.threshold";
    // 黑名单持续时间配置键
    private static final String BLACKLIST_DURATION_KEY = "rate-limit.blacklist.duration";
    
    // 黑名单数据库映射器
    @Autowired(required = false)
    private BlacklistIPMapper blacklistIPMapper;
    
    // Redis模板
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    // 限流配置服务
    @Autowired
    private RateLimitConfigService configService;
    
    // 初始化黑名单缓存
    @PostConstruct
    @Override
    public void initBlacklistCache() {
        log.info("初始化IP黑名单缓存");
        try {
            if (blacklistIPMapper != null) {
                List<BlacklistIPPO> blacklistIPs = blacklistIPMapper.findAllValid();
                log.info("从数据库加载黑名单数据，共 {} 条", blacklistIPs.size());
                
                // 清除旧的缓存
                clearBlacklistCache();
                
                // 加载到Redis
                for (BlacklistIPPO blacklistIPPO : blacklistIPs) {
                    String key = IP_BLACKLIST_PREFIX + blacklistIPPO.getIp();
                    // 计算剩余过期时间
                    long expireTime = blacklistIPPO.getExpireTime().getTime() - System.currentTimeMillis();
                    if (expireTime > 0) {
                        redisTemplate.opsForValue().set(key, "1", expireTime, TimeUnit.MILLISECONDS);
                        log.debug("IP {} 加入Redis黑名单缓存", blacklistIPPO.getIp());
                    }
                }
            }
        } catch (Exception e) {
            log.error("初始化黑名单缓存异常", e);
        }
    }
    
    /**
     * 清除Redis中的黑名单缓存
     */
    private void clearBlacklistCache() {
        try {
            // 查找所有黑名单键
            var keys = redisTemplate.keys(IP_BLACKLIST_PREFIX + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("已清除Redis中的黑名单缓存");
            }
        } catch (Exception e) {
            log.error("清除黑名单缓存异常", e);
        }
    }
    
    /**
     * 检查IP是否在黑名单中
     * @param ip IP地址
     * @return 是否在黑名单中
     */
    @Override
    public boolean isIpBlacklisted(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        try {
            // 先查询Redis缓存
            String key = IP_BLACKLIST_PREFIX + ip;
            Boolean exists = redisTemplate.hasKey(key);
            if (Boolean.TRUE.equals(exists)) {
                log.info("IP {} 在Redis黑名单中", ip);
                return true;
            }
            
            // 再查询数据库
            if (blacklistIPMapper != null) {
                BlacklistIPPO blacklistIPPO = blacklistIPMapper.findValidByIp(ip);
                if (blacklistIPPO != null) {
                    // 计算剩余过期时间
                    long expireTime = blacklistIPPO.getExpireTime().getTime() - System.currentTimeMillis();
                    if (expireTime > 0) {
                        // 添加到Redis缓存
                        redisTemplate.opsForValue().set(key, "1", expireTime, TimeUnit.MILLISECONDS);
                        log.info("IP {} 在数据库黑名单中，已同步到Redis", ip);
                        return true;
                    }
                }
            }
            
            return false;
        } catch (Exception e) {
            log.error("检查IP黑名单异常", e);
            return false;
        }
    }
    
    /**
     * 添加IP到黑名单
     * @param ip IP地址
     * @param reason 原因
     * @param duration 持续时间（秒）
     */
    @Override
    public void addToBlacklist(String ip, String reason, int duration) {
        if (ip == null || ip.isEmpty()) {
            return;
        }
        
        try {
            // 添加到Redis
            String key = IP_BLACKLIST_PREFIX + ip;
            redisTemplate.opsForValue().set(key, "1", duration, TimeUnit.SECONDS);
            log.info("IP {} 已添加到Redis黑名单，持续时间 {} 秒", ip, duration);
            
            // 添加到数据库
            if (blacklistIPMapper != null) {
                BlacklistIPPO blacklistIPPO = new BlacklistIPPO();
                blacklistIPPO.setIp(ip);
                blacklistIPPO.setReason(reason);
                blacklistIPPO.setCreateTime(new Date());
                
                // 计算过期时间
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.SECOND, duration);
                blacklistIPPO.setExpireTime(calendar.getTime());
                
                blacklistIPPO.setStatus(1);
                blacklistIPPO.setViolationCount(getViolationCount(ip));
                
                blacklistIPMapper.insert(blacklistIPPO);
                log.info("IP {} 已添加到数据库黑名单", ip);
            }
        } catch (Exception e) {
            log.error("添加IP到黑名单异常", e);
        }
    }
    
    /**
     * 从黑名单中移除IP
     * @param ip IP地址
     */
    @Override
    public void removeFromBlacklist(String ip) {
        if (ip == null || ip.isEmpty()) {
            return;
        }
        
        try {
            // 从Redis删除
            String key = IP_BLACKLIST_PREFIX + ip;
            redisTemplate.delete(key);
            log.info("IP {} 已从Redis黑名单中移除", ip);
            
            // 从数据库移除（逻辑删除）
            if (blacklistIPMapper != null) {
                BlacklistIPPO blacklistIPPO = blacklistIPMapper.findValidByIp(ip);
                if (blacklistIPPO != null) {
                    blacklistIPMapper.updateStatus(blacklistIPPO.getId(), 0);
                    log.info("IP {} 已从数据库黑名单中移除", ip);
                }
            }
        } catch (Exception e) {
            log.error("从黑名单移除IP异常", e);
        }
    }

    /**
     * 获取所有黑名单IP
     * @return 黑名单IP列表
     */
    @Override
    public List<BlacklistIPPO> getAllBlacklist() {
        if (blacklistIPMapper != null) {
            return blacklistIPMapper.findAllValid();
        }
        return List.of();
    }

    /**
     * 记录IP违规
     * @param ip IP地址
     * @param reason 原因
     * @return 是否记录成功
     */
    @Override
    public boolean recordViolation(String ip, String reason) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        try {
            // 构建违规计数 key
            String violationsKey = IP_VIOLATIONS_PREFIX + ip;
            
            // 增加违规计数
            Long violations = redisTemplate.opsForValue().increment(violationsKey, 1);
            
            // 设置过期时间（1小时）
            if (violations != null && violations == 1) {
                // 设置过期时间（1小时）
                redisTemplate.expire(violationsKey, 3600, TimeUnit.SECONDS);
            }

            // 获取黑名单阈值，默认5次
            int threshold = configService.getConfigValue(BLACKLIST_THRESHOLD_KEY, 5);
            // 获取黑名单持续时间，默认3天
            int duration = configService.getConfigValue(BLACKLIST_DURATION_KEY, 259200);
            
            // 检查是否超过黑名单阈值
            if (violations != null && violations >= threshold) {
                // 添加到黑名单
                addToBlacklist(ip, reason, duration);
                log.warn("IP {} 违规次数达到阈值 {}，加入黑名单 {} 秒", ip, violations, duration);
                return true;
            } else {
                log.info("IP {} 违规次数: {}/{}", ip, violations, threshold);
                return false;
            }
        } catch (Exception e) {
            log.error("记录IP违规异常", e);
            return false;
        }
    }
    
    /**
     * 获取IP违规次数
     * @param ip IP地址
     * @return 违规次数
     */
    private Integer getViolationCount(String ip) {
        try {
            String violationsKey = IP_VIOLATIONS_PREFIX + ip;
            String value = redisTemplate.opsForValue().get(violationsKey);
            return value != null ? Integer.parseInt(value) : 0;
        } catch (Exception e) {
            log.error("获取IP违规次数异常", e);
            return 0;
        }
    }

    /**
     * 清理过期黑名单
     */
    @Scheduled(cron = "0 0 0 * * ?") // 每天零点执行
    @Override
    public void cleanExpiredBlacklist() {
        log.info("开始清理过期黑名单");
        if (blacklistIPMapper != null) {
            try {
                int count = blacklistIPMapper.cleanExpired();
                log.info("已清理 {} 条过期黑名单记录", count);
            } catch (Exception e) {
                log.error("清理过期黑名单异常", e);
            }
        }
    }
} 