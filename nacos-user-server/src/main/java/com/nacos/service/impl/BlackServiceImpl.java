package com.nacos.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.business.db.mapper.BlackMapper;
import com.business.db.model.dto.BlackQueryDTO;
import com.business.db.model.po.BlacklistPO;
import com.business.db.model.vo.BlacklistVO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;
import com.nacos.service.IBlackService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 黑名单
 * @className: BlackServiceImpl
 * @author: Admin
 * @createDate: 2023-10-12
 * @version: 1.0
 *
 */

@Service
@Transactional(rollbackFor = Exception.class)
public class BlackServiceImpl extends ServiceImpl<BlackMapper, BlacklistPO> implements IBlackService {

    @Override
    public Result<Page<BlacklistVO>> queryPage(BlackQueryDTO dto) {
        Page<BlacklistVO> page = new Page<>(dto.getPageNumber(),dto.getPageSize());
        return Result.SUCCESS(this.baseMapper.queryPage(page, dto));
    }

    @Override
    public Result<Boolean> add(BlacklistPO blacklistPO) {
        return Result.SUCCESS(this.save(blacklistPO));
    }

    @Override
    public Result<Boolean> update(BlacklistPO blacklistPO) {
        return Result.SUCCESS(this.saveOrUpdate(blacklistPO));
    }

    @Override
    public Result<Boolean> delete(BaseDeleteEntity params) {
        return Result.SUCCESS(this.removeByIds(params.getIds()));
    }

}
