package com.nacos.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.mapper.*;
import com.business.db.model.dto.*;
import com.business.db.model.dto.draw.MjStyleConfigQueryDTO;
import com.business.db.model.po.*;
import com.business.db.model.po.draw.ImgDrawStyleClassifyLabelPO;
import com.business.db.model.po.draw.ImgDrawStyleConfigPO;
import com.business.db.model.vo.*;
import com.business.db.model.vo.draw.MjStyleConfigQueryVO;
import com.business.enums.BIntEnum;
import com.business.enums.BMyGalleryTypeEnum;
import com.business.enums.BRedisKeyEnum;
import com.business.enums.BResultEnum;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.business.model.po.ImgDrawRecordPO;
import com.business.utils.TaskM2MUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.ddimg.ImgDrawUtil;
import com.nacos.enums.*;
import com.nacos.exception.IBusinessException;
import com.nacos.mjapi.MJCommonEnum;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.DrawService;
import com.nacos.utils.DateUtil;
import com.nacos.utils.JwtUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.util.StringUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
public class DrawServiceImpl implements DrawService {

    @Resource
    private UserDDRecordMapper userDDRecordMapper;
    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;//绘图记录
    @Resource
    private ImgDrawDetlMapper imgDrawDetlMapper;//绘图详情
    @Resource
    private PayRecordMapper payRecordMapper;

    @Resource
    private ImgDrawStyleConfigMapper imgDrawStyleConfigMapper;

    @Resource
    private ImgDrawStyleConfigClassifyMapper imgDrawStyleConfigClassifyMapper;

    @Autowired
    private UserPrivateConfigMapper userPrivateConfigMapper;

    @Resource
    private ImgCollectMapper imgCollectMapper;

    @Resource
    private DrawGalleryMapper drawGalleryMapper;


    @Override
    public Result<IPage<ImgDrawHistoryVO>> historyRecord(ImgDrawHistoryDTO imgDrawHistoryDTO) {
        List<ImgDrawHistoryVO> imgDrawHistoryVOS = new ArrayList<>();

        List<Integer> funTypeList = null;
        if (imgDrawHistoryDTO.getFunType() != null) {
            funTypeList = Arrays.asList(imgDrawHistoryDTO.getFunType());
        } else {
            funTypeList = Arrays.asList(
                    ImgDrawEnum.FUN_TYPE_DRAW.getValue(),
                    ImgDrawEnum.FUN_TYPE_VIDEO.getValue(),
                    ImgDrawEnum.FUN_TYPE_AUDIO.getValue(),
                    ImgDrawEnum.FUN_TYPE_HEIGHT.getValue(),
                    ImgDrawEnum.FUN_TYPE_SKETCH.getValue(),
                    ImgDrawEnum.FUN_TYPE_STRUCTURE.getValue(),
                    ImgDrawEnum.FUN_TYPE_INPAINT.getValue(),
                    ImgDrawEnum.FUN_TYPE_INPAINTING.getValue(),
                    ImgDrawEnum.FUN_TYPE_SCALING.getValue()
            );
        }
        Long userId = JwtUtil.getUserId();
        /*PayRecordPO payRecordPO = payRecordMapper.selectOne(
                new LambdaQueryWrapper<PayRecordPO>()
                        .eq(PayRecordPO::getUserId, userId)
                        .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                        .in(PayRecordPO::getOrderType, PayRecordEnum.ORDER_TYPE_VIP.getIntValue(), PayRecordEnum.ORDER_TYPE_SVIP.getIntValue())
                        .ge(PayRecordPO::getExpirationTime, BDateUtil.getDateNowShanghai())
                        .orderByDesc(PayRecordPO::getExpirationTime)
                        .last("LIMIT 0,1")
        );*/
        /*Integer historyDays = null;
        List<UserRightsConfigBO> userRightsConfigBOList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.VIP_RIGHTS_CONFIG.getKey()), new TypeReference<List<UserRightsConfigBO>>() {
        });
        UserRightsConfigBO userRightsConfig = com.business.utils.ImgDrawUtil.getUserRightsConfigById(userRightsConfigBOList, BUserRightsConfigEnum.RIGHTS_HISTORY.getIntValue());*/

        // 新版的会员权益并发数
        /*if (payRecordPO != null && payRecordPO.getVipConfigId() != null) {
            historyDays = -1;
        } else {
            historyDays = userRightsConfig.getNotPackageValue();
        }*/

        //获取redis历史记录: 按时间倒序
        Page<ImgDrawRecordPO> imgDrawRecordPOPage = new Page<>(imgDrawHistoryDTO.getPageNumber(), imgDrawHistoryDTO.getPageSize());
        // if (historyDays == -1) {
        imgDrawRecordPOPage = imgDrawRecordMapper.selectPage(
                imgDrawRecordPOPage,
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getUserId, userId)
                        .ne(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
                        .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .in(ImgDrawRecordPO::getFunType, funTypeList)
                        .orderByDesc(ImgDrawRecordPO::getCreateTime)
        );
        // }
        /*else {
            imgDrawRecordPOPage = imgDrawRecordMapper.selectPage(
                    imgDrawRecordPOPage,
                    new LambdaQueryWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getUserId, userId)
                            .ne(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_FAIL.getValue())
                            .eq(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                            .ge(ImgDrawRecordPO::getCreateTime, DateUtil.getDateByMinusDays(historyDays))
                            .le(ImgDrawRecordPO::getCreateTime, DateUtil.getDateNowShanghai())
                            .in(ImgDrawRecordPO::getFunType, funTypeList)
                            .orderByDesc(ImgDrawRecordPO::getCreateTime)
            );
        }*/
        if (imgDrawRecordPOPage.getRecords() != null && !imgDrawRecordPOPage.getRecords().isEmpty()) {
            for (ImgDrawRecordPO record : imgDrawRecordPOPage.getRecords()) {
                ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
                imgDrawHistoryVO.setId(record.getId());
                List<String> imgUrls = new ArrayList<>();
                imgUrls.add(record.getInitImgUrls());
                imgDrawHistoryVO.setInitImgUrls(imgUrls);
                imgDrawHistoryVO.setFunType(record.getFunType());
                imgDrawHistoryVO.setModeAttribute(record.getModeAttribute());
                imgDrawHistoryVO.setOptAttribute(record.getOptAttribute());
                imgDrawHistoryVO.setOriginalImgId(record.getOriginalImgId());
                if (record.getOptAttribute() == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_2X.getValue()
                        || record.getOptAttribute() == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_4X.getValue()
                        || record.getOptAttribute() == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_2X_SUBTLE.getValue()
                        || record.getOptAttribute() == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_2X_CREATIVE.getValue()
                ) {

                    ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectOne(
                            new LambdaQueryWrapper<ImgDrawDetlPO>()
                                    .eq(ImgDrawDetlPO::getId, record.getOriginalImgId())
                                    .last("OR (deleted = 1 AND id = " + record.getOriginalImgId() + ")")
                    );
                    if (imgDrawDetlPO != null) {
                        imgDrawHistoryVO.setOriginalImgUrl(imgDrawDetlPO.getImgUrl());
                    }
                }
                imgDrawHistoryVO.setTimeTitle(ImgDrawUtil.getTimeTitle(record.getCreateTime()));
                imgDrawHistoryVO.setOptTitleOne(ImgOptModelEnum.getOptTitleOne(record.getOptAttribute()));
                if (record.getOptAttribute() == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_ZOOM_CUSTOM.getValue()
                        || record.getOptAttribute() == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_ZOOM_CHANGE_AR.getValue()) {
                    imgDrawHistoryVO.setOptTitleTwo(record.getOptDescribe() == null ? ImgOptModelEnum.getOptTitleTwo(record.getOptAttribute()) : record.getOptDescribe());
                } else {
                    imgDrawHistoryVO.setOptTitleTwo(ImgOptModelEnum.getOptTitleTwo(record.getOptAttribute()));
                }
                imgDrawHistoryVO.setPrompt(record.getPromptInit());
                imgDrawHistoryVO.setPromptUse(record.getPromptUse());

                imgDrawHistoryVO.setStatus(record.getStatus());
                imgDrawHistoryVO.setImgQuantity(record.getImgQuantity());
                imgDrawHistoryVO.setWhDivide(record.getWhDivide());

                //装载音频信息：
                imgDrawHistoryVO.setAudioTitle(record.getAudioTitle());
                imgDrawHistoryVO.setAudioStyle(record.getAudioStyle());
                imgDrawHistoryVO.setAudioLyric(record.getAudioLyric());
                imgDrawHistoryVO.setAudioTaskInfo(TaskM2MUtil.ImgDrawRecordPO2AudioTaskInfo(record));
                imgDrawHistoryVO.setMusicOssUrl(record.getInitImgUrls());
                imgDrawHistoryVO.setContinueAt(record.getOriginalImgId());
                imgDrawHistoryVO.setJobId(record.getMjJobId());

                imgDrawHistoryVO.setInitImgObject(record.getInitImgObject());
                imgDrawHistoryVO.setInitImgUrls(ImgDrawUtil.getInitImgUrlsList(record.getInitImgUrls()));
                imgDrawHistoryVO.setFinishTime(record.getFinishTime() != null ? DateUtil.longToLocalDateTimeStr(record.getFinishTime()) : null);
                imgDrawHistoryVO.setIsPublish(record.getIsPublish());
                imgDrawHistoryVO.setRemark(record.getRemark());
                List<ImgDrawDetlPO> imgDrawDetlPOList = imgDrawDetlMapper.selectList(
                        new LambdaQueryWrapper<ImgDrawDetlPO>()
                                .eq(ImgDrawDetlPO::getDrawRecordId, record.getId())
                                .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                                .isNotNull(ImgDrawDetlPO::getImgUrl)
                                .orderByAsc(ImgDrawDetlPO::getImgIndex)
                );
                List<ImgDrawDetlVO> imgDrawDetlVOS = getImgDrawDetlVOS(imgDrawDetlPOList, imgDrawHistoryVO);
                imgDrawHistoryVO.setImgDrawDetls(imgDrawDetlVOS);
                imgDrawHistoryVOS.add(imgDrawHistoryVO);
            }
        }
        IPage<ImgDrawHistoryVO> ImgDrawHistoryVOPage = new Page<>();
        ImgDrawHistoryVOPage.setRecords(imgDrawHistoryVOS);
        ImgDrawHistoryVOPage.setCurrent(imgDrawRecordPOPage.getCurrent());
        ImgDrawHistoryVOPage.setTotal(imgDrawRecordPOPage.getTotal());
        ImgDrawHistoryVOPage.setPages(imgDrawRecordPOPage.getPages());
        ImgDrawHistoryVOPage.setSize(imgDrawRecordPOPage.getSize());
        return Result.SUCCESS(ImgDrawHistoryVOPage);
    }

    @Override
    public Result<Page<ImgCommunityVO>> queryHomePage(CommunityQueryDTO dto) {
        long total = 5000;
        if (!Objects.isNull(dto.getPrompt()) && !dto.getPrompt().isEmpty() && !Objects.isNull(dto.getPainterId())) {
            total = imgDrawRecordMapper.queryHomeListTotal(dto);
        }
        if (total == 5000 && Objects.equals(dto.getImageTagId(), 2L)) {
            dto.setPrompt("新年");
            total = 2000;
        }
        if (total == 5000 && Objects.equals(dto.getImageTagId(), 3L)) {
            dto.setPrompt("汉服");
            total = 2000;
        }
        if (total == 5000 && Objects.equals(dto.getImageTagId(), 4L)) {
            dto.setPrompt("摄影");
            total = 2000;
        }
        Page<ImgCommunityVO> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        page.setTotal(total);
        List<ImgCommunityVO> imgCommunityVOList = imgDrawRecordMapper.queryHomeList(dto);
        if (imgCommunityVOList != null && !imgCommunityVOList.isEmpty()) {
            Collections.shuffle(imgCommunityVOList);
            page.setRecords(imgCommunityVOList);
        }

        return Result.SUCCESS(page);
    }

    @NotNull
    private static List<ImgDrawDetlVO> getImgDrawDetlVOS(List<ImgDrawDetlPO> imgDrawDetlPOList, ImgDrawHistoryVO imgDrawHistoryVO) {
        List<ImgDrawDetlVO> imgDrawDetlVOS = new ArrayList<>();
        if (imgDrawDetlPOList != null && !imgDrawDetlPOList.isEmpty()) {
            for (ImgDrawDetlPO imgDrawDetlPO : imgDrawDetlPOList) {
                ImgDrawDetlVO imgDrawDetlVO = new ImgDrawDetlVO();
                imgDrawDetlVO.setId(imgDrawDetlPO.getId());
                imgDrawDetlVO.setDrawRecordId(imgDrawDetlPO.getDrawRecordId());
                if (imgDrawDetlPO.getCreateTime().compareTo(com.nacos.utils.DateUtil.getStringToDate("2024-02-07 00:00:00")) < 0) {
                    imgDrawDetlVO.setOptAttribute(ImgOptModelEnum.DALLE_OPT_ATTRIBUTE_DRAW.getValue());
                } else {
                    imgDrawDetlVO.setOptAttribute(imgDrawDetlPO.getOptAttribute());
                }
                imgDrawDetlVO.setModeAttribute(imgDrawDetlPO.getModeAttribute());
                imgDrawDetlVO.setImgIndex(imgDrawDetlPO.getImgIndex());
                imgDrawDetlVO.setWhDivide(imgDrawDetlPO.getWhDivide());
                imgDrawDetlVO.setImgWidth(imgDrawDetlPO.getImgWidth());
                imgDrawDetlVO.setImgHeight(imgDrawDetlPO.getImgHeight());
                imgDrawDetlVO.setImgHue(imgDrawDetlPO.getImgHue());
                imgDrawDetlVO.setImgSize(imgDrawDetlPO.getImgSize());
                imgDrawDetlVO.setImgSourceUrl(imgDrawDetlPO.getImgSourceUrl());
                imgDrawDetlVO.setImgUrl(imgDrawDetlPO.getImgUrl());
                imgDrawDetlVO.setImgType(imgDrawDetlPO.getImgType());
                imgDrawDetlVO.setIsPublish(imgDrawDetlPO.getIsPublish());
                imgDrawDetlVO.setAudioUrl(imgDrawDetlPO.getAudioUrl());
                imgDrawDetlVO.setVideoUrl(imgDrawDetlPO.getVideoUrl());
                if (Objects.equals(imgDrawDetlPO.getIsPublish(), BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue())) {
                    imgDrawHistoryVO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue());
                }
                if (imgDrawHistoryVO.getOptAttribute() == ImgOptModelEnum.GOAPI_OPT_ATTRIBUTE_UPSCALE_2X.getValue()
                        || imgDrawHistoryVO.getOptAttribute() == ImgOptModelEnum.GOAPI_OPT_ATTRIBUTE_UPSCALE_4X.getValue()
                        || imgDrawHistoryVO.getOptAttribute() == ImgOptModelEnum.GOAPI_OPT_ATTRIBUTE_UPSCALE_8X.getValue()
                        || imgDrawHistoryVO.getOptAttribute() == ImgOptModelEnum.LE_OPT_ATTRIBUTE_UPSCALE_REDRAW.getValue()
                        || imgDrawHistoryVO.getOptAttribute() == ImgOptModelEnum.LE_OPT_ATTRIBUTE_SKETCH_PAINT.getValue()) {
                    imgDrawHistoryVO.setOriginalImgUrl(imgDrawDetlPO.getImgSourceUrl());
                }
                imgDrawDetlVOS.add(imgDrawDetlVO);
            }
        }
        return imgDrawDetlVOS;
    }

    private static Integer getNumberGeneratedImg(Integer optAttribute) {
        if (optAttribute == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_2X.getValue()
                || optAttribute == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_UPSCALE_4X.getValue()
                || optAttribute == ImgOptModelEnum.DALLE_OPT_ATTRIBUTE_DRAW.getValue()) {
            return ImgDrawEnum.IMG_NUMBER_ONE.getValue();
        }
        return ImgDrawEnum.IMG_NUMBER_FOUR.getValue();
    }

    @Override
    public Result<String> historyVip(Integer languageTagId) {
        Long count = userDDRecordMapper.selectCount(
                new LambdaQueryWrapper<UserDDRecordPO>()
                        .eq(UserDDRecordPO::getUserId, JwtUtil.getUserId())
                        .eq(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_PAY.getIntValue())
                        .in(UserDDRecordPO::getTypeItem, UserDDrecordEnum.TYPE_ITEM_PAY_VIP.getIntValue(), UserDDrecordEnum.TYPE_ITEM_PAY_SVIP.getIntValue())
                        .gt(UserDDRecordPO::getExpirationTime, DateUtil.getDateNowShanghai())
        );
        if (count > 0) {
            return Result.SUCCESS();
        }
        return Objects.equals(languageTagId, CommonIntEnum.LANGUAGE_CN.getIntValue()) ?
                Result.SUCCESS("点点会为您保存7天的任务，开通会员可延长保存时间") :
                Result.SUCCESS("Diandian will save tasks for you for 7 days, and opening a membership can extend the save time");
    }

    @Override
    public Result<Long> historyRecordStateQuantity() {
        List<Integer> funTypeList = Arrays.asList(ImgDrawEnum.FUN_TYPE_DRAW.getValue(), ImgDrawEnum.FUN_TYPE_VIDEO.getValue(),
                ImgDrawEnum.FUN_TYPE_AUDIO.getValue(), ImgDrawEnum.FUN_TYPE_HEIGHT.getValue(), ImgDrawEnum.FUN_TYPE_SKETCH.getValue(),
                ImgDrawEnum.FUN_TYPE_STRUCTURE.getValue(), ImgDrawEnum.FUN_TYPE_INPAINT.getValue(), ImgDrawEnum.FUN_TYPE_INPAINTING.getValue(),
                ImgDrawEnum.FUN_TYPE_SCALING.getValue()
        );
        Long count = imgDrawRecordMapper.selectCount(
                new LambdaQueryWrapper<ImgDrawRecordPO>()
                        .eq(ImgDrawRecordPO::getUserId, JwtUtil.getUserId())
                        .in(ImgDrawRecordPO::getFunType, funTypeList)
                        .in(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_QUEUING.getValue(), ImgDrawEnum.STATUS_IN_PROGRESS.getValue())
        );
        return Result.SUCCESS(count == null ? 0 : count);
    }

    @Override
    public Result<Integer> saveToGallery(Long imgDetlId) {
        return Result.SUCCESS(imgDrawDetlMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawDetlPO::getId, imgDetlId)
                        .set(ImgDrawDetlPO::getIsSave, CommonIntEnum.SHOW_TRUE.getIntValue())));
    }

    @Override
    public Result<Object> optIsPublishRecord(Long imgDrawRecordId) throws IBusinessException {
        Long userId = JwtUtil.getUserId();
        List<ImgDrawDetlPO> imgDrawDetlPOList = imgDrawDetlMapper.selectList(
                new LambdaQueryWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawRecordId)
                        .eq(ImgDrawDetlPO::getUserId, userId)
        );
        if (imgDrawDetlPOList == null || imgDrawDetlPOList.isEmpty()) {
            return Result.ERROR(BResultEnum.ERROR_FAILED.getMsg());
        }
        int isPublishDefalte = BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue();
        for (ImgDrawDetlPO imgDrawDetlPO : imgDrawDetlPOList) {
            if (Objects.equals(imgDrawDetlPO.getIsPublish(), BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue())) {
                isPublishDefalte = BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue();
                break;
            }
        }
        if (isPublishDefalte == BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue()) {
            //如果是打开状态，校验一下是否为vip，非vip禁止操作
            Integer queryUserGrade = payRecordMapper.queryUserGrade(userId);
            if (queryUserGrade == null) {
                return Result.ERROR(BResultEnum.ERROR_VIP.getMsg());
            }
        }
        //进行相反操作
        if (imgDrawDetlMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawRecordId)
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .set(ImgDrawDetlPO::getIsPublish, Objects.equals(isPublishDefalte, BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue()) ? BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue() : BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue())
        ) > 0) {
            return Result.SUCCESS(BResultEnum.SUCCESS_OPT.getMsg());
        }
        return Result.ERROR(BResultEnum.ERROR_OPT.getMsg());
    }

    @Override
    public Result<Object> optIsPublishDetl(Long imgDrawDetlId) throws IBusinessException {
        Long userId = JwtUtil.getUserId();
        ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectOne(
                new LambdaQueryWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(ImgDrawDetlPO::getId, imgDrawDetlId)
                        .eq(ImgDrawDetlPO::getUserId, userId)
        );
        if (imgDrawDetlPO == null || Objects.equals(imgDrawDetlPO.getDeleted(), CommonIntEnum.DELETED_TRUE.getIntValue())) {
            return Result.ERROR("图片详情不存在");
        }
        int isPublishDefalte = BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue();
        if (Objects.equals(imgDrawDetlPO.getIsPublish(), BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue())) {
            isPublishDefalte = BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue();
        }
        if (isPublishDefalte == BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue()) {
            //如果是打开状态，校验一下是否为vip，非vip禁止操作
            Integer queryUserGrade = payRecordMapper.queryUserGrade(userId);
            if (queryUserGrade == null) {
                return Result.ERROR(BResultEnum.ERROR_VIP.getMsg());
            }
        }
        //进行相反操作
        if (imgDrawDetlMapper.update(
                null,
                new LambdaUpdateWrapper<ImgDrawDetlPO>()
                        .eq(ImgDrawDetlPO::getId, imgDrawDetlId)
                        .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .set(ImgDrawDetlPO::getIsPublish, Objects.equals(isPublishDefalte, BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue()) ? BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue() : BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue())
        ) > 0) {
            return Result.SUCCESS(BResultEnum.SUCCESS_OPT.getMsg());
        }
        return Result.ERROR(BResultEnum.ERROR_OPT.getMsg());
    }

    @Override
    public Result<Page<ImgGalleryVO>> myGalleryPage(GalleryQueryDTO dto) {
        Page<ImgGalleryVO> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        if (ObjectUtil.isNull(dto.getPainterId())) {
            dto.setIsHimself(CommonIntEnum.SHOW_TRUE.getIntValue());
            dto.setPainterId(dto.getUserId());
        } else if (Objects.equals(dto.getPainterId(), dto.getUserId())) {
            dto.setIsHimself(CommonIntEnum.SHOW_TRUE.getIntValue());
        } else {
            dto.setIsHimself(CommonIntEnum.SHOW_FALSE.getIntValue());
        }
        if (dto.getPainterId().longValue() == dto.getUserId().longValue()) {
            return Result.SUCCESS(imgDrawDetlMapper.queryGalleryPage(page, dto));
        }

        LambdaQueryWrapper<UserPrivateConfigPO> lamb = new LambdaQueryWrapper<UserPrivateConfigPO>();
        lamb.eq(UserPrivateConfigPO::getUserId, dto.getPainterId());
        lamb.eq(UserPrivateConfigPO::getFunType, 1);
        UserPrivateConfigPO userPrivateConfigPO = userPrivateConfigMapper.selectOne(lamb);

        if (userPrivateConfigPO != null && userPrivateConfigPO.getIsPrivate() == 1) {
            //隐私权限
            return Result.SUCCESS("该用户已设置隐藏作品!", null);
        } else {
            //非隐私权限
            return Result.SUCCESS(imgDrawDetlMapper.queryGalleryPage(page, dto));
        }

    }

    @Override
    public Result<ImgCommunityVO> queryGalleryByImgId(GalleryInfoQueryDTO dto) {
        return Result.SUCCESS(imgDrawDetlMapper.queryGalleryByImgId(dto));
    }

    public Result<Map<String, List<GuidePageVO>>> guidePageList() {
        Map<String, List<GuidePageVO>> listMap = new HashMap<>();
        List<GuidePageVO> guidePageVOList = imgDrawRecordMapper.guidePageList();
        listMap.put("sixList", guidePageVOList.subList(0, 5));
        listMap.put("eightList", guidePageVOList.subList(6, 13));
        listMap.put("tenList", guidePageVOList.subList(14, 23));
        return Result.SUCCESS(listMap);
    }

    @Override
    public Result<ImgDrawHistoryVO> queryAImgDrawDetlVO(ImgDrawInfoDTO dto) {
        if (dto.getDrawRecordId() != null && dto.getDrawDetleId() != null) {
            LambdaQueryWrapper<ImgDrawDetlPO> queryWrapper = new LambdaQueryWrapper<ImgDrawDetlPO>()
                    .eq(ImgDrawDetlPO::getId, dto.getDrawDetleId());
            ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectById(dto.getDrawRecordId());
            return Result.SUCCESS(initImgDrawHistoryVO(imgDrawRecordPO, queryWrapper));
        }
        ImgDrawRecordPO imgDrawRecordPO = imgDrawRecordMapper.selectById(dto.getDrawRecordId());
        LambdaQueryWrapper<ImgDrawDetlPO> queryWrapper = new LambdaQueryWrapper<ImgDrawDetlPO>()
                .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawRecordPO.getId())
                .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                .orderByAsc(ImgDrawDetlPO::getImgIndex);
        return Result.SUCCESS(initImgDrawHistoryVO(imgDrawRecordPO, queryWrapper));
    }

    @Override
    public Result<Object> pullMjTaskInfos(List<String> mjJobIdList) throws Exception {
        List<ImgDrawHistoryVO> imgHistoryVOList = new ArrayList<ImgDrawHistoryVO>();
        if (mjJobIdList != null && mjJobIdList.size() > 0) {
            for (String mjJobId : mjJobIdList) {
                if (StringUtil.isBlank(mjJobId)) {
                    return Result.ERROR("mjJobId不能为空！");
                }
                //通过 jobId 获取 redis 缓存中的任务信息
                String jsonObjectStr = RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_FADE_IN_JOB_ID.getStrKey(), mjJobId));
                // 获取 json 中绘图详情信息
                if (StringUtil.isNotBlank(jsonObjectStr)) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ImgDrawHistoryVO imgHistoryVO = objectMapper.readValue(jsonObjectStr, ImgDrawHistoryVO.class);
                    imgHistoryVOList.add(imgHistoryVO);
                }
            }
        }
        return Result.SUCCESS(imgHistoryVOList);
    }

    private ImgDrawHistoryVO initImgDrawHistoryVO(ImgDrawRecordPO imgDrawRecordPO, LambdaQueryWrapper<ImgDrawDetlPO> queryWrapper) {
        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgDrawRecordPO.getId());
        imgDrawHistoryVO.setOriginalImgId(imgDrawRecordPO.getOriginalImgId());
        imgDrawHistoryVO.setFunType(imgDrawRecordPO.getFunType());
        imgDrawHistoryVO.setTimeTitle(ImgDrawUtil.getTimeTitle(imgDrawRecordPO.getCreateTime()));
        imgDrawHistoryVO.setOptTitleOne(ImgOptModelEnum.getOptTitleOne(imgDrawRecordPO.getOptAttribute()));
        if (imgDrawRecordPO.getOptAttribute() == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_ZOOM_CUSTOM.getValue()
                || imgDrawRecordPO.getOptAttribute() == ImgOptModelEnum.MJ_OPT_ATTRIBUTE_ZOOM_CHANGE_AR.getValue()) {
            imgDrawHistoryVO.setOptTitleTwo(imgDrawRecordPO.getOptDescribe() == null ? ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute()) : imgDrawRecordPO.getOptDescribe());
        } else {
            imgDrawHistoryVO.setOptTitleTwo(ImgOptModelEnum.getOptTitleTwo(imgDrawRecordPO.getOptAttribute()));
        }
        imgDrawHistoryVO.setOptAttribute(imgDrawRecordPO.getOptAttribute());
        imgDrawHistoryVO.setModeAttribute(imgDrawRecordPO.getModeAttribute());
        imgDrawHistoryVO.setPrompt(imgDrawRecordPO.getPromptInit());
        imgDrawHistoryVO.setPromptUse(imgDrawRecordPO.getPromptUse());
        imgDrawHistoryVO.setStatus(imgDrawRecordPO.getStatus());
        imgDrawHistoryVO.setImgQuantity(imgDrawRecordPO.getImgQuantity());
        imgDrawHistoryVO.setWhDivide(imgDrawRecordPO.getWhDivide());
        imgDrawHistoryVO.setInitImgUrls(ImgDrawUtil.getInitImgUrlsList(imgDrawRecordPO.getInitImgUrls()));
        imgDrawHistoryVO.setFinishTime(imgDrawRecordPO.getFinishTime() != null ? DateUtil.longToLocalDateTimeStr(imgDrawRecordPO.getFinishTime()) : null);
        imgDrawHistoryVO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue());
        imgDrawHistoryVO.setAudioTitle(imgDrawRecordPO.getAudioTitle());
        imgDrawHistoryVO.setAudioLyric(imgDrawRecordPO.getAudioLyric());
        imgDrawHistoryVO.setAudioStyle(imgDrawRecordPO.getAudioStyle());
        List<ImgDrawDetlPO> imgDrawDetlPOList = imgDrawDetlMapper.selectList(queryWrapper);
        List<ImgDrawDetlVO> imgDrawDetlVOS = getImgDrawDetlVOS(imgDrawDetlPOList, imgDrawHistoryVO);
        imgDrawHistoryVO.setImgDrawDetls(imgDrawDetlVOS);
        return imgDrawHistoryVO;
    }

    @Override
    public Result<LinkedHashMap<String, String>> getInstructMatching() throws Exception {
        LinkedHashMap<String, String> instructMap = new LinkedHashMap<String, String>();
        instructMap.put("--基础V2.1", "--v 6.1");
        instructMap.put("--基础V2", "--v 6.0");
        instructMap.put("--动漫V2", "--niji 6");
        instructMap.put("--基础", "--v 5.2");
        instructMap.put("--动漫", "--niji 5");
        instructMap.put("--灵动", "DALL·E 3");
        instructMap.put("--风格 真实", "--style raw");
        instructMap.put("--风格 可爱", "--style cute");
        instructMap.put("--风格 场景", "--style scenic");
        instructMap.put("--风格 原版", "--style original");
        instructMap.put("--风格 丰富", "--style expressive");
        instructMap.put("--风格化", "--s");
        instructMap.put("--比例", "--ar");
        instructMap.put("--慢速", "--relax");
        instructMap.put("--标准", "--fast");
        instructMap.put("--快速", "--turbo");
        instructMap.put("--v 6.0", "--v 6.0");
        instructMap.put("--aspect", "--ar");
        instructMap.put("--stylize", "--s");
        return Result.SUCCESS(instructMap);
    }

    @Override
    public Result<LinkedHashMap<String, Object>> getStyleClassifyList(Integer languageTagId) throws Exception {

        // mj风格 分类-标签 列表
        List<ImgDrawStyleClassifyLabelPO> imgDrawStyleClassifyPOList = imgDrawStyleConfigClassifyMapper.selectClassifyLabelLists();

        if (imgDrawStyleClassifyPOList.isEmpty()) return Result.SUCCESS(new LinkedHashMap<>());

        Map<String, Object> Map = new HashMap();
        Map.put("labelName", "全部");
        Long labelNum = 123456789L;
        for (ImgDrawStyleClassifyLabelPO img : imgDrawStyleClassifyPOList) {
            labelNum++;
            Map.put("labelId", labelNum);
            img.setLabelArray("[" + JSONObject.toJSONString(Map) + "," + img.getLabelArray() + "]");
        }

        String labelArray = imgDrawStyleClassifyPOList.get(0).getLabelArray();
        JSONArray parse = JSONArray.parse(labelArray);
        JSONObject jsonObject = parse.getJSONObject(0);
        Long labelId = jsonObject.getLong("labelId");
        --labelId;
        jsonObject.put("labelId", labelId);

        LinkedHashMap<String, Object> styleMap = new LinkedHashMap<String, Object>();


        List<ImgDrawStyleConfigPO> imgDrawStyleConfigPOS = imgDrawStyleConfigMapper.selectStyleConfigIdsByClassifyIdAndLabelId(null, null, 1);
        ImgDrawStyleClassifyLabelPO first = new ImgDrawStyleClassifyLabelPO();
        first.setClassifyId(123456789L);
        first.setClassifyName("推荐");
        JSONArray array = new JSONArray();
        array.add(jsonObject);
        first.setLabelArray(array.toString());


        imgDrawStyleClassifyPOList.add(0, first);
        styleMap.put("classifyId", imgDrawStyleClassifyPOList.get(0).getClassifyId());
        styleMap.put("labelId", labelId);
        if (imgDrawStyleConfigPOS != null && !imgDrawStyleConfigPOS.isEmpty()) {
            styleMap.put("styleConfigList", encapsulationMjStyleConfigQueryVOList(imgDrawStyleConfigPOS));
        }
        int num = 0;
        for (ImgDrawStyleClassifyLabelPO imgDrawStyleClassifyLabel : imgDrawStyleClassifyPOList) {
            String labelArray1 = imgDrawStyleClassifyLabel.getLabelArray();
            JSONArray parse1 = JSONArray.parse(labelArray1);
            for (int l = 0; l < parse1.size(); l++) {
                JSONObject jsonObject1 = parse1.getJSONObject(l);
                jsonObject1.put("labelNum", num++);
            }
            imgDrawStyleClassifyLabel.setLabelArray(JSONArray.toJSONString(parse1));
        }

        styleMap.put("styleClassifyList", imgDrawStyleClassifyPOList);
        return Result.SUCCESS(styleMap);
    }

    @Override
    public Result<IPage<MjStyleConfigQueryVO>> mjStylePage(MjStyleConfigQueryDTO mjStyleConfigQueryDTO) {
        String key = BRedisKeyEnum.REDIS_STYLE_CONFIG_PREFIX.getKey() + mjStyleConfigQueryDTO.getClassifyId() + "_" + mjStyleConfigQueryDTO.getLabelId() + "_" + mjStyleConfigQueryDTO.getPageNumber();
        String value = RedisUtil.getValue(key);
        if (StringUtils.isNotBlank(value)) {
            IPage<MjStyleConfigQueryVO> iPage = JSONObject.parseObject(value, IPage.class);
            return Result.SUCCESS(iPage);
        }

        if (null == mjStyleConfigQueryDTO.getClassifyId()) {
            return null;
        }
        List<ImgDrawStyleConfigPO> imgDrawStyleConfigPOS = new ArrayList<>();
        if (mjStyleConfigQueryDTO.getClassifyId() == 123456789L) {

            imgDrawStyleConfigPOS = imgDrawStyleConfigMapper.selectStyleConfigIdsByClassifyIdAndLabelId(null, null, 1);
        } else {
            if (mjStyleConfigQueryDTO.getLabelId() >= 123456789L && mjStyleConfigQueryDTO.getLabelId() <= 123456889L) {
                imgDrawStyleConfigPOS = imgDrawStyleConfigMapper.selectStyleConfigIdsByClassifyIdAndLabelId(mjStyleConfigQueryDTO.getClassifyId(), null, 0);
            } else {
                imgDrawStyleConfigPOS = imgDrawStyleConfigMapper.selectStyleConfigIdsByClassifyIdAndLabelId(mjStyleConfigQueryDTO.getClassifyId(), mjStyleConfigQueryDTO.getLabelId(), 0);
            }
        }

        if (imgDrawStyleConfigPOS.isEmpty()) {
            return Result.SUCCESS();
        }
        List<Long> list = imgDrawStyleConfigPOS.stream().map(ImgDrawStyleConfigPO::getId).toList();

        Page<ImgDrawStyleConfigPO> imgDrawStyleConfigPOPage = new Page<>(mjStyleConfigQueryDTO.getPageNumber(), mjStyleConfigQueryDTO.getPageSize());
        imgDrawStyleConfigPOPage = imgDrawStyleConfigMapper.selectPage(imgDrawStyleConfigPOPage, new LambdaQueryWrapper<ImgDrawStyleConfigPO>()
                .in(ImgDrawStyleConfigPO::getId, list)
                .in(ImgDrawStyleConfigPO::getType, Arrays.asList(1, 2))
                .eq(ImgDrawStyleConfigPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                .eq(ImgDrawStyleConfigPO::getIsUse, MJCommonEnum.IS_USE_TRUE.getIntValue())
                .orderByDesc(ImgDrawStyleConfigPO::getSort)
                .orderByDesc(ImgDrawStyleConfigPO::getCreateTime)
        );

        IPage<MjStyleConfigQueryVO> mjStyleConfigQueryVOIPage = new Page<>();

        mjStyleConfigQueryVOIPage.setRecords(getMjStyleConfigQueryVOS(imgDrawStyleConfigPOPage));
        mjStyleConfigQueryVOIPage.setCurrent(imgDrawStyleConfigPOPage.getCurrent());
        mjStyleConfigQueryVOIPage.setTotal(imgDrawStyleConfigPOPage.getTotal());
        mjStyleConfigQueryVOIPage.setPages(imgDrawStyleConfigPOPage.getPages());
        mjStyleConfigQueryVOIPage.setSize(imgDrawStyleConfigPOPage.getSize());

        RedisUtil.setValueSeconds(key, JSONObject.toJSONString(mjStyleConfigQueryVOIPage), 10, TimeUnit.MINUTES);

        return Result.SUCCESS(mjStyleConfigQueryVOIPage);
    }


    @Override
    public Result<Object> mjStyleOnVideo() {
        List<ImgDrawStyleConfigPO> imgDrawStyleConfigPOS = imgDrawStyleConfigMapper.selectList(new LambdaQueryWrapper<ImgDrawStyleConfigPO>()
                .eq(ImgDrawStyleConfigPO::getType, 3)
                .eq(ImgDrawStyleConfigPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                .eq(ImgDrawStyleConfigPO::getIsUse, MJCommonEnum.IS_USE_TRUE.getIntValue())
                .orderByDesc(ImgDrawStyleConfigPO::getSort)
                .orderByDesc(ImgDrawStyleConfigPO::getCreateTime)
        );
        return Result.SUCCESS(encapsulationMjStyleConfigQueryVOList(imgDrawStyleConfigPOS));
    }

    @Override
    public Result<Object> imgDrawDetlDeletes(List<Long> imgDrawDetlIds) {
        if (null == imgDrawDetlIds || imgDrawDetlIds.isEmpty()) {
            return Result.ERROR("请检查参数设置！");
        }
        for (Long imgDrawDetlId : imgDrawDetlIds) {
            //查询详情是否存在
            ImgDrawDetlPO imgDrawDetlPO = imgDrawDetlMapper.selectOne(
                    new LambdaQueryWrapper<ImgDrawDetlPO>()
                            .eq(ImgDrawDetlPO::getId, imgDrawDetlId)
                            .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
            );
            if (imgDrawDetlPO == null || ObjectUtil.isEmpty(imgDrawDetlPO)) {
                continue;
            }
            if (imgDrawDetlMapper.update(
                    null,
                    new LambdaUpdateWrapper<ImgDrawDetlPO>()
                            .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                            .eq(ImgDrawDetlPO::getId, imgDrawDetlId)
                            .set(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
            ) < 1) {
                continue;
//                return Result.ERROR("删除失败");
            }
            //查询任务列表详情是否存在
            List<ImgDrawDetlPO> imgDrawDetlPOList = imgDrawDetlMapper.selectList(
                    new LambdaQueryWrapper<ImgDrawDetlPO>()
                            .eq(ImgDrawDetlPO::getDrawRecordId, imgDrawDetlPO.getDrawRecordId())
                            .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
            );
            if (imgDrawDetlPOList != null && !imgDrawDetlPOList.isEmpty()) {
                continue;
//                return Result.SUCCESS();
            }
            if (imgDrawRecordMapper.update(
                    null,
                    new LambdaUpdateWrapper<ImgDrawRecordPO>()
                            .eq(ImgDrawRecordPO::getId, imgDrawDetlPO.getDrawRecordId())
                            .set(ImgDrawRecordPO::getDeleted, CommonIntEnum.DELETED_TRUE.getIntValue())
            ) < 1) {
                continue;
//                return Result.ERROR("删除失败");
            }
        }
        return Result.SUCCESS("批量修改成功！");
    }

    @Override
    public Result<Object> optIsPublishRecords(List<Long> imgDrawDetlIds) {
        Long userId = JwtUtil.getUserId();
        for (Long imgDrawDetlId : imgDrawDetlIds) {
            List<ImgDrawDetlPO> imgDrawDetlPOList = imgDrawDetlMapper.selectList(
                    new LambdaQueryWrapper<ImgDrawDetlPO>()
                            .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                            .eq(ImgDrawDetlPO::getId, imgDrawDetlId)
                            .eq(ImgDrawDetlPO::getUserId, userId)
            );
            if (imgDrawDetlPOList == null || imgDrawDetlPOList.isEmpty()) {
                continue;
//                return Result.ERROR(BResultEnum.ERROR_FAILED.getMsg());
            }
            int isPublishDefalte = BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue();
            for (ImgDrawDetlPO imgDrawDetlPO : imgDrawDetlPOList) {
                if (Objects.equals(imgDrawDetlPO.getIsPublish(), BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue())) {
                    isPublishDefalte = BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue();
                    break;
                }
            }
            if (isPublishDefalte == BIntEnum.IMG_DRAW_IS_PUBLISH_TRUE.getIntValue()) {
                //如果是打开状态，校验一下是否为vip，非vip禁止操作
                Integer queryUserGrade = payRecordMapper.queryUserGrade(userId);
                if (queryUserGrade == null) {
                    return Result.ERROR(BResultEnum.ERROR_VIP.getMsg());
                }
            }
            //进行相反操作
            if (imgDrawDetlMapper.update(
                    null,
                    new LambdaUpdateWrapper<ImgDrawDetlPO>()
                            .eq(ImgDrawDetlPO::getId, imgDrawDetlId)
                            .eq(ImgDrawDetlPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                            .set(ImgDrawDetlPO::getIsPublish, BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue())
            ) > 0) {
            }
        }
        return Result.SUCCESS(BResultEnum.SUCCESS_OPT.getMsg());
    }

    @Override
    public Result<?> collectWorks(List<ImgCollectDTO> dtoList) {
        Long userId = JwtUtil.getUserId();
        if (null == dtoList || dtoList.isEmpty()) {
            return Result.ERROR("参数为空！");
        }
        for (ImgCollectDTO dto : dtoList) {
            List<ImgCollectPO> galleryCollect = imgCollectMapper.selectList(
                    new LambdaQueryWrapper<ImgCollectPO>()
                            .eq(ImgCollectPO::getTaskId, dto.getTaskId())
                            .eq(ImgCollectPO::getImgId, dto.getImgId())
                            .eq(ImgCollectPO::getUserId, userId)
            );
            if (CollectionUtil.isEmpty(galleryCollect) && galleryCollect.size() == 0) {
                ImgCollectPO collect = new ImgCollectPO();
                collect.setUserId(dto.getUserId());
                collect.setTaskId(dto.getTaskId());
                collect.setImgId(dto.getImgId());
                imgCollectMapper.insert(collect);
            }
        }
        return Result.SUCCESS("收藏成功！");
    }

    @Override
    public Result<Page<ImgGalleryVO>> queryCollectPage(GalleryQueryDTO dto) {
        if (ObjectUtil.isNull(dto.getPainterId())) {
            dto.setPainterId(dto.getUserId());
        }
        Page<ImgGalleryVO> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        if (dto.getJobType() == BMyGalleryTypeEnum.COLLECT.getType()) {
            return Result.SUCCESS(drawGalleryMapper.queryCollectPage(page, dto));
        }
        return Result.SUCCESS(drawGalleryMapper.queryLikePage(page, dto));
    }

    private void sendMessage(Long userId, SysInteractionPO sysInteractionPO) {
        boolean state = BRedisServiceUtil.sendMessageMJ(BMessageSendUtil.getJSONStr(userId, BMessageSendEnum.INTERACTION_PUSH, JSONObject.toJSONString(sysInteractionPO)));
        log.info("互动消息推送状态:{}", state);
    }

    @NotNull
    private static List<MjStyleConfigQueryVO> getMjStyleConfigQueryVOS(Page<ImgDrawStyleConfigPO> imgDrawStyleConfigPOPage) {
        if (imgDrawStyleConfigPOPage.getRecords() != null && !imgDrawStyleConfigPOPage.getRecords().isEmpty()) {
            return encapsulationMjStyleConfigQueryVOList(imgDrawStyleConfigPOPage.getRecords());
        }
        return new ArrayList<>();
    }

    private static List<MjStyleConfigQueryVO> encapsulationMjStyleConfigQueryVOList(List<ImgDrawStyleConfigPO> imgDrawStyleConfigPOList) {
        List<MjStyleConfigQueryVO> mjStyleConfigQueryVOList = new ArrayList<>();
        for (ImgDrawStyleConfigPO record : imgDrawStyleConfigPOList) {
            MjStyleConfigQueryVO mjStyleConfigQueryVO = new MjStyleConfigQueryVO(record.getId(), record.getStyleName(), record.getStyleUrl());
            mjStyleConfigQueryVOList.add(mjStyleConfigQueryVO);
        }
        return mjStyleConfigQueryVOList;
    }

}
