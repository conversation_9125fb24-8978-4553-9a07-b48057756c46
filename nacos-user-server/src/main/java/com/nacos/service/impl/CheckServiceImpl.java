package com.nacos.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.mapper.*;
import com.business.db.model.bo.UserRightsConfigBO;
import com.business.db.model.po.*;
import com.business.db.model.vo.UserDDInfoVO;
import com.business.db.model.vo.UserDDRecordVO;
import com.business.enums.BRedisKeyEnum;
import com.business.enums.BUserRightsConfigEnum;
import com.business.utils.BDateUtil;
import com.business.utils.BMemberUtil;
import com.business.utils.ImgDrawUtil;
import com.nacos.base.BasePageHelper;
import com.nacos.enums.*;
import com.nacos.exception.IBusinessException;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.CheckService;
import com.nacos.utils.DateUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class CheckServiceImpl implements CheckService {

    @Resource
    private UserDDRecordMapper userDDRecordMapper;

    /* 会员等级 */
    @Resource
    private VipGradeMapper vipGradeMapper;

    @Resource
    private PayRecordMapper payRecordMapper;

    /* 枚举字典 */
    @Resource
    private DictConfigMapper dictConfigMapper;

    /* 点子变动记录 */
    @Resource
    private FlowRecordMapper flowRecordMapper;

    @Resource
    private UserRightsConfigMapper userRightsConfigMapper;

    @Override
    public Result<Boolean> getResidueDDDeduct(Long userId, Double deduct) {
        LambdaQueryWrapper<UserDDRecordPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserDDRecordPO::getUserId, userId);
        queryWrapper.ge(UserDDRecordPO::getExpirationTime, DateUtil.getDateNowShanghai());
        queryWrapper.and(i -> i.last("total > total_usage"));
        queryWrapper.orderByAsc(UserDDRecordPO::getExpirationTime)
        ;
        List<UserDDRecordPO> userDDRecordPOS = userDDRecordMapper.selectList(queryWrapper);
        log.info("用户点子余额 {}", userDDRecordPOS);
        if (userDDRecordPOS == null || userDDRecordPOS.isEmpty()) {
            return Result.ERROR("点子余额不足");
        }
        //装载是否存在回退的余额记录
        UserDDRecordPO userDDRecordPOReturn = null;
        List<UserDDRecordPO> userDDRecordPOActivityList = new ArrayList<>();
        List<UserDDRecordPO> userDDRecordPOTaskList = new ArrayList<>();
        List<UserDDRecordPO> userDDRecordPOPayList = new ArrayList<>();

        double total = 0;
        double totalUsage = 0;
        ///遍历4种类型
        for (UserDDRecordPO userDDRecordPO : userDDRecordPOS) {
            //记录一下总余额
            total = new BigDecimal(Double.toString(total)).add(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue();
            totalUsage = new BigDecimal(Double.toString(totalUsage)).add(new BigDecimal(userDDRecordPO.getTotalUsage().toString())).doubleValue();
            //回退余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_RETURN.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOReturn = userDDRecordPO;
            }
            //活动余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_ACTIVITY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOActivityList.add(userDDRecordPO);
            }
            //任务余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_TASK.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOTaskList.add(userDDRecordPO);
            }
            //支付余额
            if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_PAY.getIntValue()) && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordPOPayList.add(userDDRecordPO);
            }
        }
        double residue = new BigDecimal(total).subtract(new BigDecimal(totalUsage)).doubleValue();
        if (residue < deduct) {
            return Result.ERROR("点子余额不足");
        }
        //点子余额充足，扣除
        //1优先扣除回退点子数量
        if (userDDRecordPOReturn != null) {
            Result<Double> result = getUserDDRecordPODeduct(userDDRecordPOReturn, deduct);
            if (result.getData() == null) {
                return Result.ERROR("更新点点余额失败");
            } else if (result.getData() > 0) {
                deduct = result.getData();
            } else {
                return Result.SUCCESS(true);
            }
        }

        //活动余额不为空
        if (!userDDRecordPOActivityList.isEmpty() && deduct > 0) {
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOActivityList) {
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deduct);
                if (result.getData() == null) {
                    return Result.ERROR("更新点点余额失败");
                } else if (result.getData() > 0) {
                    deduct = result.getData();
                } else {
                    return Result.SUCCESS(true);
                }
            }
        }

        //任务余额不为空
        if (!userDDRecordPOTaskList.isEmpty() && deduct > 0) {
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOTaskList) {
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deduct);
                if (result.getData() == null) {
                    return Result.ERROR("更新点点余额失败");
                } else if (result.getData() > 0) {
                    deduct = result.getData();
                } else {
                    return Result.SUCCESS(true);
                }
            }
        }

        //支付余额不为空
        if (!userDDRecordPOPayList.isEmpty() && deduct > 0) {
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOPayList) {
                Result<Double> result = getUserDDRecordPODeduct(userDDRecordPO, deduct);
                if (result.getData() == null) {
                    return Result.ERROR("更新点点余额失败");
                } else if (result.getData() > 0) {
                    deduct = result.getData();
                } else {
                    return Result.SUCCESS(true);
                }
            }
        }
        return Result.ERROR("点点余额不足");
    }

    public Result<Double> getUserDDRecordPODeduct(UserDDRecordPO userDDRecordPO, Double deduct) {
        double returnDeduct = new BigDecimal(userDDRecordPO.getTotalUsage().toString()).add(new BigDecimal(deduct.toString())).doubleValue();
        LambdaUpdateWrapper<UserDDRecordPO> updateWrapper = Wrappers.lambdaUpdate(UserDDRecordPO.class);
        updateWrapper.eq(UserDDRecordPO::getUserId, userDDRecordPO.getUserId());
        updateWrapper.eq(UserDDRecordPO::getId, userDDRecordPO.getId());

        //足额被扣除：直接返回
        if (returnDeduct <= userDDRecordPO.getTotal()) {
            updateWrapper.set(UserDDRecordPO::getTotalUsage, returnDeduct);
            if (userDDRecordMapper.update(null, updateWrapper) > 0) {
                return Result.SUCCESS((double) 0);
            }
            return Result.ERROR("更新点点余额失败");
        }
        //非足额扣除
        updateWrapper.set(UserDDRecordPO::getTotalUsage, userDDRecordPO.getTotal());
        if (userDDRecordMapper.update(null, updateWrapper) < 1) {
            return Result.ERROR("更新点点余额失败");
        }
        return Result.SUCCESS(new BigDecimal(String.valueOf(returnDeduct)).subtract(new BigDecimal(userDDRecordPO.getTotal().toString())).doubleValue());
    }

    @Override
    public Result<Double> ddResidue(Long userId) {
        BigDecimal residue = new BigDecimal("0");
        try {
            LambdaQueryWrapper<UserDDRecordPO> queryWrapper = new LambdaQueryWrapper<UserDDRecordPO>()
                    .eq(UserDDRecordPO::getUserId, userId)
                    .ge(UserDDRecordPO::getExpirationTime, DateUtil.getDateNowShanghai())
                    .last("AND total > total_usage");
            List<UserDDRecordPO> userDDRecordPOS = userDDRecordMapper.selectList(queryWrapper);
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOS) {
                residue = residue.add(new BigDecimal(userDDRecordPO.getTotal().toString()).subtract(new BigDecimal(userDDRecordPO.getTotalUsage().toString())));
            }
        } catch (Exception e) {
            log.error("获取用户余额异常 {}", e.getMessage(), e);
        }
        return Result.SUCCESS(residue.doubleValue());
    }

    @Override
    public boolean setReturnDD(Long userId, Double ddQuantity) {
        LambdaQueryWrapper<UserDDRecordPO> queryWrapper = new LambdaQueryWrapper<UserDDRecordPO>()
                .eq(UserDDRecordPO::getUserId, userId)
                .eq(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_RETURN.getIntValue())
                .eq(UserDDRecordPO::getTypeItem, UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue());
        UserDDRecordPO userDDRecordPO = userDDRecordMapper.selectOne(queryWrapper);
        if (userDDRecordPO == null) {
            return userDDRecordMapper.insert(new UserDDRecordPO(
                    userId,
                    Long.valueOf(UserDDrecordEnum.SOURCE_ID_INVALID.getIntValue()),
                    UserDDrecordEnum.TYPE_RETURN.getIntValue(),
                    UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue(),
                    ddQuantity,
                    DateUtil.getDateAddMonth(DateUtil.getDateNowShanghai(), 600)
            )) > 0;
        }
        LambdaUpdateWrapper<UserDDRecordPO> updateWrapper = Wrappers.lambdaUpdate(UserDDRecordPO.class);
        updateWrapper.eq(UserDDRecordPO::getUserId, userId)
                .eq(UserDDRecordPO::getId, userDDRecordPO.getId())
                .eq(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_RETURN.getIntValue())
                .eq(UserDDRecordPO::getTypeItem, UserDDrecordEnum.TYPE_RETURN_ALL.getIntValue())
                .set(UserDDRecordPO::getTotal, new BigDecimal(userDDRecordPO.getTotal().toString()).add(new BigDecimal(ddQuantity.toString())).doubleValue())
                .set(UserDDRecordPO::getTotalUsage, userDDRecordPO.getTotalUsage());
        return userDDRecordMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public Integer getUserGrade(Long userId) {
        // 获取用户等级，条件：用户ID、支付状态为成功、订单类型不为加油包、订单过期时间大于当前时间、按过期时间降序排序
        List<PayRecordPO> payRecordPOS = payRecordMapper.selectList(
                new LambdaQueryWrapper<PayRecordPO>()
                        .eq(PayRecordPO::getUserId, userId)
                        .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                        .ne(PayRecordPO::getType, PayRecordEnum.ORDER_TYPE_JYB.getIntValue())
                        .gt(PayRecordPO::getExpirationTime, DateUtil.getDateNowShanghai())
                        .orderByDesc(PayRecordPO::getExpirationTime)
        );
        Integer grade = VipGradeEnum.MEMBER_PT.getIntValue();
        if (payRecordPOS != null && !payRecordPOS.isEmpty()) {
            for (PayRecordPO payRecordPO : payRecordPOS) {
                if (Objects.equals(payRecordPO.getOrderType(), PayRecordEnum.ORDER_TYPE_SVIP.getIntValue())) {
                    return VipGradeEnum.MEMBER_SVIP.getIntValue();
                }
                grade = VipGradeEnum.MEMBER_VIP.getIntValue();
            }
        }
        log.info("用户等级为 {}", grade);
        return grade;
    }

    @Override
    public Result<UserDDInfoVO> getUserDDInfo(Long userId) {
        UserDDInfoVO userDDInfoVO = new UserDDInfoVO();
        userDDInfoVO.setUserId(userId);
        userDDInfoVO.setVipGrade(VipGradeEnum.MEMBER_PT.getIntValue());
        userDDInfoVO.setVipGradeName(VipGradeEnum.MEMBER_PT.getStrValue());
        userDDInfoVO.setVipGradeShow("已过期");
        try {
            BigDecimal ddAllQuantity = new BigDecimal("0");
            BigDecimal ddUseQuantity = new BigDecimal("0");
            BigDecimal residue = new BigDecimal("0");

            // 查询点子记录数
            LambdaQueryWrapper<UserDDRecordPO> queryWrapper = new LambdaQueryWrapper<UserDDRecordPO>()
                    .eq(UserDDRecordPO::getUserId, userId)
                    .ge(UserDDRecordPO::getExpirationTime, DateUtil.getDateNowShanghai());
            List<UserDDRecordPO> userDDRecordPOS = userDDRecordMapper.selectList(queryWrapper);
            for (UserDDRecordPO userDDRecordPO : userDDRecordPOS) {
                residue = residue.add(new BigDecimal(userDDRecordPO.getTotal().toString()).subtract(new BigDecimal(userDDRecordPO.getTotalUsage().toString())));
               /* if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_PAY.getIntValue()) && Objects.equals(userDDRecordPO.getTypeItem(), UserDDrecordEnum.TYPE_ITEM_PAY_VIP.getIntValue())){
                    userDDInfoVO.setVipGrade(VipGradeEnum.MEMBER_VIP.getIntValue());
                    userDDInfoVO.setVipGradeName(VipGradeEnum.MEMBER_VIP.getStrValue());
                    userDDInfoVO.setVipExpirationTime(userDDRecordPO.getExpirationTime());
                }
                if (Objects.equals(userDDRecordPO.getType(), UserDDrecordEnum.TYPE_PAY.getIntValue()) && Objects.equals(userDDRecordPO.getTypeItem(), UserDDrecordEnum.TYPE_ITEM_PAY_SVIP.getIntValue())){
                    userDDInfoVO.setVipGrade(VipGradeEnum.MEMBER_SVIP.getIntValue());
                    userDDInfoVO.setVipGradeName(VipGradeEnum.MEMBER_SVIP.getStrValue());
                    userDDInfoVO.setVipExpirationTime(userDDRecordPO.getExpirationTime());
                }*/
                ddAllQuantity = ddAllQuantity.add(new BigDecimal(userDDRecordPO.getTotal().toString()));
                ddUseQuantity = ddUseQuantity.add(new BigDecimal(userDDRecordPO.getTotalUsage().toString()));
            }
            userDDInfoVO.setDdQuantity(residue.doubleValue());
            userDDInfoVO.setDdAllQuantity(ddAllQuantity.doubleValue());
            userDDInfoVO.setDdUseQuantity(ddUseQuantity.doubleValue());
            userDDInfoVO.setOrderQuantity(userDDRecordPOS.size());
            VipGradePO vipGradePO = vipGradeMapper.selectOne(new LambdaQueryWrapper<VipGradePO>().eq(VipGradePO::getGrade, userDDInfoVO.getVipGrade()));
            if (vipGradePO != null) {
                userDDInfoVO.setHistoryDays(vipGradePO.getHistoryRetention());
                // 老的会员并发数
                // userDDInfoVO.setDrawConcurrency(vipGradePO.getDrawConcurrency());
            }

            log.info("查询用户支付的最新信息，userId= {}", userId);
            // 查询充值记录，条件：用户ID、支付状态为成功、订单类型为会员或SVIP、按过期时间降序排序
            Long count = payRecordMapper.selectCount(new LambdaQueryWrapper<PayRecordPO>()
                    .eq(PayRecordPO::getUserId, userId)
                    .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                    .in(PayRecordPO::getOrderType, PayRecordEnum.ORDER_TYPE_VIP.getIntValue(), PayRecordEnum.ORDER_TYPE_SVIP.getIntValue())
                    .orderByDesc(PayRecordPO::getExpirationTime)
                    .last("LIMIT 0,1"));
            if (count > 0) {
                PayRecordPO payRecordPO = payRecordMapper.selectOne(
                        new LambdaQueryWrapper<PayRecordPO>()
                                .eq(PayRecordPO::getUserId, userId)
                                .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                                .in(PayRecordPO::getOrderType, PayRecordEnum.ORDER_TYPE_VIP.getIntValue(), PayRecordEnum.ORDER_TYPE_SVIP.getIntValue())
                                .ge(PayRecordPO::getExpirationTime, BDateUtil.getDateNowShanghai())
                                .orderByDesc(PayRecordPO::getExpirationTime)
                                .last("LIMIT 0,1")
                );
                // 新版的会员权益并发数
                if (payRecordPO == null) {
                    userDDInfoVO.setVipGradeShow("已过期");
                } else {
                    Long vipConfigId = payRecordPO.getVipConfigId();
                    List<UserRightsConfigBO> userRightsConfigBOList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.VIP_RIGHTS_CONFIG.getKey()), new TypeReference<List<UserRightsConfigBO>>() {
                    });
                    UserRightsConfigBO userRightsConfigBO = ImgDrawUtil.getUserRightsConfigById(userRightsConfigBOList, BUserRightsConfigEnum.RIGHTS_TASK_CONCURRENCY.getIntValue());
                    // UserRightsConfigPO userRightsConfigPO =  userRightsConfigMapper.selectById(BUserRightsConfigEnum.RIGHTS_TASK_CONCURRENCY.getIntValue());
                    userDDInfoVO.setDrawConcurrency(BMemberUtil.getMemberConcurrentCount(vipConfigId.intValue(), userRightsConfigBO));
                    if (payRecordPO.getExpirationTime() != null && DateUtil.getDateNowShanghai().compareTo(payRecordPO.getExpirationTime()) <= 0) {
                        if (payRecordPO.getVipConfigId().intValue() == 29) {
                            userDDInfoVO.setVipGradeName(VipGradeEnum.MEMBER_SVIP_LONG.getStrValue());
                            userDDInfoVO.setVipGradeShow("No.".concat(String.format("%03d", payRecordPO.getJiangxinNo() == null ? 88 : payRecordPO.getJiangxinNo())));
                        } else if (payRecordPO.getVipConfigId().intValue() == 99) {
                            userDDInfoVO.setVipGradeName(VipGradeEnum.MEMBER_SVIP_LIFELONG.getStrValue());
                            userDDInfoVO.setVipGradeShow("永久有效");
                        } else {
                            userDDInfoVO.setVipGradeName(VipGradeEnum.MEMBER_SVIP.getStrValue());
                            userDDInfoVO.setVipExpirationTime(payRecordPO.getExpirationTime());
                            userDDInfoVO.setVipGradeShow("会员到期 " + DateUtil.getToStringYYYY_MM_dd(userDDInfoVO.getVipExpirationTime()));
                        }
                    }
                }
            } else {
                userDDInfoVO.setVipGradeShow("立即开通 享会员权益");
            }
        } catch (Exception e) {
            log.error("获取用户余额异常 {}", e.getMessage(), e);
        }
        return Result.SUCCESS(userDDInfoVO);
    }

    @Override
    public Result<Page<UserDDRecordVO>> ddRecord(Long userId, BasePageHelper basePageHelper) {
        if (userId == null) {
            return Result.SUCCESS();
        }
        Page<UserDDRecordPO> page = new Page<>(basePageHelper.getPageNumber(), basePageHelper.getPageSize());
        LambdaQueryWrapper<UserDDRecordPO> queryWrapper = new LambdaQueryWrapper<UserDDRecordPO>()
                .eq(UserDDRecordPO::getUserId, userId)
                .ne(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_RETURN.getIntValue())
                .orderByDesc(UserDDRecordPO::getCreateTime);
        page = userDDRecordMapper.selectPage(page, queryWrapper);
        List<UserDDRecordPO> userDDRecordPOS = page.getRecords();
        //校验无数据直接返回
        if (userDDRecordPOS == null || userDDRecordPOS.isEmpty()) {
            return Result.SUCCESS();
        }
        //处理逻辑
        List<UserDDRecordVO> userDDRecordVOS = new ArrayList<>();
        for (UserDDRecordPO userDDRecordPO : userDDRecordPOS) {
            UserDDRecordVO userDDRecordVO = new UserDDRecordVO();
            //未过期，未用完: 使用中
            if (DateUtil.getDateNowShanghai().compareTo(userDDRecordPO.getExpirationTime()) < 0 && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordVO.setState(UserDDrecordEnum.STATE_INUSE.getIntValue());
            }
            // 已用完
            if (userDDRecordPO.getTotal() <= userDDRecordPO.getTotalUsage()) {
                userDDRecordVO.setState(UserDDrecordEnum.STATE_USEED.getIntValue());
            }
            //已过期状态
            if (DateUtil.getDateNowShanghai().compareTo(userDDRecordPO.getExpirationTime()) > 0 && userDDRecordPO.getTotal() > userDDRecordPO.getTotalUsage()) {
                userDDRecordVO.setState(UserDDrecordEnum.STATE_EXPIRE.getIntValue());
            }
            userDDRecordVO.setCreateTime(userDDRecordPO.getCreateTime());
            userDDRecordVO.setExpirationTime(userDDRecordPO.getExpirationTime());
            userDDRecordVO.setTotal(userDDRecordPO.getTotal());
            userDDRecordVO.setTotalUsage(userDDRecordPO.getTotalUsage());
            userDDRecordVO.setType(userDDRecordPO.getType());
            userDDRecordVO.setTypeItem(userDDRecordPO.getTypeItem());
            userDDRecordVO.setSourceId(userDDRecordPO.getSourceId());
            userDDRecordVO.setRemark(userDDRecordPO.getRemark());
            userDDRecordVOS.add(userDDRecordVO);
        }
        Page<UserDDRecordVO> page2 = new Page<>();
        page2.setPages(page.getPages());
        page2.setSize(page.getSize());
        page2.setTotal(page.getTotal());
        page2.setRecords(userDDRecordVOS);
        return Result.SUCCESS(page2);
    }

    /**
     * 邀请赠送
     *
     * @param userId      邀请人id
     * @param loginUserId 被邀请人id，即登陆id
     * @return 返回是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean setUserDDInviteGive(Long userId, Long loginUserId) throws IBusinessException {
        if (userId == null || loginUserId == null) {
            return true;
        }
        List<DictConfigPO> dictConfigPOS = dictConfigMapper.selectList(
                new LambdaQueryWrapper<DictConfigPO>()
                        .eq(DictConfigPO::getDictType, DictConfigEnum.DD_ADD_TASK_GIVE_INVITE.getDictType())
                        .eq(DictConfigPO::getIsUse, DictConfigEnum.getIsUseTrue())
        );
        //邀请人
        double yaoqingren = 0;
        //注册人，即被邀请人
        double zhuceren = 0;
        double appFirst = 0;
        double webFirst = 0;
        if (dictConfigPOS != null && !dictConfigPOS.isEmpty()) {
            for (DictConfigPO dictConfigPO : dictConfigPOS) {
                if (dictConfigPO.getDictKey().equals(DictConfigEnum.DD_ADD_TASK_GIVE_INVITE.getDictKey()) && dictConfigPO.getDictValue() != null) {
                    yaoqingren = Double.parseDouble(dictConfigPO.getDictValue());
                }
                if (dictConfigPO.getDictKey().equals(DictConfigEnum.DD_ADD_TASK_GIVE_INVITEE.getDictKey()) && dictConfigPO.getDictValue() != null) {
                    zhuceren = Double.parseDouble(dictConfigPO.getDictValue());
                }
                if (dictConfigPO.getDictKey().equals(DictConfigEnum.DD_ADD_TASK_GIVE_FIRST_APP.getDictKey()) && dictConfigPO.getDictValue() != null) {
                    appFirst = Double.parseDouble(dictConfigPO.getDictValue());
                }
                if (dictConfigPO.getDictKey().equals(DictConfigEnum.DD_ADD_TASK_GIVE_FIRST_WEB.getDictKey()) && dictConfigPO.getDictValue() != null) {
                    webFirst = Double.parseDouble(dictConfigPO.getDictValue());
                }
            }
        }

        //1 增加邀请人点子数量
        if (yaoqingren > 0) {
            if (userDDRecordMapper.insert(new UserDDRecordPO(
                    userId,
                    loginUserId,
                    UserDDrecordEnum.TYPE_TASK.getIntValue(),
                    UserDDrecordEnum.TYPE_ITEM_TASK_GIVE_INVITE_NEW_USER.getIntValue(),
                    yaoqingren,
                    DateUtil.getDateAddMonth(DateUtil.getCurrentDateTime(), 1)
            )) < 1) {
                throw new IBusinessException("邀请人新增用户点子余额信息失败");
            }

            FlowRecordPO flowRecord = FlowRecordPO.builder()
                    .recordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue())
                    .num(yaoqingren)
                    .userId(userId)
                    .operateType(FlowRecordEnum.OPERATE_TYPE_ADD_TASK_YAOQINGREN.getIntValue())
                    .remark(FlowRecordEnum.OPERATE_TYPE_ADD_TASK_YAOQINGREN.getStrValue())
                    .build();
            if (flowRecordMapper.insert(flowRecord) < 1) {
                throw new IBusinessException("新增流水失败");
            }
        }


        //2 增加被邀请人，即注册人点子数量
        if (zhuceren > 0) {
            if (userDDRecordMapper.insert(new UserDDRecordPO(
                    loginUserId,
                    userId,
                    UserDDrecordEnum.TYPE_TASK.getIntValue(),
                    UserDDrecordEnum.TYPE_ITEM_TASK_GIVE_INVITED_BY_FRIENDS.getIntValue(),
                    zhuceren,
                    DateUtil.getDateAddMonth(DateUtil.getCurrentDateTime(), 1)
            )) < 1) {
                throw new IBusinessException("注册人新增用户点子余额信息失败");
            }
            FlowRecordPO flowRecord2 = FlowRecordPO.builder()
                    .recordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue())
                    .num(yaoqingren)
                    .userId(userId)
                    .operateType(FlowRecordEnum.OPERATE_TYPE_ADD_TASK_BEIYAOQINGREN.getIntValue())
                    .remark(FlowRecordEnum.OPERATE_TYPE_ADD_TASK_BEIYAOQINGREN.getStrValue())
                    .build();
            if (flowRecordMapper.insert(flowRecord2) < 1) {
                throw new IBusinessException("新增流水失败");
            }
        }
        if (appFirst > 0 || webFirst > 0) {
            if (userDDRecordMapper.insert(new UserDDRecordPO(
                    loginUserId,
                    userId,
                    UserDDrecordEnum.TYPE_TASK.getIntValue(),
                    appFirst > 0 ? UserDDrecordEnum.TYPE_ITEM_TASK_GIVE_APP.getIntValue() : UserDDrecordEnum.TYPE_ITEM_TASK_GIVE_WEB.getIntValue(),
                    appFirst > 0 ? appFirst : webFirst,
                    DateUtil.getDateAddMonth(DateUtil.getCurrentDateTime(), 1)
            )) < 1) {
                throw new IBusinessException("注册人新增用户点子余额信息失败");
            }
            FlowRecordPO flowRecord2 = FlowRecordPO.builder()
                    .recordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue())
                    .num(appFirst > 0 ? appFirst : webFirst)
                    .userId(loginUserId)
                    .operateType(FlowRecordEnum.OPERATE_TYPE_ADD_ACTIVITY_REGISTER.getIntValue())
                    .remark(FlowRecordEnum.OPERATE_TYPE_ADD_ACTIVITY_REGISTER.getStrValue())
                    .build();
            if (flowRecordMapper.insert(flowRecord2) < 1) {
                throw new IBusinessException("新增流水失败");
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean setUserDDAppOrWebGive(Long userId, Integer type) throws IBusinessException {
        if (!Objects.equals(type, UserDDrecordEnum.TYPE_ITEM_TASK_GIVE_APP.getIntValue()) && !Objects.equals(type, UserDDrecordEnum.TYPE_ITEM_TASK_GIVE_WEB.getIntValue())) {
            return true;
        }
        //1、校验用户是否已赠送，已赠送直接返回
        if (userDDRecordMapper.selectCount(new LambdaQueryWrapper<UserDDRecordPO>()
                .eq(UserDDRecordPO::getUserId, userId)
                .eq(UserDDRecordPO::getType, UserDDrecordEnum.TYPE_TASK.getIntValue())
                .eq(UserDDRecordPO::getTypeItem, type)
        ) > 0) {
            return true;
        }
        //2 未赠送，执行查询操作
        List<DictConfigPO> dictConfigPOS = dictConfigMapper.selectList(
                new LambdaQueryWrapper<DictConfigPO>()
                        .eq(DictConfigPO::getDictType, DictConfigEnum.DD_ADD_TASK_GIVE_FIRST_APP.getDictType())
                        .eq(DictConfigPO::getIsUse, DictConfigEnum.getIsUseTrue())
        );
        //app 首次登陆赠送
        double appFirst = 0;
        //web首次登陆赠送
        double webFirst = 0;
        if (dictConfigPOS != null && !dictConfigPOS.isEmpty()) {
            for (DictConfigPO dictConfigPO : dictConfigPOS) {
                if (dictConfigPO.getDictKey().equals(DictConfigEnum.DD_ADD_TASK_GIVE_FIRST_APP.getDictKey()) && dictConfigPO.getDictValue() != null) {
                    appFirst = Double.parseDouble(dictConfigPO.getDictValue());
                }
                if (dictConfigPO.getDictKey().equals(DictConfigEnum.DD_ADD_TASK_GIVE_FIRST_WEB.getDictKey()) && dictConfigPO.getDictValue() != null) {
                    webFirst = Double.parseDouble(dictConfigPO.getDictValue());
                }
            }
        }

        //1 查询app或web登陆文本
        if (userDDRecordMapper.insert(new UserDDRecordPO(
                userId,
                (long) UserDDrecordEnum.SOURCE_ID_INVALID.getIntValue(),
                UserDDrecordEnum.TYPE_TASK.getIntValue(),
                UserDDrecordEnum.TYPE_ITEM_TASK_GIVE_INVITE_NEW_USER.getIntValue(),
                Objects.equals(type, UserDDrecordEnum.TYPE_ITEM_TASK_GIVE_APP.getIntValue()) ? appFirst : webFirst,
                DateUtil.getDateAddMonth(DateUtil.getCurrentDateTime(), 1)
        )) < 1) {
            throw new IBusinessException("首次平台登陆赠送用户点子余额信息失败");
        }
        return false;
    }
}
