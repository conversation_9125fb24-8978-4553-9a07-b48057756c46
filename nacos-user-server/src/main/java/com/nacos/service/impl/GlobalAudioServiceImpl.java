package com.nacos.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.audio.model.AudioStyleBO;
import com.business.audio.model.AudioStyleOneBO;
import com.business.audio.model.AudioStyleTagBO;
import com.business.audio.model.AudioStyleUrlBO;
import com.business.db.mapper.AudioGiveRecordMapper;
import com.business.db.mapper.AudioHomeDataMapper;
import com.business.db.mapper.AudioModelConfigMapper;
import com.business.db.mapper.PayRecordMapper;
import com.business.db.model.bo.UserRightsConfigBO;
import com.business.db.model.po.PayRecordPO;
import com.business.db.model.vo.AudioModelConfigVO;
import com.business.enums.*;
import com.business.model.page.AudioHomeDataDTO;
import com.business.model.po.AudioHomeDataPO;
import com.business.model.po.AudioModelConfigPO;
import com.business.model.vo.AudioHomeDataVO;
import com.business.model.vo.AudioModelConfigHomeVO;
import com.business.utils.BDateUtil;
import com.business.utils.BMemberUtil;
import com.nacos.auth.JwtNewUtil;
import com.nacos.enums.CommonIntEnum;
import com.nacos.enums.ImgOptModelEnum;
import com.nacos.exception.IBusinessException;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.GlobalAudioService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class GlobalAudioServiceImpl implements GlobalAudioService {


    @Resource
    private PayRecordMapper payRecordMapper;
    @Resource
    private AudioGiveRecordMapper audioGiveRecordMapper;
    @Resource
    private AudioHomeDataMapper audioHomeDataMapper;
    @Resource
    private AudioModelConfigMapper audioModelConfigMapper;
    @Value("${draw.models}")
    private String drawModel;
    // 音乐默认提示词，后期可能会改
    private final static List<String> defaultPromptList = Arrays.asList("创作一首浪漫抒情的日本流行歌曲，描述恋人之间温柔的告白和美好的回忆，旋律温柔，带有轻柔的吉他伴奏。", "创作一首励志的摇滚歌曲，表达奋斗和坚持不懈的精神，旋律充满力量，伴随着强劲的电吉他和鼓点", "创作一首节奏蓝调（R&B）歌曲，讲述夜晚思念爱人的情感，旋律感性且带有节奏感，配有柔和的键盘和低音贝斯");

    @Override
    public Result<List<AudioModelConfigVO>> styleList(Long languageTagId) {
        AudioModelConfigVO audio = new AudioModelConfigVO();
        audio.setId((long) ImgOptModelEnum.AUDIO_ATTRIBUTE_SUNO.getValue());
        audio.setUseDDQua(BDDUseNumEnum.SUNO_AUDIO.getDdUseNumDou());
        audio.setUseVipDDQua(BDDUseNumEnum.SUNO_AUDIO.getDdVipUseNumDou());
        audio.setAudioDefaultPrompt(defaultPromptList);
        List<AudioStyleTagBO> voices = BAudioModelEnum.getAudioStyleVoice();
        for (AudioStyleTagBO voice : voices) {
            if (voice == null) {
                continue;
            }
            //原生
            if (Objects.equals(voice.getTagId(), Long.valueOf(BAudioModelEnum.VOICE_NATIVE.getId()))) {
                List<AudioStyleBO> roles = BAudioModelEnum.getAudioStyleRoles();
                List<AudioStyleUrlBO> audioTags = new ArrayList<>();
                AudioStyleUrlBO singleChoiceTag1 = new AudioStyleUrlBO();
                singleChoiceTag1.setTag("角色");
                singleChoiceTag1.setAudioStyles(roles);
                audioTags.add(singleChoiceTag1);
                voice.setAudioTags(audioTags);
            }
        }
        List<AudioStyleOneBO> audioStyleOneTags = new ArrayList<>();
        AudioStyleOneBO audioStyleOneBO = new AudioStyleOneBO();
        audioStyleOneBO.setTag("声音");
        audioStyleOneBO.setAudioTags(voices);
        audioStyleOneTags.add(audioStyleOneBO);
        audio.setAudioStyleOneTags(audioStyleOneTags);
        List<AudioStyleUrlBO> audioStyleUrls = new ArrayList<>();
        List<AudioModelConfigPO> audioModelConfigPOS = audioModelConfigMapper.selectList(
                new LambdaQueryWrapper<AudioModelConfigPO>()
                        .eq(AudioModelConfigPO::getLanguageTagId, languageTagId)
                        .eq(AudioModelConfigPO::getDeleted, BIntEnum.IS_FALSE.getIntValue())
                        .orderByAsc(AudioModelConfigPO::getSort)
        );
        if (audioModelConfigPOS != null && !audioModelConfigPOS.isEmpty()) {
            HashMap<Integer, List<AudioStyleBO>> hashMap = getAudioMpdelListHashMap(audioModelConfigPOS);
            hashMap.forEach(
                    (k, v) -> {
                        BAudioStyleTypeEnum bAudioStyleTypeEnum = BAudioStyleTypeEnum.getById(k);
                        if (bAudioStyleTypeEnum != null) {
                            AudioStyleUrlBO audioStyleUrlBO = new AudioStyleUrlBO();
                            audioStyleUrlBO.setTag(bAudioStyleTypeEnum.getName());
                            audioStyleUrlBO.setAudioStyles(v);
                            audioStyleUrls.add(audioStyleUrlBO);
                        }
                    }
            );
            audio.setAudioStyleUrlTags(audioStyleUrls);
            audio.setDrawingModels(drawModel);
        }
        List<AudioModelConfigVO> audioModelConfigVOS = new ArrayList<>();
        audioModelConfigVOS.add(audio);
        return Result.SUCCESS(audioModelConfigVOS);
    }

    @NotNull
    private static HashMap<Integer, List<AudioStyleBO>> getAudioMpdelListHashMap(List<AudioModelConfigPO> audioModelConfigPOS) {
        HashMap<Integer, List<AudioStyleBO>> hashMap = new HashMap<>();

        for (AudioModelConfigPO audioModelConfigPO : audioModelConfigPOS) {
            if (audioModelConfigPO == null) {
                continue;
            }
            List<AudioStyleBO> audioStyleBOS = hashMap.get(audioModelConfigPO.getType());
            if (audioStyleBOS == null || audioStyleBOS.isEmpty()) {
                audioStyleBOS = new ArrayList<>();
            }
            AudioStyleBO audioStyleBO = new AudioStyleBO();
            audioStyleBO.setId(audioModelConfigPO.getId());
            audioStyleBO.setName(audioModelConfigPO.getNameShow());
            audioStyleBO.setUrl(audioModelConfigPO.getModelUrl());
            audioStyleBO.setIsVip(audioModelConfigPO.getIsVip());
            audioStyleBO.setState(audioModelConfigPO.getState());
            audioStyleBO.setVipUseTag(audioModelConfigPO.getVipUseTag());
            audioStyleBO.setStateOffTag(audioModelConfigPO.getStateOffTag());
            audioStyleBOS.add(audioStyleBO);
            hashMap.put(audioModelConfigPO.getType(), audioStyleBOS);
        }
        return hashMap;
    }

    @Override
    public Result<List<AudioModelConfigHomeVO>> getAudioModelConfigHomepage(Long languageTagId) {
        List<AudioModelConfigPO> audioModelConfigList = audioModelConfigMapper.selectList(
                new LambdaQueryWrapper<AudioModelConfigPO>()
                        .eq(AudioModelConfigPO::getLanguageTagId, languageTagId)
                        .eq(AudioModelConfigPO::getDeleted, BIntEnum.IS_FALSE.getIntValue())
                        .eq(AudioModelConfigPO::getIsHomepageShow, BIntEnum.IS_TRUE.getIntValue())
                        .orderByAsc(AudioModelConfigPO::getSort)
        );
        if (audioModelConfigList == null || audioModelConfigList.isEmpty()) {
            return null;
        }
        List<AudioModelConfigHomeVO> audioModelConfigVOList = new ArrayList<>();
        for (AudioModelConfigPO audioModelConfig : audioModelConfigList) {
            AudioModelConfigHomeVO audioModelConfigVO = new AudioModelConfigHomeVO();
            audioModelConfigVO.setId(audioModelConfig.getId());
            audioModelConfigVO.setLanguageTagId(audioModelConfig.getLanguageTagId());
            audioModelConfigVO.setNameShow(audioModelConfig.getNameShow());
            audioModelConfigVO.setWebHomepageUrl(audioModelConfig.getWebHomepageUrl());
            audioModelConfigVOList.add(audioModelConfigVO);
        }
        return Result.SUCCESS(audioModelConfigVOList);
    }

    @Override
    public Result<IPage<AudioHomeDataVO>> playList(AudioHomeDataDTO audioHomeDataDTO) {
        List<AudioHomeDataVO> audioDataVOList = new ArrayList<>();
        Page<AudioHomeDataPO> audioHomeDataPOPage = new Page<>(audioHomeDataDTO.getPageNumber(), audioHomeDataDTO.getPageSize());
        audioHomeDataPOPage = audioHomeDataMapper.selectPage(
                audioHomeDataPOPage,
                new LambdaQueryWrapper<AudioHomeDataPO>()
                        .eq(AudioHomeDataPO::getState, CommonIntEnum.DELETED_TRUE.getIntValue())
                        .eq(AudioHomeDataPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(AudioHomeDataPO::getStyleId, audioHomeDataDTO.getStyleId())
                        .orderByDesc(AudioHomeDataPO::getCreateTime)
        );
        if (audioHomeDataPOPage.getRecords() != null && !audioHomeDataPOPage.getRecords().isEmpty()) {
            List<AudioHomeDataPO> audioHomeDataPOS = audioHomeDataPOPage.getRecords();
            Collections.shuffle(audioHomeDataPOS);
            for (AudioHomeDataPO record : audioHomeDataPOS) {
                AudioHomeDataVO audioHomeDataVO = new AudioHomeDataVO();
                audioHomeDataVO.setId(record.getId());
                audioHomeDataVO.setLanguageTagId(record.getLanguageTagId());
                audioHomeDataVO.setTitle(record.getTitle());
                audioHomeDataVO.setTags(record.getTags());
                audioHomeDataVO.setDuration(record.getDuration());
                audioHomeDataVO.setPrompt(record.getPrompt());
                audioHomeDataVO.setImageLargeUrl(record.getImageLargeUrl());
                audioHomeDataVO.setImageUrl(record.getImageUrl());
                audioHomeDataVO.setAudioUrl(record.getAudioUrl());
                audioDataVOList.add(audioHomeDataVO);
            }
        }
        IPage<AudioHomeDataVO> audioHomeDataVO = new Page<>();
        audioHomeDataVO.setRecords(audioDataVOList);
        audioHomeDataVO.setCurrent(audioHomeDataPOPage.getCurrent());
        audioHomeDataVO.setTotal(audioHomeDataPOPage.getTotal());
        audioHomeDataVO.setPages(audioHomeDataPOPage.getPages());
        audioHomeDataVO.setSize(audioHomeDataPOPage.getSize());
        return Result.SUCCESS(audioHomeDataVO);
    }

    @Override
    public Result<Integer> giveRemainCount() throws IBusinessException {
        Long userId = JwtNewUtil.getUserId();
        Integer giveCount = getMemberLevel(userId);
        if (giveCount == null) {
            return Result.SUCCESS(0);
        }
        Integer useCount = audioGiveRecordMapper.getGiveCount(JwtNewUtil.getUserId());
        return Result.SUCCESS((giveCount - (useCount == null ? 0 : useCount)));
    }

    private Integer getMemberLevel(Long userId) {
        PayRecordPO payRecordPO = payRecordMapper.selectOne(
                new LambdaQueryWrapper<PayRecordPO>()
                        .eq(PayRecordPO::getUserId, userId)
                        .eq(PayRecordPO::getState, BPayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                        .in(PayRecordPO::getOrderType, BPayRecordEnum.ORDER_TYPE_VIP.getIntValue(), BPayRecordEnum.ORDER_TYPE_SVIP.getIntValue())
                        .ge(PayRecordPO::getExpirationTime, BDateUtil.getDateNowShanghai())
                        .orderByDesc(PayRecordPO::getExpirationTime)
                        .last("LIMIT 0,1")
        );
        if (payRecordPO != null) {
            List<UserRightsConfigBO> userRightsConfigBOList = JSON.parseObject(RedisUtil.getValue(BRedisKeyEnum.VIP_RIGHTS_CONFIG.getKey()), new TypeReference<List<UserRightsConfigBO>>() {
            });
            UserRightsConfigBO userRightsConfigBO = com.business.utils.ImgDrawUtil.getUserRightsConfigById(userRightsConfigBOList, BUserRightsConfigEnum.RIGHTS_MUSIC_GIVE_AWAY.getIntValue());
            return BMemberUtil.getMemberConcurrentCount(payRecordPO.getVipConfigId().intValue(), userRightsConfigBO);
        }
        return null;
    }
}
