package com.nacos.service;

import com.business.db.model.po.*;
import com.business.db.model.vo.*;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;

import java.util.List;

public interface GlobalService {

    Result<List<AppTagConfigPO>> appTagList(Long languageTagId);

    /**
     * 获取语言列表
     *
     * @return 语言列表实体
     */
    Result<List<LanguageTagPO>> languageList();

    /**
     * 获取banner列表
     *
     * @param languageTagId 语言标签id
     * @return banner列表实体
     */
    Result<List<BannerConfigPO>> bannerList(Long languageTagId);

    Result<List<FunctionConfigPO>> functionList(Long languageTagId);

    /**
     * 获取功能列表
     *
     * @param languageTagId 语言标签id
     * @return 功能列表实体
     */
    Result<List<FunctionConfigVO>> functionListNew(Long languageTagId);

    /**
     * 获取编辑功能列表
     *
     * @param languageTagId 语言标签id
     * @return 功能列表实体
     */
    Result<List<FunctionConfigVO>> functionEditList(Long languageTagId);

    /**
     * 获取图片标签列表
     *
     * @param languageTagId 语言标签id
     * @return 图片标签列表实体
     */
    Result<List<ImgTagConfigPO>> imageTagList(Long languageTagId);

    /**
     * 获取绘图模型列表
     *
     * @param languageTagId 语言标签id
     * @return 绘图模型列表实体
     */
    Result<List<ImgModelConfigVO>> imageModelConfigList(Long languageTagId, String version);

    /**
     * 获取绘图模型列表
     *
     * @param languageTagId 语言标签id
     * @return 绘图模型列表实体
     */
    Result<List<ImgModelConfigVO>> imageModelConfigListNew(Long languageTagId, String version);

    /**
     * 获取绘图编辑列表
     *
     * @param languageTagId 语言标签id
     * @param imgDetlId     图片详情id
     * @param parentOperate 上层操作id
     * @return 绘图编辑列表实体
     */
    Result<List<ImgOptConfigVO>> imageOptConfigList(Long languageTagId, Long imgDetlId, Integer parentOperate) throws IBusinessException;

    Result<List<PosterOptConfigVO>> imageOptConfigList(Long languageTagId, Integer modelId) throws IBusinessException;

    Result<List<ImgOptConfigVO>> imageOptConfigListParent(Long languageTagId) throws IBusinessException;

    Result<List<VipBannerPO>> vipBanner(Long languageTagId);

    Result<Object> getVideoListRandom();

    Result<RetentionPopupVO> retentionPopup(Long languageTagId);

}
