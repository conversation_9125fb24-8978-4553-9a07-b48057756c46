spring:
  profiles:
    active: @env@
  application:
    name: nacos-webhook-server
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${file.uploadPath}
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  #    设置上传文件大小
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB

#配置mybatis-plus 开启日志
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl #关闭sql日志
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
  mapper-locations: classpath:/mapper/*.xml



