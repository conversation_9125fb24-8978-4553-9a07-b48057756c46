package com.nacos.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 课程购买记录实体类
 */
@Data
@TableName("course_pay_record")
@Schema(description = "课程购买记录实体")
public class CoursePayRecord {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Long userId;

    /**
     * 课程id
     */
    @Schema(description = "课程id")
    private Long coursesId;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型")
    private Integer orderType;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private Integer type;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private LocalDateTime expirationTime;

    /**
     * 状态 0-未支付 1-已支付
     */
    @Schema(description = "状态 0-未支付 1-已支付")
    private Integer state;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 数据版本
     */
    @Schema(description = "数据版本")
    private Integer dataVersion;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private Long creator;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @Schema(description = "操作人")
    private Long operator;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    private LocalDateTime operateTime;
}