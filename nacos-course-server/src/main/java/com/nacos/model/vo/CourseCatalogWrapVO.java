package com.nacos.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 课程目录包装类
 * 包含目录列表和统计信息：
 * 1. 课程目录的树形结构
 * 2. 章节数量统计
 * 3. 课程更新状态
 */
@Data
@Schema(description = "课程目录包装类")
public class CourseCatalogWrapVO {
    
    @Schema(description = "课程目录列表，树形结构，按章节顺序排列")
    private List<CourseCatalogVO> catalogs;
    
    @Schema(description = "课程章数，一级目录的数量")
    private Integer chapterCount;
    
    @Schema(description = "课程节数，二级目录的总数量")
    private Integer lessonCount;
    
    @Schema(description = "更新状态，可能的值：全部更新、持续更新中、暂无内容")
    private String updateStatus;
} 