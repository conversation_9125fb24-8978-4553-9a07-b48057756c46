package com.nacos.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程介绍展示对象
 * 用于返回课程的详细介绍信息
 * 包括介绍内容、配图等
 */
@Data
@Schema(description = "课程介绍展示对象")
public class CourseVO {

    @Schema(description = "课程ID")
    private Long id;

    @Schema(description = "课程名称")
    private String title;

    @Schema(description = "课程价格")
    private BigDecimal price;

    @Schema(description = "课程封面图片链接")
    private String imageUrl;

    @Schema(description = "状态：0-下线, 1-上线, 2-草稿")
    private Integer status;

    @Schema(description = "创建时间")
    private String createdAt;

    @Schema(description = "更新时间")
    private String updatedAt;

    @Schema(description = "购买人数")
    private Integer purchaseCount;

    @Schema(description = "学员评价数量")
    private Integer commentCount;

    @Schema(description = "课程内容简介")
    private String content;

    @Schema(description = "父章节数量")
    private Long chapterCount;

    @Schema(description = "子章节数量")
    private Long lessonCount;

    @Schema(description = "课程介绍图片数组")
    private List<String> introductionImages;

    @Schema(description = "用户是否已购买该课程")
    private Boolean hasPurchased;

} 