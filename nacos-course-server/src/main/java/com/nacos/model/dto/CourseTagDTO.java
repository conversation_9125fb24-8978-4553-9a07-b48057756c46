package com.nacos.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 标签数据传输对象
 */
@Data
@Schema(description = "标签数据传输对象")
public class CourseTagDTO {
    
    @Schema(description = "标签名称")
    private String name;

    @Schema(description = "标签图片URL")
    private String imageUrl;

    @Schema(description = "标签链接URL")
    private String linkUrl;
    
    @Schema(description = "状态：0-不可用, 1-可用")
    private Integer status;
} 