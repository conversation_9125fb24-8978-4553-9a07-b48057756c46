package com.nacos.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程数据传输对象
 */
@Data
@Schema(description = "课程数据传输对象")
public class CourseDTO {
    
    @Schema(description = "课程名称")
    private String title;

    @Schema(description = "课程内容简介")
    private String content;
    
    @Schema(description = "课程价格")
    private BigDecimal price;
    
    @Schema(description = "课程封面图片链接")
    private String imageUrl;
    
    @Schema(description = "状态：0-下线, 1-上线, 2-草稿")
    private Integer status;

    @Schema(description = "课程介绍图片列表")
    private List<String> introductionImages;
} 