package com.nacos.handler;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nacos.mapper.CourseMapper;
import com.nacos.model.po.Course;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Random;

@Component
@Log4j2
@RequiredArgsConstructor
public class CourseTaskHandler {
    
    private final CourseMapper courseMapper;
    private final Random random = new Random();

    @Value("${task.course.min-increment}")
    private int minIncrement;

    @Value("${task.course.max-increment}")
    private int maxIncrement;

    public void handlePurchaseCountIncrement() {
        try {
            List<Course> courseList = courseMapper.selectList(
                    new LambdaUpdateWrapper<Course>()
            );
            courseList.forEach(course -> {
                // 每次增加购买人数前先随机休眠一段时间
                try {
                    Thread.sleep(random.nextInt(500, 2000)); // 随机休眠500-2000毫秒
                } catch (InterruptedException e) {
                    System.err.println("线程被中断：" + e.getMessage());
                    Thread.currentThread().interrupt(); // 重新中断当前线程
                }
                int increment = random.nextInt(minIncrement, maxIncrement + 1);// [minIncrement, maxIncrement]
                courseMapper.update(null,
                        new LambdaUpdateWrapper<Course>()
                                .eq(Course::getId, course.getId())
                                .setSql("purchase_count = purchase_count + " + increment)
                );
                log.info("定时增加课程{}购买人数：{}", course.getId(), increment);
            });
        } catch (Exception e) {
            log.error("定时增加课程购买人数任务失败{}", e.getMessage(), e);
        }
    }
} 