package com.nacos;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Slf4j
@ComponentScan(basePackages = {"com.nacos", "com.business"})
@MapperScan("com.nacos.mapper")
@EnableTransactionManagement
@EnableAsync
@EnableScheduling
@SpringBootApplication
public class CourseServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(CourseServerApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  Course启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}
