package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nacos.mapper.*;
import com.nacos.model.dto.CourseDTO;
import com.nacos.model.dto.CourseQueryDTO;
import com.nacos.model.po.Course;
import com.nacos.model.po.CoursePayRecord;
import com.nacos.model.po.CourseCatalog;
import com.nacos.model.vo.*;
import com.nacos.result.Result;
import com.nacos.service.CourseService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.nacos.converter.CourseConverter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程服务实现类
 * 实现课程的核心业务逻辑，包括：
 * 1. 课程基本信息的CRUD操作
 * 2. 课程内容管理
 * 3. 课程目录的树形结构维护
 * 4. 课程标签关联管理
 * 5. 课程状态流转控制
 */
@Service
@RequiredArgsConstructor
public class CourseServiceImpl implements CourseService {

    private static final Logger logger = LoggerFactory.getLogger(CourseServiceImpl.class);

    private final CourseMapper courseMapper;
    private final CourseCatalogMapper catalogMapper;
    private final CourseTagJoinMapper courseTagJoinMapper;
    private final CourseTagMapper courseTagMapper;
    private final CoursesPayRecordMapper coursesPayRecordMapper;
    private final CourseCatalogMapper courseCatalogMapper;

    /**
     * 创建课程
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Map<String, Object>> createCourse(CourseDTO courseDTO) {
        logger.info("开始创建课程: {}", courseDTO);
        try {
            // 创建课程基本信息
            Course course = new Course();
            BeanUtils.copyProperties(courseDTO, course);
            course.setId(IdWorker.getId());// 自动生成ID
            course.setStatus(1); // 默认状态为可用

            // 处理介绍图片数组
            if (courseDTO.getIntroductionImages() != null && !courseDTO.getIntroductionImages().isEmpty()) {
                String introduction = String.join(",", courseDTO.getIntroductionImages());// 图片数组转字符串
                course.setIntroduction(introduction);
            }
            // 保存课程基本信息
            courseMapper.insert(course);
            Map<String, Object> result = new HashMap<>();
            result.put("id", course.getId());// 课程ID
            return Result.SUCCESS("更新课程成功", result);
        } catch (Exception e) {
            logger.error("创建课程失败: {}", e.getMessage(), e);
            return Result.ERROR("创建课程失败：" + e.getMessage());
        }
    }

    /**
     * 更新课程信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Map<String, Object>> updateCourse(Long id, CourseDTO courseDTO) {
        try {
            Course course = courseMapper.selectById(id);
            if (course == null) {
                return Result.ERROR("课程不存在");
            }
            BeanUtils.copyProperties(courseDTO, course);
            // 处理介绍图片数组
            if (courseDTO.getIntroductionImages() != null && !courseDTO.getIntroductionImages().isEmpty()) {
                String introduction = String.join(",", courseDTO.getIntroductionImages());// 图片数组转字符串
                course.setIntroduction(introduction);
            }
            courseMapper.updateById(course);

            // 返回更新后的课程ID
            Map<String, Object> result = new HashMap<>();
            result.put("id", course.getId());// 课程ID
            return Result.SUCCESS("更新课程成功", result);
        } catch (Exception e) {
            logger.error("更新课程失败: {}", e.getMessage(), e);
            return Result.ERROR("更新课程失败：" + e.getMessage());
        }
    }


    /**
     * 删除课程
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> deleteCourse(Long id) {
        try {
            Course course = courseMapper.selectById(id);
            if (course == null) {
                return Result.ERROR("课程不存在");
            }
            courseMapper.deleteById(id);
            return Result.SUCCESS("课程删除成功，课程ID:" + course.getId());
        } catch (Exception e) {
            logger.error("删除课程失败: {}", e.getMessage(), e);
            return Result.ERROR("删除课程失败：" + e.getMessage());
        }
    }

    /**
     * 更新课程状态
     * 支持的状态值：
     * - 0: 下线，课程不可见
     * - 1: 上线，课程可见可购买
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateStatus(Long id, Integer status) {
        try {
            Course course = courseMapper.selectById(id);
            if (course == null) {
                return Result.ERROR("课程不存在");
            }
            if (status < 0 || status > 2) {
                return Result.ERROR("无效的状态值");
            }
            course.setStatus(status);
            courseMapper.updateById(course);
            return Result.SUCCESS("查询成功", null);
        } catch (Exception e) {
            logger.error("更新课程状态失败: {}", e.getMessage(), e);
            return Result.ERROR("更新课程状态失败：" + e.getMessage());
        }
    }

    /**
     * 分页获取课程列表
     * 前台接口
     * 只需要基本信息，使用基础转换
     * - 当status为null时，查询所有状态的课程
     * - 当status不为null时，查询指定状态的课程
     */
    @Override
    public Result<Page<CourseBaseVO>> listCourses(CourseQueryDTO courseQueryDTO) {
        logger.info("分页查询课程列表: {}", courseQueryDTO);
        try {
            Page<Course> page = new Page<>(courseQueryDTO.getCurrent(), courseQueryDTO.getSize());
            Page<Course> coursePage = courseMapper.selectCoursePage(page, courseQueryDTO);
            
            // 使用转换器转换分页对象
            Page<CourseBaseVO> resultPage = CourseConverter.toCourseBaseVOPage(coursePage);

            logger.info("查询成功，总记录数：{}，当前页数据量：{}", resultPage.getTotal(), resultPage.getRecords().size());
            return Result.SUCCESS("查询成功", resultPage);
        } catch (Exception e) {
            logger.error("分页查询课程列表失败: {}", e.getMessage(), e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据标签查询课程
     */
    @Override
    public Result<Page<CourseBaseVO>> listCoursesByTag(CourseQueryDTO courseQueryDTO) {
        logger.info("根据标签查询课程列表: {}", courseQueryDTO);
        try {
            Page<Course> page = new Page<>(courseQueryDTO.getCurrent(), courseQueryDTO.getSize());
            Page<Course> coursePage = courseMapper.selectPageByTagId(courseQueryDTO.getTagId(), page);

            // 使用转换器转换分页对象
            Page<CourseBaseVO> resultPage = CourseConverter.toCourseBaseVOPage(coursePage);

            logger.info("查询成功，总记录数：{}，当前页数据量：{}", resultPage.getTotal(), resultPage.getRecords().size());
            return Result.SUCCESS("查询成功", resultPage);
        } catch (Exception e) {
            logger.error("根据标签查询课程列表失败: {}", e.getMessage(), e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取课程介绍
     * 包括课程ID、介绍内容、图片URL、创建时间
     */
    @Override
    public Result<CourseVO> getCourseIntroduction(Long id, Long userId) {
        try {
            Course course = courseMapper.selectById(id);
            if (course == null) {
                return Result.ERROR("课程不存在");
            }
            
            // 使用转换器转换对象
            CourseVO vo = CourseConverter.toCourseVO(course);
            // 查询用户是否购买了该课程
            if (userId != null) {
                vo.setHasPurchased(checkUserPurchased(userId, id));
            } else {
                vo.setHasPurchased(false);
            }
            
            return Result.SUCCESS("查询成功", vo);
        } catch (Exception e) {
            logger.error("获取课程介绍失败: {}", e.getMessage(), e);
            return Result.ERROR("获取课程介绍失败：" + e.getMessage());
        }
    }

    /**
     * 更新课程购买数量
     */
    @Override
    public void updatePurchaseCount(Long courseId) {
        Course course = courseMapper.selectById(courseId);
        if (course != null) {
            course.setPurchaseCount(course.getPurchaseCount() + 1);
            courseMapper.updateById(course);
        }
    }

    /**
     * 更新课程评论数量
     */
    @Override
    public void updateCommentCount(Long courseId) {
        Course course = courseMapper.selectById(courseId);
        if (course != null) {
            course.setCommentCount(course.getCommentCount() + 1);
            courseMapper.updateById(course);
        }
    }


    /**
     * 查询用户购买的课程列表
     *
     * @param courseQueryDTO 查询条件
     * @return 课程列表分页结果
     */
    @Override
    public Result<Page<CourseBaseVO>> getUserCourses(CourseQueryDTO courseQueryDTO) {
        Long userId = courseQueryDTO.getUserId();
        logger.info("查询用户购买的课程列表: userId={}, dto={}", userId, courseQueryDTO);
        try {
            Page<Course> page = new Page<>(courseQueryDTO.getCurrent(), courseQueryDTO.getSize());

            // 查询用户购买的课程
            LambdaQueryWrapper<CoursePayRecord> payRecordWrapper = new LambdaQueryWrapper<>();
            payRecordWrapper.eq(CoursePayRecord::getUserId, userId)
                    .eq(CoursePayRecord::getState, 1)  // 已支付的订单
                    .eq(CoursePayRecord::getDeleted, 0); // 未删除的记录

            // 查询购买记录
            List<CoursePayRecord> payRecords = coursesPayRecordMapper.selectList(payRecordWrapper);

            // 如果没有购买记录，直接返回空
            if (CollectionUtils.isEmpty(payRecords)) {
                return Result.SUCCESS(new Page<CourseBaseVO>());
            }

            // 获取课程ID列表信息
            List<Long> courseIds = payRecords.stream()
                    .map(CoursePayRecord::getCoursesId)
                    .collect(Collectors.toList());

            // 查询课程列表
            LambdaQueryWrapper<Course> courseWrapper = new LambdaQueryWrapper<>();
            courseWrapper.in(Course::getId, courseIds)
                    .orderByDesc(Course::getCreatedAt);

            Page<Course> coursePage = courseMapper.selectPage(page, courseWrapper);

            // 使用转换器转换分页对象
            Page<CourseBaseVO> resultPage = CourseConverter.toCourseBaseVOPage(coursePage);

            logger.info("查询成功，总记录数：{}，当前页数据量：{}", resultPage.getTotal(), resultPage.getRecords().size());
            return Result.SUCCESS("查询成功", resultPage);
        } catch (Exception e) {
            logger.error("查询用户购买的课程列表失败: userId={}, error={}", userId, e.getMessage(), e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询课程购买记录
     *
     * @param page 页码
     * @param size 页大小
     * @return 课程购买记录分页结果
     */
    @Override
    public Result<Page<CoursePayRecordVO>> getCoursePayRecords(Integer page, Integer size) {
        logger.info("查询课程购买记录, page={}, size={}", page, size);
        try {
            Page<CoursePayRecord> payRecordPage = new Page<>(page, size);
            Page<CoursePayRecord> payRecords = coursesPayRecordMapper.selectPage(payRecordPage, null);
            
            // 使用转换器转换分页对象
            Page<CoursePayRecordVO> resultPage = CourseConverter.toCoursePayRecordVOPage(payRecords);
            
            logger.info("查询成功，总记录数：{}，当前页数据量：{}", resultPage.getTotal(), resultPage.getRecords().size());
            return Result.SUCCESS("查询成功", resultPage);
        } catch (Exception e) {
            logger.error("查询课程购买记录失败, page={}, size={}, error={}", page, size, e.getMessage(), e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询课程列表
     * 后台接口
     * @param courseQueryDTO 查询条件
     * @return 课程详情分页结果
     */
    @Override
    public Result<Page<CourseVO>> listCoursesDetail(CourseQueryDTO courseQueryDTO) {
        logger.info("分页查询课程详情: {}", courseQueryDTO);
        try {
            Page<Course> page = new Page<>(courseQueryDTO.getCurrent(), courseQueryDTO.getSize());
            Page<Course> coursePage = courseMapper.selectCoursePage(page, courseQueryDTO);
            
            // 使用转换器转换分页对象
            Page<CourseVO> resultPage = CourseConverter.toCourseVOPage(coursePage);
            
            // 查询每个课程的章节统计信息
            for (CourseVO courseVO : resultPage.getRecords()) {
                // 查询父章节数量 (parentId为null的为一级目录)
                LambdaQueryWrapper<CourseCatalog> parentWrapper = new LambdaQueryWrapper<>();
                parentWrapper.eq(CourseCatalog::getCourseId, courseVO.getId())
                            .isNull(CourseCatalog::getParentId);  // 一级目录parentId为null
                Long parentCount = courseCatalogMapper.selectCount(parentWrapper);
                courseVO.setChapterCount(parentCount);
                
                // 查询子章节数量 (parentId不为null的为二级目录)
                LambdaQueryWrapper<CourseCatalog> childWrapper = new LambdaQueryWrapper<>();
                childWrapper.eq(CourseCatalog::getCourseId, courseVO.getId())
                           .isNotNull(CourseCatalog::getParentId);  // 二级目录parentId不为null
                Long childCount = courseCatalogMapper.selectCount(childWrapper);
                courseVO.setLessonCount(childCount);
            }
            logger.info("查询成功，总记录数：{}，当前页数据量：{}", resultPage.getTotal(), resultPage.getRecords().size());
            return Result.SUCCESS("查询成功", resultPage);
        } catch (Exception e) {
            logger.error("分页查询课程详情失败: {}", e.getMessage(), e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }

    /**
     * 检查用户是否购买了课程
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否已购买
     */
    private boolean checkUserPurchased(Long userId, Long courseId) {
        if (userId == null) {
            return false;
        }
        try {
            // 查询用户是否购买了该课程
            LambdaQueryWrapper<CoursePayRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CoursePayRecord::getUserId, userId)
                    .eq(CoursePayRecord::getCoursesId, courseId)
                    .eq(CoursePayRecord::getState, 1);  // state=1 表示已购买

            CoursePayRecord record = coursesPayRecordMapper.selectOne(queryWrapper);
            return record != null;
        } catch (Exception e) {
            logger.error("检查用户购买状态失败: userId={}, courseId={}, error={}", userId, courseId, e.getMessage());
            return false;
        }
    }
} 