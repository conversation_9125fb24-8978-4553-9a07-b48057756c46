package com.nacos.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nacos.model.po.Course;
import com.nacos.model.po.CoursePayRecord;
import com.nacos.model.vo.CourseBaseVO;
import com.nacos.model.vo.CoursePayRecordVO;
import com.nacos.model.vo.CourseVO;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 课程对象转换器
 * 集中处理课程相关的对象转换逻辑
 */
public class CourseConverter {

    /**
     * 将 Course 转换为 CourseBaseVO
     */
    public static CourseBaseVO toCourseBaseVO(Course course) {
        if (course == null) {
            return null;
        }
        CourseBaseVO vo = new CourseBaseVO();
        BeanUtils.copyProperties(course, vo);
        return vo;
    }

    /**
     * 将 Course 转换为 CourseVO
     */
    public static CourseVO toCourseVO(Course course) {
        if (course == null) {
            return null;
        }
        CourseVO vo = new CourseVO();
        BeanUtils.copyProperties(course, vo);
        
        // 处理图片链接
        if (StringUtils.hasText(course.getIntroduction())) {
            List<String> images = Arrays.asList(course.getIntroduction().split(","));
            vo.setIntroductionImages(images);
        } else {
            vo.setIntroductionImages(new ArrayList<>());
        }
        
        return vo;
    }

    /**
     * 将 CoursePayRecord 转换为 CoursePayRecordVO
     */
    public static CoursePayRecordVO toCoursePayRecordVO(CoursePayRecord record) {
        if (record == null) {
            return null;
        }
        CoursePayRecordVO vo = new CoursePayRecordVO();
        BeanUtils.copyProperties(record, vo);
        return vo;
    }

    /**
     * 将 Course 列表转换为 CourseBaseVO 列表
     */
    public static List<CourseBaseVO> toCourseBaseVOList(List<Course> courseList) {
        if (courseList == null) {
            return new ArrayList<>();
        }
        return courseList.stream()
                .map(CourseConverter::toCourseBaseVO)
                .collect(Collectors.toList());
    }

    /**
     * 将 Course 列表转换为 CourseVO 列表
     */
    public static List<CourseVO> toCourseVOList(List<Course> courseList) {
        if (courseList == null) {
            return new ArrayList<>();
        }
        return courseList.stream()
                .map(CourseConverter::toCourseVO)
                .collect(Collectors.toList());
    }

    /**
     * 将 CoursePayRecord 列表转换为 CoursePayRecordVO 列表
     */
    public static List<CoursePayRecordVO> toCoursePayRecordVOList(List<CoursePayRecord> recordList) {
        if (recordList == null) {
            return new ArrayList<>();
        }
        return recordList.stream()
                .map(CourseConverter::toCoursePayRecordVO)
                .collect(Collectors.toList());
    }

    /**
     * 将 Course 分页对象转换为 CourseBaseVO 分页对象
     */
    public static Page<CourseBaseVO> toCourseBaseVOPage(Page<Course> page) {
        if (page == null) {
            return new Page<>();
        }
        Page<CourseBaseVO> voPage = new Page<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal()
        );
        voPage.setRecords(toCourseBaseVOList(page.getRecords()));
        return voPage;
    }

    /**
     * 将 Course 分页对象转换为 CourseVO 分页对象
     */
    public static Page<CourseVO> toCourseVOPage(Page<Course> page) {
        if (page == null) {
            return new Page<>();
        }
        Page<CourseVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        voPage.setRecords(toCourseVOList(page.getRecords()));
        return voPage;
    }

    /**
     * 将 CoursePayRecord 分页对象转换为 CoursePayRecordVO 分页对象
     */
    public static Page<CoursePayRecordVO> toCoursePayRecordVOPage(Page<CoursePayRecord> page) {
        if (page == null) {
            return new Page<>();
        }
        Page<CoursePayRecordVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        voPage.setRecords(toCoursePayRecordVOList(page.getRecords()));
        return voPage;
    }
} 