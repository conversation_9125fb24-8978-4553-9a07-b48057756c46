package com.nacos.utils;

import com.alibaba.fastjson2.JSONObject;
import com.business.db.model.vo.admin.AdminUserJwtVO;
import com.nacos.exception.IBusinessException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.Date;

public class JwtTokenGenerator {
    private static final String SECRET_KEY = "ddadminkey1234567890";
    private static final String codeKey = "********************************";

    //创建token
    public static String generateToken(AdminUserJwtVO adminUserJwtVO) throws Exception {
        long currentTimeMillis = System.currentTimeMillis();
        Date now = new Date(currentTimeMillis);
        Date expiryDate = new Date(currentTimeMillis + 3600000 * 24);
        String subject = JSONObject.toJSONString(adminUserJwtVO);
        return Jwts.builder()
                .setSubject(encrypt(subject, codeKey)) // 设置主题，通常是用户名或用户ID
                .setIssuer("ddadmin") // 设置签发者
                .setIssuedAt(now) // 设置签发时间
                .setExpiration(expiryDate) // 设置过期时间
                .signWith(SignatureAlgorithm.HS256, SECRET_KEY) // 设置签名算法和秘密key
                .compact(); // 生成JWT
    }

    //校验token
    public static boolean validateToken() {
        try {
            if(null == RequestContextHolder.getRequestAttributes()){
                return false;
            }
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String token = request.getHeader("token");
            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY.getBytes())
                    .parseClaimsJws(token)
                    .getBody();
            return !claims.getExpiration().before(new Date());
        } catch (Exception e) {
            return false;
        }
    }

    //获取token解析信息
    public static AdminUserJwtVO getAdminUserJwtVO() throws IBusinessException {
        try {
            if(null == RequestContextHolder.getRequestAttributes()){
               throw new IBusinessException("token 失效");
            }
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String token = request.getHeader("token");
            Claims claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY.getBytes())
                    .parseClaimsJws(token)
                    .getBody();
            String subject = decrypt(claims.getSubject(), codeKey);
            AdminUserJwtVO adminUserJwtVO = JSONObject.parseObject(subject, AdminUserJwtVO.class);
            if (adminUserJwtVO == null){
                throw new IBusinessException("token 失效");
            }
            return adminUserJwtVO;
        } catch (Exception e) {
            throw new IBusinessException("token 失效");
        }
    }

    public static SecretKey getKeyFromPassword(String password) throws Exception {
        SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
        PBEKeySpec spec = new PBEKeySpec(password.toCharArray(), "1234".getBytes(), 65536, 256);
        return new SecretKeySpec(factory.generateSecret(spec).getEncoded(), "AES");
    }

    // 加密方法
    public static String encrypt(String data, String key) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, getKeyFromPassword(key));
        byte[] encrypted = cipher.doFinal(data.getBytes());
        return Base64.getEncoder().encodeToString(encrypted);
    }

    // 解密方法
    public static String decrypt(String encryptedData, String key) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE,getKeyFromPassword(key));
        byte[] decoded = Base64.getDecoder().decode(encryptedData);
        byte[] decrypted = cipher.doFinal(decoded);
        return new String(decrypted);
    }

    public static void main(String[] args) throws Exception {
//        System.out.println(generateToken(new AdminUserJwtVO()));
        System.out.println();
    }

}
