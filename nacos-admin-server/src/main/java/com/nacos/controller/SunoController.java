package com.nacos.controller;


import com.business.config.SetCookieReq;
import com.business.config.SetCookieResp;
import com.nacos.result.Result;
import com.nacos.service.SunoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Tag(name = "配置获取Token接口的参数", description = "配置获取Token接口的参数")
@RestController
@RequestMapping("/configuration")
@Slf4j
public class SunoController {

    @Resource
    public SunoService sunoService;

    @Operation(summary = "配置获取token的参数")
    @PostMapping(value = "/suno/setCookieParam", name = "配置获取token的参数")
    public Result<Object> updateSunoCookie(@RequestBody() SetCookieReq cookieReq) {
        return sunoService.updateSunoCookie(cookieReq);
    }
    @Operation(summary = "get配置获取token的参数列表")
    @GetMapping(value = "/suno/getCookieParam", name = "get配置获取token的参数列表")
    public Result<List<SetCookieResp>> getThirdPartyCookiesList() {
        return sunoService.getThirdPartyCookiesList();
    }

}
