package com.nacos.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.AdminUserLoginDTO;
import com.business.db.model.po.admin.AdminUserPO;
import com.business.db.model.vo.AdminUserVO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;
import com.nacos.service.IAdminUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "用户相关接口", description = "用户相关接口")
@RestController
@RequestMapping("/user")
@Slf4j
public class AdminUserController {

    @Resource
    private IAdminUserService adminUserService;

    @Operation(summary = "查询写真主题列表分页")
    @RequestMapping(value = "/queryPage",name = "查询写真主题列表分页", method = RequestMethod.POST)
    public Result<Page<AdminUserVO>>  queryPage(@RequestBody AdminUserLoginDTO dto) {
        return adminUserService.queryPage(dto);
    }

    @Operation(summary = "新增写真主题")
    @RequestMapping(value = "/add",name = "新增写真主题", method = RequestMethod.POST)
    public Result<?> add(@Validated @RequestBody AdminUserPO adminUserPO) {
        return adminUserService.add(adminUserPO);
    }

    @Operation(summary = "编辑写真主题")
    @RequestMapping(value = "/update",name = "编辑写真主题", method = RequestMethod.POST)
    public Result<?>  update(@Validated @RequestBody AdminUserPO adminUserPO) {
        return adminUserService.update(adminUserPO);
    }

    @Operation(summary = "删除写真主题")
    @RequestMapping(value = "/delete",name = "删除写真主题", method = RequestMethod.POST)
    public Result<?>  delete(@Validated @RequestBody BaseDeleteEntity params) {
        return adminUserService.delete(params);
    }

}
