package com.nacos.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.business.db.model.dto.SysReportRecordAuditDTO;
import com.business.db.model.dto.SysReportRecordQueryDTO;
import com.business.db.model.po.SysReportTypePO;
import com.business.db.model.vo.SysReportRecordVO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;

import java.util.List;

/**
 * 举报信息
 * @className: ISysReportService
 * @createDate: 2024-09-23
 *
 */
public interface ISysReportService {

    Result<IPage<SysReportRecordVO>> queryPage(SysReportRecordQueryDTO reportRecordQueryDTO);

    Result<List<SysReportTypePO>> getReportTypeList();

    Result<Integer> reportAudit(SysReportRecordAuditDTO sysReportRecordAuditDTO);

    Result<Integer> add(SysReportTypePO sysReportTypePO);

    Result<Integer>  update(SysReportTypePO sysReportTypePO);

    Result<Integer>  delete(BaseDeleteEntity params);

}
