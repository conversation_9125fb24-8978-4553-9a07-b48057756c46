package com.nacos.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.business.db.model.dto.ExchangeRioDTO;
import com.business.db.model.po.ExchangeRecordPO;
import com.business.db.model.po.ExchangeRioPO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;

import java.util.List;

public interface IExchangeRecordService {

    Result<IPage<ExchangeRecordPO>> queryExchangePage(ExchangeRioDTO exchangeRioDTO);

    Result<Integer> insertBatch(ExchangeRioPO exchangeRioPO);

    Result<Integer> delete(BaseDeleteEntity params);

    List<ExchangeRecordPO> exchangeList(List<Long> ids);

    /*用户兑换点子操作*/
//    Result<Void> updateExchange(ExchangeCodeReq req);
//
//
//    /**
//     * 导入兑换信息
//     * @param file
//     * @return
//     */
//    Result<?> importExchange(MultipartFile file) throws Exception;
//
//    /**
//     * 定时任务处理兑换码过期
//     */
//    void automaticTasks();
}
