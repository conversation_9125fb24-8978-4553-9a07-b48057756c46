package com.nacos.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.business.db.mapper.PhotoTopicMapper;
import com.business.db.mapper.PhotoTopicTypeMapper;
import com.business.db.model.dto.PhotoTopicTypeDTO;
import com.business.db.model.po.PhotoTopicPO;
import com.business.db.model.po.PhotoTopicTypePO;
import com.business.db.model.vo.PhotoTopicTypeVO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;
import com.nacos.service.IPhotoTopicTypeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class PhotoTopicTypeServiceImpl extends ServiceImpl<PhotoTopicTypeMapper, PhotoTopicTypePO> implements IPhotoTopicTypeService {

    @Resource
    private PhotoTopicMapper photoTopicMapper;

    @Override
    public Result<Page<PhotoTopicTypeVO>> queryPage(PhotoTopicTypeDTO dto) {
        Page<PhotoTopicTypeVO> page = new Page<>(dto.getPageNumber(),dto.getPageSize());
        return Result.SUCCESS(this.baseMapper.queryPage(page, dto));
    }

    @Override
    public Result<Boolean> add(List<PhotoTopicTypePO> photoTopicTypeList) {
        return Result.SUCCESS(this.saveBatch(photoTopicTypeList));
    }

    @Override
    public Result<Integer> update(PhotoTopicTypePO photoTopicTypePO) {
        photoTopicTypePO.setOperateTime(DateUtil.date());
        return Result.SUCCESS(this.baseMapper.updateById(photoTopicTypePO));
    }

    @Override
    public Result<Boolean> delete(BaseDeleteEntity params) {
        Long count = photoTopicMapper.selectCount(new LambdaQueryWrapper<PhotoTopicPO>()
                .in(PhotoTopicPO::getTopicTypeId, params.getIds()));
        if (count > 0) {
            return Result.ERROR("类型下面有模板数据，不能删除");
        }
        return Result.SUCCESS(this.removeByIds(params.getIds()));
    }

    @Override
    public Result<Boolean> sortBy(BaseDeleteEntity params) {
        for (int i = 0; i < params.getIds().size(); i++) {
            Long id = params.getIds().get(i);
            this.baseMapper.update(null, new LambdaUpdateWrapper<PhotoTopicTypePO>()
                    .eq(PhotoTopicTypePO::getId, id)
                    .set(PhotoTopicTypePO::getSort, i + 1));
        }
        return Result.SUCCESS();
    }

}
