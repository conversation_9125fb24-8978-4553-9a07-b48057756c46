package com.nacos.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.business.db.model.dto.PhotoTopicSceneDTO;
import com.business.db.model.po.PhotoTopicScenePO;
import com.business.db.model.vo.PhotoTopicSceneVO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;

import java.util.List;


public interface IPhotoTopicSceneService extends IService<PhotoTopicScenePO> {

    Result<Page<PhotoTopicSceneVO>> queryPage(PhotoTopicSceneDTO dto);

    Result<Boolean> add(List<PhotoTopicScenePO> photoTopicScenePOList);

    Result<Boolean>  update(PhotoTopicScenePO photoTopicScenePO);

    Result<Boolean>  delete(BaseDeleteEntity params);


}
