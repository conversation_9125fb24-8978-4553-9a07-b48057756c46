package com.nacos.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.business.db.model.dto.admin.AdminPayRecordPageDTO;
import com.business.db.model.dto.admin.AdminVipConfigPageDTO;
import com.business.db.model.dto.admin.UserQueryPageDTO;
import com.business.db.model.po.UserPO;
import com.business.db.model.vo.admin.AdminPayRecordVO;
import com.business.db.model.vo.admin.AdminVipConfigVO;
import com.business.db.model.vo.admin.UsersListVO;
import com.nacos.result.Result;

import java.util.List;


public interface IUserService extends IService<UserPO> {

    Result<?>  queryPage(UserQueryPageDTO dto);

    Result<List<UsersListVO>> selectUserList();

    Result<IPage<AdminPayRecordVO>>  queryOrderPage(AdminPayRecordPageDTO recordPageDTO);

    Result<IPage<AdminVipConfigVO>>  queryInterestsPage(AdminVipConfigPageDTO adminVipConfigPageDTO);

}
