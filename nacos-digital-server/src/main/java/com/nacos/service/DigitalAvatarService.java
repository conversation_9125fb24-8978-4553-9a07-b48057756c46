package com.nacos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.po.DigitalUserAvatarPO;
import com.nacos.entity.vo.DigitalUserAvatarVO;
import com.nacos.entity.dto.ChanJingAvatarRequestDTO;
import com.nacos.entity.dto.DigitalAvatarTrainDTO;
import com.nacos.model.ChanJing.model.CustomisedPersonResponse;
import com.nacos.model.ChanJing.model.GetVideoDetailResponse;
import com.nacos.result.Result;
import com.nacos.model.ChanJing.model.ListCustomisedPersonResponse;
import com.nacos.entity.dto.CreateVideoRequestDTO;

import java.util.List;

public interface DigitalAvatarService extends IService<DigitalUserAvatarPO> {
    
    /**
     * 获取数字人列表
     * @return 数字人列表
     */
    Result<List<DigitalUserAvatarVO>> listAvatars();
    
    /**
     * 添加数字人
     * @param avatarVO 数字人信息
     * @return 操作结果
     */
    Result<Boolean> addAvatar(DigitalUserAvatarVO avatarVO);
    
    /**
     * 更新数字人
     * @param avatarVO 数字人信息
     * @return 操作结果
     */
    Result<Boolean> updateAvatar(DigitalUserAvatarVO avatarVO);
    
    /**
     * 删除数字人
     * @param id 数字人ID
     * @return 操作结果
     */
    Result<Boolean> deleteAvatar(Long id);
    
    /**
     * 更新数字人状态
     * @param id 数字人ID
     * @param status 状态
     * @return 操作结果
     */
    Result<Boolean> updateStatus(Long id, Integer status);
    
    /**
     * 根据组ID和用户ID获取数字人列表
     * 获取指定组ID和用户ID的所有启用的数字人
     * @param groupId 组ID
     * @param userId 用户ID
     * @return 数字人列表
     */
    Result<List<DigitalUserAvatarVO>> listAvatarsByGroupId(Long groupId, String userId);
    
    /**
     * 根据用户ID获取数字人列表
     * @param userId 用户ID
     * @return 数字人列表
     */
    Result<List<DigitalUserAvatarVO>> listAvatarsByUserId(String userId);
    
    /**
     * 开始训练数字人
     * 上传训练视频、授权视频、声音文件，并进行声音克隆
     * @param trainDTO 数字人训练信息
     * @return 操作结果
     */
    Result<String> trainAvatar(DigitalAvatarTrainDTO trainDTO);

    /**
     * 开始训练数字人
     * 上传训练视频、授权视频、声音文件，并进行声音克隆
     * @param trainDTO 数字人训练信息
     * @return 操作结果
     */
    Result<String> trainAvatarNew(DigitalAvatarTrainDTO trainDTO);

    /**
     * 禅境定制数字人接口
     * @param requestDTO 禅境定制数字人请求
     * @return 操作结果
     */
    Result<String> createChanjingAvatar(ChanJingAvatarRequestDTO requestDTO);

    /**
     * 拉取禅境定制数字人列表
     * @param page 当前页
     * @param pageSize 每页数量
     * @return 操作结果，包含数字人列表和分页信息的JSON字符串
     */
    Result<String> listChanjingAvatars(Integer page, Integer pageSize);

    /**
     * 创建禅境视频合成任务
     * @param requestDTO 视频合成请求参数
     * @return 操作结果，成功时data为视频任务ID
     */
    Result<String> createChanjingVideo(CreateVideoRequestDTO requestDTO);

    /**
     * 拉取禅境定制数字人详细信息
     * @param avatarVideoId 禅境视频任务ID
     * @return 操作结果，包含数字人信息
     */
    Result<CustomisedPersonResponse> customisedPerson(String avatarVideoId);

    Result<GetVideoDetailResponse> getVideoDetail(String videoId);
} 