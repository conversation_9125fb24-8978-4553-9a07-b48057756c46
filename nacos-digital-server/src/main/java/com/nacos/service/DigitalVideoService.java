package com.nacos.service;

import com.nacos.entity.dto.DigitalVideoGenerationDTO;
import com.nacos.entity.vo.DigitalVO;
import com.nacos.result.Result;

/**
 * 数字人视频Service接口
 */
public interface DigitalVideoService {

    /**
     * 提交数字人视频生成请求（异步）
     *
     * @param digitalVideoGenerationDTO 数字人视频生成请求参数
     * @return 任务ID
     */
    Result<String> submitDigitalVideoGenerationRequest(DigitalVideoGenerationDTO digitalVideoGenerationDTO);

    /**
     * 处理排队中的任务
     */
    void processQueueingTasks();


    /**
     * 处理超时任务
     */
    void processTimeoutTasks();
}
