package com.nacos.service;

import com.nacos.entity.po.DigitalVoiceStylesPO;
import com.nacos.entity.vo.DigitalVoiceSystemVO;

import java.util.List;

/**
 * 音色双写服务接口
 * 负责在迁移期间同时写入新旧表
 */
public interface VoiceDualWriteService {
    
    /**
     * 双写音色数据
     * 同时写入旧表(digital_voice_styles)和新表(digital_voice_system)
     * 
     * @param oldVoice 旧表音色数据
     * @return 是否双写成功
     */
    boolean dualWriteVoice(DigitalVoiceStylesPO oldVoice);
    
    /**
     * 双写音色更新
     * 同时更新旧表和新表的音色数据
     * 
     * @param oldVoice 旧表音色数据
     * @return 是否双写成功
     */
    boolean dualUpdateVoice(DigitalVoiceStylesPO oldVoice);
    
    /**
     * 双写音色删除
     * 同时删除旧表和新表的音色数据
     * 
     * @param voiceId 音色ID
     * @return 是否双写成功
     */
    boolean dualDeleteVoice(String voiceId);
    
    /**
     * 批量双写音色数据
     * 
     * @param oldVoices 旧表音色数据列表
     * @return 成功双写的数量
     */
    int batchDualWriteVoices(List<DigitalVoiceStylesPO> oldVoices);
    
    /**
     * 同步分类数据到维度表
     * 将分类数据同步到新的维度表
     * 
     * @param categoryCode 分类编码
     * @param categoryName 分类名称
     * @return 是否同步成功
     */
    boolean syncCategoryToDimension(String categoryCode, String categoryName);
    
    /**
     * 同步标签数据到新标签表
     * 将标签数据同步到新的标签表
     * 
     * @param tagCode 标签编码
     * @param tagName 标签名称
     * @param dimensionCode 所属维度编码
     * @return 是否同步成功
     */
    boolean syncTagToNewTable(String tagCode, String tagName, String dimensionCode);
    
    /**
     * 同步音色分类关联到新表
     * 将音色分类关联关系同步到新的标签关联表
     * 
     * @param voiceId 音色ID
     * @param categoryIds 分类ID列表
     * @return 是否同步成功
     */
    boolean syncVoiceCategoryRelations(String voiceId, List<Long> categoryIds);
    
    /**
     * 验证新旧表数据一致性
     * 
     * @param voiceId 音色ID
     * @return 是否一致
     */
    boolean validateDataConsistency(String voiceId);
    
    /**
     * 生成新的音色ID
     * 按照新的ID格式生成音色ID
     * 
     * @return 新的音色ID
     */
    String generateNewVoiceId();
    
    /**
     * 获取音色ID映射关系
     * 
     * @param oldVoiceId 旧音色ID
     * @return 新音色ID，如果不存在映射则返回null
     */
    String getNewVoiceId(String oldVoiceId);
    
    /**
     * 保存音色ID映射关系
     * 
     * @param oldVoiceId 旧音色ID
     * @param newVoiceId 新音色ID
     */
    void saveVoiceIdMapping(String oldVoiceId, String newVoiceId);
}
