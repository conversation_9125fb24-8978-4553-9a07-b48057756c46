package com.nacos.service;

import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.entity.vo.DigitalAudioTaskVO;
import com.nacos.entity.vo.PageResultVO;
import com.nacos.result.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数字人音频生成任务表PO 服务类
 * </p>
 *
 * <AUTHOR> @since 2024-07-30
 */
public interface IDigitalAudioTaskService extends IService<DigitalAudioTaskPO> {

    /**
     * 根据任务ID查找任务
     *
     * @param taskId 任务ID
     * @return 任务视图对象VO，如果未找到或已删除则可能为null
     */
    DigitalAudioTaskVO findByTaskId(String taskId);

    /**
     * 根据用户ID查找任务列表 (确保未删除，按创建时间降序)
     *
     * @param userId 用户ID
     * @return 任务视图对象VO列表
     */
    List<DigitalAudioTaskVO> findByUserId(String userId);

    /**
     * 创建一个新的音频生成任务
     *
     * @param taskPO 任务实体PO，应包含必要的初始信息如taskId, userId, requestParamsJson
     * @return 创建成功返回任务ID，失败返回null
     */
    Result<String> createTask(DigitalAudioTaskPO taskPO);

    /**
     * 更新任务状态
     *
     * @param taskId   任务ID
     * @param status   新的状态码
     * @param errorMsg 如果任务失败，则为错误信息；成功则可为null
     * @return true 如果更新成功, false otherwise
     */
    boolean updateTaskStatus(String taskId, Integer status, String errorMsg);

    /**
     * 标记任务完成，并记录生成的音频URL和时长
     *
     * @param taskId            任务ID
     * @param generatedAudioUrl 生成的音频URL
     * @param durationMs        音频时长（毫秒）
     * @return true 如果更新成功, false otherwise
     */
    boolean completeTask(String taskId, String generatedAudioUrl, Integer durationMs);

    /**
     * 逻辑删除任务
     * @param taskId 任务ID
     * @return true 如果删除成功
     */
    boolean deleteTaskLogically(String taskId);


    /**
     * 查询任务列表
     * @param params 查询参数（包含分页参数：pageNum、pageSize）
     * @return 包含分页信息和任务列表的结果
     */
    Result<Page<DigitalAudioTaskVO>> listTasks(Map<String, Object> params);

} 