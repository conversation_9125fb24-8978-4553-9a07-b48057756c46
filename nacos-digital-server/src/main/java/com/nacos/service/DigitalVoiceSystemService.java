package com.nacos.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.po.DigitalVoiceSystemPO;
import com.nacos.entity.vo.DigitalVoiceSystemVO;
import com.nacos.entity.vo.VoiceMultiDimensionQueryVO;

import java.util.List;
import java.util.Map;

/**
 * 系统音色服务接口
 */
public interface DigitalVoiceSystemService extends IService<DigitalVoiceSystemPO> {
    
    /**
     * 多维度查询音色
     *
     * @param dimensionConditions 维度查询条件 Map<维度编码, 标签编码列表>
     * @param keyword 关键词搜索
     * @param providers 供应商筛选
     * @param languages 语言筛选
     * @param genders 性别筛选
     * @param syncIds 同步表ID筛选
     * @param thirdPartyVoiceIds 第三方音色ID筛选
     * @param queryLogic 查询逻辑：AND/OR
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 多维度查询结果
     */
    VoiceMultiDimensionQueryVO multiDimensionQuery(
        Map<String, List<String>> dimensionConditions,
        String keyword,
        List<String> providers,
        List<String> languages,
        List<String> genders,
        List<Long> syncIds,
        List<String> thirdPartyVoiceIds,
        String queryLogic,
        Integer currentPage,
        Integer pageSize
    );
    
    /**
     * 根据音色ID获取音色详情
     * 
     * @param voiceId 音色ID
     * @return 音色详情，包含标签信息
     */
    DigitalVoiceSystemVO getVoiceDetail(String voiceId);
    
    /**
     * 根据音色ID列表获取音色信息
     * 
     * @param voiceIds 音色ID列表
     * @return 音色信息列表
     */
    List<DigitalVoiceSystemVO> getVoicesByIds(List<String> voiceIds);
    
    /**
     * 获取推荐音色列表
     * 
     * @param limit 限制数量
     * @return 推荐音色列表
     */
    List<DigitalVoiceSystemVO> getRecommendedVoices(Integer limit);
    
    /**
     * 根据供应商获取音色列表
     * 
     * @param provider 供应商标识
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 分页音色列表
     */
    Page<DigitalVoiceSystemVO> getVoicesByProvider(String provider, Integer currentPage, Integer pageSize);
    
    /**
     * 根据标签获取音色列表
     * 
     * @param tagIds 标签ID列表
     * @param queryLogic 查询逻辑：AND/OR
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 分页音色列表
     */
    Page<DigitalVoiceSystemVO> getVoicesByTags(List<Long> tagIds, String queryLogic, Integer currentPage, Integer pageSize);
    
    /**
     * 搜索音色
     * 
     * @param keyword 关键词
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 分页音色列表
     */
    Page<DigitalVoiceSystemVO> searchVoices(String keyword, Integer currentPage, Integer pageSize);
    
    /**
     * 更新音色标签关联
     * 
     * @param voiceId 音色ID
     * @param tagIds 标签ID列表
     */
    void updateVoiceTagRelations(String voiceId, List<Long> tagIds);
    
    /**
     * 批量更新音色标签关联
     * 
     * @param voiceTagMap 音色ID与标签ID列表的映射
     */
    void batchUpdateVoiceTagRelations(Map<String, List<Long>> voiceTagMap);
    
    /**
     * 创建音色
     * 
     * @param voiceSystemVO 音色信息
     * @return 创建的音色信息
     */
    DigitalVoiceSystemVO createVoice(DigitalVoiceSystemVO voiceSystemVO);
    
    /**
     * 更新音色信息
     * 
     * @param voiceId 音色ID
     * @param voiceSystemVO 音色信息
     * @return 更新后的音色信息
     */
    DigitalVoiceSystemVO updateVoice(String voiceId, DigitalVoiceSystemVO voiceSystemVO);
    
    /**
     * 删除音色（逻辑删除）
     * 
     * @param voiceId 音色ID
     * @return 是否删除成功
     */
    boolean deleteVoice(String voiceId);
    
    /**
     * 更新音色状态
     * 
     * @param voiceId 音色ID
     * @param status 状态：0-下架 1-上架
     * @return 是否更新成功
     */
    boolean updateVoiceStatus(String voiceId, Integer status);
    
    /**
     * 更新音色推荐状态
     * 
     * @param voiceId 音色ID
     * @param isRecommended 是否推荐：0-否 1-是
     * @return 是否更新成功
     */
    boolean updateVoiceRecommendStatus(String voiceId, Integer isRecommended);
    
    /**
     * 获取音色统计信息
     * 
     * @return 统计信息Map
     */
    Map<String, Object> getVoiceStatistics();
    
    /**
     * 根据维度统计音色数量
     * 
     * @return 维度统计信息
     */
    Map<String, Map<String, Integer>> getVoiceCountByDimensions();
}
