package com.nacos.service;

import com.nacos.entity.vo.DigitalAvatarInstanceDetailVO;
import com.nacos.entity.vo.DigitalAvatarInstanceListVO;
import com.nacos.entity.vo.DigitalAvatarCategoryStatsVO;
import com.nacos.entity.vo.DigitalAvatarGroupListVO;
import com.nacos.result.Result;
import java.util.List;

/**
 * 数字人分身实例服务接口
 * 基于正确的数据模型：分类 -> 组 -> 分身
 */
public interface DigitalAvatarInstanceService {

    // 注意：用户组列表功能已存在于 DigitalUserGroupService.listUserGroups(userId)

    /**
     * 获取组下的分身列表
     *
     * @param groupId 组ID
     * @param userId 用户ID，用于权限验证
     * @return 该组下的数字人分身列表
     */
    Result<DigitalAvatarInstanceListVO> listInstancesByGroup(String groupId, String userId);

    /**
     * 获取数字人分身详情
     *
     * @param avatarId 数字人ID
     * @param userId 用户ID，用于权限验证
     * @return 数字人分身详细信息，包含关联的音色信息
     */
    Result<DigitalAvatarInstanceDetailVO> getAvatarInstanceDetail(String avatarId, String userId);

    /**
     * 获取数字人分类统计信息
     *
     * @param userId 用户ID，用于统计我的数字人数量
     * @return 分类统计信息，区分我的数字人和公共数字人
     */
    Result<DigitalAvatarCategoryStatsVO> getCategoryStats(String userId);
}
