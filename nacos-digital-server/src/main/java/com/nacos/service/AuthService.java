package com.nacos.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.entity.po.DigitalPayRecord;
import com.nacos.mapper.DigitalPayRecordMapper;
import com.nacos.utils.JwtNewUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 认证服务类
 * 用于处理用户认证和会员等级判断
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final DigitalPayRecordMapper digitalPayRecordMapper;

    /**
     * 获取用户会员等级
     * 0-普通用户 1-VIP 2-SVIP
     */
    public Integer getMemberLevel(Long userId) {
        try {
            DigitalPayRecord record = digitalPayRecordMapper.selectOne(
                new LambdaQueryWrapper<DigitalPayRecord>()
                    .eq(DigitalPayRecord::getUserId, userId)
                    .eq(DigitalPayRecord::getState, 1) // 支付成功状态
                    .ge(DigitalPayRecord::getExpirationTime, LocalDateTime.now()) // 未过期
                    .orderByDesc(DigitalPayRecord::getExpirationTime)
                    .last("LIMIT 1")
            );
            return record != null ? record.getVipGrade() : 0;
        } catch (Exception e) {
            log.error("获取用户会员等级异常", e);
            return 0;
        }
    }
    
    /**
     * 获取当前用户会员等级
     */
    public Integer getCurrentUserMemberLevel() {
        Long userId = JwtNewUtil.getUserId();
        if (userId == null) {
            return 0;
        }
        return getMemberLevel(userId);
    }
    
    /**
     * 获取用户角色列表
     */
    public List<String> getUserRoles(Long userId) {
        List<String> roles = new ArrayList<>();
        roles.add("USER"); // 默认角色
        
        Integer memberLevel = getMemberLevel(userId);
        if (memberLevel != null) {
            if (memberLevel >= 1) {
                roles.add("VIP");
            }
            if (memberLevel >= 2) {
                roles.add("SVIP");
            }
        }
        
        return roles;
    }
} 