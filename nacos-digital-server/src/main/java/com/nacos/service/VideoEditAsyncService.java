package com.nacos.service;

import com.nacos.entity.po.VideoEditTaskItemPO;
import com.nacos.entity.po.VideoEditTaskPO;
import com.nacos.result.Result;

import java.util.List;

/**
 * 视频编辑异步处理Service接口
 * 参考DigitalVideoAsyncService的设计模式
 */
public interface VideoEditAsyncService {

    /**
     * 异步处理视频编辑任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param taskItems 任务项列表
     */
    void processEditTasks(String taskId, String userId, List<VideoEditTaskItemPO> taskItems);

    /**
     * 提交单个编辑操作
     * @param taskItemPO 任务项
     * @param userId 用户ID
     * @return 处理结果
     */
    Result<String> submitEditOperation(VideoEditTaskItemPO taskItemPO, String userId);

    /**
     * 检查任务状态
     * @param apiJobId 第三方API任务ID
     * @param taskItemPO 任务项
     * @param userId 用户ID
     * @return 检查结果
     */
    Result<String> checkTaskStatus(String apiJobId, VideoEditTaskItemPO taskItemPO, String userId);

    /**
     * 处理任务完成逻辑
     * @param mainTask 主任务
     * @return 处理结果
     */
    Result<String> handleTaskCompletion(VideoEditTaskPO mainTask);

    /**
     * 计算编辑操作的费用乘数
     * @param taskId 任务ID
     * @return 费用乘数
     */
    int calculateCostMultiplier(String taskId);
}
