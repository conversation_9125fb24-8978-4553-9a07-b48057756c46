package com.nacos.service;

import com.nacos.entity.bo.VideoEditTaskStatusUpdateBO;
import com.nacos.entity.bo.VideoEditSubTaskUpdateBO;
import com.nacos.entity.dto.VideoEditTaskItem;
import com.nacos.entity.po.VideoEditTaskItemPO;
import com.nacos.entity.vo.VideoEditTaskVO;
import com.nacos.entity.vo.VideoEditTaskStatusVO;
import com.nacos.entity.vo.VideoEditTaskItemWithCoverVO;
import com.nacos.result.Result;

import java.util.List;

/**
 * 视频编辑任务Service接口
 * 参考DigitalVideoTaskService的设计模式
 */
public interface VideoEditTaskService {

    /**
     * 创建视频编辑任务
     *
     * @param userId 用户ID
     * @param taskItems 任务项列表
     * @return 任务ID
     */
    Result<String> createTask(String userId, List<VideoEditTaskItem> taskItems);

    /**
     * 创建视频编辑任务（支持全局屏幕尺寸参数）
     *
     * @param userId 用户ID
     * @param taskItems 任务项列表
     * @param globalScreenWidth 全局屏幕宽度（可选）
     * @param globalScreenHeight 全局屏幕高度（可选）
     * @return 任务ID
     */
    Result<String> createTask(String userId, List<VideoEditTaskItem> taskItems, Integer globalScreenWidth, Integer globalScreenHeight);

    /**
     * 查询用户的视频编辑任务列表
     *
     * @param userId 用户ID
     * @return 任务列表
     */
    Result<List<VideoEditTaskVO>> listUserTasks(String userId);

    /**
     * 查询任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情，包含子任务列表
     */
    Result<VideoEditTaskVO> getTaskDetail(String taskId);

    /**
     * 更新主任务数据（使用BO对象）
     *
     * @param updateBO 主任务数据更新BO
     * @return 更新结果
     */
    Result<Boolean> updateTask(VideoEditTaskStatusUpdateBO updateBO);

    /**
     * 更新子任务数据（使用BO对象）
     *
     * @param updateBO 子任务数据更新BO
     * @return 更新结果
     */
    Result<Boolean> updateSubTask(VideoEditSubTaskUpdateBO updateBO);

    /**
     * 获取用户的进行中和成功的任务列表
     * @param userId 用户ID
     * @return 任务状态列表
     */
    List<VideoEditTaskStatusVO> getActiveTaskList(String userId);

    /**
     * 根据状态查询任务列表（用于异步处理）
     * @param status 任务状态
     * @param limit 限制数量
     * @return 任务列表
     */
    Result<List<VideoEditTaskVO>> getTasksByStatus(Integer status, Integer limit);

    /**
     * 统计用户任务数量
     * @param userId 用户ID
     * @param status 任务状态，null表示统计所有状态
     * @return 任务数量
     */
    Result<Integer> countUserTasks(String userId, Integer status);

    /**
     * 根据任务ID获取子任务列表
     * @param taskId 主任务ID
     * @return 子任务列表
     */
    List<VideoEditTaskItemPO> getTaskItemsByTaskId(String taskId);

    /**
     * 获取用户活跃任务列表（支持分页）
     * @param userId 用户ID
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @param includeSubTasks 是否包含子任务详情
     * @return 任务状态列表
     */
    Result<List<VideoEditTaskStatusVO>> getActiveTaskListPaged(String userId, Integer pageNum, Integer pageSize, Boolean includeSubTasks);

    /**
     * 获取任务状态（支持子任务分页）
     * @param taskId 任务ID
     * @param subTaskPageNum 子任务页码（从1开始）
     * @param subTaskPageSize 子任务每页大小
     * @return 任务状态信息
     */
    Result<VideoEditTaskStatusVO> getTaskStatusWithPagedSubTasks(String taskId, Integer subTaskPageNum, Integer subTaskPageSize);

    /**
     * 获取进行中的子任务列表
     * 用于定时任务轮询状态检查
     * @return 进行中的子任务列表
     */
    List<VideoEditTaskItemPO> getInProgressTaskItems();

    /**
     * 获取带主任务封面信息的子任务详情
     * @param subTaskId 子任务ID
     * @return 带封面信息的子任务详情
     */
    Result<VideoEditTaskItemWithCoverVO> getSubTaskWithCover(String subTaskId);

    /**
     * 根据任务ID获取所有带主任务封面信息的子任务列表
     * @param taskId 主任务ID
     * @return 带封面信息的子任务列表
     */
    Result<List<VideoEditTaskItemWithCoverVO>> getSubTasksWithCover(String taskId);
}
