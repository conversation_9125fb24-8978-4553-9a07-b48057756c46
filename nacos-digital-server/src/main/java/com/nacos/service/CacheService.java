//package com.nacos.service;
//
//import org.springframework.cache.CacheManager;
//import org.springframework.stereotype.Service;
//
//@Service
//public class CacheService {
//
//    private final CacheManager cacheManager;
//
//    public CacheService(CacheManager cacheManager) {
//        this.cacheManager = cacheManager;
//    }
//
//    // 手动清除某个缓存 key
//    public void evictCache(String cacheName, Object key) {
//        org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
//        if (cache != null) {
//            cache.evict(key);
//        }
//    }
//
//    // 清除整个缓存名称下的所有数据
//    public void evictAllCache(String cacheName) {
//        org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
//        if (cache != null) {
//            cache.clear();
//        }
//    }
//}
//
