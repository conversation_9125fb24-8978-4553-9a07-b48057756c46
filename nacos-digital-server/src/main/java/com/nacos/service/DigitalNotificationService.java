package com.nacos.service;

import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.entity.po.DigitalNotificationPO;
import com.nacos.result.Result;

import java.util.List;

public interface DigitalNotificationService {

    /**
     * 创建数字人通知
     */
    Result<Void> createNotification(Long userId, String taskId, Integer notifType, String notifContent);

    /**
     * 获取用户未读通知列表
     */
    Result<List<DigitalNotificationPO>> getUnreadNotifications(Long userId);

    /**
     * 标记通知为已读
     */
    Result<Void> markAsRead(Long userId, Long notificationId);

    /**
     * 删除通知
     */
    Result<Void> deleteNotification(Long userId, Long notificationId);

    // ==================== 音频生成任务通知方法 ====================

    /**
     * 发送音频生成任务已接收通知（排队中）
     *
     * @param taskPO 任务PO对象
     */
    void sendAudioGenerationQueuedNotification(DigitalAudioTaskPO taskPO);

    /**
     * 发送音频生成任务开始处理通知
     *
     * @param taskPO 任务PO对象
     */
    void sendAudioGenerationStartNotification(DigitalAudioTaskPO taskPO);

    /**
     * 发送音频生成任务成功完成通知
     *
     * @param taskPO 任务PO对象
     * @param audioUrl 生成的音频URL
     * @param durationMs 音频时长（毫秒）
     */
    void sendAudioGenerationSuccessNotification(DigitalAudioTaskPO taskPO, String audioUrl, Integer durationMs);

    /**
     * 发送音频生成任务失败通知
     *
     * @param taskPO 任务PO对象
     * @param errorMessage 错误信息
     */
    void sendAudioGenerationFailureNotification(DigitalAudioTaskPO taskPO, String errorMessage);

    /**
     * 发送音频生成任务超时通知
     *
     * @param taskPO 任务PO对象
     */
    void sendAudioGenerationTimeoutNotification(DigitalAudioTaskPO taskPO);
}