package com.nacos.service;

import com.business.db.model.po.digital.DigitalVectorTaskPO;
import com.nacos.entity.dto.DigitalVideoGenerationDTO;
import com.business.db.model.po.digital.DigitalVectorItemPO;
import com.nacos.result.Result;

import java.util.List;

/**
 * 数字人视频Service接口
 */
public interface DigitalVectorService {

    /**
     * 提交数字人视频生成请求（异步）
     *
     * @param digitalVideoGenerationDTO 数字人视频生成请求参数
     * @return 任务ID
     */
    Result<String> addDoc(DigitalVideoGenerationDTO digitalVideoGenerationDTO);

    /**
     * 处理排队中的任务
     */
    void processQueueingTasks();


    /**
     * 处理超时任务
     */
    void processTimeoutTasks();

    void saveVectorDocument(String userId, String originalFilename, String fileUrl, String md5,Double vectorSize);

    List<DigitalVectorItemPO> getUserActiveDocuments(String userId);

    void processItemIndex();

    void processDocument();

    void processDocumentStatus();

    void processAddDocumentsJob();

    void updateDocumentStatus(String docId, String userId, int knowledgeItemStatus15);

    DigitalVectorTaskPO getIndexInfo(Long userId);

    void processDelIndexDoc();

    void processDelSpaceDoc();
}
