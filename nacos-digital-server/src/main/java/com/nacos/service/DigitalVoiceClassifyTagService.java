package com.nacos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.po.DigitalVoiceClassifyTagPO;
import com.nacos.entity.vo.VoiceClassifyTagVO;

import java.util.List;
import java.util.Map;

/**
 * 音色分类标签服务接口
 */
public interface DigitalVoiceClassifyTagService extends IService<DigitalVoiceClassifyTagPO> {
    
    /**
     * 根据维度获取标签列表
     * 
     * @param dimensionCode 维度编码
     * @return 该维度下的标签列表
     */
    List<VoiceClassifyTagVO> listTagsByDimension(String dimensionCode);
    
    /**
     * 根据维度获取层级标签树
     * 
     * @param dimensionCode 维度编码
     * @return 该维度下的层级标签树
     */
    List<VoiceClassifyTagVO> getTagTreeByDimension(String dimensionCode);
    
    /**
     * 根据标签编码获取标签信息
     * 
     * @param tagCode 标签编码
     * @return 标签信息，如果不存在则返回null
     */
    VoiceClassifyTagVO getTagByCode(String tagCode);
    
    /**
     * 根据标签ID列表获取标签信息
     * 
     * @param tagIds 标签ID列表
     * @return 标签信息列表
     */
    List<VoiceClassifyTagVO> getTagsByIds(List<Long> tagIds);
    
    /**
     * 获取所有启用的标签，按维度分组
     * 
     * @return 按维度分组的标签Map
     */
    Map<String, List<VoiceClassifyTagVO>> listTagsGroupByDimension();
    
    /**
     * 创建新标签
     * 
     * @param tagCode 标签编码
     * @param tagName 标签名称
     * @param dimensionCode 维度编码
     * @param parentId 父标签ID
     * @param sortOrder 排序号
     * @return 创建的标签信息
     */
    VoiceClassifyTagVO createTag(String tagCode, String tagName, String dimensionCode, Long parentId, Integer sortOrder);
    
    /**
     * 更新标签信息
     * 
     * @param tagCode 标签编码
     * @param tagName 标签名称
     * @param parentId 父标签ID
     * @param sortOrder 排序号
     * @param status 状态
     * @return 更新后的标签信息
     */
    VoiceClassifyTagVO updateTag(String tagCode, String tagName, Long parentId, Integer sortOrder, Integer status);
    
    /**
     * 删除标签（逻辑删除）
     * 
     * @param tagCode 标签编码
     * @return 是否删除成功
     */
    boolean deleteTag(String tagCode);
    
    /**
     * 检查标签编码是否存在
     * 
     * @param tagCode 标签编码
     * @return 是否存在
     */
    boolean existsByCode(String tagCode);
    
    /**
     * 获取标签的子标签列表
     * 
     * @param parentId 父标签ID
     * @return 子标签列表
     */
    List<VoiceClassifyTagVO> getChildTags(Long parentId);
    
    /**
     * 统计标签下的音色数量
     * 
     * @param tagId 标签ID
     * @return 音色数量
     */
    Integer countVoicesByTag(Long tagId);
    
    /**
     * 批量统计标签下的音色数量
     * 
     * @param tagIds 标签ID列表
     * @return 标签ID与音色数量的映射
     */
    Map<Long, Integer> batchCountVoicesByTags(List<Long> tagIds);
    
    /**
     * 初始化基础标签数据
     */
    void initializeBaseTags();
}
