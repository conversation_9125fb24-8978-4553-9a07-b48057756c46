package com.nacos.service;

/**
 * 数字人视频Service接口
 */
public interface ChangjinMiniService {

    //处理排队中的任务
    void processQueueingTasks();


    //处理超时任务
    void processTimeoutTasks();

    //训练
    void processVideo();

    //上传
    void processVoiceUpload();

    //推送
    void processFinish();

    //训练状态
    void processVideoTrainStatus();

    //克隆
    void processVoiceClone();

    //生成视频状态
    void processVideoGenerationTaskStatus();

    //生成视频
    void processVideoGeneration();

    //生成音频
    void processVoiceGeneration();

    //视频任务状态
    void processVideoTaskStatusFinish();

    //视频状态
    void processVideoStatus();
}
