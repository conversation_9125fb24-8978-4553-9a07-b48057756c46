package com.nacos.service;

import com.nacos.result.Result;

/**
 * 检验点子数
 * <AUTHOR>
 * @date 2023-12-28
 */
public interface CheckService {

    /**
     * 账户点点余额消费--扣点子
     * @param userId 用户id
     * @param deduct 扣除金额
     * @return 返回是否成功true/false
     */
    Result<Boolean> getResidueDDDeduct(Long userId, Double deduct);

    Result<Boolean> kouSpace(Long userId, Double dzNumber);

    /**
     * 设置扣除错误账户信息点点余额 -- 退还点子
     * @param userId 用户id
     * @param ddQuantity 点点数量
     * @return 返回是否成功true/false
     */
    boolean setReturnDD(Long userId,Double ddQuantity);

    boolean setReturnSpace(Long userId, Double ddQuantity);

    Result<Boolean> checkYue(Long userId, double dzNumber);

    Result<Boolean> checkKownledgeSpace(Long userId, Double fileSize);

}
