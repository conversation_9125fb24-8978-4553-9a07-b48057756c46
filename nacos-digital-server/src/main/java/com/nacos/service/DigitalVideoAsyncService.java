package com.nacos.service;

import com.nacos.entity.dto.DigitalTaskItem;
import com.nacos.entity.po.DigitalVideoTaskItemPO;
import com.nacos.entity.po.DigitalVideoTaskPO;
import com.nacos.entity.vo.DigitalAudioVO;
import com.nacos.result.Result;

import java.util.List;
import java.util.Map;

/**
 * 数字人视频异步处理Service接口
 */
public interface DigitalVideoAsyncService {

    /**
     * 异步处理视频任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param taskItems 任务项列表
     */
    void processVideoTasks(String taskId, String userId, List<DigitalVideoTaskItemPO> taskItems);

    /**
     * 提交数字人视频音频任务
     * @param taskItemPO
     * @param userId
     * @return
     */
    Result<DigitalAudioVO> submitAudioTask(DigitalVideoTaskItemPO taskItemPO, String userId);

    /**
     * 检查并合并视频
     * @param mainTask
     * @return
     */
    Result<String> checkAndMergeVideos(DigitalVideoTaskPO mainTask);

    /**
     * 计算音频长度
     * @param taskId
     * @return
     */
    int countAudioLength(String taskId);
}