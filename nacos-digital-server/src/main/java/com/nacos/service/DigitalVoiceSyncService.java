package com.nacos.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.po.DigitalVoiceSyncPO;
import com.nacos.entity.vo.DigitalVoiceSyncVO;
import com.nacos.result.Result;

import java.util.List;
import java.util.Map;

/**
 * 音色同步服务接口
 * 负责第三方API音色数据的同步管理
 */
public interface DigitalVoiceSyncService extends IService<DigitalVoiceSyncPO> {
    
    /**
     * 同步所有供应商的音色数据
     * @return 同步结果
     */
    Result<String> syncAllVoiceData();
    
    /**
     * 同步指定供应商的音色数据
     * @param provider 供应商标识
     * @return 同步结果
     */
    Result<String> syncVoiceDataByProvider(String provider);
    
    /**
     * 同步Minimax语音列表
     * @return 同步结果
     */
    Result<String> syncMinimaxVoiceData();
    
    /**
     * 同步Microsoft语音列表
     * @return 同步结果
     */
    Result<String> syncMicrosoftVoiceData();
    
    /**
     * 同步ElevenLabs语音列表
     * @return 同步结果
     */
    Result<String> syncElevenLabsVoiceData();
    
    /**
     * 重试失败的同步记录
     * @return 重试结果
     */
    Result<String> retryFailedSyncs();
    
    /**
     * 重试指定供应商的失败同步记录
     * @param provider 供应商标识
     * @return 重试结果
     */
    Result<String> retryFailedSyncsByProvider(String provider);
    
    /**
     * 获取同步状态统计
     * @return 统计信息
     */
    Map<String, Object> getSyncStatusStatistics();
    
    /**
     * 获取同步状态统计（按供应商）
     * @param provider 供应商标识
     * @return 统计信息
     */
    Map<String, Object> getSyncStatusStatisticsByProvider(String provider);
    
    /**
     * 分页查询同步记录
     * @param provider 供应商标识（可选）
     * @param syncStatus 同步状态（可选）
     * @param keyword 关键词搜索（可选）
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    Page<DigitalVoiceSyncVO> querySyncRecords(
        String provider, 
        Integer syncStatus, 
        String keyword, 
        Integer currentPage, 
        Integer pageSize
    );
    
    /**
     * 根据ID获取同步记录详情
     * @param id 同步记录ID
     * @return 同步记录详情
     */
    DigitalVoiceSyncVO getSyncRecordDetail(Long id);
    
    /**
     * 发布同步记录到系统表
     * @param syncId 同步记录ID
     * @return 发布结果
     */
    Result<String> publishSyncRecord(Long syncId);
    
    /**
     * 批量发布同步记录到系统表
     * @param syncIds 同步记录ID列表
     * @return 发布结果
     */
    Result<String> batchPublishSyncRecords(List<Long> syncIds);
    
    /**
     * 取消发布（从系统表移除）
     * @param syncId 同步记录ID
     * @return 取消发布结果
     */
    Result<String> unpublishSyncRecord(Long syncId);
    
    /**
     * 更新同步记录
     * @param syncId 同步记录ID
     * @param syncVO 更新的同步记录信息
     * @return 更新结果
     */
    Result<String> updateSyncRecord(Long syncId, DigitalVoiceSyncVO syncVO);
    
    /**
     * 删除同步记录
     * @param syncId 同步记录ID
     * @return 删除结果
     */
    Result<String> deleteSyncRecord(Long syncId);
    
    /**
     * 清理过期的失败记录
     * @param days 保留天数
     * @return 清理结果
     */
    Result<String> cleanExpiredFailedRecords(Integer days);
    
    /**
     * 获取最近的同步日志
     * @param limit 限制数量
     * @return 同步日志列表
     */
    List<DigitalVoiceSyncVO> getRecentSyncLogs(Integer limit);
    
    /**
     * 检查数据一致性
     * @return 一致性检查结果
     */
    Map<String, Object> checkDataConsistency();
    
    /**
     * 修复数据不一致问题
     * @return 修复结果
     */
    Result<String> fixDataInconsistency();
}
