package com.nacos.service;

import com.nacos.entity.dto.VideoEditRequestDTO;
import com.nacos.result.Result;

/**
 * 视频编辑Service接口
 * 参考DigitalVideoService的设计模式
 */
public interface VideoEditService {

    /**
     * 提交视频编辑请求（异步）
     *
     * @param videoEditRequestDTO 视频编辑请求参数
     * @return 任务ID
     */
    Result<String> submitVideoEditRequest(VideoEditRequestDTO videoEditRequestDTO);

    /**
     * 处理排队中的任务
     */
    void processQueueingTasks();

    /**
     * 处理进行中的任务状态检查
     * 用于轮询禅境API状态，确保状态码10（生成中）能继续被检查
     */
    void processInProgressTasks();

    /**
     * 处理超时任务
     */
    void processTimeoutTasks();
}
