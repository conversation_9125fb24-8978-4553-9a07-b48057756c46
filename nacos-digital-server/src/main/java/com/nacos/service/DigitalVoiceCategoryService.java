package com.nacos.service;

import com.nacos.entity.po.DigitalVoiceCategoryPO;
import com.nacos.entity.vo.DigitalVoiceCategoryVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 音色分类服务接口
 */
public interface DigitalVoiceCategoryService {
    
    /**
     * 获取所有启用的分类列表
     * 
     * @return 启用状态的分类列表
     */
    List<DigitalVoiceCategoryPO> listEnabledCategories();
    
    /**
     * 根据分类编码获取音色列表
     * 
     * @param categoryCode 分类编码
     * @return 该分类下的音色ID列表
     */
    List<String> listVoiceIdsByCategory(String categoryCode);
    
    /**
     * 更新音色分类关联
     * 
     * @param voiceId 音色ID
     * @param categoryIds 分类ID列表
     */
    void updateVoiceCategoryRelation(String voiceId, List<Long> categoryIds);

    /**
     * 获取所有分类及其下的音色列表
     * 
     * @return 包含音色列表的分类信息列表
     */
    List<DigitalVoiceCategoryVO> listCategoriesWithVoices();
    
    /**
     * 根据分类编码获取分类信息及其下的音色列表
     * 
     * @param categoryCode 分类编码
     * @return 包含音色列表的分类信息，如果分类不存在则返回null
     */
    DigitalVoiceCategoryVO getCategoryWithVoices(String categoryCode);

    /**
     * 获取所有分类（不包含音色列表）
     * 
     * @return 不包含音色列表的分类信息列表
     */
    List<DigitalVoiceCategoryVO> listAllCategories();

    /**
     * 批量更新系统音色的分类关联
     * @param voiceCategories Map<音色ID, 分类编码>
     */
    void batchUpdateSystemVoiceCategories(List<String> voiceIds, String categoryCode);


    void initializeSystemVoiceCategories();
}