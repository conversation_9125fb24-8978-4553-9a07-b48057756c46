package com.nacos.service;

import com.business.db.model.po.FlowRecordPO;
import com.nacos.result.Result;

/**
 * 检验点子数
 * <AUTHOR>
 * @date 2023-12-28
 */
public interface FeiyongService {

    /**
     * 退费
     * @param userId
     * @param logId
     * @param remark
     * @param feiyongType
     */
    void tuifei(Long userId, Long logId, String remark,String feiyongType);

    /**
     * 检查余额
     * @param userId
     * @return
     */
    void tuiKownledgeSpace(Long userId, Long id, String remark, Double fileSize);

    boolean checkYue(Long userId, String feiyongType);

    /**
     * 扣费
     * @return
     */
    boolean checkKownledgeSpace(long l, String type, Double fileSize);

    boolean koufei(Long userId, Long logId, String remark, String feiyongType);

    boolean kouKownledgeSpace(String userId, Long id, String remark, Double fileSize);

    /**
     * 按乘数退费multiplier
     * @param userId
     * @param logId
     * @param remark
     * @param feiyongType
     * @param multiplier
     */
    void tuifei(Long userId, Long logId, String remark,String feiyongType,Integer multiplier);

    /**
     * 按乘数扣费multiplier
     * @param userId
     * @param logId
     * @param remark
     * @param feiyongType
     * @param multiplier
     * @return
     */
    boolean koufei(Long userId, Long logId, String remark,String feiyongType, Integer multiplier);

    boolean checkYueMultiplier(Long aLong, long id, String 萤火虫生成数字人视频扣费校验, String redisKey, int multiplier);
    void updateRemainingTimes(Long userId, Double ddQuantity, FlowRecordPO flowRecordPO);



}
