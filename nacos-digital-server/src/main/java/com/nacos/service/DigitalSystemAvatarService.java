package com.nacos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.po.DigitalSystemAvatarPO;
import com.nacos.entity.vo.DigitalAvatarDefaultVO;
import com.nacos.entity.vo.DigitalSystemAvatarVO;
import com.nacos.result.Result;
import java.util.List;
import java.util.Map;

public interface DigitalSystemAvatarService extends IService<DigitalSystemAvatarPO> {
    
    /**
     * 获取系统数字人列表
     * @return 系统数字人列表
     */
    Result<List<DigitalSystemAvatarVO>> listSystemAvatars();
    
    /**
     * 添加系统数字人
     * @param avatarVO 系统数字人信息
     * @return 操作结果
     */
    Result<Boolean> addSystemAvatar(DigitalSystemAvatarVO avatarVO);
    
    /**
     * 更新系统数字人
     * @param avatarVO 系统数字人信息
     * @return 操作结果
     */
    Result<Boolean> updateSystemAvatar(DigitalSystemAvatarVO avatarVO);
    
    /**
     * 删除系统数字人
     * @param id 系统数字人ID
     * @return 操作结果
     */
    Result<Boolean> deleteSystemAvatar(Long id);
    
    /**
     * 更新系统数字人状态
     * @param id 系统数字人ID
     * @param status 状态
     * @return 操作结果
     */
    Result<Boolean> updateStatus(Long id, Integer status);
    
    /**
     * 根据组ID获取系统数字人列表
     * @param groupId 组ID
     * @return 系统数字人列表
     */
    Result<List<DigitalSystemAvatarVO>> listSystemAvatarsByGroupId(String groupId);

    /**
     * 获取默认数字人形象与音色
     * @return 默认数字人形象与音色
     */
    Result<DigitalAvatarDefaultVO> getDefaultAvatarAndVoice(String groupId);
} 