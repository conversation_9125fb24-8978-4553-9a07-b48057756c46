package com.nacos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.po.DigitalVoiceClassifyDimensionPO;
import com.nacos.entity.vo.VoiceClassifyDimensionVO;

import java.util.List;

/**
 * 音色分类维度服务接口
 */
public interface DigitalVoiceClassifyDimensionService extends IService<DigitalVoiceClassifyDimensionPO> {
    
    /**
     * 获取所有启用的维度列表
     * 
     * @return 启用状态的维度列表
     */
    List<VoiceClassifyDimensionVO> listEnabledDimensions();
    
    /**
     * 根据维度编码获取维度信息
     * 
     * @param dimensionCode 维度编码
     * @return 维度信息，如果不存在则返回null
     */
    VoiceClassifyDimensionVO getDimensionByCode(String dimensionCode);
    
    /**
     * 获取维度及其下的标签列表
     * 
     * @param dimensionCode 维度编码
     * @return 包含标签列表的维度信息
     */
    VoiceClassifyDimensionVO getDimensionWithTags(String dimensionCode);
    
    /**
     * 获取所有维度及其下的标签列表
     * 
     * @return 包含标签列表的维度信息列表
     */
    List<VoiceClassifyDimensionVO> listDimensionsWithTags();
    
    /**
     * 创建新维度
     * 
     * @param dimensionCode 维度编码
     * @param dimensionName 维度名称
     * @param sortOrder 排序号
     * @return 创建的维度信息
     */
    VoiceClassifyDimensionVO createDimension(String dimensionCode, String dimensionName, Integer sortOrder);
    
    /**
     * 更新维度信息
     * 
     * @param dimensionCode 维度编码
     * @param dimensionName 维度名称
     * @param sortOrder 排序号
     * @param status 状态
     * @return 更新后的维度信息
     */
    VoiceClassifyDimensionVO updateDimension(String dimensionCode, String dimensionName, Integer sortOrder, Integer status);
    
    /**
     * 删除维度（逻辑删除）
     * 
     * @param dimensionCode 维度编码
     * @return 是否删除成功
     */
    boolean deleteDimension(String dimensionCode);
    
    /**
     * 检查维度编码是否存在
     * 
     * @param dimensionCode 维度编码
     * @return 是否存在
     */
    boolean existsByCode(String dimensionCode);
    
    /**
     * 初始化基础维度数据
     */
    void initializeBaseDimensions();
}
