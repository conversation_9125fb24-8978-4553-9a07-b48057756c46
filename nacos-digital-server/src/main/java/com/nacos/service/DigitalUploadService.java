package com.nacos.service;

import com.nacos.entity.dto.DigitalVoiceStyleUploadDTO;
import com.nacos.result.Result;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface DigitalUploadService {
    
    /**
     * 批量上传系统音色音频
     *
     * @param uploadDTOList 音色音频上传列表
     * @param userId 用户ID
     * @return 上传结果
     */
    Result<Void> batchUploadVoiceStyles(List<DigitalVoiceStyleUploadDTO> uploadDTOList, String userId);

    /**
     * 上传视频
     *
     * @param file 视频文件
     * @param userId 用户ID
     * @param groupId 组ID
     * @param isAuth 是否认证
     * @return 上传结果
     */
    Result<String> uploadVideo(MultipartFile file, String userId, String groupId, Integer isAuth);
    
    /**
     * 上传音频文件
     * @param file 音频文件
     * @param userId 用户ID
     * @return 文件访问路径
     */
    Result<String> uploadAudio(MultipartFile file, String userId);
    
    /**
     * 上传形象视频
     * @param file 形象视频文件
     * @param userId 用户ID
     * @return 文件访问路径
     */
    Result<String> uploadDigitalVideo(MultipartFile file, String userId);

}