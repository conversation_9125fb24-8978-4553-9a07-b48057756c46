package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.entity.po.*;
import com.nacos.entity.vo.*;
import com.nacos.mapper.*;
import com.nacos.service.DigitalAvatarInstanceService;
import com.nacos.result.Result;
import com.nacos.config.SyncAudioConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数字人分身实例服务实现类
 */
@Slf4j
@Service
public class DigitalAvatarInstanceServiceImpl implements DigitalAvatarInstanceService {

    @Autowired
    private DigitalUserAvatarMapper digitalUserAvatarMapper;
    
    @Autowired
    private DigitalSystemAvatarMapper digitalSystemAvatarMapper;
    
    @Autowired
    private DigitalAvatarCategoryMapper digitalAvatarCategoryMapper;
    
    @Autowired
    private DigitalGroupCategoryRelationMapper groupCategoryRelationMapper;
    
    @Autowired
    private DigitalSystemGroupMapper digitalSystemGroupMapper;
    
    @Autowired
    private DigitalUserGroupMapper digitalUserGroupMapper;
    
    @Autowired
    private DigitalVoiceUserCloneMapper digitalVoiceUserCloneMapper;
    
    @Autowired
    private DigitalVoiceStylesMapper digitalVoiceStylesMapper;

    @Autowired
    private SyncAudioConfig syncAudioConfig;

    // 注意：用户组列表功能已存在于 DigitalUserGroupService.listUserGroups(userId)

    /**
     * 根据组ID获取该组下的数字人分身列表
     *
     * @param groupId 组ID，可以是用户组或系统组
     * @param userId 用户ID，用于权限验证和所有者判断
     * @return 包含分身列表的结果对象
     */
    @Override
    public Result<DigitalAvatarInstanceListVO> listInstancesByGroup(String groupId, String userId) {
        try {
            log.info("获取组下的分身列表 - groupId: {}, userId: {}", groupId, userId);

            // 1. 验证组的存在性和权限
            Integer groupType = validateGroupAccess(groupId, userId);
            if (groupType == null) {
                return Result.ERROR("组不存在或无权限访问");
            }

            // 2. 构建返回结果
            DigitalAvatarInstanceListVO result = new DigitalAvatarInstanceListVO();
            result.setGroupId(groupId);
            result.setGroupType(groupType);

            // 3. 设置组名称
            setGroupName(result, groupId, groupType);

            // 4. 获取该组下的分身列表
            List<DigitalAvatarInstanceListVO.AvatarInstanceVO> instances =
                getInstancesByGroupId(groupId, groupType, userId);

            result.setAvatarInstances(instances);

            log.info("成功获取组下的分身列表，分身数量: {}", instances.size());
            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("获取组下的分身列表异常 - groupId: {}", groupId, e);
            return Result.ERROR("获取分身列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取数字人分身的详细信息
     *
     * @param avatarId 数字人分身ID
     * @param userId 用户ID，用于权限验证和所有者判断
     * @return 包含分身详细信息的结果对象，包括基本信息、组信息、分类信息、音色信息等
     */
    @Override
    public Result<DigitalAvatarInstanceDetailVO> getAvatarInstanceDetail(String avatarId, String userId) {
        try {
            log.info("获取数字人分身详情 - avatarId: {}, userId: {}", avatarId, userId);
            
            // 1. 先查询用户数字人
            DigitalUserAvatarPO userAvatar = digitalUserAvatarMapper.selectOne(
                new LambdaQueryWrapper<DigitalUserAvatarPO>()
                    .eq(DigitalUserAvatarPO::getAvatarId, avatarId)
                    .eq(DigitalUserAvatarPO::getIsDeleted, 0)
            );
            
            DigitalAvatarInstanceDetailVO result = new DigitalAvatarInstanceDetailVO();
            
            if (userAvatar != null) {
                // 用户数字人
                buildUserAvatarDetail(result, userAvatar, userId);
            } else {
                // 查询系统数字人
                DigitalSystemAvatarPO systemAvatar = digitalSystemAvatarMapper.selectOne(
                    new LambdaQueryWrapper<DigitalSystemAvatarPO>()
                        .eq(DigitalSystemAvatarPO::getAvatarId, avatarId)
                        .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
                );
                
                if (systemAvatar != null) {
                    buildSystemAvatarDetail(result, systemAvatar, userId);
                } else {
                    return Result.ERROR("数字人不存在或已删除");
                }
            }
            
            log.info("成功获取数字人分身详情 - avatarId: {}", avatarId);
            return Result.SUCCESS(result);
            
        } catch (Exception e) {
            log.error("获取数字人分身详情异常 - avatarId: {}", avatarId, e);
            return Result.ERROR("获取数字人分身详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取数字人分类统计信息
     *
     * @param userId 用户ID
     * @return 包含我的数字人和公共数字人分类统计的结果对象
     */
    @Override
    public Result<DigitalAvatarCategoryStatsVO> getCategoryStats(String userId) {
        try {
            log.info("获取数字人分类统计 - userId: {}", userId);
            
            DigitalAvatarCategoryStatsVO result = new DigitalAvatarCategoryStatsVO();
            
            // 1. 获取我的数字人统计
            DigitalAvatarCategoryStatsVO.CategoryStatsVO myAvatarsStats = getMyAvatarsStats(userId);
            result.setMyAvatars(myAvatarsStats);
            
            // 2. 获取公共数字人统计
            DigitalAvatarCategoryStatsVO.CategoryStatsVO publicAvatarsStats = getPublicAvatarsStats();
            result.setPublicAvatars(publicAvatarsStats);
            
            log.info("成功获取数字人分类统计 - userId: {}", userId);
            return Result.SUCCESS(result);
            
        } catch (Exception e) {
            log.error("获取数字人分类统计异常 - userId: {}", userId, e);
            return Result.ERROR("获取数字人分类统计失败: " + e.getMessage());
        }
    }
    
    // 私有方法实现...

    /**
     * 根据分类代码获取分类列表
     *
     * @param categoryCode 分类代码，如果为空则获取所有分类
     * @return 分类列表，按排序字段升序排列
     */
    private List<DigitalAvatarCategoryPO> getCategories(String categoryCode) {
        LambdaQueryWrapper<DigitalAvatarCategoryPO> wrapper = new LambdaQueryWrapper<DigitalAvatarCategoryPO>()
            .eq(DigitalAvatarCategoryPO::getStatus, 1)
            .orderByAsc(DigitalAvatarCategoryPO::getSort);

        if (StringUtils.hasText(categoryCode)) {
            wrapper.eq(DigitalAvatarCategoryPO::getCategoryCode, categoryCode);
        }

        return digitalAvatarCategoryMapper.selectList(wrapper);
    }

    /**
     * 根据分类代码获取该分类下的数字人分身实例列表
     *
     * @param categoryCode 分类代码
     * @param groupType 组类型（1-用户组，2-系统组，null-所有类型）
     * @param userId 用户ID
     * @return 分身实例列表
     */
    private List<DigitalAvatarInstanceListVO.AvatarInstanceVO> getAvatarInstancesByCategory(
            String categoryCode, Integer groupType, String userId) {

        List<DigitalAvatarInstanceListVO.AvatarInstanceVO> result = new ArrayList<>();

        // 1. 获取该分类关联的组ID列表
        Long categoryId = getCategoryIdByCode(categoryCode);
        if (categoryId == null) {
            return result;
        }
        List<String> groupIds = getGroupIdsByCategory(categoryId, groupType);

        if (groupIds.isEmpty()) {
            return result;
        }

        // 2. 根据groupType查询对应的数字人
        if (groupType == null || groupType == 1) {
            // 查询用户数字人
            List<DigitalUserAvatarPO> userAvatars = digitalUserAvatarMapper.selectList(
                new LambdaQueryWrapper<DigitalUserAvatarPO>()
                    .in(DigitalUserAvatarPO::getGroupId, groupIds)
                    .eq(DigitalUserAvatarPO::getUserId, userId)
                    .eq(DigitalUserAvatarPO::getStatus, 1)
                    .eq(DigitalUserAvatarPO::getIsDeleted, 0)
            );

            for (DigitalUserAvatarPO avatar : userAvatars) {
                result.add(buildAvatarInstanceVO(avatar, true, userId));
            }
        }

        if (groupType == null || groupType == 2) {
            // 查询系统数字人
            List<DigitalSystemAvatarPO> systemAvatars = digitalSystemAvatarMapper.selectList(
                new LambdaQueryWrapper<DigitalSystemAvatarPO>()
                    .in(DigitalSystemAvatarPO::getGroupId, groupIds)
                    .eq(DigitalSystemAvatarPO::getStatus, 1)
                    .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
            );

            for (DigitalSystemAvatarPO avatar : systemAvatars) {
                result.add(buildAvatarInstanceVO(avatar, false, userId));
            }
        }

        return result;
    }

    /**
     * 根据分类ID获取关联的组ID列表
     *
     * @param categoryId 分类ID
     * @param groupType 组类型（1-用户组，2-系统组，null-所有类型）
     * @return 组ID列表
     */
    private List<String> getGroupIdsByCategory(Long categoryId, Integer groupType) {
        // 根据分类ID获取关联的组ID
        List<DigitalGroupCategoryRelationPO> relations = groupCategoryRelationMapper.selectList(
            new LambdaQueryWrapper<DigitalGroupCategoryRelationPO>()
                .eq(DigitalGroupCategoryRelationPO::getCategoryId, categoryId)
                .eq(groupType != null, DigitalGroupCategoryRelationPO::getGroupType, groupType)
        );

        return relations.stream()
            .map(DigitalGroupCategoryRelationPO::getGroupId)
            .collect(Collectors.toList());
    }

    /**
     * 根据分类代码获取分类ID
     *
     * @param categoryCode 分类代码
     * @return 分类ID，如果不存在则返回null
     */
    private Long getCategoryIdByCode(String categoryCode) {
        DigitalAvatarCategoryPO category = digitalAvatarCategoryMapper.selectOne(
            new LambdaQueryWrapper<DigitalAvatarCategoryPO>()
                .eq(DigitalAvatarCategoryPO::getCategoryCode, categoryCode)
        );
        return category != null ? category.getId() : null;
    }

    /**
     * 构建用户数字人分身实例VO对象
     *
     * @param userAvatar 用户数字人PO对象
     * @param isOwner 是否为所有者
     * @param userId 用户ID
     * @return 分身实例VO对象
     */
    private DigitalAvatarInstanceListVO.AvatarInstanceVO buildAvatarInstanceVO(
            DigitalUserAvatarPO userAvatar, boolean isOwner, String userId) {

        DigitalAvatarInstanceListVO.AvatarInstanceVO vo = new DigitalAvatarInstanceListVO.AvatarInstanceVO();
        vo.setAvatarId(userAvatar.getAvatarId());
        vo.setAvatarName(userAvatar.getAvatarName());
        vo.setGroupId(userAvatar.getGroupId());
        vo.setCoverUrl(userAvatar.getCoverUrl());
        vo.setAvatarVideoUrl(userAvatar.getAvatarVideoUrl());
        vo.setAvatarType(userAvatar.getAvatarType());
        vo.setStatus(userAvatar.getStatus());
        vo.setGroupType(userAvatar.getGroupType());
        vo.setIsOwner(isOwner);
        vo.setDuration(userAvatar.getDuration());
        vo.setFileSize(userAvatar.getFileSize());

        // 获取组名称
        if (StringUtils.hasText(userAvatar.getGroupId())) {
            DigitalUserGroupPO group = digitalUserGroupMapper.selectOne(
                new LambdaQueryWrapper<DigitalUserGroupPO>()
                    .eq(DigitalUserGroupPO::getGroupId, userAvatar.getGroupId())
            );
            vo.setGroupName(group != null ? group.getGroupName() : "");
        }

        // 检查是否有关联音色
        checkVoiceInfo(vo, userAvatar.getAvatarId());

        return vo;
    }

    /**
     * 构建系统数字人分身实例VO对象
     *
     * @param systemAvatar 系统数字人PO对象
     * @param isOwner 是否为所有者
     * @param userId 用户ID
     * @return 分身实例VO对象
     */
    private DigitalAvatarInstanceListVO.AvatarInstanceVO buildAvatarInstanceVO(
            DigitalSystemAvatarPO systemAvatar, boolean isOwner, String userId) {

        DigitalAvatarInstanceListVO.AvatarInstanceVO vo = new DigitalAvatarInstanceListVO.AvatarInstanceVO();
        vo.setAvatarId(systemAvatar.getAvatarId());
        vo.setAvatarName(systemAvatar.getAvatarName());
        vo.setGroupId(systemAvatar.getGroupId());
        vo.setCoverUrl(systemAvatar.getCoverUrl());
        vo.setAvatarVideoUrl(systemAvatar.getAvatarVideoUrl());
        vo.setAvatarType(systemAvatar.getAvatarType());
        vo.setStatus(systemAvatar.getStatus());
        vo.setGroupType(systemAvatar.getGroupType());
        vo.setIsOwner(isOwner);
        vo.setDuration(systemAvatar.getDuration());
        vo.setFileSize(systemAvatar.getFileSize());

        // 获取组名称
        if (StringUtils.hasText(systemAvatar.getGroupId())) {
            DigitalSystemGroupPO group = digitalSystemGroupMapper.selectOne(
                new LambdaQueryWrapper<DigitalSystemGroupPO>()
                    .eq(DigitalSystemGroupPO::getGroupId, systemAvatar.getGroupId())
            );
            vo.setGroupName(group != null ? group.getGroupName() : "");
        }

        // 检查是否有关联音色
        checkVoiceInfo(vo, systemAvatar.getAvatarId());

        return vo;
    }

    /**
     * 检查数字人分身的音色信息
     *
     * @param vo 分身实例VO对象
     * @param avatarId 数字人分身ID
     */
    private void checkVoiceInfo(DigitalAvatarInstanceListVO.AvatarInstanceVO vo, String avatarId) {
        // 查询关联的音色数量
        Long voiceCount = digitalVoiceUserCloneMapper.selectCount(
            new LambdaQueryWrapper<DigitalVoiceUserClonePO>()
                .eq(DigitalVoiceUserClonePO::getAvatarId, avatarId)
                .eq(DigitalVoiceUserClonePO::getStatus, 1)
                .eq(DigitalVoiceUserClonePO::getIsDeleted, 0)
        );

        vo.setHasVoice(voiceCount > 0);
        vo.setVoiceCount(voiceCount.intValue());
    }

    /**
     * 构建用户数字人分身详情信息
     *
     * @param result 详情VO对象
     * @param userAvatar 用户数字人PO对象
     * @param userId 用户ID
     */
    private void buildUserAvatarDetail(DigitalAvatarInstanceDetailVO result,
            DigitalUserAvatarPO userAvatar, String userId) {

        BeanUtils.copyProperties(userAvatar, result);
        result.setIsOwner(userId.equals(userAvatar.getUserId()));

        // 获取组信息和分类信息
        setGroupAndCategoryInfo(result, userAvatar.getGroupId(), 1);

        // 获取音色信息（用户数字人优先查找克隆音色）
        setVoiceInfo(result, userAvatar.getAvatarId(), 1);
    }

    /**
     * 构建系统数字人分身详情信息
     *
     * @param result 详情VO对象
     * @param systemAvatar 系统数字人PO对象
     * @param userId 用户ID
     */
    private void buildSystemAvatarDetail(DigitalAvatarInstanceDetailVO result,
            DigitalSystemAvatarPO systemAvatar, String userId) {

        BeanUtils.copyProperties(systemAvatar, result);
        result.setIsOwner(false); // 系统数字人不属于任何用户

        // 获取组信息和分类信息
        setGroupAndCategoryInfo(result, systemAvatar.getGroupId(), 2);

        // 获取音色信息（系统数字人使用默认系统音色）
        setVoiceInfo(result, systemAvatar.getAvatarId(), 2);
    }

    /**
     * 设置组信息和分类信息
     *
     * @param result 详情VO对象
     * @param groupId 组ID
     * @param groupType 组类型（1-用户组，2-系统组）
     */
    private void setGroupAndCategoryInfo(DigitalAvatarInstanceDetailVO result, String groupId, Integer groupType) {
        if (!StringUtils.hasText(groupId)) {
            return;
        }

        if (groupType == 1) {
            // 用户组
            DigitalUserGroupPO group = digitalUserGroupMapper.selectOne(
                new LambdaQueryWrapper<DigitalUserGroupPO>()
                    .eq(DigitalUserGroupPO::getGroupId, groupId)
            );
            if (group != null) {
                result.setGroupName(group.getGroupName());
            }
        } else if (groupType == 2) {
            // 系统组
            DigitalSystemGroupPO group = digitalSystemGroupMapper.selectOne(
                new LambdaQueryWrapper<DigitalSystemGroupPO>()
                    .eq(DigitalSystemGroupPO::getGroupId, groupId)
            );
            if (group != null) {
                result.setGroupName(group.getGroupName());
            }

            // 获取分类信息（只有系统组才有分类）
            DigitalGroupCategoryRelationPO relation = groupCategoryRelationMapper.selectOne(
                new LambdaQueryWrapper<DigitalGroupCategoryRelationPO>()
                    .eq(DigitalGroupCategoryRelationPO::getGroupId, groupId)
                    .eq(DigitalGroupCategoryRelationPO::getGroupType, groupType)
            );

            if (relation != null) {
                DigitalAvatarCategoryPO category = digitalAvatarCategoryMapper.selectById(relation.getCategoryId());
                if (category != null) {
                    result.setAvatarCategoryCode(category.getCategoryCode());
                    result.setAvatarCategoryName(category.getCategoryName());
                }
            }
        }
    }

    /**
     * 设置数字人分身的音色信息
     *
     * @param result 详情VO对象
     * @param avatarId 数字人分身ID
     * @param avatarType 数字人类型（1-用户数字人，2-系统数字人）
     */
    private void setVoiceInfo(DigitalAvatarInstanceDetailVO result, String avatarId, Integer avatarType) {
        if (avatarType == 1) {
            // 用户数字人：优先查找克隆音色，没有则使用默认系统音色
            setUserVoiceInfo(result, avatarId);
        } else if (avatarType == 2) {
            // 系统数字人：直接使用默认系统音色
            setDefaultVoiceInfo(result);
        }
    }

    /**
     * 设置用户数字人的音色信息
     *
     * @param result 详情VO对象
     * @param avatarId 数字人分身ID
     */
    private void setUserVoiceInfo(DigitalAvatarInstanceDetailVO result, String avatarId) {
        // 查询用户克隆音色
        DigitalVoiceUserClonePO userVoice = digitalVoiceUserCloneMapper.selectOne(
            new LambdaQueryWrapper<DigitalVoiceUserClonePO>()
                .eq(DigitalVoiceUserClonePO::getAvatarId, avatarId)
                .eq(DigitalVoiceUserClonePO::getStatus, 1)
                .eq(DigitalVoiceUserClonePO::getIsDeleted, 0)
                .last("LIMIT 1")
        );

        if (userVoice != null) {
            // 使用用户克隆音色
            DigitalAvatarInstanceDetailVO.VoiceInfoVO voiceInfo = new DigitalAvatarInstanceDetailVO.VoiceInfoVO();
            voiceInfo.setVoiceId(userVoice.getVoiceId());
            voiceInfo.setVoiceName(userVoice.getVoiceName());
            voiceInfo.setProvider(userVoice.getProvider());
            voiceInfo.setDemoAudio(userVoice.getDemoAudio());
            voiceInfo.setVoiceType(userVoice.getVoiceType());
            result.setVoiceInfo(voiceInfo);
        } else {
            // 没有克隆音色，使用默认系统音色
            setDefaultVoiceInfo(result);
        }
    }

    /**
     * 设置默认音色信息
     *
     * @param result 详情VO对象
     */
    private void setDefaultVoiceInfo(DigitalAvatarInstanceDetailVO result) {
        // 从配置中获取默认音色ID
        String defaultVoiceId = syncAudioConfig.getDefaultVoiceId();

        DigitalVoiceStylesPO defaultVoice = digitalVoiceStylesMapper.selectOne(
            new LambdaQueryWrapper<DigitalVoiceStylesPO>()
                .eq(DigitalVoiceStylesPO::getVoiceId, defaultVoiceId)
        );

        if (defaultVoice != null) {
            DigitalAvatarInstanceDetailVO.VoiceInfoVO voiceInfo = new DigitalAvatarInstanceDetailVO.VoiceInfoVO();
            voiceInfo.setVoiceId(defaultVoice.getVoiceId());
            voiceInfo.setVoiceName(defaultVoice.getVoiceName());
            voiceInfo.setProvider(defaultVoice.getProvider());
            voiceInfo.setDemoAudio(defaultVoice.getDemoAudio());
            voiceInfo.setLanguage(defaultVoice.getLanguage());
            voiceInfo.setGender(defaultVoice.getGender());
            voiceInfo.setDescription(defaultVoice.getDescription());
            voiceInfo.setVoiceType(1); // 系统音色
            result.setVoiceInfo(voiceInfo);
        }
    }



    /**
     * 获取我的数字人分类统计信息
     *
     * @param userId 用户ID
     * @return 我的数字人分类统计VO对象
     */
    private DigitalAvatarCategoryStatsVO.CategoryStatsVO getMyAvatarsStats(String userId) {
        DigitalAvatarCategoryStatsVO.CategoryStatsVO stats = new DigitalAvatarCategoryStatsVO.CategoryStatsVO();

        // 获取用户数字人总数
        Long totalCount = digitalUserAvatarMapper.selectCount(
            new LambdaQueryWrapper<DigitalUserAvatarPO>()
                .eq(DigitalUserAvatarPO::getUserId, userId)
                .eq(DigitalUserAvatarPO::getStatus, 1)
                .eq(DigitalUserAvatarPO::getIsDeleted, 0)
        );
        stats.setTotalInstanceCount(totalCount.intValue());

        // 获取分类统计
        List<DigitalAvatarCategoryStatsVO.CategoryWithCountVO> categories = getCategoryStatsForUser(userId);
        stats.setCategories(categories);

        return stats;
    }

    /**
     * 获取公共数字人分类统计信息
     *
     * @return 公共数字人分类统计VO对象
     */
    private DigitalAvatarCategoryStatsVO.CategoryStatsVO getPublicAvatarsStats() {
        DigitalAvatarCategoryStatsVO.CategoryStatsVO stats = new DigitalAvatarCategoryStatsVO.CategoryStatsVO();

        // 获取系统数字人总数
        Long totalCount = digitalSystemAvatarMapper.selectCount(
            new LambdaQueryWrapper<DigitalSystemAvatarPO>()
                .eq(DigitalSystemAvatarPO::getStatus, 1)
                .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
        );
        stats.setTotalInstanceCount(totalCount.intValue());

        // 获取分类统计
        List<DigitalAvatarCategoryStatsVO.CategoryWithCountVO> categories = getCategoryStatsForSystem();
        stats.setCategories(categories);

        return stats;
    }

    /**
     * 获取用户数字人的分类统计信息
     *
     * @param userId 用户ID
     * @return 分类统计列表
     */
    private List<DigitalAvatarCategoryStatsVO.CategoryWithCountVO> getCategoryStatsForUser(String userId) {
        // 获取所有分类
        List<DigitalAvatarCategoryPO> allCategories = digitalAvatarCategoryMapper.selectList(
            new LambdaQueryWrapper<DigitalAvatarCategoryPO>()
                .eq(DigitalAvatarCategoryPO::getStatus, 1)
                .orderByAsc(DigitalAvatarCategoryPO::getSort)
        );

        return allCategories.stream().map(category -> {
            DigitalAvatarCategoryStatsVO.CategoryWithCountVO categoryVO =
                new DigitalAvatarCategoryStatsVO.CategoryWithCountVO();
            BeanUtils.copyProperties(category, categoryVO);

            // 统计该分类下用户数字人数量
            int avatarCount = countUserAvatarsByCategory(category.getCategoryCode(), userId);
            categoryVO.setInstanceCount(avatarCount);

            return categoryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取系统数字人的分类统计信息
     *
     * @return 分类统计列表
     */
    private List<DigitalAvatarCategoryStatsVO.CategoryWithCountVO> getCategoryStatsForSystem() {
        // 获取所有分类
        List<DigitalAvatarCategoryPO> allCategories = digitalAvatarCategoryMapper.selectList(
            new LambdaQueryWrapper<DigitalAvatarCategoryPO>()
                .eq(DigitalAvatarCategoryPO::getStatus, 1)
                .orderByAsc(DigitalAvatarCategoryPO::getSort)
        );

        return allCategories.stream().map(category -> {
            DigitalAvatarCategoryStatsVO.CategoryWithCountVO categoryVO =
                new DigitalAvatarCategoryStatsVO.CategoryWithCountVO();
            BeanUtils.copyProperties(category, categoryVO);

            // 统计该分类下系统数字人数量
            int avatarCount = countSystemAvatarsByCategory(category.getCategoryCode());
            categoryVO.setInstanceCount(avatarCount);

            return categoryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 统计指定分类下用户数字人的数量
     *
     * @param categoryCode 分类代码
     * @param userId 用户ID
     * @return 数字人数量
     */
    private int countUserAvatarsByCategory(String categoryCode, String userId) {
        Long categoryId = getCategoryIdByCode(categoryCode);
        if (categoryId == null) {
            return 0;
        }
        List<String> groupIds = getGroupIdsByCategory(categoryId, 1);
        if (groupIds.isEmpty()) {
            return 0;
        }

        Long count = digitalUserAvatarMapper.selectCount(
            new LambdaQueryWrapper<DigitalUserAvatarPO>()
                .in(DigitalUserAvatarPO::getGroupId, groupIds)
                .eq(DigitalUserAvatarPO::getUserId, userId)
                .eq(DigitalUserAvatarPO::getStatus, 1)
                .eq(DigitalUserAvatarPO::getIsDeleted, 0)
        );

        return count.intValue();
    }

    /**
     * 统计指定分类下系统数字人的数量
     *
     * @param categoryCode 分类代码
     * @return 数字人数量
     */
    private int countSystemAvatarsByCategory(String categoryCode) {
        Long categoryId = getCategoryIdByCode(categoryCode);
        if (categoryId == null) {
            return 0;
        }
        List<String> groupIds = getGroupIdsByCategory(categoryId, 2);
        if (groupIds.isEmpty()) {
            return 0;
        }

        Long count = digitalSystemAvatarMapper.selectCount(
            new LambdaQueryWrapper<DigitalSystemAvatarPO>()
                .in(DigitalSystemAvatarPO::getGroupId, groupIds)
                .eq(DigitalSystemAvatarPO::getStatus, 1)
                .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
        );

        return count.intValue();
    }

    // 新增的辅助方法

    /**
     * 构建数字人组列表VO对象
     *
     * @param group 用户组PO对象
     * @param isOwner 是否为所有者
     * @param userId 用户ID
     * @return 组列表VO对象
     */
    private DigitalAvatarGroupListVO buildGroupListVO(DigitalUserGroupPO group, boolean isOwner, String userId) {
        DigitalAvatarGroupListVO vo = new DigitalAvatarGroupListVO();
        BeanUtils.copyProperties(group, vo);
        vo.setGroupType(1); // 用户组
        vo.setIsOwner(isOwner);

        // 统计该组下的分身数量
        Long instanceCount = digitalUserAvatarMapper.selectCount(
            new LambdaQueryWrapper<DigitalUserAvatarPO>()
                .eq(DigitalUserAvatarPO::getGroupId, group.getGroupId())
                .eq(DigitalUserAvatarPO::getStatus, 1)
                .eq(DigitalUserAvatarPO::getIsDeleted, 0)
        );
        vo.setInstanceCount(instanceCount.intValue());

        // 统计有音色的分身数量
        Long voiceInstanceCount = digitalVoiceUserCloneMapper.selectCount(
            new LambdaQueryWrapper<DigitalVoiceUserClonePO>()
                .exists("SELECT 1 FROM digital_user_avatar WHERE avatar_id = digital_voice_user_clone.avatar_id " +
                       "AND group_id = '" + group.getGroupId() + "' AND status = 1 AND is_deleted = 0")
                .eq(DigitalVoiceUserClonePO::getStatus, 1)
                .eq(DigitalVoiceUserClonePO::getIsDeleted, 0)
        );
        vo.setVoiceInstanceCount(voiceInstanceCount.intValue());

        return vo;
    }

    /**
     * 验证组访问权限
     *
     * @param groupId 组ID
     * @param userId 用户ID
     * @return 组类型（1-用户组，2-系统组），如果无权限则返回null
     */
    private Integer validateGroupAccess(String groupId, String userId) {
        // 先检查是否为用户组
        DigitalUserGroupPO userGroup = digitalUserGroupMapper.selectOne(
            new LambdaQueryWrapper<DigitalUserGroupPO>()
                .eq(DigitalUserGroupPO::getGroupId, groupId)
                .eq(DigitalUserGroupPO::getUserId, userId)
                .eq(DigitalUserGroupPO::getStatus, 1)
                .eq(DigitalUserGroupPO::getIsDeleted, 0)
        );

        if (userGroup != null) {
            return 1; // 用户组
        }

        // 检查是否为系统组（公共数字人）
        DigitalSystemGroupPO systemGroup = digitalSystemGroupMapper.selectOne(
            new LambdaQueryWrapper<DigitalSystemGroupPO>()
                .eq(DigitalSystemGroupPO::getGroupId, groupId)
                .eq(DigitalSystemGroupPO::getStatus, 1)
                .eq(DigitalSystemGroupPO::getIsDeleted, 0)
        );

        if (systemGroup != null) {
            return 2; // 系统组
        }

        return null; // 组不存在或无权限
    }

    /**
     * 设置组名称
     *
     * @param result 分身实例列表VO对象
     * @param groupId 组ID
     * @param groupType 组类型（1-用户组，2-系统组）
     */
    private void setGroupName(DigitalAvatarInstanceListVO result, String groupId, Integer groupType) {
        if (groupType == 1) {
            // 用户组
            DigitalUserGroupPO group = digitalUserGroupMapper.selectOne(
                new LambdaQueryWrapper<DigitalUserGroupPO>()
                    .eq(DigitalUserGroupPO::getGroupId, groupId)
            );
            if (group != null) {
                result.setGroupName(group.getGroupName());
            }
        } else {
            // 系统组
            DigitalSystemGroupPO group = digitalSystemGroupMapper.selectOne(
                new LambdaQueryWrapper<DigitalSystemGroupPO>()
                    .eq(DigitalSystemGroupPO::getGroupId, groupId)
            );
            if (group != null) {
                result.setGroupName(group.getGroupName());
            }
        }
    }

    /**
     * 根据组ID获取该组下的分身实例列表
     *
     * @param groupId 组ID
     * @param groupType 组类型（1-用户组，2-系统组）
     * @param userId 用户ID
     * @return 分身实例列表
     */
    private List<DigitalAvatarInstanceListVO.AvatarInstanceVO> getInstancesByGroupId(
            String groupId, Integer groupType, String userId) {

        List<DigitalAvatarInstanceListVO.AvatarInstanceVO> result = new ArrayList<>();

        if (groupType == 1) {
            // 用户组下的分身
            List<DigitalUserAvatarPO> userAvatars = digitalUserAvatarMapper.selectList(
                new LambdaQueryWrapper<DigitalUserAvatarPO>()
                    .eq(DigitalUserAvatarPO::getGroupId, groupId)
                    .eq(DigitalUserAvatarPO::getStatus, 1)
                    .eq(DigitalUserAvatarPO::getIsDeleted, 0)
            );
            // 添加分身信息
            for (DigitalUserAvatarPO avatar : userAvatars) {
                result.add(buildAvatarInstanceVO(avatar, true, userId));
            }
        } else {
            // 系统组下的分身
            List<DigitalSystemAvatarPO> systemAvatars = digitalSystemAvatarMapper.selectList(
                new LambdaQueryWrapper<DigitalSystemAvatarPO>()
                    .eq(DigitalSystemAvatarPO::getGroupId, groupId)
                    .eq(DigitalSystemAvatarPO::getStatus, 1)
                    .eq(DigitalSystemAvatarPO::getIsDeleted, 0)
            );
            // 添加分身信息
            for (DigitalSystemAvatarPO avatar : systemAvatars) {
                result.add(buildAvatarInstanceVO(avatar, false, userId));
            }
        }

        return result;
    }
}
