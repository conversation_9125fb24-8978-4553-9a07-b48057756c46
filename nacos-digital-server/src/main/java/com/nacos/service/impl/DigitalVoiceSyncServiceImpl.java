package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nacos.entity.po.DigitalVoiceSyncPO;
import com.nacos.entity.po.DigitalVoiceSystemPO;
import com.nacos.entity.vo.DigitalVoiceSyncVO;
import com.nacos.mapper.DigitalVoiceSyncMapper;
import com.nacos.mapper.DigitalVoiceSystemMapper;
import com.nacos.result.Result;
import com.nacos.service.DigitalVoiceSyncService;
import com.nacos.service.DigitalVoiceSystemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 音色同步服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalVoiceSyncServiceImpl extends ServiceImpl<DigitalVoiceSyncMapper, DigitalVoiceSyncPO> 
    implements DigitalVoiceSyncService {

    private final DigitalVoiceSystemMapper digitalVoiceSystemMapper;
    private final DigitalVoiceSystemService digitalVoiceSystemService;
    
    private final AtomicInteger voiceIdSequence = new AtomicInteger(1);

    @Override
    public Result<String> syncAllVoiceData() {
        String methodName = "syncAllVoiceData";
        log.info("[{}] 开始同步所有供应商音色数据", methodName);
        
        try {
            List<String> results = new ArrayList<>();
            
            // 同步各个供应商
            Result<String> minimaxResult = syncMinimaxVoiceData();
            results.add("Minimax: " + minimaxResult.getMessage());
            
            Result<String> microsoftResult = syncMicrosoftVoiceData();
            results.add("Microsoft: " + microsoftResult.getMessage());
            
            Result<String> elevenLabsResult = syncElevenLabsVoiceData();
            results.add("ElevenLabs: " + elevenLabsResult.getMessage());
            
            String message = String.join("; ", results);
            log.info("[{}] 所有供应商音色数据同步完成: {}", methodName, message);
            return Result.SUCCESS(message);
            
        } catch (Exception e) {
            log.error("[{}] 同步所有供应商音色数据失败", methodName, e);
            return Result.ERROR("同步失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> syncVoiceDataByProvider(String provider) {
        switch (provider.toUpperCase()) {
            case "MINIMAX":
                return syncMinimaxVoiceData();
            case "MICROSOFT":
                return syncMicrosoftVoiceData();
            case "ELEVENLABS":
                return syncElevenLabsVoiceData();
            default:
                return Result.ERROR("不支持的供应商: " + provider);
        }
    }

    @Override
    public Result<String> syncMinimaxVoiceData() {
        String methodName = "syncMinimaxVoiceData";
        log.info("[{}] 开始同步Minimax音色数据", methodName);

        try {
            // 调用Minimax API获取音色数据
            com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO voiceResponse =
                com.nacos.model.MiniMax.MiniMaxApiUtil.getVoiceId("all");

            if (voiceResponse == null) {
                log.warn("[{}] Minimax API返回为空", methodName);
                return Result.ERROR("Minimax API返回为空");
            }

            // 检查API响应状态
            if (voiceResponse.getBaseResp() != null && voiceResponse.getBaseResp().getStatusCode() != 0) {
                String errorMsg = String.format("Minimax API调用失败，状态码: %d, 消息: %s",
                    voiceResponse.getBaseResp().getStatusCode(),
                    voiceResponse.getBaseResp().getStatusMsg());
                log.error("[{}] {}", methodName, errorMsg);
                return Result.ERROR(errorMsg);
            }

            List<Object> allVoiceData = new ArrayList<>();

            // 处理系统音色
            if (voiceResponse.getSystemVoice() != null) {
                allVoiceData.addAll(voiceResponse.getSystemVoice());
                log.info("[{}] 获取到{}个Minimax系统音色", methodName, voiceResponse.getSystemVoice().size());
            }

            // 处理克隆音色
            if (voiceResponse.getVoiceCloning() != null) {
                allVoiceData.addAll(voiceResponse.getVoiceCloning());
                log.info("[{}] 获取到{}个Minimax克隆音色", methodName, voiceResponse.getVoiceCloning().size());
            }

            // 处理同步数据
            int syncCount = processSyncData("MINIMAX", allVoiceData);

            String message = String.format("Minimax音色同步完成，处理%d条记录", syncCount);
            log.info("[{}] {}", methodName, message);
            return Result.SUCCESS(message);

        } catch (Exception e) {
            log.error("[{}] 同步Minimax音色数据失败", methodName, e);
            return Result.ERROR("同步Minimax失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> syncMicrosoftVoiceData() {
        String methodName = "syncMicrosoftVoiceData";
        log.info("[{}] 开始同步Microsoft音色数据", methodName);

        try {
            // 获取Azure配置
            String[] azureConfig = getAzureConfig();
            String subscriptionKey = azureConfig[0];
            String region = azureConfig[1];

            if (subscriptionKey == null || region == null) {
                log.warn("[{}] Azure配置无效，跳过Microsoft音色同步", methodName);
                return Result.ERROR("Azure配置无效，无法同步Microsoft音色");
            }

            // 调用Microsoft API获取音色数据
            Result<String> voicesResult = com.nacos.model.AzureAudio.AzureAudioApiUtil.getVoicesList(subscriptionKey, region);

            if (!voicesResult.isSuccess() || voicesResult.getData() == null) {
                log.warn("[{}] Microsoft API返回失败：{}", methodName, voicesResult.getMessage());
                return Result.ERROR("Microsoft API调用失败: " + voicesResult.getMessage());
            }

            // 解析JSON响应为VoiceInfo对象列表
            List<Object> allVoiceData = parseMicrosoftVoicesJson(voicesResult.getData());

            if (allVoiceData == null || allVoiceData.isEmpty()) {
                log.warn("[{}] Microsoft API返回为空或无音色数据", methodName);
                return Result.SUCCESS("Microsoft音色同步完成，无新数据");
            }

            log.info("[{}] 获取到{}个Microsoft音色", methodName, allVoiceData.size());

            // 处理同步数据
            int syncCount = processSyncData("MICROSOFT", allVoiceData);

            String message = String.format("Microsoft音色同步完成，处理%d条记录", syncCount);
            log.info("[{}] {}", methodName, message);
            return Result.SUCCESS(message);

        } catch (Exception e) {
            log.error("[{}] 同步Microsoft音色数据失败", methodName, e);
            return Result.ERROR("同步Microsoft失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> syncElevenLabsVoiceData() {
        String methodName = "syncElevenLabsVoiceData";
        log.info("[{}] 开始同步ElevenLabs音色数据", methodName);

        try {
            // 调用ElevenLabs API获取音色数据
            List<com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO.Voice> voiceList =
                com.nacos.model.Elevenlabs.ElevenLabsApiUtil.getAllVoices();

            if (voiceList == null || voiceList.isEmpty()) {
                log.warn("[{}] ElevenLabs API返回为空或无音色数据", methodName);
                return Result.SUCCESS("ElevenLabs音色同步完成，无新数据");
            }

            log.info("[{}] 获取到{}个ElevenLabs音色", methodName, voiceList.size());

            // 转换为Object列表以适配processSyncData方法
            List<Object> allVoiceData = new ArrayList<>(voiceList);

            // 处理同步数据
            int syncCount = processSyncData("ELEVENLABS", allVoiceData);

            String message = String.format("ElevenLabs音色同步完成，处理%d条记录", syncCount);
            log.info("[{}] {}", methodName, message);
            return Result.SUCCESS(message);

        } catch (Exception e) {
            log.error("[{}] 同步ElevenLabs音色数据失败", methodName, e);
            return Result.ERROR("同步ElevenLabs失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> retryFailedSyncs() {
        String methodName = "retryFailedSyncs";
        log.info("[{}] 开始重试失败的同步记录", methodName);
        
        try {
            // 查询失败的同步记录
            List<DigitalVoiceSyncPO> failedRecords = this.list(
                new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .eq(DigitalVoiceSyncPO::getSyncStatus, 2) // 同步失败
                    .lt(DigitalVoiceSyncPO::getSyncRetryCount, 3) // 重试次数小于3
            );
            
            if (failedRecords.isEmpty()) {
                return Result.SUCCESS("没有需要重试的失败记录");
            }
            
            int retryCount = 0;
            for (DigitalVoiceSyncPO record : failedRecords) {
                try {
                    // 重试同步逻辑
                    retrySingleRecord(record);
                    retryCount++;
                } catch (Exception e) {
                    log.error("[{}] 重试同步记录失败，ID: {}", methodName, record.getId(), e);
                }
            }
            
            String message = String.format("重试完成，成功重试%d条记录", retryCount);
            log.info("[{}] {}", methodName, message);
            return Result.SUCCESS(message);
            
        } catch (Exception e) {
            log.error("[{}] 重试失败的同步记录失败", methodName, e);
            return Result.ERROR("重试失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> retryFailedSyncsByProvider(String provider) {
        String methodName = "retryFailedSyncsByProvider";
        log.info("[{}] 开始重试{}的失败同步记录", methodName, provider);
        
        try {
            List<DigitalVoiceSyncPO> failedRecords = this.list(
                new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .eq(DigitalVoiceSyncPO::getProvider, provider)
                    .eq(DigitalVoiceSyncPO::getSyncStatus, 2)
                    .lt(DigitalVoiceSyncPO::getSyncRetryCount, 3)
            );
            
            if (failedRecords.isEmpty()) {
                return Result.SUCCESS("没有需要重试的失败记录");
            }
            
            int retryCount = 0;
            for (DigitalVoiceSyncPO record : failedRecords) {
                try {
                    retrySingleRecord(record);
                    retryCount++;
                } catch (Exception e) {
                    log.error("[{}] 重试同步记录失败，ID: {}", methodName, record.getId(), e);
                }
            }
            
            String message = String.format("重试完成，成功重试%d条记录", retryCount);
            return Result.SUCCESS(message);
            
        } catch (Exception e) {
            log.error("[{}] 重试{}的失败同步记录失败", methodName, provider, e);
            return Result.ERROR("重试失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSyncStatusStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 总体统计
            long totalCount = this.count();
            long successCount = this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                .eq(DigitalVoiceSyncPO::getSyncStatus, 1));
            long failedCount = this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                .eq(DigitalVoiceSyncPO::getSyncStatus, 2));
            long publishedCount = this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                .eq(DigitalVoiceSyncPO::getIsPublished, 1));
            
            stats.put("totalCount", totalCount);
            stats.put("successCount", successCount);
            stats.put("failedCount", failedCount);
            stats.put("publishedCount", publishedCount);
            stats.put("successRate", totalCount > 0 ? (double) successCount / totalCount * 100 : 0);
            
            // 按供应商统计
            Map<String, Map<String, Object>> providerStats = new HashMap<>();
            List<String> providers = Arrays.asList("MINIMAX", "MICROSOFT", "ELEVENLABS");
            
            for (String provider : providers) {
                Map<String, Object> providerStat = new HashMap<>();
                long providerTotal = this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .eq(DigitalVoiceSyncPO::getProvider, provider));
                long providerSuccess = this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .eq(DigitalVoiceSyncPO::getProvider, provider)
                    .eq(DigitalVoiceSyncPO::getSyncStatus, 1));
                long providerFailed = this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .eq(DigitalVoiceSyncPO::getProvider, provider)
                    .eq(DigitalVoiceSyncPO::getSyncStatus, 2));
                
                providerStat.put("total", providerTotal);
                providerStat.put("success", providerSuccess);
                providerStat.put("failed", providerFailed);
                providerStat.put("successRate", providerTotal > 0 ? (double) providerSuccess / providerTotal * 100 : 0);
                
                providerStats.put(provider, providerStat);
            }
            
            stats.put("providerStats", providerStats);
            stats.put("lastUpdateTime", LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("获取同步状态统计失败", e);
            stats.put("error", "获取统计信息失败: " + e.getMessage());
        }
        
        return stats;
    }

    @Override
    public Map<String, Object> getSyncStatusStatisticsByProvider(String provider) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            long totalCount = this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                .eq(DigitalVoiceSyncPO::getProvider, provider));
            long successCount = this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                .eq(DigitalVoiceSyncPO::getProvider, provider)
                .eq(DigitalVoiceSyncPO::getSyncStatus, 1));
            long failedCount = this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                .eq(DigitalVoiceSyncPO::getProvider, provider)
                .eq(DigitalVoiceSyncPO::getSyncStatus, 2));
            long publishedCount = this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                .eq(DigitalVoiceSyncPO::getProvider, provider)
                .eq(DigitalVoiceSyncPO::getIsPublished, 1));
            
            stats.put("provider", provider);
            stats.put("totalCount", totalCount);
            stats.put("successCount", successCount);
            stats.put("failedCount", failedCount);
            stats.put("publishedCount", publishedCount);
            stats.put("successRate", totalCount > 0 ? (double) successCount / totalCount * 100 : 0);
            stats.put("publishRate", successCount > 0 ? (double) publishedCount / successCount * 100 : 0);
            
        } catch (Exception e) {
            log.error("获取{}供应商同步状态统计失败", provider, e);
            stats.put("error", "获取统计信息失败: " + e.getMessage());
        }
        
        return stats;
    }

    /**
     * 处理同步数据的通用方法
     */
    private int processSyncData(String provider, List<?> apiDataList) {
        String methodName = "processSyncData";
        log.info("[{}] 开始处理{}供应商的{}条音色数据", methodName, provider, apiDataList.size());

        int processedCount = 0;
        int newCount = 0;
        int updateCount = 0;
        int errorCount = 0;

        for (Object apiDataObj : apiDataList) {
            try {
                // 根据供应商类型转换API数据
                DigitalVoiceSyncPO syncRecord = convertApiDataToSyncPO(provider, apiDataObj);
                if (syncRecord == null) {
                    log.warn("[{}] 无法转换API数据，跳过处理：{}", methodName, apiDataObj);
                    continue;
                }

                // 检查是否已存在
                DigitalVoiceSyncPO existingRecord = this.getOne(
                    new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                        .eq(DigitalVoiceSyncPO::getThirdPartyVoiceId, syncRecord.getThirdPartyVoiceId())
                        .eq(DigitalVoiceSyncPO::getProvider, provider)
                );

                if (existingRecord == null) {
                    // 新增同步记录
                    this.save(syncRecord);
                    newCount++;
                    log.debug("[{}] 新增音色同步记录: {}", methodName, syncRecord.getThirdPartyVoiceId());
                } else {
                    // 更新现有记录
                    updateExistingSyncRecord(existingRecord, syncRecord);
                    this.updateById(existingRecord);
                    updateCount++;
                    log.debug("[{}] 更新音色同步记录: {}", methodName, syncRecord.getThirdPartyVoiceId());
                }

                processedCount++;

            } catch (Exception e) {
                errorCount++;
                log.error("[{}] 处理音色数据失败，provider: {}, data: {}", methodName, provider, apiDataObj, e);
                // 记录失败的同步记录
                recordFailedSync(provider, apiDataObj, e.getMessage());
            }
        }

        log.info("[{}] {}供应商数据处理完成，总计:{}，新增:{}，更新:{}，失败:{}",
                 methodName, provider, processedCount, newCount, updateCount, errorCount);
        return processedCount;
    }

    /**
     * 将API数据转换为DigitalVoiceSyncPO
     */
    private DigitalVoiceSyncPO convertApiDataToSyncPO(String provider, Object apiDataObj) {
        if (apiDataObj == null) {
            return null;
        }

        try {
            DigitalVoiceSyncPO syncPO = new DigitalVoiceSyncPO();
            syncPO.setProvider(provider);
            syncPO.setSyncStatus(1); // 默认同步成功
            syncPO.setSyncRetryCount(0);
            syncPO.setLastSyncTime(LocalDateTime.now());
            syncPO.setIsPublished(0); // 默认未发布

            switch (provider.toUpperCase()) {
                case "MINIMAX":
                    return convertMinimaxData(syncPO, apiDataObj);
                case "MICROSOFT":
                    return convertMicrosoftData(syncPO, apiDataObj);
                case "ELEVENLABS":
                    return convertElevenLabsData(syncPO, apiDataObj);
                default:
                    log.warn("不支持的供应商类型: {}", provider);
                    return null;
            }
        } catch (Exception e) {
            log.error("转换API数据失败，provider: {}, data: {}", provider, apiDataObj, e);
            return null;
        }
    }

    /**
     * 转换Minimax API数据
     */
    private DigitalVoiceSyncPO convertMinimaxData(DigitalVoiceSyncPO syncPO, Object apiDataObj) {
        // 处理Minimax系统音色数据
        if (apiDataObj instanceof com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.SystemVoice) {
            com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.SystemVoice systemVoice =
                (com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.SystemVoice) apiDataObj;

            syncPO.setThirdPartyVoiceId(systemVoice.getVoiceId());
            syncPO.setOriginalVoiceName(systemVoice.getVoiceName());
            syncPO.setOriginalDescription(systemVoice.getDescription());
            // Minimax系统音色默认为中文
            syncPO.setOriginalLanguage("zh-CN");
            // 其他字段根据实际API返回设置
            return syncPO;
        }

        // 处理Minimax克隆音色数据
        if (apiDataObj instanceof com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.VoiceCloning) {
            com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.VoiceCloning voiceCloning =
                (com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.VoiceCloning) apiDataObj;

            syncPO.setThirdPartyVoiceId(voiceCloning.getVoiceId());
            // 处理描述列表
            if (voiceCloning.getDescription() != null && !voiceCloning.getDescription().isEmpty()) {
                syncPO.setOriginalDescription(String.join(", ", voiceCloning.getDescription()));
            }
            syncPO.setOriginalLanguage("zh-CN");
            return syncPO;
        }

        log.warn("未知的Minimax数据类型: {}", apiDataObj.getClass().getName());
        return null;
    }

    /**
     * 转换Microsoft API数据
     */
    private DigitalVoiceSyncPO convertMicrosoftData(DigitalVoiceSyncPO syncPO, Object apiDataObj) {
        // 处理Microsoft VoiceInfo数据
        if (apiDataObj instanceof com.microsoft.cognitiveservices.speech.VoiceInfo) {
            com.microsoft.cognitiveservices.speech.VoiceInfo voiceInfo =
                (com.microsoft.cognitiveservices.speech.VoiceInfo) apiDataObj;

            syncPO.setThirdPartyVoiceId(voiceInfo.getName());
            syncPO.setOriginalVoiceName(voiceInfo.getName()); // 使用getName()作为显示名称
            syncPO.setOriginalLanguage(voiceInfo.getLocale());
            syncPO.setOriginalGender(voiceInfo.getGender().toString());

            // 构建描述信息
            StringBuilder description = new StringBuilder();
            description.append("语言: ").append(voiceInfo.getLocale());
            // 注意：VoiceInfo可能没有getStyleList方法，需要根据实际API调整
            syncPO.setOriginalDescription(description.toString());

            return syncPO;
        }

        // 处理自定义的Microsoft音色数据
        if (apiDataObj instanceof MicrosoftVoiceData) {
            MicrosoftVoiceData voiceData = (MicrosoftVoiceData) apiDataObj;

            syncPO.setThirdPartyVoiceId(voiceData.getName());
            syncPO.setOriginalVoiceName(voiceData.getDisplayName() != null ?
                voiceData.getDisplayName() : voiceData.getName());
            syncPO.setOriginalLanguage(voiceData.getLocale());
            syncPO.setOriginalGender(voiceData.getGender());
            syncPO.setOriginalDescription(voiceData.getDescription());

            // 构建扩展属性JSON
            if (voiceData.getVoiceType() != null || voiceData.getAttributes() != null) {
                com.alibaba.fastjson2.JSONObject others = new com.alibaba.fastjson2.JSONObject();
                if (voiceData.getVoiceType() != null) {
                    others.put("voiceType", voiceData.getVoiceType());
                }
                if (voiceData.getAttributes() != null) {
                    others.put("attributes", voiceData.getAttributes());
                }
                if (voiceData.getShortName() != null) {
                    others.put("shortName", voiceData.getShortName());
                }
                syncPO.setOthers(others.toJSONString());
            }

            return syncPO;
        }

        log.warn("未知的Microsoft数据类型: {}", apiDataObj.getClass().getName());
        return null;
    }

    /**
     * 转换ElevenLabs API数据
     */
    private DigitalVoiceSyncPO convertElevenLabsData(DigitalVoiceSyncPO syncPO, Object apiDataObj) {
        // 处理ElevenLabs Voice数据
        if (apiDataObj instanceof com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO.Voice) {
            com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO.Voice voice =
                (com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO.Voice) apiDataObj;

            syncPO.setThirdPartyVoiceId(voice.getVoiceId());
            syncPO.setOriginalVoiceName(voice.getName());
            syncPO.setOriginalDescription(voice.getDescription());
            syncPO.setOriginalDemoAudio(voice.getPreviewUrl());

            // 从labels中提取性别和语言信息
            if (voice.getLabels() != null) {
                syncPO.setOriginalGender(voice.getLabels().getGender());
                syncPO.setOriginalLanguage(voice.getLabels().getLanguage());
            }

            // 构建扩展属性JSON
            if (voice.getLabels() != null || voice.getCategory() != null) {
                com.alibaba.fastjson2.JSONObject others = new com.alibaba.fastjson2.JSONObject();
                if (voice.getLabels() != null) {
                    others.put("labels", voice.getLabels());
                }
                if (voice.getCategory() != null) {
                    others.put("category", voice.getCategory());
                }
                syncPO.setOthers(others.toJSONString());
            }

            return syncPO;
        }

        log.warn("未知的ElevenLabs数据类型: {}", apiDataObj.getClass().getName());
        return null;
    }

    /**
     * 更新现有同步记录
     */
    private void updateExistingSyncRecord(DigitalVoiceSyncPO existingRecord, DigitalVoiceSyncPO newRecord) {
        // 更新API返回的原始数据
        existingRecord.setOriginalVoiceName(newRecord.getOriginalVoiceName());
        existingRecord.setOriginalDescription(newRecord.getOriginalDescription());
        existingRecord.setOriginalLanguage(newRecord.getOriginalLanguage());
        existingRecord.setOriginalGender(newRecord.getOriginalGender());
        existingRecord.setOriginalDemoAudio(newRecord.getOriginalDemoAudio());
        existingRecord.setOthers(newRecord.getOthers());

        // 更新同步状态
        existingRecord.setSyncStatus(1); // 更新为同步成功
        existingRecord.setLastSyncTime(LocalDateTime.now());
        existingRecord.setSyncRetryCount(0); // 重置重试次数
        existingRecord.setSyncErrorMsg(null); // 清除错误信息
    }

    /**
     * 记录失败的同步
     */
    private void recordFailedSync(String provider, Object apiDataObj, String errorMsg) {
        try {
            DigitalVoiceSyncPO failedRecord = new DigitalVoiceSyncPO();
            failedRecord.setProvider(provider);
            failedRecord.setSyncStatus(2); // 同步失败
            failedRecord.setSyncErrorMsg(errorMsg);
            failedRecord.setSyncRetryCount(0);
            failedRecord.setLastSyncTime(LocalDateTime.now());
            failedRecord.setIsPublished(0);

            // 尝试从API数据中提取基本信息
            try {
                String voiceId = extractVoiceIdFromApiData(apiDataObj);
                if (voiceId != null) {
                    failedRecord.setThirdPartyVoiceId(voiceId);
                }
                String voiceName = extractVoiceNameFromApiData(apiDataObj);
                if (voiceName != null) {
                    failedRecord.setOriginalVoiceName(voiceName);
                }
            } catch (Exception e) {
                log.debug("无法从API数据中提取基本信息: {}", e.getMessage());
            }

            this.save(failedRecord);
            log.info("已记录失败的同步信息，provider: {}, error: {}", provider, errorMsg);
        } catch (Exception e) {
            log.error("记录失败同步信息时出错", e);
        }
    }

    /**
     * 从API数据中提取音色ID
     */
    private String extractVoiceIdFromApiData(Object apiDataObj) {
        if (apiDataObj instanceof com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.SystemVoice) {
            return ((com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.SystemVoice) apiDataObj).getVoiceId();
        } else if (apiDataObj instanceof com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.VoiceCloning) {
            return ((com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.VoiceCloning) apiDataObj).getVoiceId();
        } else if (apiDataObj instanceof com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO.Voice) {
            return ((com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO.Voice) apiDataObj).getVoiceId();
        } else if (apiDataObj instanceof com.microsoft.cognitiveservices.speech.VoiceInfo) {
            return ((com.microsoft.cognitiveservices.speech.VoiceInfo) apiDataObj).getName();
        } else if (apiDataObj instanceof MicrosoftVoiceData) {
            return ((MicrosoftVoiceData) apiDataObj).getName();
        }
        return null;
    }

    /**
     * 从API数据中提取音色名称
     */
    private String extractVoiceNameFromApiData(Object apiDataObj) {
        if (apiDataObj instanceof com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.SystemVoice) {
            return ((com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO.SystemVoice) apiDataObj).getVoiceName();
        } else if (apiDataObj instanceof com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO.Voice) {
            return ((com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO.Voice) apiDataObj).getName();
        } else if (apiDataObj instanceof com.microsoft.cognitiveservices.speech.VoiceInfo) {
            return ((com.microsoft.cognitiveservices.speech.VoiceInfo) apiDataObj).getName();
        } else if (apiDataObj instanceof MicrosoftVoiceData) {
            MicrosoftVoiceData voiceData = (MicrosoftVoiceData) apiDataObj;
            return voiceData.getDisplayName() != null ? voiceData.getDisplayName() : voiceData.getName();
        }
        return null;
    }

    /**
     * 重试单个同步记录
     */
    private void retrySingleRecord(DigitalVoiceSyncPO record) {
        String methodName = "retrySingleRecord";
        log.info("[{}] 开始重试同步记录，ID: {}, 供应商: {}, 音色ID: {}",
                 methodName, record.getId(), record.getProvider(), record.getThirdPartyVoiceId());

        try {
            // 根据供应商重新获取API数据
            Object apiData = fetchSingleVoiceData(record.getProvider(), record.getThirdPartyVoiceId());

            if (apiData != null) {
                // 转换API数据
                DigitalVoiceSyncPO updatedRecord = convertApiDataToSyncPO(record.getProvider(), apiData);
                if (updatedRecord != null) {
                    // 更新现有记录
                    updateExistingSyncRecord(record, updatedRecord);
                    record.setSyncStatus(1); // 标记为成功
                    record.setSyncErrorMsg(null); // 清除错误信息
                    log.info("[{}] 重试同步成功，ID: {}", methodName, record.getId());
                } else {
                    throw new RuntimeException("API数据转换失败");
                }
            } else {
                throw new RuntimeException("无法获取API数据");
            }

            // 更新重试信息
            record.setSyncRetryCount(record.getSyncRetryCount() + 1);
            record.setLastSyncTime(LocalDateTime.now());
            this.updateById(record);

        } catch (Exception e) {
            // 标记为失败
            record.setSyncStatus(2);
            record.setSyncErrorMsg(e.getMessage());
            record.setSyncRetryCount(record.getSyncRetryCount() + 1);
            record.setLastSyncTime(LocalDateTime.now());
            this.updateById(record);

            log.error("[{}] 重试同步失败，ID: {}, 错误: {}", methodName, record.getId(), e.getMessage());
            throw e;
        }
    }

    /**
     * 获取单个音色的API数据
     */
    private Object fetchSingleVoiceData(String provider, String voiceId) {
        try {
            switch (provider.toUpperCase()) {
                case "MINIMAX":
                    // Minimax需要获取所有音色然后筛选
                    com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO voiceResponse =
                        com.nacos.model.MiniMax.MiniMaxApiUtil.getVoiceId("all");
                    if (voiceResponse != null) {
                        // 在系统音色中查找
                        if (voiceResponse.getSystemVoice() != null) {
                            for (var systemVoice : voiceResponse.getSystemVoice()) {
                                if (voiceId.equals(systemVoice.getVoiceId())) {
                                    return systemVoice;
                                }
                            }
                        }
                        // 在克隆音色中查找
                        if (voiceResponse.getVoiceCloning() != null) {
                            for (var voiceCloning : voiceResponse.getVoiceCloning()) {
                                if (voiceId.equals(voiceCloning.getVoiceId())) {
                                    return voiceCloning;
                                }
                            }
                        }
                    }
                    break;

                case "ELEVENLABS":
                    // ElevenLabs需要获取所有音色然后筛选
                    List<com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO.Voice> voiceList =
                        com.nacos.model.Elevenlabs.ElevenLabsApiUtil.getAllVoices();
                    if (voiceList != null) {
                        for (var voice : voiceList) {
                            if (voiceId.equals(voice.getVoiceId())) {
                                return voice;
                            }
                        }
                    }
                    break;

                case "MICROSOFT":
                    // Microsoft需要获取所有音色然后筛选
                    String[] azureConfig = getAzureConfig();
                    if (azureConfig[0] != null && azureConfig[1] != null) {
                        Result<String> voicesResult = com.nacos.model.AzureAudio.AzureAudioApiUtil
                            .getVoicesList(azureConfig[0], azureConfig[1]);
                        if (voicesResult.isSuccess()) {
                            List<Object> allVoiceData = parseMicrosoftVoicesJson(voicesResult.getData());
                            if (allVoiceData != null) {
                                for (Object voiceData : allVoiceData) {
                                    if (voiceData instanceof MicrosoftVoiceData) {
                                        MicrosoftVoiceData msVoice = (MicrosoftVoiceData) voiceData;
                                        if (voiceId.equals(msVoice.getName())) {
                                            return msVoice;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    break;

                default:
                    log.warn("不支持的供应商类型: {}", provider);
                    return null;
            }
        } catch (Exception e) {
            log.error("获取单个音色API数据失败，provider: {}, voiceId: {}", provider, voiceId, e);
        }

        return null;
    }

    @Override
    public Page<DigitalVoiceSyncVO> querySyncRecords(String provider, Integer syncStatus, String keyword,
                                                    Integer currentPage, Integer pageSize) {
        try {
            Page<DigitalVoiceSyncPO> page = new Page<>(
                currentPage != null ? currentPage : 1,
                pageSize != null ? pageSize : 20
            );

            LambdaQueryWrapper<DigitalVoiceSyncPO> query = new LambdaQueryWrapper<>();

            if (StringUtils.hasText(provider)) {
                query.eq(DigitalVoiceSyncPO::getProvider, provider);
            }
            if (syncStatus != null) {
                query.eq(DigitalVoiceSyncPO::getSyncStatus, syncStatus);
            }
            if (StringUtils.hasText(keyword)) {
                query.and(wrapper -> wrapper
                    .like(DigitalVoiceSyncPO::getOriginalVoiceName, keyword)
                    .or()
                    .like(DigitalVoiceSyncPO::getThirdPartyVoiceId, keyword)
                );
            }

            query.orderByDesc(DigitalVoiceSyncPO::getLastSyncTime);

            Page<DigitalVoiceSyncPO> resultPage = this.page(page, query);

            // 转换为VO
            Page<DigitalVoiceSyncVO> voPage = new Page<>();
            BeanUtils.copyProperties(resultPage, voPage, "records");

            List<DigitalVoiceSyncVO> voList = resultPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
            voPage.setRecords(voList);

            return voPage;

        } catch (Exception e) {
            log.error("查询同步记录失败", e);
            throw new RuntimeException("查询同步记录失败", e);
        }
    }

    @Override
    public DigitalVoiceSyncVO getSyncRecordDetail(Long id) {
        try {
            DigitalVoiceSyncPO syncRecord = this.getById(id);
            if (syncRecord == null) {
                throw new RuntimeException("同步记录不存在");
            }

            return convertToVO(syncRecord);

        } catch (Exception e) {
            log.error("获取同步记录详情失败，ID: {}", id, e);
            throw new RuntimeException("获取同步记录详情失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> publishSyncRecord(Long syncId) {
        String methodName = "publishSyncRecord";
        log.info("[{}] 开始发布同步记录，ID: {}", methodName, syncId);

        try {
            DigitalVoiceSyncPO syncRecord = this.getById(syncId);
            if (syncRecord == null) {
                return Result.ERROR("同步记录不存在");
            }

            if (syncRecord.getSyncStatus() != 1) {
                return Result.ERROR("只能发布同步成功的记录");
            }

            if (syncRecord.getIsPublished() == 1) {
                return Result.ERROR("该记录已经发布");
            }

            // 创建系统音色记录
            DigitalVoiceSystemPO systemVoice = convertToSystemVoice(syncRecord);
            digitalVoiceSystemMapper.insert(systemVoice);

            // 更新同步记录状态
            syncRecord.setIsPublished(1);
            syncRecord.setPublishedVoiceId(systemVoice.getVoiceId());
            this.updateById(syncRecord);

            log.info("[{}] 发布同步记录成功，ID: {}, 系统音色ID: {}", methodName, syncId, systemVoice.getVoiceId());
            return Result.SUCCESS("发布成功，系统音色ID: " + systemVoice.getVoiceId());

        } catch (Exception e) {
            log.error("[{}] 发布同步记录失败，ID: {}", methodName, syncId, e);
            return Result.ERROR("发布失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> batchPublishSyncRecords(List<Long> syncIds) {
        String methodName = "batchPublishSyncRecords";
        log.info("[{}] 开始批量发布同步记录，数量: {}", methodName, syncIds.size());

        try {
            int successCount = 0;
            List<String> errors = new ArrayList<>();

            for (Long syncId : syncIds) {
                try {
                    Result<String> result = publishSyncRecord(syncId);
                    if (result.isSuccess()) {
                        successCount++;
                    } else {
                        errors.add("ID " + syncId + ": " + result.getMessage());
                    }
                } catch (Exception e) {
                    errors.add("ID " + syncId + ": " + e.getMessage());
                }
            }

            String message = String.format("批量发布完成，成功%d个，失败%d个", successCount, errors.size());
            if (!errors.isEmpty()) {
                message += "。失败详情: " + String.join("; ", errors);
            }

            log.info("[{}] {}", methodName, message);
            return Result.SUCCESS(message);

        } catch (Exception e) {
            log.error("[{}] 批量发布同步记录失败", methodName, e);
            return Result.ERROR("批量发布失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> unpublishSyncRecord(Long syncId) {
        String methodName = "unpublishSyncRecord";
        log.info("[{}] 开始取消发布同步记录，ID: {}", methodName, syncId);

        try {
            DigitalVoiceSyncPO syncRecord = this.getById(syncId);
            if (syncRecord == null) {
                return Result.ERROR("同步记录不存在");
            }

            if (syncRecord.getIsPublished() != 1) {
                return Result.ERROR("该记录未发布");
            }

            // 删除系统音色记录（逻辑删除）
            if (StringUtils.hasText(syncRecord.getPublishedVoiceId())) {
                digitalVoiceSystemService.deleteVoice(syncRecord.getPublishedVoiceId());
            }

            // 更新同步记录状态
            syncRecord.setIsPublished(0);
            syncRecord.setPublishedVoiceId(null);
            this.updateById(syncRecord);

            log.info("[{}] 取消发布同步记录成功，ID: {}", methodName, syncId);
            return Result.SUCCESS("取消发布成功");

        } catch (Exception e) {
            log.error("[{}] 取消发布同步记录失败，ID: {}", methodName, syncId, e);
            return Result.ERROR("取消发布失败: " + e.getMessage());
        }
    }

    /**
     * 转换为VO对象
     */
    private DigitalVoiceSyncVO convertToVO(DigitalVoiceSyncPO po) {
        DigitalVoiceSyncVO vo = new DigitalVoiceSyncVO();
        BeanUtils.copyProperties(po, vo);

        // 设置状态文本
        if (po.getSyncStatus() != null) {
            switch (po.getSyncStatus()) {
                case 1:
                    vo.setSyncStatusText("同步成功");
                    break;
                case 2:
                    vo.setSyncStatusText("同步失败");
                    break;
                default:
                    vo.setSyncStatusText("未知状态");
            }
        }

        return vo;
    }

    /**
     * 转换为系统音色对象
     */
    private DigitalVoiceSystemPO convertToSystemVoice(DigitalVoiceSyncPO syncRecord) {
        DigitalVoiceSystemPO systemVoice = new DigitalVoiceSystemPO();

        systemVoice.setVoiceId(generateNewVoiceId());
        systemVoice.setSyncId(syncRecord.getId());
        systemVoice.setThirdPartyVoiceId(syncRecord.getThirdPartyVoiceId());
        systemVoice.setProvider(syncRecord.getProvider());
        systemVoice.setVoiceName(syncRecord.getOriginalVoiceName());
        systemVoice.setDescription(syncRecord.getOriginalDescription());
        systemVoice.setLanguage(syncRecord.getOriginalLanguage());
        systemVoice.setGender(syncRecord.getOriginalGender());
        systemVoice.setDemoAudioUrl(syncRecord.getOriginalDemoAudio());
        systemVoice.setSortWeight(0);
        systemVoice.setStatus(1); // 正常状态

        // 设置推荐状态
        boolean isRecommended = "MINIMAX".equals(syncRecord.getProvider()) ||
                               "MICROSOFT".equals(syncRecord.getProvider());
        systemVoice.setIsRecommended(isRecommended ? 1 : 0);

        systemVoice.setCreatorId("SYSTEM_SYNC");
        systemVoice.setCreatedTime(LocalDateTime.now());
        systemVoice.setUpdateTime(LocalDateTime.now());

        return systemVoice;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> updateSyncRecord(Long syncId, DigitalVoiceSyncVO syncVO) {
        String methodName = "updateSyncRecord";
        log.info("[{}] 开始更新同步记录，ID: {}", methodName, syncId);

        try {
            DigitalVoiceSyncPO syncRecord = this.getById(syncId);
            if (syncRecord == null) {
                return Result.ERROR("同步记录不存在");
            }

            // 只允许更新特定字段
            if (StringUtils.hasText(syncVO.getOriginalVoiceName())) {
                syncRecord.setOriginalVoiceName(syncVO.getOriginalVoiceName());
            }
            if (StringUtils.hasText(syncVO.getOriginalDescription())) {
                syncRecord.setOriginalDescription(syncVO.getOriginalDescription());
            }
            if (StringUtils.hasText(syncVO.getOriginalLanguage())) {
                syncRecord.setOriginalLanguage(syncVO.getOriginalLanguage());
            }
            if (StringUtils.hasText(syncVO.getOriginalGender())) {
                syncRecord.setOriginalGender(syncVO.getOriginalGender());
            }
            if (StringUtils.hasText(syncVO.getOriginalDemoAudio())) {
                syncRecord.setOriginalDemoAudio(syncVO.getOriginalDemoAudio());
            }

            syncRecord.setUpdateTime(LocalDateTime.now());
            boolean updated = this.updateById(syncRecord);

            if (updated) {
                log.info("[{}] 更新同步记录成功，ID: {}", methodName, syncId);
                return Result.SUCCESS("更新成功");
            } else {
                return Result.ERROR("更新失败");
            }

        } catch (Exception e) {
            log.error("[{}] 更新同步记录失败，ID: {}", methodName, syncId, e);
            return Result.ERROR("更新失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> deleteSyncRecord(Long syncId) {
        String methodName = "deleteSyncRecord";
        log.info("[{}] 开始删除同步记录，ID: {}", methodName, syncId);

        try {
            DigitalVoiceSyncPO syncRecord = this.getById(syncId);
            if (syncRecord == null) {
                return Result.ERROR("同步记录不存在");
            }

            // 如果已发布，需要先取消发布
            if (syncRecord.getIsPublished() == 1) {
                Result<String> unpublishResult = unpublishSyncRecord(syncId);
                if (!unpublishResult.isSuccess()) {
                    return Result.ERROR("删除失败，无法取消发布: " + unpublishResult.getMessage());
                }
            }

            // 删除同步记录
            boolean deleted = this.removeById(syncId);

            if (deleted) {
                log.info("[{}] 删除同步记录成功，ID: {}", methodName, syncId);
                return Result.SUCCESS("删除成功");
            } else {
                return Result.ERROR("删除失败");
            }

        } catch (Exception e) {
            log.error("[{}] 删除同步记录失败，ID: {}", methodName, syncId, e);
            return Result.ERROR("删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> cleanExpiredFailedRecords(Integer days) {
        String methodName = "cleanExpiredFailedRecords";
        log.info("[{}] 开始清理过期的失败记录，保留天数: {}", methodName, days);

        try {
            if (days == null || days <= 0) {
                days = 30; // 默认保留30天
            }

            LocalDateTime expiredTime = LocalDateTime.now().minusDays(days);

            // 查询过期的失败记录
            List<DigitalVoiceSyncPO> expiredRecords = this.list(
                new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .eq(DigitalVoiceSyncPO::getSyncStatus, 2) // 同步失败
                    .eq(DigitalVoiceSyncPO::getIsPublished, 0) // 未发布
                    .lt(DigitalVoiceSyncPO::getLastSyncTime, expiredTime)
            );

            if (expiredRecords.isEmpty()) {
                return Result.SUCCESS("没有需要清理的过期失败记录");
            }

            // 批量删除
            List<Long> expiredIds = expiredRecords.stream()
                .map(DigitalVoiceSyncPO::getId)
                .collect(Collectors.toList());

            boolean deleted = this.removeByIds(expiredIds);

            if (deleted) {
                String message = String.format("清理完成，删除了%d条过期失败记录", expiredRecords.size());
                log.info("[{}] {}", methodName, message);
                return Result.SUCCESS(message);
            } else {
                return Result.ERROR("清理失败");
            }

        } catch (Exception e) {
            log.error("[{}] 清理过期失败记录失败", methodName, e);
            return Result.ERROR("清理失败: " + e.getMessage());
        }
    }

    @Override
    public List<DigitalVoiceSyncVO> getRecentSyncLogs(Integer limit) {
        String methodName = "getRecentSyncLogs";
        log.info("[{}] 获取最近的同步日志，限制数量: {}", methodName, limit);

        try {
            if (limit == null || limit <= 0) {
                limit = 50; // 默认获取50条
            }

            List<DigitalVoiceSyncPO> recentRecords = this.list(
                new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .orderByDesc(DigitalVoiceSyncPO::getLastSyncTime)
                    .last("LIMIT " + limit)
            );

            return recentRecords.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("[{}] 获取最近同步日志失败", methodName, e);
            throw new RuntimeException("获取最近同步日志失败", e);
        }
    }

    @Override
    public Map<String, Object> checkDataConsistency() {
        String methodName = "checkDataConsistency";
        log.info("[{}] 开始检查数据一致性", methodName);

        Map<String, Object> result = new HashMap<>();
        List<String> issues = new ArrayList<>();

        try {
            // 1. 检查已发布但系统表中不存在的记录
            List<DigitalVoiceSyncPO> publishedSyncRecords = this.list(
                new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .eq(DigitalVoiceSyncPO::getIsPublished, 1)
                    .isNotNull(DigitalVoiceSyncPO::getPublishedVoiceId)
            );

            int orphanedPublishedCount = 0;
            for (DigitalVoiceSyncPO syncRecord : publishedSyncRecords) {
                long systemCount = digitalVoiceSystemMapper.selectCount(
                    new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                        .eq(DigitalVoiceSystemPO::getVoiceId, syncRecord.getPublishedVoiceId())
                );
                if (systemCount == 0) {
                    orphanedPublishedCount++;
                }
            }

            if (orphanedPublishedCount > 0) {
                issues.add(String.format("发现%d条已发布但系统表中不存在的同步记录", orphanedPublishedCount));
            }

            // 2. 检查系统表中存在但同步表中标记为未发布的记录
            List<DigitalVoiceSystemPO> systemVoices = digitalVoiceSystemMapper.selectList(
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .isNotNull(DigitalVoiceSystemPO::getSyncId)
            );

            int inconsistentSystemCount = 0;
            for (DigitalVoiceSystemPO systemVoice : systemVoices) {
                DigitalVoiceSyncPO syncRecord = this.getById(systemVoice.getSyncId());
                if (syncRecord != null && syncRecord.getIsPublished() != 1) {
                    inconsistentSystemCount++;
                }
            }

            if (inconsistentSystemCount > 0) {
                issues.add(String.format("发现%d条系统表存在但同步表标记为未发布的记录", inconsistentSystemCount));
            }

            // 3. 检查重复的第三方音色ID
            Map<String, Long> duplicateCheck = new HashMap<>();
            List<DigitalVoiceSyncPO> allSyncRecords = this.list();
            for (DigitalVoiceSyncPO record : allSyncRecords) {
                String key = record.getProvider() + ":" + record.getThirdPartyVoiceId();
                duplicateCheck.put(key, duplicateCheck.getOrDefault(key, 0L) + 1);
            }

            long duplicateCount = duplicateCheck.values().stream()
                .filter(count -> count > 1)
                .count();

            if (duplicateCount > 0) {
                issues.add(String.format("发现%d组重复的第三方音色ID", duplicateCount));
            }

            // 4. 检查同步失败但重试次数异常的记录
            long abnormalRetryCount = this.count(
                new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .eq(DigitalVoiceSyncPO::getSyncStatus, 2)
                    .gt(DigitalVoiceSyncPO::getSyncRetryCount, 5)
            );

            if (abnormalRetryCount > 0) {
                issues.add(String.format("发现%d条同步失败但重试次数异常的记录", abnormalRetryCount));
            }

            // 汇总结果
            result.put("checkTime", LocalDateTime.now());
            result.put("totalSyncRecords", this.count());
            result.put("publishedRecords", this.count(new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                .eq(DigitalVoiceSyncPO::getIsPublished, 1)));
            result.put("systemVoiceRecords", digitalVoiceSystemMapper.selectCount(null));
            result.put("issuesFound", issues.size());
            result.put("issues", issues);
            result.put("isConsistent", issues.isEmpty());

            log.info("[{}] 数据一致性检查完成，发现{}个问题", methodName, issues.size());

        } catch (Exception e) {
            log.error("[{}] 数据一致性检查失败", methodName, e);
            result.put("error", "检查失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> fixDataInconsistency() {
        String methodName = "fixDataInconsistency";
        log.info("[{}] 开始修复数据不一致问题", methodName);

        try {
            List<String> fixResults = new ArrayList<>();

            // 1. 修复已发布但系统表中不存在的记录
            List<DigitalVoiceSyncPO> orphanedPublished = this.list(
                new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .eq(DigitalVoiceSyncPO::getIsPublished, 1)
                    .isNotNull(DigitalVoiceSyncPO::getPublishedVoiceId)
            );

            int fixedOrphaned = 0;
            for (DigitalVoiceSyncPO syncRecord : orphanedPublished) {
                long systemCount = digitalVoiceSystemMapper.selectCount(
                    new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                        .eq(DigitalVoiceSystemPO::getVoiceId, syncRecord.getPublishedVoiceId())
                );
                if (systemCount == 0) {
                    // 重置发布状态
                    syncRecord.setIsPublished(0);
                    syncRecord.setPublishedVoiceId(null);
                    this.updateById(syncRecord);
                    fixedOrphaned++;
                }
            }

            if (fixedOrphaned > 0) {
                fixResults.add(String.format("修复了%d条孤立的已发布记录", fixedOrphaned));
            }

            // 2. 修复系统表存在但同步表标记为未发布的记录
            List<DigitalVoiceSystemPO> systemVoices = digitalVoiceSystemMapper.selectList(
                new LambdaQueryWrapper<DigitalVoiceSystemPO>()
                    .isNotNull(DigitalVoiceSystemPO::getSyncId)
            );

            int fixedInconsistent = 0;
            for (DigitalVoiceSystemPO systemVoice : systemVoices) {
                DigitalVoiceSyncPO syncRecord = this.getById(systemVoice.getSyncId());
                if (syncRecord != null && syncRecord.getIsPublished() != 1) {
                    // 更新同步记录状态
                    syncRecord.setIsPublished(1);
                    syncRecord.setPublishedVoiceId(systemVoice.getVoiceId());
                    this.updateById(syncRecord);
                    fixedInconsistent++;
                }
            }

            if (fixedInconsistent > 0) {
                fixResults.add(String.format("修复了%d条状态不一致的记录", fixedInconsistent));
            }

            // 3. 清理重试次数异常的失败记录
            List<DigitalVoiceSyncPO> abnormalRetryRecords = this.list(
                new LambdaQueryWrapper<DigitalVoiceSyncPO>()
                    .eq(DigitalVoiceSyncPO::getSyncStatus, 2)
                    .gt(DigitalVoiceSyncPO::getSyncRetryCount, 5)
            );

            int fixedAbnormalRetry = 0;
            for (DigitalVoiceSyncPO record : abnormalRetryRecords) {
                record.setSyncRetryCount(3); // 重置为最大重试次数
                this.updateById(record);
                fixedAbnormalRetry++;
            }

            if (fixedAbnormalRetry > 0) {
                fixResults.add(String.format("修复了%d条重试次数异常的记录", fixedAbnormalRetry));
            }

            String message;
            if (fixResults.isEmpty()) {
                message = "数据一致性良好，无需修复";
            } else {
                message = "修复完成: " + String.join("; ", fixResults);
            }

            log.info("[{}] {}", methodName, message);
            return Result.SUCCESS(message);

        } catch (Exception e) {
            log.error("[{}] 修复数据不一致问题失败", methodName, e);
            return Result.ERROR("修复失败: " + e.getMessage());
        }
    }

    /**
     * 生成新的音色ID
     */
    private String generateNewVoiceId() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        int sequence = voiceIdSequence.getAndIncrement();
        return String.format("SYS_%s_%04d", dateStr, sequence);
    }

    /**
     * 获取Azure配置信息
     */
    private String[] getAzureConfig() {
        try {
            // 从配置文件或环境变量获取Azure配置
            String subscriptionKey = System.getProperty("azure.speech.subscription-key",
                System.getenv("AZURE_SPEECH_KEY"));
            String region = System.getProperty("azure.speech.region",
                System.getenv("AZURE_SPEECH_REGION"));

            // 如果环境变量为空，使用默认值
            if (subscriptionKey == null || subscriptionKey.trim().isEmpty()) {
                subscriptionKey = "ec363118097946b1adae818d9169b3ee"; // 默认值
            }
            if (region == null || region.trim().isEmpty()) {
                region = "eastus"; // 默认值
            }

            return new String[]{subscriptionKey, region};
        } catch (Exception e) {
            log.error("获取Azure配置失败", e);
            return new String[]{null, null};
        }
    }

    /**
     * 解析Microsoft API返回的JSON数据
     */
    private List<Object> parseMicrosoftVoicesJson(String jsonData) {
        try {
            com.alibaba.fastjson2.JSONArray jsonArray = com.alibaba.fastjson2.JSON.parseArray(jsonData);
            List<Object> voiceList = new ArrayList<>();

            for (int i = 0; i < jsonArray.size(); i++) {
                com.alibaba.fastjson2.JSONObject voiceObj = jsonArray.getJSONObject(i);
                // 创建一个简单的数据对象来存储Microsoft音色信息
                MicrosoftVoiceData voiceData = new MicrosoftVoiceData();
                voiceData.setName(voiceObj.getString("name"));
                voiceData.setShortName(voiceObj.getString("shortName"));
                voiceData.setDisplayName(voiceObj.getString("displayName"));
                voiceData.setLocale(voiceObj.getString("locale"));
                voiceData.setGender(voiceObj.getString("gender"));
                voiceData.setVoiceType(voiceObj.getString("voiceType"));
                voiceData.setDescription(voiceObj.getString("description"));
                voiceData.setAttributes(voiceObj.getString("attributes"));

                voiceList.add(voiceData);
            }

            return voiceList;
        } catch (Exception e) {
            log.error("解析Microsoft音色JSON数据失败", e);
            return null;
        }
    }

    /**
     * Microsoft音色数据类
     */
    private static class MicrosoftVoiceData {
        private String name;
        private String shortName;
        private String displayName;
        private String locale;
        private String gender;
        private String voiceType;
        private String description;
        private String attributes;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getShortName() { return shortName; }
        public void setShortName(String shortName) { this.shortName = shortName; }
        public String getDisplayName() { return displayName; }
        public void setDisplayName(String displayName) { this.displayName = displayName; }
        public String getLocale() { return locale; }
        public void setLocale(String locale) { this.locale = locale; }
        public String getGender() { return gender; }
        public void setGender(String gender) { this.gender = gender; }
        public String getVoiceType() { return voiceType; }
        public void setVoiceType(String voiceType) { this.voiceType = voiceType; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getAttributes() { return attributes; }
        public void setAttributes(String attributes) { this.attributes = attributes; }
    }
}
