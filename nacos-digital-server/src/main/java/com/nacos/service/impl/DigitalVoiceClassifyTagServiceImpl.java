package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nacos.entity.po.DigitalVoiceClassifyTagPO;
import com.nacos.entity.po.DigitalVoiceTagRelationPO;
import com.nacos.entity.vo.VoiceClassifyTagVO;
import com.nacos.mapper.DigitalVoiceClassifyTagMapper;
import com.nacos.mapper.DigitalVoiceTagRelationMapper;
import com.nacos.service.DigitalVoiceClassifyTagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 音色分类标签服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalVoiceClassifyTagServiceImpl extends ServiceImpl<DigitalVoiceClassifyTagMapper, DigitalVoiceClassifyTagPO>
        implements DigitalVoiceClassifyTagService {

    private final DigitalVoiceTagRelationMapper digitalVoiceTagRelationMapper;

    /**
     * 根据维度获取标签列表
     */
    @Override
    public List<VoiceClassifyTagVO> listTagsByDimension(String dimensionCode) {
        if (!StringUtils.hasText(dimensionCode)) {
            return new ArrayList<>();
        }
        
        try {
            List<DigitalVoiceClassifyTagPO> tags = this.list(
                new LambdaQueryWrapper<DigitalVoiceClassifyTagPO>()
                    .eq(DigitalVoiceClassifyTagPO::getDimensionCode, dimensionCode)
                    .eq(DigitalVoiceClassifyTagPO::getStatus, 1)
                    .orderByAsc(DigitalVoiceClassifyTagPO::getSortOrder)
            );
            
            return tags.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据维度获取标签列表失败，dimensionCode: {}", dimensionCode, e);
            throw new RuntimeException("获取标签列表失败", e);
        }
    }

    /**
     * 根据维度获取层级标签树
     */
    @Override
    public List<VoiceClassifyTagVO> getTagTreeByDimension(String dimensionCode) {
        List<VoiceClassifyTagVO> allTags = listTagsByDimension(dimensionCode);
        return buildTagTree(allTags);
    }

    /**
     * 根据标签编码获取标签信息
     */
    @Override
    public VoiceClassifyTagVO getTagByCode(String tagCode) {
        if (!StringUtils.hasText(tagCode)) {
            return null;
        }
        
        try {
            DigitalVoiceClassifyTagPO tag = this.getOne(
                new LambdaQueryWrapper<DigitalVoiceClassifyTagPO>()
                    .eq(DigitalVoiceClassifyTagPO::getTagCode, tagCode)
                    .eq(DigitalVoiceClassifyTagPO::getStatus, 1)
            );
            
            return tag != null ? convertToVO(tag) : null;
        } catch (Exception e) {
            log.error("根据编码获取标签信息失败，tagCode: {}", tagCode, e);
            throw new RuntimeException("获取标签信息失败", e);
        }
    }

    /**
     * 根据标签ID列表获取标签信息
     */
    @Override
    public List<VoiceClassifyTagVO> getTagsByIds(List<Long> tagIds) {
        if (tagIds == null || tagIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            List<DigitalVoiceClassifyTagPO> tags = this.listByIds(tagIds);
            return tags.stream()
                .filter(tag -> tag.getStatus() == 1)
                .map(this::convertToVO)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据ID列表获取标签信息失败，tagIds: {}", tagIds, e);
            throw new RuntimeException("获取标签信息失败", e);
        }
    }

    /**
     * 获取所有启用的标签，按维度分组
     */
    @Override
    public Map<String, List<VoiceClassifyTagVO>> listTagsGroupByDimension() {
        try {
            List<DigitalVoiceClassifyTagPO> allTags = this.list(
                new LambdaQueryWrapper<DigitalVoiceClassifyTagPO>()
                    .eq(DigitalVoiceClassifyTagPO::getStatus, 1)
                    .orderByAsc(DigitalVoiceClassifyTagPO::getDimensionCode)
                    .orderByAsc(DigitalVoiceClassifyTagPO::getSortOrder)
            );
            
            return allTags.stream()
                .map(this::convertToVO)
                .collect(Collectors.groupingBy(VoiceClassifyTagVO::getDimensionCode));
        } catch (Exception e) {
            log.error("获取按维度分组的标签列表失败", e);
            throw new RuntimeException("获取标签列表失败", e);
        }
    }

    /**
     * 创建新标签
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VoiceClassifyTagVO createTag(String tagCode, String tagName, String dimensionCode, Long parentId, Integer sortOrder) {
        if (!StringUtils.hasText(tagCode) || !StringUtils.hasText(tagName) || !StringUtils.hasText(dimensionCode)) {
            throw new IllegalArgumentException("标签编码、名称和维度编码不能为空");
        }
        
        // 检查编码是否已存在
        if (existsByCode(tagCode)) {
            throw new IllegalArgumentException("标签编码已存在：" + tagCode);
        }
        
        try {
            DigitalVoiceClassifyTagPO tag = new DigitalVoiceClassifyTagPO();
            tag.setTagCode(tagCode);
            tag.setTagName(tagName);
            tag.setDimensionCode(dimensionCode);
            tag.setParentId(parentId);
            tag.setSortOrder(sortOrder != null ? sortOrder : 0);
            tag.setStatus(1);
            
            boolean success = this.save(tag);
            if (!success) {
                throw new RuntimeException("创建标签失败");
            }
            
            return convertToVO(tag);
        } catch (Exception e) {
            log.error("创建标签失败，tagCode: {}, tagName: {}", tagCode, tagName, e);
            throw new RuntimeException("创建标签失败", e);
        }
    }

    /**
     * 更新标签信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VoiceClassifyTagVO updateTag(String tagCode, String tagName, Long parentId, Integer sortOrder, Integer status) {
        if (!StringUtils.hasText(tagCode)) {
            throw new IllegalArgumentException("标签编码不能为空");
        }
        
        try {
            DigitalVoiceClassifyTagPO tag = this.getOne(
                new LambdaQueryWrapper<DigitalVoiceClassifyTagPO>()
                    .eq(DigitalVoiceClassifyTagPO::getTagCode, tagCode)
            );
            
            if (tag == null) {
                throw new IllegalArgumentException("标签不存在：" + tagCode);
            }
            
            if (StringUtils.hasText(tagName)) {
                tag.setTagName(tagName);
            }
            if (parentId != null) {
                tag.setParentId(parentId);
            }
            if (sortOrder != null) {
                tag.setSortOrder(sortOrder);
            }
            if (status != null) {
                tag.setStatus(status);
            }
            
            boolean success = this.updateById(tag);
            if (!success) {
                throw new RuntimeException("更新标签失败");
            }
            
            return convertToVO(tag);
        } catch (Exception e) {
            log.error("更新标签失败，tagCode: {}", tagCode, e);
            throw new RuntimeException("更新标签失败", e);
        }
    }

    /**
     * 删除标签（逻辑删除）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTag(String tagCode) {
        if (!StringUtils.hasText(tagCode)) {
            throw new IllegalArgumentException("标签编码不能为空");
        }
        
        try {
            DigitalVoiceClassifyTagPO tag = this.getOne(
                new LambdaQueryWrapper<DigitalVoiceClassifyTagPO>()
                    .eq(DigitalVoiceClassifyTagPO::getTagCode, tagCode)
            );
            
            if (tag == null) {
                return false;
            }
            
            // 设置为禁用状态
            tag.setStatus(0);
            return this.updateById(tag);
        } catch (Exception e) {
            log.error("删除标签失败，tagCode: {}", tagCode, e);
            throw new RuntimeException("删除标签失败", e);
        }
    }

    /**
     * 检查标签编码是否存在
     */
    @Override
    public boolean existsByCode(String tagCode) {
        if (!StringUtils.hasText(tagCode)) {
            return false;
        }
        
        try {
            return this.count(
                new LambdaQueryWrapper<DigitalVoiceClassifyTagPO>()
                    .eq(DigitalVoiceClassifyTagPO::getTagCode, tagCode)
            ) > 0;
        } catch (Exception e) {
            log.error("检查标签编码是否存在失败，tagCode: {}", tagCode, e);
            return false;
        }
    }

    /**
     * 获取标签的子标签列表
     */
    @Override
    public List<VoiceClassifyTagVO> getChildTags(Long parentId) {
        if (parentId == null) {
            return new ArrayList<>();
        }
        
        try {
            List<DigitalVoiceClassifyTagPO> childTags = this.list(
                new LambdaQueryWrapper<DigitalVoiceClassifyTagPO>()
                    .eq(DigitalVoiceClassifyTagPO::getParentId, parentId)
                    .eq(DigitalVoiceClassifyTagPO::getStatus, 1)
                    .orderByAsc(DigitalVoiceClassifyTagPO::getSortOrder)
            );
            
            return childTags.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取子标签列表失败，parentId: {}", parentId, e);
            throw new RuntimeException("获取子标签列表失败", e);
        }
    }

    /**
     * 统计标签下的音色数量
     */
    @Override
    public Integer countVoicesByTag(Long tagId) {
        if (tagId == null) {
            return 0;
        }
        
        try {
            return Math.toIntExact(digitalVoiceTagRelationMapper.selectCount(
                new LambdaQueryWrapper<DigitalVoiceTagRelationPO>()
                    .eq(DigitalVoiceTagRelationPO::getTagId, tagId)
            ));
        } catch (Exception e) {
            log.error("统计标签下音色数量失败，tagId: {}", tagId, e);
            return 0;
        }
    }

    /**
     * 批量统计标签下的音色数量
     */
    @Override
    public Map<Long, Integer> batchCountVoicesByTags(List<Long> tagIds) {
        if (tagIds == null || tagIds.isEmpty()) {
            return new HashMap<>();
        }
        
        try {
            List<DigitalVoiceTagRelationPO> relations = digitalVoiceTagRelationMapper.selectList(
                new LambdaQueryWrapper<DigitalVoiceTagRelationPO>()
                    .in(DigitalVoiceTagRelationPO::getTagId, tagIds)
            );
            
            return relations.stream()
                .collect(Collectors.groupingBy(
                    DigitalVoiceTagRelationPO::getTagId,
                    Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
        } catch (Exception e) {
            log.error("批量统计标签下音色数量失败，tagIds: {}", tagIds, e);
            return new HashMap<>();
        }
    }

    /**
     * 初始化基础标签数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initializeBaseTags() {
        try {
            // 基础标签数据
            Object[][] baseTags = {
                // 性别维度标签
                {"GENDER_MALE", "男性", "GENDER", null, 1},
                {"GENDER_FEMALE", "女性", "GENDER", null, 2},
                // 语言维度标签
                {"LANG_ZH_CN", "中文", "LANGUAGE", null, 1},
                {"LANG_EN_US", "英文", "LANGUAGE", null, 2},
                // 供应商维度标签
                {"PROVIDER_MINIMAX", "MiniMax", "PROVIDER", null, 1},
                {"PROVIDER_MICROSOFT", "Microsoft", "PROVIDER", null, 2},
                {"PROVIDER_ELEVENLABS", "ElevenLabs", "PROVIDER", null, 3}
            };
            
            for (Object[] tagData : baseTags) {
                String code = (String) tagData[0];
                String name = (String) tagData[1];
                String dimensionCode = (String) tagData[2];
                Long parentId = (Long) tagData[3];
                Integer sort = (Integer) tagData[4];
                
                if (!existsByCode(code)) {
                    createTag(code, name, dimensionCode, parentId, sort);
                    log.info("初始化标签：{} - {}", code, name);
                }
            }
        } catch (Exception e) {
            log.error("初始化基础标签数据失败", e);
            throw new RuntimeException("初始化基础标签数据失败", e);
        }
    }

    /**
     * 构建标签树
     */
    private List<VoiceClassifyTagVO> buildTagTree(List<VoiceClassifyTagVO> allTags) {
        Map<Long, VoiceClassifyTagVO> tagMap = allTags.stream()
            .collect(Collectors.toMap(VoiceClassifyTagVO::getId, tag -> tag));
        
        List<VoiceClassifyTagVO> rootTags = new ArrayList<>();
        
        for (VoiceClassifyTagVO tag : allTags) {
            if (tag.getParentId() == null) {
                rootTags.add(tag);
            } else {
                VoiceClassifyTagVO parent = tagMap.get(tag.getParentId());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(tag);
                }
            }
        }
        
        return rootTags;
    }

    /**
     * PO转VO
     */
    private VoiceClassifyTagVO convertToVO(DigitalVoiceClassifyTagPO po) {
        VoiceClassifyTagVO vo = new VoiceClassifyTagVO();
        BeanUtils.copyProperties(po, vo);
        vo.setChildren(new ArrayList<>());
        vo.setVoiceCount(0); // 默认值，需要时单独查询
        return vo;
    }
}
