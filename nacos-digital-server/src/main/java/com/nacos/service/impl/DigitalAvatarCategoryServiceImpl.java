package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nacos.entity.po.DigitalAvatarCategoryPO;
import com.nacos.entity.po.DigitalGroupCategoryRelationPO;
import com.nacos.entity.po.DigitalSystemGroupPO;
import com.nacos.entity.vo.DigitalAvatarCategoryVO;
import com.nacos.entity.vo.DigitalSystemGroupVO;
import com.nacos.mapper.DigitalAvatarCategoryMapper;
import com.nacos.mapper.DigitalGroupCategoryRelationMapper;
import com.nacos.mapper.DigitalSystemGroupMapper;
import com.nacos.service.DigitalAvatarCategoryService;
import com.nacos.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DigitalAvatarCategoryServiceImpl extends ServiceImpl<DigitalAvatarCategoryMapper, DigitalAvatarCategoryPO> implements DigitalAvatarCategoryService {

    @Autowired
    private DigitalGroupCategoryRelationMapper relationMapper;
    
    @Autowired
    private DigitalSystemGroupMapper systemGroupMapper;

    @Override
    public Result<List<DigitalAvatarCategoryVO>> listCategories() {
        try {
            LambdaQueryWrapper<DigitalAvatarCategoryPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalAvatarCategoryPO::getIsDeleted, 0)
                   .orderByAsc(DigitalAvatarCategoryPO::getSort);
            List<DigitalAvatarCategoryPO> list = this.list(wrapper);
            List<DigitalAvatarCategoryVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
            return Result.SUCCESS(voList);
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            return Result.ERROR("获取分类列表失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> addCategory(DigitalAvatarCategoryVO categoryVO) {
        try {
            if (!StringUtils.hasText(categoryVO.getCategoryName())) {
                return Result.ERROR("分类名称不能为空");
            }

            // 检查分类名称是否已存在
            LambdaQueryWrapper<DigitalAvatarCategoryPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalAvatarCategoryPO::getCategoryName, categoryVO.getCategoryName())
                   .eq(DigitalAvatarCategoryPO::getIsDeleted, 0);
            if (this.count(wrapper) > 0) {
                return Result.ERROR("分类名称已存在");
            }

            DigitalAvatarCategoryPO po = new DigitalAvatarCategoryPO();
            BeanUtils.copyProperties(categoryVO, po);
            
            // 不需要手动设置这些值，会由MyMetaObjectHandler自动填充
            // categoryId会自动生成
            // status会自动设置默认值1
            // sort会自动设置默认值0
            // createdTime会自动填充
            // updateTime会自动填充
            // isDeleted会自动设置默认值0
            
            boolean success = this.save(po);
            return success ? Result.SUCCESS(true) : Result.ERROR("添加分类失败");
        } catch (Exception e) {
            log.error("添加分类失败", e);
            return Result.ERROR("添加分类失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateCategory(DigitalAvatarCategoryVO categoryVO) {
        try {
            if (categoryVO.getId() == null) {
                return Result.ERROR("分类ID不能为空");
            }
            if (!StringUtils.hasText(categoryVO.getCategoryName())) {
                return Result.ERROR("分类名称不能为空");
            }

            // 检查分类是否存在
            DigitalAvatarCategoryPO existingCategory = this.getById(categoryVO.getId());
            if (existingCategory == null || existingCategory.getIsDeleted() == 1) {
                return Result.ERROR("分类不存在");
            }

            // 检查分类名称是否重复（排除自身）
            LambdaQueryWrapper<DigitalAvatarCategoryPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalAvatarCategoryPO::getCategoryName, categoryVO.getCategoryName())
                   .eq(DigitalAvatarCategoryPO::getIsDeleted, 0)
                   .ne(DigitalAvatarCategoryPO::getId, categoryVO.getId());
            if (this.count(wrapper) > 0) {
                return Result.ERROR("分类名称已存在");
            }

            DigitalAvatarCategoryPO po = new DigitalAvatarCategoryPO();
            BeanUtils.copyProperties(categoryVO, po);
            // updateTime会由MyMetaObjectHandler自动填充
            boolean success = this.updateById(po);
            return success ? Result.SUCCESS(true) : Result.ERROR("更新分类失败");
        } catch (Exception e) {
            log.error("更新分类失败", e);
            return Result.ERROR("更新分类失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> deleteCategory(Long id) {
        try {
            if (id == null) {
                return Result.ERROR("分类ID不能为空");
            }

            DigitalAvatarCategoryPO category = this.getById(id);
            if (category == null || category.getIsDeleted() == 1) {
                return Result.ERROR("分类不存在");
            }

            // 逻辑删除，会自动设置isDeleted=1
            // updateTime会由MyMetaObjectHandler自动填充
            boolean success = this.removeById(id);
            return success ? Result.SUCCESS(true) : Result.ERROR("删除分类失败");
        } catch (Exception e) {
            log.error("删除分类失败", e);
            return Result.ERROR("删除分类失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateStatus(Long id, Integer status) {
        try {
            if (id == null) {
                return Result.ERROR("分类ID不能为空");
            }
            if (status == null || (status != 0 && status != 1)) {
                return Result.ERROR("状态值无效");
            }

            DigitalAvatarCategoryPO category = this.getById(id);
            if (category == null || category.getIsDeleted() == 1) {
                return Result.ERROR("分类不存在");
            }
            
            DigitalAvatarCategoryPO po = new DigitalAvatarCategoryPO();
            po.setId(id);
            po.setStatus(status);
            // updateTime会由MyMetaObjectHandler自动填充
            boolean success = this.updateById(po);
            return success ? Result.SUCCESS(true) : Result.ERROR("更新状态失败");
        } catch (Exception e) {
            log.error("更新分类状态失败", e);
            return Result.ERROR("更新分类状态失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<DigitalSystemGroupVO>> listSystemGroupsByCategoryCode(String categoryCode) {
        try {
            if (!StringUtils.hasText(categoryCode)) {
                return Result.ERROR("分类编码不能为空");
            }

            // 1. 根据分类编码获取分类信息
            LambdaQueryWrapper<DigitalAvatarCategoryPO> categoryWrapper = new LambdaQueryWrapper<>();
            categoryWrapper.eq(DigitalAvatarCategoryPO::getCategoryCode, categoryCode)
                         .eq(DigitalAvatarCategoryPO::getIsDeleted, 0)
                         .eq(DigitalAvatarCategoryPO::getStatus, 1);
            DigitalAvatarCategoryPO category = this.getOne(categoryWrapper);
            
            if (category == null) {
                return Result.ERROR("分类不存在或已禁用");
            }

            // 2. 获取分类关联的系统组ID列表
            LambdaQueryWrapper<DigitalGroupCategoryRelationPO> relationWrapper = new LambdaQueryWrapper<>();
            relationWrapper.eq(DigitalGroupCategoryRelationPO::getCategoryId, category.getId())
                         .eq(DigitalGroupCategoryRelationPO::getGroupType, 2); // 2-系统组
            List<DigitalGroupCategoryRelationPO> relations = relationMapper.selectList(relationWrapper);
            
            if (relations.isEmpty()) {
                return Result.SUCCESS(Collections.emptyList());
            }

            // 3. 获取系统组列表
            List<String> groupIds = relations.stream()
                                          .map(DigitalGroupCategoryRelationPO::getGroupId)
                                          .collect(Collectors.toList());
            
            LambdaQueryWrapper<DigitalSystemGroupPO> groupWrapper = new LambdaQueryWrapper<>();
            groupWrapper.in(DigitalSystemGroupPO::getGroupId, groupIds)
                       .eq(DigitalSystemGroupPO::getIsDeleted, 0)
                       .eq(DigitalSystemGroupPO::getStatus, 1)
                       .orderByAsc(DigitalSystemGroupPO::getSort);
            
            List<DigitalSystemGroupPO> groups = systemGroupMapper.selectList(groupWrapper);
            List<DigitalSystemGroupVO> voList = groups.stream()
                                                    .map(this::convertToGroupVO)
                                                    .collect(Collectors.toList());
            return Result.SUCCESS(voList);
        } catch (Exception e) {
            log.error("获取分类系统组列表失败，分类编码：{}", categoryCode, e);
            return Result.ERROR("获取分类系统组列表失败");
        }
    }

    private DigitalSystemGroupVO convertToGroupVO(DigitalSystemGroupPO po) {
        if (po == null) {
            return null;
        }
        DigitalSystemGroupVO vo = new DigitalSystemGroupVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    private DigitalAvatarCategoryVO convertToVO(DigitalAvatarCategoryPO po) {
        if (po == null) {
            return null;
        }
        DigitalAvatarCategoryVO vo = new DigitalAvatarCategoryVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }
} 