package com.nacos.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.nacos.entity.enums.DigitalNotificationEnum;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.entity.po.DigitalNotificationPO;
import com.nacos.mapper.DigitalNotificationMapper;
import com.nacos.result.Result;
import com.nacos.service.DigitalNotificationService;
import com.nacos.utils.MessageSendUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DigitalNotificationServiceImpl extends ServiceImpl<DigitalNotificationMapper, DigitalNotificationPO> implements DigitalNotificationService {

    @Override
    public Result<Void> createNotification(Long userId, String taskId, Integer notifType, String notifContent) {
        try {
            // 1. 创建通知记录
            DigitalNotificationPO notification = DigitalNotificationPO.builder()
                .userId(userId)
                .taskId(taskId)
                .notifType(notifType)
                .notifTitle(DigitalNotificationEnum.getDesc(notifType))
                .notifContent(notifContent)
                .isRead(0)
                .build();
            
            save(notification);
            return Result.SUCCESS();
        } catch (Exception e) {
            log.error("创建通知失败：userId={}, taskId={}, type={}", userId, taskId, notifType, e);
            return Result.ERROR("创建通知失败");
        }
    }

    @Override
    public Result<List<DigitalNotificationPO>> getUnreadNotifications(Long userId) {
        try {
            List<DigitalNotificationPO> notifications = list(
                new LambdaQueryWrapper<DigitalNotificationPO>()
                    .eq(DigitalNotificationPO::getUserId, userId)
                    .eq(DigitalNotificationPO::getIsRead, 0)
                    .eq(DigitalNotificationPO::getIsDeleted, 0)
                    .orderByDesc(DigitalNotificationPO::getCreatedTime)
            );
            return Result.SUCCESS(notifications);
        } catch (Exception e) {
            log.error("获取未读通知失败：userId={}", userId, e);
            return Result.ERROR("获取未读通知失败");
        }
    }

    @Override
    public Result<Void> markAsRead(Long userId, Long notificationId) {
        try {
            update().eq("id", notificationId)
                    .eq("user_id", userId)
                    .set("is_read", 1)
                    .update();
            return Result.SUCCESS();
        } catch (Exception e) {
            log.error("标记通知已读失败：userId={}, notificationId={}", userId, notificationId, e);
            return Result.ERROR("标记通知已读失败");
        }
    }

    @Override
    public Result<Void> deleteNotification(Long userId, Long notificationId) {
        try {
            update().eq("id", notificationId)
                    .eq("user_id", userId)
                    .set("is_deleted", 1)
                    .update();
            return Result.SUCCESS();
        } catch (Exception e) {
            log.error("删除通知失败：userId={}, notificationId={}", userId, notificationId, e);
            return Result.ERROR("删除通知失败");
        }
    }

    // ==================== 音频生成任务通知方法实现 ====================

    @Override
    public void sendAudioGenerationQueuedNotification(DigitalAudioTaskPO taskPO) {
        String methodName = "sendAudioGenerationQueuedNotification";
        try {
            // 1. 创建数据库通知记录
            createNotification(
                    Long.valueOf(taskPO.getUserId()),
                    taskPO.getTaskId(),
                    DigitalNotificationEnum.AUDIO_GENERATION_QUEUING.getValue(),
                    "您的音频生成任务已接收，正在排队等待处理"
            );

            // 2. 构建WebSocket通知数据
            JSONObject notificationData = buildAudioNotificationData(taskPO);
            notificationData.put("status", "queued");
            notificationData.put("message", "音频生成任务已接收，正在排队等待处理");
            notificationData.put("progress", 0);

            // 3. 发送WebSocket通知
            sendWebSocketNotification(taskPO.getUserId(), notificationData);

            log.info("[{}] 音频生成任务已接收通知发送成功: taskId={}, userId={}",
                    methodName, taskPO.getTaskId(), taskPO.getUserId());

        } catch (Exception e) {
            log.error("[{}] 发送音频生成任务已接收通知异常: taskId={}, error={}",
                    methodName, taskPO.getTaskId(), e.getMessage(), e);
        }
    }

    @Override
    public void sendAudioGenerationStartNotification(DigitalAudioTaskPO taskPO) {
        String methodName = "sendAudioGenerationStartNotification";
        try {
            // 1. 创建数据库通知记录
            createNotification(
                    Long.valueOf(taskPO.getUserId()),
                    taskPO.getTaskId(),
                    DigitalNotificationEnum.AUDIO_GENERATION_PROCESSING.getValue(),
                    "您的音频生成任务已开始处理，请耐心等待"
            );

            // 2. 构建WebSocket通知数据
            JSONObject notificationData = buildAudioNotificationData(taskPO);
            notificationData.put("status", "processing");
            notificationData.put("message", "音频生成任务开始处理");
            notificationData.put("progress", 10);

            // 3. 发送WebSocket通知
            sendWebSocketNotification(taskPO.getUserId(), notificationData);

            log.info("[{}] 音频生成开始处理通知发送成功: taskId={}, userId={}",
                    methodName, taskPO.getTaskId(), taskPO.getUserId());

        } catch (Exception e) {
            log.error("[{}] 发送音频生成开始处理通知异常: taskId={}, error={}",
                    methodName, taskPO.getTaskId(), e.getMessage(), e);
        }
    }

    @Override
    public void sendAudioGenerationSuccessNotification(DigitalAudioTaskPO taskPO, String audioUrl, Integer durationMs) {
        String methodName = "sendAudioGenerationSuccessNotification";
        try {
            // 1. 创建数据库通知记录
            String content = String.format("您的音频生成任务已完成！音频时长：%d秒",
                    durationMs != null ? durationMs / 1000 : 0);
            createNotification(
                    Long.valueOf(taskPO.getUserId()),
                    taskPO.getTaskId(),
                    DigitalNotificationEnum.AUDIO_GENERATION_SUCCESS.getValue(),
                    content
            );

            // 2. 构建WebSocket通知数据
            JSONObject notificationData = buildAudioNotificationData(taskPO);
            notificationData.put("status", "success");
            notificationData.put("message", "音频生成成功");
            notificationData.put("progress", 100);
            notificationData.put("audioUrl", audioUrl);
            notificationData.put("durationMs", durationMs);

            // 3. 发送WebSocket通知
            sendWebSocketNotification(taskPO.getUserId(), notificationData);

            log.info("[{}] 音频生成成功通知发送成功: taskId={}, userId={}, audioUrl={}",
                    methodName, taskPO.getTaskId(), taskPO.getUserId(), audioUrl);

        } catch (Exception e) {
            log.error("[{}] 发送音频生成成功通知异常: taskId={}, error={}",
                    methodName, taskPO.getTaskId(), e.getMessage(), e);
        }
    }

    @Override
    public void sendAudioGenerationFailureNotification(DigitalAudioTaskPO taskPO, String errorMessage) {
        String methodName = "sendAudioGenerationFailureNotification";
        try {
            // 1. 创建数据库通知记录
            String content = String.format("您的音频生成任务失败：%s", errorMessage);
            createNotification(
                    Long.valueOf(taskPO.getUserId()),
                    taskPO.getTaskId(),
                    DigitalNotificationEnum.AUDIO_GENERATION_FAIL.getValue(),
                    content
            );

            // 2. 构建WebSocket通知数据
            JSONObject notificationData = buildAudioNotificationData(taskPO);
            notificationData.put("status", "failed");
            notificationData.put("message", "音频生成失败");
            notificationData.put("progress", 0);
            notificationData.put("errorMessage", errorMessage);

            // 3. 发送WebSocket通知
            sendWebSocketNotification(taskPO.getUserId(), notificationData);

            log.info("[{}] 音频生成失败通知发送成功: taskId={}, userId={}, error={}",
                    methodName, taskPO.getTaskId(), taskPO.getUserId(), errorMessage);

        } catch (Exception e) {
            log.error("[{}] 发送音频生成失败通知异常: taskId={}, error={}",
                    methodName, taskPO.getTaskId(), e.getMessage(), e);
        }
    }

    @Override
    public void sendAudioGenerationTimeoutNotification(DigitalAudioTaskPO taskPO) {
        String methodName = "sendAudioGenerationTimeoutNotification";
        try {
            // 1. 创建数据库通知记录
            createNotification(
                    Long.valueOf(taskPO.getUserId()),
                    taskPO.getTaskId(),
                    DigitalNotificationEnum.AUDIO_GENERATION_TIMEOUT.getValue(),
                    "您的音频生成任务超时，请稍后重试"
            );

            // 2. 构建WebSocket通知数据
            JSONObject notificationData = buildAudioNotificationData(taskPO);
            notificationData.put("status", "timeout");
            notificationData.put("message", "音频生成超时");
            notificationData.put("progress", 0);

            // 3. 发送WebSocket通知
            sendWebSocketNotification(taskPO.getUserId(), notificationData);

            log.info("[{}] 音频生成超时通知发送成功: taskId={}, userId={}",
                    methodName, taskPO.getTaskId(), taskPO.getUserId());

        } catch (Exception e) {
            log.error("[{}] 发送音频生成超时通知异常: taskId={}, error={}",
                    methodName, taskPO.getTaskId(), e.getMessage(), e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建音频通知基础数据
     *
     * @param taskPO 任务PO对象
     * @return 基础通知数据
     */
    private JSONObject buildAudioNotificationData(DigitalAudioTaskPO taskPO) {
        JSONObject data = new JSONObject();
        data.put("taskId", taskPO.getTaskId());
        data.put("userId", taskPO.getUserId());
        data.put("provider", taskPO.getProvider());
        data.put("createdTime", taskPO.getCreatedTime());
        data.put("updateTime", taskPO.getUpdateTime());
        data.put("timestamp", System.currentTimeMillis());
        return data;
    }

    /**
     * 发送WebSocket通知
     *
     * @param userId 用户ID
     * @param notificationData 通知数据
     */
    private void sendWebSocketNotification(String userId, JSONObject notificationData) {
        try {
            String jsonMessage = BMessageSendUtil.getJSONStr(
                    Long.valueOf(userId),
                    BMessageSendEnum.AUDIO_GENERATION_PUSH,
                    notificationData.toString()
            );

            boolean sent = BRedisServiceUtil.sendMessageDigital(jsonMessage);
            if (!sent) {
                log.warn("WebSocket通知发送失败: userId={}, data={}", userId, notificationData.toString());
            }

        } catch (Exception e) {
            log.error("发送WebSocket通知异常: userId={}, error={}", userId, e.getMessage(), e);
        }
    }
}