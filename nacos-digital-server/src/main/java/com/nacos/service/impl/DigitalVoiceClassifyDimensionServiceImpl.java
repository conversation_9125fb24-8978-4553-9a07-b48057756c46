package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nacos.entity.po.DigitalVoiceClassifyDimensionPO;
import com.nacos.entity.vo.VoiceClassifyDimensionVO;
import com.nacos.entity.vo.VoiceClassifyTagVO;
import com.nacos.mapper.DigitalVoiceClassifyDimensionMapper;
import com.nacos.service.DigitalVoiceClassifyDimensionService;
import com.nacos.service.DigitalVoiceClassifyTagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 音色分类维度服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalVoiceClassifyDimensionServiceImpl extends ServiceImpl<DigitalVoiceClassifyDimensionMapper, DigitalVoiceClassifyDimensionPO>
        implements DigitalVoiceClassifyDimensionService {

    private final DigitalVoiceClassifyTagService digitalVoiceClassifyTagService;

    /**
     * 获取所有启用的维度列表
     * 按照排序号升序排列
     */
    @Override
    public List<VoiceClassifyDimensionVO> listEnabledDimensions() {
        try {
            List<DigitalVoiceClassifyDimensionPO> dimensions = this.list(
                new LambdaQueryWrapper<DigitalVoiceClassifyDimensionPO>()
                    .eq(DigitalVoiceClassifyDimensionPO::getStatus, 1)
                    .orderByAsc(DigitalVoiceClassifyDimensionPO::getSortOrder)
            );
            
            return dimensions.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取启用维度列表失败", e);
            throw new RuntimeException("获取维度列表失败", e);
        }
    }

    /**
     * 根据维度编码获取维度信息
     */
    @Override
    public VoiceClassifyDimensionVO getDimensionByCode(String dimensionCode) {
        if (!StringUtils.hasText(dimensionCode)) {
            return null;
        }
        
        try {
            DigitalVoiceClassifyDimensionPO dimension = this.getOne(
                new LambdaQueryWrapper<DigitalVoiceClassifyDimensionPO>()
                    .eq(DigitalVoiceClassifyDimensionPO::getDimensionCode, dimensionCode)
                    .eq(DigitalVoiceClassifyDimensionPO::getStatus, 1)
            );
            
            return dimension != null ? convertToVO(dimension) : null;
        } catch (Exception e) {
            log.error("根据编码获取维度信息失败，dimensionCode: {}", dimensionCode, e);
            throw new RuntimeException("获取维度信息失败", e);
        }
    }

    /**
     * 获取维度及其下的标签列表
     */
    @Override
    public VoiceClassifyDimensionVO getDimensionWithTags(String dimensionCode) {
        VoiceClassifyDimensionVO dimension = getDimensionByCode(dimensionCode);
        if (dimension == null) {
            return null;
        }
        
        try {
            // 获取该维度下的标签列表
            List<VoiceClassifyTagVO> tags = digitalVoiceClassifyTagService.listTagsByDimension(dimensionCode);
            dimension.setTagList(tags);
            
            return dimension;
        } catch (Exception e) {
            log.error("获取维度及标签列表失败，dimensionCode: {}", dimensionCode, e);
            throw new RuntimeException("获取维度及标签列表失败", e);
        }
    }

    /**
     * 获取所有维度及其下的标签列表
     */
    @Override
    public List<VoiceClassifyDimensionVO> listDimensionsWithTags() {
        try {
            List<VoiceClassifyDimensionVO> dimensions = listEnabledDimensions();
            
            // 为每个维度加载标签列表
            return dimensions.stream().map(dimension -> {
                try {
                    List<VoiceClassifyTagVO> tags = digitalVoiceClassifyTagService.listTagsByDimension(dimension.getDimensionCode());
                    dimension.setTagList(tags);
                    return dimension;
                } catch (Exception e) {
                    log.warn("加载维度标签失败，dimensionCode: {}", dimension.getDimensionCode(), e);
                    dimension.setTagList(new ArrayList<>());
                    return dimension;
                }
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取所有维度及标签列表失败", e);
            throw new RuntimeException("获取维度及标签列表失败", e);
        }
    }

    /**
     * 创建新维度
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VoiceClassifyDimensionVO createDimension(String dimensionCode, String dimensionName, Integer sortOrder) {
        if (!StringUtils.hasText(dimensionCode) || !StringUtils.hasText(dimensionName)) {
            throw new IllegalArgumentException("维度编码和名称不能为空");
        }
        
        // 检查编码是否已存在
        if (existsByCode(dimensionCode)) {
            throw new IllegalArgumentException("维度编码已存在：" + dimensionCode);
        }
        
        try {
            DigitalVoiceClassifyDimensionPO dimension = new DigitalVoiceClassifyDimensionPO();
            dimension.setDimensionCode(dimensionCode);
            dimension.setDimensionName(dimensionName);
            dimension.setSortOrder(sortOrder != null ? sortOrder : 0);
            dimension.setStatus(1);
            
            boolean success = this.save(dimension);
            if (!success) {
                throw new RuntimeException("创建维度失败");
            }
            
            return convertToVO(dimension);
        } catch (Exception e) {
            log.error("创建维度失败，dimensionCode: {}, dimensionName: {}", dimensionCode, dimensionName, e);
            throw new RuntimeException("创建维度失败", e);
        }
    }

    /**
     * 更新维度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VoiceClassifyDimensionVO updateDimension(String dimensionCode, String dimensionName, Integer sortOrder, Integer status) {
        if (!StringUtils.hasText(dimensionCode)) {
            throw new IllegalArgumentException("维度编码不能为空");
        }
        
        try {
            DigitalVoiceClassifyDimensionPO dimension = this.getOne(
                new LambdaQueryWrapper<DigitalVoiceClassifyDimensionPO>()
                    .eq(DigitalVoiceClassifyDimensionPO::getDimensionCode, dimensionCode)
            );
            
            if (dimension == null) {
                throw new IllegalArgumentException("维度不存在：" + dimensionCode);
            }
            
            if (StringUtils.hasText(dimensionName)) {
                dimension.setDimensionName(dimensionName);
            }
            if (sortOrder != null) {
                dimension.setSortOrder(sortOrder);
            }
            if (status != null) {
                dimension.setStatus(status);
            }
            
            boolean success = this.updateById(dimension);
            if (!success) {
                throw new RuntimeException("更新维度失败");
            }
            
            return convertToVO(dimension);
        } catch (Exception e) {
            log.error("更新维度失败，dimensionCode: {}", dimensionCode, e);
            throw new RuntimeException("更新维度失败", e);
        }
    }

    /**
     * 删除维度（逻辑删除）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDimension(String dimensionCode) {
        if (!StringUtils.hasText(dimensionCode)) {
            throw new IllegalArgumentException("维度编码不能为空");
        }
        
        try {
            DigitalVoiceClassifyDimensionPO dimension = this.getOne(
                new LambdaQueryWrapper<DigitalVoiceClassifyDimensionPO>()
                    .eq(DigitalVoiceClassifyDimensionPO::getDimensionCode, dimensionCode)
            );
            
            if (dimension == null) {
                return false;
            }
            
            // 设置为禁用状态
            dimension.setStatus(0);
            return this.updateById(dimension);
        } catch (Exception e) {
            log.error("删除维度失败，dimensionCode: {}", dimensionCode, e);
            throw new RuntimeException("删除维度失败", e);
        }
    }

    /**
     * 检查维度编码是否存在
     */
    @Override
    public boolean existsByCode(String dimensionCode) {
        if (!StringUtils.hasText(dimensionCode)) {
            return false;
        }
        
        try {
            return this.count(
                new LambdaQueryWrapper<DigitalVoiceClassifyDimensionPO>()
                    .eq(DigitalVoiceClassifyDimensionPO::getDimensionCode, dimensionCode)
            ) > 0;
        } catch (Exception e) {
            log.error("检查维度编码是否存在失败，dimensionCode: {}", dimensionCode, e);
            return false;
        }
    }

    /**
     * 初始化基础维度数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initializeBaseDimensions() {
        try {
            // 基础维度数据
            String[][] baseDimensions = {
                {"GENDER", "性别", "1"},
                {"LANGUAGE", "语言", "2"},
                {"STYLE", "风格", "3"},
                {"PROVIDER", "供应商", "4"}
            };
            
            for (String[] dimData : baseDimensions) {
                String code = dimData[0];
                String name = dimData[1];
                Integer sort = Integer.valueOf(dimData[2]);
                
                if (!existsByCode(code)) {
                    createDimension(code, name, sort);
                    log.info("初始化维度：{} - {}", code, name);
                }
            }
        } catch (Exception e) {
            log.error("初始化基础维度数据失败", e);
            throw new RuntimeException("初始化基础维度数据失败", e);
        }
    }

    /**
     * PO转VO
     */
    private VoiceClassifyDimensionVO convertToVO(DigitalVoiceClassifyDimensionPO po) {
        VoiceClassifyDimensionVO vo = new VoiceClassifyDimensionVO();
        BeanUtils.copyProperties(po, vo);
        vo.setTagList(new ArrayList<>()); // 默认空列表
        return vo;
    }
}
