package com.nacos.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.business.message.BMessageSendEnum;
import com.business.message.mq.BRedisServiceUtil;
import com.nacos.config.TencentCloudConfig;
import com.nacos.entity.bo.TaskStatusUpdateBO;
import com.nacos.entity.dto.DigitalTaskItem;
import com.nacos.entity.dto.DigitalVideoGenerationDTO;
import com.nacos.entity.enums.DigitalNotificationEnum;
import com.nacos.entity.enums.VideoTaskStatusEnum;
import com.nacos.entity.po.DigitalSystemAvatarPO;
import com.nacos.entity.po.DigitalUserAvatarPO;
import com.nacos.entity.po.DigitalVideoTaskItemPO;
import com.nacos.entity.po.DigitalVideoTaskPO;
import com.nacos.mapper.DigitalSystemAvatarMapper;
import com.nacos.mapper.DigitalUserAvatarMapper;
import com.nacos.mapper.DigitalVideoTaskItemMapper;
import com.nacos.mapper.DigitalVideoTaskMapper;
import com.nacos.model.TXDigital.TXDigitalApisUtil;
import com.nacos.result.Result;
import com.nacos.service.*;
import com.nacos.utils.MessageSendUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数字人视频服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TxMiniServiceImpl implements TxMiniService {

    private final DigitalUserAvatarMapper digitalUserAvatarMapper;
    private final DigitalSystemAvatarMapper digitalSystemAvatarMapper;
    private final DigitalVideoTaskMapper digitalVideoTaskMapper;
    private final DigitalVideoTaskService digitalVideoTaskService;
    private final DigitalVideoAsyncService digitalVideoAsyncService;
    private final TXDigitalApisUtil txDigitalApisUtil;
    private final DigitalVideoTaskItemMapper digitalVideoTaskItemMapper;
    private final DigitalNotificationService digitalNotificationService;
    @Autowired
    private TencentCloudConfig tencentCloudConfig;

    /**
     * 处理排队中的任务
     */
    @Override
    public void processQueueingTasks() {
        String methodName = "processQueueingTasks";
        try {
            // 查询任务队列中有几个排队任务
            long queueingCount = digitalVideoTaskMapper.selectCount(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.QUEUING.getValue())
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );
            if (queueingCount == 0) {
//                log.info("[{}] 没有排队中的任务", methodName);
                return;
            }

            log.info("[{}] 当前任务队列中排队任务数量：{}", methodName, queueingCount);
            // 1. 首先检查是否有进行中的任务
            long inProgressCount = digitalVideoTaskMapper.selectCount(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.IN_PROGRESS.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );

            // 如果已经有进行中的任务，则不处理排队任务
            if (inProgressCount > 0) {
                log.info("[{}] 当前有{}个进行中的任务，暂不处理排队任务", methodName, inProgressCount);
                return;
            }

            // 2. 查询排队中的任务（按创建时间升序，最早创建的任务优先处理）
            List<DigitalVideoTaskPO> queueingTasks = digitalVideoTaskMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.QUEUING.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                            .orderByAsc(DigitalVideoTaskPO::getCreatedTime)
                            .last("LIMIT 1") // 只取一个任务处理
            );

            if (queueingTasks.isEmpty()) {
//                log.debug("[{}] 没有排队中的任务", methodName);
                return;
            }

            // 3. 处理队列中的第一个任务
            DigitalVideoTaskPO task = queueingTasks.getFirst();
            log.info("[{}] 开始处理排队任务：taskId={}, userId={}", methodName, task.getTaskId(), task.getUserId());

            try {
                // 4. 查询子任务详情
                List<DigitalVideoTaskItemPO> taskItems = digitalVideoTaskItemMapper.selectList(
                        new LambdaQueryWrapper<DigitalVideoTaskItemPO>()
                                .eq(DigitalVideoTaskItemPO::getTaskId, task.getTaskId())
                                .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                                .orderByAsc(DigitalVideoTaskItemPO::getSequence)
                );

                if (taskItems.isEmpty()) {
                    String errorMsg = "该任务没有子任务";
                    log.error("[{}] {}：taskId={}", methodName, errorMsg, task.getTaskId());
                    digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                        .taskId(task.getTaskId())
                        .status(VideoTaskStatusEnum.FAILED.getValue())
                        .errorMsg(errorMsg)
                        .build()
                    );
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.FAILED.getValue(), errorMsg);
                    return;
                }
                // 更新任务状态为进行中
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(task.getTaskId())
                    .status(VideoTaskStatusEnum.IN_PROGRESS.getValue())
                    .build()
                );
                // 提交到异步服务处理
                digitalVideoAsyncService.processVideoTasks(
                        task.getTaskId(),
                        task.getUserId(),
                        taskItems
                );
                log.info("[{}] 任务已提交到异步服务处理：taskId={}", methodName, task.getTaskId());
                pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.IN_PROGRESS.getValue(), "任务已提交到异步服务处理");
            } catch (Exception e) {
                String errorMsg = "处理任务异常：" + e.getMessage();
                log.error("[{}] {}：taskId={}", methodName, errorMsg, task.getTaskId(), e);
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(task.getTaskId())
                    .status(VideoTaskStatusEnum.FAILED.getValue())
                    .errorMsg(errorMsg)
                    .build()
                );
                pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.FAILED.getValue(), errorMsg);
            }
        } catch (Exception e) {
            log.error("[{}] 处理排队中的任务异常", methodName, e);
        }
    }

    /**
     * 处理超时任务
     */
    @Override
    public void processTimeoutTasks() {
        String methodName = "processTimeoutTasks";
        log.info("[{}] 开始处理超时任务", methodName);

        try {
            // 获取配置的超时时间（分钟）
            int timeoutMinutes = 30; // 默认30分钟

            // 计算超时时间点
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);

            // 查询可能超时的任务（进行中状态且更新时间超过阈值）
            List<DigitalVideoTaskPO> potentialTimeoutTasks = digitalVideoTaskMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.IN_PROGRESS.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                            .lt(DigitalVideoTaskPO::getUpdateTime, timeoutThreshold)
            );

            if (potentialTimeoutTasks.isEmpty()) {
                log.info("[{}] 没有超时的任务", methodName);
                return;
            }

            log.info("[{}] 发现{}个可能超时的任务", methodName, potentialTimeoutTasks.size());

            // 处理每个可能超时的任务
            for (DigitalVideoTaskPO task : potentialTimeoutTasks) {
                try {
                    log.info("[{}] 处理超时任务：taskId={}, createdTime={}, updateTime={}",
                            methodName, task.getTaskId(), task.getCreatedTime(), task.getUpdateTime());

                    // 更新任务状态为超时
                    task.setStatus(VideoTaskStatusEnum.TIMEOUT.getValue());
                    task.setErrorMsg("任务处理超时，请重试");
                    task.setUpdateTime(new Date());
                    digitalVideoTaskMapper.updateById(task);

                    // 推送任务状态更新
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.TIMEOUT.getValue(), "任务处理超时，请重试");

                    log.info("[{}] 任务{}已标记为超时", methodName, task.getTaskId());

                } catch (Exception e) {
                    log.error("[{}] 处理超时任务{}异常", methodName, task.getTaskId(), e);
                }
            }
        } catch (Exception e) {
            log.error("[{}] 处理超时任务异常", methodName, e);
        }
    }

    @Override
    public void processVideo() {
        //1 从队列中获取任务
        // 从digital_user_avatar表中获取video_task_status=0的记录
        //2 处理任务

        //3 处理完成后，更新任务状态
        //4 推送任务状态更新
    }

    @Override
    public void processVoice() {

    }

    @Override
    public void processFinish() {

    }

    /**
     * 推送任务状态更新
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param status 任务状态
     * @param message 状态消息
     */
    private void pushTaskStatus(String userId, String taskId, Integer status, String message) {
        String methodName = "pushTaskStatus";
        log.info("[{}] 开始推送任务状态：userId={}, taskId={}, status={}", methodName, userId, taskId, status);
        
        try {
            // 1. 查询任务信息
            DigitalVideoTaskPO task = digitalVideoTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getTaskId, taskId)
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );
            
            if (task != null) {
                // 2. 如果任务完成或失败，发送通知消息
                if (status != null && (status == VideoTaskStatusEnum.SUCCESS.getValue() || status == VideoTaskStatusEnum.FAILED.getValue())) {
                    log.debug("[{}] 构建通知消息：userId={}, taskId={}, status={}", methodName, userId, taskId, status);
                    
                    // 使用新的数字人专用通知服务
                    int notifType = status == VideoTaskStatusEnum.SUCCESS.getValue() ? 
                        DigitalNotificationEnum.VIDEO_TASK_SUCCESS.getValue() : 
                        DigitalNotificationEnum.VIDEO_TASK_FAIL.getValue();
                    
                    String notifContent = message != null ? message : 
                        (status == VideoTaskStatusEnum.SUCCESS.getValue() ? 
                            "您的数字人视频已生成完成，点击查看" : 
                            "很抱歉，您的数字人视频生成失败，请重试");
                    
                    // 1. 创建通知记录
                    digitalNotificationService.createNotification(
                        Long.valueOf(userId),
                        taskId,
                        notifType,
                        notifContent
                    );
                    
                    // 2. 发送通知消息
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("taskId", taskId);
                    dataMap.put("notifType", notifType);
                    dataMap.put("notifTitle", DigitalNotificationEnum.getDesc(notifType));
                    dataMap.put("notifContent", notifContent);
                    
                    String jsonMessage = MessageSendUtil.getJSONStr(
                        userId,
                        BMessageSendEnum.VIDEO_JOB_DIGITAL_PUSH,
                        dataMap
                    );
                    BRedisServiceUtil.sendMessageDigital(jsonMessage);
                }
            } else {
                log.warn("[{}] 未找到任务信息，无法推送状态: userId={}, taskId={}", methodName, userId, taskId);
            }
        } catch (Exception e) {
            log.error("[{}] 推送任务状态失败：userId={}, taskId={}, error={}", methodName, userId, taskId, e.getMessage(), e);
        }
    }
}
