package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.entity.bo.VideoEditTaskStatusUpdateBO;
import com.nacos.entity.bo.VideoEditSubTaskUpdateBO;
import com.nacos.entity.dto.VideoEditTaskItem;
import com.nacos.entity.enums.VideoEditTaskStatusEnum;
import com.nacos.entity.enums.VideoEditTaskItemStatusEnum;
import com.nacos.entity.po.VideoEditTaskPO;
import com.nacos.entity.po.VideoEditTaskItemPO;
import com.nacos.entity.vo.VideoEditTaskVO;
import com.nacos.entity.vo.VideoEditTaskItemVO;
import com.nacos.entity.vo.VideoEditTaskStatusVO;
import com.nacos.entity.vo.VideoEditTaskItemWithCoverVO;
import com.nacos.mapper.VideoEditTaskMapper;
import com.nacos.mapper.VideoEditTaskItemMapper;
import com.nacos.mapper.DigitalAudioTaskMapper;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.result.Result;
import com.nacos.service.VideoEditTaskService;
import com.nacos.utils.DigitalFileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 视频编辑任务Service实现类
 * 参考DigitalVideoTaskServiceImpl的设计模式
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VideoEditTaskServiceImpl implements VideoEditTaskService {

    private final VideoEditTaskMapper taskMapper;
    private final VideoEditTaskItemMapper taskItemMapper;
    private final DigitalAudioTaskMapper digitalAudioTaskMapper;

    /**
     * 创建视频编辑任务
     * @param userId 用户ID
     * @param taskItems 任务项列表
     * @return 任务ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> createTask(String userId, List<VideoEditTaskItem> taskItems) {
        return createTask(userId, taskItems, null, null);
    }

    /**
     * 创建视频编辑任务（支持全局屏幕尺寸参数）
     * @param userId 用户ID
     * @param taskItems 任务项列表
     * @param globalScreenWidth 全局屏幕宽度（可选）
     * @param globalScreenHeight 全局屏幕高度（可选）
     * @return 任务ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> createTask(String userId, List<VideoEditTaskItem> taskItems, Integer globalScreenWidth, Integer globalScreenHeight) {
        String methodName = "createTask";
        log.info("[{}] 开始创建视频编辑任务，userId：{}，taskItems：{}", methodName, userId, taskItems.size());

        try {
            // 参数验证
            if (StringUtils.isBlank(userId)) {
                log.error("[{}] 用户ID为空", methodName);
                return Result.ERROR("用户ID不能为空");
            }
            if (taskItems == null || taskItems.isEmpty()) {
                log.error("[{}] 任务项列表为空", methodName);
                return Result.ERROR("任务项列表不能为空");
            }

            // 生成任务ID
            String generateTaskId = generateTaskId();
            Date now = new Date();

            // 创建主任务对象
            VideoEditTaskPO taskPO = new VideoEditTaskPO()
                    .setTaskId(generateTaskId)
                    .setUserId(userId)
                    .setTotalCount(taskItems.size())
                    .setSuccessCount(0)
                    .setFailedCount(0)
                    .setStatus(VideoEditTaskStatusEnum.QUEUING.getValue())
                    .setProgress(0)
                    .setCreatedTime(now)
                    .setUpdateTime(now)
                    .setIsDeleted(0);
            
            // 插入主任务数据
            taskMapper.insert(taskPO);

            // 构建全局任务参数JSON
            String globalTaskParameters = buildGlobalTaskParameters(globalScreenWidth, globalScreenHeight);

            // 创建子任务
            for (int i = 0; i < taskItems.size(); i++) {
                VideoEditTaskItem item = taskItems.get(i);
                String subTaskId = generateSubTaskId(generateTaskId, i + 1);

                // 尝试获取音频时长
                Long audioLength = getAudioLengthFromUrl(item.getVoiceUrl());
                if (audioLength != null) {
                    log.info("[{}] 获取音频时长成功，subTaskId：{}，时长：{}ms", methodName, subTaskId, audioLength);
                } else {
                    log.warn("[{}] 获取音频时长失败，subTaskId：{}，将在后续处理时重新获取", methodName, subTaskId);
                }

                VideoEditTaskItemPO itemPO = new VideoEditTaskItemPO()
                        .setTaskId(generateTaskId)
                        .setUserId(userId)
                        .setSubTaskId(subTaskId)
                        .setAvatarId(item.getAvatarId())
                        .setSourceVoiceUrl(item.getVoiceUrl())
                        .setOther(item.getOther())
                        .setTaskParameters(globalTaskParameters)  // 设置全局任务参数
                        .setAudioLength(audioLength)  // 设置音频时长
                        .setSequence(i + 1)
                        .setStatus(VideoEditTaskItemStatusEnum.PENDING.getValue())
                        .setProgress(0)
                        .setCreatedTime(now)
                        .setUpdateTime(now)
                        .setIsDeleted(0);

                taskItemMapper.insert(itemPO);
            }

            log.info("[{}] 创建视频编辑任务成功，taskId：{}", methodName, generateTaskId);
            return Result.SUCCESS(generateTaskId);

        } catch (Exception e) {
            log.error("[{}] 创建视频编辑任务异常", methodName, e);
            return Result.ERROR("创建任务失败：" + e.getMessage());
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        // 使用格式：VET_yyyyMMddHHmm_4位随机数 (Video Edit Task)
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        String dateStr = sdf.format(new Date());
        String randomNum = String.format("%04d", (int) (Math.random() * 10000));
        return "VET_" + dateStr + "_" + randomNum;
    }

    /**
     * 生成子任务ID
     */
    private String generateSubTaskId(String taskId, int sequence) {
        return taskId + "_" + sequence;
    }

    /**
     * 从音频URL获取音频时长（毫秒）- 混合策略优化版本
     * 优先从digital_audio_task表获取，失败时使用文件解析
     */
    private Long getAudioLengthFromUrl(String voiceUrl) {
        String methodName = "getAudioLengthFromUrl";
        try {
            if (StringUtils.isBlank(voiceUrl)) {
                return null;
            }

            log.debug("[{}] 开始获取音频时长（混合策略），URL：{}", methodName, voiceUrl);

            // 1. 优先从digital_audio_task表获取时长（快速、准确）
            Long durationFromAudioTask = getAudioLengthFromAudioTask(voiceUrl);
            if (durationFromAudioTask != null && durationFromAudioTask > 0) {
                log.info("[{}] 从digital_audio_task表获取音频时长成功，URL：{}，时长：{}ms",
                        methodName, voiceUrl, durationFromAudioTask);
                return durationFromAudioTask;
            }

            // 2. 备选方案：使用文件解析方式（兼容性保证）
            log.debug("[{}] digital_audio_task表中未找到音频时长，使用文件解析方式，URL：{}", methodName, voiceUrl);
            Long audioLength = DigitalFileUtil.getAudioLengthByUrl(voiceUrl);
            if (audioLength != null && audioLength > 0) {
                log.info("[{}] 文件解析获取音频时长成功，URL：{}，时长：{}ms", methodName, voiceUrl, audioLength);
                return audioLength;
            }

            log.warn("[{}] 所有方式都无法获取音频时长，URL：{}", methodName, voiceUrl);
            return null;
        } catch (Exception e) {
            log.warn("[{}] 获取音频时长异常，URL：{}，错误：{}", methodName, voiceUrl, e.getMessage());
            return null;
        }
    }

    /**
     * 从digital_audio_task表获取音频时长（毫秒）
     * 通过音频URL关联查询已生成的音频任务记录
     */
    private Long getAudioLengthFromAudioTask(String voiceUrl) {
        String methodName = "getAudioLengthFromAudioTask";
        try {
            if (StringUtils.isBlank(voiceUrl)) {
                return null;
            }

            log.debug("[{}] 开始从digital_audio_task表查询音频时长，URL：{}", methodName, voiceUrl);

            // 查询digital_audio_task表，通过generated_audio_url关联
            DigitalAudioTaskPO audioTask = digitalAudioTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalAudioTaskPO>()
                    .eq(DigitalAudioTaskPO::getGeneratedAudioUrl, voiceUrl)
                    .eq(DigitalAudioTaskPO::getStatus, 2) // 生成成功状态
                    .eq(DigitalAudioTaskPO::getIsDeleted, false)
                    .orderByDesc(DigitalAudioTaskPO::getCreatedTime)
                    .last("LIMIT 1")
            );

            if (audioTask != null && audioTask.getDurationMs() != null && audioTask.getDurationMs() > 0) {
                Long durationMs = audioTask.getDurationMs().longValue();
                log.debug("[{}] 从digital_audio_task表查询音频时长成功，URL：{}，时长：{}ms，任务ID：{}",
                        methodName, voiceUrl, durationMs, audioTask.getTaskId());
                return durationMs;
            }

            log.debug("[{}] digital_audio_task表中未找到匹配的音频记录，URL：{}", methodName, voiceUrl);
            return null;

        } catch (Exception e) {
            log.warn("[{}] 从digital_audio_task表查询音频时长异常，URL：{}，错误：{}",
                    methodName, voiceUrl, e.getMessage());
            return null;
        }
    }

    /**
     * 构建全局任务参数JSON
     * 注意：此方法构建的参数将在StandardVideoProcessor中被完整的API参数覆盖
     * 这里主要用于初始化taskParameters字段，便于在API调用前传递全局参数
     * @param globalScreenWidth 全局屏幕宽度
     * @param globalScreenHeight 全局屏幕高度
     * @return JSON字符串
     */
    private String buildGlobalTaskParameters(Integer globalScreenWidth, Integer globalScreenHeight) {
        try {
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            com.fasterxml.jackson.databind.node.ObjectNode params = mapper.createObjectNode();

            if (globalScreenWidth != null && globalScreenWidth > 0) {
                params.put("screenWidth", globalScreenWidth);
            }

            if (globalScreenHeight != null && globalScreenHeight > 0) {
                params.put("screenHeight", globalScreenHeight);
            }

            // 如果没有任何全局参数，返回null
            if (params.size() == 0) {
                return null;
            }

            return mapper.writeValueAsString(params);
        } catch (Exception e) {
            log.error("构建全局任务参数JSON失败", e);
            return null;
        }
    }

    /**
     * 查询用户任务列表
     */
    @Override
    public Result<List<VideoEditTaskVO>> listUserTasks(String userId) {
        String methodName = "listUserTasks";
        log.info("[{}] 开始查询用户任务列表，userId：{}", methodName, userId);

        try {
            if (StringUtils.isBlank(userId)) {
                log.error("[{}] 用户ID为空", methodName);
                return Result.ERROR("用户ID不能为空");
            }

            // 查询主任务列表
            List<VideoEditTaskPO> taskList = taskMapper.selectList(
                    new LambdaQueryWrapper<VideoEditTaskPO>()
                            .eq(VideoEditTaskPO::getUserId, userId)
                            .eq(VideoEditTaskPO::getIsDeleted, 0)
                            .orderByDesc(VideoEditTaskPO::getCreatedTime)
            );

            // 转换为VO对象
            List<VideoEditTaskVO> voList = taskList.stream()
                    .map(this::convertToTaskVO)
                    .collect(Collectors.toList());

            log.info("[{}] 查询用户任务列表成功，userId：{}，任务数量：{}", methodName, userId, voList.size());
            return Result.SUCCESS(voList);

        } catch (Exception e) {
            log.error("[{}] 查询用户任务列表异常", methodName, e);
            return Result.ERROR("查询任务列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询任务详情
     */
    @Override
    public Result<VideoEditTaskVO> getTaskDetail(String taskId) {
        String methodName = "getTaskDetail";
        log.info("[{}] 开始查询任务详情，taskId：{}", methodName, taskId);

        try {
            if (StringUtils.isBlank(taskId)) {
                log.error("[{}] 任务ID为空", methodName);
                return Result.ERROR("任务ID不能为空");
            }

            // 查询主任务
            VideoEditTaskPO taskPO = taskMapper.selectOne(
                    new LambdaQueryWrapper<VideoEditTaskPO>()
                            .eq(VideoEditTaskPO::getTaskId, taskId)
                            .eq(VideoEditTaskPO::getIsDeleted, 0)
            );

            if (taskPO == null) {
                log.error("[{}] 任务不存在，taskId：{}", methodName, taskId);
                return Result.ERROR("任务不存在");
            }

            // 查询子任务列表
            List<VideoEditTaskItemPO> itemList = taskItemMapper.selectList(
                    new LambdaQueryWrapper<VideoEditTaskItemPO>()
                            .eq(VideoEditTaskItemPO::getTaskId, taskId)
                            .eq(VideoEditTaskItemPO::getIsDeleted, 0)
                            .orderByAsc(VideoEditTaskItemPO::getSequence)
            );

            // 转换为VO对象
            VideoEditTaskVO taskVO = convertToTaskVO(taskPO);
            taskVO.setItems(itemList.stream()
                    .map(this::convertToItemVO)
                    .collect(Collectors.toList()));

            log.info("[{}] 查询任务详情成功，taskId：{}", methodName, taskId);
            return Result.SUCCESS(taskVO);

        } catch (Exception e) {
            log.error("[{}] 查询任务详情异常", methodName, e);
            return Result.ERROR("查询任务详情失败：" + e.getMessage());
        }
    }

    /**
     * 更新主任务数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateTask(VideoEditTaskStatusUpdateBO updateBO) {
        String methodName = "updateTask";
        log.info("[{}] 开始更新主任务数据，taskId：{}，status：{}，errorMsg：{}",
                methodName, updateBO.getTaskId(), updateBO.getStatus(), updateBO.getErrorMsg());

        try {
            if (StringUtils.isBlank(updateBO.getTaskId())) {
                log.error("[{}] 任务ID为空", methodName);
                return Result.ERROR("任务ID不能为空");
            }

            VideoEditTaskPO updatePO = new VideoEditTaskPO();
            updatePO.setUpdateTime(new Date());

            if (updateBO.getStatus() != null) {
                updatePO.setStatus(updateBO.getStatus());
            }
            if (StringUtils.isNotBlank(updateBO.getOutputVideoUrl())) {
                updatePO.setOutputVideoUrl(updateBO.getOutputVideoUrl());
            }
            if (StringUtils.isNotBlank(updateBO.getOutputCoverUrl())) {
                updatePO.setOutputCoverUrl(updateBO.getOutputCoverUrl());
            }
            if (updateBO.getProgress() != null) {
                updatePO.setProgress(updateBO.getProgress());
            }
            if (updateBO.getSuccessCount() != null) {
                updatePO.setSuccessCount(updateBO.getSuccessCount());
            }
            if (updateBO.getFailedCount() != null) {
                updatePO.setFailedCount(updateBO.getFailedCount());
            }
            if (updateBO.getCost() != null) {
                updatePO.setCost(updateBO.getCost());
            }
            if (StringUtils.isNotBlank(updateBO.getErrorMsg())) {
                updatePO.setErrorMsg(updateBO.getErrorMsg());
            }

            int updateCount = taskMapper.update(updatePO,
                    new LambdaQueryWrapper<VideoEditTaskPO>()
                            .eq(VideoEditTaskPO::getTaskId, updateBO.getTaskId())
                            .eq(VideoEditTaskPO::getIsDeleted, 0)
            );

            if (updateCount > 0) {
                log.info("[{}] 更新主任务数据成功，taskId：{}", methodName, updateBO.getTaskId());
                return Result.SUCCESS(true);
            } else {
                log.error("[{}] 更新主任务数据失败，任务不存在，taskId：{}", methodName, updateBO.getTaskId());
                return Result.ERROR("任务不存在");
            }

        } catch (Exception e) {
            log.error("[{}] 更新主任务数据异常", methodName, e);
            return Result.ERROR("更新任务失败：" + e.getMessage());
        }
    }

    /**
     * 更新子任务数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateSubTask(VideoEditSubTaskUpdateBO updateBO) {
        String methodName = "updateSubTask";
        log.info("[{}] 开始更新子任务数据，subTaskId：{}，status：{}，outputVideoUrl：{}，apiJobId：{}，errorMsg：{}",
                methodName, updateBO.getSubTaskId(), updateBO.getStatus(), updateBO.getOutputVideoUrl(),
                updateBO.getApiJobId(), updateBO.getErrorMsg());

        try {
            if (StringUtils.isBlank(updateBO.getSubTaskId())) {
                log.error("[{}] 子任务ID为空", methodName);
                return Result.ERROR("子任务ID不能为空");
            }

            VideoEditTaskItemPO updatePO = new VideoEditTaskItemPO();
            updatePO.setUpdateTime(new Date());

            if (updateBO.getStatus() != null) {
                updatePO.setStatus(updateBO.getStatus());
            }
            if (StringUtils.isNotBlank(updateBO.getOutputVideoUrl())) {
                updatePO.setOutputVideoUrl(updateBO.getOutputVideoUrl());
            }
            if (updateBO.getProgress() != null) {
                updatePO.setProgress(updateBO.getProgress());
            }

            if (updateBO.getOutputFileSize() != null) {
                updatePO.setOutputFileSize(updateBO.getOutputFileSize());
            }
            if (updateBO.getProcessingDuration() != null) {
                updatePO.setProcessingDuration(updateBO.getProcessingDuration());
            }
            if (StringUtils.isNotBlank(updateBO.getApiJobId())) {
                updatePO.setApiJobId(updateBO.getApiJobId());
            }
            if (updateBO.getCost() != null) {
                updatePO.setCost(updateBO.getCost());
            }
            if (StringUtils.isNotBlank(updateBO.getErrorMsg())) {
                updatePO.setErrorMsg(updateBO.getErrorMsg());
            }

            int updateCount = taskItemMapper.update(updatePO,
                    new LambdaQueryWrapper<VideoEditTaskItemPO>()
                            .eq(VideoEditTaskItemPO::getSubTaskId, updateBO.getSubTaskId())
                            .eq(VideoEditTaskItemPO::getIsDeleted, 0)
            );

            if (updateCount > 0) {
                log.info("[{}] 更新子任务数据成功，subTaskId：{}", methodName, updateBO.getSubTaskId());
                return Result.SUCCESS(true);
            } else {
                log.error("[{}] 更新子任务数据失败，子任务不存在，subTaskId：{}", methodName, updateBO.getSubTaskId());
                return Result.ERROR("子任务不存在");
            }

        } catch (Exception e) {
            log.error("[{}] 更新子任务数据异常", methodName, e);
            return Result.ERROR("更新子任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户的进行中和成功的任务列表
     */
    @Override
    public List<VideoEditTaskStatusVO> getActiveTaskList(String userId) {
        String methodName = "getActiveTaskList";
        log.info("[{}] 开始查询用户活跃任务列表，userId：{}", methodName, userId);

        try {
            if (StringUtils.isBlank(userId)) {
                log.warn("[{}] 用户ID为空", methodName);
                return List.of();
            }

            // 查询进行中和成功的任务
            List<VideoEditTaskPO> taskList = taskMapper.selectList(
                    new LambdaQueryWrapper<VideoEditTaskPO>()
                            .eq(VideoEditTaskPO::getUserId, userId)
                            .in(VideoEditTaskPO::getStatus,
                                VideoEditTaskStatusEnum.IN_PROGRESS.getValue(),
                                VideoEditTaskStatusEnum.PROCESSING.getValue(),
                                VideoEditTaskStatusEnum.SUCCESS.getValue())
                            .eq(VideoEditTaskPO::getIsDeleted, 0)
                            .orderByDesc(VideoEditTaskPO::getCreatedTime)
            );

            if (taskList.isEmpty()) {
                log.info("[{}] 用户无活跃任务，userId：{}", methodName, userId);
                return List.of();
            }

            // 批量转换为StatusVO对象（优化性能，避免N+1查询）
            List<VideoEditTaskStatusVO> statusVOList = convertToStatusVOListOptimized(taskList);

            log.info("[{}] 查询用户活跃任务列表成功，userId：{}，任务数量：{}", methodName, userId, statusVOList.size());
            return statusVOList;

        } catch (Exception e) {
            log.error("[{}] 查询用户活跃任务列表异常", methodName, e);
            return List.of();
        }
    }

    /**
     * 根据状态查询任务列表（用于异步处理）
     */
    @Override
    public Result<List<VideoEditTaskVO>> getTasksByStatus(Integer status, Integer limit) {
        String methodName = "getTasksByStatus";
        log.info("[{}] 开始根据状态查询任务列表，status：{}，limit：{}", methodName, status, limit);

        try {
            if (status == null) {
                log.error("[{}] 状态参数为空", methodName);
                return Result.ERROR("状态参数不能为空");
            }

            List<VideoEditTaskPO> taskList = taskMapper.getTasksByStatus(status, limit != null ? limit : 50);

            // 转换为VO对象
            List<VideoEditTaskVO> voList = taskList.stream()
                    .map(this::convertToTaskVO)
                    .collect(Collectors.toList());

            log.info("[{}] 根据状态查询任务列表成功，status：{}，任务数量：{}", methodName, status, voList.size());
            return Result.SUCCESS(voList);

        } catch (Exception e) {
            log.error("[{}] 根据状态查询任务列表异常", methodName, e);
            return Result.ERROR("查询任务列表失败：" + e.getMessage());
        }
    }

    /**
     * 统计用户任务数量
     */
    @Override
    public Result<Integer> countUserTasks(String userId, Integer status) {
        String methodName = "countUserTasks";
        log.info("[{}] 开始统计用户任务数量，userId：{}，status：{}", methodName, userId, status);

        try {
            if (StringUtils.isBlank(userId)) {
                log.error("[{}] 用户ID为空", methodName);
                return Result.ERROR("用户ID不能为空");
            }

            Integer count = taskMapper.countTasksByUser(userId, status);

            log.info("[{}] 统计用户任务数量成功，userId：{}，status：{}，count：{}", methodName, userId, status, count);
            return Result.SUCCESS(count);

        } catch (Exception e) {
            log.error("[{}] 统计用户任务数量异常", methodName, e);
            return Result.ERROR("统计任务数量失败：" + e.getMessage());
        }
    }

    /**
     * 转换PO为TaskVO
     */
    private VideoEditTaskVO convertToTaskVO(VideoEditTaskPO taskPO) {
        VideoEditTaskVO taskVO = new VideoEditTaskVO();
        BeanUtils.copyProperties(taskPO, taskVO);

        // 转换时间类型
        if (taskPO.getCreatedTime() != null) {
            taskVO.setCreatedTime(taskPO.getCreatedTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (taskPO.getStartTime() != null) {
            taskVO.setStartTime(taskPO.getStartTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (taskPO.getFinishTime() != null) {
            taskVO.setFinishTime(taskPO.getFinishTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }

        return taskVO;
    }

    /**
     * 转换PO为ItemVO
     */
    private VideoEditTaskItemVO convertToItemVO(VideoEditTaskItemPO itemPO) {
        VideoEditTaskItemVO itemVO = new VideoEditTaskItemVO();
        BeanUtils.copyProperties(itemPO, itemVO);

        // 转换时间类型
        if (itemPO.getCreatedTime() != null) {
            itemVO.setCreatedTime(itemPO.getCreatedTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (itemPO.getProcessStartTime() != null) {
            itemVO.setProcessStartTime(itemPO.getProcessStartTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (itemPO.getProcessEndTime() != null) {
            itemVO.setProcessEndTime(itemPO.getProcessEndTime().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }

        return itemVO;
    }

    /**
     * 批量转换PO为StatusVO（优化版本，避免N+1查询）
     */
    private List<VideoEditTaskStatusVO> convertToStatusVOListOptimized(List<VideoEditTaskPO> taskList) {
        if (taskList == null || taskList.isEmpty()) {
            return List.of();
        }

        try {
            // 提取所有任务ID
            List<String> taskIds = taskList.stream()
                    .map(VideoEditTaskPO::getTaskId)
                    .collect(Collectors.toList());

            // 批量查询所有子任务
            Map<String, List<VideoEditTaskItemPO>> subTaskMap = batchGetSubTasksByTaskIds(taskIds);

            // 转换为StatusVO对象
            return taskList.stream()
                    .map(taskPO -> convertToStatusVOWithSubTasks(taskPO, subTaskMap.get(taskPO.getTaskId())))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("批量转换StatusVO异常", e);
            // 降级处理：使用原有的单个转换方法
            return taskList.stream()
                    .map(this::convertToStatusVO)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 批量查询子任务，按任务ID分组
     */
    private Map<String, List<VideoEditTaskItemPO>> batchGetSubTasksByTaskIds(List<String> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return Map.of();
        }

        try {
            // 批量查询所有子任务
            List<VideoEditTaskItemPO> allSubTasks = taskItemMapper.selectList(
                    new LambdaQueryWrapper<VideoEditTaskItemPO>()
                            .in(VideoEditTaskItemPO::getTaskId, taskIds)
                            .eq(VideoEditTaskItemPO::getIsDeleted, 0)
                            .orderByAsc(VideoEditTaskItemPO::getSequence)
            );

            // 按任务ID分组
            return allSubTasks.stream()
                    .collect(Collectors.groupingBy(VideoEditTaskItemPO::getTaskId));

        } catch (Exception e) {
            log.error("批量查询子任务异常，taskIds：{}", taskIds, e);
            return Map.of();
        }
    }

    /**
     * 转换PO为StatusVO（带子任务信息）
     */
    private VideoEditTaskStatusVO convertToStatusVOWithSubTasks(VideoEditTaskPO taskPO,
                                                               List<VideoEditTaskItemPO> subTaskList) {
        VideoEditTaskStatusVO statusVO = new VideoEditTaskStatusVO();
        BeanUtils.copyProperties(taskPO, statusVO);

        // 设置状态描述
        if (taskPO.getStatus() != null) {
            VideoEditTaskStatusEnum statusEnum = VideoEditTaskStatusEnum.getByValue(taskPO.getStatus());
            statusVO.setStatusDesc(statusEnum != null ? statusEnum.getDesc() : "未知状态");
        }

        // 填充子任务信息
        fillSubTaskInfoWithData(statusVO, subTaskList != null ? subTaskList : List.of());

        return statusVO;
    }

    /**
     * 转换PO为StatusVO（单个任务，兼容原有逻辑）
     */
    private VideoEditTaskStatusVO convertToStatusVO(VideoEditTaskPO taskPO) {
        VideoEditTaskStatusVO statusVO = new VideoEditTaskStatusVO();
        BeanUtils.copyProperties(taskPO, statusVO);

        // 设置状态描述
        if (taskPO.getStatus() != null) {
            VideoEditTaskStatusEnum statusEnum = VideoEditTaskStatusEnum.getByValue(taskPO.getStatus());
            statusVO.setStatusDesc(statusEnum != null ? statusEnum.getDesc() : "未知状态");
        }

        // 填充子任务信息
        fillSubTaskInfo(statusVO, taskPO.getTaskId());

        return statusVO;
    }

    /**
     * 填充子任务信息到StatusVO（使用已有数据，避免重复查询）
     */
    private void fillSubTaskInfoWithData(VideoEditTaskStatusVO statusVO, List<VideoEditTaskItemPO> subTaskList) {
        try {
            // 数据校验
            if (subTaskList == null) {
                subTaskList = List.of();
            }

            // 查询主任务信息（用于获取封面URL）
            VideoEditTaskPO mainTaskPO = taskMapper.selectOne(
                new LambdaQueryWrapper<VideoEditTaskPO>()
                    .eq(VideoEditTaskPO::getTaskId, statusVO.getTaskId())
                    .eq(VideoEditTaskPO::getIsDeleted, 0)
            );

            // 转换为带封面信息的VO对象
            List<VideoEditTaskItemWithCoverVO> subTaskVOList = subTaskList.stream()
                    .map(subTaskPO -> convertToItemWithCoverVO(subTaskPO, mainTaskPO))
                    .collect(Collectors.toList());

            // 设置子任务列表
            statusVO.setSubTasks(subTaskVOList);

            // 统计各状态子任务数量
            Map<Integer, Long> statusCountMap = subTaskList.stream()
                    .collect(Collectors.groupingBy(
                            VideoEditTaskItemPO::getStatus,
                            Collectors.counting()
                    ));

            // 计算各状态统计
            statusVO.setPendingSubTaskCount(calculateSubTaskCountByStatus(statusCountMap,
                    VideoEditTaskItemStatusEnum.PENDING));
            statusVO.setProcessingSubTaskCount(calculateProcessingSubTaskCount(statusCountMap));
            statusVO.setSuccessSubTaskCount(calculateSubTaskCountByStatus(statusCountMap,
                    VideoEditTaskItemStatusEnum.SUCCESS));
            statusVO.setFailedSubTaskCount(calculateFailedSubTaskCount(statusCountMap));
            statusVO.setTotalSubTaskCount(subTaskList.size());

        } catch (Exception e) {
            log.error("填充子任务信息异常", e);
            // 设置默认值，避免返回null
            setDefaultSubTaskInfo(statusVO);
        }
    }

    /**
     * 填充子任务信息到StatusVO（通过任务ID查询）
     */
    private void fillSubTaskInfo(VideoEditTaskStatusVO statusVO, String taskId) {
        try {
            // 查询所有子任务
            List<VideoEditTaskItemPO> subTaskList = getTaskItemsByTaskId(taskId);
            fillSubTaskInfoWithData(statusVO, subTaskList);

        } catch (Exception e) {
            log.error("填充子任务信息异常，taskId：{}", taskId, e);
            setDefaultSubTaskInfo(statusVO);
        }
    }

    /**
     * 设置默认的子任务信息
     */
    private void setDefaultSubTaskInfo(VideoEditTaskStatusVO statusVO) {
        statusVO.setSubTasks(List.of());
        statusVO.setPendingSubTaskCount(0);
        statusVO.setProcessingSubTaskCount(0);
        statusVO.setSuccessSubTaskCount(0);
        statusVO.setFailedSubTaskCount(0);
        statusVO.setTotalSubTaskCount(0);
    }

    /**
     * 计算指定状态的子任务数量
     */
    private Integer calculateSubTaskCountByStatus(Map<Integer, Long> statusCountMap,
                                                VideoEditTaskItemStatusEnum statusEnum) {
        return statusCountMap.getOrDefault(statusEnum.getValue(), 0L).intValue();
    }

    /**
     * 计算处理中状态的子任务数量（包括准备中、处理中、验证中、上传中）
     */
    private Integer calculateProcessingSubTaskCount(Map<Integer, Long> statusCountMap) {
        return statusCountMap.entrySet().stream()
                .filter(entry -> {
                    VideoEditTaskItemStatusEnum statusEnum = VideoEditTaskItemStatusEnum.getByValue(entry.getKey());
                    return statusEnum != null && statusEnum.isProcessing();
                })
                .mapToInt(entry -> entry.getValue().intValue())
                .sum();
    }

    /**
     * 计算失败状态的子任务数量（包括各种失败状态）
     */
    private Integer calculateFailedSubTaskCount(Map<Integer, Long> statusCountMap) {
        return statusCountMap.entrySet().stream()
                .filter(entry -> {
                    VideoEditTaskItemStatusEnum statusEnum = VideoEditTaskItemStatusEnum.getByValue(entry.getKey());
                    return statusEnum != null && statusEnum.isFailed();
                })
                .mapToInt(entry -> entry.getValue().intValue())
                .sum();
    }

    /**
     * 根据任务ID获取子任务列表
     */
    @Override
    public List<VideoEditTaskItemPO> getTaskItemsByTaskId(String taskId) {
        return taskItemMapper.selectList(
                new LambdaQueryWrapper<VideoEditTaskItemPO>()
                        .eq(VideoEditTaskItemPO::getTaskId, taskId)
                        .eq(VideoEditTaskItemPO::getIsDeleted, 0)
                        .orderByAsc(VideoEditTaskItemPO::getSequence)
        );
    }

    /**
     * 获取用户活跃任务列表（支持分页）
     */
    @Override
    public Result<List<VideoEditTaskStatusVO>> getActiveTaskListPaged(String userId, Integer pageNum, Integer pageSize, Boolean includeSubTasks) {
        String methodName = "getActiveTaskListPaged";
        log.info("[{}] 开始查询用户活跃任务列表（分页），userId：{}，pageNum：{}，pageSize：{}，includeSubTasks：{}",
                methodName, userId, pageNum, pageSize, includeSubTasks);

        try {
            if (StringUtils.isBlank(userId)) {
                log.error("[{}] 用户ID为空", methodName);
                return Result.ERROR("用户ID不能为空");
            }

            // 参数校验和默认值设置
            int currentPage = pageNum != null && pageNum > 0 ? pageNum : 1;
            int currentSize = pageSize != null && pageSize > 0 ? pageSize : 10;
            boolean needSubTasks = includeSubTasks != null ? includeSubTasks : true;

            // 计算偏移量
            int offset = (currentPage - 1) * currentSize;

            // 查询进行中和成功的任务（分页）
            List<VideoEditTaskPO> taskList = taskMapper.selectList(
                    new LambdaQueryWrapper<VideoEditTaskPO>()
                            .eq(VideoEditTaskPO::getUserId, userId)
                            .in(VideoEditTaskPO::getStatus,
                                VideoEditTaskStatusEnum.IN_PROGRESS.getValue(),
                                VideoEditTaskStatusEnum.PROCESSING.getValue(),
                                VideoEditTaskStatusEnum.SUCCESS.getValue())
                            .eq(VideoEditTaskPO::getIsDeleted, 0)
                            .orderByDesc(VideoEditTaskPO::getCreatedTime)
                            .last("LIMIT " + offset + ", " + currentSize)
            );

            if (taskList.isEmpty()) {
                log.info("[{}] 用户无活跃任务，userId：{}", methodName, userId);
                return Result.SUCCESS(List.of());
            }

            List<VideoEditTaskStatusVO> statusVOList;
            if (needSubTasks) {
                // 包含子任务信息
                statusVOList = convertToStatusVOListOptimized(taskList);
            } else {
                // 不包含子任务信息，只返回主任务状态
                statusVOList = taskList.stream()
                        .map(this::convertToStatusVOWithoutSubTasks)
                        .collect(Collectors.toList());
            }

            log.info("[{}] 查询用户活跃任务列表成功（分页），userId：{}，任务数量：{}", methodName, userId, statusVOList.size());
            return Result.SUCCESS(statusVOList);

        } catch (Exception e) {
            log.error("[{}] 查询用户活跃任务列表异常（分页）", methodName, e);
            return Result.ERROR("查询任务列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务状态（支持子任务分页）
     */
    @Override
    public Result<VideoEditTaskStatusVO> getTaskStatusWithPagedSubTasks(String taskId, Integer subTaskPageNum, Integer subTaskPageSize) {
        String methodName = "getTaskStatusWithPagedSubTasks";
        log.info("[{}] 开始查询任务状态（子任务分页），taskId：{}，subTaskPageNum：{}，subTaskPageSize：{}",
                methodName, taskId, subTaskPageNum, subTaskPageSize);

        try {
            if (StringUtils.isBlank(taskId)) {
                log.error("[{}] 任务ID为空", methodName);
                return Result.ERROR("任务ID不能为空");
            }

            // 查询主任务
            VideoEditTaskPO taskPO = taskMapper.selectOne(
                    new LambdaQueryWrapper<VideoEditTaskPO>()
                            .eq(VideoEditTaskPO::getTaskId, taskId)
                            .eq(VideoEditTaskPO::getIsDeleted, 0)
            );

            if (taskPO == null) {
                log.warn("[{}] 任务不存在，taskId：{}", methodName, taskId);
                return Result.ERROR("任务不存在");
            }

            // 转换为StatusVO
            VideoEditTaskStatusVO statusVO = new VideoEditTaskStatusVO();
            BeanUtils.copyProperties(taskPO, statusVO);

            // 设置状态描述
            if (taskPO.getStatus() != null) {
                VideoEditTaskStatusEnum statusEnum = VideoEditTaskStatusEnum.getByValue(taskPO.getStatus());
                statusVO.setStatusDesc(statusEnum != null ? statusEnum.getDesc() : "未知状态");
            }

            // 分页查询子任务
            fillSubTaskInfoPaged(statusVO, taskId, subTaskPageNum, subTaskPageSize);

            log.info("[{}] 查询任务状态成功（子任务分页），taskId：{}", methodName, taskId);
            return Result.SUCCESS(statusVO);

        } catch (Exception e) {
            log.error("[{}] 查询任务状态异常（子任务分页）", methodName, e);
            return Result.ERROR("查询任务状态失败：" + e.getMessage());
        }
    }

    /**
     * 转换PO为StatusVO（不包含子任务信息，用于性能优化）
     */
    private VideoEditTaskStatusVO convertToStatusVOWithoutSubTasks(VideoEditTaskPO taskPO) {
        VideoEditTaskStatusVO statusVO = new VideoEditTaskStatusVO();
        BeanUtils.copyProperties(taskPO, statusVO);

        // 设置状态描述
        if (taskPO.getStatus() != null) {
            VideoEditTaskStatusEnum statusEnum = VideoEditTaskStatusEnum.getByValue(taskPO.getStatus());
            statusVO.setStatusDesc(statusEnum != null ? statusEnum.getDesc() : "未知状态");
        }

        // 设置默认的子任务信息（不查询具体子任务）
        setDefaultSubTaskInfo(statusVO);

        return statusVO;
    }

    /**
     * 分页填充子任务信息
     */
    private void fillSubTaskInfoPaged(VideoEditTaskStatusVO statusVO, String taskId, Integer pageNum, Integer pageSize) {
        try {
            // 参数校验和默认值设置
            int currentPage = pageNum != null && pageNum > 0 ? pageNum : 1;
            int currentSize = pageSize != null && pageSize > 0 ? pageSize : 20;

            // 计算偏移量
            int offset = (currentPage - 1) * currentSize;

            // 分页查询子任务
            List<VideoEditTaskItemPO> subTaskList = taskItemMapper.selectList(
                    new LambdaQueryWrapper<VideoEditTaskItemPO>()
                            .eq(VideoEditTaskItemPO::getTaskId, taskId)
                            .eq(VideoEditTaskItemPO::getIsDeleted, 0)
                            .orderByAsc(VideoEditTaskItemPO::getSequence)
                            .last("LIMIT " + offset + ", " + currentSize)
            );

            // 查询总的子任务统计信息（不分页）
            List<VideoEditTaskItemPO> allSubTasks = getTaskItemsByTaskId(taskId);

            // 查询主任务信息（用于获取封面URL）
            VideoEditTaskPO mainTaskPO = taskMapper.selectOne(
                new LambdaQueryWrapper<VideoEditTaskPO>()
                    .eq(VideoEditTaskPO::getTaskId, taskId)
                    .eq(VideoEditTaskPO::getIsDeleted, 0)
            );

            // 转换分页的子任务为带封面信息的VO
            List<VideoEditTaskItemWithCoverVO> subTaskVOList = subTaskList.stream()
                    .map(subTaskPO -> convertToItemWithCoverVO(subTaskPO, mainTaskPO))
                    .collect(Collectors.toList());

            // 设置分页的子任务列表
            statusVO.setSubTasks(subTaskVOList);

            // 统计所有子任务的状态（基于全量数据）
            Map<Integer, Long> statusCountMap = allSubTasks.stream()
                    .collect(Collectors.groupingBy(
                            VideoEditTaskItemPO::getStatus,
                            Collectors.counting()
                    ));

            // 计算各状态统计
            statusVO.setPendingSubTaskCount(calculateSubTaskCountByStatus(statusCountMap,
                    VideoEditTaskItemStatusEnum.PENDING));
            statusVO.setProcessingSubTaskCount(calculateProcessingSubTaskCount(statusCountMap));
            statusVO.setSuccessSubTaskCount(calculateSubTaskCountByStatus(statusCountMap,
                    VideoEditTaskItemStatusEnum.SUCCESS));
            statusVO.setFailedSubTaskCount(calculateFailedSubTaskCount(statusCountMap));
            statusVO.setTotalSubTaskCount(allSubTasks.size());

        } catch (Exception e) {
            log.error("分页填充子任务信息异常，taskId：{}", taskId, e);
            setDefaultSubTaskInfo(statusVO);
        }
    }

    /**
     * 获取进行中的子任务列表
     * 用于定时任务轮询状态检查
     */
    @Override
    public List<VideoEditTaskItemPO> getInProgressTaskItems() {
        String methodName = "getInProgressTaskItems";

        try {
            // 查询需要轮询状态的子任务（准备中和处理中状态）
            List<VideoEditTaskItemPO> inProgressItems = taskItemMapper.selectList(
                new LambdaQueryWrapper<VideoEditTaskItemPO>()
                    .in(VideoEditTaskItemPO::getStatus,
                        VideoEditTaskItemStatusEnum.PREPARING.getValue(),
                        VideoEditTaskItemStatusEnum.PROCESSING.getValue())
                    .eq(VideoEditTaskItemPO::getIsDeleted, 0)
                    .isNotNull(VideoEditTaskItemPO::getApiJobId)
                    .ne(VideoEditTaskItemPO::getApiJobId, "")
                    .orderByAsc(VideoEditTaskItemPO::getCreatedTime)
                    .last("LIMIT 50") // 限制数量，避免一次处理太多
            );

            log.debug("[{}] 查询到{}个进行中的子任务", methodName, inProgressItems.size());
            return inProgressItems;

        } catch (Exception e) {
            log.error("[{}] 查询进行中的子任务异常", methodName, e);
            return List.of();
        }
    }

    /**
     * 获取带主任务封面信息的子任务详情
     */
    @Override
    public Result<VideoEditTaskItemWithCoverVO> getSubTaskWithCover(String subTaskId) {
        String methodName = "getSubTaskWithCover";

        try {
            if (StringUtils.isBlank(subTaskId)) {
                return Result.ERROR("子任务ID不能为空");
            }

            // 查询子任务信息
            VideoEditTaskItemPO subTaskPO = taskItemMapper.selectOne(
                new LambdaQueryWrapper<VideoEditTaskItemPO>()
                    .eq(VideoEditTaskItemPO::getSubTaskId, subTaskId)
                    .eq(VideoEditTaskItemPO::getIsDeleted, 0)
            );

            if (subTaskPO == null) {
                return Result.ERROR("子任务不存在");
            }

            // 查询主任务信息
            VideoEditTaskPO mainTaskPO = taskMapper.selectOne(
                new LambdaQueryWrapper<VideoEditTaskPO>()
                    .eq(VideoEditTaskPO::getTaskId, subTaskPO.getTaskId())
                    .eq(VideoEditTaskPO::getIsDeleted, 0)
            );

            if (mainTaskPO == null) {
                return Result.ERROR("主任务不存在");
            }

            // 转换为VO
            VideoEditTaskItemWithCoverVO vo = convertToItemWithCoverVO(subTaskPO, mainTaskPO);
            return Result.SUCCESS(vo);

        } catch (Exception e) {
            log.error("[{}] 获取带封面信息的子任务详情异常，subTaskId：{}", methodName, subTaskId, e);
            return Result.ERROR("获取子任务详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据任务ID获取所有带主任务封面信息的子任务列表
     */
    @Override
    public Result<List<VideoEditTaskItemWithCoverVO>> getSubTasksWithCover(String taskId) {
        String methodName = "getSubTasksWithCover";

        try {
            if (StringUtils.isBlank(taskId)) {
                return Result.ERROR("任务ID不能为空");
            }

            // 查询主任务信息
            VideoEditTaskPO mainTaskPO = taskMapper.selectOne(
                new LambdaQueryWrapper<VideoEditTaskPO>()
                    .eq(VideoEditTaskPO::getTaskId, taskId)
                    .eq(VideoEditTaskPO::getIsDeleted, 0)
            );

            if (mainTaskPO == null) {
                return Result.ERROR("主任务不存在");
            }

            // 查询所有子任务
            List<VideoEditTaskItemPO> subTaskList = taskItemMapper.selectList(
                new LambdaQueryWrapper<VideoEditTaskItemPO>()
                    .eq(VideoEditTaskItemPO::getTaskId, taskId)
                    .eq(VideoEditTaskItemPO::getIsDeleted, 0)
                    .orderByAsc(VideoEditTaskItemPO::getSequence)
            );

            // 转换为VO列表
            List<VideoEditTaskItemWithCoverVO> voList = subTaskList.stream()
                .map(subTaskPO -> convertToItemWithCoverVO(subTaskPO, mainTaskPO))
                .collect(Collectors.toList());

            return Result.SUCCESS(voList);

        } catch (Exception e) {
            log.error("[{}] 获取带封面信息的子任务列表异常，taskId：{}", methodName, taskId, e);
            return Result.ERROR("获取子任务列表失败：" + e.getMessage());
        }
    }

    /**
     * 转换为带封面信息的子任务VO
     */
    private VideoEditTaskItemWithCoverVO convertToItemWithCoverVO(VideoEditTaskItemPO subTaskPO, VideoEditTaskPO mainTaskPO) {
        VideoEditTaskItemWithCoverVO vo = new VideoEditTaskItemWithCoverVO();

        // 复制子任务信息
        BeanUtils.copyProperties(subTaskPO, vo);

        // 设置状态描述
        if (subTaskPO.getStatus() != null) {
            VideoEditTaskItemStatusEnum statusEnum = VideoEditTaskItemStatusEnum.getByValue(subTaskPO.getStatus());
            vo.setStatusDesc(statusEnum != null ? statusEnum.getDesc() : "未知状态");
        }

        // 设置主任务信息
        vo.setTaskId(mainTaskPO.getTaskId());
        vo.setOutputCoverUrl(mainTaskPO.getOutputCoverUrl());
        vo.setMainTaskStatus(mainTaskPO.getStatus());

        // 设置主任务状态描述
        if (mainTaskPO.getStatus() != null) {
            VideoEditTaskStatusEnum mainStatusEnum = VideoEditTaskStatusEnum.getByValue(mainTaskPO.getStatus());
            vo.setMainTaskStatusDesc(mainStatusEnum != null ? mainStatusEnum.getDesc() : "未知状态");
        }

        // 转换Date到LocalDateTime
        if (mainTaskPO.getCreatedTime() != null) {
            vo.setMainTaskCreatedTime(mainTaskPO.getCreatedTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (mainTaskPO.getUpdateTime() != null) {
            vo.setMainTaskUpdateTime(mainTaskPO.getUpdateTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }

        return vo;
    }
}
