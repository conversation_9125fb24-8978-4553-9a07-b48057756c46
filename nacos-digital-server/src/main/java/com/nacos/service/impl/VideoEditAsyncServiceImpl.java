package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.business.message.BMessageSendEnum;
import com.nacos.utils.DigitalFileUtil;
import com.business.message.mq.BRedisServiceUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.nacos.entity.bo.VideoEditTaskStatusUpdateBO;
import com.nacos.entity.bo.VideoEditSubTaskUpdateBO;
import com.nacos.entity.enums.VideoEditTaskStatusEnum;
import com.nacos.entity.enums.VideoEditTaskItemStatusEnum;
import com.nacos.entity.po.VideoEditTaskItemPO;
import com.nacos.entity.po.VideoEditTaskPO;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.mapper.VideoEditTaskMapper;
import com.nacos.mapper.VideoEditTaskItemMapper;
import com.nacos.mapper.DigitalAudioTaskMapper;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.result.Result;
import com.nacos.service.FeiyongService;
import com.nacos.service.VideoEditAsyncService;
import com.nacos.service.VideoEditTaskService;
import com.nacos.service.processor.VideoEditProcessor;
import com.nacos.service.processor.VideoEditProcessorFactory;
import com.nacos.utils.DigitalFileUtil;
import com.nacos.utils.MessageSendUtil;
import org.apache.commons.lang3.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 视频编辑异步处理Service实现类
 * 参考DigitalVideoAsyncServiceImpl的设计模式
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VideoEditAsyncServiceImpl implements VideoEditAsyncService {

    private final VideoEditTaskMapper taskMapper;
    private final VideoEditTaskItemMapper taskItemMapper;
    private final VideoEditTaskService videoEditTaskService;
    private final DigitalAudioTaskMapper digitalAudioTaskMapper;
    private final VideoEditProcessorFactory processorFactory;
    private final FeiyongService feiyongService;

    // 轮询间隔（毫秒）
    private static final long POLL_INTERVAL = 10000; // 10秒
    // 最大重试次数
    private static final int MAX_RETRIES = 180; // 30分钟

    /**
     * 处理视频编辑任务的主入口方法
     * 
     * @param taskId    任务ID
     * @param userId    用户ID
     * @param taskItems 任务项列表
     */
    @Async("videoTaskExecutor")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processEditTasks(String taskId, String userId, List<VideoEditTaskItemPO> taskItems) {
        String methodName = "processEditTasks";
        log.info("[{}] 开始处理视频编辑任务，taskId：{}, userId: {}", methodName, taskId, userId);
        long logId = IdWorker.getId();
        boolean koufeiResult = false;
        int multiplier = 1;

        try {
            // 1. 验证任务状态
            VideoEditTaskPO mainTask = taskMapper.selectOne(
                    new LambdaQueryWrapper<VideoEditTaskPO>()
                            .eq(VideoEditTaskPO::getTaskId, taskId)
                            .eq(VideoEditTaskPO::getStatus, VideoEditTaskStatusEnum.IN_PROGRESS.getValue())
                            .eq(VideoEditTaskPO::getIsDeleted, 0));

            if (mainTask == null) {
                log.error("[{}] 主任务不存在或状态不正确：taskId={}", methodName, taskId);
                return;
            }

            // 2. 获取子任务列表（如果传入的taskItems为空，则从数据库查询）
            List<VideoEditTaskItemPO> actualTaskItems = taskItems;
            if (actualTaskItems == null || actualTaskItems.isEmpty()) {
                actualTaskItems = taskItemMapper.selectList(
                        new LambdaQueryWrapper<VideoEditTaskItemPO>()
                                .eq(VideoEditTaskItemPO::getTaskId, taskId)
                                .eq(VideoEditTaskItemPO::getIsDeleted, 0)
                                .orderByAsc(VideoEditTaskItemPO::getSequence));
            }

            if (actualTaskItems.isEmpty()) {
                log.error("[{}] 没有找到子任务：taskId={}", methodName, taskId);
                return;
            }

            // 3. 处理每个子任务
            boolean hasError = false;
            for (VideoEditTaskItemPO subTask : actualTaskItems) {
                try {
                    // 检查子任务状态
                    if (subTask.getStatus() != VideoEditTaskItemStatusEnum.PENDING.getValue()) {
                        log.error("[{}] 子任务状态不正确：subTaskId={}，子任务状态：{}",
                                methodName, subTask.getSubTaskId(),
                                VideoEditTaskItemStatusEnum.getDesc(subTask.getStatus()));
                        continue;
                    }
                    // 处理子任务
                    processSubTask(subTask, userId);
                } catch (Exception e) {
                    String errorMsg = "处理子任务异常：" + e.getMessage();
                    log.error("[{}] {}：subTaskId={}", methodName, errorMsg, subTask.getSubTaskId(), e);

                    // 更新子任务状态
                    videoEditTaskService.updateSubTask(VideoEditSubTaskUpdateBO.builder()
                            .subTaskId(subTask.getSubTaskId())
                            .status(VideoEditTaskItemStatusEnum.FAILED.getValue())
                            .errorMsg(errorMsg)
                            .build());
                    hasError = true;
                }
            }

            // 3. 检查是否有错误
            if (hasError) {
                log.error("[{}] 存在子任务处理失败，任务ID：{}", methodName, taskId);
                videoEditTaskService.updateTask(VideoEditTaskStatusUpdateBO.builder()
                        .taskId(taskId)
                        .status(VideoEditTaskStatusEnum.FAILED.getValue())
                        .errorMsg("部分子任务处理失败")
                        .build());
                pushTaskStatus(userId, taskId, VideoEditTaskStatusEnum.FAILED.getValue(), "联系客服处理");
                return;
            }

            // 4. 处理任务完成逻辑
            handleTaskCompletion(mainTask);

            // 5. 扣费（按音频时长计费 - 混合策略优化）
            multiplier = calculateCostMultiplier(taskId);
            log.info("[{}] 开始扣费，taskId：{}，按音频时长计费（混合策略），总分钟数：{}", methodName, taskId, multiplier);
            koufeiResult = feiyongService.koufei(Long.valueOf(userId), logId, "视频编辑扣费（按音频时长-混合策略）",
                    DDUseRuleEnum.VIDEO_EDIT_PER_MIN.getRedisKey(), multiplier);
            pushTaskStatus(userId, taskId, VideoEditTaskStatusEnum.SUCCESS.getValue(),
                    VideoEditTaskStatusEnum.SUCCESS.getDesc());

        } catch (Exception e) {
            if (koufeiResult) {
                // 如果已经扣费成功，则进行退费
                log.info("[{}] 开始退费，taskId：{}，按音频时长退费（混合策略），总分钟数：{}", methodName, taskId, multiplier);
                feiyongService.tuifei(Long.valueOf(userId), logId, "视频编辑退费（按音频时长-混合策略）",
                        DDUseRuleEnum.VIDEO_EDIT_PER_MIN.getRedisKey(), multiplier);
            }
            log.error("[{}] 处理视频编辑任务异常", methodName, e);
            videoEditTaskService.updateTask(VideoEditTaskStatusUpdateBO.builder()
                    .taskId(taskId)
                    .status(VideoEditTaskStatusEnum.FAILED.getValue())
                    .errorMsg("任务处理异常：" + e.getMessage())
                    .build());
            pushTaskStatus(userId, taskId, VideoEditTaskStatusEnum.FAILED.getValue(), "任务处理异常");
        }
    }

    /**
     * 处理单个子任务
     */
    private void processSubTask(VideoEditTaskItemPO subTask, String userId) {
        String methodName = "processSubTask";
        log.info("[{}] 开始处理子任务，subTaskId：{}", methodName, subTask.getSubTaskId());

        try {
            // 1. 更新子任务状态为准备中
            videoEditTaskService.updateSubTask(VideoEditSubTaskUpdateBO.builder()
                    .subTaskId(subTask.getSubTaskId())
                    .status(VideoEditTaskItemStatusEnum.PREPARING.getValue())
                    .build());

            // 2. 提交编辑操作
            Result<String> submitResult = submitEditOperation(subTask, userId);
            if (!submitResult.isSuccess()) {
                videoEditTaskService.updateSubTask(VideoEditSubTaskUpdateBO.builder()
                        .subTaskId(subTask.getSubTaskId())
                        .status(VideoEditTaskItemStatusEnum.FAILED.getValue())
                        .errorMsg("提交编辑操作失败：" + submitResult.getMessage())
                        .build());
                throw new RuntimeException("编辑操作失败：" + submitResult.getMessage());
            }

            // 3. 更新状态为处理中
            String apiJobId = submitResult.getData();
            videoEditTaskService.updateSubTask(VideoEditSubTaskUpdateBO.builder()
                    .subTaskId(subTask.getSubTaskId())
                    .status(VideoEditTaskItemStatusEnum.PROCESSING.getValue())
                    .apiJobId(apiJobId)
                    .build());

            // 推送状态处理中通知
            pushTaskStatus(userId, subTask.getSubTaskId(), VideoEditTaskItemStatusEnum.PROCESSING.getValue(), null);

            // 4. 轮询检查任务状态
            pollTaskStatus(apiJobId, subTask, userId);

        } catch (Exception e) {
            log.error("[{}] 处理子任务异常", methodName, e);
            throw e;
        }
    }

    /**
     * 提交单个编辑操作
     */
    @Override
    public Result<String> submitEditOperation(VideoEditTaskItemPO taskItemPO, String userId) {
        String methodName = "submitEditOperation";
        log.info("[{}] 提交数字人视频编辑操作，subTaskId：{}，avatarId：{}，voiceUrl：{}",
                methodName, taskItemPO.getSubTaskId(), taskItemPO.getAvatarId(), taskItemPO.getSourceVoiceUrl());

        try {
            // 使用标准数字人视频编辑处理器
            VideoEditProcessor processor = processorFactory.getProcessor("DigitalAvatarVideoProcessor");

            // 调用处理器处理，基于avatarId和voiceUrl
            Result<VideoEditProcessor.VideoEditResult> processResult = processor.process(taskItemPO,
                    taskItemPO.getTaskParameters());

            if (processResult.isSuccess() && processResult.getData() != null) {
                String apiJobId = processResult.getData().getApiJobId();
                log.info("[{}] 数字人视频编辑操作提交成功，subTaskId：{}，apiJobId：{}",
                        methodName, taskItemPO.getSubTaskId(), apiJobId);
                return Result.SUCCESS(apiJobId);
            } else {
                log.error("[{}] 数字人视频编辑操作提交失败，subTaskId：{}，错误：{}",
                        methodName, taskItemPO.getSubTaskId(), processResult.getMessage());
                return Result.ERROR("编辑操作提交失败：" + processResult.getMessage());
            }

        } catch (Exception e) {
            log.error("[{}] 提交数字人视频编辑操作异常", methodName, e);
            return Result.ERROR("提交编辑操作异常：" + e.getMessage());
        }
    }

    /**
     * 轮询检查任务状态
     */
    private void pollTaskStatus(String apiJobId, VideoEditTaskItemPO subTask, String userId) {
        String methodName = "pollTaskStatus";
        log.info("[{}] 开始轮询任务状态，apiJobId：{}", methodName, apiJobId);

        int retryCount = 0;
        while (retryCount < MAX_RETRIES) {
            try {
                Thread.sleep(POLL_INTERVAL);

                Result<String> checkResult = checkTaskStatus(apiJobId, subTask, userId);
                if (checkResult.isSuccess()) {
                    log.info("[{}] 任务状态检查完成，apiJobId：{}", methodName, apiJobId);
                    return;
                }

                retryCount++;
                log.debug("[{}] 任务仍在处理中，继续轮询，apiJobId：{}，重试次数：{}", methodName, apiJobId, retryCount);

            } catch (InterruptedException e) {
                log.error("[{}] 轮询被中断", methodName, e);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("[{}] 轮询异常", methodName, e);
                retryCount++;
            }
        }

        // 超时处理
        if (retryCount >= MAX_RETRIES) {
            log.error("[{}] 任务处理超时，apiJobId：{}", methodName, apiJobId);
            videoEditTaskService.updateSubTask(VideoEditSubTaskUpdateBO.builder()
                    .subTaskId(subTask.getSubTaskId())
                    .status(VideoEditTaskItemStatusEnum.TIMEOUT.getValue())
                    .errorMsg("任务处理超时")
                    .build());
        }
    }

    /**
     * 检查任务状态
     */
    @Override
    public Result<String> checkTaskStatus(String apiJobId, VideoEditTaskItemPO taskItemPO, String userId) {
        String methodName = "checkTaskStatus";
        log.info("[{}] 检查任务状态，apiJobId：{}", methodName, apiJobId);

        try {
            // 使用标准数字人视频编辑处理器
            VideoEditProcessor processor = processorFactory.getProcessor("DigitalAvatarVideoProcessor");

            // 调用处理器检查状态
            Result<VideoEditProcessor.VideoEditResult> statusResult = processor.checkStatus(apiJobId);

            if (statusResult.isSuccess() && statusResult.getData() != null) {
                VideoEditProcessor.VideoEditResult result = statusResult.getData();

                // 根据状态处理
                // 系统内部状态：0-待处理 1-准备中 2-处理中 3-处理成功 4-处理失败 5-处理超时 6-已取消 7-验证中 8-上传中 9-下载失败 10-上传失败
                switch (result.getStatus()) {
                    case 0: // 待处理
                    case 1: // 准备中
                    case 2: // 处理中
                        // 更新进度
                        if (result.getProgress() != null) {
                            videoEditTaskService.updateSubTask(VideoEditSubTaskUpdateBO.builder()
                                    .subTaskId(taskItemPO.getSubTaskId())
                                    .status(VideoEditTaskItemStatusEnum.PROCESSING.getValue()) // 处理中
                                    .progress(result.getProgress())
                                    .build());
                        }
                        return Result.ERROR("任务仍在处理中");

                    case 3: // 处理成功
                        // 先更新子任务状态为上传中
                        videoEditTaskService.updateSubTask(VideoEditSubTaskUpdateBO.builder()
                                .subTaskId(taskItemPO.getSubTaskId())
                                .status(VideoEditTaskItemStatusEnum.UPLOADING.getValue())
                                .progress(95)
                                .build());

                        // 上传视频到阿里云OSS
                        String ossVideoUrl = null;
                        try {
                            log.info("[{}] 开始上传视频到阿里云OSS，subTaskId：{}，原始URL：{}",
                                    methodName, taskItemPO.getSubTaskId(), result.getOutputVideoUrl());

                            // 使用DigitalFileUtil上传视频文件到OSS
                            // type=2表示生成的数字人视频临时目录
                            ossVideoUrl = DigitalFileUtil.uploadDigitalResource(
                                    result.getOutputVideoUrl(), // 源视频URL
                                    taskItemPO.getSubTaskId(), // 文件名采用子任务id
                                    userId, // 用户ID
                                    null, // 分组ID
                                    2, // 文件类型：生成的数字人视频临时目录
                                    false // 用户文件
                            );

                            if (StringUtils.isBlank(ossVideoUrl)) {
                                throw new RuntimeException("上传视频到OSS失败，返回路径为空");
                            }

                            log.info("[{}] 视频上传到OSS成功，subTaskId：{}，OSS URL：{}",
                                    methodName, taskItemPO.getSubTaskId(), ossVideoUrl);

                            // 截取视频封面
                            String coverUrl = null;
                            try {
                                coverUrl = DigitalFileUtil.extractVideoCover(ossVideoUrl,
                                        "cover_" + taskItemPO.getSubTaskId(),
                                        userId, null, 8, false);
                                if (StringUtils.isNotBlank(coverUrl)) {
                                    log.info("[{}] 截取视频封面成功，subTaskId：{}，coverUrl：{}",
                                            methodName, taskItemPO.getSubTaskId(), coverUrl);
                                } else {
                                    log.warn("[{}] 截取视频封面失败，subTaskId：{}", methodName, taskItemPO.getSubTaskId());
                                }
                            } catch (Exception coverException) {
                                log.error("[{}] 截取视频封面异常，subTaskId：{}，错误：{}",
                                        methodName, taskItemPO.getSubTaskId(), coverException.getMessage(), coverException);
                            }

                            // 上传成功后，更新子任务状态为完成
                            VideoEditSubTaskUpdateBO.VideoEditSubTaskUpdateBOBuilder updateBuilder = VideoEditSubTaskUpdateBO.builder()
                                    .subTaskId(taskItemPO.getSubTaskId())
                                    .status(VideoEditTaskItemStatusEnum.SUCCESS.getValue())
                                    .outputVideoUrl(ossVideoUrl) // 使用OSS URL
                                    .progress(100)
                                    .outputFileSize(result.getOutputFileSize())
                                    .processingDuration(result.getProcessingDuration())
                                    .cost(result.getCost());

                            // 子任务不需要封面，直接更新任务状态
                            videoEditTaskService.updateSubTask(updateBuilder.build());

                            // 推送成功通知
                            pushTaskStatus(userId, taskItemPO.getSubTaskId(),
                                    VideoEditTaskItemStatusEnum.SUCCESS.getValue(), null);
                            return Result.SUCCESS("任务处理成功");

                        } catch (Exception uploadException) {
                            log.error("[{}] 上传视频到OSS失败，subTaskId：{}，错误：{}",
                                    methodName, taskItemPO.getSubTaskId(), uploadException.getMessage(),
                                    uploadException);

                            // 上传失败，更新子任务状态为上传失败
                            videoEditTaskService.updateSubTask(VideoEditSubTaskUpdateBO.builder()
                                    .subTaskId(taskItemPO.getSubTaskId())
                                    .status(VideoEditTaskItemStatusEnum.UPLOAD_FAILED.getValue())
                                    .errorMsg("视频上传到OSS失败：" + uploadException.getMessage())
                                    .build());

                            // 推送上传失败通知
                            pushTaskStatus(userId, taskItemPO.getSubTaskId(),
                                    VideoEditTaskItemStatusEnum.UPLOAD_FAILED.getValue(),
                                    "视频上传失败：" + uploadException.getMessage());
                            return Result.ERROR("视频上传失败：" + uploadException.getMessage());
                        }

                    case 4: // 处理失败
                        // 更新子任务状态
                        videoEditTaskService.updateSubTask(VideoEditSubTaskUpdateBO.builder()
                                .subTaskId(taskItemPO.getSubTaskId())
                                .status(VideoEditTaskItemStatusEnum.FAILED.getValue())
                                .errorMsg(result.getErrorMsg())
                                .build());

                        // 推送失败通知
                        pushTaskStatus(userId, taskItemPO.getSubTaskId(), VideoEditTaskItemStatusEnum.FAILED.getValue(),
                                result.getErrorMsg());
                        return Result.SUCCESS("任务处理失败");

                    default:
                        log.warn("[{}] 未知的任务状态：{}", methodName, result.getStatus());
                        return Result.ERROR("未知的任务状态");
                }
            } else {
                log.error("[{}] 状态检查失败，apiJobId：{}，错误：{}", methodName, apiJobId, statusResult.getMessage());
                return Result.ERROR("状态检查失败：" + statusResult.getMessage());
            }

        } catch (Exception e) {
            log.error("[{}] 检查任务状态异常", methodName, e);
            return Result.ERROR("检查任务状态异常：" + e.getMessage());
        }
    }

    /**
     * 处理任务完成逻辑
     */
    @Override
    public Result<String> handleTaskCompletion(VideoEditTaskPO mainTask) {
        String methodName = "handleTaskCompletion";
        log.info("[{}] 处理任务完成逻辑，taskId：{}", methodName, mainTask.getTaskId());

        try {
            // 查询所有子任务
            List<VideoEditTaskItemPO> subTasks = taskItemMapper.selectList(
                    new LambdaQueryWrapper<VideoEditTaskItemPO>()
                            .eq(VideoEditTaskItemPO::getTaskId, mainTask.getTaskId())
                            .eq(VideoEditTaskItemPO::getIsDeleted, 0));

            if (subTasks.isEmpty()) {
                log.error("[{}] 未找到子任务，taskId：{}", methodName, mainTask.getTaskId());
                return Result.ERROR("未找到子任务");
            }

            // 检查所有子任务是否完成
            boolean allCompleted = subTasks.stream()
                    .allMatch(subTask -> subTask.getStatus() == VideoEditTaskItemStatusEnum.SUCCESS.getValue());

            if (!allCompleted) {
                log.info("[{}] 还有子任务未完成，taskId：{}", methodName, mainTask.getTaskId());
                return Result.ERROR("还有子任务未完成");
            }

            // 统计成功和失败数量
            long successCount = subTasks.stream()
                    .filter(subTask -> subTask.getStatus() == VideoEditTaskItemStatusEnum.SUCCESS.getValue())
                    .count();

            long failedCount = subTasks.stream()
                    .filter(subTask -> subTask.getStatus() == VideoEditTaskItemStatusEnum.FAILED.getValue())
                    .count();

            // 获取第一个成功的输出视频作为主输出（如果有多个可以考虑合并）
            String outputVideoUrl = subTasks.stream()
                    .filter(subTask -> subTask.getStatus() == VideoEditTaskItemStatusEnum.SUCCESS.getValue())
                    .filter(subTask -> StringUtils.isNotBlank(subTask.getOutputVideoUrl()))
                    .map(VideoEditTaskItemPO::getOutputVideoUrl)
                    .findFirst()
                    .orElse(null);

            // 计算总费用
            Double totalCost = subTasks.stream()
                    .filter(subTask -> subTask.getCost() != null)
                    .mapToDouble(VideoEditTaskItemPO::getCost)
                    .sum();

            // 生成主任务封面URL
            String outputCoverUrl = null;
            if (StringUtils.isNotBlank(outputVideoUrl)) {
                try {
                    outputCoverUrl = DigitalFileUtil.extractVideoCover(outputVideoUrl,
                            "main_cover_" + mainTask.getTaskId(),
                            mainTask.getUserId(), null, 8, false);
                    if (StringUtils.isNotBlank(outputCoverUrl)) {
                        log.info("[{}] 生成主任务封面成功，taskId：{}，coverUrl：{}",
                                methodName, mainTask.getTaskId(), outputCoverUrl);
                    } else {
                        log.warn("[{}] 生成主任务封面失败，taskId：{}", methodName, mainTask.getTaskId());
                    }
                } catch (Exception coverException) {
                    log.error("[{}] 生成主任务封面异常，taskId：{}，错误：{}",
                            methodName, mainTask.getTaskId(), coverException.getMessage(), coverException);
                }
            }

            // 更新主任务状态
            videoEditTaskService.updateTask(VideoEditTaskStatusUpdateBO.builder()
                    .taskId(mainTask.getTaskId())
                    .status(VideoEditTaskStatusEnum.SUCCESS.getValue())
                    .outputVideoUrl(outputVideoUrl)
                    .outputCoverUrl(outputCoverUrl) // 🔑 关键：添加封面URL
                    .successCount((int) successCount)
                    .failedCount((int) failedCount)
                    .progress(100)
                    .cost(totalCost > 0 ? totalCost : null)
                    .build());

            log.info("[{}] 任务完成处理成功，taskId：{}，成功数：{}，失败数：{}",
                    methodName, mainTask.getTaskId(), successCount, failedCount);
            return Result.SUCCESS("任务完成处理成功");

        } catch (Exception e) {
            log.error("[{}] 处理任务完成逻辑异常", methodName, e);
            return Result.ERROR("处理任务完成逻辑异常：" + e.getMessage());
        }
    }

    /**
     * 计算编辑操作的费用乘数（按音频时长计费）
     */
    @Override
    public int calculateCostMultiplier(String taskId) {
        String methodName = "calculateCostMultiplier";
        log.info("[{}] 开始计算费用乘数（按音频时长），taskId：{}", methodName, taskId);

        try {
            // 统计所有子任务的音频总时长
            int totalMinutes = countTotalAudioLength(taskId);

            log.info("[{}] 费用乘数计算完成，taskId：{}，总音频时长：{}分钟", methodName, taskId, totalMinutes);
            return Math.max(totalMinutes, 1); // 至少为1分钟

        } catch (Exception e) {
            log.error("[{}] 计算费用乘数异常", methodName, e);
            return 1; // 异常时返回默认乘数
        }
    }

    /**
     * 统计所有子任务的音频总时长（分钟数，向上取整）
     * 使用混合策略：优先从digital_audio_task表获取，备选文件解析
     */
    private int countTotalAudioLength(String taskId) {
        String methodName = "countTotalAudioLength";
        log.info("[{}] 开始统计音频总时长（混合策略），taskId：{}", methodName, taskId);

        try {
            // 查询所有子任务
            List<VideoEditTaskItemPO> subTasks = taskItemMapper.selectList(
                    new LambdaQueryWrapper<VideoEditTaskItemPO>()
                            .eq(VideoEditTaskItemPO::getTaskId, taskId)
                            .eq(VideoEditTaskItemPO::getIsDeleted, 0));

            if (subTasks.isEmpty()) {
                log.warn("[{}] 未找到子任务，返回默认时长1分钟", methodName);
                return 1;
            }

            long totalLengthMs = 0;
            int processedCount = 0;
            int skippedCount = 0;
            int audioTaskHitCount = 0;  // digital_audio_task表命中次数
            int fileParseCount = 0;     // 文件解析次数

            for (VideoEditTaskItemPO subTask : subTasks) {
                try {
                    Long audioLength = subTask.getAudioLength();

                    // 如果数据库中已有音频时长，直接使用
                    if (audioLength != null && audioLength > 0) {
                        totalLengthMs += audioLength;
                        processedCount++;
                        log.debug("[{}] 子任务{}使用已存储的音频时长：{}ms", methodName, subTask.getSubTaskId(), audioLength);
                    } else {
                        // 如果没有音频时长，使用混合策略获取
                        String voiceUrl = subTask.getSourceVoiceUrl();
                        if (StringUtils.isNotBlank(voiceUrl)) {
                            // 先尝试从digital_audio_task表获取
                            Long audioTaskLength = getAudioLengthFromAudioTask(voiceUrl);
                            if (audioTaskLength != null && audioTaskLength > 0) {
                                totalLengthMs += audioTaskLength;
                                processedCount++;
                                audioTaskHitCount++;

                                // 更新数据库中的音频时长
                                updateSubTaskAudioLength(subTask.getSubTaskId(), audioTaskLength);
                                log.debug("[{}] 子任务{}从digital_audio_task表获取音频时长：{}ms", methodName, subTask.getSubTaskId(), audioTaskLength);
                            } else {
                                // 备选方案：文件解析
                                Long urlAudioLength = DigitalFileUtil.getAudioLengthByUrl(voiceUrl);
                                if (urlAudioLength != null && urlAudioLength > 0) {
                                    totalLengthMs += urlAudioLength;
                                    processedCount++;
                                    fileParseCount++;

                                    // 更新数据库中的音频时长
                                    updateSubTaskAudioLength(subTask.getSubTaskId(), urlAudioLength);
                                    log.debug("[{}] 子任务{}通过文件解析获取音频时长：{}ms", methodName, subTask.getSubTaskId(), urlAudioLength);
                                } else {
                                    // 无法获取音频时长，使用默认值（1分钟）
                                    long defaultLength = 60 * 1000; // 1分钟
                                    totalLengthMs += defaultLength;
                                    skippedCount++;
                                    log.warn("[{}] 子任务{}无法获取音频时长，使用默认值：{}ms", methodName, subTask.getSubTaskId(), defaultLength);
                                }
                            }
                        } else {
                            // 音频URL为空，使用默认值
                            long defaultLength = 60 * 1000; // 1分钟
                            totalLengthMs += defaultLength;
                            skippedCount++;
                            log.warn("[{}] 子任务{}音频URL为空，使用默认值：{}ms", methodName, subTask.getSubTaskId(), defaultLength);
                        }
                    }
                } catch (Exception e) {
                    // 单个子任务处理失败，使用默认值
                    long defaultLength = 60 * 1000; // 1分钟
                    totalLengthMs += defaultLength;
                    skippedCount++;
                    log.error("[{}] 处理子任务{}音频时长异常，使用默认值：{}ms", methodName, subTask.getSubTaskId(), defaultLength, e);
                }
            }

            // 转换为分钟数（向上取整）
            int totalMinutes = (int) Math.ceil(totalLengthMs / 1000.0 / 60);

            log.info("[{}] 音频时长统计完成（混合策略），taskId：{}，总时长：{}ms（{}分钟），" +
                    "处理成功：{}个，digital_audio_task命中：{}个，文件解析：{}个，使用默认值：{}个",
                    methodName, taskId, totalLengthMs, totalMinutes, processedCount, audioTaskHitCount, fileParseCount, skippedCount);

            return Math.max(totalMinutes, 1); // 至少为1分钟

        } catch (Exception e) {
            log.error("[{}] 统计音频总时长异常，taskId：{}", methodName, taskId, e);
            return 1; // 异常时返回默认值
        }
    }

    /**
     * 从音频URL获取音频时长（毫秒）- 混合策略优化版本
     * 优先从digital_audio_task表获取，失败时使用文件解析
     */
    private Long getAudioLengthFromUrl(String voiceUrl) {
        String methodName = "getAudioLengthFromUrl";
        try {
            if (StringUtils.isBlank(voiceUrl)) {
                return null;
            }

            log.debug("[{}] 开始获取音频时长（混合策略），URL：{}", methodName, voiceUrl);

            // 1. 优先从digital_audio_task表获取时长（快速、准确）
            Long durationFromAudioTask = getAudioLengthFromAudioTask(voiceUrl);
            if (durationFromAudioTask != null && durationFromAudioTask > 0) {
                log.info("[{}] 从digital_audio_task表获取音频时长成功，URL：{}，时长：{}ms",
                        methodName, voiceUrl, durationFromAudioTask);
                return durationFromAudioTask;
            }

            // 2. 备选方案：使用文件解析方式（兼容性保证）
            log.debug("[{}] digital_audio_task表中未找到音频时长，使用文件解析方式，URL：{}", methodName, voiceUrl);
            Long audioLength = DigitalFileUtil.getAudioLengthByUrl(voiceUrl);
            if (audioLength != null && audioLength > 0) {
                log.info("[{}] 文件解析获取音频时长成功，URL：{}，时长：{}ms", methodName, voiceUrl, audioLength);
                return audioLength;
            }

            log.warn("[{}] 所有方式都无法获取音频时长，URL：{}", methodName, voiceUrl);
            return null;
        } catch (Exception e) {
            log.error("[{}] 获取音频时长异常，URL：{}", methodName, voiceUrl, e);
            return null;
        }
    }

    /**
     * 从digital_audio_task表获取音频时长（毫秒）
     * 通过音频URL关联查询已生成的音频任务记录
     */
    private Long getAudioLengthFromAudioTask(String voiceUrl) {
        String methodName = "getAudioLengthFromAudioTask";
        try {
            if (StringUtils.isBlank(voiceUrl)) {
                return null;
            }

            log.debug("[{}] 开始从digital_audio_task表查询音频时长，URL：{}", methodName, voiceUrl);

            // 查询digital_audio_task表，通过generated_audio_url关联
            DigitalAudioTaskPO audioTask = digitalAudioTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalAudioTaskPO>()
                    .eq(DigitalAudioTaskPO::getGeneratedAudioUrl, voiceUrl)
                    .eq(DigitalAudioTaskPO::getStatus, 2) // 生成成功状态
                    .eq(DigitalAudioTaskPO::getIsDeleted, false)
                    .orderByDesc(DigitalAudioTaskPO::getCreatedTime)
                    .last("LIMIT 1")
            );

            if (audioTask != null && audioTask.getDurationMs() != null && audioTask.getDurationMs() > 0) {
                Long durationMs = audioTask.getDurationMs().longValue();
                log.debug("[{}] 从digital_audio_task表查询音频时长成功，URL：{}，时长：{}ms，任务ID：{}",
                        methodName, voiceUrl, durationMs, audioTask.getTaskId());
                return durationMs;
            }

            log.debug("[{}] digital_audio_task表中未找到匹配的音频记录，URL：{}", methodName, voiceUrl);
            return null;

        } catch (Exception e) {
            log.warn("[{}] 从digital_audio_task表查询音频时长异常，URL：{}，错误：{}",
                    methodName, voiceUrl, e.getMessage());
            return null;
        }
    }

    /**
     * 更新子任务的音频时长
     */
    private void updateSubTaskAudioLength(String subTaskId, Long audioLength) {
        String methodName = "updateSubTaskAudioLength";
        try {
            log.debug("[{}] 更新子任务音频时长，subTaskId：{}，audioLength：{}ms", methodName, subTaskId, audioLength);

            VideoEditTaskItemPO updatePO = new VideoEditTaskItemPO();
            updatePO.setAudioLength(audioLength);

            int updated = taskItemMapper.update(updatePO,
                    new LambdaUpdateWrapper<VideoEditTaskItemPO>()
                            .eq(VideoEditTaskItemPO::getSubTaskId, subTaskId)
                            .eq(VideoEditTaskItemPO::getIsDeleted, 0));

            if (updated > 0) {
                log.debug("[{}] 子任务音频时长更新成功，subTaskId：{}", methodName, subTaskId);
            } else {
                log.warn("[{}] 子任务音频时长更新失败，subTaskId：{}", methodName, subTaskId);
            }
        } catch (Exception e) {
            log.error("[{}] 更新子任务音频时长异常，subTaskId：{}", methodName, subTaskId, e);
        }
    }

    /**
     * 推送任务状态
     */
    private void pushTaskStatus(String userId, String taskId, Integer status, String errorMsg) {
        String methodName = "pushTaskStatus";
        log.info("[{}] 推送任务状态，userId：{}，taskId：{}，status：{}", methodName, userId, taskId, status);

        try {
            // 构建推送数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("taskId", taskId);
            dataMap.put("status", status);
            dataMap.put("statusDesc", VideoEditTaskStatusEnum.getDesc(status));
            if (StringUtils.isNotBlank(errorMsg)) {
                dataMap.put("errorMsg", errorMsg);
            }

            // 发送WebSocket消息
            String jsonMessage = MessageSendUtil.getJSONStr(
                    userId,
                    BMessageSendEnum.VIDEO_EDIT_JOB_PUSH, // 使用专门的视频编辑推送类型
                    dataMap);
            BRedisServiceUtil.sendMessageDigital(jsonMessage);

            log.info("[{}] 任务状态推送成功，userId：{}，taskId：{}", methodName, userId, taskId);

        } catch (Exception e) {
            log.error("[{}] 推送任务状态失败：userId={}, taskId={}, error={}", methodName, userId, taskId, e.getMessage(), e);
        }
    }
}
