package com.nacos.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nacos.entity.dto.DigitalVoiceStyleCreateDTO;
import com.nacos.entity.dto.DigitalVoiceCloningDTO;
import com.nacos.entity.po.DigitalVoiceUserClonePO;
import com.nacos.entity.vo.DigitalVoiceStyleVO;
import com.nacos.entity.vo.DigitalVoiceUserCloneVO;
import com.nacos.mapper.DigitalVoiceUserCloneMapper;
import com.nacos.service.DigitalAudioService;
import com.nacos.service.DigitalVoiceUserCloneService;
import com.nacos.result.Result;
import com.nacos.utils.IdGeneratorUtil;
import com.nacos.utils.MessageSendUtil;
import com.business.message.BMessageSendUtil;
import com.business.message.BMessageSendEnum;
import com.business.message.mq.BRedisServiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DigitalVoiceUserCloneServiceImpl extends ServiceImpl<DigitalVoiceUserCloneMapper, DigitalVoiceUserClonePO> implements DigitalVoiceUserCloneService {

    @Autowired
    private DigitalAudioService digitalAudioService;

    /**
     * 获取用户克隆音色列表
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public Result<List<DigitalVoiceUserCloneVO>> getUserVoiceList(String userId) {
        try {
            if (StringUtils.isEmpty(userId)) {
                return Result.ERROR("用户ID不能为空");
            }

            // 构建查询条件
            LambdaQueryWrapper<DigitalVoiceUserClonePO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DigitalVoiceUserClonePO::getUserId, userId)
                    .eq(DigitalVoiceUserClonePO::getIsDeleted, 0)
                    .orderByDesc(DigitalVoiceUserClonePO::getCreatedTime);

            List<DigitalVoiceUserClonePO> poList = list(wrapper);
            
            // 转换为VO列表
            List<DigitalVoiceUserCloneVO> voList = poList.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            
            /* //测试WebSocket消息推送
            try {
                log.info("开始测试WebSocket消息推送，userId={}", userId);
                
                // 构建测试消息内容
                Map<String, Object> messageData = new HashMap<>();
                messageData.put("testTime", System.currentTimeMillis());
                messageData.put("message", "这是一条测试消息");
                messageData.put("voiceCount", voList.size());

                String jsonMessage = MessageSendUtil.getJSONStr(
                    userId,
                    BMessageSendEnum.VIDEO_JOB_DIGITAL_PUSH,
                    messageData);

                boolean sendResult = BRedisServiceUtil.sendMessageDigital(jsonMessage);
                log.info("测试任务状态消息发送结果：{}", sendResult ? "成功" : "失败");
                
            } catch (Exception e) {
                log.error("测试WebSocket消息推送失败：{}", e.getMessage(), e);
                // 测试失败不影响正常业务逻辑
            } */
            
            return Result.SUCCESS("获取成功", voList);
        } catch (Exception e) {
            log.error("获取用户克隆音色列表失败，userId: {}", userId, e);
            return Result.ERROR("获取用户克隆音色列表失败");
        }
    }

    /**
     * 将PO转换为VO
     */
    private DigitalVoiceUserCloneVO convertToVO(DigitalVoiceUserClonePO po) {
        if (po == null) {
            return null;
        }
        DigitalVoiceUserCloneVO vo = new DigitalVoiceUserCloneVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    /**
     * 删除用户克隆音色
     *
     * @param userId 用户ID
     * @param voiceId 音色ID
     * @return 结果
     */
    @Override
    public Result<String> deleteUserVoice(String userId, String voiceId) {
        try {
            // 参数验证
            if (StringUtils.isEmpty(userId)) {
                return Result.ERROR("用户ID不能为空");
            }
            if (StringUtils.isEmpty(voiceId)) {
                return Result.ERROR("音色ID不能为空");
            }

            // 构建更新条件
            LambdaUpdateWrapper<DigitalVoiceUserClonePO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(DigitalVoiceUserClonePO::getUserId, userId)
                    .eq(DigitalVoiceUserClonePO::getVoiceId, voiceId)
                    .eq(DigitalVoiceUserClonePO::getIsDeleted, 0)
                    .set(DigitalVoiceUserClonePO::getIsDeleted, 1);
            // updateTime会由MyMetaObjectHandler自动填充

            boolean update = update(wrapper);
            if (!update) {
                return Result.ERROR("删除失败");
            }
            return Result.SUCCESS("删除成功");
        } catch (Exception e) {
            log.error("删除用户克隆音色失败，userId: {}, voiceId: {}", userId, voiceId, e);
            return Result.ERROR("删除用户克隆音色失败");
        }
    }

    /**
     * 更新用户克隆音色名称
     *
     * @param userId 用户ID
     * @param voiceId 音色ID
     * @param voiceName 音色名称
     * @return 结果
     */
    @Override
    public Result<String> updateUserVoiceName(String userId, String voiceId, String voiceName) {
        try {
            // 参数验证
            if (StringUtils.isEmpty(userId)) {
                return Result.ERROR("用户ID不能为空");
            }
            if (StringUtils.isEmpty(voiceId)) {
                return Result.ERROR("音色ID不能为空");
            }
            if (StringUtils.isEmpty(voiceName)) {
                return Result.ERROR("音色名称不能为空");
            }

            // 构建更新条件
            LambdaUpdateWrapper<DigitalVoiceUserClonePO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(DigitalVoiceUserClonePO::getUserId, userId)
                   .eq(DigitalVoiceUserClonePO::getVoiceId, voiceId)
                   .eq(DigitalVoiceUserClonePO::getIsDeleted, 0)
                   .set(DigitalVoiceUserClonePO::getVoiceName, voiceName);
            // updateTime会由MyMetaObjectHandler自动填充

            boolean success = update(wrapper);
            if (!success) {
                return Result.ERROR("更新音色名称失败");
            }
            return Result.SUCCESS("更新音色名称成功");
        } catch (Exception e) {
            log.error("更新音色名称失败，userId: {}, voiceId: {}", userId, voiceId, e);
            return Result.ERROR("更新音色名称失败");
        }
    }

    /**
     * 创建音色
     * 用户创建音色，包括上传音频、克隆音色、上传OSS等操作
     * @param digitalVoiceStyleCreateDTO 音色创建参数
     *                              userId: 用户ID
     *                              cloneFile: 克隆音频文件
     *                              clonePurpose: 克隆音频上传目的
     *                              cloneText: 克隆音频验证文本
     *                              promptFile: 提示音频文件
     *                              promptPurpose: 提示音频上传目的
     *                              promptText: 提示音频文本
     *                              text: 克隆音频文本
     * @return 创建的音色信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<DigitalVoiceStyleVO> createVoiceStyle(DigitalVoiceStyleCreateDTO digitalVoiceStyleCreateDTO) {
        String methodName = "createVoiceStyle";
        try {
            // 1. 参数校验
            if (digitalVoiceStyleCreateDTO == null) {
                return Result.ERROR("参数不完整，请检查输入");
            }
            
            String userId = digitalVoiceStyleCreateDTO.getUserId();
            if (userId == null || userId.isEmpty()) {
                return Result.ERROR("用户ID不能为空");
            }
            
            if (digitalVoiceStyleCreateDTO.getCloneFile() == null || digitalVoiceStyleCreateDTO.getPromptFile() == null) {
                return Result.ERROR("音频文件不能为空");
            }

            log.info("[{}]开始创建音色，用户ID：{}", methodName, userId);

            // 2. 上传克隆音频文件到MiniMax
            Result<String> cloneUploadResult = digitalAudioService.uploadAudio(
                    digitalVoiceStyleCreateDTO.getCloneFile(), 
                    digitalVoiceStyleCreateDTO.getClonePurpose(), 
                    userId);
                    
            if (!cloneUploadResult.isSuccess() || cloneUploadResult.getData() == null) {
                log.error("[{}]克隆音频文件上传失败：userId={}, error={}", methodName, userId, cloneUploadResult.getMessage());
                return Result.ERROR("克隆音频文件上传失败：" + cloneUploadResult.getMessage());
            }
            
            String cloneFileId = cloneUploadResult.getData();
            log.info("[{}]克隆音频文件上传成功：userId={}, fileId={}", methodName, userId, cloneFileId);

            // 3. 上传提示音频文件到MiniMax
            Result<String> promptUploadResult = digitalAudioService.uploadAudio(
                    digitalVoiceStyleCreateDTO.getPromptFile(), 
                    digitalVoiceStyleCreateDTO.getPromptPurpose(), 
                    userId);
                    
            if (!promptUploadResult.isSuccess() || promptUploadResult.getData() == null) {
                log.error("[{}]提示音频文件上传失败：userId={}, error={}", methodName, userId, promptUploadResult.getMessage());
                return Result.ERROR("提示音频文件上传失败：" + promptUploadResult.getMessage());
            }
            
            String promptFileId = promptUploadResult.getData();
            log.info("[{}]提示音频文件上传成功：userId={}, fileId={}", methodName, userId, promptFileId);

            // 4. 构建克隆请求参数
            DigitalVoiceCloningDTO digitalVoiceCloningDTO = buildVoiceCloningDTO(
                    digitalVoiceStyleCreateDTO, 
                    cloneFileId, 
                    promptFileId);

            // 5. 调用克隆接口
            log.info("[{}]开始克隆音色：userId={}", methodName, userId);
            Result<String> cloningResult = digitalAudioService.voiceCloning(digitalVoiceCloningDTO, userId);
            
            if (!cloningResult.isSuccess() || cloningResult.getData() == null) {
                log.error("[{}]音色克隆失败：userId={}, error={}", methodName, userId, cloningResult.getMessage());
                return Result.ERROR("音色克隆失败：" + cloningResult.getMessage());
            }
            
            String ossUrl = cloningResult.getData();
            
            // 6. 构建返回对象
            DigitalVoiceStyleVO voiceStyleVO = new DigitalVoiceStyleVO();
            voiceStyleVO.setVoiceId(IdGeneratorUtil.generateVoiceIdWithUser(userId));
            voiceStyleVO.setDemoAudio(ossUrl);
            voiceStyleVO.setVoiceName(digitalVoiceStyleCreateDTO.getVoiceName());
            voiceStyleVO.setProvider("MINIMAX");

            log.info("[{}]音色创建成功：userId={}, voiceId={}", methodName, userId, voiceStyleVO.getVoiceId());
            return Result.SUCCESS("音色创建成功", voiceStyleVO);

        } catch (Exception e) {
            String userId = digitalVoiceStyleCreateDTO != null ? digitalVoiceStyleCreateDTO.getUserId() : "unknown";
            log.error("[{}]创建音色异常：userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }

    /**
     * 创建音色
     * 用户创建音色，包括上传音频、克隆音色、上传OSS等操作
     * @param digitalVoiceStyleCreateDTO 音色创建参数
     *                              userId: 用户ID
     *                              cloneFile: 克隆音频文件
     *                              clonePurpose: 克隆音频上传目的
     *                              cloneText: 克隆音频验证文本
     *                              promptFile: 提示音频文件
     *                              promptPurpose: 提示音频上传目的
     *                              promptText: 提示音频文本
     *                              text: 克隆音频文本
     * @return 创建的音色信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<DigitalVoiceStyleVO> createVoiceStyleMicroSoft(DigitalVoiceStyleCreateDTO digitalVoiceStyleCreateDTO) {
        String methodName = "createVoiceStyleMicroSoft";
        try {
            // 1. 参数校验
            if (digitalVoiceStyleCreateDTO == null) {
                return Result.ERROR("参数不完整，请检查输入");
            }

            String userId = digitalVoiceStyleCreateDTO.getUserId();
            if (userId == null || userId.isEmpty()) {
                return Result.ERROR("用户ID不能为空");
            }

            if (digitalVoiceStyleCreateDTO.getCloneFile() == null || digitalVoiceStyleCreateDTO.getPromptFile() == null) {
                return Result.ERROR("音频文件不能为空");
            }

            log.info("[{}]开始创建音色，用户ID：{}", methodName, userId);

            // 2. 上传克隆音频文件到MiniMax
            Result<String> cloneUploadResult = digitalAudioService.uploadAudio(
                    digitalVoiceStyleCreateDTO.getCloneFile(),
                    digitalVoiceStyleCreateDTO.getClonePurpose(),
                    userId);

            if (!cloneUploadResult.isSuccess() || cloneUploadResult.getData() == null) {
                log.error("[{}]克隆音频文件上传失败：userId={}, error={}", methodName, userId, cloneUploadResult.getMessage());
                return Result.ERROR("克隆音频文件上传失败：" + cloneUploadResult.getMessage());
            }

            String cloneFileId = cloneUploadResult.getData();
            log.info("[{}]克隆音频文件上传成功：userId={}, fileId={}", methodName, userId, cloneFileId);

            // 3. 上传提示音频文件到MiniMax
            Result<String> promptUploadResult = digitalAudioService.uploadAudio(
                    digitalVoiceStyleCreateDTO.getPromptFile(),
                    digitalVoiceStyleCreateDTO.getPromptPurpose(),
                    userId);

            if (!promptUploadResult.isSuccess() || promptUploadResult.getData() == null) {
                log.error("[{}]提示音频文件上传失败：userId={}, error={}", methodName, userId, promptUploadResult.getMessage());
                return Result.ERROR("提示音频文件上传失败：" + promptUploadResult.getMessage());
            }

            String promptFileId = promptUploadResult.getData();
            log.info("[{}]提示音频文件上传成功：userId={}, fileId={}", methodName, userId, promptFileId);

            // 4. 构建克隆请求参数
            DigitalVoiceCloningDTO digitalVoiceCloningDTO = buildVoiceCloningDTO(
                    digitalVoiceStyleCreateDTO,
                    cloneFileId,
                    promptFileId);

            // 5. 调用克隆接口
            log.info("[{}]开始克隆音色：userId={}", methodName, userId);
            Result<String> cloningResult = digitalAudioService.voiceCloning(digitalVoiceCloningDTO, userId);

            if (!cloningResult.isSuccess() || cloningResult.getData() == null) {
                log.error("[{}]音色克隆失败：userId={}, error={}", methodName, userId, cloningResult.getMessage());
                return Result.ERROR("音色克隆失败：" + cloningResult.getMessage());
            }

            String ossUrl = cloningResult.getData();

            // 6. 构建返回对象
            DigitalVoiceStyleVO voiceStyleVO = new DigitalVoiceStyleVO();
            voiceStyleVO.setVoiceId(IdGeneratorUtil.generateVoiceIdWithUser(userId));
            voiceStyleVO.setDemoAudio(ossUrl);
            voiceStyleVO.setVoiceName(digitalVoiceStyleCreateDTO.getVoiceName());
            voiceStyleVO.setProvider("AZURE");

            log.info("[{}]音色创建成功：userId={}, voiceId={}", methodName, userId, voiceStyleVO.getVoiceId());
            return Result.SUCCESS("音色创建成功", voiceStyleVO);

        } catch (Exception e) {
            String userId = digitalVoiceStyleCreateDTO != null ? digitalVoiceStyleCreateDTO.getUserId() : "unknown";
            log.error("[{}]创建音色异常：userId={}, error={}", methodName, userId, e.getMessage(), e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }
    
    /**
     * 构建声音克隆DTO
     */
    private DigitalVoiceCloningDTO buildVoiceCloningDTO(
            DigitalVoiceStyleCreateDTO createDTO, 
            String cloneFileId, 
            String promptFileId) {
            
        DigitalVoiceCloningDTO cloningDTO = new DigitalVoiceCloningDTO();
        cloningDTO.setFileId(Long.parseLong(cloneFileId));
        cloningDTO.setVoiceName(createDTO.getVoiceName());
        cloningDTO.setTextValidation(createDTO.getCloneText());
        cloningDTO.setText(createDTO.getText());

        // 设置克隆提示参数
        DigitalVoiceCloningDTO.ClonePrompt clonePrompt = new DigitalVoiceCloningDTO.ClonePrompt();
        clonePrompt.setPromptAudio(Long.valueOf(promptFileId));
        clonePrompt.setPromptText(createDTO.getPromptText());
        cloningDTO.setClonePrompt(clonePrompt);
        
        return cloningDTO;
    }
} 