package com.nacos.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.aliyun.dashvector.models.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.business.alibailian.BaiLianUtil;
import com.business.db.mapper.DigitalVectorItemMapper;
import com.business.db.mapper.DigitalVectorTaskMapper;
import com.business.db.model.po.digital.DigitalVectorItemPO;
import com.business.db.model.po.digital.DigitalVectorTaskPO;
import com.business.message.BMessageSendEnum;
import com.business.message.mq.BRedisServiceUtil;
import com.nacos.config.TencentCloudConfig;
import com.nacos.entity.bo.TaskStatusUpdateBO;
import com.nacos.entity.dto.DigitalVideoGenerationDTO;
import com.nacos.entity.enums.DigitalNotificationEnum;
import com.nacos.entity.enums.VideoTaskStatusEnum;
import com.nacos.entity.po.*;
import com.nacos.exception.E;
import com.nacos.mapper.*;
import com.nacos.model.TXDigital.TXDigitalApisUtil;
import com.nacos.result.Result;
import com.nacos.service.*;
import com.nacos.utils.BFeiShuUtil;
import com.nacos.utils.MessageSendUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aliyun.dashvector.models.requests.InsertDocRequest;
import com.aliyun.dashvector.models.responses.Response;

import com.aliyun.dashvector.DashVectorClient;

import com.aliyun.dashvector.DashVectorCollection;

import com.aliyun.dashvector.models.Vector;
import com.aliyun.dashvector.models.Doc;
import com.aliyun.dashvector.models.requests.QueryDocRequest;

import com.aliyun.dashvector.models.requests.DeleteDocRequest;

import java.util.Arrays;

import static com.nacos.constant.CommonConst.*;

/**
 * 数字人视频服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalVectorServiceImpl implements DigitalVectorService {

    private final DigitalUserAvatarMapper digitalUserAvatarMapper;
    private final DigitalSystemAvatarMapper digitalSystemAvatarMapper;
    private final DigitalVideoTaskMapper digitalVideoTaskMapper;
    private final DigitalVectorTaskMapper digitalVectorTaskMapper;
    private final DigitalVectorItemMapper digitalVectorItemMapper;
    private final DigitalVideoTaskService digitalVideoTaskService;
    private final DigitalVideoAsyncService digitalVideoAsyncService;
    private final TXDigitalApisUtil txDigitalApisUtil;
    private final DigitalVideoTaskItemMapper digitalVideoTaskItemMapper;
    private final DigitalNotificationService digitalNotificationService;
    @Autowired
    private TencentCloudConfig tencentCloudConfig;
    private final FeiyongService feiyongService;

    /**
     * 提交数字人生成视频请求
     *
     * @param digitalVideoGenerationDTO 提交数字人生成视频请求参数
     * @return 任务ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> addDoc(DigitalVideoGenerationDTO digitalVideoGenerationDTO) {
        String methodName = "addDoc";
        log.info("[{}] 开始提交数字人生成视频任务，参数：{}", methodName, JSONObject.toJSONString(digitalVideoGenerationDTO));

        DashVectorClient client = new DashVectorClient("YOUR_API_KEY", "YOUR_CLUSTER_ENDPOINT");

        Response<Void> response = client.create("quickstart", 4);
        System.out.println(response);
        DashVectorCollection collection = client.get("quickstart");

        assert collection.isSuccess();

        Doc doc1 = Doc.builder()
                .id("1")
                .vector(
                        Vector.builder()
                                .value(Arrays.asList(0.1f, 0.2f, 0.3f, 0.4f))
                                .build()
                ).build();

        Doc doc2 = Doc.builder()
                .id("2")
                .vector(
                        Vector.builder()
                                .value(Arrays.asList(0.2f, 0.3f, 0.4f, 0.5f))
                                .build()
                ).fields(new HashMap<String, Object>(){{
                    put("age", 20);
                    put("name", "zhangsan");
                }}).build();

        Doc doc3 = Doc.builder()
                .id("3")
                .field("anykey", "anyvalue")
                .vector(
                        Vector.builder()
                                .value(Arrays.asList(0.3f, 0.4f, 0.5f, 0.6f))
                                .build()
                ).build();

        InsertDocRequest request = InsertDocRequest.builder()
                .docs(Arrays.asList(doc1, doc2, doc3))
                .build();

        Response<List<DocOpResult>> response3 = collection.insert(request);

        Vector vector = Vector.builder().value(Arrays.asList(0.1f, 0.2f, 0.3f, 0.4f)).build();

        QueryDocRequest request1 = QueryDocRequest.builder()
                .vector(vector)
                .topk(2)
                .build();

        Response<List<Doc>> response1 = collection.query(request1);

        DeleteDocRequest request4 = DeleteDocRequest.builder()
                .id("1")
                .build();

        Response<List<DocOpResult>> response4 = collection.delete(request4);

        Response<CollectionStats> response5 = collection.stats();

//        Response<Void> response6 = client.delete("quickstart");

        return Result.ERROR("11");
    }

    /**
     * 处理排队中的任务
     */
    @Override
    public void processQueueingTasks() {
        String methodName = "processQueueingTasks";
        try {
            // 查询任务队列中有几个排队任务
            long queueingCount = digitalVideoTaskMapper.selectCount(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.QUEUING.getValue())
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );
            if (queueingCount == 0) {
                log.info("[{}] 没有排队中的任务", methodName);
                return;
            }

            log.info("[{}] 当前任务队列中排队任务数量：{}", methodName, queueingCount);
            // 1. 首先检查是否有进行中的任务
            long inProgressCount = digitalVideoTaskMapper.selectCount(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.IN_PROGRESS.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );

            // 如果已经有进行中的任务，则不处理排队任务
            if (inProgressCount > 0) {
                log.info("[{}] 当前有{}个进行中的任务，暂不处理排队任务", methodName, inProgressCount);
                return;
            }

            // 2. 查询排队中的任务（按创建时间升序，最早创建的任务优先处理）
            List<DigitalVideoTaskPO> queueingTasks = digitalVideoTaskMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.QUEUING.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                            .orderByAsc(DigitalVideoTaskPO::getCreatedTime)
                            .last("LIMIT 1") // 只取一个任务处理
            );

            if (queueingTasks.isEmpty()) {
                log.debug("[{}] 没有排队中的任务", methodName);
                return;
            }

            // 3. 处理队列中的第一个任务
            DigitalVideoTaskPO task = queueingTasks.getFirst();
            log.info("[{}] 开始处理排队任务：taskId={}, userId={}", methodName, task.getTaskId(), task.getUserId());

            try {
                // 4. 查询子任务详情
                List<DigitalVideoTaskItemPO> taskItems = digitalVideoTaskItemMapper.selectList(
                        new LambdaQueryWrapper<DigitalVideoTaskItemPO>()
                                .eq(DigitalVideoTaskItemPO::getTaskId, task.getTaskId())
                                .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                                .orderByAsc(DigitalVideoTaskItemPO::getSequence)
                );

                if (taskItems.isEmpty()) {
                    String errorMsg = "该任务没有子任务";
                    log.error("[{}] {}：taskId={}", methodName, errorMsg, task.getTaskId());
                    digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                        .taskId(task.getTaskId())
                        .status(VideoTaskStatusEnum.FAILED.getValue())
                        .errorMsg(errorMsg)
                        .build()
                    );
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.FAILED.getValue(), errorMsg);
                    return;
                }
                // 更新任务状态为进行中
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(task.getTaskId())
                    .status(VideoTaskStatusEnum.IN_PROGRESS.getValue())
                    .build()
                );
                // 提交到异步服务处理
                digitalVideoAsyncService.processVideoTasks(
                        task.getTaskId(),
                        task.getUserId(),
                        taskItems
                );
                log.info("[{}] 任务已提交到异步服务处理：taskId={}", methodName, task.getTaskId());
                pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.IN_PROGRESS.getValue(), "任务已提交到异步服务处理");
            } catch (Exception e) {
                String errorMsg = "处理任务异常：" + e.getMessage();
                log.error("[{}] {}：taskId={}", methodName, errorMsg, task.getTaskId(), e);
                digitalVideoTaskService.updateTask(TaskStatusUpdateBO.builder()
                    .taskId(task.getTaskId())
                    .status(VideoTaskStatusEnum.FAILED.getValue())
                    .errorMsg(errorMsg)
                    .build()
                );
                pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.FAILED.getValue(), errorMsg);
            }
        } catch (Exception e) {
            log.error("[{}] 处理排队中的任务异常", methodName, e);
        }
    }

    /**
     * 处理超时任务
     */
    @Override
    public void processTimeoutTasks() {
        String methodName = "processTimeoutTasks";
        log.info("[{}] 开始处理超时任务", methodName);

        try {
            // 获取配置的超时时间（分钟）
            int timeoutMinutes = 30; // 默认30分钟

            // 计算超时时间点
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);

            // 查询可能超时的任务（进行中状态且更新时间超过阈值）
            List<DigitalVideoTaskPO> potentialTimeoutTasks = digitalVideoTaskMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getStatus, VideoTaskStatusEnum.IN_PROGRESS.getValue())
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                            .lt(DigitalVideoTaskPO::getUpdateTime, timeoutThreshold)
            );

            if (potentialTimeoutTasks.isEmpty()) {
                log.info("[{}] 没有超时的任务", methodName);
                return;
            }

            log.info("[{}] 发现{}个可能超时的任务", methodName, potentialTimeoutTasks.size());

            // 处理每个可能超时的任务
            for (DigitalVideoTaskPO task : potentialTimeoutTasks) {
                try {
                    log.info("[{}] 处理超时任务：taskId={}, createdTime={}, updateTime={}",
                            methodName, task.getTaskId(), task.getCreatedTime(), task.getUpdateTime());

                    // 更新任务状态为超时
                    task.setStatus(VideoTaskStatusEnum.TIMEOUT.getValue());
                    task.setErrorMsg("任务处理超时，请重试");
                    task.setUpdateTime(new Date());
                    digitalVideoTaskMapper.updateById(task);

                    // 推送任务状态更新
                    pushTaskStatus(task.getUserId(), task.getTaskId(), VideoTaskStatusEnum.TIMEOUT.getValue(), "任务处理超时，请重试");

                    log.info("[{}] 任务{}已标记为超时", methodName, task.getTaskId());

                } catch (Exception e) {
                    log.error("[{}] 处理超时任务{}异常", methodName, task.getTaskId(), e);
                }
            }
        } catch (Exception e) {
            log.error("[{}] 处理超时任务异常", methodName, e);
        }
    }

    @Override
    public void saveVectorDocument(String userId, String originalFilename, String fileUrl, String md5,Double vectorSize) {
        digitalVectorItemMapper.insert(
                DigitalVectorItemPO.builder()
                .userId(userId)
                .originalFilename(originalFilename)
                .fileUrl(fileUrl)
                .status(KNOWLEDGE_ITEM_STATUS_0)
                .md5(md5)
                .fileSize(vectorSize)
                .build()
        );
    }

    /**
     * 查询用户未被删除的文档列表
     * @param userId 用户ID
     * @return 文档列表
     */
    @Override
    public List<DigitalVectorItemPO> getUserActiveDocuments(String userId) {
        String methodName = "getUserActiveDocuments";
        log.info("[{}] 查询用户未被删除的文档：userId={}", methodName, userId);

        return digitalVectorItemMapper.selectList(
                new LambdaQueryWrapper<DigitalVectorItemPO>()
                        .eq(DigitalVectorItemPO::getUserId, userId)
                        .eq(DigitalVectorItemPO::getIsDeleted, 0)
        );
    }

    @Override
    public void processItemIndex() {
        String methodName = "processItemIndex";
        // 1. 获取并锁定待处理的文档记录（状态为0）
        LambdaQueryWrapper<DigitalVectorItemPO> queryWrapper = new LambdaQueryWrapper<DigitalVectorItemPO>()
                .eq(DigitalVectorItemPO::getStatus, KNOWLEDGE_ITEM_STATUS_0) // 状态为0（待处理）
                .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发处理

        DigitalVectorItemPO itemRecord = digitalVectorItemMapper.selectOne(queryWrapper);
        if (itemRecord == null) {
//            log.info("[{}] 没有待处理的文档任务", methodName);
            return;
        }

        // 更新状态为3（处理中）
        itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_3);
        digitalVectorItemMapper.updateById(itemRecord);

        try {
            boolean lease = feiyongService.checkKownledgeSpace(Long.parseLong(itemRecord.getUserId()), "文档上传", itemRecord.getFileSize());
            if (!lease){
                throw new RuntimeException("知识空间不足，请充值");
            }

            // 2. 根据userId查找DigitalVectorTask记录
            LambdaQueryWrapper<DigitalVectorTaskPO> taskQueryWrapper = new LambdaQueryWrapper<DigitalVectorTaskPO>()
                    .eq(DigitalVectorTaskPO::getUserId, itemRecord.getUserId());
            DigitalVectorTaskPO taskRecord = digitalVectorTaskMapper.selectOne(taskQueryWrapper);

            String indexId;
            if (taskRecord != null && StringUtils.isNotBlank(taskRecord.getIndexId())) {
                // 如果存在记录，直接使用已有indexId
                indexId = taskRecord.getIndexId();
                log.info("[{}] 使用已有的index记录：userId={}, indexId={}", methodName, itemRecord.getUserId(), indexId);
            } else {
                // 如果不存在记录，调用接口创建新index并保存
                log.info("[{}] 开始创建新indesx，userId={}", methodName, itemRecord.getUserId());

                Result<String> response = createIndexForUser(itemRecord.getUserId(), itemRecord.getOriginalFilename());
                
                if (!response.isSuccess()) {
                    throw new RuntimeException("创建index失败：" + response.getMessage());
                }
                
                indexId = response.getData();
                
                // 创建新的DigitalVectorTask记录
                digitalVectorTaskMapper.insert(
                    DigitalVectorTaskPO.builder()
                        .userId(itemRecord.getUserId())
                        .indexId(indexId)
                        .build()
                );
                
                log.info("[{}] 新index创建成功：userId={}, indexId={}", methodName, itemRecord.getUserId(), indexId);
            }

            // 3. 更新item状态为1（处理成功）
            itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_1);
            digitalVectorItemMapper.updateById(itemRecord);
            
            log.info("[{}] 文档处理成功：itemId={}, indexId={}", methodName, itemRecord.getId(), indexId);

        } catch (Exception e) {
            log.error("[{}] 处理文档任务异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "文档处理失败", "ERRORInfo=" + e.getMessage());
            
            // 更新状态为2（处理失败）
            // 3. 更新item状态为1（处理成功）
            itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_2);
            itemRecord.setErrorMsg(e.getMessage());
            digitalVectorItemMapper.updateById(itemRecord);
        }
    }

    private Result<String> createIndexForUser(String userId, String originalFilename) {
        return BaiLianUtil.createIndex(userId+"库");
    }

    /**
     * // 1. 从digitalVectorItemMapper获取status = KNOWLEDGE_ITEM_STATUS_1的记录并更新为KNOWLEDGE_ITEM_STATUS_6（处理中）
     * // 2. 根据获取的userId，查找DigitalVectorTaskMapper的userId，将ITem中的fileUrl中的文件调用addFile方法上传，获取fileId。存入item表的fileid字段中 并更新item状态为KNOWLEDGE_ITEM_STATUS_4
     * // 3.出现异常则更新状态为KNOWLEDGE_ITEM_STATUS_5，并飞书报警
     */
    @Override
    public void processDocument() {
        String methodName = "processDocument";
        // 1. 获取并锁定待处理的文档记录（状态为KNOWLEDGE_ITEM_STATUS_1）
        LambdaQueryWrapper<DigitalVectorItemPO> queryWrapper = new LambdaQueryWrapper<DigitalVectorItemPO>()
                .eq(DigitalVectorItemPO::getStatus, KNOWLEDGE_ITEM_STATUS_1) // 状态为1（待处理）
                .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发处理

        DigitalVectorItemPO itemRecord = digitalVectorItemMapper.selectOne(queryWrapper);
        if (itemRecord == null) {
//            log.info("[{}] 没有待处理的文档任务", methodName);
            return;
        }

        // 更新状态为KNOWLEDGE_ITEM_STATUS_6（处理中）
        itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_6);
        digitalVectorItemMapper.updateById(itemRecord);
        try {

            // 2. 根据userId查找DigitalVectorTask记录
            LambdaQueryWrapper<DigitalVectorTaskPO> taskQueryWrapper = new LambdaQueryWrapper<DigitalVectorTaskPO>()
                    .eq(DigitalVectorTaskPO::getUserId, itemRecord.getUserId());
            DigitalVectorTaskPO taskRecord = digitalVectorTaskMapper.selectOne(taskQueryWrapper);

            if (taskRecord == null || StringUtils.isBlank(taskRecord.getIndexId())) {
                throw new RuntimeException("未找到有效的indexId");
            }

            String indexId = taskRecord.getIndexId();
            log.info("[{}] 开始处理文档上传：itemId={}, indexId={}", methodName, itemRecord.getId(), indexId);

            // 调用addFile方法上传文件，获取fileId
            //处理文件上传的问题，将
            String url = itemRecord.getFileUrl();
            Result<String> response = addFile(itemRecord,taskRecord);
            
            if (!response.isSuccess()) {
                throw new RuntimeException("文件上传失败"+response.getMessage());
            }
            
            log.info("[{}] 文件上传成功：itemId={}, fileId={}", methodName, itemRecord.getId(), response.getData());


            // 更新item状态为KNOWLEDGE_ITEM_STATUS_4（已提交任务）
            itemRecord.setFileId(response.getData());
            itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_4);
            digitalVectorItemMapper.updateById(itemRecord);
            
        } catch (Exception e) {
            log.error("[{}] 处理文档任务异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "文档处理失败", "ERRORInfo=" + e.getMessage());
            
            // 更新状态为KNOWLEDGE_ITEM_STATUS_5（处理失败）
            try {
                itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_5);
                itemRecord.setErrorMsg(e.getMessage());
                digitalVectorItemMapper.updateById(itemRecord);
            } catch (Exception ex) {
                log.error("[{}] 更新文档失败状态异常", methodName, ex);
            }
        }
    }

    private Result<String> addFile(DigitalVectorItemPO itemRecord, DigitalVectorTaskPO taskRecord) {
        return BaiLianUtil.addFile(itemRecord.getFileUrl(), itemRecord.getMd5(), taskRecord.getIndexId());
    }


    @Override
    public void processAddDocumentsJob() {
        String methodName = "processAddDocumentsJob";
        // 1. 获取并锁定待处理的文档记录（状态为KNOWLEDGE_ITEM_STATUS_1）
        LambdaQueryWrapper<DigitalVectorItemPO> queryWrapper = new LambdaQueryWrapper<DigitalVectorItemPO>()
                .eq(DigitalVectorItemPO::getStatus, KNOWLEDGE_ITEM_STATUS_4)
                .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发处理

        DigitalVectorItemPO itemRecord = digitalVectorItemMapper.selectOne(queryWrapper);
        if (itemRecord == null) {
//            log.info("[{}] 没有待处理的文档任务", methodName);
            return;
        }

        // 更新状态为KNOWLEDGE_ITEM_STATUS_6（处理中）
        itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_9);
        digitalVectorItemMapper.updateById(itemRecord);
        try {
            Result<String> response =BaiLianUtil.describeFile(itemRecord.getFileId());
            if (!response.isSuccess()) {
                throw new RuntimeException("文件描述失败"+response.getMessage());
            } else {
                String parseStatus = response.getData();

                switch (parseStatus) {
                    case "INIT", "PARSING" -> {
                        log.info("[{}] 文件仍在解析中，状态为{}：itemId={}, fileId={}", methodName, parseStatus, itemRecord.getId(), itemRecord.getFileId());
                        itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_4);
                        digitalVectorItemMapper.updateById(itemRecord);
                        return;
                    }
                    case "PARSE_SUCCESS" -> log.info("[{}] 文件解析完成：itemId={}, fileId={}", methodName, itemRecord.getId(), itemRecord.getFileId());
                    case "PARSE_FAILED" -> {
                        String errorMsg = "文件解析失败：itemId=" + itemRecord.getId() + ", fileId=" + itemRecord.getFileId();
                        log.error("[{}] {}", methodName, errorMsg);
                        throw new RuntimeException(errorMsg);
                    }
                    default -> {
                        log.warn("[{}] 未知的解析状态：{}", methodName, parseStatus);
                        throw new RuntimeException("未知的解析状态"+parseStatus);
                    }
                }

            }

            // 2. 根据userId查找DigitalVectorTask记录
            LambdaQueryWrapper<DigitalVectorTaskPO> taskQueryWrapper = new LambdaQueryWrapper<DigitalVectorTaskPO>()
                    .eq(DigitalVectorTaskPO::getUserId, itemRecord.getUserId());
            DigitalVectorTaskPO taskRecord = digitalVectorTaskMapper.selectOne(taskQueryWrapper);


            // 调用SubmitIndexAddDocumentsJob方法，获取jobId
            Result<String> result = submitIndexAddDocumentsJob(taskRecord, itemRecord);

            if (!result.isSuccess()) {
                throw new RuntimeException("提交索引添加文档任务失败"+result.getMessage());
            }

            log.info("[{}] 提交索引添加文档任务成功：itemId={}, jobId={}", methodName, itemRecord.getId(), result.getData());

            // 更新item状态为KNOWLEDGE_ITEM_STATUS_4（已提交任务）
            itemRecord.setJobId(result.getData());
            itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_7);
            digitalVectorItemMapper.updateById(itemRecord);

        } catch (Exception e) {
            log.error("[{}] 处理文档任务异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "文档处理失败", "ERRORInfo=" + e.getMessage());

            // 更新状态为KNOWLEDGE_ITEM_STATUS_5（处理失败）
            try {
                itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_8);
                itemRecord.setErrorMsg(e.getMessage());
                digitalVectorItemMapper.updateById(itemRecord);
            } catch (Exception ex) {
                log.error("[{}] 更新文档失败状态异常", methodName, ex);
            }
        }
    }

    @Override
    public void updateDocumentStatus(String docId, String userId, int status) {
        // 根据docId和userId查询文档记录
        DigitalVectorItemPO itemRecord = digitalVectorItemMapper.selectOne(
                new LambdaQueryWrapper<DigitalVectorItemPO>()
                        .eq(DigitalVectorItemPO::getId, docId)
                        .eq(DigitalVectorItemPO::getUserId, userId)
                        .eq(DigitalVectorItemPO::getIsDeleted, 0)
        );

        if (itemRecord == null) {
            log.warn("未找到对应的文档记录，docId={}, userId={}", docId, userId);
            return;
        }

        try {
            // 更新文档状态
            itemRecord.setStatus(status);
            digitalVectorItemMapper.updateById(itemRecord);
            log.info("文档状态更新成功：docId={}, newStatus={}", docId, status);
        } catch (Exception e) {
            log.error("更新文档状态异常，docId={}, error={}", docId, e.getMessage(), e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "文档状态更新失败", "ERRORInfo=" + e.getMessage());
        }
    }

    @Override
    public DigitalVectorTaskPO getIndexInfo(Long userId) {
        //根据gptSceneGenerationDTO的用户id，userId从DigitalVectorTaskPO中查询查询用户知识库id，
        // 创建查询条件
        LambdaQueryWrapper<DigitalVectorTaskPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DigitalVectorTaskPO::getUserId, userId);
//                     .eq(DigitalVectorTaskPO::getStatus, 2); // 假设状态为2表示任务已完成

        // 查询用户的知识库任务信息
        DigitalVectorTaskPO digitalVectorTaskPO = digitalVectorTaskMapper.selectOne(queryWrapper);

        if (digitalVectorTaskPO == null) {
            throw new E("未找到有效的知识库任务信息");
        }

        return digitalVectorTaskPO;
    }


    @Override
    public void processDelIndexDoc() {
        String methodName = "processDelIndexDoc";
        // 1. 获取并锁定待处理的文档记录（状态为KNOWLEDGE_ITEM_STATUS_19）
        LambdaQueryWrapper<DigitalVectorItemPO> queryWrapper = new LambdaQueryWrapper<DigitalVectorItemPO>()
                .eq(DigitalVectorItemPO::getStatus, KNOWLEDGE_ITEM_STATUS_19) // 状态为19（待删除）
                .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发处理

        DigitalVectorItemPO itemRecord = digitalVectorItemMapper.selectOne(queryWrapper);
        if (itemRecord == null) {
            return;
        }

        // 更新状态为KNOWLEDGE_ITEM_STATUS_15（处理中）
        itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_15);
        digitalVectorItemMapper.updateById(itemRecord);

        try {
            // 2. 根据userId查找DigitalVectorTask记录
            LambdaQueryWrapper<DigitalVectorTaskPO> taskQueryWrapper = new LambdaQueryWrapper<DigitalVectorTaskPO>()
                    .eq(DigitalVectorTaskPO::getUserId, itemRecord.getUserId());
            DigitalVectorTaskPO taskRecord = digitalVectorTaskMapper.selectOne(taskQueryWrapper);

            if (taskRecord == null || StringUtils.isBlank(taskRecord.getIndexId())) {
                throw new RuntimeException("未找到有效的indexId");
            }

            log.info("[{}] 开始删除索引文档：itemId={}, indexId={}", methodName, itemRecord.getId(), taskRecord.getIndexId());

            // 调用DeleteIndexDocument方法删除文档
            Result<String> response = deleteIndexDocument(taskRecord, itemRecord);

            if (!response.isSuccess()) {
                throw new RuntimeException("删除索引文档失败：" + response.getMessage());
            }

            log.info("[{}] 索引文档删除成功：itemId={}, docId={}", methodName, itemRecord.getId(), response.getData());

            // 更新item状态为KNOWLEDGE_ITEM_STATUS_13（删除成功）
            itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_16);
            digitalVectorItemMapper.updateById(itemRecord);
            int rows = digitalVectorItemMapper.update(null,
                    new LambdaUpdateWrapper<DigitalVectorItemPO>()
                            .set(DigitalVectorItemPO::getIsDeleted, 1)
                            .set(DigitalVectorItemPO::getStatus, KNOWLEDGE_ITEM_STATUS_16)
                            .eq(DigitalVectorItemPO::getId, itemRecord.getId())
            );
            feiyongService.tuiKownledgeSpace(Long.valueOf(itemRecord.getUserId()), itemRecord.getId(), "文档空间退回成功", itemRecord.getFileSize());
        } catch (Exception e) {
            log.error("[{}] 删除索引文档异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "删除索引文档失败", "ERRORInfo=" + e.getMessage());

            // 更新状态为KNOWLEDGE_ITEM_STATUS_14（删除失败）
            try {
                itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_14);
                itemRecord.setErrorMsg(e.getMessage());
                digitalVectorItemMapper.updateById(itemRecord);
            } catch (Exception ex) {
                log.error("[{}] 更新文档删除失败状态异常", methodName, ex);
            }
        }
    }


     @Override
     public void processDelSpaceDoc() {
         String methodName = "processDelSpaceDoc";
         // 1. 获取并锁定待处理的文档记录（状态为KNOWLEDGE_ITEM_STATUS_13）
         LambdaQueryWrapper<DigitalVectorItemPO> queryWrapper = new LambdaQueryWrapper<DigitalVectorItemPO>()
                 .eq(DigitalVectorItemPO::getStatus, KNOWLEDGE_ITEM_STATUS_13) // 状态为13（待删除）
                 .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发处理

         DigitalVectorItemPO itemRecord = digitalVectorItemMapper.selectOne(queryWrapper);
         if (itemRecord == null) {
             return;
         }

         // 更新状态为KNOWLEDGE_ITEM_STATUS_18（处理中）
         itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_18);
         digitalVectorItemMapper.updateById(itemRecord);

         try {
//             // 2. 根据userId查找DigitalVectorTask记录
//             LambdaQueryWrapper<DigitalVectorTaskPO> taskQueryWrapper = new LambdaQueryWrapper<DigitalVectorTaskPO>()
//                     .eq(DigitalVectorTaskPO::getUserId, itemRecord.getUserId());
//             DigitalVectorTaskPO taskRecord = digitalVectorTaskMapper.selectOne(taskQueryWrapper);
//
//             if (taskRecord == null || StringUtils.isBlank(taskRecord.getIndexId())) {
//                 throw new RuntimeException("未找到有效的indexId");
//             }

             log.info("[{}] 开始调用DelSpaceDoc方法：itemId={}", methodName, itemRecord.getId());

             // 调用DelSpaceDoc方法删除文档
             Result<String> response = delSpaceDoc(null, itemRecord);

             if (!response.isSuccess()) {
                 throw new RuntimeException("DelSpaceDoc执行失败：" + response.getMessage());
             }

             log.info("[{}] DelSpaceDoc执行成功：itemId={}, docId={}", methodName, itemRecord.getId(), response.getData());

             // 更新item状态为KNOWLEDGE_ITEM_STATUS_16（删除成功）
             itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_10);
             digitalVectorItemMapper.updateById(itemRecord);


         } catch (Exception e) {
             log.error("[{}] DelSpaceDoc处理异常", methodName, e);
             // 飞书报警
             BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "DelSpaceDoc处理失败", "ERRORInfo=" + e.getMessage());

             // 更新状态为KNOWLEDGE_ITEM_STATUS_17（处理失败）
             try {
                 itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_17);
                 itemRecord.setErrorMsg(e.getMessage());
                 digitalVectorItemMapper.updateById(itemRecord);
             } catch (Exception ex) {
                 log.error("[{}] 更新文档删除失败状态异常", methodName, ex);
             }
         }
     }

     private Result<String> delSpaceDoc(DigitalVectorTaskPO taskRecord, DigitalVectorItemPO itemRecord) {
         return BaiLianUtil.delSpaceDoc(itemRecord.getFileId());
     }

    private Result<String> deleteIndexDocument(DigitalVectorTaskPO taskRecord, DigitalVectorItemPO itemRecord) {
        return BaiLianUtil.deleteIndexDocument(taskRecord.getIndexId(), itemRecord.getFileId());
    }


    private Result<String> submitIndexAddDocumentsJob(DigitalVectorTaskPO taskRecord, DigitalVectorItemPO itemRecord) {
        return BaiLianUtil.submitIndexAddDocumentsJob(taskRecord.getIndexId(), itemRecord.getFileId());
    }

    /**
     * // 1. 从digitalVectorItemMapper获取status = KNOWLEDGE_ITEM_STATUS_7的记录并更新为KNOWLEDGE_ITEM_STATUS_12（处理中）
     * // 2. 获取jobId。然后调用GetIndexJobStatus 方法，获取Status，根据status的四种状态COMPLETED：执行成功。
     * FAILED：执行失败。
     * RUNNING：执行中。
     * PENDING：等待执行。存入item表的jobStatus字段中 如果为RUNNING或PENDING更新item状态为KNOWLEDGE_ITEM_STATUS_10，如果为COMPLETED则更新为KNOWLEDGE_ITEM_STATUS_11，如果为FAILED则更新为KNOWLEDGE_ITEM_STATUS_11
     * // 3.出现异常则更新状态为KNOWLEDGE_ITEM_STATUS_10，并飞书报警
     */
    @Override
    public void processDocumentStatus() {
        String methodName = "processDocumentStatus";
        // 1. 获取并锁定待处理的文档记录（状态为KNOWLEDGE_ITEM_STATUS_7）
        LambdaQueryWrapper<DigitalVectorItemPO> queryWrapper = new LambdaQueryWrapper<DigitalVectorItemPO>()
                .eq(DigitalVectorItemPO::getStatus, KNOWLEDGE_ITEM_STATUS_7) // 状态为7（待处理）
                .last("LIMIT 1 FOR UPDATE"); // 加锁防止并发处理

        DigitalVectorItemPO itemRecord = digitalVectorItemMapper.selectOne(queryWrapper);
        if (itemRecord == null) {
//            log.info("[{}] 没有待处理的文档任务", methodName);
            return;
        }

        // 更新状态为KNOWLEDGE_ITEM_STATUS_12（处理中）
        itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_12);
        digitalVectorItemMapper.updateById(itemRecord);

        // 2. 根据userId查找DigitalVectorTask记录
        LambdaQueryWrapper<DigitalVectorTaskPO> taskQueryWrapper = new LambdaQueryWrapper<DigitalVectorTaskPO>()
                .eq(DigitalVectorTaskPO::getUserId, itemRecord.getUserId());
        DigitalVectorTaskPO taskRecord = digitalVectorTaskMapper.selectOne(taskQueryWrapper);
        boolean koufeiFlag = false ;
        try {
            String jobId = itemRecord.getJobId();
            if (StringUtils.isBlank(jobId)) {
                throw new RuntimeException("jobId为空");
            }

            log.info("[{}] 开始获取索引任务状态：itemId={}, jobId={}", methodName, itemRecord.getId(), jobId);

            // 2. 调用API获取任务状态
            Result<String> result = getIndexJobStatus(itemRecord, taskRecord);

            if (!result.isSuccess()) {
                throw new RuntimeException("获取索引任务状态失败：" + result.getMessage());
            }

            String status = result.getData();
            log.info("[{}] 索引任务状态：jobId={}, status={}", methodName, jobId, status);

            // 更新item表的jobStatus字段
            itemRecord.setJobStatus(status);

            // 根据不同状态更新item的状态
            switch (status) {
                case "COMPLETED":
                    itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_13); // 执行成功
                    koufeiFlag = feiyongService.kouKownledgeSpace(itemRecord.getUserId(), itemRecord.getId(), "文档处理成功", itemRecord.getFileSize());
                    break;
                case "FAILED":
                    itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_11); // 执行失败
                    BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "文档处理失败", "ERRORInfo=" + status);
                    break;
                case "RUNNING":
                case "PENDING":
                    itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_7); // 执行中/等待执行
                    break;
                default:
                    log.warn("[{}] 未知的任务状态：{}", methodName, status);
                    throw new RuntimeException("未知的任务状态：" + status); // 默认按执行中处理
            }

            digitalVectorItemMapper.updateById(itemRecord);
            log.info("[{}] 文档状态更新成功：itemId={}, newStatus={}", methodName, itemRecord.getId(), itemRecord.getStatus());

        } catch (Exception e) {
            if (koufeiFlag){
                feiyongService.tuiKownledgeSpace(Long.valueOf(itemRecord.getUserId()), itemRecord.getId(), "文档空间退回成功", itemRecord.getFileSize());
            }
            log.error("[{}] 处理文档任务异常", methodName, e);
            // 飞书报警
            BFeiShuUtil.sedCardWarnFromText(BFeiShuUtil.P1, "文档处理失败", "ERRORInfo=" + e.getMessage());

            // 更新状态为KNOWLEDGE_ITEM_STATUS_10（处理失败）
            try {
                itemRecord.setStatus(KNOWLEDGE_ITEM_STATUS_11);
                itemRecord.setErrorMsg(e.getMessage());
                digitalVectorItemMapper.updateById(itemRecord);
            } catch (Exception ex) {
                log.error("[{}] 更新文档失败状态异常", methodName, ex);
            }
        }
    }

    /**
     * 获取索引任务状态
     * @param itemRecord 文档记录
     * @return Result<String> 包含状态的响应对象
     */
    private Result<String> getIndexJobStatus(DigitalVectorItemPO itemRecord, DigitalVectorTaskPO taskPO) {
        return BaiLianUtil.getIndexJobStatus(itemRecord.getJobId(),taskPO.getIndexId());
    }


    /**
     * 推送任务状态更新
     * @param userId 用户ID
     * @param taskId 任务ID
     * @param status 任务状态
     * @param message 状态消息
     */
    private void pushTaskStatus(String userId, String taskId, Integer status, String message) {
        String methodName = "pushTaskStatus";
        log.info("[{}] 开始推送任务状态：userId={}, taskId={}, status={}", methodName, userId, taskId, status);
        
        try {
            // 1. 查询任务信息
            DigitalVideoTaskPO task = digitalVideoTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getTaskId, taskId)
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );
            
            if (task != null) {
                // 2. 如果任务完成或失败，发送通知消息
                if (status != null && (status == VideoTaskStatusEnum.SUCCESS.getValue() || status == VideoTaskStatusEnum.FAILED.getValue())) {
                    log.debug("[{}] 构建通知消息：userId={}, taskId={}, status={}", methodName, userId, taskId, status);
                    
                    // 使用新的数字人专用通知服务
                    int notifType = status == VideoTaskStatusEnum.SUCCESS.getValue() ? 
                        DigitalNotificationEnum.VIDEO_TASK_SUCCESS.getValue() : 
                        DigitalNotificationEnum.VIDEO_TASK_FAIL.getValue();
                    
                    String notifContent = message != null ? message : 
                        (status == VideoTaskStatusEnum.SUCCESS.getValue() ? 
                            "您的数字人视频已生成完成，点击查看" : 
                            "很抱歉，您的数字人视频生成失败，请重试");
                    
                    // 1. 创建通知记录
                    digitalNotificationService.createNotification(
                        Long.valueOf(userId),
                        taskId,
                        notifType,
                        notifContent
                    );
                    
                    // 2. 发送通知消息
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("taskId", taskId);
                    dataMap.put("notifType", notifType);
                    dataMap.put("notifTitle", DigitalNotificationEnum.getDesc(notifType));
                    dataMap.put("notifContent", notifContent);
                    
                    String jsonMessage = MessageSendUtil.getJSONStr(
                        userId,
                        BMessageSendEnum.VIDEO_JOB_DIGITAL_PUSH,
                        dataMap
                    );
                    BRedisServiceUtil.sendMessageDigital(jsonMessage);
                }
            } else {
                log.warn("[{}] 未找到任务信息，无法推送状态: userId={}, taskId={}", methodName, userId, taskId);
            }
        } catch (Exception e) {
            log.error("[{}] 推送任务状态失败：userId={}, taskId={}, error={}", methodName, userId, taskId, e.getMessage(), e);
        }
    }
}
