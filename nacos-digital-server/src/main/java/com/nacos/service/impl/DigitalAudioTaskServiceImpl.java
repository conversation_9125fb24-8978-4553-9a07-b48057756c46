package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.mapper.DigitalAudioTaskMapper;
import com.nacos.result.Result;
import com.nacos.service.IDigitalAudioTaskService;
import com.nacos.entity.vo.DigitalAudioTaskVO;
import com.nacos.entity.vo.PageResultVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;

/**
 * <p>
 * 数字人音频生成任务表PO 服务实现类
 * </p>
 *
 */
@Service
public class DigitalAudioTaskServiceImpl extends ServiceImpl<DigitalAudioTaskMapper, DigitalAudioTaskPO> implements IDigitalAudioTaskService { 

    private static final Logger log = LoggerFactory.getLogger(DigitalAudioTaskServiceImpl.class);

    /**
     * 根据任务ID查找任务
     * @param taskId 任务ID
     * @return 任务实体PO，如果未找到或已删除则可能为null
     */
    @Override
    public DigitalAudioTaskVO findByTaskId(String taskId) {  
        DigitalAudioTaskPO po = this.getOne(new LambdaQueryWrapper<DigitalAudioTaskPO>()
                .eq(DigitalAudioTaskPO::getTaskId, taskId)
                .eq(DigitalAudioTaskPO::getIsDeleted, false));
        if (po == null) {
            return null;
        }
        return convertPoToVo(po);
    }

    /**
     * 根据用户ID查找任务列表
     * @param userId 用户ID
     * @return 任务实体PO列表
     */
    @Override
    public List<DigitalAudioTaskVO> findByUserId(String userId) { 
        List<DigitalAudioTaskPO> pos = this.list(new LambdaQueryWrapper<DigitalAudioTaskPO>()
                .eq(DigitalAudioTaskPO::getUserId, userId)
                .eq(DigitalAudioTaskPO::getIsDeleted, false)
                .orderByDesc(DigitalAudioTaskPO::getCreatedTime));
        if (pos == null || pos.isEmpty()) {
            return new ArrayList<>();
        }
        return pos.stream()
                  .map(this::convertPoToVo)
                  .collect(Collectors.toList());
    }

    private DigitalAudioTaskVO convertPoToVo(DigitalAudioTaskPO po) {
        if (po == null) {
            return null;
        }
        return DigitalAudioTaskVO.builder()
                .taskId(po.getTaskId())
                .userId(po.getUserId())
                .taskName(po.getTaskName())
                .generatedAudioUrl(po.getGeneratedAudioUrl())
                .status(po.getStatus())
                .errorMsg(po.getErrorMsg())
                .durationMs(po.getDurationMs())
                .build();
    }

    /**
     * 创建新的音频生成任务
     * @param taskPO 任务实体PO，应包含必要的初始信息如taskId, userId, requestParamsJson
     * @return 创建成功返回任务ID，失败返回null
     */
    @Override
    @Transactional
    public Result<String> createTask(DigitalAudioTaskPO taskPO) {
        if (taskPO == null || taskPO.getTaskId() == null) {
            log.warn("创建任务失败：任务PO或任务ID为空");
            return Result.ERROR("创建任务失败：任务PO或任务ID为空");
        }
        DigitalAudioTaskVO existingTaskVO = findByTaskId(taskPO.getTaskId());
        if (existingTaskVO != null) {
            log.warn("创建任务失败：任务ID {} 已存在", taskPO.getTaskId());
            return Result.ERROR("创建任务失败：任务ID已存在");
        }

        taskPO.setIsDeleted(false);
        taskPO.setCreatedTime(LocalDateTime.now());
        taskPO.setUpdateTime(LocalDateTime.now());
        if (taskPO.getStatus() == null) {
            taskPO.setStatus(0);
        }

        boolean saved = save(taskPO);
        if (saved) {
            log.info("创建任务成功：taskId={}, userId={}", taskPO.getTaskId(), taskPO.getUserId());
            return Result.SUCCESS("创建任务成功", taskPO.getTaskId());
        } else {
            log.error("创建任务失败：保存到数据库失败, taskId={}", taskPO.getTaskId());
            return Result.ERROR("创建任务失败：保存到数据库失败");
        }
    }

    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 新的状态码
     * @param errorMsg 如果任务失败，则为错误信息；成功则可为null
     * @return true 如果更新成功, false otherwise
     */
    @Override
    @Transactional
    public boolean updateTaskStatus(String taskId, Integer status, String errorMsg) {
        if (taskId == null || status == null) {
            log.warn("更新任务状态失败：任务ID或状态为空. TaskId: {}", taskId);
            return false;
        }
        LambdaUpdateWrapper<DigitalAudioTaskPO> updateWrapper = new LambdaUpdateWrapper<>(); 
        updateWrapper.eq(DigitalAudioTaskPO::getTaskId, taskId)
                     .eq(DigitalAudioTaskPO::getIsDeleted, false) 
                     .set(DigitalAudioTaskPO::getStatus, status)
                     .set(DigitalAudioTaskPO::getUpdateTime, LocalDateTime.now());

        if (errorMsg != null) {
            updateWrapper.set(DigitalAudioTaskPO::getErrorMsg, errorMsg);
        } else {
            updateWrapper.set(DigitalAudioTaskPO::getErrorMsg, null);
        }
        
        return update(updateWrapper); 
    }

    /**
     * 标记任务完成，并记录生成的音频URL和时长
     * @param taskId 任务ID
     * @param generatedAudioUrl 生成的音频URL
     * @param durationMs 音频时长（毫秒）
     * @return true 如果更新成功, false otherwise
     */
    @Override
    @Transactional
    public boolean completeTask(String taskId, String generatedAudioUrl, Integer durationMs) {
        if (taskId == null || generatedAudioUrl == null) {
            log.warn("完成任务失败：任务ID或生成音频URL为空. TaskId: {}", taskId);
            return false;
        }
        Integer successStatus = 2; 

        LambdaUpdateWrapper<DigitalAudioTaskPO> updateWrapper = new LambdaUpdateWrapper<>(); 
        updateWrapper.eq(DigitalAudioTaskPO::getTaskId, taskId)
                     .eq(DigitalAudioTaskPO::getIsDeleted, false)
                     .set(DigitalAudioTaskPO::getStatus, successStatus)
                     .set(DigitalAudioTaskPO::getGeneratedAudioUrl, generatedAudioUrl)
                     .set(DigitalAudioTaskPO::getDurationMs, durationMs)
                     .set(DigitalAudioTaskPO::getErrorMsg, null) 
                     .set(DigitalAudioTaskPO::getUpdateTime, LocalDateTime.now());
        return update(updateWrapper); 
    }

    /**
     * 逻辑删除任务
     * @param taskId 任务ID
     * @return true 如果删除成功
     */
     @Override
    @Transactional
    public boolean deleteTaskLogically(String taskId) {
        if (taskId == null) {
            log.warn("逻辑删除任务失败：任务ID为空");
            return false;
        }
        LambdaUpdateWrapper<DigitalAudioTaskPO> updateWrapper = new LambdaUpdateWrapper<>(); 
        updateWrapper.eq(DigitalAudioTaskPO::getTaskId, taskId)
                     .set(DigitalAudioTaskPO::getIsDeleted, true)
                     .set(DigitalAudioTaskPO::getUpdateTime, LocalDateTime.now());
        return update(updateWrapper); 
    }

    /**
     * 查询任务列表
     * @param params 查询参数（包含分页参数：pageNum、pageSize）
     * @return 包含分页信息和任务列表的结果
     */
    @Override
    public Result<Page<DigitalAudioTaskVO>> listTasks(Map<String, Object> params) {
        try {
            // 获取分页参数
            Integer pageNum = params.get("pageNum") != null ? Integer.parseInt(params.get("pageNum").toString()) : 1;
            Integer pageSize = params.get("pageSize") != null ? Integer.parseInt(params.get("pageSize").toString()) : 10;
            
            // 构建查询条件
            LambdaQueryWrapper<DigitalAudioTaskPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DigitalAudioTaskPO::getIsDeleted, false);
            
            // 添加用户ID过滤条件
            if (params.get("userId") != null) {
                queryWrapper.eq(DigitalAudioTaskPO::getUserId, params.get("userId").toString());
            }
            
            // 处理状态条件：检查是否有状态参数
            if (params.get("status") != null) {
                // 单个状态查询
                queryWrapper.eq(DigitalAudioTaskPO::getStatus, Integer.parseInt(params.get("status").toString()));
            } else {
                // 默认查询"排队中(0)、进行中(1)、生成成功(2)"状态的任务
                queryWrapper.in(DigitalAudioTaskPO::getStatus, Arrays.asList(0, 1, 2));
            }
            
            // 添加状态列表过滤条件（如果提供了多个状态值）
            if (params.get("statusList") != null) {
                queryWrapper.in(DigitalAudioTaskPO::getStatus, params.get("statusList"));
            }
            
            // 按创建时间降序排序
            queryWrapper.orderByDesc(DigitalAudioTaskPO::getCreatedTime);
            
            // 创建分页对象
            Page<DigitalAudioTaskPO> poPage = new Page<>(pageNum, pageSize);
            // 执行分页查询
            poPage = this.page(poPage, queryWrapper);
            
            // 转换为VO列表
            List<DigitalAudioTaskVO> taskVoList = poPage.getRecords().stream()
                    .map(this::convertPoToVo)
                    .collect(Collectors.toList());
            
            // 创建VO分页对象
            Page<DigitalAudioTaskVO> voPage = new Page<>();
            voPage.setRecords(taskVoList);
            voPage.setTotal(poPage.getTotal());
            voPage.setSize(poPage.getSize());
            voPage.setCurrent(poPage.getCurrent());
            voPage.setPages(poPage.getPages());
            
            // 返回结果，包含分页信息
            Result<Page<DigitalAudioTaskVO>> result = Result.SUCCESS("查询成功", voPage);
            log.info("查询任务列表成功，共 {} 条记录，分 {} 页", poPage.getTotal(), poPage.getPages());
            return result;
        } catch (Exception e) {
            log.error("查询任务列表失败：", e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }
} 