package com.nacos.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nacos.entity.bo.SubTaskUpdateBO;
import com.nacos.entity.bo.TaskStatusUpdateBO;
import com.nacos.entity.dto.DigitalTaskItem;
import com.nacos.entity.enums.VideoTaskItemStatusEnum;
import com.nacos.entity.enums.VideoTaskStatusEnum;
import com.nacos.entity.po.DigitalVideoTaskItemPO;
import com.nacos.entity.po.DigitalVideoTaskPO;
import com.nacos.entity.vo.DigitalVideoTaskItemVO;
import com.nacos.entity.vo.DigitalVideoTaskVO;
import com.nacos.entity.vo.DigitalVideoTaskStatusVO;
import com.nacos.exception.E;
import com.nacos.mapper.DigitalVideoTaskItemMapper;
import com.nacos.mapper.DigitalVideoTaskMapper;
import com.nacos.result.Result;
import com.nacos.service.DigitalVideoTaskService;

import cn.hutool.core.lang.UUID;
import com.nacos.utils.DigitalFileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.nacos.constant.CommonConst.VOICE_GENERNA_STATUS_4;

/**
 * 数字人视频生成任务Service实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DigitalVideoTaskServiceImpl implements DigitalVideoTaskService {

    private final DigitalVideoTaskMapper taskMapper;
    private final DigitalVideoTaskItemMapper taskItemMapper;

    /**
     * 创建视频生成任务
     * @param userId 用户ID
     * @param taskItems 任务项列表
     * @param avatarVideoUrlMap
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> createTask(String userId, List<DigitalTaskItem> taskItems, Map<String, String> avatarVideoUrlMap) {
        String methodName = "createTask";
        log.info("[{}] 开始创建视频生成任务，userId：{}，taskItems：{}", methodName, userId, taskItems.size());

        try {
            // 参数验证
            if (StringUtils.isBlank(userId)) {
                log.error("[{}] 用户ID为空", methodName);
                return Result.ERROR("用户ID不能为空");
            }
            if (taskItems == null || taskItems.isEmpty()) {
                log.error("[{}] 任务项列表为空", methodName);
                return Result.ERROR("任务项列表不能为空");
            }

            // 生成任务ID
            String generateTaskId = generateTaskId();
            Date now = new Date();

            // 创建主任务对象
            DigitalVideoTaskPO taskPO = new DigitalVideoTaskPO()
                    .setTaskId(generateTaskId)
                    .setUserId(userId)
                    .setTotalCount(taskItems.size())
                    .setSuccessCount(0)
                    .setFailedCount(0)
                    .setStatus(VideoTaskStatusEnum.QUEUING.getValue())
                    .setCreatedTime(now)
                    .setUpdateTime(now)
                    .setIsDeleted(0);
            // 插入主任务数据
            taskMapper.insert(taskPO);

            // 创建子任务
            for (int i = 0; i < taskItems.size(); i++) {
                DigitalTaskItem item = taskItems.get(i);

                DigitalVideoTaskItemPO itemPO = new DigitalVideoTaskItemPO()
                        .setTaskId(generateTaskId)
                        .setUserId(userId)
                        .setSubTaskId(generateSubTaskId(generateTaskId, i))
                        .setAvatarId(item.getAvatarId())
                        .setTextContent(item.getText())
                        .setSrcVideoUrl(avatarVideoUrlMap.get(item.getAvatarId()))
                        .setVoiceId(item.getVoiceId())
                        .setSpeed(item.getSpeed())
                        .setSrcLang(item.getSrcLang())
                        .setDstLang(item.getDstLang())
                        .setSequence(i)
                        .setStatus(VideoTaskItemStatusEnum.PENDING.getValue())
                        .setCreatedTime(now)
                        .setUpdateTime(now)
                        .setIsDeleted(0);
                //如果item的voiceFile不为空，则上传到oss中
                if (item.getVoiceFile() != null && !item.getVoiceFile().isEmpty()) {
                    String ossUrl = DigitalFileUtil.uploadDigitalResource(item.getVoiceFile(), null, userId, null, 10, true);
                    itemPO.setAudioUrl(ossUrl);
                    Long audioLength = DigitalFileUtil.getAudioLength(item.getVoiceFile());
                    itemPO.setAudioLength(audioLength);
                    itemPO.setVoiceStatus(VOICE_GENERNA_STATUS_4);
                }
                if (item.getVoiceUrl() != null) {
                    itemPO.setAudioUrl(item.getVoiceUrl());
                    Long audioLength = DigitalFileUtil.getAudioLengthByUrl(item.getVoiceUrl());
                    itemPO.setAudioLength(audioLength);
                    itemPO.setVoiceStatus(VOICE_GENERNA_STATUS_4);
                }
                // 插入子任务数据
                taskItemMapper.insert(itemPO);
            }
            log.info("[{}] 创建视频生成任务成功，generateTaskId：{}", methodName, generateTaskId);
            return Result.SUCCESS(generateTaskId);

        } catch (Exception e) {
            log.error("[{}] 创建视频生成任务异常", methodName, e);
            throw new E("创建任务失败：" + e.getMessage());
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        // 使用格式：VT_yyyyMMddHHmm_4位随机数
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        String dateStr = sdf.format(new Date());
        String randomNum = String.format("%04d", (int) (Math.random() * 10000));
        return "VT_" + dateStr + "_" + randomNum;
    }

    /**
     * 生成子任务ID
     */
    private String generateSubTaskId(String taskId, int sequence) {
        return taskId + "_" + sequence;
    }

    /**
     * 查询用户任务列表
     */
    @Override
    public Result<List<DigitalVideoTaskVO>> listUserTasks(String userId) {
        String methodName = "listUserTasks";
        log.info("[{}] 开始查询用户任务列表，userId：{}", methodName, userId);

        try {
            if (StringUtils.isBlank(userId)) {
                log.error("[{}] 用户ID为空", methodName);
                return Result.ERROR("用户ID不能为空");
            }

            // 查询主任务列表
            List<DigitalVideoTaskPO> taskList = taskMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getUserId, userId)
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                            .orderByDesc(DigitalVideoTaskPO::getCreatedTime)
            );

            // 转换为VO对象
            List<DigitalVideoTaskVO> voList = taskList.stream()
                    .map(this::convertToTaskVO)
                    .collect(Collectors.toList());

            log.info("[{}] 查询用户任务列表成功，userId：{}，任务数量：{}", methodName, userId, voList.size());
            return Result.SUCCESS(voList);

        } catch (Exception e) {
            log.error("[{}] 查询用户任务列表异常", methodName, e);
            return Result.ERROR("查询任务列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询任务详情
     */
    @Override
    public Result<DigitalVideoTaskVO> getTaskDetail(String taskId) {
        String methodName = "getTaskDetail";
        log.info("[{}] 开始查询任务详情，taskId：{}", methodName, taskId);

        try {
            if (StringUtils.isBlank(taskId)) {
                log.error("[{}] 任务ID为空", methodName);
                return Result.ERROR("任务ID不能为空");
            }

            // 查询主任务
            DigitalVideoTaskPO taskPO = taskMapper.selectOne(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getTaskId, taskId)
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );

            if (taskPO == null) {
                log.error("[{}] 任务不存在，taskId：{}", methodName, taskId);
                return Result.ERROR("任务不存在");
            }

            // 查询子任务列表
            List<DigitalVideoTaskItemPO> itemList = taskItemMapper.selectList(
                    new LambdaQueryWrapper<DigitalVideoTaskItemPO>()
                            .eq(DigitalVideoTaskItemPO::getTaskId, taskId)
                            .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                            .orderByAsc(DigitalVideoTaskItemPO::getSequence)
            );

            // 转换为VO对象
            DigitalVideoTaskVO taskVO = convertToTaskVO(taskPO);
            taskVO.setItems(itemList.stream()
                    .map(this::convertToItemVO)
                    .collect(Collectors.toList()));

            log.info("[{}] 查询任务详情成功，taskId：{}", methodName, taskId);
            return Result.SUCCESS(taskVO);

        } catch (Exception e) {
            log.error("[{}] 查询任务详情异常", methodName, e);
            return Result.ERROR("查询任务详情失败：" + e.getMessage());
        }
    }

    /**
     * 更新主任务数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateTask(TaskStatusUpdateBO updateBO) {
        String methodName = "updateTask";
        log.info("[{}] 开始更新主任务数据，taskId：{}，status：{}，errorMsg：{}",
                methodName, updateBO.getTaskId(), updateBO.getStatus(), updateBO.getErrorMsg());

        try {
            if (StringUtils.isBlank(updateBO.getTaskId())) {
                log.error("[{}] 任务ID为空", methodName);
                return Result.ERROR("任务ID不能为空");
            }

            if (updateBO.getStatus() == null) {
                log.error("[{}] 状态为空", methodName);
                return Result.ERROR("状态不能为空");
            }

            // 更新任务状态
            LambdaUpdateWrapper<DigitalVideoTaskPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DigitalVideoTaskPO::getTaskId, updateBO.getTaskId())
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                    .set(DigitalVideoTaskPO::getStatus, updateBO.getStatus())
                    .set(updateBO.getVideoUrl() != null, DigitalVideoTaskPO::getVideoUrl, updateBO.getVideoUrl())
                    .set(updateBO.getCoverUrl() != null, DigitalVideoTaskPO::getCoverUrl, updateBO.getCoverUrl())
                    .set(updateBO.getErrorMsg() != null, DigitalVideoTaskPO::getErrorMsg, updateBO.getErrorMsg())
                    .set(DigitalVideoTaskPO::getUpdateTime, new Date());

            int rows = taskMapper.update(null, updateWrapper);
            if (rows > 0) {
                log.info("[{}] 更新主任务数据成功，taskId：{}", methodName, updateBO.getTaskId());
                return Result.SUCCESS(true);
            } else {
                log.error("[{}] 更新主任务数据失败，taskId：{}", methodName, updateBO.getTaskId());
                return Result.ERROR("更新任务数据失败，任务不存在或已删除");
            }
        } catch (Exception e) {
            log.error("[{}] 更新主任务数据异常：{}", methodName, e.getMessage(), e);
            return Result.ERROR("更新任务数据异常：" + e.getMessage());
        }
    }

    /**
     * 更新子任务数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> updateSubTask(SubTaskUpdateBO updateBO) {
        String methodName = "updateSubTask";
        log.info("[{}] 开始更新子任务数据，subTaskId：{}，status：{}，videoUrl：{}，audioUrl：{}，apiJobId：{}，errorMsg：{}",
                methodName, updateBO.getSubTaskId(), updateBO.getStatus(), updateBO.getVideoUrl(),
                updateBO.getAudioUrl(), updateBO.getApiJobId(), updateBO.getErrorMsg());

        try {
            if (StringUtils.isBlank(updateBO.getSubTaskId())) {
                log.error("[{}] 子任务ID为空", methodName);
                return Result.ERROR("子任务ID不能为空");
            }

            if (updateBO.getStatus() == null) {
                log.error("[{}] 状态为空", methodName);
                return Result.ERROR("状态不能为空");
            }

            // 更新子任务状态
            LambdaUpdateWrapper<DigitalVideoTaskItemPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(DigitalVideoTaskItemPO::getSubTaskId, updateBO.getSubTaskId())
                    .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                    .set(DigitalVideoTaskItemPO::getStatus, updateBO.getStatus())
                    .set(updateBO.getVideoUrl() != null, DigitalVideoTaskItemPO::getVideoUrl, updateBO.getVideoUrl())
                    .set(updateBO.getAudioUrl() != null, DigitalVideoTaskItemPO::getAudioUrl, updateBO.getAudioUrl())
                    .set(updateBO.getApiJobId() != null, DigitalVideoTaskItemPO::getApiJobId, updateBO.getApiJobId())
                    .set(updateBO.getErrorMsg() != null, DigitalVideoTaskItemPO::getErrorMsg, updateBO.getErrorMsg())
                    .set(updateBO.getAudioLength() != null, DigitalVideoTaskItemPO::getAudioLength, updateBO.getAudioLength())
                    .set(DigitalVideoTaskItemPO::getUpdateTime, new Date());

            int rows = taskItemMapper.update(null, updateWrapper);
            if (rows > 0) {
                log.info("[{}] 更新子任务数据成功，subTaskId：{}", methodName, updateBO.getSubTaskId());

                boolean isSuccess = false;
                // 如果状态是成功或失败，更新主任务的统计数据
                if (updateBO.getStatus() == VideoTaskItemStatusEnum.AUDIO_FAILED.getValue() ||
                    updateBO.getStatus() == VideoTaskItemStatusEnum.VIDEO_FAILED.getValue()) {
                    isSuccess = false;
                } else {
                    isSuccess = true;
                }
                updateTaskCounts(updateBO.getSubTaskId(), isSuccess);

                return Result.SUCCESS(true);
            } else {
                log.error("[{}] 更新子任务数据失败，subTaskId：{}", methodName, updateBO.getSubTaskId());
                return Result.ERROR("更新子任务数据失败，子任务不存在或已删除");
            }
        } catch (Exception e) {
            log.error("[{}] 更新子任务数据异常：{}", methodName, e.getMessage(), e);
            return Result.ERROR("更新子任务数据异常：" + e.getMessage());
        }
    }

    /**
     * 更新主任务的成功/失败计数
     */
    private void updateTaskCounts(String subTaskId, boolean isSuccess) {
        // 查询子任务获取主任务ID
        DigitalVideoTaskItemPO itemPO = taskItemMapper.selectOne(
                new LambdaQueryWrapper<DigitalVideoTaskItemPO>()
                        .eq(DigitalVideoTaskItemPO::getSubTaskId, subTaskId)
                        .eq(DigitalVideoTaskItemPO::getIsDeleted, 0)
                        .select(DigitalVideoTaskItemPO::getTaskId)
        );

        if (itemPO != null) {
            // 更新主任务计数
            String taskId = itemPO.getTaskId();
            DigitalVideoTaskPO taskPO = taskMapper.selectOne(
                    new LambdaQueryWrapper<DigitalVideoTaskPO>()
                            .eq(DigitalVideoTaskPO::getTaskId, taskId)
                            .eq(DigitalVideoTaskPO::getIsDeleted, 0)
            );

            if (taskPO != null) {
                if (isSuccess) {
                    taskPO.setSuccessCount(taskPO.getSuccessCount() + 1);
                } else {
                    taskPO.setFailedCount(taskPO.getFailedCount() + 1);
                }

                // 根据成功失败数更新状态
                if (taskPO.getSuccessCount() + taskPO.getFailedCount() >= taskPO.getTotalCount()) {
                    if (taskPO.getFailedCount() == 0) {
                        taskPO.setStatus(VideoTaskStatusEnum.SUCCESS.getValue()); // 全部成功
                    } else if (taskPO.getSuccessCount() == 0) {
                        taskPO.setStatus(VideoTaskStatusEnum.FAILED.getValue()); // 全部失败
                    } else {
                        taskPO.setStatus(2); // 部分成功
                    }
                }

                taskMapper.updateById(taskPO);
            }
        }
    }

    /**
     * 将PO对象转换为VO对象（主任务）
     */
    private DigitalVideoTaskVO convertToTaskVO(DigitalVideoTaskPO po) {
        if (po == null) {
            return null;
        }
        DigitalVideoTaskVO vo = new DigitalVideoTaskVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    /**
     * 将PO对象转换为VO对象（子任务）
     */
    private DigitalVideoTaskItemVO convertToItemVO(DigitalVideoTaskItemPO po) {
        if (po == null) {
            return null;
        }
        DigitalVideoTaskItemVO vo = new DigitalVideoTaskItemVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    /**
     * 查询用户任务列表(成功和进行中的任务)
     */
    @Override
    public List<DigitalVideoTaskStatusVO> getActiveTaskList(String userId) {
        String methodName = "getActiveTaskList";
        log.info("[{}] 开始查询用户任务列表，userId：{}", methodName, userId);

        try {
            // 查询排队中、进行中和成功的任务
            List<DigitalVideoTaskPO> tasks = taskMapper.selectList(
                new LambdaQueryWrapper<DigitalVideoTaskPO>()
                    .eq(DigitalVideoTaskPO::getUserId, userId)
                    .eq(DigitalVideoTaskPO::getIsDeleted, 0)
                    .in(DigitalVideoTaskPO::getStatus, 
                        VideoTaskStatusEnum.QUEUING.getValue(),
                        VideoTaskStatusEnum.IN_PROGRESS.getValue(),
                        VideoTaskStatusEnum.VIDEO_SUCCESS.getValue(),
                        VideoTaskStatusEnum.SUCCESS.getValue())
                    .orderByDesc(DigitalVideoTaskPO::getCreatedTime)
            );

            // 转换为VO对象
            return tasks.stream()
                .map(task -> {
                    Integer simpleStatus = convertToSimpleStatus(task.getStatus());
                    return DigitalVideoTaskStatusVO.builder()
                        .taskId(task.getTaskId())
                        .userId(task.getUserId())
                        .status(simpleStatus)
                        .statusDesc(generateDescription(task))
                        .errorMsg(task.getErrorMsg())
                        .videoUrl(task.getVideoUrl())
                        .coverUrl(task.getCoverUrl())
                        .createdTime(task.getCreatedTime())
                        .updateTime(task.getUpdateTime())
                        .progress(calculateProgress(task))
                        .build();
                })
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("[{}] 查询用户任务列表失败：{}", methodName, e.getMessage(), e);
            throw new RuntimeException("查询任务列表失败：" + e.getMessage());
        }
    }

    /**
     * 计算任务进度
     * @param task 任务实体
     * @return 进度值（0-100）
     */
    private Integer calculateProgress(DigitalVideoTaskPO task) {
        if (task.getStatus().equals(VideoTaskStatusEnum.SUCCESS.getValue())) {
            return 100;
        } else if (task.getStatus().equals(VideoTaskStatusEnum.IN_PROGRESS.getValue())) {
            // TODO: 这里可以根据实际业务逻辑计算具体进度
            // 获取成功和失败的子任务数量
            int successCount = task.getSuccessCount();
            int failedCount = task.getFailedCount();
            int totalCount = task.getTotalCount();
            
            // 计算进度
            if (totalCount == 0) {
                return 0;
            }
            return (successCount + failedCount) * 100 / totalCount;
        }
        return 0;
    }

    /**
     * 生成任务描述
     * @param task 任务实体
     * @return 描述文本
     */
    private String generateDescription(DigitalVideoTaskPO task) {
        // 当状态为4（成功）时返回1，其他状态（1,2,3）返回0
        if (task.getStatus().equals(VideoTaskStatusEnum.SUCCESS.getValue())) {
            return "完成";
        } else {
            return "生成中";
        }
    }

    /**
     * 将状态转换为简化状态（0-生成中，1-完成）
     */
    private Integer convertToSimpleStatus(Integer originalStatus) {
        return originalStatus.equals(VideoTaskStatusEnum.SUCCESS.getValue()) ? 1 : 0;
    }
}