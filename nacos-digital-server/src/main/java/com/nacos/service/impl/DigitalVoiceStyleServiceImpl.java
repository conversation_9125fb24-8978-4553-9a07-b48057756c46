package com.nacos.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nacos.cache.VoiceInfoCache;
import com.nacos.entity.po.DigitalVoiceStylesPO;
import com.nacos.entity.vo.DigitalVoiceStyleVO;
import com.nacos.event.VoiceInfoSyncEvent;
import com.nacos.mapper.DigitalVoiceInfoMapper;
import com.nacos.model.AzureAudio.AzureAudioApiUtil;
import com.nacos.model.Elevenlabs.ElevenLabsApiUtil;
import com.nacos.model.Elevenlabs.model.ElevenLabsVoiceListResponseBO;
import com.nacos.model.MiniMax.MiniMaxApiUtil;
import com.nacos.model.MiniMax.model.MiniMaxGetVoiceIdResponseBO;
import com.nacos.result.Result;
import com.nacos.service.DigitalVoiceStyleService;
import com.nacos.service.VoiceDualWriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DigitalVoiceStyleServiceImpl extends ServiceImpl<DigitalVoiceInfoMapper, DigitalVoiceStylesPO> implements DigitalVoiceStyleService {

    @Autowired
    private VoiceInfoCache voiceInfoCache;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    @Autowired
    private DigitalVoiceInfoMapper digitalVoiceInfoMapper;

    @Autowired
    private VoiceDualWriteService voiceDualWriteService;

    // Azure语音服务配置
    @Value("${azure.speech.subscription-key:}")
    private String azureSubscriptionKey;
    
    // Azure语音服务区域
    @Value("${azure.speech.region:}")
    private String azureRegion;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> syncVoiceStyles() {
        String methodName = "syncVoiceStyles";
        try {
            // 同步结果
            List<String> syncResults = new ArrayList<>();
            
            // 1. 同步 Minimax 语音
            try {
                Result<String> minimaxResult = syncMinimaxVoiceStyles();
                syncResults.add(minimaxResult.getMessage());
            } catch (Exception e) {
                log.error("[{}]同步Minimax语音数据失败：{}", methodName, e.getMessage(), e);
                syncResults.add("同步Minimax语音数据失败：" + e.getMessage());
            }
            
            // 2. 同步 Microsoft 语音
            try {
                Result<String> microsoftResult = syncMicrosoftVoiceStyles();
                syncResults.add(microsoftResult.getMessage());
            } catch (Exception e) {
                log.error("[{}]同步Microsoft语音数据失败：{}", methodName, e.getMessage(), e);
                syncResults.add("同步Microsoft语音数据失败：" + e.getMessage());
            }

            // 3. 同步 ElevenLabs 语音
            try {
                Result<String> elevenLabsResult = syncElevenLabsVoiceStyles();
                syncResults.add(elevenLabsResult.getMessage());
            } catch (Exception e) {
                log.error("[{}]同步ElevenLabs语音数据失败：{}", methodName, e.getMessage(), e);
                syncResults.add("同步ElevenLabs语音数据失败：" + e.getMessage());
            }

            // 4. 更新缓存
            updateVoiceInfoCache();
            
            // 返回结果
            return Result.SUCCESS(String.join("\n", syncResults));
        } catch (Exception e) {
            log.error("[{}]同步语音数据发生未知异常：{}", methodName, e.getMessage(), e);
            return Result.ERROR("系统异常，请稍后重试");
        }
    }
    
    /**
     * 同步Minimax语音数据
     * @return 同步结果\\
     * ;.
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> syncMinimaxVoiceStyles() {
        String methodName = "syncMinimaxVoiceStyles";
        try {
            // 调用工具类方法
            MiniMaxGetVoiceIdResponseBO responseBO = MiniMaxApiUtil.getVoiceId("all");

            if (responseBO == null || responseBO.getBaseResp() == null
                    || responseBO.getBaseResp().getStatusCode() != 0) {
                return Result.ERROR("获取Minimax音色风格失败");
            }
            
            int newCount = 0;
            int updateCount = 0;
            
            // 处理系统音色
            if (responseBO.getSystemVoice() != null) {
                for (MiniMaxGetVoiceIdResponseBO.SystemVoice voice : responseBO.getSystemVoice()) {
                    // 查询是否已存在
                    DigitalVoiceStylesPO existingVoice = digitalVoiceInfoMapper.selectOne(
                        new LambdaQueryWrapper<DigitalVoiceStylesPO>()
                            .eq(DigitalVoiceStylesPO::getVoiceId, voice.getVoiceId())
                    );
                    
                    if (existingVoice == null) {
                        // 新增音色
                        DigitalVoiceStylesPO voiceStyles = new DigitalVoiceStylesPO();
                        voiceStyles.setVoiceName(voice.getVoiceName());
                        voiceStyles.setVoiceType(1); // 系统音色
                        voiceStyles.setVoiceId(voice.getVoiceId()); // 系统音色需要使用API返回的voiceId
                        voiceStyles.setProvider("MINIMAX"); // 设置供应商为MINIMAX
                        voiceStyles.setDescription(voice.getDescription());
                        // createdTime会自动填充
                        // updateTime会自动填充
                        // lastSyncTime会自动填充
                        // isDeleted会自动设置默认值0
                        digitalVoiceInfoMapper.insert(voiceStyles);

                        // 双写到新表
                        try {
                            voiceDualWriteService.dualWriteVoice(voiceStyles);
                        } catch (Exception e) {
                            log.warn("双写音色数据失败，voiceId: {}", voiceStyles.getVoiceId(), e);
                        }

                        newCount++;
                    } else {
                        // 更新音色，但保留示例音频
                        existingVoice.setVoiceName(voice.getVoiceName());
                        existingVoice.setProvider("MINIMAX"); // 确保供应商标识正确
                        existingVoice.setDescription(voice.getDescription());
                        // updateTime和lastSyncTime会由MyMetaObjectHandler自动填充
                        digitalVoiceInfoMapper.updateById(existingVoice);

                        // 双写更新到新表
                        try {
                            voiceDualWriteService.dualUpdateVoice(existingVoice);
                        } catch (Exception e) {
                            log.warn("双写更新音色数据失败，voiceId: {}", existingVoice.getVoiceId(), e);
                        }

                        updateCount++;
                    }
                }
            }
            
            // 处理克隆音色
            if (responseBO.getVoiceCloning() != null) {
                for (MiniMaxGetVoiceIdResponseBO.VoiceCloning voice : responseBO.getVoiceCloning()) {
                    // 查询是否已存在
                    DigitalVoiceStylesPO existingVoice = digitalVoiceInfoMapper.selectOne(
                        new LambdaQueryWrapper<DigitalVoiceStylesPO>()
                            .eq(DigitalVoiceStylesPO::getVoiceId, voice.getVoiceId())
                    );
                    
                    if (existingVoice == null) {
                        // 新增音色
                        DigitalVoiceStylesPO voiceStyles = new DigitalVoiceStylesPO();
                        voiceStyles.setVoiceType(2); // 克隆音色
                        voiceStyles.setVoiceId(voice.getVoiceId()); // 克隆音色需要使用API返回的voiceId
                        voiceStyles.setProvider("MINIMAX"); // 设置供应商为MINIMAX
                        // 设置描述（如果有）
                        if (voice.getDescription() != null && !voice.getDescription().isEmpty()) {
                            // 处理List<String>类型的描述
                            voiceStyles.setDescription(String.join(", ", voice.getDescription()));
                        }
                        // createdTime会自动填充
                        // updateTime会自动填充
                        // lastSyncTime会自动填充
                        // isDeleted会自动设置默认值0
                        digitalVoiceInfoMapper.insert(voiceStyles);

                        // 双写到新表
                        try {
                            voiceDualWriteService.dualWriteVoice(voiceStyles);
                        } catch (Exception e) {
                            log.warn("双写克隆音色数据失败，voiceId: {}", voiceStyles.getVoiceId(), e);
                        }

                        newCount++;
                    } else {
                        // 更新音色，但保留示例音频
                        existingVoice.setProvider("MINIMAX"); // 确保供应商标识正确
                        // 设置描述（如果有）
                        if (voice.getDescription() != null && !voice.getDescription().isEmpty()) {
                            // 处理List<String>类型的描述
                            existingVoice.setDescription(String.join(", ", voice.getDescription()));
                        }
                        // updateTime和lastSyncTime会由MyMetaObjectHandler自动填充
                        digitalVoiceInfoMapper.updateById(existingVoice);
                        updateCount++;
                    }
                }
            }
            
            log.info("[{}]同步Minimax语音列表完成：新增{}个，更新{}个", methodName, newCount, updateCount);
            return Result.SUCCESS(String.format("同步Minimax语音列表成功，新增%d个，更新%d个", newCount, updateCount));
        } catch (Exception e) {
            log.error("[{}]同步Minimax语音列表失败：{}", methodName, e.getMessage(), e);
            return Result.ERROR("同步Minimax语音列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 同步Microsoft语音数据
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> syncMicrosoftVoiceStyles() {
        String methodName = "syncMicrosoftVoiceStyles";
        try {
            // 检查Azure配置是否有效
            if (azureSubscriptionKey == null || azureRegion == null ||
                azureSubscriptionKey.trim().isEmpty() || azureRegion.trim().isEmpty()) {
                log.warn("[{}]Azure语音服务配置无效", methodName);
                return Result.ERROR("Azure语音服务配置无效");
            }
            
            // 调用Azure API获取语音列表
            Result<String> voicesResult = AzureAudioApiUtil.getVoicesList(azureSubscriptionKey, azureRegion);
            
            if (!voicesResult.isSuccess() || voicesResult.getData() == null) {
                log.warn("[{}]未获取到Microsoft语音列表：{}", methodName, voicesResult.getMessage());
                return Result.ERROR("未获取到Microsoft语音列表：" + voicesResult.getMessage());
            }
            
            // 解析返回的JSON字符串
            List<JSONObject> voicesList;
            try {
                // AzureApiUtil返回的是JSON数组字符串，直接解析为数组
                voicesList = JSON.parseArray(voicesResult.getData(), JSONObject.class);
            } catch (Exception e) {
                log.error("[{}]解析语音列表JSON失败：{}", methodName, e.getMessage(), e);
                return Result.ERROR("解析语音列表数据失败");
            }
            
            log.info("[{}]成功获取Microsoft语音列表，共{}个语音", methodName, voicesList.size());
            
            // 处理语音列表数据并保存到数据库
            int newCount = 0;
            int updateCount = 0;
            
            for (JSONObject voice : voicesList) {
                try {
                    // 获取必要字段
                    String voiceId = voice.getString("shortName");
                    if (voiceId == null) {
                        voiceId = voice.getString("Name"); // 尝试替代字段
                    }
                    
                    String voiceName = voice.getString("displayName");
                    if (voiceName == null) {
                        voiceName = voice.getString("DisplayName"); // 尝试替代字段
                    }
                    
                    String locale = voice.getString("locale");
                    if (locale == null) {
                        locale = voice.getString("Locale"); // 尝试替代字段
                    }
                    
                    String gender = voice.getString("gender");
                    if (gender == null) {
                        gender = voice.getString("Gender"); // 尝试替代字段
                    }
                    
                    String voiceType = voice.getString("voiceType");
                    if (voiceType == null) {
                        voiceType = voice.getString("VoiceType"); // 尝试替代字段
                    }
                    
                    // 如果关键字段缺失，跳过此语音
                    if (voiceId == null || voiceName == null) {
                        log.warn("[{}]语音数据缺少关键字段，跳过：{}", methodName, voice.toJSONString());
                        continue;
                    }
                    
                    // 获取描述和属性
                    String description = voice.getString("description");
                    String attributes = voice.getString("attributes");
                    
                    // 查询是否已存在
                    DigitalVoiceStylesPO existingVoice = digitalVoiceInfoMapper.selectOne(
                        new LambdaQueryWrapper<DigitalVoiceStylesPO>()
                            .eq(DigitalVoiceStylesPO::getVoiceId, voiceId)
                    );
                    
                    if (existingVoice == null) {
                        // 新增音色
                        DigitalVoiceStylesPO voiceStyles = new DigitalVoiceStylesPO();
                        voiceStyles.setVoiceId(voiceId);
                        voiceStyles.setProvider("MICROSOFT");
                        voiceStyles.setVoiceName(voiceName);
                        voiceStyles.setVoiceType(1); // 系统音色
                        
                        // 使用生成的详细描述
                        if (StringUtils.hasText(description)) {
                            voiceStyles.setDescription(description);
                        } else {
                            voiceStyles.setDescription("Microsoft Azure 语音: " + voiceName);
                        }
                        
                        // 设置语言和性别
                        voiceStyles.setLanguage(locale);
                        voiceStyles.setGender(gender);
                        
                        // 设置属性
                        if (StringUtils.hasText(attributes)) {
                            voiceStyles.setAttributes(attributes);
                        } else {
                            // 构建attributes JSON
                            JSONObject attributesObj = new JSONObject();
                            if (StringUtils.hasText(voiceType)) {
                                attributesObj.put("voiceType", voiceType);
                            }
                            
                            if (!attributesObj.isEmpty()) {
                                voiceStyles.setAttributes(attributesObj.toJSONString());
                            }
                        }
                        
                        // createdTime会自动填充
                        // updateTime会自动填充
                        // lastSyncTime会自动填充
                        // isDeleted会自动设置默认值0
                        digitalVoiceInfoMapper.insert(voiceStyles);

                        // 双写到新表
                        try {
                            voiceDualWriteService.dualWriteVoice(voiceStyles);
                        } catch (Exception e) {
                            log.warn("双写Microsoft音色数据失败，voiceId: {}", voiceStyles.getVoiceId(), e);
                        }

                        newCount++;
                    } else {
                        // 更新音色，但保留示例音频
                        existingVoice.setVoiceName(voiceName);
                        existingVoice.setProvider("MICROSOFT");
                        existingVoice.setLanguage(locale);
                        existingVoice.setGender(gender);
                        
                        // 使用生成的详细描述
                        if (StringUtils.hasText(description)) {
                            existingVoice.setDescription(description);
                        }
                        
                        // 设置属性
                        if (StringUtils.hasText(attributes)) {
                            existingVoice.setAttributes(attributes);
                        } else {
                            // 构建attributes JSON
                            JSONObject attributesObj = new JSONObject();
                            if (StringUtils.hasText(voiceType)) {
                                attributesObj.put("voiceType", voiceType);
                            }
                            
                            if (!attributesObj.isEmpty()) {
                                existingVoice.setAttributes(attributesObj.toJSONString());
                            }
                        }
                        
                        // updateTime和lastSyncTime会由MyMetaObjectHandler自动填充
                        digitalVoiceInfoMapper.updateById(existingVoice);

                        // 双写更新到新表
                        try {
                            voiceDualWriteService.dualUpdateVoice(existingVoice);
                        } catch (Exception e) {
                            log.warn("双写更新Microsoft音色数据失败，voiceId: {}", existingVoice.getVoiceId(), e);
                        }

                        updateCount++;
                    }
                } catch (Exception e) {
                    log.warn("[{}]处理单个语音数据失败：{}", methodName, e.getMessage());
                    // 继续处理下一个语音
                }
            }
            
            log.info("[{}]同步Microsoft语音列表完成：新增{}个，更新{}个", methodName, newCount, updateCount);
            
            // 更新缓存
            updateVoiceInfoCache();
            
            return Result.SUCCESS(String.format("同步Microsoft语音列表成功，新增%d个，更新%d个", newCount, updateCount));
        } catch (Exception e) {
            log.error("[{}]同步Microsoft语音列表失败：{}", methodName, e.getMessage(), e);
            return Result.ERROR("同步Microsoft语音列表失败：" + e.getMessage());
        }
    }

    /**
     * 同步ElevenLabs语音数据
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> syncElevenLabsVoiceStyles() {
        String methodName = "syncElevenLabsVoiceStyles";
        try {
            // 调用工具类方法
            List<ElevenLabsVoiceListResponseBO.Voice> voices = ElevenLabsApiUtil.getAllVoices();

            if (voices == null || voices.isEmpty()) {
                log.warn("[{}]未获取到ElevenLabs语音数据", methodName);
                return Result.ERROR("未获取到ElevenLabs语音数据");
            }

            int newCount = 0;
            int updateCount = 0;

            for (ElevenLabsVoiceListResponseBO.Voice voice : voices) {
                try {
                    // 验证语音数据有效性
                    if (!ElevenLabsApiUtil.isValidVoice(voice)) {
                        log.warn("[{}]跳过无效语音数据: {}", methodName, voice);
                        continue;
                    }

                    // 检查语音是否已存在
                    DigitalVoiceStylesPO existingVoice = this.getOne(
                        new LambdaQueryWrapper<DigitalVoiceStylesPO>()
                            .eq(DigitalVoiceStylesPO::getVoiceId, voice.getVoiceId())
                            .eq(DigitalVoiceStylesPO::getProvider, "ELEVENLABS")
                            .eq(DigitalVoiceStylesPO::getIsDeleted, 0)
                    );

                    if (existingVoice == null) {
                        // 新增语音
                        DigitalVoiceStylesPO voiceStyle = ElevenLabsApiUtil.convertToDigitalVoiceStyle(voice);
                        if (voiceStyle != null) {
                            digitalVoiceInfoMapper.insert(voiceStyle);

                            // 双写到新表
                            try {
                                voiceDualWriteService.dualWriteVoice(voiceStyle);
                            } catch (Exception e) {
                                log.warn("双写ElevenLabs音色数据失败，voiceId: {}", voiceStyle.getVoiceId(), e);
                            }

                            newCount++;
                        }
                    } else {
                        // 更新语音，转换新数据并保留现有的示例音频
                        DigitalVoiceStylesPO updatedVoice = ElevenLabsApiUtil.convertToDigitalVoiceStyle(voice);
                        if (updatedVoice != null) {
                            // 保留原有ID和示例音频
                            updatedVoice.setId(existingVoice.getId());
                            if (StringUtils.hasText(existingVoice.getDemoAudio())) {
                                updatedVoice.setDemoAudio(existingVoice.getDemoAudio());
                            }

                            // updateTime和lastSyncTime会由MyMetaObjectHandler自动填充
                            digitalVoiceInfoMapper.updateById(updatedVoice);

                            // 双写更新到新表
                            try {
                                voiceDualWriteService.dualUpdateVoice(updatedVoice);
                            } catch (Exception e) {
                                log.warn("双写更新ElevenLabs音色数据失败，voiceId: {}", updatedVoice.getVoiceId(), e);
                            }

                            updateCount++;
                        }
                    }
                } catch (Exception e) {
                    log.warn("[{}]处理单个语音数据失败：{}", methodName, e.getMessage());
                    // 继续处理下一个语音
                }
            }

            log.info("[{}]同步ElevenLabs语音列表完成：新增{}个，更新{}个", methodName, newCount, updateCount);
            return Result.SUCCESS(String.format("同步ElevenLabs语音列表成功，新增%d个，更新%d个", newCount, updateCount));
        } catch (Exception e) {
            log.error("[{}]同步ElevenLabs语音列表失败：{}", methodName, e.getMessage(), e);
            return Result.ERROR("同步ElevenLabs语音列表失败：" + e.getMessage());
        }
    }

    /**
     * 更新语音信息缓存
     */
    private void updateVoiceInfoCache() {
        // 查询所有未删除的语音信息
        List<DigitalVoiceStylesPO> allVoices = this.list(
            new LambdaQueryWrapper<DigitalVoiceStylesPO>()
                .eq(DigitalVoiceStylesPO::getIsDeleted, 0)
        );
        
        // 更新缓存
        voiceInfoCache.updateCache(allVoices);
        log.info("语音信息缓存已更新，共{}条数据", allVoices.size());
    }

    @Override
    public DigitalVoiceStylesPO getVoiceInfo(String voiceId) {
        // 如果缓存未初始化，触发同步事件
        if (!voiceInfoCache.isInitialized()) {
            eventPublisher.publishEvent(new VoiceInfoSyncEvent(this, false));
        }
        return voiceInfoCache.getVoiceInfo(voiceId);
    }

    @Override
    public List<DigitalVoiceStylesPO> getAllVoiceInfo() {
        // 如果缓存未初始化，触发同步事件
        if (!voiceInfoCache.isInitialized()) {
            eventPublisher.publishEvent(new VoiceInfoSyncEvent(this, false));
        }
        return voiceInfoCache.getAllVoiceInfo();
    }
    
    /**
     * 监听同步事件
     */
    @EventListener
    public void handleVoiceInfoSyncEvent(VoiceInfoSyncEvent event) {
        if (event.isForceSync() || !voiceInfoCache.isInitialized()) {
            syncVoiceStyles();
        }
    }

    @Override
    public List<DigitalVoiceStyleVO> getVoiceStylesByIds(List<String> voiceIds) {
        if (voiceIds == null || voiceIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 查询音色信息
        List<DigitalVoiceStylesPO> voiceStyles = this.list(
            new LambdaQueryWrapper<DigitalVoiceStylesPO>()
                .in(DigitalVoiceStylesPO::getVoiceId, voiceIds)
                .eq(DigitalVoiceStylesPO::getIsDeleted, 0)
        );
        
        // 转换为VO
        return voiceStyles.stream().map(po -> {
            DigitalVoiceStyleVO vo = new DigitalVoiceStyleVO();
            BeanUtils.copyProperties(po, vo);
            return vo;
        }).collect(Collectors.toList());
    }
} 