package com.nacos.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nacos.entity.po.DigitalAvatarCategoryPO;
import com.nacos.entity.po.DigitalSystemGroupPO;
import com.nacos.entity.vo.DigitalAvatarCategoryVO;
import com.nacos.entity.vo.DigitalSystemGroupVO;
import com.nacos.result.Result;
import java.util.List;

public interface DigitalAvatarCategoryService extends IService<DigitalAvatarCategoryPO> {
    
    /**
     * 获取所有分类列表
     * @return 分类列表
     */
    Result<List<DigitalAvatarCategoryVO>> listCategories();
    
    /**
     * 添加分类
     * @param categoryVO 分类信息
     * @return 操作结果
     */
    Result<Boolean> addCategory(DigitalAvatarCategoryVO categoryVO);
    
    /**
     * 更新分类
     * @param categoryVO 分类信息
     * @return 操作结果
     */
    Result<Boolean> updateCategory(DigitalAvatarCategoryVO categoryVO);
    
    /**
     * 删除分类
     * @param id 分类ID
     * @return 操作结果
     */
    Result<Boolean> deleteCategory(Long id);
    
    /**
     * 更新分类状态
     * @param id 分类ID
     * @param status 状态
     * @return 操作结果
     */
    Result<Boolean> updateStatus(Long id, Integer status);

    /**
     * 根据分类编码获取系统组列表
     * @param categoryCode 分类编码
     * @return 系统组列表
     */
    Result<List<DigitalSystemGroupVO>> listSystemGroupsByCategoryCode(String categoryCode);
} 