package com.nacos.service;

import com.nacos.entity.po.DigitalAudioTaskPO;

/**
 * 异步数字音频服务接口
 * 负责处理音频生成任务的异步执行
 * 
 */
public interface AsyncDigitalAudioService {

    /**
     * 初始化线程池
     */
    void init();

    /**
     * 异步处理音频生成任务
     * 
     * @param taskPO 任务PO对象
     */
    void processAudioGenerationAsync(DigitalAudioTaskPO taskPO);

    /**
     * 处理排队中的音频生成任务
     */
    void processQueuingAudioTasks();

    /**
     * 处理超时的音频生成任务
     */
    void processTimeoutAudioTasks();

    /**
     * 优雅地关闭任务线程池
     */
    void shutdown();
}
