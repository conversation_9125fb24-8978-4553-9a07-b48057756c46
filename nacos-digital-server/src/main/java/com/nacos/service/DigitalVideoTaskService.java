package com.nacos.service;

import com.nacos.entity.bo.SubTaskUpdateBO;
import com.nacos.entity.bo.TaskStatusUpdateBO;
import com.nacos.entity.dto.DigitalTaskItem;
import com.nacos.entity.vo.DigitalVideoTaskVO;
import com.nacos.entity.vo.DigitalVideoTaskStatusVO;
import com.nacos.result.Result;

import java.util.List;
import java.util.Map;

/**
 * 数字人视频生成任务Service接口
 */
public interface DigitalVideoTaskService {

    /**
     * 创建视频生成任务
     *
     * @param userId 用户ID
     * @param taskItems 任务项列表
     * @return 任务ID
     */
    Result<String> createTask(String userId, List<DigitalTaskItem> taskItems, Map<String, String> avatarVideoUrlMap);

    /**
     * 查询用户的视频生成任务列表
     *
     * @param userId 用户ID
     * @return 任务列表
     */
    Result<List<DigitalVideoTaskVO>> listUserTasks(String userId);

    /**
     * 查询任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情，包含子任务列表
     */
    Result<DigitalVideoTaskVO> getTaskDetail(String taskId);

    /**
     * 更新主任务数据（使用BO对象）
     *
     * @param updateBO 主任务数据更新BO
     * @return 更新结果
     */
    Result<Boolean> updateTask(TaskStatusUpdateBO updateBO);

    /**
     * 更新子任务数据（使用BO对象）
     *
     * @param updateBO 子任务数据更新BO
     * @return 更新结果
     */
    Result<Boolean> updateSubTask(SubTaskUpdateBO updateBO);

    /**
     * 获取用户的进行中和成功的任务列表
     * @param userId 用户ID
     * @return 任务状态列表
     */
    List<DigitalVideoTaskStatusVO> getActiveTaskList(String userId);
}