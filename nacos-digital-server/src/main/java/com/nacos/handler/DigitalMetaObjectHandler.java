package com.nacos.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.function.Supplier;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 数字人模块的字段自动填充处理器
 * 用于处理数字人相关表的字段自动填充
 * 包括：时间字段、ID字段、默认值等
 */
@Primary
@Component("digitalMetaObjectHandler")
@Slf4j
public class DigitalMetaObjectHandler implements MetaObjectHandler {
    
    private static final int RETRY_TIMES = 3;
    private static final String DIGITAL_ENTITY_PACKAGE = "com.nacos.entity.po";
    private static final AtomicInteger VOICE_ID_SEQUENCE = new AtomicInteger(1);
    
    @Override
    public void insertFill(MetaObject metaObject) {
        // 只处理数字人模块的实体类
        if (!isDigitalEntity(metaObject)) {
            return;
        }
        
        log.info("开始进行字段自动填充....");
        
        // 处理时间字段
        LocalDateTime now = LocalDateTime.now();
        this.strictInsertFill(metaObject, "createdTime", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "lastSyncTime", LocalDateTime.class, now);
        
        // 处理各种ID字段
        // 使用雪花算法的ID
        fillIdIfNull(metaObject, "userId", this::generateSnowflakeId);
        fillIdIfNull(metaObject, "groupId", this::generateSnowflakeId);
        fillIdIfNull(metaObject, "avatarId", this::generateSnowflakeId);
        fillIdIfNull(metaObject, "videoId", this::generateSnowflakeId);
        fillIdIfNull(metaObject, "categoryId", this::generateSnowflakeId);
        fillIdIfNull(metaObject, "tagId", this::generateSnowflakeId);
        
        // 使用符合规范的voiceId
        fillIdIfNull(metaObject, "voiceId", this::generateVoiceId);
        
        // 处理默认值
        fillDefaultValueIfNull(metaObject, "status", () -> 1);
        fillDefaultValueIfNull(metaObject, "isDeleted", () -> 0);
        fillDefaultValueIfNull(metaObject, "sort", () -> 0);
        fillDefaultValueIfNull(metaObject, "sortOrder", () -> 0);
    }
    
    @Override
    public void updateFill(MetaObject metaObject) {
        // 只处理数字人模块的实体类
        if (!isDigitalEntity(metaObject)) {
            return;
        }
        
//        log.info("开始进行数字人模块更新填充....");
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    }
    
    /**
     * 判断是否为数字人模块的实体类
     */
    private boolean isDigitalEntity(MetaObject metaObject) {
        String className = metaObject.getOriginalObject().getClass().getName();
        return className.startsWith(DIGITAL_ENTITY_PACKAGE);
    }
    
    /**
     * 如果字段为空则填充ID
     */
    private <T> void fillIdIfNull(MetaObject metaObject, String fieldName, Supplier<T> idGenerator) {
        Object fieldValue = getFieldValByName(fieldName, metaObject);
        if (fieldValue == null) {
            for (int i = 0; i < RETRY_TIMES; i++) {
                try {
                    T value = idGenerator.get();
                    setFieldValByName(fieldName, value, metaObject);
                    break;
                } catch (Exception e) {
                    if (i == RETRY_TIMES - 1) {
                        log.error("生成ID失败: {}", fieldName, e);
                        throw new RuntimeException("生成ID失败: " + fieldName, e);
                    }
                }
            }
        }
    }
    
    /**
     * 如果字段为空则填充默认值
     */
    private <T> void fillDefaultValueIfNull(MetaObject metaObject, String fieldName, Supplier<T> defaultValue) {
        Object fieldValue = getFieldValByName(fieldName, metaObject);
        if (fieldValue == null) {
            T value = defaultValue.get();
            setFieldValByName(fieldName, value, metaObject);
        }
    }
    
    /**
     * 生成雪花算法ID
     */
    private String generateSnowflakeId() {
        return String.valueOf(IdWorker.getId());
    }
    
    /**
     * 生成符合规范的voiceId
     * 规则：
     * 1. 长度范围[8,256]
     * 2. 首字符必须为英文字母
     * 3. 允许数字、字母、-、_
     * 4. 末位字符不可为-、_
     * 示例：MiniMax001
     */
    private String generateVoiceId() {
        // 使用前缀 "MiniMax" 确保首字符为英文字母
        // 添加13位时间戳确保唯一性和足够长度
        // 添加3位序列号避免同一毫秒内的冲突
        // 最终长度为 "MiniMax" (7位) + 时间戳 (13位) + 序列号 (3位) = 23位
        return String.format("MiniMax%d%03d", 
            System.currentTimeMillis(), 
            VOICE_ID_SEQUENCE.getAndIncrement() % 1000);
    }
}