package com.nacos.model.TXaPaas.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Map;
import java.util.HashMap;

@Data
@NoArgsConstructor
@Schema(title = "腾讯智绘AI PaaS视频制作(视频免训练)请求体")
public class TxAIPaasVideoMakeNoTrainRequest {

    @JSONField(name = "Header")
    @Schema(title = "请求头部，通常为空对象")
    private Map<String, Object> Header;

    @JSONField(name = "Payload")
    @Schema(title = "请求的业务数据")
    private RequestPayload Payload;

    public TxAIPaasVideoMakeNoTrainRequest(RequestPayload payload) {
        this.Header = new HashMap<>();
        this.Payload = payload;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(title = "视频制作(视频免训练)请求Payload")
    public static class RequestPayload {
        @JSONField(name = "RefVideoUrl")
        @Schema(title = "模板视频 URL", required = true)
        private String RefVideoUrl;

        @JSONField(name = "DriverType")
        @Schema(title = "驱动类型 (Text, OriginalVoice)", required = true)
        private String DriverType;

        @JSONField(name = "IdentityWrittenUrl")
        @Schema(title = "PDF格式授权书URL")
        private String IdentityWrittenUrl;

        @JSONField(name = "IdentityVideoUrl")
        @Schema(title = "MP4格式视频授权书URL")
        private String IdentityVideoUrl;

        @JSONField(name = "InputAudioUrl")
        @Schema(title = "驱动数智人的音频 URL (DriverType为OriginalVoice时必填)")
        private String InputAudioUrl;

        @JSONField(name = "InputSsml")
        @Schema(title = "播报文本内容 (SSML, DriverType为Text时必填)")
        private String InputSsml;

        @JSONField(name = "SpeechParam")
        @Schema(title = "定义音频的详细参数 (DriverType为Text时必填)")
        private SpeechParam SpeechParam;

        @JSONField(name = "ConcurrencyType")
        @Schema(title = "视频制作任务使用的资源类型 (Exclusive, Shared)")
        private String ConcurrencyType;

        @JSONField(name = "VideoLoop")
        @Schema(title = "音频长于视频时的对齐方式 (0:反向拼接, 1:正向拼接)", defaultValue = "0")
        private Integer VideoLoop;

        @JSONField(name = "VideoParametersConsistent")
        @Schema(title = "视频输出标准对齐选项 (0:不强制对齐, 1:对齐输入)", defaultValue = "0")
        private Integer VideoParametersConsistent;

        @JSONField(name = "CallbackUrl")
        @Schema(title = "回调URL")
        private String CallbackUrl;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(title = "音频参数 (用于视频免训练)")
    public static class SpeechParam {
        @JSONField(name = "Speed")
        @Schema(title = "语速 (0.5-1.5)", required = true) 
        private Float Speed;

        @JSONField(name = "TimbreKey")
        @Schema(title = "音色key", required = true) 
        private String TimbreKey;

        @JSONField(name = "Volume")
        @Schema(title = "音量大小 (0-10)")
        private Integer Volume;

        @JSONField(name = "EmotionCategory")
        @Schema(title = "控制合成音频的情感")
        private String EmotionCategory;

        @JSONField(name = "EmotionIntensity")
        @Schema(title = "控制合成音频情感程度 (50-200)")
        private Integer EmotionIntensity;

        @JSONField(name = "TimbreLanguage")
        @Schema(title = "音色语种")
        private String TimbreLanguage;
    }
} 