package com.nacos.model.TXaPaas;

import com.nacos.model.TXaPaas.model.TxAIPaasQueryProgressResponse;
import com.nacos.model.TXaPaas.model.TxAIPaasVideoMakeNoTrainRequest;
import com.nacos.model.TXaPaas.model.TxAIPaasVideoMakeNoTrainResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component; 
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson2.JSONObject;
import okhttp3.Response;
import java.io.IOException;

@Slf4j
@Component 
public class TXaPaasApiUtil {

    /**
     * 查询视频进度
     * 
     * @param taskId 任务ID
     * @param appKey 应用密钥
     * @param accessToken 访问令牌
     * @return 视频进度响应
     */
    public TxAIPaasQueryProgressResponse queryVideoProgress(String taskId, String appKey, String accessToken) {
        String methodName = "util.queryVideoProgress"; 
        log.info("[{}] - 校验参数并尝试查询视频进度，任务ID: {}", methodName, taskId);

        if (StringUtils.isAnyBlank(taskId, appKey, accessToken)) {
            String errorMsg = "请求参数校验失败：TaskId, AppKey, 和 AccessToken 均不能为空";
            log.error("[{}] - {}", methodName, errorMsg);
            return TxAIPaasQueryProgressResponse.createErrorBo(TXaPaasApi.PARAM_VALIDATION_ERROR_CODE, errorMsg, null);
        }

        log.info("[{}] - 参数校验通过，准备调用API查询视频进度.", methodName);
        try (Response response = TXaPaasApi.queryVideoProgress(taskId, appKey, accessToken)) {
            String responseBodyString = response.body() != null ? response.body().string() : "";
            int httpStatusCode = response.code();
            log.info("[{}] - API调用完成，HTTP响应码: {}", methodName, httpStatusCode);

            if (!response.isSuccessful()) {
                String errorMsg = String.format("API请求失败. HTTP状态码: %d", httpStatusCode);
                log.error("[{}] - {}. 原始响应体: {}", methodName, errorMsg, responseBodyString);
                try {
                    TxAIPaasQueryProgressResponse errorResponse = JSONObject.parseObject(responseBodyString, TxAIPaasQueryProgressResponse.class);
                    if (errorResponse != null && errorResponse.getHeader() != null && errorResponse.getHeader().getCode() != 0) {
                         log.warn("[{}] - 尝试从失败响应中解析腾讯API错误信息成功. Code: {}, Message: {}", methodName, errorResponse.getHeader().getCode(), errorResponse.getHeader().getMessage());
                         return TxAIPaasQueryProgressResponse.createErrorBo(errorResponse.getHeader().getCode(), errorResponse.getHeader().getMessage(), errorResponse.getHeader().getRequestID());
                    }
                } catch (Exception parseEx) {
                    log.warn("[{}] - 解析错误响应体为腾讯API结构化错误失败: {}", methodName, parseEx.getMessage());
                }
                return TxAIPaasQueryProgressResponse.createErrorBo(httpStatusCode, "API请求失败，HTTP状态码: " + httpStatusCode, null);
            }
            
            if (StringUtils.isBlank(responseBodyString)) {
                log.error("[{}] - API请求成功但响应体为空.", methodName);
                return TxAIPaasQueryProgressResponse.createErrorBo(TXaPaasApi.INTERNAL_ERROR_CODE, "API请求成功但响应体为空", null); 
            }

            TxAIPaasQueryProgressResponse progressResponse = JSONObject.parseObject(responseBodyString, TxAIPaasQueryProgressResponse.class);
            if (progressResponse == null) {
                log.error("[{}] - 解析成功响应体到业务对象失败. 原始响应体: {}", methodName, responseBodyString);
                return TxAIPaasQueryProgressResponse.createErrorBo(TXaPaasApi.INTERNAL_ERROR_CODE, "响应数据解析失败：无法将JSON转换为对象", null);
            }

            if (progressResponse.getHeader() != null && progressResponse.getHeader().getCode() != 0) {
                log.warn("[{}] - API调用成功，但腾讯API在Header中返回业务错误。状态码: {}, 消息: {}", 
                         methodName, progressResponse.getHeader().getCode(), progressResponse.getHeader().getMessage());
            } else if (progressResponse.getHeader() != null) {
                 log.info("[{}] - API调用成功且腾讯API业务处理成功。腾讯API状态码: {}, 消息: {}", 
                         methodName, progressResponse.getHeader().getCode(), progressResponse.getHeader().getMessage());
            }
            return progressResponse;

        } catch (IOException e) {
            log.error("[{}] - API调用期间发生网络请求IO异常: {}", methodName, e.getMessage(), e);
            return TxAIPaasQueryProgressResponse.createErrorBo(TXaPaasApi.INTERNAL_ERROR_CODE, "网络请求IO异常: " + e.getMessage(), null);
        } catch (Exception e) { 
            log.error("[{}] - API调用准备或处理期间发生意外系统异常: {}", methodName, e.getMessage(), e);
            return TxAIPaasQueryProgressResponse.createErrorBo(TXaPaasApi.INTERNAL_ERROR_CODE, "系统处理异常: " + e.getMessage(), null);
        }
    }

    /**
     * 提交视频制作 (免训练) 任务
     * 
     * @param requestPayload 视频制作请求的Payload部分
     * @param appKey 应用密钥
     * @param accessToken 访问令牌
     * @return 视频制作响应
     */
    public TxAIPaasVideoMakeNoTrainResponse videomakeNoTrain(TxAIPaasVideoMakeNoTrainRequest.RequestPayload requestPayload, String appKey, String accessToken) {
        String methodName = "util.videomakeNoTrain"; 
        log.info("[{}] - 校验参数并尝试提交视频制作 (免训练) 任务.", methodName);

        if (requestPayload == null || StringUtils.isAnyBlank(appKey, accessToken)) {
            String errorMsg = "请求参数校验失败：requestPayload, AppKey, 和 AccessToken 均不能为空";
            log.error("[{}] - {}", methodName, errorMsg);
            return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(TXaPaasApi.PARAM_VALIDATION_ERROR_CODE, errorMsg, null);
        }
        if (StringUtils.isBlank(requestPayload.getRefVideoUrl())) {
            String errorMsg = "请求参数校验失败：requestPayload中的RefVideoUrl不能为空";
            log.error("[{}] - {}", methodName, errorMsg);
            return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(TXaPaasApi.PARAM_VALIDATION_ERROR_CODE, errorMsg, null);
        }
        if (StringUtils.isBlank(requestPayload.getDriverType())) {
            String errorMsg = "请求参数校验失败：requestPayload中的DriverType不能为空";
            log.error("[{}] - {}", methodName, errorMsg);
            return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(TXaPaasApi.PARAM_VALIDATION_ERROR_CODE, errorMsg, null);
        }
        if ("Text".equals(requestPayload.getDriverType()) && 
            (requestPayload.getSpeechParam() == null || 
             requestPayload.getSpeechParam().getSpeed() == null || 
             StringUtils.isBlank(requestPayload.getSpeechParam().getTimbreKey()) ||
             StringUtils.isBlank(requestPayload.getInputSsml()))){
            String errorMsg = "请求参数校验失败：Text驱动类型时，InputSsml和SpeechParam (含Speed, TimbreKey)不能为空";
            log.error("[{}] - {}", methodName, errorMsg);
            return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(TXaPaasApi.PARAM_VALIDATION_ERROR_CODE, errorMsg, null);
        }
        if ("OriginalVoice".equals(requestPayload.getDriverType()) && StringUtils.isBlank(requestPayload.getInputAudioUrl())){
            String errorMsg = "请求参数校验失败：OriginalVoice驱动类型时，InputAudioUrl不能为空";
            log.error("[{}] - {}", methodName, errorMsg);
            return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(TXaPaasApi.PARAM_VALIDATION_ERROR_CODE, errorMsg, null);
        }

        log.info("[{}] - 参数校验通过，准备调用API提交视频制作 (免训练) 任务.", methodName);
        try (Response response = TXaPaasApi.videomakeNoTrain(requestPayload, appKey, accessToken)) {
            String responseBodyString = response.body() != null ? response.body().string() : "";
            int httpStatusCode = response.code();
            log.info("[{}] - API调用完成，HTTP响应码: {}", methodName, httpStatusCode);
            log.debug("[{}] - 原始响应体: {}", methodName, responseBodyString);

            if (!response.isSuccessful()) {
                String errorMsg = String.format("API请求失败. HTTP状态码: %d", httpStatusCode);
                log.error("[{}] - {}. 原始响应体: {}", methodName, errorMsg, responseBodyString);
                try {
                    TxAIPaasVideoMakeNoTrainResponse errorResponse = JSONObject.parseObject(responseBodyString, TxAIPaasVideoMakeNoTrainResponse.class);
                    if (errorResponse != null && errorResponse.getHeader() != null && errorResponse.getHeader().getCode() != 0) {
                        log.warn("[{}] - 尝试从失败响应中解析腾讯API错误信息成功. Code: {}, Message: {}", methodName, errorResponse.getHeader().getCode(), errorResponse.getHeader().getMessage());
                        return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(errorResponse.getHeader().getCode(), errorResponse.getHeader().getMessage(), errorResponse.getHeader().getRequestID());
                    }
                } catch (Exception parseEx) {
                    log.warn("[{}] - 解析错误响应体为腾讯API结构化错误失败: {}", methodName, parseEx.getMessage());
                }
                return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(httpStatusCode, "API请求失败，HTTP状态码: " + httpStatusCode, null);
            }

            if (StringUtils.isBlank(responseBodyString)) {
                log.error("[{}] - API请求成功但响应体为空.", methodName);
                return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(TXaPaasApi.INTERNAL_ERROR_CODE, "API请求成功但响应体为空", null);
            }

            TxAIPaasVideoMakeNoTrainResponse noTrainResponse = JSONObject.parseObject(responseBodyString, TxAIPaasVideoMakeNoTrainResponse.class);

            if (noTrainResponse == null) {
                log.error("[{}] - 解析响应体到业务对象失败. 原始响应体: {}", methodName, responseBodyString);
                return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(TXaPaasApi.INTERNAL_ERROR_CODE, "响应数据解析失败：无法将JSON转换为对象", null);
            }
            
            if (noTrainResponse.getHeader() != null && noTrainResponse.getHeader().getCode() != 0) {
                log.warn("[{}] - API调用成功，但腾讯API在Header中返回业务错误。状态码: {}, 消息: {}", 
                         methodName, noTrainResponse.getHeader().getCode(), noTrainResponse.getHeader().getMessage());
            } else if (noTrainResponse.getHeader() != null) {
                 log.info("[{}] - API调用成功且腾讯API业务处理成功。腾讯API状态码: {}, 消息: {}", 
                         methodName, noTrainResponse.getHeader().getCode(), noTrainResponse.getHeader().getMessage());
            }
            return noTrainResponse;

        } catch (IOException e) {
            log.error("[{}] - API调用期间发生网络请求IO异常: {}", methodName, e.getMessage(), e);
            return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(TXaPaasApi.INTERNAL_ERROR_CODE, "网络请求IO异常: " + e.getMessage(), null);
        } catch (Exception e) {
            log.error("[{}] - API调用准备或处理期间发生意外系统异常: {}", methodName, e.getMessage(), e);
            return TxAIPaasVideoMakeNoTrainResponse.createErrorBo(TXaPaasApi.INTERNAL_ERROR_CODE, "系统处理异常: " + e.getMessage(), null);
        }
    }

}
