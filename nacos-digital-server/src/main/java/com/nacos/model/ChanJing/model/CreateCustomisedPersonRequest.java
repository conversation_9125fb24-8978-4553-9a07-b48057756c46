package com.nacos.model.ChanJing.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "定制禅境数字人请求实体")
public class CreateCustomisedPersonRequest {

    @Schema(title = "定制数字人名称")
    @JsonProperty("name")
    private String name;

    @Schema(title = "数字人素材视频")
    @JsonProperty("material_video")
    private String materialVideo;

    @Schema(title = "回调地址")
    @JsonProperty("callback")
    private String callback;

    @Schema(title = "训练类型（可选）")
    @JsonProperty("train_type")
    private String trainType;
} 