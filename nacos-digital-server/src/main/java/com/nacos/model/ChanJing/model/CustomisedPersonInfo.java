package com.nacos.model.ChanJing.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(title = "定制数字人信息")
public class CustomisedPersonInfo {

    @Schema(title = "数字人形象id")
    @JsonProperty("id")
    private String id;

    @Schema(title = "数字人名称")
    @JsonProperty("name")
    private String name;

    @Schema(title = "类型，默认person")
    @JsonProperty("type")
    private String type;

    @Schema(title = "预览封面图")
    @JsonProperty("pic_url")
    private String picUrl;

    @Schema(title = "预览地址")
    @JsonProperty("preview_url")
    private String previewUrl;

    @Schema(title = "形象宽度")
    @JsonProperty("width")
    private Integer width;

    @Schema(title = "形象高度")
    private Integer height;

    @Schema(title = "数字人的声音音色id")
    @JsonProperty("audio_man_id")
    private String audioManId;

    @Schema(title = "当前状态: 1制作中，2成功，4失败，5系统错误")
    @JsonProperty("status")
    private Integer status;

    @Schema(title = "失败后显示错误原因")
    @JsonProperty("err_reason")
    private String errReason;

    @Schema(title = "是否可用，1可用，0不可用")
    @JsonProperty("is_open")
    private Integer isOpen;

    @Schema(title = "失败原因")
    @JsonProperty("reason")
    private String reason;

    @Schema(title = "进度百分比，0-100")
    @JsonProperty("progress")
    private Integer progress;
} 