package com.nacos.model.ChanJing.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页信息
 */
@Data
@Schema(title = "分页信息")
public class PageInfo {

    /**
     * 当前页码
     */
    @Schema(title = "当前页码")
    @JsonProperty("page")
    private Integer page;

    /**
     * 页面大小
     */
    @Schema(title = "页面大小")
    @JsonProperty("size")
    private Integer size;

    /**
     * 数字人总数
     */
    @Schema(title = "总条目数")
    @JsonProperty("total_count")
    private Integer totalCount;

    /**
     * 总页数
     */
    @Schema(title = "总页数")
    @JsonProperty("total_page")
    private Integer totalPage;
} 