package com.nacos.model.TXDigital.model;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "腾讯云视频转译提交任务请求实体")
public class TXVideoTranslateJobRequestBO {

    @Schema(description = "视频URL地址")
    @JSONField(name = "VideoUrl")
    private String videoUrl;

    @Schema(description = "源语言，例如：en、zh")
    @JSONField(name = "SrcLang")
    private String srcLang;

    @Schema(description = "音频URL地址")
    @JSONField(name = "AudioUrl")
    private String audioUrl;

    @Schema(description = "目标语言，例如：zh、en")
    @JSONField(name = "DstLang")
    private String dstLang;

    @Schema(description = "翻译语种类型，默认不填")
    @JSONField(name = "VoiceType")
    private String voiceType;

    @Schema(description = "是否需要确认，0-不需要，1-需要。默认：0")
    @JSONField(name = "Confirm")
    private Integer confirm = 0;

    @Schema(description = "是否移除背景音，0-不移除，1-移除。默认：0")
    @JSONField(name = "RemoveVocal")
    private Integer removeVocal = 1;

    @Schema(description = "是否启用口型驱动，0-不启用，1-启用。默认：0")
    @JSONField(name = "LipSync")
    private Integer lipSync = 1;

}