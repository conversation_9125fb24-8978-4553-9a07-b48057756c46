package com.nacos.model.TXDigital;


import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import org.apache.commons.lang3.StringUtils;
import com.nacos.model.TXDigital.model.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import com.nacos.utils.DigitalFileUtil;
import org.springframework.stereotype.Component;


import static com.nacos.model.TXDigital.TXDigitalApis.doRequest;

@Slf4j
@Schema(title = "TXDigitalApisUtil api工具类")
@Component
public class TXDigitalApisUtil {

    public static final String STATUS_FAILED = "failed"; // 失败
    public static final String STATUS_SUCCEED = "succeed"; // 成功
    public static final String STATUS_PROCESSING = "processing"; // 处理中
    public static final String STATUS_SUBMITTED = "submitted"; // 已提交
    public static final String STATUS_QUEUING = "queuing"; // 队列中

    private static final TXVideoTranslateHeadersRequestBO headersRequestBO = new TXVideoTranslateHeadersRequestBO();
    private static final String ENDPOINT = "tmt.tencentcloudapi.com";
    private static final String REGION = "ap-guangzhou";

    /**
     * 提交视频转译任务
     *
     * @return TXVideoTranslateJobResponseBO 包含任务ID和错误信息的响应对象
     */
    public TXVideoTranslateJobResponseBO submitTXVideoTranslateJob(
            TXVideoTranslateJobRequestBO jobRequestBO,
            String secretId,
            String secretKey) {
        String methodName = "submitTXVideoTranslateJob";
        log.info("[{}] 请求参数：{}", methodName, JSONObject.toJSONString(jobRequestBO));
        // 参数验证
        if (jobRequestBO == null) {
            log.error("请求参数不能为空");
            return createErrorResponse("请求参数不能为空");
        }
        if (StringUtils.isBlank(secretId) || StringUtils.isBlank(secretKey)) {
            log.error("secretId或secretKey不能为空");
            return createErrorResponse("secretId或secretKey不能为空");
        }

        try {
            headersRequestBO.setAction("SubmitVideoTranslateJob");
            JSONObject body = JSONObject.parseObject(JSONObject.toJSONString(jobRequestBO, JSONWriter.Feature.WriteMapNullValue));
            log.info("提交视频转译任务请求参数：{}", JSONObject.toJSONString(jobRequestBO));

            // 发送请求
            String responseStr = doRequest(secretId, secretKey, headersRequestBO, JSONObject.toJSONString(body));
            if (StringUtils.isBlank(responseStr)) {
                return createErrorResponse("API请求失败");
            }

            // 解析响应
            TXVideoTranslateJobResponseBO responseBO = JSONObject.parseObject(responseStr, TXVideoTranslateJobResponseBO.class);
            if (responseBO == null) {
                return createErrorResponse("响应解析失败");
            }

            // 检查响应内容
            if (responseBO.getResponse() == null) {
                return createErrorResponse("响应内容为空");
            }

            log.info("提交视频转译任务成功，响应内容：{}", responseBO.getResponse().toString());
            return responseBO;

        } catch (Exception e) {
            String errorMsg = "提交视频转译任务异常：" + e.getMessage();
            log.error(errorMsg, e);
            return createErrorResponse(errorMsg);
        }
    }

    /**
     * 查询视频转译任务状态
     *
     * @param jobId     任务ID
     * @param secretId  密钥ID
     * @param secretKey 密钥
     * @return 查询结果，包含任务状态、错误信息、视频URL等
     */
    public TXVideoTranslateJobQueryResponseBO queryTXVideoTranslateJobStatus(String jobId, String secretId, String secretKey) {
        String methodName = "queryTXVideoTranslateJobStatus";
        log.info("[{}] 请求参数：jobId={}, secretId={}, secretKey={}", methodName, jobId, secretKey);
        // 参数验证
        if (StringUtils.isBlank(jobId)) {
            log.error("查询视频转译任务失败：任务ID不能为空");
            return createQueryErrorResponse("任务ID不能为空");
        }
        if (StringUtils.isBlank(secretId) || StringUtils.isBlank(secretKey)) {
            log.error("查询视频转译任务失败：secretId或secretKey不能为空");
            return createQueryErrorResponse("secretId或secretKey不能为空");
        }

        try {
            // 构建请求参数
            TXVideoTranslateJobQueryRequestBO requestBO = new TXVideoTranslateJobQueryRequestBO();
            requestBO.setJobId(jobId);
            headersRequestBO.setAction("DescribeVideoTranslateJob");

            // 发送请求
            String responseStr = doRequest(secretId, secretKey, headersRequestBO, JSONObject.toJSONString(requestBO));
            if (StringUtils.isBlank(responseStr)) {
                return createQueryErrorResponse("API请求失败");
            }

            // 解析响应
            TXVideoTranslateJobQueryResponseBO responseBO = JSONObject.parseObject(
                    responseStr, TXVideoTranslateJobQueryResponseBO.class);

            if (responseBO == null || responseBO.getResponse() == null) {
                return createQueryErrorResponse("响应解析失败或响应内容为空");
            }

            // 检查是否有错误
            if (responseBO.getResponse().getError() != null) {
                String errorCode = responseBO.getResponse().getError().getCode();
                String errorMessage = responseBO.getResponse().getError().getMessage();
                log.error("查询视频转译任务失败：code={}, message={}", errorCode, errorMessage);

                // 特殊错误处理
                if ("FailedOperation.JobNotExist".equals(errorCode)) {
                    log.error("任务不存在，jobId: {}", jobId);
                }
                // 直接返回包含错误信息的响应
                return responseBO;
            }

            // 记录任务状态
            Integer jobStatus = responseBO.getResponse().getJobStatus();
            if (jobStatus != null) {
                String statusDesc = switch (jobStatus) {
                    case 1 -> "音频翻译中";
                    case 2 -> "音频翻译失败";
                    case 3 -> "音频翻译成功";
                    case 4 -> "音频结果待确认";
                    case 5 -> "音频结果已确认完毕";
                    case 6 -> "视频翻译中";
                    case 7 -> "视频翻译失败";
                    case 8 -> "视频翻译成功";
                    default -> "未知状态";
                };
                log.info("视频转译任务状态：jobId={}, status={}, desc={}",
                        jobId, jobStatus, statusDesc);
            }

            return responseBO;

        } catch (Exception e) {
            String errorMsg = String.format("查询视频转译任务异常，jobId: %s, error: %s", jobId, e.getMessage());
            log.error(errorMsg, e);
            return createQueryErrorResponse(errorMsg);
        }
    }

    /**
     * 创建错误响应对象 - 提交任务
     */
    private static TXVideoTranslateJobResponseBO createErrorResponse(String errorMessage) {
        TXVideoTranslateJobResponseBO responseBO = new TXVideoTranslateJobResponseBO();
        TXVideoTranslateJobResponseBO.Response response = new TXVideoTranslateJobResponseBO.Response();
        TXVideoTranslateJobResponseBO.Error error = new TXVideoTranslateJobResponseBO.Error();
        error.setCode("InternalError");
        error.setMessage(errorMessage);
        response.setError(error);
        responseBO.setResponse(response);
        return responseBO;
    }

    /**
     * 创建错误响应对象 - 查询任务
     */
    private static TXVideoTranslateJobQueryResponseBO createQueryErrorResponse(String errorMessage) {
        TXVideoTranslateJobQueryResponseBO responseBO = new TXVideoTranslateJobQueryResponseBO();
        TXVideoTranslateJobQueryResponseBO.Response response = new TXVideoTranslateJobQueryResponseBO.Response();
        TXVideoTranslateJobQueryResponseBO.Error error = new TXVideoTranslateJobQueryResponseBO.Error();
        error.setCode("InternalError");
        error.setMessage(errorMessage);
        response.setError(error);
        responseBO.setResponse(response);
        return responseBO;
    }

    /**
     * 上传数字人相关文件
     *
     * @deprecated 请使用 {@link DigitalFileUtil#uploadDigitalResource(Object, String, String, String, Integer, Boolean)}
     */
    @Deprecated
    public static String uploadDigitalResource(byte[] file, String fileName, String userId, String groupId, Integer type, Boolean isSystem) {
        return DigitalFileUtil.uploadDigitalResource(file, fileName, userId, groupId, type, isSystem);
    }

}
