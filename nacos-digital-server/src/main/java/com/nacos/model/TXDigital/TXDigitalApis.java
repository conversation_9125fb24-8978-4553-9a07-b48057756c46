package com.nacos.model.TXDigital;

import com.nacos.model.TXDigital.model.TXVideoTranslateHeadersRequestBO;
import com.nacos.model.TXDigital.model.TXVideoTranslateJobQueryResponseBO;
import com.nacos.tool.BrotliInterceptor;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.security.InvalidKeyException;
import java.util.Date;

@Slf4j
@Schema(title = "TXDigitalApis api类")
public class TXDigitalApis {

    private static final String TENCENT_CLOUD_API_URL= "https://vtc.tencentcloudapi.com";

    @Getter
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .addInterceptor(new BrotliInterceptor())
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .build();

    /**
     * 执行HTTP请求的方法
     *
     * @param secretId  腾讯云API密钥ID
     * @param secretKey 腾讯云API密钥Key
     * @param requestBO 请求参数对象
     * @param body      请求体JSON字符串
     * @return 响应字符串
     */
    public static String doRequest(
            String secretId, String secretKey,
            TXVideoTranslateHeadersRequestBO requestBO,
            String body
    ) throws IOException, NoSuchAlgorithmException, InvalidKeyException {
        try {
            // 构建HTTP请求对象
            Request request = buildRequest(secretId, secretKey, requestBO, body);
            
            // 记录请求信息
            log.info("请求方法和URL: {} {}", request.method(), request.url());
            log.info("请求头: {}", request.headers());
            log.info("请求体: {}", body);

            // 发送请求并获取响应
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("请求失败，HTTP状态码: {}", response.code());
                    return null;
                }

                // 获取响应体
                String responseBody = response.body().string();
                log.info("响应内容: {}", responseBody);
                return responseBody;
            }
        } catch (Exception e) {
            log.error("请求发生异常：", e);
            throw e;
        }
    }

    /**
     * 构建HTTP请求对象
     *
     * @return 构建好的Request对象
     */
    public static Request buildRequest(
            String secretId, String secretKey,
            TXVideoTranslateHeadersRequestBO requestBO,
            String body
    ) throws NoSuchAlgorithmException, InvalidKeyException {
        String host = "vtc.tencentcloudapi.com";
        String url = "https://" + host;
        String contentType = "application/json; charset=utf-8";
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String auth = getAuth(secretId, secretKey, host, contentType, timestamp, body);
        return new Request.Builder()
                .header("Host", host)
                .header("X-TC-Timestamp", timestamp)
                .header("X-TC-Version", requestBO.getVersion())
                .header("X-TC-Action", requestBO.getAction())
                .header("X-TC-Region", requestBO.getRegion())
                .header("X-TC-RequestClient", "SDK_JAVA_BAREBONE")
                .header("Authorization", auth)
                .url(url)
                .post(RequestBody.create(MediaType.parse(contentType), body))
                .build();
    }

    /**
     * 生成腾讯云API的签名认证信息
     *
     * @param secretId    密钥ID
     * @param secretKey   密钥Key
     * @param host        请求域名
     * @param contentType 内容类型
     * @param timestamp   时间戳
     * @param body        请求体
     * @return 签名字符串
     */
    private static String getAuth(
            String secretId, String secretKey, String host, String contentType,
            String timestamp, String body
    ) throws NoSuchAlgorithmException, InvalidKeyException {
        // 计算请求体的哈希值
        String canonicalUri = "/";
        String canonicalQueryString = "";
        String canonicalHeaders = "content-type:" + contentType + "\nhost:" + host + "\n";
        String signedHeaders = "content-type;host";
        String hashedRequestPayload = sha256Hex(body.getBytes(StandardCharsets.UTF_8));
        String canonicalRequest = "POST"
                + "\n"
                + canonicalUri
                + "\n"
                + canonicalQueryString
                + "\n"
                + canonicalHeaders
                + "\n"
                + signedHeaders
                + "\n"
                + hashedRequestPayload;
        // 计算签名所需的时间戳和凭证范围
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        String date = sdf.format(new Date(Long.valueOf(timestamp + "000")));
        String service = host.split("\\.")[0];
        String credentialScope = date + "/" + service + "/" + "tc3_request";
        String hashedCanonicalRequest = sha256Hex(canonicalRequest.getBytes(StandardCharsets.UTF_8));
        String stringToSign = "TC3-HMAC-SHA256\n" + timestamp + "\n" + credentialScope + "\n" + hashedCanonicalRequest;
        // 计算签名
        byte[] secretDate = hmac256(("TC3" + secretKey).getBytes(StandardCharsets.UTF_8), date);
        byte[] secretService = hmac256(secretDate, service);
        byte[] secretSigning = hmac256(secretService, "tc3_request");
        String signature = printHexBinary(hmac256(secretSigning, stringToSign)).toLowerCase();
        return "TC3-HMAC-SHA256 "
                + "Credential="
                + secretId
                + "/"
                + credentialScope
                + ", "
                + "SignedHeaders="
                + signedHeaders
                + ", "
                + "Signature="
                + signature;
    }

    /**
     * 计算字符串的SHA-256哈希值，并转换为小写十六进制字符串
     *
     * @param b 需要计算哈希的字节数组
     * @return 十六进制哈希字符串
     */
    public static String sha256Hex(byte[] b) throws NoSuchAlgorithmException {
        MessageDigest md;
        md = MessageDigest.getInstance("SHA-256");
        byte[] d = md.digest(b);
        return printHexBinary(d).toLowerCase();
    }

    // 用于转换十六进制的字符数组
    private static final char[] hexCode = "0123456789ABCDEF".toCharArray();

    /**
     * 将字节数组转换为十六进制字符串
     *
     * @param data 需要转换的字节数组
     * @return 十六进制字符串
     */
    public static String printHexBinary(byte[] data) {
        StringBuilder r = new StringBuilder(data.length * 2);
        for (byte b : data) {
            r.append(hexCode[(b >> 4) & 0xF]);
            r.append(hexCode[(b & 0xF)]);
        }
        return r.toString();
    }

    /**
     * 使用HMAC-SHA256算法计算消息认证码
     *
     * @param key 密钥
     * @param msg 消息内容
     * @return HMAC值的字节数组
     */
    public static byte[] hmac256(byte[] key, String msg) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, mac.getAlgorithm());
        mac.init(secretKeySpec);
        return mac.doFinal(msg.getBytes(StandardCharsets.UTF_8));
    }
}
