package com.nacos.constant;


import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Component
public class CommonConst {

    //用户提交视频任务状态
    public static final int VIDEO_TASK_STATUS_0 = 0;
    //任务提交成功
    public static final int VIDEO_TASK_STATUS_1 = 1;
    //任务提交失败
    public static final int VIDEO_TASK_STATUS_2 = 2;
    //训练任务开始处理中
    public static final int VIDEO_TASK_STATUS_3 = 3;
    //训练成功
    public static final int VIDEO_TASK_STATUS_4 = 4;
    //训练失败
    public static final int VIDEO_TASK_STATUS_5 = 5;
    //训练状态开始处理中
    public static final int VIDEO_TASK_STATUS_6 = 6;

    //用户提交音频任务状态
    public static final int VOICE_TASK_STATUS_0 = 0;
    //任务提交成功
    public static final int VOICE_TASK_STATUS_1 = 1;
    //任务提交失败
    public static final int VOICE_TASK_STATUS_2 = 2;
    //上传任务开始处理中
    public static final int VOICE_TASK_STATUS_3 = 3;
    //训练成功
    public static final int VOICE_TASK_STATUS_4 = 4;
    //训练失败
    public static final int VOICE_TASK_STATUS_5 = 5;
    //训练状态开始处理中
    public static final int VOICE_TASK_STATUS_6 = 6;

    //用户提交视频任务状态
    public static final int VIDEO_GENERNA_STATUS_0 = 0;
    //任务提交成功_GENERNA
    public static final int VIDEO_GENERNA_STATUS_1 = 1;
    //任务提交失败_GENERNA
    public static final int VIDEO_GENERNA_STATUS_2 = 2;
    //训练任务开始处理中_GENERNA
    public static final int VIDEO_GENERNA_STATUS_3 = 3;
    //训练成功_GENERNA
    public static final int VIDEO_GENERNA_STATUS_4 = 4;
    //训练失败_GENERNA
    public static final int VIDEO_GENERNA_STATUS_5 = 5;
    //训练状态开始处理中_GENERNA
    public static final int VIDEO_GENERNA_STATUS_6 = 6;

    //用户提交视频任务状态
    public static final int VOICE_GENERNA_STATUS_0 = 0;
    //任务提交成功_GENERNA
    public static final int VOICE_GENERNA_STATUS_1 = 1;
    //任务提交失败_GENERNA
    public static final int VOICE_GENERNA_STATUS_2 = 2;
    //训练任务开始处理中_GENERNA
    public static final int VOICE_GENERNA_STATUS_3 = 3;
    //训练成功_GENERNA
    public static final int VOICE_GENERNA_STATUS_4 = 4;
    //训练失败_GENERNA
    public static final int VOICE_GENERNA_STATUS_5 = 5;
    //训练状态开始处理中_GENERNA
    public static final int VOICE_GENERNA_STATUS_6 = 6;

    //分任务状态处理
    public static final int ITEM_TASK_TOTAL_STATUS_1 = 1;
    //分任务成功
    public static final int ITEM_TASK_TOTAL_STATUS_2 = 2;

    //知识库状态-创建待处理
    public static final int KNOWLEDGE_ITEM_STATUS_0 = 0;
    //知识库状态-创建处理成功
    public static final int KNOWLEDGE_ITEM_STATUS_1 = 1;
    //知识库状态-创建处理失败
    public static final int KNOWLEDGE_ITEM_STATUS_2 = 2;
    //知识库状态-创建处理中
    public static final int KNOWLEDGE_ITEM_STATUS_3 = 3;
    //知识库状态-文档上传成功
    public static final int KNOWLEDGE_ITEM_STATUS_4 = 4;
    //知识库状态-文档上传失败
    public static final int KNOWLEDGE_ITEM_STATUS_5 = 5;
    //知识库状态-文档上传中
    public static final int KNOWLEDGE_ITEM_STATUS_6 = 6;
    //知识库状态-切片更新任务成功
    public static final int KNOWLEDGE_ITEM_STATUS_7 = 7;
    //知识库状态-切片更新任务失败
    public static final int KNOWLEDGE_ITEM_STATUS_8 = 8;
    //知识库状态-切片更新任务进行中
    public static final int KNOWLEDGE_ITEM_STATUS_9 = 9;
    //知识库状态-更新成功
    public static final int KNOWLEDGE_ITEM_STATUS_10 = 10;
    //知识库状态-更新失败
    public static final int KNOWLEDGE_ITEM_STATUS_11 = 11;
    //知识库状态-更新中
    public static final int KNOWLEDGE_ITEM_STATUS_12 = 12;
    //知识库状态-应用数据更新成功
    public static final int KNOWLEDGE_ITEM_STATUS_16 = 16;
    //知识库状态-应用数据更新失败
    public static final int KNOWLEDGE_ITEM_STATUS_17 = 17;
    //知识库状态-应用数据更新中
    public static final int KNOWLEDGE_ITEM_STATUS_18 = 18;
    //知识库状态-索引数据删除成功
    public static final int KNOWLEDGE_ITEM_STATUS_13 = 13;
    //知识库状态-索引数据删除失败
    public static final int KNOWLEDGE_ITEM_STATUS_14 = 14;
    //知识库状态-索引数据删除中
    public static final int KNOWLEDGE_ITEM_STATUS_15 = 15;
    //知识库状态-索引数据待删除
    public static final int KNOWLEDGE_ITEM_STATUS_19 = 19;

    //支持.pdf,.doc,.docx,.txt,.md,.pptx,.ppt,.png,.jpg,.jpeg,.bmp,.gif,.xls,.xlsx,.html
    public static final Set<String> KNOWLEDGE_FILE_SUFFIX = ImmutableSet.of(".pdf",".doc",".docx",".txt",".md",".pptx",".ppt",".png",".jpg",".jpeg",".bmp",".gif",".xls",".xlsx",".html");

}
