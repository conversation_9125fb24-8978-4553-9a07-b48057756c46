package com.nacos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nacos.entity.po.VideoEditTaskPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 视频编辑主任务Mapper接口
 * 参考DigitalVideoTaskMapper的设计模式
 */
@Mapper
public interface VideoEditTaskMapper extends BaseMapper<VideoEditTaskPO> {

    /**
     * 根据用户ID查询视频编辑任务列表
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("SELECT * FROM digital_video_edit_task WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY created_time DESC LIMIT #{limit}")
    List<VideoEditTaskPO> getTasksByUserId(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 根据状态查询任务列表
     * @param status 任务状态
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("SELECT * FROM digital_video_edit_task WHERE status = #{status} AND is_deleted = 0 ORDER BY created_time ASC LIMIT #{limit}")
    List<VideoEditTaskPO> getTasksByStatus(@Param("status") Integer status, @Param("limit") Integer limit);

    /**
     * 统计用户的任务数量
     * @param userId 用户ID
     * @param status 任务状态，null表示统计所有状态
     * @return 任务数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM digital_video_edit_task WHERE user_id = #{userId} AND is_deleted = 0" +
            "<if test='status != null'> AND status = #{status}</if>" +
            "</script>")
    Integer countTasksByUser(@Param("userId") String userId, @Param("status") Integer status);
}
