package com.nacos.controller;

import com.nacos.entity.vo.DigitalAvatarDefaultVO;
import com.nacos.entity.vo.DigitalSystemAvatarVO;
import com.nacos.service.DigitalSystemAvatarService;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

@Slf4j
@Tag(name = "系统数字人管理")
@RestController
@RequestMapping("/avatar/system")
public class DigitalSystemAvatarController {

    @Autowired
    private DigitalSystemAvatarService systemAvatarService;

    /**
     * 获取系统数字人列表
     * URL: /avatar/system/list
     * Method: GET
     * @return 系统数字人列表
     */
    @Operation(summary = "获取系统数字人列表")
    @GetMapping("/list")
    public Result<List<DigitalSystemAvatarVO>> listSystemAvatars() {
        return systemAvatarService.listSystemAvatars();
    }

    /**
     * 添加系统数字人
     * URL: /avatar/system/add
     * Method: POST
     * @param avatarVO 系统数字人信息
     * @return 操作结果
     */
    @Operation(summary = "添加系统数字人")
    @PostMapping("/add")
    public Result<Boolean> addSystemAvatar(@RequestBody DigitalSystemAvatarVO avatarVO) {
        return systemAvatarService.addSystemAvatar(avatarVO);
    }

    /**
     * 更新系统数字人
     * URL: /avatar/system/update
     * Method: PUT
     * @param avatarVO 系统数字人信息
     * @return 操作结果
     */
    @Operation(summary = "更新系统数字人")
    @PutMapping("/update")
    public Result<Boolean> updateSystemAvatar(@RequestBody DigitalSystemAvatarVO avatarVO) {
        return systemAvatarService.updateSystemAvatar(avatarVO);
    }

    /**
     * 删除系统数字人
     * URL: /avatar/system/delete/{id}
     * Method: DELETE
     * @param id 系统数字人ID
     * @return 操作结果
     */
    @Operation(summary = "删除系统数字人")
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> deleteSystemAvatar(@PathVariable Long id) {
        return systemAvatarService.deleteSystemAvatar(id);
    }

    /**
     * 更新系统数字人状态
     * URL: /avatar/system/status/{id}/{status}
     * Method: PUT
     * @param id 系统数字人ID
     * @param status 状态
     * @return 操作结果
     */
    @Operation(summary = "更新系统数字人状态")
    @PutMapping("/status/{id}/{status}")
    public Result<Boolean> updateStatus(@PathVariable Long id, @PathVariable Integer status) {
        return systemAvatarService.updateStatus(id, status);
    }

    /**
     * 根据组ID获取系统数字人列表
     * URL: /avatar/system/group/{groupId}
     * Method: GET
     * @param groupId 组ID
     * @return 系统数字人列表
     */
    @Operation(summary = "根据组ID获取系统数字人列表")
    @GetMapping("/group/{groupId}")
    public Result<List<DigitalSystemAvatarVO>> listSystemAvatarsByGroupId(@PathVariable String groupId) {
        return systemAvatarService.listSystemAvatarsByGroupId(groupId);
    }

    /**
     * 创建视频时默认数字人形象与音色
     * url: /avatar/system/default
     * method: GET
     * @param groupId 组ID
     * @return 数字人形象与音色
     */
    @Operation(summary = "创建视频时默认数字人形象与音色")
    @GetMapping("/default")
    public Result<DigitalAvatarDefaultVO> getDefaultAvatarAndVoice(@RequestParam String groupId) {
        return systemAvatarService.getDefaultAvatarAndVoice(groupId);
    }
} 