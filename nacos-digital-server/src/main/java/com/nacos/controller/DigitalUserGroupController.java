package com.nacos.controller;

import com.nacos.entity.dto.DigitalUserGroupDTO;
import com.nacos.entity.vo.DigitalUserGroupVO;
import com.nacos.result.Result;
import com.nacos.service.DigitalUserGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数字人用户组控制器
 */
@Slf4j
@Tag(name = "数字人用户组管理")
@RestController
@RequestMapping("/group")
public class DigitalUserGroupController {
    
    @Autowired
    private DigitalUserGroupService digitalUserGroupService;
    
    /**
     * 创建数字人用户组
     * URL: /group/create
     * Method: POST
     * @param groupDTO 数字人用户组信息
     * @return 创建的数字人用户组ID，失败返回错误信息
     */
    @Operation(summary = "创建数字人用户组")
    @PostMapping("/create")
    public Result<DigitalUserGroupVO> createGroup(@RequestBody DigitalUserGroupDTO groupDTO) {
        return digitalUserGroupService.createGroup(groupDTO);
    }
    
    /**
     * 更新数字人用户组
     * URL: /group/update
     * Method: POST
     * @param groupDTO 数字人用户组信息
     * @return 操作结果，失败返回错误信息
     */
    @Operation(summary = "更新数字人用户组")
    @PostMapping("/update")
    public Result<Boolean> updateGroup(@RequestBody DigitalUserGroupDTO groupDTO) {
        return digitalUserGroupService.updateGroup(groupDTO);
    }
    
    /**
     * 删除数字人用户组
     * URL: group/delete/{groupId}
     * Method: POST
     * @param id 数字人用户组id
     * @return 操作结果，失败返回错误信息
     */
    @Operation(summary = "删除数字人用户组")
    @PostMapping("/delete/{id}")
    public Result<Boolean> deleteGroup(@Parameter(description = "数字人用户组ID") @PathVariable Long id) {
        return digitalUserGroupService.deleteGroup(id);
    }
    
    /**
     * 获取用户的所有数字人用户组
     * URL: /group/list/{userId}
     * Method: GET
     * @param userId 用户ID
     * @return 数字人用户组列表，失败返回错误信息
     */
    @Operation(summary = "获取用户的所有数字人用户组")
    @GetMapping("/list/{userId}")
    public Result<List<DigitalUserGroupVO>> listUserGroups(@Parameter(description = "用户ID") @PathVariable String userId) {
        return digitalUserGroupService.listUserGroups(userId);
    }
    
    /**
     * 获取数字人用户组详情
     * URL: /group/detail/{groupId}
     * Method: GET
     * @param groupId 数字人用户组ID
     * @return 数字人用户组详情，失败返回错误信息
     */
    @Operation(summary = "获取数字人用户组详情")
    @GetMapping("/detail/{groupId}")
    public Result<DigitalUserGroupVO> getGroupDetail(@Parameter(description = "数字人用户组ID") @PathVariable Long groupId) {
        return digitalUserGroupService.getGroupDetail(groupId);
    }
} 