package com.nacos.controller;

import com.nacos.entity.vo.DigitalVoiceCategoryVO;
import com.nacos.result.Result;
import com.nacos.service.DigitalVoiceCategoryService;
import com.nacos.service.impl.DigitalVoiceCategoryServiceImpl;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "分类管理")
@Log4j2
@RestController
@RequestMapping("/voice/category")
@RequiredArgsConstructor
public class DigitalVoiceCategoryController {

    private final DigitalVoiceCategoryService digitalVoiceCategoryService;

    /**
     * 获取所有分类（不包含音色列表）
     * url: /voice/category/all
     * method: GET
     * @return 所有分类（不包含音色列表）
     */
    @GetMapping("/all")
    public Result<List<DigitalVoiceCategoryVO>> listAllCategories() {
        return Result.SUCCESS(digitalVoiceCategoryService.listAllCategories());
    }


    /**
     * 根据分类编码获取分类信息及其下的音色列表
     * URL: /voice/category/{categoryCode}
     * @param categoryCode 分类编码
     * @return 分类信息及其下的音色列表
     */
    @GetMapping("/{categoryCode}")
    public Result<DigitalVoiceCategoryVO> getCategory(@PathVariable String categoryCode) {
        return Result.SUCCESS(digitalVoiceCategoryService.getCategoryWithVoices(categoryCode));
    }

    /**
     * 更新音色的分类关联
     * url: /voice/category/relation
     * method: POST
     * @param voiceId 音色ID
     * @param categoryIds 分类ID列表
     * @return 空
     */
    @PostMapping("/relation")
    public Result<Void> updateVoiceCategoryRelation(@RequestParam String voiceId, 
                                                   @RequestParam List<Long> categoryIds) {
        digitalVoiceCategoryService.updateVoiceCategoryRelation(voiceId, categoryIds);
        return Result.SUCCESS();
    }

    /**
     * 批量更新系统音色的分类关联
     * url: /voice/category/batch-update
     * method: POST
     * @param voiceIds 音色ID列表
     * @param categoryCode 分类编码
     * @return 空
     */
    @PostMapping("/batch-update")
    public Result<Void> batchUpdateSystemVoiceCategories(@RequestParam List<String> voiceIds,
                                                       @RequestParam String categoryCode) {
        digitalVoiceCategoryService.batchUpdateSystemVoiceCategories(voiceIds, categoryCode);
        return Result.SUCCESS();
    }

    /**
     * 初始化系统音色分类关联
     * url: /voice/category/initialize
     * method: POST
     * @return 空
     */
    @PostMapping("/initialize")
    public Result<Void> initializeSystemVoiceCategories() {
        digitalVoiceCategoryService.initializeSystemVoiceCategories();
        return Result.SUCCESS();
    }
} 