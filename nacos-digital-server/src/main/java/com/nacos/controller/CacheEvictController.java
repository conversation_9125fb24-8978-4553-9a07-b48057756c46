//package com.nacos.controller;
//
//import com.nacos.constant.CommonConst;
//import com.nacos.exception.IBusinessException;
//import com.nacos.model.dto.GptSceneGenerationDTO;
//import com.nacos.service.CacheService;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//
//import javax.annotation.Resource;
//
//public class CacheEvictController {
//
//    @Resource
//    private CacheService cacheService;
//
//    @PostMapping(value = "/textCacheEvict", name = "文案生成")
//    public String sceneGeneration(@RequestBody GptSceneGenerationDTO gptSceneGenerationDTO) throws IBusinessException {
//        cacheService.evictCache("configModelServiceImpl.queryByModelCategoryAndIsEnabled","#"+CommonConst.MODEL_CATEGORY_TEXT+"_#"+CommonConst.MODEL_ENABLED);
//        return "清除configModelServiceImpl.queryByModelCategoryAndIsEnabled的#"+CommonConst.MODEL_CATEGORY_TEXT+"_#"+CommonConst.MODEL_ENABLED;
//    }
//
//
//}
