package com.nacos.controller;

import com.business.db.model.po.digital.DigitalVectorTaskPO;
import com.nacos.constant.CommonConst;
import com.nacos.entity.dto.DigitalVectorUploadDTO;
import com.nacos.entity.dto.VideoMergeTestDTO;
import com.business.db.model.po.digital.DigitalVectorItemPO;
import com.nacos.result.Result;
import com.nacos.service.DigitalVectorService;
import com.nacos.service.FeiyongService;
import com.nacos.utils.DigitalFileUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import com.business.alibailian.Md5Utils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.UUID;

import static com.google.common.io.Files.getFileExtension;
import static com.nacos.constant.CommonConst.KNOWLEDGE_FILE_SUFFIX;
import static com.nacos.constant.CommonConst.KNOWLEDGE_ITEM_STATUS_19;

@Tag(name = "向量检索", description = "向量检索")
@Log4j2
@RestController
@RequiredArgsConstructor
@RequestMapping("/vector")
public class DigitalVectorController {

    private final DigitalVectorService digitalVectorService;

    private final FeiyongService feiyongService;

    @PostMapping(value = "/uploadDoc" ,consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Schema(description = "向量文档上传接口")
    public Result<String> uploadDoc(@ModelAttribute @Valid DigitalVectorUploadDTO digitalVectorUploadDTO) {
        log.info("开始处理向量文档上传，参数：{}", digitalVectorUploadDTO);

        try {
            // 1. 参数校验
            if (digitalVectorUploadDTO == null) {
                return Result.ERROR("上传参数不能为空");
            }
            if (ObjectUtils.isEmpty(digitalVectorUploadDTO.getVectorFile())) {
                return Result.ERROR("文件名不能为空");
            }
            if (StringUtils.isBlank(digitalVectorUploadDTO.getUserId())) {
                return Result.ERROR("用户ID不能为空");
            }

            String md5 = Md5Utils.getFileMd5(digitalVectorUploadDTO.getVectorFile());

            //获取文件大小，转换成M,精确到小数点后2位
            long fileSizeBytes = digitalVectorUploadDTO.getVectorFile().getSize();

            // 2. 转换为MB并保留2位小数
            double fileSizeMB = fileSizeBytes / (1024.0 * 1024.0);
            String formattedSize = String.format("%.2f", fileSizeMB);
            Double fileSize = Double.parseDouble(formattedSize);

            if (fileSize > 100) {
                return Result.ERROR("文件大小不能超过100MB");
            }

            boolean lease = feiyongService.checkKownledgeSpace(Long.parseLong(digitalVectorUploadDTO.getUserId()), "文档上传", fileSize);
            if (!lease){
                return Result.ERROR("知识空间不足，请充值");
            }

            // 2. 文件存储到OSS
            String fileUrl;
            String fileName = digitalVectorUploadDTO.getVectorFile().getOriginalFilename();

            String preName = "";
            // 2. 获取文件后缀
            String extension = "";
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0) {
                extension = fileName.substring(dotIndex);
                preName = fileName.substring(0, dotIndex);
            }
            preName = preName.replaceAll(
                    "[\\u3000\\p{P}\\p{S}\\s&&[^a-zA-Z0-9_]]",  // 包含全角空格(\\u3000)、标点、特殊符号和空格
                    ""
            );

            // 3. 截取前100个字符（不含后缀）
            if (preName.length() > 100) {
                preName = preName.substring(0, 100);
            }

            // 4. 重新拼接文件名和后缀
            String finalFilename = preName + extension;

            if (!CommonConst.KNOWLEDGE_FILE_SUFFIX.contains(extension)){
                return Result.ERROR("文件类型不支持，请上传以下类型"+ KNOWLEDGE_FILE_SUFFIX.toString());
            }
            try {
                fileUrl  = DigitalFileUtil.uploadDigitalResource(
                        digitalVectorUploadDTO.getVectorFile(),
                        finalFilename,
                        digitalVectorUploadDTO.getUserId(),
                        null,
                        9,
                        false
                );
            } catch (Exception e) {
                log.error("文件上传OSS失败", e);
                return Result.ERROR("文件上传失败: " + e.getMessage());
            }

            if (fileUrl == null) {
                return Result.ERROR("文件上传失败");
            }

            // 3. 添加数据库记录
            try {
                digitalVectorService.saveVectorDocument(
                        digitalVectorUploadDTO.getUserId(),
                        digitalVectorUploadDTO.getVectorFile().getOriginalFilename(),
                        fileUrl,
                        md5,
                        fileSize
                );
            } catch (Exception e) {
                log.error("保存数据库记录失败", e);
                // 尝试删除已上传的OSS文件
//                DigitalFileUtil.deleteFromOSS(fileUrl);
                return Result.ERROR("文档保存失败: " + e.getMessage());
            }

            return Result.SUCCESS("文档上传成功，解析中，稍后刷新状态");

        } catch (Exception e) {
            log.error("处理向量文档上传时发生异常", e);
            return Result.ERROR("系统异常: " + e.getMessage());
        }
    }

    @PostMapping("/listDoc")
    @Schema(description = "文档列表")
    public Result<List<DigitalVectorItemPO>> listDoc(@RequestParam String userId) {
        log.info("开始查询文档列表，用户ID：{}", userId);
        
        try {
            // 参数校验
            if (StringUtils.isBlank(userId)) {
                return Result.ERROR("用户ID不能为空");
            }
            
            // 调用Service层查询未删除的文档
            List<DigitalVectorItemPO> documentList = digitalVectorService.getUserActiveDocuments(userId);
            
            log.info("成功查询到{}个文档", documentList.size());
            return Result.SUCCESS(documentList);
        } catch (Exception e) {
            log.error("查询文档列表时发生异常", e);
            return Result.ERROR("系统异常: " + e.getMessage());
        }
    }

    @PostMapping("/delDoc")
    @Schema(description = "删除文档")
    public Result<String> delDoc(@RequestParam String docId, @RequestParam String userId) {
        log.info("删除文档，用户ID：{}，docId{}",userId, docId);

        try {
            // 参数校验
            if (StringUtils.isBlank(userId)) {
                return Result.ERROR("用户ID不能为空");
            }
            digitalVectorService.updateDocumentStatus(docId, userId, KNOWLEDGE_ITEM_STATUS_19);
            return Result.SUCCESS("文档删除任务已提交，稍后刷新");
        } catch (Exception e) {
            log.error("查询文档列表时发生异常", e);
            return Result.ERROR("系统异常: " + e.getMessage());
        }
    }

    @PostMapping("/getIndexInfo")
    public Result<DigitalVectorTaskPO> getIndexInfo(@RequestParam Long userId) {
        log.info("getIndexInfo，用户ID：{}",userId);

        try {
            // 参数校验
            if (ObjectUtils.isEmpty(userId)) {
                return Result.ERROR("用户ID不能为空");
            }
            DigitalVectorTaskPO result = digitalVectorService.getIndexInfo(userId);
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("查询文档列表时发生异常", e);
            return Result.ERROR("系统异常: " + e.getMessage());
        }
    }
    /**
     * 测试视频拼接功能
     * url: /video/test-merge
     * method: POST
     * @param mergeRequest 视频拼接请求参数
     * @return 拼接后的视频URL
     */
    @PostMapping("/test-merge")
    @Schema(description = "测试视频拼接功能")
    public Result<String> testMergeVideos(@RequestBody VideoMergeTestDTO mergeRequest) {
        log.info("开始测试视频拼接功能，参数：{}", mergeRequest);
        try {
            if (mergeRequest.getVideoUrls() == null || mergeRequest.getVideoUrls().size() < 2) {
                return Result.ERROR("至少需要两个视频进行拼接");
            }
            
            // 直接调用DigitalFileUtil的视频合并方法
            String mergedVideoUrl = DigitalFileUtil.mergeVideos(
                mergeRequest.getVideoUrls(), 
                mergeRequest.getUserId(),
                null
            );
            
            if (StringUtils.isBlank(mergedVideoUrl)) {
                return Result.ERROR("视频拼接失败");
            }
            
            return Result.SUCCESS(mergedVideoUrl);
        } catch (Exception e) {
            log.error("测试视频拼接时发生异常", e);
            return Result.ERROR("视频拼接异常：" + e.getMessage());
        }
    }

}
