package com.nacos.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nacos.entity.vo.DigitalVoiceSyncVO;
import com.nacos.result.Result;
import com.nacos.service.DigitalVoiceSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 音色同步管理控制器
 */
@Tag(name = "音色同步管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/voice/sync")
public class DigitalVoiceSyncController {

    private final DigitalVoiceSyncService digitalVoiceSyncService;

    /**
     * 同步所有供应商音色数据
     */
    @Operation(summary = "同步所有供应商音色数据")
    @PostMapping("/all")
    public Result<String> syncAllVoiceData() {
        return digitalVoiceSyncService.syncAllVoiceData();
    }

    /**
     * 同步指定供应商音色数据
     */
    @Operation(summary = "同步指定供应商音色数据")
    @PostMapping("/provider/{provider}")
    public Result<String> syncVoiceDataByProvider(
            @Parameter(description = "供应商标识") @PathVariable String provider) {
        return digitalVoiceSyncService.syncVoiceDataByProvider(provider);
    }

    /**
     * 同步Minimax音色数据
     */
    @Operation(summary = "同步Minimax音色数据")
    @PostMapping("/minimax")
    public Result<String> syncMinimaxVoiceData() {
        return digitalVoiceSyncService.syncMinimaxVoiceData();
    }

    /**
     * 同步Microsoft音色数据
     */
    @Operation(summary = "同步Microsoft音色数据")
    @PostMapping("/microsoft")
    public Result<String> syncMicrosoftVoiceData() {
        return digitalVoiceSyncService.syncMicrosoftVoiceData();
    }

    /**
     * 同步ElevenLabs音色数据
     */
    @Operation(summary = "同步ElevenLabs音色数据")
    @PostMapping("/elevenlabs")
    public Result<String> syncElevenLabsVoiceData() {
        return digitalVoiceSyncService.syncElevenLabsVoiceData();
    }

    /**
     * 重试失败的同步记录
     */
    @Operation(summary = "重试失败的同步记录")
    @PostMapping("/retry")
    public Result<String> retryFailedSyncs() {
        return digitalVoiceSyncService.retryFailedSyncs();
    }

    /**
     * 重试指定供应商的失败同步记录
     */
    @Operation(summary = "重试指定供应商的失败同步记录")
    @PostMapping("/retry/{provider}")
    public Result<String> retryFailedSyncsByProvider(
            @Parameter(description = "供应商标识") @PathVariable String provider) {
        return digitalVoiceSyncService.retryFailedSyncsByProvider(provider);
    }

    /**
     * 获取同步状态统计
     */
    @Operation(summary = "获取同步状态统计")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getSyncStatusStatistics() {
        try {
            Map<String, Object> statistics = digitalVoiceSyncService.getSyncStatusStatistics();
            return Result.SUCCESS(statistics);
        } catch (Exception e) {
            log.error("获取同步状态统计失败", e);
            return Result.ERROR("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定供应商的同步状态统计
     */
    @Operation(summary = "获取指定供应商的同步状态统计")
    @GetMapping("/statistics/{provider}")
    public Result<Map<String, Object>> getSyncStatusStatisticsByProvider(
            @Parameter(description = "供应商标识") @PathVariable String provider) {
        try {
            Map<String, Object> statistics = digitalVoiceSyncService.getSyncStatusStatisticsByProvider(provider);
            return Result.SUCCESS(statistics);
        } catch (Exception e) {
            log.error("获取{}供应商同步状态统计失败", provider, e);
            return Result.ERROR("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询同步记录
     */
    @Operation(summary = "分页查询同步记录")
    @GetMapping("/records")
    public Result<Page<DigitalVoiceSyncVO>> querySyncRecords(
            @Parameter(description = "供应商标识") @RequestParam(required = false) String provider,
            @Parameter(description = "同步状态：1-成功 2-失败") @RequestParam(required = false) Integer syncStatus,
            @Parameter(description = "关键词搜索") @RequestParam(required = false) String keyword,
            @Parameter(description = "当前页码") @RequestParam(defaultValue = "1") Integer currentPage,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer pageSize) {
        try {
            Page<DigitalVoiceSyncVO> result = digitalVoiceSyncService.querySyncRecords(
                provider, syncStatus, keyword, currentPage, pageSize);
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("查询同步记录失败", e);
            return Result.ERROR("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取同步记录详情
     */
    @Operation(summary = "获取同步记录详情")
    @GetMapping("/records/{id}")
    public Result<DigitalVoiceSyncVO> getSyncRecordDetail(
            @Parameter(description = "同步记录ID") @PathVariable Long id) {
        try {
            DigitalVoiceSyncVO detail = digitalVoiceSyncService.getSyncRecordDetail(id);
            return Result.SUCCESS(detail);
        } catch (Exception e) {
            log.error("获取同步记录详情失败，ID: {}", id, e);
            return Result.ERROR("获取详情失败: " + e.getMessage());
        }
    }

    /**
     * 发布同步记录到系统表
     */
    @Operation(summary = "发布同步记录到系统表")
    @PostMapping("/publish/{syncId}")
    public Result<String> publishSyncRecord(
            @Parameter(description = "同步记录ID") @PathVariable Long syncId) {
        return digitalVoiceSyncService.publishSyncRecord(syncId);
    }

    /**
     * 批量发布同步记录到系统表
     */
    @Operation(summary = "批量发布同步记录到系统表")
    @PostMapping("/publish/batch")
    public Result<String> batchPublishSyncRecords(
            @Parameter(description = "同步记录ID列表") @RequestBody List<Long> syncIds) {
        return digitalVoiceSyncService.batchPublishSyncRecords(syncIds);
    }

    /**
     * 取消发布（从系统表移除）
     */
    @Operation(summary = "取消发布（从系统表移除）")
    @PostMapping("/unpublish/{syncId}")
    public Result<String> unpublishSyncRecord(
            @Parameter(description = "同步记录ID") @PathVariable Long syncId) {
        return digitalVoiceSyncService.unpublishSyncRecord(syncId);
    }

    /**
     * 更新同步记录
     */
    @Operation(summary = "更新同步记录")
    @PutMapping("/records/{syncId}")
    public Result<String> updateSyncRecord(
            @Parameter(description = "同步记录ID") @PathVariable Long syncId,
            @RequestBody DigitalVoiceSyncVO syncVO) {
        return digitalVoiceSyncService.updateSyncRecord(syncId, syncVO);
    }

    /**
     * 删除同步记录
     */
    @Operation(summary = "删除同步记录")
    @DeleteMapping("/records/{syncId}")
    public Result<String> deleteSyncRecord(
            @Parameter(description = "同步记录ID") @PathVariable Long syncId) {
        return digitalVoiceSyncService.deleteSyncRecord(syncId);
    }

    /**
     * 清理过期的失败记录
     */
    @Operation(summary = "清理过期的失败记录")
    @PostMapping("/cleanup")
    public Result<String> cleanExpiredFailedRecords(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "30") Integer days) {
        return digitalVoiceSyncService.cleanExpiredFailedRecords(days);
    }

    /**
     * 获取最近的同步日志
     */
    @Operation(summary = "获取最近的同步日志")
    @GetMapping("/logs")
    public Result<List<DigitalVoiceSyncVO>> getRecentSyncLogs(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "50") Integer limit) {
        try {
            List<DigitalVoiceSyncVO> logs = digitalVoiceSyncService.getRecentSyncLogs(limit);
            return Result.SUCCESS(logs);
        } catch (Exception e) {
            log.error("获取最近同步日志失败", e);
            return Result.ERROR("获取日志失败: " + e.getMessage());
        }
    }

    /**
     * 检查数据一致性
     */
    @Operation(summary = "检查数据一致性")
    @GetMapping("/consistency/check")
    public Result<Map<String, Object>> checkDataConsistency() {
        try {
            Map<String, Object> result = digitalVoiceSyncService.checkDataConsistency();
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("检查数据一致性失败", e);
            return Result.ERROR("检查失败: " + e.getMessage());
        }
    }

    /**
     * 修复数据不一致问题
     */
    @Operation(summary = "修复数据不一致问题")
    @PostMapping("/consistency/fix")
    public Result<String> fixDataInconsistency() {
        return digitalVoiceSyncService.fixDataInconsistency();
    }
}
