package com.nacos.controller.system;

import com.nacos.entity.dto.DigitalVoiceStyleUploadDTO;
import com.nacos.service.DigitalUploadService;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 文件上传业务Controller
 * 专注于文件上传处理的业务逻辑操作
 * 数据CRUD操作已迁移到admin包的SystemAdminController
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "文件上传业务", description = "文件上传处理业务逻辑")
@RestController
@RequestMapping("/api/v1/system/upload")
public class UploadController {

    @Autowired
    private DigitalUploadService digitalUploadService;

    /**
     * 上传音频文件
     * URL: /api/v1/system/upload/audio
     * Method: POST
     */
    @Operation(summary = "上传音频文件", description = "上传音频文件")
    @PostMapping(value = "/audio", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> uploadAudio(
            @Parameter(description = "音频文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId) {
        log.info("上传音频文件，用户ID：{}，文件名：{}", userId, file.getOriginalFilename());
        return digitalUploadService.uploadAudio(file, userId);
    }

    /**
     * 上传视频文件
     * URL: /api/v1/system/upload/video
     * Method: POST
     */
    @Operation(summary = "上传视频文件", description = "上传视频文件")
    @PostMapping(value = "/video", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> uploadVideo(
            @Parameter(description = "视频文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId,
            @Parameter(description = "组ID") @RequestParam("groupId") String groupId,
            @Parameter(description = "是否认证：0-否，1-是") @RequestParam(defaultValue = "0") Integer isAuth) {
        log.info("上传视频文件，用户ID：{}，组ID：{}，认证：{}，文件名：{}", userId, groupId, isAuth, file.getOriginalFilename());
        return digitalUploadService.uploadVideo(file, userId, groupId, isAuth);
    }

    /**
     * 上传形象视频
     * URL: /api/v1/system/upload/digital-video
     * Method: POST
     */
    @Operation(summary = "上传形象视频", description = "上传数字人形象视频")
    @PostMapping(value = "/digital-video", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> uploadDigitalVideo(
            @Parameter(description = "形象视频文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "用户ID") @RequestParam("userId") String userId) {
        log.info("上传形象视频，用户ID：{}，文件名：{}", userId, file.getOriginalFilename());
        return digitalUploadService.uploadDigitalVideo(file, userId);
    }

    /**
     * 批量上传系统音色音频
     * URL: /api/v1/system/upload/voice/styles/batch
     * Method: POST
     */
    @Operation(summary = "批量上传系统音色音频", description = "批量上传系统音色音频文件")
    @PostMapping(value = "/voice/styles/batch", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<Void> batchUploadVoiceStyles(
            @RequestPart("files") List<MultipartFile> files,
            @RequestParam("voiceIds") List<String> voiceIds,
            @RequestParam("userId") String userId) {
        log.info("批量上传系统音色音频，文件数量：{}，音色ID数量：{}，用户ID：{}",
                files.size(), voiceIds.size(), userId);

        if (files.size() != voiceIds.size()) {
            return Result.ERROR("文件数量与音色ID数量不匹配");
        }

        List<DigitalVoiceStyleUploadDTO> uploadDTOList = new ArrayList<>();
        for (int i = 0; i < files.size(); i++) {
            DigitalVoiceStyleUploadDTO dto = new DigitalVoiceStyleUploadDTO();
            dto.setAudioFile(files.get(i));
            dto.setVoiceId(voiceIds.get(i));
            uploadDTOList.add(dto);
        }

        return digitalUploadService.batchUploadVoiceStyles(uploadDTOList, userId);
    }


}
