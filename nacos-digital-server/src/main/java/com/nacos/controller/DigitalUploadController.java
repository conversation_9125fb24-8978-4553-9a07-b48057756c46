package com.nacos.controller;

import com.nacos.entity.dto.DigitalVoiceStyleUploadDTO;
import com.nacos.result.Result;
import com.nacos.service.DigitalUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("")
@Tag(name = "数字人上传接口")
public class DigitalUploadController {

    @Autowired
    private DigitalUploadService digitalUploadService;

    /**
     * 批量上传系统音色音频
     *
     * @param files 音频文件列表
     * @param voiceIds 音色ID列表
     * @param userId 用户ID
     * @return 上传结果
     */
    @Operation(summary = "批量上传系统音色音频")
    @PostMapping(value = "/voice/styles/upload/batch", consumes = "multipart/form-data")
    public Result<Void> batchUploadVoiceStyles(
            @RequestPart("files") List<MultipartFile> files,
            @RequestParam("voiceIds") List<String> voiceIds,
            @RequestParam("userId") String userId) {
        log.info("开始批量上传系统音色音频，文件数量：{}，音色ID数量：{}，用户ID：{}", 
                files.size(), voiceIds.size(), userId);
                
        if (files.size() != voiceIds.size()) {
            return Result.ERROR("文件数量与音色ID数量不匹配");
        }
        
        List<DigitalVoiceStyleUploadDTO> uploadDTOList = new ArrayList<>();
        for (int i = 0; i < files.size(); i++) {
            DigitalVoiceStyleUploadDTO dto = new DigitalVoiceStyleUploadDTO();
            dto.setAudioFile(files.get(i));
            dto.setVoiceId(voiceIds.get(i));
            uploadDTOList.add(dto);
        }
        
        return digitalUploadService.batchUploadVoiceStyles(uploadDTOList, userId);
    }

    /**
     * 上传视频
     * url: /video/upload
     * method: POST
     * @param file
     * @param userId
     * @param groupId
     * @param isAuth
     * @return
     */
    @PostMapping("/video/upload")
    public Result<String> uploadVideo(
            @RequestParam("file") MultipartFile file,
            @RequestParam("userId") String userId,
            @RequestParam("groupId") String groupId,
            @RequestParam("isAuth") Integer isAuth
            ) {
        return digitalUploadService.uploadVideo(file, userId, groupId, isAuth);
    }



//    @PostMapping("/image/upload")
//    public Result<String> uploadDigitalVideo(@RequestParam("file") MultipartFile file,
//                                           @RequestParam("userId") String userId) {
//        try {
//            String path = DigitalUploadService.uploadDigitalVideo(file, userId);
//            if (path != null) {
//                return Result.SUCCESS(path);
//            }
//            return Result.ERROR("上传失败");
//        } catch (Exception e) {
//            log.error("上传形象视频失败：", e);
//            return Result.ERROR("上传失败：" + e.getMessage());
//        }
//    }

}
