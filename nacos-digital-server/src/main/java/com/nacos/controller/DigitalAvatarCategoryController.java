package com.nacos.controller;

import com.nacos.entity.po.DigitalSystemGroupPO;
import com.nacos.entity.vo.DigitalAvatarCategoryVO;
import com.nacos.entity.vo.DigitalSystemGroupVO;
import com.nacos.service.DigitalAvatarCategoryService;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Slf4j
@Tag(name = "数字人分类管理")
@RestController
@RequestMapping("/avatar/category")
public class DigitalAvatarCategoryController {

    @Autowired
    private DigitalAvatarCategoryService categoryService;

    /**
     * 获取分类列表
     * URL: /avatar/category/list
     * Method: GET
     * @return List<DigitalAvatarCategoryVO>
     */
    @Operation(summary = "获取分类列表")
    @GetMapping("/list")
    public Result<List<DigitalAvatarCategoryVO>> listCategories() {
        return categoryService.listCategories();
    }

    /**
     *  添加分类
     * URL: /avatar/category/add
     * Method: POST
     * @param categoryVO
     * @return boolean
     */
    @Operation(summary = "添加分类")
    @PostMapping("/add")
    public Result<Boolean> addCategory(@RequestBody DigitalAvatarCategoryVO categoryVO) {
        return categoryService.addCategory(categoryVO);
    }

    /**
     *  更新分类
     * URL: /avatar/category/update
     * Method: PUT
     * @param categoryVO
     * @return boolean
     */
    @Operation(summary = "更新分类")
    @PutMapping("/update")
    public Result<Boolean> updateCategory(@RequestBody DigitalAvatarCategoryVO categoryVO) {
        return categoryService.updateCategory(categoryVO);
    }

    /**
     *  删除分类
     * URL: /avatar/category/delete/{id}
     * Method: DELETE
     * @param id
     * @return boolean
     */
    @Operation(summary = "删除分类")
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> deleteCategory(@PathVariable Long id) {
        return categoryService.deleteCategory(id);
    }

    /**
     *  更新分类状态
     * URL: /avatar/category/status/{id}/{status}
     * Method: PUT
     * @param id
     * @param status
     * @return boolean
     */
    @Operation(summary = "更新分类状态")
    @PutMapping("/status/{id}/{status}")
    public Result<Boolean> updateStatus(@PathVariable Long id, @PathVariable Integer status) {
        return categoryService.updateStatus(id, status);
    }

    /**
     * 根据分类编码获取系统组列表
     * URL: /avatar/category/groups/{categoryCode}
     * Method: GET
     * @param categoryCode 分类编码
     * @return 系统组列表
     */
    @Operation(summary = "根据分类编码获取系统组列表")
    @GetMapping("/groups/{categoryCode}")
    public Result<List<DigitalSystemGroupVO>> listSystemGroupsByCategoryCode(@PathVariable String categoryCode) {
        return categoryService.listSystemGroupsByCategoryCode(categoryCode);
    }
} 