package com.nacos.controller.media;

import com.nacos.entity.vo.DigitalVoiceStyleVO;
import com.nacos.entity.vo.DigitalVoiceUserCloneVO;
import com.nacos.result.Result;
import com.nacos.service.DigitalAudioService;
import com.nacos.service.DigitalVoiceUserCloneService;
import com.nacos.service.DigitalVoiceCategoryService;
import com.nacos.service.DigitalVoiceSystemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 音色管理业务Controller
 * 专注于音色克隆、分类管理等业务逻辑操作
 * 数据CRUD操作已迁移到admin包的AudioAdminController
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "音色管理业务", description = "音色克隆与分类管理业务逻辑")
@RestController
@RequestMapping("/api/v1/media/voice")
public class VoiceController {

    @Autowired
    private DigitalAudioService digitalAudioService;

    @Autowired
    private DigitalVoiceUserCloneService voiceUserCloneService;

    @Autowired
    private DigitalVoiceCategoryService voiceCategoryService;

    @Autowired
    private DigitalVoiceSystemService digitalVoiceSystemService;

    /**
     * 获取系统音色列表（业务接口）
     * URL: /api/v1/media/voice/system/list
     * Method: GET
     */
    @Operation(summary = "获取系统音色列表", description = "获取可用的系统音色列表")
    @GetMapping("/system/list")
    public Result<List<DigitalVoiceStyleVO>> getSystemVoices() {
        log.info("获取系统音色列表");
        return digitalAudioService.getSystemVoiceStyles();
    }

    /**
     * 获取用户音色列表（业务接口）
     * URL: /api/v1/media/voice/user/list
     * Method: GET
     */
    @Operation(summary = "获取用户音色列表", description = "获取指定用户的音色列表")
    @GetMapping("/user/list")
    public Result<List<DigitalVoiceUserCloneVO>> getUserVoices(
            @Parameter(description = "用户ID") @RequestParam("userId") String userId) {
        log.info("获取用户音色列表，用户ID：{}", userId);
        return digitalAudioService.getUserVoiceStyles(userId);
    }

    /**
     * 音色推荐接口
     * URL: /api/v1/media/voice/recommend
     * Method: GET
     */
    @Operation(summary = "音色推荐", description = "根据用户偏好推荐合适的音色")
    @GetMapping("/recommend")
    public Result<List<DigitalVoiceStyleVO>> recommendVoices(
            @Parameter(description = "用户ID") @RequestParam("userId") String userId,
            @Parameter(description = "推荐类型") @RequestParam(defaultValue = "popular") String type) {
        log.info("音色推荐，用户ID：{}，类型：{}", userId, type);

        try {
            // 优先使用新的推荐服务，如果失败则回退到旧服务
            if ("popular".equals(type)) {
                // 使用新的推荐服务
                List<com.nacos.entity.vo.DigitalVoiceSystemVO> newVoices = digitalVoiceSystemService.getRecommendedVoices(10);
                if (newVoices != null && !newVoices.isEmpty()) {
                    // 转换为旧的VO格式以保持兼容性
                    List<DigitalVoiceStyleVO> compatibleVoices = convertToCompatibleFormat(newVoices);
                    return Result.SUCCESS(compatibleVoices);
                }
            }

            // 回退到原有的系统音色服务
            return digitalAudioService.getSystemVoiceStyles();
        } catch (Exception e) {
            log.warn("新推荐服务失败，回退到原有服务，userId: {}", userId, e);
            return digitalAudioService.getSystemVoiceStyles();
        }
    }

    /**
     * 获取启用的音色分类
     * URL: /api/v1/media/voice/categories/enabled
     * Method: GET
     */
    @Operation(summary = "获取启用的音色分类", description = "获取所有启用状态的音色分类")
    @GetMapping("/categories/enabled")
    public Result<List<com.nacos.entity.po.DigitalVoiceCategoryPO>> getEnabledCategories() {
        log.info("获取启用的音色分类列表");
        List<com.nacos.entity.po.DigitalVoiceCategoryPO> categories = voiceCategoryService.listEnabledCategories();
        return Result.SUCCESS(categories);
    }

    /**
     * 根据分类编码获取音色ID列表
     * URL: /api/v1/media/voice/categories/{categoryCode}/voice-ids
     * Method: GET
     */
    @Operation(summary = "根据分类编码获取音色ID列表", description = "获取指定分类下的音色ID列表")
    @GetMapping("/categories/{categoryCode}/voice-ids")
    public Result<List<String>> getVoiceIdsByCategory(
            @Parameter(description = "分类编码") @PathVariable String categoryCode) {
        log.info("根据分类编码获取音色ID列表，分类编码：{}", categoryCode);
        List<String> voiceIds = voiceCategoryService.listVoiceIdsByCategory(categoryCode);
        return Result.SUCCESS(voiceIds);
    }

    /**
     * 高级音色搜索（新增接口）
     * URL: /api/v1/media/voice/advanced-search
     * Method: GET
     */
    @Operation(summary = "高级音色搜索", description = "支持多条件的高级音色搜索功能")
    @GetMapping("/advanced-search")
    public Result<List<DigitalVoiceStyleVO>> advancedSearch(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "供应商") @RequestParam(required = false) String provider,
            @Parameter(description = "语言") @RequestParam(required = false) String language,
            @Parameter(description = "性别") @RequestParam(required = false) String gender,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("高级音色搜索，keyword: {}, provider: {}, language: {}, gender: {}", keyword, provider, language, gender);

        try {
            // 使用新的搜索服务
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.nacos.entity.vo.DigitalVoiceSystemVO> result;

            if (keyword != null && !keyword.trim().isEmpty()) {
                result = digitalVoiceSystemService.searchVoices(keyword, page, size);
            } else if (provider != null && !provider.trim().isEmpty()) {
                result = digitalVoiceSystemService.getVoicesByProvider(provider, page, size);
            } else {
                // 默认获取推荐音色
                List<com.nacos.entity.vo.DigitalVoiceSystemVO> recommended = digitalVoiceSystemService.getRecommendedVoices(size);
                List<DigitalVoiceStyleVO> compatible = convertToCompatibleFormat(recommended);
                return Result.SUCCESS(compatible);
            }

            List<DigitalVoiceStyleVO> compatible = convertToCompatibleFormat(result.getRecords());
            return Result.SUCCESS(compatible);
        } catch (Exception e) {
            log.error("高级音色搜索失败", e);
            // 回退到原有服务
            return digitalAudioService.getSystemVoiceStyles();
        }
    }

    /**
     * 获取音色详细信息（新增接口）
     * URL: /api/v1/media/voice/detail/{voiceId}
     * Method: GET
     */
    @Operation(summary = "获取音色详细信息", description = "获取指定音色的详细信息，包含标签等扩展信息")
    @GetMapping("/detail/{voiceId}")
    public Result<Object> getVoiceDetail(
            @Parameter(description = "音色ID") @PathVariable String voiceId) {
        log.info("获取音色详细信息，voiceId: {}", voiceId);

        try {
            // 优先使用新的详情服务
            com.nacos.entity.vo.DigitalVoiceSystemVO newDetail = digitalVoiceSystemService.getVoiceDetail(voiceId);
            if (newDetail != null) {
                return Result.SUCCESS(newDetail);
            }

            // 回退到原有服务（这里需要根据实际情况实现）
            log.warn("新服务未找到音色详情，voiceId: {}", voiceId);
            return Result.ERROR("音色不存在");
        } catch (Exception e) {
            log.error("获取音色详细信息失败，voiceId: {}", voiceId, e);
            return Result.ERROR("获取音色详情失败：" + e.getMessage());
        }
    }

    // ========================================
    // 私有辅助方法
    // ========================================

    /**
     * 转换新VO格式为兼容的旧VO格式
     */
    private List<DigitalVoiceStyleVO> convertToCompatibleFormat(List<com.nacos.entity.vo.DigitalVoiceSystemVO> newVoices) {
        if (newVoices == null || newVoices.isEmpty()) {
            return new java.util.ArrayList<>();
        }

        return newVoices.stream().map(newVoice -> {
            DigitalVoiceStyleVO oldVoice = new DigitalVoiceStyleVO();
            // 使用新的音色ID，保持兼容性
            oldVoice.setVoiceId(newVoice.getVoiceId());
            oldVoice.setVoiceName(newVoice.getVoiceName());
            oldVoice.setProvider(newVoice.getProvider());
            oldVoice.setDescription(newVoice.getDescription());
            oldVoice.setLanguage(newVoice.getLanguage());
            oldVoice.setGender(newVoice.getGender());
            oldVoice.setDemoAudio(newVoice.getDemoAudioUrl());
            // 设置其他属性为空，因为DigitalVoiceStyleVO没有这些字段
            return oldVoice;
        }).collect(java.util.stream.Collectors.toList());
    }
}
