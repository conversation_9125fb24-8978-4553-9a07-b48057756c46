package com.nacos.controller.media;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nacos.entity.dto.VoiceMultiDimensionQueryDTO;
import com.nacos.entity.vo.*;
import com.nacos.result.Result;
import com.nacos.service.DigitalVoiceSystemService;
import com.nacos.service.DigitalVoiceClassifyDimensionService;
import com.nacos.service.DigitalVoiceClassifyTagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 音色分类管理Controller
 * 提供新的多维度查询和分类管理功能
 */
@Slf4j
@Tag(name = "音色分类管理", description = "音色多维度查询和分类管理API")
@RestController
@RequestMapping("/api/v1/media/voice/classify")
@RequiredArgsConstructor
@Validated
public class VoiceClassifyController {

    private final DigitalVoiceSystemService digitalVoiceSystemService;
    private final DigitalVoiceClassifyDimensionService voiceClassifyDimensionService;
    private final DigitalVoiceClassifyTagService digitalVoiceClassifyTagService;

    /**
     * 多维度查询音色
     * URL: /api/v1/media/voice/classify/multi-dimension-query
     * Method: POST
     */
    @Operation(summary = "多维度查询音色", description = "支持按维度、标签、关键词等多条件查询音色")
    @PostMapping("/multi-dimension-query")
    public Result<VoiceMultiDimensionQueryVO> multiDimensionQuery(
            @Valid @RequestBody VoiceMultiDimensionQueryDTO queryDTO) {
        log.info("多维度查询音色，查询条件：{}", queryDTO);
        
        try {
            VoiceMultiDimensionQueryVO result = digitalVoiceSystemService.multiDimensionQuery(
                queryDTO.getDimensionConditions(),
                queryDTO.getKeyword(),
                queryDTO.getProviders(),
                queryDTO.getLanguages(),
                queryDTO.getGenders(),
                queryDTO.getSyncIds(),
                queryDTO.getThirdPartyVoiceIds(),
                queryDTO.getQueryLogic(),
                queryDTO.getCurrentPage(),
                queryDTO.getPageSize()
            );
            
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("多维度查询音色失败", e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有维度列表
     * URL: /api/v1/media/voice/classify/dimensions
     * Method: GET
     */
    @Operation(summary = "获取所有维度列表", description = "获取启用状态的所有音色分类维度")
    @GetMapping("/dimensions")
    public Result<List<VoiceClassifyDimensionVO>> listDimensions() {
        log.info("获取所有维度列表");
        
        try {
            List<VoiceClassifyDimensionVO> dimensions = voiceClassifyDimensionService.listEnabledDimensions();
            return Result.SUCCESS(dimensions);
        } catch (Exception e) {
            log.error("获取维度列表失败", e);
            return Result.ERROR("获取维度列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取维度及其标签树
     * URL: /api/v1/media/voice/classify/dimensions/with-tags
     * Method: GET
     */
    @Operation(summary = "获取维度及其标签树", description = "获取所有维度及其下的标签层级结构")
    @GetMapping("/dimensions/with-tags")
    public Result<List<VoiceClassifyDimensionVO>> listDimensionsWithTags() {
        log.info("获取维度及其标签树");
        
        try {
            List<VoiceClassifyDimensionVO> dimensions = voiceClassifyDimensionService.listDimensionsWithTags();
            return Result.SUCCESS(dimensions);
        } catch (Exception e) {
            log.error("获取维度及标签树失败", e);
            return Result.ERROR("获取维度及标签树失败：" + e.getMessage());
        }
    }

    /**
     * 根据维度编码获取标签列表
     * URL: /api/v1/media/voice/classify/dimensions/{dimensionCode}/tags
     * Method: GET
     */
    @Operation(summary = "根据维度编码获取标签列表", description = "获取指定维度下的所有标签")
    @GetMapping("/dimensions/{dimensionCode}/tags")
    public Result<List<VoiceClassifyTagVO>> getTagsByDimension(
            @Parameter(description = "维度编码") @PathVariable @NotBlank String dimensionCode) {
        log.info("根据维度编码获取标签列表，dimensionCode: {}", dimensionCode);
        
        try {
            List<VoiceClassifyTagVO> tags = digitalVoiceClassifyTagService.listTagsByDimension(dimensionCode);
            return Result.SUCCESS(tags);
        } catch (Exception e) {
            log.error("获取标签列表失败，dimensionCode: {}", dimensionCode, e);
            return Result.ERROR("获取标签列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据维度编码获取标签树
     * URL: /api/v1/media/voice/classify/dimensions/{dimensionCode}/tag-tree
     * Method: GET
     */
    @Operation(summary = "根据维度编码获取标签树", description = "获取指定维度下的层级标签树结构")
    @GetMapping("/dimensions/{dimensionCode}/tag-tree")
    public Result<List<VoiceClassifyTagVO>> getTagTreeByDimension(
            @Parameter(description = "维度编码") @PathVariable @NotBlank String dimensionCode) {
        log.info("根据维度编码获取标签树，dimensionCode: {}", dimensionCode);
        
        try {
            List<VoiceClassifyTagVO> tagTree = digitalVoiceClassifyTagService.getTagTreeByDimension(dimensionCode);
            return Result.SUCCESS(tagTree);
        } catch (Exception e) {
            log.error("获取标签树失败，dimensionCode: {}", dimensionCode, e);
            return Result.ERROR("获取标签树失败：" + e.getMessage());
        }
    }

    /**
     * 根据标签ID列表获取音色
     * URL: /api/v1/media/voice/classify/voices/by-tags
     * Method: POST
     */
    @Operation(summary = "根据标签ID列表获取音色", description = "根据标签ID列表查询音色，支持AND/OR逻辑")
    @PostMapping("/voices/by-tags")
    public Result<Page<DigitalVoiceSystemVO>> getVoicesByTags(
            @Parameter(description = "标签ID列表") @RequestBody @NotEmpty List<Long> tagIds,
            @Parameter(description = "查询逻辑") @RequestParam(defaultValue = "OR") String queryLogic,
            @Parameter(description = "当前页码") @RequestParam(defaultValue = "1") Integer currentPage,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer pageSize) {
        log.info("根据标签ID列表获取音色，tagIds: {}, queryLogic: {}", tagIds, queryLogic);
        
        try {
            Page<DigitalVoiceSystemVO> result = digitalVoiceSystemService.getVoicesByTags(
                tagIds, queryLogic, currentPage, pageSize);
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("根据标签获取音色失败，tagIds: {}", tagIds, e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据供应商获取音色
     * URL: /api/v1/media/voice/classify/voices/by-provider/{provider}
     * Method: GET
     */
    @Operation(summary = "根据供应商获取音色", description = "获取指定供应商的音色列表")
    @GetMapping("/voices/by-provider/{provider}")
    public Result<Page<DigitalVoiceSystemVO>> getVoicesByProvider(
            @Parameter(description = "供应商标识") @PathVariable @NotBlank String provider,
            @Parameter(description = "当前页码") @RequestParam(defaultValue = "1") Integer currentPage,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer pageSize) {
        log.info("根据供应商获取音色，provider: {}", provider);
        
        try {
            Page<DigitalVoiceSystemVO> result = digitalVoiceSystemService.getVoicesByProvider(
                provider, currentPage, pageSize);
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("根据供应商获取音色失败，provider: {}", provider, e);
            return Result.ERROR("查询失败：" + e.getMessage());
        }
    }

    /**
     * 搜索音色
     * URL: /api/v1/media/voice/classify/voices/search
     * Method: GET
     */
    @Operation(summary = "搜索音色", description = "根据关键词搜索音色")
    @GetMapping("/voices/search")
    public Result<Page<DigitalVoiceSystemVO>> searchVoices(
            @Parameter(description = "搜索关键词") @RequestParam @NotBlank String keyword,
            @Parameter(description = "当前页码") @RequestParam(defaultValue = "1") Integer currentPage,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer pageSize) {
        log.info("搜索音色，keyword: {}", keyword);
        
        try {
            Page<DigitalVoiceSystemVO> result = digitalVoiceSystemService.searchVoices(
                keyword, currentPage, pageSize);
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("搜索音色失败，keyword: {}", keyword, e);
            return Result.ERROR("搜索失败：" + e.getMessage());
        }
    }

    /**
     * 获取推荐音色
     * URL: /api/v1/media/voice/classify/voices/recommended
     * Method: GET
     */
    @Operation(summary = "获取推荐音色", description = "获取系统推荐的音色列表")
    @GetMapping("/voices/recommended")
    public Result<List<DigitalVoiceSystemVO>> getRecommendedVoices(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        log.info("获取推荐音色，limit: {}", limit);
        
        try {
            List<DigitalVoiceSystemVO> result = digitalVoiceSystemService.getRecommendedVoices(limit);
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("获取推荐音色失败", e);
            return Result.ERROR("获取推荐音色失败：" + e.getMessage());
        }
    }

    /**
     * 获取音色详情
     * URL: /api/v1/media/voice/classify/voices/{voiceId}
     * Method: GET
     */
    @Operation(summary = "获取音色详情", description = "根据音色ID获取详细信息，包含标签信息")
    @GetMapping("/voices/{voiceId}")
    public Result<DigitalVoiceSystemVO> getVoiceDetail(
            @Parameter(description = "音色ID") @PathVariable @NotBlank String voiceId) {
        log.info("获取音色详情，voiceId: {}", voiceId);
        
        try {
            DigitalVoiceSystemVO result = digitalVoiceSystemService.getVoiceDetail(voiceId);
            if (result == null) {
                return Result.ERROR("音色不存在");
            }
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("获取音色详情失败，voiceId: {}", voiceId, e);
            return Result.ERROR("获取音色详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取音色统计信息
     * URL: /api/v1/media/voice/classify/statistics
     * Method: GET
     */
    @Operation(summary = "获取音色统计信息", description = "获取音色的统计信息，包括总数、分类统计等")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getVoiceStatistics() {
        log.info("获取音色统计信息");
        
        try {
            Map<String, Object> statistics = digitalVoiceSystemService.getVoiceStatistics();
            return Result.SUCCESS(statistics);
        } catch (Exception e) {
            log.error("获取音色统计信息失败", e);
            return Result.ERROR("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据维度统计音色数量
     * URL: /api/v1/media/voice/classify/statistics/by-dimensions
     * Method: GET
     */
    @Operation(summary = "根据维度统计音色数量", description = "获取各维度下标签的音色数量统计")
    @GetMapping("/statistics/by-dimensions")
    public Result<Map<String, Map<String, Integer>>> getVoiceCountByDimensions() {
        log.info("根据维度统计音色数量");
        
        try {
            Map<String, Map<String, Integer>> statistics = digitalVoiceSystemService.getVoiceCountByDimensions();
            return Result.SUCCESS(statistics);
        } catch (Exception e) {
            log.error("根据维度统计音色数量失败", e);
            return Result.ERROR("获取统计信息失败：" + e.getMessage());
        }
    }
}
