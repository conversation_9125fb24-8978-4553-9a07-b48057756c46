package com.nacos.controller.media;

import com.nacos.entity.dto.DigitalVideoGenerationDTO;
import com.nacos.result.Result;
import com.nacos.service.DigitalVideoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

/**
 * 视频处理业务Controller
 * 专注于视频生成、编辑等业务逻辑操作
 * 数据CRUD操作已迁移到admin包的VideoAdminController
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "视频处理业务", description = "视频生成与编辑业务逻辑")
@RestController
@RequestMapping("/api/v1/media/video")
public class VideoController {

    @Autowired
    private DigitalVideoService digitalVideoService;

    /**
     * 数字人视频生成接口
     * URL: /digital/api/v1/media/video/generate
     * Method: POST
     */
    @Operation(summary = "数字人视频生成", description = "提交数字人视频生成请求（异步）")
    @PostMapping(value = "/generate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Schema(description = "提交数字人视频生成请求（异步）")
    public Result<String> generateVideo(
            @ModelAttribute @Valid DigitalVideoGenerationDTO digitalVideoGenerationDTO) {
        log.info("数字人视频生成请求，参数：{}", digitalVideoGenerationDTO);
        return digitalVideoService.submitDigitalVideoGenerationRequest(digitalVideoGenerationDTO);
    }

    /**
     * 处理排队中的视频任务
     * URL: /digital/api/v1/media/video/process/queue
     * Method: POST
     */
    @Operation(summary = "处理排队中的视频任务", description = "手动触发处理排队中的视频生成任务")
    @PostMapping("/process/queue")
    public Result<String> processQueueingTasks() {
        log.info("手动处理排队中的视频任务");
        digitalVideoService.processQueueingTasks();
        return Result.SUCCESS("排队任务处理已触发", "success");
    }

    /**
     * 处理超时视频任务
     * URL: /digital/api/v1/media/video/process/timeout
     * Method: POST
     */
    @Operation(summary = "处理超时视频任务", description = "手动触发处理超时的视频生成任务")
    @PostMapping("/process/timeout")
    public Result<String> processTimeoutTasks() {
        log.info("手动处理超时的视频任务");
        digitalVideoService.processTimeoutTasks();
        return Result.SUCCESS("超时任务处理已触发", "success");
    }
}
