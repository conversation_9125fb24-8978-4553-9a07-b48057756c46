package com.nacos.controller.task;

import com.nacos.entity.vo.DigitalVideoTaskVO;
import com.nacos.entity.bo.TaskStatusUpdateBO;
import com.nacos.service.DigitalVideoTaskService;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 视频任务业务Controller
 * 专注于视频任务的业务逻辑操作
 * 数据CRUD操作已迁移到admin包的TaskAdminController
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "视频任务业务", description = "视频任务业务逻辑操作")
@RestController
@RequestMapping("/api/v1/tasks/video")
public class VideoTaskController {

    @Autowired
    private DigitalVideoTaskService digitalVideoTaskService;

    /**
     * 查询用户的视频任务列表
     * URL: /api/v1/tasks/video/user/{userId}
     * Method: GET
     */
    @Operation(summary = "查询用户的视频任务列表", description = "获取指定用户的视频任务列表")
    @GetMapping("/user/{userId}")
    public Result<List<DigitalVideoTaskVO>> getUserVideoTasks(
            @Parameter(description = "用户ID") @PathVariable String userId) {
        log.info("查询用户视频任务列表，用户ID：{}", userId);
        return digitalVideoTaskService.listUserTasks(userId);
    }

    /**
     * 查询视频任务详情
     * URL: /api/v1/tasks/video/{taskId}
     * Method: GET
     */
    @Operation(summary = "查询视频任务详情", description = "查询指定视频任务的详细信息")
    @GetMapping("/{taskId}")
    public Result<DigitalVideoTaskVO> getVideoTaskDetail(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        log.info("查询视频任务详情，任务ID：{}", taskId);
        return digitalVideoTaskService.getTaskDetail(taskId);
    }

    /**
     * 更新视频任务状态
     * URL: /api/v1/tasks/video/update
     * Method: PUT
     */
    @Operation(summary = "更新视频任务状态", description = "更新视频任务的状态")
    @PutMapping("/update")
    public Result<Boolean> updateVideoTaskStatus(@RequestBody TaskStatusUpdateBO updateBO) {
        log.info("更新视频任务状态，参数：{}", updateBO);
        return digitalVideoTaskService.updateTask(updateBO);
    }
}
