package com.nacos.controller.task;

import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.entity.vo.DigitalAudioTaskVO;
import com.nacos.service.IDigitalAudioTaskService;
import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 音频任务业务Controller
 * 专注于音频任务的业务逻辑操作
 * 数据CRUD操作已迁移到admin包的TaskAdminController
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Tag(name = "音频任务业务", description = "音频任务业务逻辑操作")
@RestController
@RequestMapping("/api/v1/tasks/audio")
public class AudioTaskController {

    @Autowired
    private IDigitalAudioTaskService digitalAudioTaskService;

    /**
     * 创建音频生成任务
     * URL: /api/v1/tasks/audio/create
     * Method: POST
     */
    @Operation(summary = "创建音频生成任务", description = "创建新的音频生成任务")
    @PostMapping("/create")
    public Result<String> createAudioTask(@RequestBody DigitalAudioTaskPO taskPO) {
        log.info("创建音频生成任务，参数：{}", taskPO);
        return digitalAudioTaskService.createTask(taskPO);
    }

    /**
     * 查询任务详情
     * URL: /api/v1/tasks/audio/{taskId}
     * Method: GET
     */
    @Operation(summary = "查询任务详情", description = "查询音频生成任务的详细信息")
    @GetMapping("/{taskId}")
    public Result<DigitalAudioTaskVO> getTaskDetail(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        log.info("查询音频任务详情，任务ID：{}", taskId);
        DigitalAudioTaskVO task = digitalAudioTaskService.findByTaskId(taskId);
        return Result.SUCCESS(task);
    }

    /**
     * 更新任务状态
     * URL: /api/v1/tasks/audio/{taskId}/status/{status}
     * Method: PUT
     */
    @Operation(summary = "更新任务状态", description = "更新音频生成任务的状态")
    @PutMapping("/{taskId}/status/{status}")
    public Result<Boolean> updateTaskStatus(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "任务状态") @PathVariable Integer status,
            @Parameter(description = "备注信息") @RequestParam(required = false) String remark) {
        log.info("更新音频任务状态，任务ID：{}，状态：{}，备注：{}", taskId, status, remark);
        boolean result = digitalAudioTaskService.updateTaskStatus(taskId, status, remark);
        return Result.SUCCESS(result);
    }

    /**
     * 获取用户任务列表
     * URL: /api/v1/tasks/audio/user/{userId}
     * Method: GET
     */
    @Operation(summary = "获取用户任务列表", description = "获取指定用户的音频任务列表")
    @GetMapping("/user/{userId}")
    public Result<List<DigitalAudioTaskVO>> getUserTasks(
            @Parameter(description = "用户ID") @PathVariable String userId) {
        log.info("获取用户音频任务列表，用户ID：{}", userId);
        List<DigitalAudioTaskVO> tasks = digitalAudioTaskService.findByUserId(userId);
        return Result.SUCCESS(tasks);
    }
}
