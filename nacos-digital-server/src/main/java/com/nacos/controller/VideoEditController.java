package com.nacos.controller;

import com.nacos.entity.dto.VideoEditRequestDTO;
import com.nacos.entity.dto.VideoEditTaskItem;
import com.nacos.entity.vo.VideoEditTaskVO;
import com.nacos.entity.vo.VideoEditTaskStatusVO;
import com.nacos.result.Result;
import com.nacos.service.VideoEditService;
import com.nacos.service.VideoEditTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 视频编辑控制器
 * 参考DigitalVideoController的设计模式，提供视频编辑功能的REST API接口
 */
@Tag(name = "视频编辑", description = "视频编辑功能接口")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/video")
public class VideoEditController {

    private final VideoEditService videoEditService;
    private final VideoEditTaskService videoEditTaskService;

    /**
     * 提交视频编辑请求（异步）- JSON格式
     * URL: /video/edit
     * Method: POST
     *
     * @param videoEditRequestDTO 视频编辑请求参数
     * @return 任务ID
     */
    @Operation(summary = "提交视频编辑请求（异步）- JSON格式", description = "任务提交后将异步处理，可通过任务ID查询处理状态。支持JSON格式请求体。")
    @PostMapping(value = "/edit")
    public Result<String> submitVideoEditRequest(
            @Parameter(description = "视频编辑请求参数，包含任务列表和处理参数") @RequestBody @Valid VideoEditRequestDTO videoEditRequestDTO) {

        log.info("收到视频编辑请求（JSON），用户ID：{}，任务数量：{}",
                videoEditRequestDTO.getUserId(),
                videoEditRequestDTO.getTaskItems() != null ? videoEditRequestDTO.getTaskItems().size() : 0);

        return videoEditService.submitVideoEditRequest(videoEditRequestDTO);
    }

    /**
     * 查询视频编辑任务状态
     * URL: /video/edit/{taskId}
     * Method: GET
     * 
     * @param taskId 任务ID
     * @return 任务详情，包含子任务列表和处理状态
     */
    @Operation(summary = "查询视频编辑任务状态", description = "根据任务ID查询视频编辑任务的详细状态，包括主任务状态、" +
            "子任务列表、处理进度、输出结果等信息。")
    @GetMapping("/edit/{taskId}")
    public Result<VideoEditTaskVO> getEditTaskStatus(
            @Parameter(description = "任务ID", example = "VET_202501181200_1234") @PathVariable String taskId) {

        log.info("查询视频编辑任务状态，taskId：{}", taskId);

        return videoEditTaskService.getTaskDetail(taskId);
    }

    /**
     * 查询用户视频编辑历史
     * URL: /video/edit/history
     * Method: GET
     * 
     * @param userId 用户ID
     * @return 用户的视频编辑任务历史列表
     */
    @Operation(summary = "查询用户视频编辑历史", description = "查询指定用户的所有视频编辑任务历史记录，" +
            "按创建时间倒序排列，包含任务基本信息和状态。")
    @GetMapping("/edit/history")
    public Result<List<VideoEditTaskVO>> getEditTaskHistory(
            @Parameter(description = "用户ID", required = true) @RequestParam String userId) {

        log.info("查询用户视频编辑历史，userId：{}", userId);

        return videoEditTaskService.listUserTasks(userId);
    }

    /**
     * 获取用户活跃任务列表
     * URL: /video/edit/active
     * Method: GET
     *
     * @param userId 用户ID
     * @return 用户的进行中和成功的任务列表
     */
    @Operation(summary = "获取用户活跃任务列表", description = "查询指定用户当前活跃的视频编辑任务，" +
            "包括进行中和已完成的任务，用于实时状态展示。返回的任务状态包含完整的子任务信息和统计数据。")
    @GetMapping("/edit/active")
    public Result<List<VideoEditTaskStatusVO>> getActiveTaskList(
            @Parameter(description = "用户ID", required = true) @RequestParam String userId) {

        log.info("获取用户活跃任务列表，userId：{}", userId);

        List<VideoEditTaskStatusVO> activeTaskList = videoEditTaskService.getActiveTaskList(userId);
        return Result.SUCCESS(activeTaskList);
    }

    /**
     * 获取用户活跃任务列表（支持分页）
     * URL: /video/edit/active/paged
     * Method: GET
     *
     * @param userId 用户ID
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @param includeSubTasks 是否包含子任务详情
     * @return 用户的进行中和成功的任务列表
     */
    @Operation(summary = "获取用户活跃任务列表（分页）", description = "分页查询指定用户当前活跃的视频编辑任务，" +
            "支持控制是否包含子任务详情，适用于大量任务的场景。")
    @GetMapping("/edit/active/paged")
    public Result<List<VideoEditTaskStatusVO>> getActiveTaskListPaged(
            @Parameter(description = "用户ID", required = true) @RequestParam String userId,
            @Parameter(description = "页码，从1开始，默认为1") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页大小，默认为10") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "是否包含子任务详情，默认为true") @RequestParam(defaultValue = "true") Boolean includeSubTasks) {

        log.info("获取用户活跃任务列表（分页），userId：{}，pageNum：{}，pageSize：{}，includeSubTasks：{}",
                userId, pageNum, pageSize, includeSubTasks);

        return videoEditTaskService.getActiveTaskListPaged(userId, pageNum, pageSize, includeSubTasks);
    }

    /**
     * 获取任务状态（支持子任务分页）
     * URL: /video/edit/{taskId}/status/paged
     * Method: GET
     *
     * @param taskId 任务ID
     * @param subTaskPageNum 子任务页码（从1开始）
     * @param subTaskPageSize 子任务每页大小
     * @return 任务状态信息，包含分页的子任务列表
     */
    @Operation(summary = "获取任务状态（子任务分页）", description = "查询视频编辑任务的详细状态，" +
            "支持子任务分页加载，适用于子任务数量较多的场景。返回主任务状态和分页的子任务列表。")
    @GetMapping("/edit/{taskId}/status/paged")
    public Result<VideoEditTaskStatusVO> getTaskStatusWithPagedSubTasks(
            @Parameter(description = "任务ID", example = "VET_202501181200_1234") @PathVariable String taskId,
            @Parameter(description = "子任务页码，从1开始，默认为1") @RequestParam(defaultValue = "1") Integer subTaskPageNum,
            @Parameter(description = "子任务每页大小，默认为20") @RequestParam(defaultValue = "20") Integer subTaskPageSize) {

        log.info("获取任务状态（子任务分页），taskId：{}，subTaskPageNum：{}，subTaskPageSize：{}",
                taskId, subTaskPageNum, subTaskPageSize);

        return videoEditTaskService.getTaskStatusWithPagedSubTasks(taskId, subTaskPageNum, subTaskPageSize);
    }

    /**
     * 统计用户任务数量
     * URL: /video/edit/count
     * Method: GET
     * 
     * @param userId 用户ID
     * @param status 任务状态（可选），null表示统计所有状态
     * @return 任务数量
     */
    @Operation(summary = "统计用户任务数量", description = "统计指定用户的视频编辑任务数量，" +
            "可以按状态过滤，用于用户配额管理和统计展示。")
    @GetMapping("/edit/count")
    public Result<Integer> countUserTasks(
            @Parameter(description = "用户ID", required = true) @RequestParam String userId,
            @Parameter(description = "任务状态：0-排队中 1-进行中 2-处理中 3-编辑成功 4-失败 5-超时 6-已取消") @RequestParam(required = false) Integer status) {

        log.info("统计用户任务数量，userId：{}，status：{}", userId, status);

        return videoEditTaskService.countUserTasks(userId, status);
    }

    /**
     * 根据状态查询任务列表（管理员接口）
     * URL: /video/edit/admin/tasks
     * Method: GET
     * 
     * @param status 任务状态
     * @param limit  限制数量
     * @return 任务列表
     */
    @Operation(summary = "根据状态查询任务列表（管理员接口）", description = "管理员接口，用于查询指定状态的视频编辑任务列表，" +
            "主要用于系统监控和异常处理。")
    @GetMapping("/edit/admin/tasks")
    public Result<List<VideoEditTaskVO>> getTasksByStatus(
            @Parameter(description = "任务状态", required = true) @RequestParam Integer status,
            @Parameter(description = "限制数量，默认50") @RequestParam(defaultValue = "50") Integer limit) {

        log.info("根据状态查询任务列表，status：{}，limit：{}", status, limit);

        return videoEditTaskService.getTasksByStatus(status, limit);
    }
}
