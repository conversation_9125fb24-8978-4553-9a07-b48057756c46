package com.nacos.controller;

import com.nacos.result.Result;
import com.nacos.service.DigitalVoiceSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "同步管理（兼容接口）")
@Log4j2
@RestController
@RequiredArgsConstructor
@RequestMapping("/legacy")
public class VoiceSyncController {

    private final DigitalVoiceSyncService digitalVoiceSyncService;

    /**
     * 手动同步音色风格（兼容接口）
     * URL: /legacy/voice/sync
     * Method: POST
     * @return 同步结果
     */
    @Operation(summary = "手动同步音色信息（兼容接口）")
    @PostMapping("/voice/sync")
    public Result<String> syncVoiceStyles() {
        return digitalVoiceSyncService.syncAllVoiceData();
    }

    /**
     * 手动同步Minimax语音列表（兼容接口）
     * URL: /legacy/voice/sync/minimax
     * Method: POST
     * @return 同步结果
     */
    @Operation(summary = "手动同步Minimax语音列表（兼容接口）")
    @PostMapping("/voice/sync/minimax")
    public Result<String> syncMinimaxVoiceStyles() {
        return digitalVoiceSyncService.syncMinimaxVoiceData();
    }

    /**
     * 手动同步Microsoft语音列表（兼容接口）
     * URL: /legacy/voice/sync/microsoft
     * Method: POST
     * @return 同步结果
     */
    @Operation(summary = "手动同步Microsoft语音列表（兼容接口）")
    @PostMapping("/voice/sync/microsoft")
    public Result<String> syncMicrosoftVoiceStyles() {
        return digitalVoiceSyncService.syncMicrosoftVoiceData();
    }

    /**
     * 手动同步ElevenLabs语音列表（兼容接口）
     * URL: /legacy/voice/sync/elevenlabs
     * Method: POST
     * @return 同步结果
     */
    @Operation(summary = "手动同步ElevenLabs语音列表（兼容接口）")
    @PostMapping("/voice/sync/elevenlabs")
    public Result<String> syncElevenLabsVoiceStyles() {
        return digitalVoiceSyncService.syncElevenLabsVoiceData();
    }
} 