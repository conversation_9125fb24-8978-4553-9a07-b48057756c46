package com.nacos.controller;

import com.nacos.entity.vo.DigitalAudioTaskVO;
import com.nacos.entity.vo.PageResultVO;
import com.nacos.result.Result;
import com.nacos.service.IDigitalAudioTaskService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数字音频任务控制器
 */
@Tag(name = "数字音频任务", description = "数字音频任务相关接口")
@RestController
@RequestMapping("/audio/task")
public class DigitalAudioTaskController {

    @Autowired
    private IDigitalAudioTaskService digitalAudioTaskService;

    /**
     * 查询任务列表
     * URL: /audio/task/list
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param userId   用户ID
     * @param status   任务状态（可选）
     * @return 包含分页信息和任务列表的结果
     */
    @Operation(summary = "查询任务列表", description = "分页查询音频任务列表，支持按用户ID和状态筛选，默认查询状态为排队中、进行中、生成成功的任务")
    @GetMapping("/list")
    public Result<Page<DigitalAudioTaskVO>> listTasks(
            @Parameter(description = "页码", required = true) @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页数量", required = true) @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @Parameter(description = "用户ID") @RequestParam(value = "userId", required = true) String userId,
            @Parameter(description = "任务状态：0-排队中 1-进行中 2-生成成功 3-失败 4-超时 5-已取消，不传则默认查询0、1、2状态") 
            @RequestParam(value = "status", required = false) Integer status) {

        Map<String, Object> params = new HashMap<>();
        params.put("pageNum", pageNum);
        params.put("pageSize", pageSize);

        if (userId != null && !userId.isEmpty()) {
            params.put("userId", userId);
        }

        if (status != null) {
            params.put("status", status);
        }
        // 注意：如果status为null，服务层将默认查询状态为0、1、2的任务

        return digitalAudioTaskService.listTasks(params);
    }

    /**
     * 查询音频生成任务状态 API
     * URL: /audio/task/{taskId}
     * Method: GET
     * @param taskId 任务ID
     * @return Result 包含任务状态和结果
     */
    @GetMapping("/{taskId}")
    public Result<DigitalAudioTaskVO> getTaskStatus(@PathVariable String taskId) {
        DigitalAudioTaskVO task = digitalAudioTaskService.findByTaskId(taskId);
        if (task == null) {
            return Result.ERROR("任务不存在");
        }
        return Result.SUCCESS("查询成功", task);
    }
}