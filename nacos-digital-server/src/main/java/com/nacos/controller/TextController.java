//package com.nacos.controller;
//
//import cn.hutool.core.collection.CollectionUtil;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.business.db.model.dto.text.SceneRecordQueryDTO;
//import com.business.db.model.po.ConfigModelPO;
//import com.business.db.model.po.SceneConfigPO;
//import com.business.db.model.vo.text.SceneRecordQueryVO;
//import com.nacos.base.BaseDeleteEntity;
//import com.nacos.constant.CommonConst;
//import com.nacos.exception.IBusinessException;
//import com.nacos.model.dto.GptSceneGenerationDTO;
//import com.nacos.model.vo.IntonatVO;
//import com.nacos.result.Result;
//import com.nacos.service.ConfigModelService;
//import com.nacos.service.GptTextService;
//import com.nacos.service.SceneConfigService;
//import com.nacos.service.impl.TyqwServiceImpl;
//import io.swagger.v3.oas.annotations.Operation;
//import lombok.extern.log4j.Log4j2;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import java.util.List;
//
//@RestController
//@RequestMapping("/writingFcsDrop")
//@Log4j2
//public class TextController {
//
//    @Resource
//    private TyqwServiceImpl tyqwService;
//    @Resource
//    private GptTextService gptTextService;
//    @Resource
//    private SceneConfigService sceneConfigService;
//
//    @Resource
//    private ConfigModelService configModelService;
//
//    @PostMapping(value = "/sceneGeneration", name = "文案生成")
//    public Result<Long> sceneGeneration(@RequestBody GptSceneGenerationDTO gptSceneGenerationDTO) throws IBusinessException {
//        //  查询启用的文本类型的模型配置
//        List<ConfigModelPO> list = configModelService.queryByModelCategoryAndIsEnabled(CommonConst.MODEL_CATEGORY_TEXT, CommonConst.MODEL_ENABLED);
//        if (CollectionUtil.isEmpty(list)){
//            //默认调用
//            return gptTextService.sceneGeneration(gptSceneGenerationDTO);
//        } else {
//            ConfigModelPO po = list.get(0);
//            switch (po.getModelId()){
//                case CommonConst.MODEL_CATEGORY_TYQW:
//                    return tyqwService.sceneGeneration(gptSceneGenerationDTO);
//                default:
//                    return gptTextService.sceneGeneration(gptSceneGenerationDTO);
//            }
//
//        }
//
//    }
//
//    @Operation(summary = "获取图片标签列表：不同语言展示内容不同")
//    @GetMapping(value = "/querySceneWriter/{languageTagId}",name = "查询文案场景")
//    public Result<List<SceneConfigPO>> querySceneWriter(@PathVariable Long languageTagId){
//        return sceneConfigService.querySceneWriter(languageTagId);
//    }
//
//    @GetMapping(value = "/examples/{languageTagId}/{sceneId}", name = "随机查询一条场景示例")
//    public Result<String> examples(@PathVariable Long languageTagId, @PathVariable Long sceneId){
//        return sceneConfigService.randExamples(languageTagId, sceneId);
//    }
//
//    @GetMapping(value = "/intonatList/{languageTagId}", name = "语调列表")
//    public Result<List<IntonatVO>> intonationList(@PathVariable Long languageTagId) {
//        return sceneConfigService.intonationList(languageTagId);
//    }
//
//    @PostMapping(value = "/querysceneRecords", name = "查询文案记录列表")
//    public Result<Page<SceneRecordQueryVO>> querySceneRecordPage(@RequestBody SceneRecordQueryDTO dto){
//        return gptTextService.querySceneRecordPage(dto);
//    }
//
//    @PostMapping(value = "/delSceneRecord", name = "删除文案")
//    public Result<Integer> delLog(@Validated @RequestBody BaseDeleteEntity delete){
//        return gptTextService.delSceneRecord(delete);
//    }
//
//    @GetMapping(value = "/sceneTaskCount",name = "查询文案正在执行中的任务个数")
//    public Result<Long> selectSceneTaskCount() {
//        return gptTextService.selectSceneTaskCount();
//    }
//
//
//}
