package com.nacos.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频子任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum VideoTaskItemStatusEnum {
    PENDING(0, "待处理"),
    AUDIO_GENERATING(1, "音频生成中"),
    AUDIO_FAILED(2, "音频生成失败"),
    AUDIO_SUCCESS(3, "音频生成成功"),
    VIDEO_GENERATING(4, "视频生成中"),
    VIDEO_FAILED(5, "视频生成失败"),  
    VIDEO_SUCCESS(6, "视频生成成功"),
    STATUS_PROCESS(7, "状态确认中");;

    private final int value;
    private final String desc;

    public static String getDesc(int value) {
        for (VideoTaskItemStatusEnum status : VideoTaskItemStatusEnum.values()) {
            if (status.getValue() == value) {
                return status.getDesc();
            }
        }
        return "未知状态";
    }
} 