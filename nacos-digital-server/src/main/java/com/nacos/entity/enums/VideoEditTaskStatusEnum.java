package com.nacos.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频编辑主任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum VideoEditTaskStatusEnum {
    QUEUING(0, "排队中"),
    IN_PROGRESS(1, "进行中"),
    PROCESSING(2, "处理中"),
    SUCCESS(3, "编辑成功"),
    FAILED(4, "失败"),
    TIMEOUT(5, "超时"),
    CANCELLED(6, "已取消");

    private final int value;
    private final String desc;

    /**
     * 根据值获取描述
     * @param value 状态值
     * @return 对应的描述，如果不存在则返回"未知状态"
     */
    public static String getDesc(int value) {
        for (VideoEditTaskStatusEnum status : VideoEditTaskStatusEnum.values()) {
            if (status.getValue() == value) {
                return status.getDesc();
            }
        }
        return "未知状态";
    }

    /**
     * 根据值获取枚举
     * @param value 状态值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static VideoEditTaskStatusEnum getByValue(int value) {
        for (VideoEditTaskStatusEnum status : VideoEditTaskStatusEnum.values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为终态
     * @return true表示任务已结束（成功、失败、超时、取消），false表示任务仍在进行
     */
    public boolean isTerminal() {
        return this == SUCCESS || this == FAILED || this == TIMEOUT || this == CANCELLED;
    }

    /**
     * 判断是否为成功状态
     * @return true表示任务成功完成
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 判断是否为失败状态
     * @return true表示任务失败（包括失败、超时、取消）
     */
    public boolean isFailed() {
        return this == FAILED || this == TIMEOUT || this == CANCELLED;
    }
}
