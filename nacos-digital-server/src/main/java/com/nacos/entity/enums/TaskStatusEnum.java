package com.nacos.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频任务状态枚举
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {
    QUEUING(0, "排队中"),
    IN_PROGRESS(1, "进行中"),
    VIDEO_SUBMIT_SUCCESS(2, "进行中."),
    VIDEO_TRAIN_SUCCESS(3, "进行中.."),
    VOICE_UPLOAD_SUCCESS(4, "进行中..."),
    VOICE_CLONE_SUCCESS(5, "进行中...."),
    SUCCESS(6, "成功"),
    FAILED(7, "失败");

    private final int value;
    private final String desc;

    public static String getDesc(int value) {
        for (TaskStatusEnum status : TaskStatusEnum.values()) {
            if (status.getValue() == value) {
                return status.getDesc();
            }
        }
        return "未知状态";
    }
} 