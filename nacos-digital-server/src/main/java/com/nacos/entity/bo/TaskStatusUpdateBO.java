package com.nacos.entity.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 主任务数据更新BO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "主任务数据更新BO")
public class TaskStatusUpdateBO {

    /**
     * 任务ID
     */
    @Schema(description = "任务ID")
    private String taskId;

    /**
     * 视频URL
     */
    @Schema(description = "视频URL")
    private String videoUrl;

    /**
     * 视频封面URL
     */
    @Schema(description = "视频封面URL")
    private String coverUrl;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMsg;
} 