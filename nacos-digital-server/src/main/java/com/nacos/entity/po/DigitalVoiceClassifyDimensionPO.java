package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "音色分类维度表")
@TableName("digital_voice_classify_dimension")
public class DigitalVoiceClassifyDimensionPO {
    
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @Schema(description = "维度编码")
    private String dimensionCode;
    
    @Schema(description = "维度名称")
    private String dimensionName;
    
    @Schema(description = "排序号", defaultValue = "0")
    private Integer sortOrder;
    
    @Schema(description = "状态：0-禁用 1-启用", defaultValue = "1")
    private Integer status;
    
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
}
