package com.nacos.entity.po;

import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 数字人视频生成子任务实体类
 */
@Data
@Accessors(chain = true)
@TableName("digital_video_task_item")
@Schema(description = "数字人视频生成子任务实体类")
public class DigitalVideoTaskItemPO {
    
    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @Schema(description = "主任务ID")
    private String taskId;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "子任务ID")
    private String subTaskId;

    @Schema(description = "数字人ID")
    private String avatarId;
    
    @Schema(description = "文本内容")
    private String textContent;

    @Schema(description = "生成的视频URL")
    private String videoUrl;

    @Schema(description = "生成的视频Id")
    private String videoId;
    
    @Schema(description = "生成的音频URL")
    private String audioUrl;

    @Schema(description = "生成的音频长度")
    private Long audioLength;
    
    @Schema(description = "源视频URL")
    private String srcVideoUrl;
    
    @Schema(description = "声音ID")
    private String voiceId;
    
    @Schema(description = "语速")
    private Float speed;
    
    @Schema(description = "源语言")
    private String srcLang;
    
    @Schema(description = "目标语言")
    private String dstLang;
    
    @Schema(description = "序号")
    private Integer sequence;
    
    @Schema(description = "腾讯云API任务ID")
    private String apiJobId;
    
    /**
     * 状态：0-待处理 1-音频生成中 2-音频生成失败 3-音频生成成功 4-视频生成中 5-视频生成失败 6-视频生成成功
     */
    @Schema(description = "状态：0-待处理 1-音频生成中 2-音频生成失败 3-音频生成成功 4-视频生成中 5-视频生成失败 6-视频生成成功")
    private Integer status;

    @Schema(description = "视频和音频的总状态")
    private Integer TotalStatus;

    @Schema(description = "状态：0-待处理 1生成失败 2生成成功 3生成中")
    private Integer videoStatus;

    @Schema(description = "状态：0-待处理 1生成失败 2生成成功 3生成中")
    private Integer voiceStatus;

    @Schema(description = "video消息")
    private String videoMsg;

    @Schema(description = "voice消息")
    private String voiceMsg;
    
    @Schema(description = "错误信息")
    private String errorMsg;
    
    @Schema(description = "创建时间")
    private Date createdTime;
    
    @Schema(description = "更新时间")
    private Date updateTime;
    
    @Schema(description = "是否删除：0-未删除 1-已删除")
    @TableLogic
    private Integer isDeleted;
}
