package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.Date;

/**
 * 视频编辑子任务实体类
 */
@Data
@Accessors(chain = true)
@TableName("digital_video_edit_task_item")
@Schema(description = "视频编辑子任务实体类")
public class VideoEditTaskItemPO {
    
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 主任务ID
     */
    @Schema(description = "主任务ID")
    private String taskId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 子任务ID
     */
    @Schema(description = "子任务ID")
    private String subTaskId;

    /**
     * 数字人ID
     */
    @Schema(description = "数字人ID")
    private String avatarId;

    /**
     * 源音频URL
     */
    @Schema(description = "源音频URL")
    private String sourceVoiceUrl;

    /**
     * 扩展内容
     */
    @Schema(description = "扩展内容")
    private String other;

    /**
     * 输出视频URL
     */
    @Schema(description = "输出视频URL")
    private String outputVideoUrl;

    /**
     * 任务参数JSON字符串
     */
    @Schema(description = "任务参数JSON字符串")
    private String taskParameters;
    
    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer sequence;
    
    /**
     * 第三方API供应商标识
     */
    @Schema(description = "第三方API供应商标识")
    private String apiProvider;

    /**
     * 第三方API任务ID
     */
    @Schema(description = "第三方API任务ID")
    private String apiJobId;

    /**
     * 状态：0-待处理 1-准备中 2-处理中 3-处理成功 4-处理失败 5-处理超时 6-已取消 7-验证中 8-上传中 9-下载失败 10-上传失败
     */
    @Schema(description = "状态：0-待处理 1-准备中 2-处理中 3-处理成功 4-处理失败 5-处理超时 6-已取消 7-验证中 8-上传中 9-下载失败 10-上传失败")
    private Integer status;

    /**
     * 处理进度百分比(0-100)
     */
    @Schema(description = "处理进度百分比(0-100)")
    private Integer progress;

    /**
     * 处理开始时间
     */
    @Schema(description = "处理开始时间")
    private Date processStartTime;

    /**
     * 处理结束时间
     */
    @Schema(description = "处理结束时间")
    private Date processEndTime;

    /**
     * 处理耗时(秒)
     */
    @Schema(description = "处理耗时(秒)")
    private Long processingDuration;

    /**
     * 输出文件大小(字节)
     */
    @Schema(description = "输出文件大小(字节)")
    private Long outputFileSize;

    /**
     * 音频时长(毫秒)
     */
    @Schema(description = "音频时长(毫秒)")
    private Long audioLength;

    /**
     * 费用金额
     */
    @Schema(description = "费用金额")
    private Double cost;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMsg;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createdTime;
    
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
    
    /**
     * 是否删除：0-未删除 1-已删除
     */
    @Schema(description = "是否删除：0-未删除 1-已删除")
    @TableLogic
    private Integer isDeleted;
}
