package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import com.nacos.entity.AvatarRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "系统数字人表")
@TableName("digital_system_avatar")
public class DigitalSystemAvatarPO extends AvatarRecord {
    
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @Schema(description = "数字人ID")
    @TableField(value = "avatar_id", fill = FieldFill.INSERT)
    private String avatarId;
    
    @Schema(description = "数字人名称")
    private String avatarName;
    
    @Schema(description = "所属组ID")
    @TableField(value = "group_id", fill = FieldFill.INSERT)
    private String groupId;

    @Schema(description = "数字人训练视频URL")
    private String avatarVideoUrl;
    
    @Schema(description = "数字人授权视频URL")
    private String authVideoUrl;
    
    @Schema(description = "封面URL")
    private String coverUrl;
    
    @Schema(description = "类型：1-视频数字人 2-图片数字人", defaultValue = "1")
    private Integer avatarType;
    
    @Schema(description = "视频时长(秒)")
    private Integer duration;
    
    @Schema(description = "文件大小(字节)")
    private Long fileSize;
    
    @Schema(description = "状态：0-禁用 1-启用", defaultValue = "1")
    private Integer status;
    
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    @Schema(description = "是否删除：0-未删除 1-已删除", defaultValue = "0")
    @TableLogic
    private Integer isDeleted;
    
    @Schema(description = "组类型：固定为2-系统组", defaultValue = "2")
    private Integer groupType;

    @Schema(description = "数字人视频ID")
    private String avatarVideoId;

    @Schema(title = "形象宽度")
    private Integer width;

    @Schema(title = "形象高度")
    private Integer height;

    @Schema(title = "形象高度")
    private String figuresType;

} 