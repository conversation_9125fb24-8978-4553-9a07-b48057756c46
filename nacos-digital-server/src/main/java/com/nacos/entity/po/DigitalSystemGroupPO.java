package com.nacos.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "系统数字人组表")
@TableName("digital_system_group")
public class DigitalSystemGroupPO {
    
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @Schema(description = "组ID")
    @TableField(value = "group_id", fill = FieldFill.INSERT)
    private String groupId;
    
    @Schema(description = "组名称")
    private String groupName;
    
    @Schema(description = "组描述")
    private String description;
    
    @Schema(description = "组封面URL（视频截帧）")
    private String coverUrl;
    
    @Schema(description = "排序", defaultValue = "0")
    private Integer sort;
    
    @Schema(description = "状态：0-禁用 1-启用", defaultValue = "1")
    private Integer status;
    
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    @Schema(description = "是否删除：0-未删除 1-已删除", defaultValue = "0")
    @TableLogic
    private Integer isDeleted;
} 