package com.nacos.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Map;

/**
 * 音色多维度查询请求DTO
 */
@Data
@Schema(description = "音色多维度查询请求参数")
public class VoiceMultiDimensionQueryDTO {
    
    @Schema(description = "维度查询条件", example = "{\"GENDER\": [\"MALE\", \"FEMALE\"], \"LANGUAGE\": [\"zh-CN\"]}")
    private Map<String, List<String>> dimensionConditions;
    
    @Schema(description = "关键词搜索", example = "温柔")
    private String keyword;
    
    @Schema(description = "供应商筛选", example = "[\"MINIMAX\", \"MICROSOFT\"]")
    private List<String> providers;
    
    @Schema(description = "语言筛选", example = "[\"zh-CN\", \"en-US\"]")
    private List<String> languages;
    
    @Schema(description = "性别筛选", example = "[\"male\", \"female\"]")
    private List<String> genders;

    @Schema(description = "同步表ID筛选", example = "[1, 2, 3]")
    private List<Long> syncIds;

    @Schema(description = "第三方音色ID筛选", example = "[\"third_party_voice_1\", \"third_party_voice_2\"]")
    private List<String> thirdPartyVoiceIds;

    @Schema(description = "查询逻辑", example = "AND", allowableValues = {"AND", "OR"})
    @Pattern(regexp = "^(AND|OR)$", message = "查询逻辑只能是AND或OR")
    private String queryLogic = "AND";
    
    @Schema(description = "当前页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer currentPage = 1;
    
    @Schema(description = "每页大小", example = "20")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 20;
    
    @Schema(description = "是否包含标签信息", example = "true")
    private Boolean includeTags = true;
    
    @Schema(description = "是否包含统计信息", example = "false")
    private Boolean includeStats = false;
    
    @Schema(description = "排序字段", example = "sortWeight", allowableValues = {"sortWeight", "createdTime", "voiceName", "updateTime", "provider"})
    @Pattern(regexp = "^(sortWeight|createdTime|voiceName|updateTime|provider)$", message = "排序字段只能是sortWeight、createdTime、voiceName、updateTime或provider")
    private String sortField = "sortWeight";
    
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    @Pattern(regexp = "^(ASC|DESC)$", message = "排序方向只能是ASC或DESC")
    private String sortDirection = "DESC";
}
