package com.nacos.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 视频拼接测试DTO
 */
@Data
@Schema(description = "视频拼接测试请求参数")
public class VideoMergeTestDTO {

    @Schema(description = "用户ID", required = true)
    private String userId;

    @Schema(description = "要拼接的视频URL列表（按顺序拼接）", required = true)
    private List<String> videoUrls;
} 