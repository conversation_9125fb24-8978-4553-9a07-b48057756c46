package com.nacos.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@Schema(description = "语音生成DTO")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DigitalAudioGenerationDTO {

    @Schema(description = "请求的模型版本：speech-01-turbo、speech-01-240228、speech-01-turbo-240228、speech-01-hd", required = true)
    @Builder.Default
    private String model = "speech-01-turbo";

    @Schema(description = "待合成的文本，长度限制<10000字符", required = true)
    private String text;

    @Schema(description = "音色设置")
    @Builder.Default
    private VoiceSetting voiceSetting = VoiceSetting.builder()
            .speed(1.0f)
            .vol(1.0f)
            .pitch(0)
            .emotion("neutral")
            .latexRead(false)
            .build();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "音色设置")
    public static class VoiceSetting {
        @Schema(description = "生成声音的语速，范围[0.5,2]，默认值为1.0")
        @Builder.Default
        private Float speed = 1.0f;

        @Schema(description = "生成声音的音量，范围(0,10]，默认值为1.0")
        @Builder.Default
        private Float vol = 2.0f;

        @Schema(description = "生成声音的语调，范围[-12,12]，默认值为0")
        @Builder.Default
        private Integer pitch = 0;

        @Schema(description = "请求的音色编号，与timberWeights二选一")
        private String voiceId;

        @Schema(description = "控制合成语音的情绪：happy/sad/angry/fearful/disgusted/surprised/neutral")
        private String emotion;

        @Schema(description = "是否支持朗读latex公式，默认为false")
        @Builder.Default
        private Boolean latexRead = false;
    }

    @Schema(description = "音频设置")
    @Builder.Default
    private AudioSetting audioSetting = AudioSetting.builder()
            .sampleRate(32000)
            .bitrate(128000)
            .format("mp3")
            .channel(1)
            .build();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "音频设置")
    public static class AudioSetting {
        @Schema(description = "生成声音的采样率，范围【8000，16000，22050，24000，32000，44100】，默认值为32000")
        @Builder.Default
        private Integer sampleRate = 32000;

        @Schema(description = "生成声音的比特率，范围【32000，64000，128000，256000】，默认值为128000")
        @Builder.Default
        private Integer bitrate = 128000;

        @Schema(description = "生成的音频格式，默认mp3")
        @Builder.Default
        private String format = "mp3";

        @Schema(description = "生成音频的声道数，默认1：单声道")
        @Builder.Default
        private Integer channel = 1;
    }

    @Schema(description = "音色混合权重列表，与voiceSetting.voiceId二选一")
    private List<TimberWeight> timberWeights;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "音色混合权重")
    public static class TimberWeight {
        @Schema(description = "音色ID")
        private String voiceId;

        @Schema(description = "权重，范围[1,100]")
        @Builder.Default
        private Integer weight = 50;
    }

    @Schema(description = "发音词典")
    private PronunciationDict pronunciationDict;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "发音词典")
    public static class PronunciationDict {
        @Schema(description = "替换需要特殊标注的文字、符号及对应的注音")
        private List<String> tone;
    }

    @Schema(description = "是否流式输出")
    @Builder.Default
    private Boolean stream = false;

    @Schema(description = "增强对指定的小语种和方言的识别能力")
    private String languageBoost;

} 