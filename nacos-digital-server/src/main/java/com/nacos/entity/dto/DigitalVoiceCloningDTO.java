package com.nacos.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.Valid;

@Data
@Schema(title = "音色克隆DTO")
public class DigitalVoiceCloningDTO {

    @NotNull(message = "音频文件ID不能为空")
    @Schema(title = "音频文件ID", description = "支持mp3、m4a、wav格式的音频文件ID，int64类型", required = true)
    private Long fileId;

    @Schema(title = "声音名称")
    private String voiceName;

    @Schema(title = "验证文本")
    private String textValidation;

    @Schema(title = "文本", description = "复刻试听参数，用于试听复刻效果，限制300字以内")
    private String text;

    @Schema(title = "模型", description = "声音克隆的模型，可选：speech-01-turbo、speech-01-240228、speech-01-turbo-240228、speech-01-hd")
    private String model = "speech-01-turbo";

    @Schema(title = "准确度", description = "声音克隆的准确度，取值范围[0,1]，默认0.9")
    private Double accuracy = 0.9;

    @Schema(title = "是否需要降噪", description = "是否需要降噪，默认取false")
    private Boolean needNoiseReduction = true;

    @Schema(title = "是否需要音量归一化", description = "是否需要音量归一化，默认取false")
    private Boolean needVolumeNormalization = true;

    @NotNull(message = "克隆提示不能为空")
    @Valid
    @Schema(title = "克隆提示", required = true)
    private ClonePrompt clonePrompt;

    @Data
    public static class ClonePrompt {
        @NotNull(message = "提示音频不能为空")
        @Schema(title = "提示音频", description = "音频prompt参数，填入通过File接口中的upload上传示例音频得到的\"file_id\"，示例音频时长必须小于8s", required = true)
        private Long promptAudio;

        @NotNull(message = "提示文本不能为空")
        @Schema(title = "提示文本", description = "音频prompt参数，填入示例音频的对应文本，需确保和音频内容一致，句末需有标点符号做结尾", required = true)
        private String promptText;
    }

    @Schema(title = "关联的数字人ID")
    private String avatarId;
}
