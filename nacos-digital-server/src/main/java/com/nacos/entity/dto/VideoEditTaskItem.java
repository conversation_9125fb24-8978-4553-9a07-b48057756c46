package com.nacos.entity.dto;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.IOException;

/**
 * 视频编辑任务项DTO
 * 精简的数字人视频编辑参数配置，仅包含核心的三个字段
 */
@Data
@Schema(description = "视频编辑任务项DTO")
public class VideoEditTaskItem {

    /**
     * 数字人id
     */
    @NotBlank(message = "数字人Id不能为空")
    @Schema(description = "数字人id", required = true)
    private String avatarId;

    /**
     * 音频链接
     */
    @NotBlank(message = "音频链接不能为空")
    @Schema(description = "音频链接", required = true)
    private String voiceUrl;

    /**
     * 扩展内容
     * 支持接收对象格式，自动转换为JSON字符串存储
     */
    @Schema(description = "扩展内容")
    @JsonDeserialize(using = VideoEditTaskItem.ObjectToStringDeserializer.class)
    private String other;

    /**
     * 自定义反序列化器：将对象或字符串统一转换为JSON字符串
     * 确保无论前端发送对象还是字符串，都能正确处理
     */
    public static class ObjectToStringDeserializer extends JsonDeserializer<String> {
        private static final ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public String deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            JsonToken token = parser.getCurrentToken();

            if (token == JsonToken.VALUE_STRING) {
                // 如果是字符串，直接返回
                return parser.getValueAsString();
            } else if (token == JsonToken.START_OBJECT || token == JsonToken.START_ARRAY) {
                // 如果是对象或数组，序列化为JSON字符串
                Object obj = objectMapper.readTree(parser);
                return objectMapper.writeValueAsString(obj);
            } else if (token == JsonToken.VALUE_NULL) {
                // 如果是null，返回null
                return null;
            } else {
                // 其他类型（数字、布尔值等），转换为字符串
                return parser.getValueAsString();
            }
        }
    }
}
