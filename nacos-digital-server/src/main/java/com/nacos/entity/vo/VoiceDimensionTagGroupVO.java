package com.nacos.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "音色维度标签分组VO")
public class VoiceDimensionTagGroupVO {
    
    @Schema(description = "维度编码")
    private String dimensionCode;
    
    @Schema(description = "维度名称")
    private String dimensionName;
    
    @Schema(description = "维度排序号")
    private Integer sortOrder;
    
    @Schema(description = "该维度下的标签列表")
    private List<VoiceClassifyTagVO> tags;
    
    @Schema(description = "该维度下的标签数量")
    private Integer tagCount;
    
    @Schema(description = "维度状态：0-禁用 1-启用")
    private Integer status;
}
