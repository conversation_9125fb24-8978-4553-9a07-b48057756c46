package com.nacos.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 数字人分身详情VO
 */
@Data
@Schema(description = "数字人分身详情VO")
public class DigitalAvatarInstanceDetailVO {
    
    @Schema(description = "数字人ID")
    private String avatarId;
    
    @Schema(description = "数字人名称")
    private String avatarName;
    
    @Schema(description = "所属组ID")
    private String groupId;
    
    @Schema(description = "组名称")
    private String groupName;
    
    @Schema(description = "数字人分类编码")
    private String avatarCategoryCode;

    @Schema(description = "数字人分类名称")
    private String avatarCategoryName;
    
    @Schema(description = "封面URL")
    private String coverUrl;
    
    @Schema(description = "数字人训练视频URL")
    private String avatarVideoUrl;
    
    @Schema(description = "类型：1-视频数字人 2-图片数字人")
    private Integer avatarType;
    
    @Schema(description = "视频时长(秒)")
    private Integer duration;
    
    @Schema(description = "文件大小(字节)")
    private Long fileSize;
    
    @Schema(description = "状态：0-禁用 1-启用")
    private Integer status;
    
    @Schema(description = "组类型：1-用户组 2-系统组")
    private Integer groupType;
    
    @Schema(description = "是否为当前用户拥有")
    private Boolean isOwner;
    
    @Schema(description = "主要关联音色信息")
    private VoiceInfoVO voiceInfo;

    /**
     * 音色信息VO
     */
    @Data
    @Schema(description = "音色信息VO")
    public static class VoiceInfoVO {
        
        @Schema(description = "音色ID")
        private String voiceId;
        
        @Schema(description = "音色名称")
        private String voiceName;
        
        @Schema(description = "供应商标识")
        private String provider;
        
        @Schema(description = "音色分类编码")
        private String voiceCategoryCode;
        
        @Schema(description = "示例音频URL")
        private String demoAudio;
        
        @Schema(description = "语言")
        private String language;
        
        @Schema(description = "性别")
        private String gender;
        
        @Schema(description = "音色描述")
        private String description;
        
        @Schema(description = "音色类型：1-系统音色 2-克隆音色")
        private Integer voiceType;
    }
}
