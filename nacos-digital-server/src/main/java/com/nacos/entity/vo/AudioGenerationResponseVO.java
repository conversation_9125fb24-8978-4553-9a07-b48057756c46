package com.nacos.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 同步音频生成响应VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "同步音频生成响应VO")
public class AudioGenerationResponseVO {

    @Schema(description = "生成的音频URL")
    private String audioUrl;

    @Schema(description = "音频时长（毫秒）")
    private Integer durationMs;

    @Schema(description = "任务ID（用于历史查询）")
    private String taskId;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "Asia/Shanghai", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "使用的服务商")
    private String provider;

    @Schema(description = "音频格式")
    private String audioFormat;

    @Schema(description = "任务名称")
    private String audioName;

    @Schema(description = "字幕数据列表，仅当启用字幕服务时返回")
    private List<SubtitleInfo> subtitles;

    // @Schema(description = "字幕文件下载链接，当服务商返回字幕文件URL时使用")
    // private String subtitleFileUrl;

    /**
     * 字幕信息 - 使用官方完整格式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "字幕信息")
    public static class SubtitleInfo {
        @Schema(description = "字幕文本内容")
        private String text;

        @Schema(description = "开始时间（毫秒，支持浮点数）")
        private Double timeBegin;

        @Schema(description = "结束时间（毫秒，支持浮点数）")
        private Double timeEnd;

        @Schema(description = "文本开始位置")
        private Integer textBegin;

        @Schema(description = "文本结束位置")
        private Integer textEnd;

        @Schema(description = "时间戳词汇信息，通常为null")
        private Object timestampedWords;
    }
}
