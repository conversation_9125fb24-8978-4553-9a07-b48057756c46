package com.nacos.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@Schema(description = "数字人视频响应对象")
public class DigitalVO {
    
    @Schema(description = "任务ID")
    private String taskId;
    
    @Schema(description = "任务状态")
    private String status;
    
    @Schema(description = "视频URL")
    private String videoUrl;
    
    @Schema(description = "创建时间")
    private String createTime;
}
