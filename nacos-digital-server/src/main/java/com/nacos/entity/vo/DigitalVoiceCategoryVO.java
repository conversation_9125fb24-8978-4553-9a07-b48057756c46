package com.nacos.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
public class DigitalVoiceCategoryVO {
    
    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private Long id;
    
    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String categoryName;
    
    /**
     * 分类编码
     */
    @Schema(description = "分类编码")
    private String categoryCode;
    
    /**
     * 排序号
     */
    @Schema(description = "排序号")
    private Integer sortOrder;
    
    /**
     * 该分类下的音色列表
     */
    @Schema(description = "该分类下的音色列表")
    private List<DigitalVoiceStyleVO> voiceList;
} 