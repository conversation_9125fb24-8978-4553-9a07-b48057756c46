//package com.nacos.listener;
//
//import com.business.db.model.po.FlowRecordPO;
//import com.nacos.enums.FlowRecordEnum;
//import com.nacos.server.SseEmitterServer;
//import com.nacos.service.IUserService;
//import com.nacos.service.TyqwAsyncService;
//import com.nacos.utils.gpt.Message;
//import lombok.Builder;
//import lombok.extern.slf4j.Slf4j;
//
////type 1-gpt会话  2-文案创作
//@Slf4j
//@Builder
//public class TyqwStreamListener extends AbstractStreamListener {
//
//    final Long userId;
//
//    final Integer type;
//
//    final Long way;
//
//    final Long logId;
//
//    final Double deductions;
//
//    final TyqwAsyncService asyncService;
//
//    final IUserService userService;
//
//    final String msg;
//
//    FlowRecordPO flowRecordPO;
//
//    @Override
//    public void onMsg(Object message) {
//        log.error("gpt推送数据，成功！"+logId+"======");
//        SseEmitterServer.sendMessage(userId.toString(), message);
//    }
//
//    @Override
//    public void onError(Throwable throwable, String response) {
//        //将用户使用点子数返回
//        if(flowRecordPO == null){
//            flowRecordPO = new FlowRecordPO();
//        }
//        flowRecordPO.setId(null);
//        flowRecordPO.setRecordType(0);
//        flowRecordPO.setRemark(FlowRecordEnum.FOUR.getRemark());
//        asyncService.updateRemainingTimes(userId,  deductions, flowRecordPO);
//        log.error("gpt对话异常，异常key======================================：{}",response);
//        Message message = Message.ofAssistant("服务异常请稍后再试");
//        SseEmitterServer.sendMessage(userId.toString(), message);
//        message = Message.ofAssistant("[DONE]");
//        SseEmitterServer.sendMessage(userId.toString(), message);
//        if(logId != null){
//            asyncService.endOfAnswer(logId, type,"AI对话服务异常请稍后再试");
//        }
//    }
//
//}
