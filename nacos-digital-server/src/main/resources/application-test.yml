server:
  port: 8818
  tomcat:
    uri-encoding: utf-8
spring:
  mvc:
    servlet:
      path: /digital
  application:
    name: nacos-digital-server
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ************************************************************************************************************************************************************************************************************************************************************
    username: ddsjtest
    password: R4egP0btnwGz3y
    driver-class-name: com.mysql.cj.jdbc.Driver
    tomcat:
      max-active: 30
      initial-size: 5
      max-wait: 60000
      min-idle: 2
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 25200000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  #cloud配置
  cloud:
    nacos:
      discovery: # 服务发现
        enabled: true
        server-addr: *************:8848 # Nacos 服务地址
        group: DEFAULT_GROUP # 组名
        namespace: 68161264-0efa-4aef-93cb-35482081662e # 命名空间
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
      config: # 配置中心
        enabled: true
        server-addr: *************:8848
        file-extension: yml
        namespace: 68161264-0efa-4aef-93cb-35482081662e
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
  # redis服务连接
  data:
    redis:
      host: *************
      port: 9979
      database: 5
      password: abc123
      lettuce:
        pool:
          max-idle: 100
          min-idle: 1
          max-active: 1000
          max-wait: -1
        cluster:
          refresh:
            adaptive: true
      timeout: 10000s
  config:
    import:
      - optional:nacos:${spring.application.name}.${spring.cloud.nacos.config.file-extension}

logs:
  path: /usr/local/logs


