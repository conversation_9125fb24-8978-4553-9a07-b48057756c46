version: "3.1"
services:
  digital:
    image: tabatad/jdk21
    container_name: digital
    working_dir: /server
    restart: always
    privileged: true
    ports:
      - 8818:8818
    volumes:
      - ./appfile:/appfile
      - ./logs:/usr/local/logs
      - ./conf:/usr/local/conf
    command: java -Xms2048M -Xmx4096M -Xss1M -Duser.timezone=GMT+8 -Dspring.profiles.active=test -Dspring.config.additional-location=/usr/local/conf/ -jar -Dfile.encoding=utf-8 /appfile/nacos-digital-server-0.0.1-SNAPSHOT.jar
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"
    networks:
      - ddsj-network

networks:
  ddsj-network:
    external: true