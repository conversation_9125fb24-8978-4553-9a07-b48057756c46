package com.nacos.service.wx.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.nacos.service.wx.IWxService;
import com.nacos.utils.wx.WxUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;


@Service("WxService")
@Log4j2
@Transactional(rollbackFor = Exception.class)
public class WxServiceImpl implements IWxService {

    /**
     *@Author: myl
     *@CreateTime: 20:46 2024/01/22
     *@param:  shareUrl 分享的url
     *@Description: 初始化JSSDKConfig
     */
    @Override
    public Map<String,String> initJSSDKConfig(String url) throws Exception {
        //获取AccessToken
        String accessToken = WxUtil.getJSSDKAccessToken();
        //获取JssdkGetticket
        String jsapiTicket = WxUtil.getJssdkGetticket(accessToken);
        String timestamp = Long.toString(System.currentTimeMillis() / 1000);
        String nonceStr = IdWorker.getIdStr();
        String signature = WxUtil.buildJSSDKSignature(jsapiTicket,timestamp,nonceStr,url);
        Map<String,String> map = new HashMap<String,String>();
        map.put("url", url);
        map.put("jsapi_ticket", jsapiTicket);
        map.put("nonceStr", nonceStr);
        map.put("timestamp", timestamp);
        map.put("signature", signature);
        map.put("appid", "wx5e4934ab649e98b7");
        return map;
    }



}
