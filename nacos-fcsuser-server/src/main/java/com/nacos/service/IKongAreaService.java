package com.nacos.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.DailyPicksDTO;
import com.business.db.model.vo.DailyPicksVO;
import com.business.db.model.vo.FunctionOptConfigVO;
import com.business.db.model.vo.ImgModelConfigVO;
import com.nacos.model.DailyPicksOptVO;
import com.nacos.result.Result;

import java.util.List;

public interface IKongAreaService {

    Result<Page<DailyPicksVO>> dailyPicksPage(DailyPicksDTO dailyPicksDTO);

    Result<List<DailyPicksOptVO>> dailyPicksOptConfigEditList(Long languageTagId);

    Result<List<FunctionOptConfigVO>> functionOptConfigList(Long languageTagId, Long functionConfigId);

    Result<Object> videoModelConfigList(Long languageTagId, String version);

    Result<List<ImgModelConfigVO>> imageModelConfigPoster(Long languageTagId, String version);

}
