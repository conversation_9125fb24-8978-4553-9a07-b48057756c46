package com.nacos.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.*;
import com.business.db.model.dto.draw.MjStyleConfigQueryDTO;
import com.business.db.model.vo.GuidePageVO;
import com.business.db.model.vo.ImgCommunityVO;
import com.business.db.model.vo.ImgDrawHistoryVO;
import com.business.db.model.vo.ImgGalleryVO;
import com.business.db.model.vo.draw.MjStyleConfigQueryVO;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public interface DrawService {


    Result<IPage<ImgDrawHistoryVO>> historyRecord(ImgDrawHistoryDTO imgDrawHistoryDTO);

    Result<Page<ImgCommunityVO>> queryHomePage(CommunityQueryDTO dto);

    Result<String> historyVip(Integer languageTagId);

    Result<Long> historyRecordStateQuantity();

    Result<Integer> saveToGallery(Long imgDetlId);

    //历史记录开关
    Result<Object> optIsPublishRecord(Long imgDrawRecordId) throws IBusinessException;

    //历史记录详情开关
    Result<Object> optIsPublishDetl(Long imgDrawDetlId) throws IBusinessException;

    //我的画廊列表查询
    Result<Page<ImgGalleryVO>> myGalleryPage(GalleryQueryDTO dto);

    Result<ImgCommunityVO> queryGalleryByImgId(GalleryInfoQueryDTO dto);

    Result<Map<String, List<GuidePageVO>>> guidePageList();

    Result<ImgDrawHistoryVO> queryAImgDrawDetlVO(ImgDrawInfoDTO dto);

    Result<Object> pullMjTaskInfos(List<String> JobId) throws Exception;

    Result<LinkedHashMap<String, String>> getInstructMatching() throws Exception;

    Result<LinkedHashMap<String, Object>> getStyleClassifyList(Integer languageTagId) throws Exception;

    Result<IPage<MjStyleConfigQueryVO>> mjStylePage(MjStyleConfigQueryDTO mjStyleConfigQueryDTO);

    Result<Object> mjStyleOnVideo();

    Result<Object> imgDrawDetlDeletes( List<Long> imgDrawDetlIds);

    Result<Object> optIsPublishRecords(List<Long> imgDrawDetlIds);

    Result<?> collectWorks(List<ImgCollectDTO> dtoList);

    Result<Page<ImgGalleryVO>> queryCollectPage(GalleryQueryDTO dto);
}
