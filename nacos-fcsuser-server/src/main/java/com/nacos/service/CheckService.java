package com.nacos.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.vo.UserDDInfoVO;
import com.business.db.model.vo.UserDDRecordVO;
import com.nacos.base.BasePageHelper;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;

public interface CheckService {

    /**
     * 账户点点余额消费--扣点子
     * @param userId 用户id
     * @param deduct 扣除金额
     * @return 返回是否成功true/false
     */
    Result<Boolean> getResidueDDDeduct(Long userId, Double deduct);

    /**
     * 获取账户点点余额
     * @return 返回余额信息
     */
    Result<Double> ddResidue(Long userId);

    /**
     * 设置扣除错误账户信息点点余额 -- 退还点子
     * @param userId 用户id
     * @param ddQuantity 点点数量
     * @return 返回是否成功true/false
     */
    boolean setReturnDD(Long userId,Double ddQuantity);

    /**
     * 获取用户等级
     * @param userId 用户id
     * @return 返回等级信息
     */
    @Deprecated
    Integer getUserGrade(Long userId);


    /**
     * 获取用户点点信息
     * @param userId 用户id
     * @return 返回点点信息
     */
    Result<UserDDInfoVO> getUserDDInfo(Long userId);


    /**
     * 查寻点子套餐历史记录
     * @param userId 用户id
     * @return 返回套餐记录信息
     */
    Result<Page<UserDDRecordVO>> ddRecord(Long userId, BasePageHelper basePageHelper);

    /**
     * userId  ： 上级-->
     * loginUserId  自己
     * @param userId
     * @param loginUserId
     * @return
     * @throws IBusinessException
     */
    boolean setUserDDInviteGive(Long userId,Long loginUserId) throws IBusinessException;

    /**
     * 设置用户点点信息
     * @param userId 用户id
     * @param type 2001:APP    2002:WEB
     * @return 返回是否成功true/false
     */
    boolean setUserDDAppOrWebGive(Long userId, Integer type) throws IBusinessException;

}
