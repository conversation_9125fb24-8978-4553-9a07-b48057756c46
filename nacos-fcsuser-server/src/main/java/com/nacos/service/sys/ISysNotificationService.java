package com.nacos.service.sys;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.business.db.model.dto.SysNotificationQueryDTO;
import com.business.db.model.dto.sys.SysNotificationDTO;
import com.business.db.model.dto.sys.SysNotificationUpdateDTO;
import com.business.db.model.po.SysNotificationPO;
import com.business.db.model.vo.SysActivityNotificationVO;
import com.business.db.model.vo.SysNotificationInfoVO;
import com.business.db.model.vo.SysNotificationVO;
import com.nacos.base.BaseDeleteEntity;
import com.nacos.result.Result;

/**
 * 互动消息
 * @className: ISysInteractionService
 * @author: Myl互动消息
 * @createDate: 2023-12-21互动消息
 *
 */
public interface ISysNotificationService extends IService<SysNotificationPO> {

    Result<Page<SysNotificationVO>> queryPage(SysNotificationQueryDTO dto);

    Result<Page<SysActivityNotificationVO>> queryActivityPage(SysNotificationQueryDTO dto);

    Result<SysNotificationInfoVO> getNotificatInfo(Long notifTypeId);

    Result<Boolean> setRead(SysNotificationUpdateDTO dto);

    Long selectUnreadNotification(Long userId);

    Result<Boolean> add(SysNotificationDTO dto);

    Result<Boolean>  update(SysNotificationPO notificationPO);

    Result<Boolean> delete(BaseDeleteEntity params);


}
