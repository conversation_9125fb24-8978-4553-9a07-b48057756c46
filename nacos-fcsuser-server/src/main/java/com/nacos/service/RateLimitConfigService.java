package com.nacos.service;

import java.util.Map;

/**
 * 限流配置服务接口
 */
public interface RateLimitConfigService {
    
    /**
     * 获取所有限流配置
     * @return 配置Map，key为配置键，value为配置值
     */
    Map<String, Integer> getAllConfigs();
    
    /**
     * 根据配置键获取配置值
     * @param key 配置键
     * @return 配置值
     */
    Integer getConfigValue(String key);
    
    /**
     * 根据配置键获取配置值，如果不存在则返回默认值
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    Integer getConfigValue(String key, Integer defaultValue);
    
    /**
     * 更新配置值
     * @param key 配置键
     * @param value 配置值
     * @return 是否更新成功
     */
    boolean updateConfigValue(String key, Integer value);
    
    /**
     * 刷新配置缓存
     */
    void refreshConfigCache();
} 