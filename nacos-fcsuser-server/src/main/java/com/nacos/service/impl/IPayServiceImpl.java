package com.nacos.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.business.db.mapper.*;
import com.business.db.model.po.*;
import com.business.utils.BDateUtil;
import com.nacos.auth.JwtNewUtil;
import com.nacos.enums.*;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;
import com.nacos.service.IPayService;
import com.nacos.utils.IOSPayUtil;
import com.nacos.utils.wxpay.SequenceNoUtils;
import com.nacos.utils.wxpay.WXPayApis;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-11-19 14:32
 */
@Service
@Slf4j
public class IPayServiceImpl implements IPayService {

    @Resource
    private VipConfigMapper vipConfigMapper;

    @Resource
    private UserDDRecordMapper userDDRecordMapper;

    @Resource
    private PayIosInappMapper payIosInappMapper;

    @Resource
    private PayRecordMapper payRecordMapper;

    @Resource
    private FlowRecordMapper flowRecordMapper;


    //游客支付校验----苹果应用内支付信息
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> iosTouristCheck(JSONObject jsonObject) {

        HashMap<String, Object> result = IOSPayUtil.checkIosTouristPay(jsonObject);
        if (result == null || !result.containsKey("product_id")
                || !result.containsKey("transaction_id")
                || !result.containsKey("device_code")) {
            return Result.ERROR("校验信息失败");
        }
        PayIOSInAppPO payIosInappPO = payIosInappMapper.selectOne(
                new LambdaQueryWrapper<PayIOSInAppPO>()
                        .eq(PayIOSInAppPO::getTransactionId, result.get("transaction_id"))
                        .eq(PayIOSInAppPO::getProductId, result.get("product_id"))
        );
        IOSInAppEnum iosInAppEnum = IOSInAppEnum.getIOSInAppEnum((String) result.get("product_id"));
        if (iosInAppEnum == null) {
            return Result.ERROR("产品id不存在");
        }
        if (payIosInappPO != null) {
            return Result.SUCCESS("已支付过信息，无需重复支付");
        }
        VipConfigPO vipConfigPO = vipConfigMapper.selectOne(
                new LambdaQueryWrapper<VipConfigPO>()
                        .eq(VipConfigPO::getId, iosInAppEnum.getVipConfigId())
                        .eq(VipConfigPO::getIsShow, CommonIntEnum.SHOW_TRUE.getIntValue())
        );
        if (vipConfigPO == null) {
            return Result.ERROR("产品id对应vip配置不存在");
        }

        payIosInappPO = new PayIOSInAppPO();
        //payIosInappPO.setUserId(payRecordPO.getUserId());
        //payIosInappPO.setPayRecordId(payRecordPO.getId());
        payIosInappPO.setDeviceCode(result.get("device_code").toString());
        payIosInappPO.setProductId(result.get("product_id").toString());
        payIosInappPO.setTransactionId(IOSPayUtil.getLongValue(result, "transaction_id"));
        payIosInappPO.setIsPro((Integer) result.get("is_pro"));

        Long count = payIosInappMapper.selectCount(new LambdaQueryWrapper<PayIOSInAppPO>()
                .eq(PayIOSInAppPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                .eq(PayIOSInAppPO::getTransactionId, payIosInappPO.getTransactionId()));
        if (count == 0) {
            //苹果应用内支付记录
            if (payIosInappMapper.insert(payIosInappPO) < 1) {
                return Result.SUCCESS("支付失败");
            }
        }
        return Result.SUCCESS("支付成功");
    }

    //校验苹果应用内支付信息
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> iosIapCheck(JSONObject jsonObject) throws IBusinessException {
        log.info("校验苹果应用内支付信息,{}", jsonObject);
        HashMap<String, Object> result = IOSPayUtil.checkIosPay(jsonObject);
        if (result == null || !result.containsKey("product_id") || !result.containsKey("transaction_id")) {
            return Result.ERROR("校验信息失败");
        }
        PayIOSInAppPO payIosInappPO = payIosInappMapper.selectOne(
                new LambdaQueryWrapper<PayIOSInAppPO>()
                        .eq(PayIOSInAppPO::getTransactionId, result.get("transaction_id"))
                        .eq(PayIOSInAppPO::getProductId, result.get("product_id"))
        );
        IOSInAppEnum iosInAppEnum = IOSInAppEnum.getIOSInAppEnum((String) result.get("product_id"));
        if (iosInAppEnum == null) {
            return Result.ERROR("产品id不存在");
        }
        if (payIosInappPO != null) {
            return Result.SUCCESS("已支付过信息，无需重复支付");
        }
        VipConfigPO vipConfigPO = vipConfigMapper.selectOne(
                new LambdaQueryWrapper<VipConfigPO>()
                        .eq(VipConfigPO::getId, iosInAppEnum.getVipConfigId())
                        .eq(VipConfigPO::getIsShow, CommonIntEnum.SHOW_TRUE.getIntValue())
        );
        if (vipConfigPO == null) {
            return Result.ERROR("产品id对应vip配置不存在");
        }
        //新支付成功：1、根据产品id查询相关vip充值配置；2创建支付订单信息；3创建应用内支付记录信息
        //支付订单记录信息
        PayRecordPO payRecordPO = new PayRecordPO();
        payRecordPO.setUserId(JwtNewUtil.getUserId());
        payRecordPO.setOrderNo(SequenceNoUtils.generateNo("01"));
        payRecordPO.setVipConfigId(vipConfigPO.getId());
        payRecordPO.setCreateTime(DateUtil.date());
        payRecordPO.setDiscountRatio(1.0);
        payRecordPO.setState(PayRecordEnum.STATE_PAY_SUCCESS.getIntValue());
        //加油包
        if (Objects.equals(VipConfigEnum.GRADE_PT.getIntValue(), vipConfigPO.getGrade())) {
            payRecordPO.setOrderType(PayRecordEnum.ORDER_TYPE_JYB.getIntValue());
            payRecordPO.setTotal(vipConfigPO.getCurrentMonthQuantity());
        }
        //vip支付
        if (Objects.equals(VipConfigEnum.GRADE_VIP.getIntValue(), vipConfigPO.getGrade())) {
            payRecordPO.setOrderType(PayRecordEnum.ORDER_TYPE_VIP.getIntValue());
            payRecordPO.setTotal(new BigDecimal(vipConfigPO.getMonth().toString()).multiply(new BigDecimal(vipConfigPO.getCurrentMonthQuantity().toString())).doubleValue());
        }
        //svip
        if (Objects.equals(VipConfigEnum.GRADE_SVIP.getIntValue(), vipConfigPO.getGrade())) {
            payRecordPO.setOrderType(PayRecordEnum.ORDER_TYPE_SVIP.getIntValue());
            payRecordPO.setTotal(new BigDecimal(vipConfigPO.getMonth().toString()).multiply(new BigDecimal(vipConfigPO.getCurrentMonthQuantity().toString())).doubleValue());
        }
        //苹果应用内支付更新
        payRecordPO.setType(PayRecordEnum.TYPE_ITEM_PAY_IOS_IN_APP.getIntValue());
        payRecordPO.setMonth(vipConfigPO.getMonth());
        payRecordPO.setMonthQuantity(vipConfigPO.getCurrentMonthQuantity());
        //按比率装载金额
        payRecordPO.setAmount(iosInAppEnum.getPrice());
        //查询用户当前会员等级的订单日期
        LambdaQueryWrapper<PayRecordPO> queryWrapper = new LambdaQueryWrapper<PayRecordPO>()
                .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                .ne(PayRecordPO::getVipConfigId, 8)//8为体验会员直接过滤掉
                .eq(PayRecordPO::getUserId, payRecordPO.getUserId())
                .ne(PayRecordPO::getOrderType, PayRecordEnum.ORDER_TYPE_JYB.getIntValue())
                .ge(PayRecordPO::getExpirationTime, BDateUtil.getDateNowShanghai())
                .orderByDesc(PayRecordPO::getExpirationTime).last("LIMIT 0,1");
        int ok = 0;
        PayRecordPO payRecordPOTime = payRecordMapper.selectOne(queryWrapper);
        //校验会员状态
        if (payRecordPOTime != null && payRecordPOTime.getExpirationTime() != null) {
            ok = 1;
            Date date = BDateUtil.getDateNowShanghai();
            // Date date = payRecordPOTime.getExpirationTime();
            payRecordPO.setTaskTime(date);
            payRecordPO.setExpirationTime(BDateUtil.getDateAddMonth(date, vipConfigPO.getMonth()));
        } else {
            Date date = BDateUtil.getDateNowShanghai();
            payRecordPO.setTotalUsage(vipConfigPO.getCurrentMonthQuantity());
            payRecordPO.setTaskTime(BDateUtil.getDateAddMonth(date, 1));
            payRecordPO.setExpirationTime(BDateUtil.getDateAddMonth(date, vipConfigPO.getMonth()));
        }
        //校验加油包
        if (Objects.equals(VipConfigEnum.GRADE_PT.getIntValue(), vipConfigPO.getGrade())) {
            Date date = BDateUtil.getDateNowShanghai();
            payRecordPO.setTotalUsage(vipConfigPO.getCurrentMonthQuantity());
            payRecordPO.setExpirationTime(BDateUtil.getDateAddMonth(date, vipConfigPO.getMonth()));
            payRecordPO.setTaskTime(payRecordPO.getExpirationTime());
        }
        log.info("date:{}", payRecordPO);
        if (payRecordMapper.insert(payRecordPO) < 1) {
            throw new IBusinessException("创建支付订单失败");
        }
        payIosInappPO = new PayIOSInAppPO();
        payIosInappPO.setUserId(payRecordPO.getUserId());
        payIosInappPO.setProductId(result.get("product_id").toString());
        payIosInappPO.setTransactionId(IOSPayUtil.getLongValue(result, "transaction_id"));
        payIosInappPO.setPayRecordId(payRecordPO.getId());
        payIosInappPO.setIsPro((Integer) result.get("is_pro"));

        Long count = payIosInappMapper.selectCount(new LambdaQueryWrapper<PayIOSInAppPO>()
                .eq(PayIOSInAppPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                .eq(PayIOSInAppPO::getTransactionId, payIosInappPO.getTransactionId()));
        if (count == 0) {
            //苹果应用内支付记录
            if (payIosInappMapper.insert(payIosInappPO) < 1) {
                throw new IBusinessException("创建苹果应用内支付记录失败");
            }
        }
//        if (ok == 1 && !Objects.equals(VipConfigEnum.GRADE_PT.getIntValue(), vipConfigPO.getGrade())){
//            //续费会员无需更新点点信息
//            return Result.SUCCESS("权益已到账");
//        }
        //非续费会员需要更新会员记录信息
        //创建点子记录表
        UserDDRecordPO userDDRecordPO = new UserDDRecordPO();
        userDDRecordPO.setUserId(payRecordPO.getUserId());
        userDDRecordPO.setSourceId(payRecordPO.getId());
        userDDRecordPO.setTotal(payRecordPO.getMonthQuantity());
        userDDRecordPO.setType(UserDDrecordEnum.TYPE_PAY.getIntValue());
        userDDRecordPO.setTypeItem(payRecordPO.getOrderType());
        userDDRecordPO.setTotalUsage((double) 0);
        if (Objects.equals(payRecordPO.getOrderType(), PayRecordEnum.ORDER_TYPE_JYB.getIntValue())) {
            userDDRecordPO.setExpirationTime(payRecordPO.getExpirationTime());
        } else {
            userDDRecordPO.setExpirationTime(com.nacos.utils.DateUtil.getDateAddMonth(payRecordPO.getTaskTime(), 1));
        }
        userDDRecordPO.setRemark(payRecordPO.getRemark());
        if (userDDRecordMapper.insert(userDDRecordPO) < 1) {
            throw new IBusinessException("创建点子记录失败");
        }

        FlowRecordPO flowRecordPO = new FlowRecordPO();
        flowRecordPO.setUserId(payRecordPO.getUserId());
        flowRecordPO.setRecordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue());
        flowRecordPO.setNum(payRecordPO.getMonthQuantity());
        flowRecordPO.setOperateType(FlowRecordEnum.OPERATE_TYPE_ADD_PAY.getIntValue());
        flowRecordPO.setRemark(FlowRecordEnum.OPERATE_TYPE_ADD_PAY.getStrValue());
        if (flowRecordMapper.insert(flowRecordPO) < 1) {
            throw new IBusinessException("创建点子登记信息失败");
        }
        return Result.SUCCESS("权益已到账");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<Object> iosIapCheckTouristPay(JSONObject jsonObject) throws IBusinessException {
        //ios身份id，有可能为apple id或者为应用设备id
        if (!jsonObject.containsKey("iosIdentityId")) {
            return Result.SUCCESS();
        }
        List<PayIOSInAppPO> payIosInappPOList = payIosInappMapper.selectList(
                new LambdaQueryWrapper<PayIOSInAppPO>()
                        .eq(PayIOSInAppPO::getDeviceCode, jsonObject.get("iosIdentityId"))
                        .eq(PayIOSInAppPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .isNull(PayIOSInAppPO::getUserId)
                        .isNull(PayIOSInAppPO::getPayRecordId)
                        .orderByAsc(PayIOSInAppPO::getCreateTime)
        );
        if (payIosInappPOList == null || payIosInappPOList.isEmpty()) {
            return Result.SUCCESS();
        }
        //存在未同步信息进行同步
        for (PayIOSInAppPO payIOSInAppPO : payIosInappPOList) {
            //新支付成功：1、根据产品id查询相关vip充值配置；2创建支付订单信息；3创建应用内支付记录信息
            //微信支付订单
            IOSInAppEnum iosInAppEnum = IOSInAppEnum.getIOSInAppEnum(payIOSInAppPO.getProductId());
            if (iosInAppEnum == null) {
                continue;
            }
            VipConfigPO vipConfigPO = vipConfigMapper.selectById(iosInAppEnum.getVipConfigId());
            PayRecordPO payRecordPO = new PayRecordPO();
            payRecordPO.setUserId(JwtNewUtil.getUserId());
            payRecordPO.setOrderNo(SequenceNoUtils.generateNo("01"));
            payRecordPO.setVipConfigId(vipConfigPO.getId());
            payRecordPO.setCreateTime(DateUtil.date());
            payRecordPO.setDiscountRatio(1.0);
            payRecordPO.setState(PayRecordEnum.STATE_PAY_SUCCESS.getIntValue());
            //加油包
            if (Objects.equals(VipConfigEnum.GRADE_PT.getIntValue(), vipConfigPO.getGrade())) {
                payRecordPO.setOrderType(PayRecordEnum.ORDER_TYPE_JYB.getIntValue());
                payRecordPO.setTotal(vipConfigPO.getCurrentMonthQuantity());
            }
            //vip支付
            if (Objects.equals(VipConfigEnum.GRADE_VIP.getIntValue(), vipConfigPO.getGrade())) {
                payRecordPO.setOrderType(PayRecordEnum.ORDER_TYPE_VIP.getIntValue());
                payRecordPO.setTotal(new BigDecimal(vipConfigPO.getMonth().toString()).multiply(new BigDecimal(vipConfigPO.getCurrentMonthQuantity().toString())).doubleValue());
            }
            //svip
            if (Objects.equals(VipConfigEnum.GRADE_SVIP.getIntValue(), vipConfigPO.getGrade())) {
                payRecordPO.setOrderType(PayRecordEnum.ORDER_TYPE_SVIP.getIntValue());
                payRecordPO.setTotal(new BigDecimal(vipConfigPO.getMonth().toString()).multiply(new BigDecimal(vipConfigPO.getCurrentMonthQuantity().toString())).doubleValue());
            }
            //苹果应用内支付更新
            payRecordPO.setType(PayRecordEnum.TYPE_ITEM_PAY_IOS_IN_APP.getIntValue());
            payRecordPO.setMonth(vipConfigPO.getMonth());
            payRecordPO.setMonthQuantity(vipConfigPO.getCurrentMonthQuantity());
            //按比率装载金额
            payRecordPO.setAmount(iosInAppEnum.getPrice());
            //查询用户当前会员等级的订单日期
            LambdaQueryWrapper<PayRecordPO> queryWrapper = new LambdaQueryWrapper<PayRecordPO>()
                    .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                    .ne(PayRecordPO::getVipConfigId, 8)//8为体验会员直接过滤掉
                    .eq(PayRecordPO::getUserId, payRecordPO.getUserId())
                    .ne(PayRecordPO::getOrderType, PayRecordEnum.ORDER_TYPE_JYB.getIntValue())
                    .ge(PayRecordPO::getExpirationTime, BDateUtil.getDateNowShanghai())
                    .orderByDesc(PayRecordPO::getExpirationTime).last("LIMIT 0,1");
            int ok = 0;
            PayRecordPO payRecordPOTime = payRecordMapper.selectOne(queryWrapper);
            if (payRecordPOTime != null && payRecordPOTime.getExpirationTime() != null) {
                ok = 1;
                // Date date = payRecordPOTime.getExpirationTime();
                Date date = BDateUtil.getDateNowShanghai();
                payRecordPO.setTaskTime(date);
                payRecordPO.setExpirationTime(BDateUtil.getDateAddMonth(date, vipConfigPO.getMonth()));
            } else {
                Date date = BDateUtil.getDateNowShanghai();
                payRecordPO.setTotalUsage(vipConfigPO.getCurrentMonthQuantity());
                payRecordPO.setTaskTime(BDateUtil.getDateAddMonth(date, 1));
                payRecordPO.setExpirationTime(BDateUtil.getDateAddMonth(date, vipConfigPO.getMonth()));
            }
            //校验加油包
            if (Objects.equals(VipConfigEnum.GRADE_PT.getIntValue(), vipConfigPO.getGrade())) {
                Date date = BDateUtil.getDateNowShanghai();
                payRecordPO.setTotalUsage(vipConfigPO.getCurrentMonthQuantity());
                payRecordPO.setExpirationTime(BDateUtil.getDateAddMonth(date, vipConfigPO.getMonth()));
                payRecordPO.setTaskTime(payRecordPO.getExpirationTime());
            }
            log.info("date:{}", payRecordPO);
            if (payRecordMapper.insert(payRecordPO) < 1) {
                throw new IBusinessException("创建支付订单失败");
            }
            //苹果应用内支付记录
            if (payIosInappMapper.update(
                    null,
                    new LambdaUpdateWrapper<PayIOSInAppPO>()
                            .eq(PayIOSInAppPO::getId, payIOSInAppPO.getId())
                            .set(PayIOSInAppPO::getPayRecordId, payRecordPO.getId())
                            .set(PayIOSInAppPO::getUserId, payRecordPO.getUserId())
            ) < 1) {
                throw new IBusinessException("更新苹果应用内支付记录失败");
            }
            if (ok == 1 && !Objects.equals(VipConfigEnum.GRADE_PT.getIntValue(), vipConfigPO.getGrade())) {
                continue;
            }
            //非续费会员需要更新会员记录信息
            //创建点子记录表
            UserDDRecordPO userDDRecordPO = new UserDDRecordPO();
            userDDRecordPO.setUserId(payRecordPO.getUserId());
            userDDRecordPO.setSourceId(payRecordPO.getId());
            userDDRecordPO.setTotal(payRecordPO.getMonthQuantity());
            userDDRecordPO.setType(UserDDrecordEnum.TYPE_PAY.getIntValue());
            userDDRecordPO.setTypeItem(payRecordPO.getOrderType());
            userDDRecordPO.setTotalUsage((double) 0);
            if (Objects.equals(payRecordPO.getOrderType(), PayRecordEnum.ORDER_TYPE_JYB.getIntValue())) {
                userDDRecordPO.setExpirationTime(payRecordPO.getExpirationTime());
            } else {
                userDDRecordPO.setExpirationTime(com.nacos.utils.DateUtil.getDateAddMonth(payRecordPO.getTaskTime(), 1));
            }
            userDDRecordPO.setRemark(payRecordPO.getRemark());
            if (userDDRecordMapper.insert(userDDRecordPO) < 1) {
                throw new IBusinessException("创建点子记录失败");
            }

            FlowRecordPO flowRecordPO = new FlowRecordPO();
            flowRecordPO.setUserId(payRecordPO.getUserId());
            flowRecordPO.setRecordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue());
            flowRecordPO.setNum(payRecordPO.getMonthQuantity());
            flowRecordPO.setOperateType(FlowRecordEnum.OPERATE_TYPE_ADD_PAY.getIntValue());
            flowRecordPO.setRemark(FlowRecordEnum.OPERATE_TYPE_ADD_PAY.getStrValue());
            if (flowRecordMapper.insert(flowRecordPO) < 1) {
                throw new IBusinessException("创建点子登记信息失败");
            }
        }
        return Result.SUCCESS("权益已到账");

    }

    @Override
    public void doIosAppRequestV2(HttpServletResponse response, HttpServletRequest request) {

        JSONObject wxPayResult = WXPayApis.wxCallBack(request);
        log.info("微信支付回调解密内容：{}", wxPayResult);
        if (wxPayResult == null || ObjectUtils.isEmpty(wxPayResult.getString("out_trade_no"))) {
            WXPayApis.wxPayFile(response);//返回失败
            return;
        }

//        String verifyResult = ApplePayUtil.buyAppVerify(receipt, 0);


        LambdaQueryWrapper<PayRecordPO> queryWrapper = new LambdaQueryWrapper<PayRecordPO>()
                .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_NOT.getIntValue())
                .eq(PayRecordPO::getOrderNo, wxPayResult.getString("out_trade_no"));
        PayRecordPO payRecordPO = payRecordMapper.selectOne(queryWrapper);
        if (payRecordPO == null) {
            WXPayApis.wxPayFile(response);//返回失败
            return;
        }
        log.info("查询相关订单信息：{}", payRecordPO);
        LambdaUpdateWrapper<PayRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PayRecordPO::getId, payRecordPO.getId());
        updateWrapper.set(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue());
        //校验生成时间是否为小于今天，有可能为昨天下的订单
        if (
                Integer.parseInt(com.nacos.utils.DateUtil.getToStringYYYYMMdd(payRecordPO.getTaskTime())) <= Integer.parseInt(com.nacos.utils.DateUtil.dateToString(com.nacos.utils.DateUtil.getDateNowShanghai(), "yyyyMMdd"))
        ) {
            Date date = com.nacos.utils.DateUtil.getDateAddMonth(payRecordPO.getTaskTime(), 1);
            //更新为下次更新时间
            //校验是否为加油包属性，如果为加油包则直接为失效日期
            if (Objects.equals(payRecordPO.getOrderType(), PayRecordEnum.ORDER_TYPE_JYB.getIntValue())) {
                updateWrapper.set(PayRecordPO::getTaskTime, payRecordPO.getExpirationTime());
            } else {
                updateWrapper.set(PayRecordPO::getTaskTime, date);
            }
            updateWrapper.set(PayRecordPO::getTotalUsage, new BigDecimal(payRecordPO.getTotalUsage().toString()).add(new BigDecimal(payRecordPO.getMonthQuantity().toString())));
            //创建点子记录表
            UserDDRecordPO userDDRecordPO = new UserDDRecordPO();
            userDDRecordPO.setUserId(payRecordPO.getUserId());
            userDDRecordPO.setTotal(payRecordPO.getMonthQuantity());
            userDDRecordPO.setType(UserDDrecordEnum.TYPE_PAY.getIntValue());
            userDDRecordPO.setTypeItem(payRecordPO.getOrderType());
            userDDRecordPO.setTotalUsage((double) 0);
            if (Objects.equals(payRecordPO.getOrderType(), PayRecordEnum.ORDER_TYPE_JYB.getIntValue())) {
                userDDRecordPO.setExpirationTime(payRecordPO.getExpirationTime());
            } else {
                userDDRecordPO.setExpirationTime(date);
            }
            userDDRecordPO.setRemark(payRecordPO.getRemark());
            if (userDDRecordMapper.insert(userDDRecordPO) < 1) {
                //返回失败
                WXPayApis.wxPayFile(response);
                return;
            }
        }
        if (payRecordMapper.update(null, updateWrapper) < 1) {
            WXPayApis.wxPayFile(response);
//            throw new IBusinessException("创建点子记录信息失败");
        }
        FlowRecordPO flowRecordPO = new FlowRecordPO();
        flowRecordPO.setUserId(payRecordPO.getUserId());
        flowRecordPO.setRecordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue());
        flowRecordPO.setNum(payRecordPO.getMonthQuantity());
        flowRecordPO.setOperateType(FlowRecordEnum.OPERATE_TYPE_ADD_PAY.getIntValue());
        flowRecordPO.setRemark(FlowRecordEnum.OPERATE_TYPE_ADD_PAY.getStrValue());
        if (flowRecordMapper.insert(flowRecordPO) < 1) {
            WXPayApis.wxPayFile(response);
//            throw new IBusinessException("创建点子登记信息失败");
        }
        //返回成功
        WXPayApis.wxPaySuccess(response);

        try {
//            UserPO userPO = userMapper.getByUserId(payRecordPO.getUserId());
//            VipConfigPO vipConfigPO = vipConfigMapper.selectById(payRecordPO.getVipConfigId());
//            String newDate = com.nacos.utils.DateUtil.getCurrentDateString();
//            SMSTmplCodeEnum smsTmplCodeEnum;
//            String tmplParam;
//            SysNotificationPO notificationPO;
//
//            if (payRecordPO.getOrderType().intValue() == PayRecordEnum.ORDER_TYPE_JYB.getIntValue()) {
//                smsTmplCodeEnum = SMSTmplCodeEnum.DATA_PLUS_TOPUP_TEMPLATECODE;
//                tmplParam = buildDataPlusTopupSmsTmplParam(userPO, newDate, vipConfigPO, payRecordPO);
//                notificationPO = buildDataPlusTopupSysNotification(userPO, newDate, vipConfigPO, payRecordPO);
//            } else {
//                smsTmplCodeEnum = SMSTmplCodeEnum.MEMBER_TOPUP_TEMPLATECODE;
//                tmplParam = buildMemberTopupSmsTmplParam(userPO, newDate, vipConfigPO, payRecordPO);
//                notificationPO = buildMemberTopupSysNotification(userPO, newDate, vipConfigPO, payRecordPO);
//            }
//
//            SMSAliyunUtil.phoneSmsSend(userPO.getMobile(), smsTmplCodeEnum, tmplParam);
//            sysNotificationService.save(notificationPO);
//
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("type", WebSocketEnum.WX_PAY_PUSH.getPushType());
//            jsonObject.put("userId", payRecordPO.getUserId());
//            jsonObject.put("object", JSONObject.toJSONString(notificationPO));
//            WebSocketHandler.sendMessage(String.valueOf(payRecordPO.getUserId()), jsonObject.toString());
//            Message.pushMessage(jsonObject.toString(), userService);
        } catch (Exception e) {
            log.error("充值成功推送信息失败", e);
        }

    }


}

