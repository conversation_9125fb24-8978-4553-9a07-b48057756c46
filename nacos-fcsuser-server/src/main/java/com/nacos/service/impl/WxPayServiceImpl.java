package com.nacos.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.business.db.mapper.*;
import com.business.db.model.bo.SmsTmplParamBO;
import com.business.db.model.po.*;
import com.business.enums.BNotificationEnum;
import com.business.enums.BNotificationTmplEnum;
import com.nacos.config.CommonConst;
import com.nacos.config.message.YouMengMessage;
import com.nacos.enums.*;
import com.nacos.exception.IBusinessException;
import com.nacos.handler.WebSocketHandler;
import com.nacos.result.Result;
import com.nacos.service.IUserService;
import com.nacos.service.WxPayService;
import com.nacos.service.sys.ISysNotificationService;
import com.nacos.utils.CommonUtil;
import com.nacos.utils.sms.SMSAliyunUtil;
import com.nacos.utils.wxpay.QRBase64Util;
import com.nacos.utils.wxpay.SequenceNoUtils;
import com.nacos.utils.wxpay.WXPayApis;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Log4j2
@Service
@Transactional(rollbackFor = Exception.class)
public class WxPayServiceImpl implements WxPayService {


    /*用户*/
    @Resource
    private UserMapper userMapper;
    @Resource
    private IUserService userService;
    /*会员配置*/
    @Resource
    private VipConfigMapper vipConfigMapper;
    /*会员等级*/
    @Resource
    private VipGradeMapper vipGradeMapper;
    /* 支付记录 */
    @Resource
    private PayRecordMapper payRecordMapper;
    /* 点子记录 */
    @Resource
    private UserDDRecordMapper userDDRecordMapper;
    @Resource
    private FlowRecordMapper flowRecordMapper;
    @Resource
    private ISysNotificationService sysNotificationService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void wxCallBack(HttpServletResponse response, HttpServletRequest request) throws IBusinessException {
        JSONObject wxPayResult = WXPayApis.wxCallBack(request);
        log.info("微信支付回调解密内容：{}", wxPayResult);
        if (wxPayResult == null || ObjectUtils.isEmpty(wxPayResult.getString("out_trade_no"))) {
            WXPayApis.wxPayFile(response);//返回失败
            return;
        }
        LambdaQueryWrapper<PayRecordPO> queryWrapper = new LambdaQueryWrapper<PayRecordPO>()
                .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_NOT.getIntValue())
                .eq(PayRecordPO::getOrderNo, wxPayResult.getString("out_trade_no"));
        PayRecordPO payRecordPO = payRecordMapper.selectOne(queryWrapper);
        if (payRecordPO == null) {
            WXPayApis.wxPayFile(response);//返回失败
            return;
        }
        log.info("查询相关订单信息：{}", payRecordPO);
        LambdaUpdateWrapper<PayRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PayRecordPO::getId, payRecordPO.getId());
        updateWrapper.set(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue());
        //校验生成时间是否为小于今天，有可能为昨天下的订单
        if (
                Integer.parseInt(com.nacos.utils.DateUtil.getToStringYYYYMMdd(payRecordPO.getTaskTime())) <= Integer.parseInt(com.nacos.utils.DateUtil.dateToString(com.nacos.utils.DateUtil.getDateNowShanghai(), "yyyyMMdd"))
        ) {
            Date date = com.nacos.utils.DateUtil.getDateAddMonth(payRecordPO.getTaskTime(), 1);
            //更新为下次更新时间
            //校验是否为加油包属性，如果为加油包则直接为失效日期
            if (Objects.equals(payRecordPO.getOrderType(), PayRecordEnum.ORDER_TYPE_JYB.getIntValue())) {
                updateWrapper.set(PayRecordPO::getTaskTime, payRecordPO.getExpirationTime());
            } else {
                updateWrapper.set(PayRecordPO::getTaskTime, date);
            }
            updateWrapper.set(PayRecordPO::getTotalUsage, new BigDecimal(payRecordPO.getTotalUsage().toString()).add(new BigDecimal(payRecordPO.getMonthQuantity().toString())));
            //创建点子记录表
            UserDDRecordPO userDDRecordPO = new UserDDRecordPO();
            userDDRecordPO.setUserId(payRecordPO.getUserId());
            userDDRecordPO.setTotal(payRecordPO.getMonthQuantity());
            userDDRecordPO.setType(UserDDrecordEnum.TYPE_PAY.getIntValue());
            userDDRecordPO.setTypeItem(payRecordPO.getOrderType());
            userDDRecordPO.setTotalUsage((double) 0);
            if (Objects.equals(payRecordPO.getOrderType(), PayRecordEnum.ORDER_TYPE_JYB.getIntValue())) {
                userDDRecordPO.setExpirationTime(payRecordPO.getExpirationTime());
            } else {
                userDDRecordPO.setExpirationTime(date);
            }
            userDDRecordPO.setRemark(payRecordPO.getRemark());
            if (userDDRecordMapper.insert(userDDRecordPO) < 1) {
                //返回失败
                WXPayApis.wxPayFile(response);
                return;
            }
        }
        if (payRecordMapper.update(null, updateWrapper) < 1) {
            WXPayApis.wxPayFile(response);
            throw new IBusinessException("创建点子记录信息失败");
        }
        FlowRecordPO flowRecordPO = new FlowRecordPO();
        flowRecordPO.setUserId(payRecordPO.getUserId());
        flowRecordPO.setRecordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue());
        flowRecordPO.setNum(payRecordPO.getMonthQuantity());
        flowRecordPO.setOperateType(FlowRecordEnum.OPERATE_TYPE_ADD_PAY.getIntValue());
        flowRecordPO.setRemark(FlowRecordEnum.OPERATE_TYPE_ADD_PAY.getStrValue());
        if (flowRecordMapper.insert(flowRecordPO) < 1) {
            WXPayApis.wxPayFile(response);
            throw new IBusinessException("创建点子登记信息失败");
        }
        //返回成功
        WXPayApis.wxPaySuccess(response);

        try {
            UserPO userPO = userMapper.getByUserId(payRecordPO.getUserId());
            VipConfigPO vipConfigPO = vipConfigMapper.selectById(payRecordPO.getVipConfigId());
            String newDate = com.nacos.utils.DateUtil.getCurrentDateString();
            SMSTmplCodeEnum smsTmplCodeEnum;
            String tmplParam;
            SysNotificationPO notificationPO;

            if (payRecordPO.getOrderType().intValue() == PayRecordEnum.ORDER_TYPE_JYB.getIntValue()) {
                boolean isForeign = false;
                if (PhoneUtil.isPhone(userPO.getMobile())) {
                    smsTmplCodeEnum = SMSTmplCodeEnum.DATA_PLUS_TOPUP_TEMPLATECODE;
                } else {
                    isForeign = true;
                    smsTmplCodeEnum = SMSTmplCodeEnum.INTL_DATA_PLUS_TOPUP_TEMPLATECODE;
                }
                tmplParam = buildDataPlusTopupSmsTmplParam(userPO, newDate, vipConfigPO, payRecordPO, isForeign);
                notificationPO = buildDataPlusTopupSysNotification(userPO, newDate, vipConfigPO, payRecordPO);
            } else {
                boolean isForeign = false;
                if (PhoneUtil.isPhone(userPO.getMobile())) {
                    smsTmplCodeEnum = SMSTmplCodeEnum.MEMBER_TOPUP_TEMPLATECODE;
                } else {
                    isForeign = true;
                    smsTmplCodeEnum = SMSTmplCodeEnum.INTL_MEMBER_TOPUP_TEMPLATECODE;
                }
                tmplParam = buildMemberTopupSmsTmplParam(userPO, newDate, vipConfigPO, payRecordPO, isForeign);
                notificationPO = buildMemberTopupSysNotification(userPO, newDate, vipConfigPO, payRecordPO);
            }

            SMSAliyunUtil.phoneSmsSend(userPO.getMobile(), smsTmplCodeEnum, tmplParam);
            notificationPO.setTaskId(payRecordPO.getId());
            sysNotificationService.save(notificationPO);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", WebSocketEnum.WX_PAY_PUSH.getPushType());
            jsonObject.put("userId", payRecordPO.getUserId());
            jsonObject.put("object", JSONObject.toJSONString(notificationPO));
            WebSocketHandler.sendMessage(payRecordPO.getUserId(), jsonObject.toString());
            YouMengMessage.pushMessage(jsonObject.toString(), userService);
        } catch (Exception e) {
            log.error("充值成功推送信息失败", e);
        }
    }

    public String buildDataPlusTopupSmsTmplParam(UserPO userPO, String newDate, VipConfigPO vipConfigPO, PayRecordPO payRecordPO, boolean isForeign) {
        return CommonUtil.smsConversionTool(SmsTmplParamBO.buildSmsTmplParamDataPlusTopup(userPO.getName(), newDate, vipConfigPO.getTitle(), payRecordPO.getAmount(), isForeign));
    }

    public SysNotificationPO buildDataPlusTopupSysNotification(UserPO userPO, String newDate, VipConfigPO vipConfigPO, PayRecordPO payRecordPO) {
        return SysNotificationPO.buildSysNotification(payRecordPO.getUserId(), BNotificationEnum.PAY_NOTIF.getIntValue(),
                BNotificationTmplEnum.DATA_PLUS_TOPUP_TEMPLATE.getStrTitle(), BNotificationTmplEnum.DATA_PLUS_TOPUP_TEMPLATE.getStrContent().replace("${name}", userPO.getName())
                        .replace("${date}", newDate)
                        .replace("${title}", vipConfigPO.getTitle())
                        .replace("${amount}", payRecordPO.getAmount().toString()), 0, payRecordPO.getId(), null);
    }

    public String buildMemberTopupSmsTmplParam(UserPO userPO, String newDate, VipConfigPO vipConfigPO, PayRecordPO payRecordPO, boolean isForeign) {
        String grade = vipConfigPO.getTitle().contains(CommonConst.EXPERIENCE_MEMBER) ? vipConfigPO.getTitle() : vipGradeMapper.selectById(vipConfigPO.getGrade()).getTitle().concat("-").concat(vipConfigPO.getTitle());
        return CommonUtil.smsConversionTool(SmsTmplParamBO.buildSmsTmplParamMemberTopUp(userPO.getName(), newDate, grade, payRecordPO.getAmount(), isForeign));
    }

    public SysNotificationPO buildMemberTopupSysNotification(UserPO userPO, String newDate, VipConfigPO vipConfigPO, PayRecordPO payRecordPO) {
        String grade = vipConfigPO.getTitle().contains(CommonConst.EXPERIENCE_MEMBER) ? vipConfigPO.getTitle() : vipGradeMapper.selectById(vipConfigPO.getGrade()).getTitle().concat("-").concat(vipConfigPO.getTitle());
        return SysNotificationPO.buildSysNotification(payRecordPO.getUserId(), BNotificationEnum.PAY_NOTIF.getIntValue(),
                BNotificationTmplEnum.MEMBER_TOPUP_TEMPLATE.getStrTitle(), BNotificationTmplEnum.MEMBER_TOPUP_TEMPLATE.getStrContent().replace("${name}", userPO.getName())
                        .replace("${date}", newDate)
                        .replace("${grade}", grade)
                        .replace("${amount}", payRecordPO.getAmount().toString()), 0, payRecordPO.getId(), null);
    }


    @Deprecated
    @Override
    public Result<Object> wxPayAll(Long rechargeId, Long userId, Integer wxPayType) {

        //校验充值id是否准确
        VipConfigPO vipConfigPO = vipConfigMapper.selectById(rechargeId);
        if (vipConfigPO == null) {
            return Result.ERROR("充值id不正确");
        }
        VipGradePO vipGradePO = vipGradeMapper.selectById(vipConfigPO.getGrade());
        if (vipGradePO == null) {
            return Result.ERROR("会员等级不正确");
        }
        //校验用户id是否准确
        UserPO userPO = userMapper.selectById(userId);
        if (userPO == null) {
            return Result.ERROR("用户id不正确");
        }
        //如果是不可重复购买的则拦截
        if (Objects.equals(vipConfigPO.getRepurchaseFlag(), VipConfigEnum.REPURCHASE_FLAG_FALSE.getIntValue())) {
            LambdaQueryWrapper<PayRecordPO> queryWrapper = new LambdaQueryWrapper<PayRecordPO>()
                    .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                    .eq(PayRecordPO::getVipConfigId, rechargeId)
                    .eq(PayRecordPO::getUserId, userId);
            Long count = payRecordMapper.selectCount(queryWrapper);
            if (ObjectUtil.isNotNull(count) && count > 0) {
                return Result.ERROR("该会员套餐不可重复购买");
            }
        }
        Long vipCount = payRecordMapper.selectCount(
                new LambdaQueryWrapper<PayRecordPO>()
                        .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                        .in(PayRecordPO::getOrderType, PayRecordEnum.ORDER_TYPE_VIP.getIntValue(), PayRecordEnum.ORDER_TYPE_SVIP.getIntValue())
                        .eq(PayRecordPO::getUserId, userId)
        );
        if (vipCount >= 1 && rechargeId == 8) {
            return Result.ERROR("该体验会员已失效");
        }
        BigDecimal discou1ntRate = new BigDecimal("1");//折扣比率为1，不进行折扣

        //查询会员等级旧的
//        Integer  = checkService.getUserGrade(userId);
        //查看会员当前等级
        Integer grade = payRecordMapper.queryUserGrade(userId);
        if (grade == null) {
            grade = VipGradeEnum.MEMBER_PT.getIntValue();
        }

        //校验加油包,可重复购买，并进行等级校验
        if (Objects.equals(VipConfigEnum.GRADE_PT.getIntValue(), vipConfigPO.getGrade())
                && Objects.equals(vipConfigPO.getRepurchaseFlag(), VipConfigEnum.REPURCHASE_FLAG_TRUE.getIntValue())) {

            // vip、svip计算折扣
            if (Objects.equals(grade, VipGradeEnum.MEMBER_VIP.getIntValue()) || Objects.equals(grade, VipGradeEnum.MEMBER_SVIP.getIntValue())) {
                LambdaQueryWrapper<VipConfigPO> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(VipConfigPO::getIsShow, CommonIntEnum.SHOW_TRUE.getIntValue())
                        .eq(VipConfigPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .eq(VipConfigPO::getGrade, VipConfigEnum.GRADE_PT.getIntValue())
                        .orderByAsc(VipConfigPO::getSort);
                List<VipConfigPO> vipConfigPOS = vipConfigMapper.selectList(queryWrapper);
                for (int i = 0; i < vipConfigPOS.size(); i++) {
                    BigDecimal indexRatio = new BigDecimal("0.05").multiply(new BigDecimal(String.valueOf(i))).setScale(2, RoundingMode.HALF_UP);
                    //VIP 会员展示
                    if (Objects.equals(grade, VipGradeEnum.MEMBER_VIP.getIntValue()) && Objects.equals(rechargeId, vipConfigPOS.get(i).getId())) {
                        discou1ntRate = i >= 6 ? new BigDecimal("0.7") : new BigDecimal("0.95").subtract(indexRatio).setScale(2, RoundingMode.HALF_UP);
                        break;
                    }
                    //SVIP 会员展示
                    if (Objects.equals(grade, VipGradeEnum.MEMBER_SVIP.getIntValue()) && Objects.equals(rechargeId, vipConfigPOS.get(i).getId())) {
                        discou1ntRate = i >= 6 ? new BigDecimal("0.6") : new BigDecimal("0.85").subtract(indexRatio).setScale(2, RoundingMode.HALF_UP);
                        break;
                    }
                }
            }

        }

        //微信支付订单
        PayRecordPO payRecordPO = new PayRecordPO();
        payRecordPO.setUserId(userId);
        payRecordPO.setOrderNo(SequenceNoUtils.generateNo("01"));
        payRecordPO.setVipConfigId(vipConfigPO.getId());
        payRecordPO.setCreateTime(DateUtil.date());
        payRecordPO.setDiscountRatio(discou1ntRate.doubleValue());
        payRecordPO.setState(PayRecordEnum.STATE_PAY_NOT.getIntValue());
        //区分支付方式(微信加油包支付)
        if (Objects.equals(VipConfigEnum.GRADE_PT.getIntValue(), vipConfigPO.getGrade())) {
            payRecordPO.setOrderType(PayRecordEnum.ORDER_TYPE_JYB.getIntValue());
            payRecordPO.setTotal(vipConfigPO.getCurrentMonthQuantity());
        }
        //区分支付方式(微信vip支付)
        if (Objects.equals(VipConfigEnum.GRADE_VIP.getIntValue(), vipConfigPO.getGrade())) {
            payRecordPO.setOrderType(PayRecordEnum.ORDER_TYPE_VIP.getIntValue());
            payRecordPO.setTotal(new BigDecimal(vipConfigPO.getMonth().toString()).multiply(new BigDecimal(vipConfigPO.getCurrentMonthQuantity().toString())).doubleValue());
        }
        //区分支付方式(微信svip支付)
        if (Objects.equals(VipConfigEnum.GRADE_SVIP.getIntValue(), vipConfigPO.getGrade())) {
            payRecordPO.setOrderType(PayRecordEnum.ORDER_TYPE_SVIP.getIntValue());
            payRecordPO.setTotal(new BigDecimal(vipConfigPO.getMonth().toString()).multiply(new BigDecimal(vipConfigPO.getCurrentMonthQuantity().toString())).doubleValue());
        }
        //区分微信支付
        payRecordPO.setType(wxPayType);
        payRecordPO.setMonth(vipConfigPO.getMonth());
        payRecordPO.setMonthQuantity(vipConfigPO.getCurrentMonthQuantity());

        //按比率装载金额
        payRecordPO.setAmount(vipConfigPO.getCurrentPrice().multiply(discou1ntRate));

        //查询用户当前会员等级的订单日期
        LambdaQueryWrapper<PayRecordPO> queryWrapper = new LambdaQueryWrapper<PayRecordPO>()
                .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                .ne(PayRecordPO::getVipConfigId, 8)//8为体验会员直接过滤掉
                .eq(PayRecordPO::getUserId, userId)
                .ne(PayRecordPO::getOrderType, PayRecordEnum.ORDER_TYPE_JYB.getIntValue())
                .ge(PayRecordPO::getExpirationTime, com.nacos.utils.DateUtil.getDateNowShanghai())
                .orderByDesc(PayRecordPO::getExpirationTime).last("LIMIT 0,1");
        Date date = com.nacos.utils.DateUtil.getDateNowShanghai();
        PayRecordPO payRecordPOTime = payRecordMapper.selectOne(queryWrapper);
        if (payRecordPOTime != null && payRecordPOTime.getExpirationTime() != null) {
            date = payRecordPOTime.getExpirationTime();
        }
        //校验加油包
        if (Objects.equals(VipConfigEnum.GRADE_PT.getIntValue(), vipConfigPO.getGrade())) {
            date = com.nacos.utils.DateUtil.getDateNowShanghai();
        }

        log.info("date:{}", date);
        payRecordPO.setTaskTime(date);
        payRecordPO.setExpirationTime(com.nacos.utils.DateUtil.getDateAddMonth(date, vipConfigPO.getMonth()));
        // 初始化规则权益
        // 规则：点点{用户ID}-购买{商品名称}权益
        // 案例：点点会员DERSF-购买SVIP月度权益
        String description = "";
        if (Objects.equals(vipGradePO.getGrade(), VipGradeEnum.MEMBER_PT.getIntValue())) {
            description = VipGradeEnum.getByValue(grade) + ": " + userPO.getInvitationCode() + "-购买" + VipConfigEnum.RECHARGE_TYPE_JYB.getStrValue() + vipConfigPO.getTitle() + "权益";
        } else {
            description = VipGradeEnum.getByValue(grade) + ": " + userPO.getInvitationCode() + "-购买" + vipGradePO.getTitle() + vipConfigPO.getTitle() + "权益";
        }

        LambdaQueryWrapper<PayRecordPO> queryWrapperOrderNO = new LambdaQueryWrapper<PayRecordPO>()
                .eq(PayRecordPO::getOrderNo, payRecordPO.getOrderNo());
        Long flightNumber = payRecordMapper.selectCount(queryWrapperOrderNO);
        if (flightNumber != null && flightNumber > 0) {
            return Result.ERROR("订单已存在");
        }

        //生成订单
        log.info("生成订单参数：{}", payRecordPO);
        int d = payRecordMapper.insert(payRecordPO);
        log.info("创建订单操作返回结果：{}", d);
        if (d > 0) {
            if (Objects.equals(PayRecordEnum.TYPE_ITEM_PAY_WXPAYNATIVE.getIntValue(), wxPayType)) {
                log.info("调用微信扫码支付：{}", payRecordPO);
                String imageBate64String = QRBase64Util.getQRImageBate64String(WXPayApis.postNativePlaceOrder(payRecordPO.getOrderNo(), payRecordPO.getAmount(), description));
                if (imageBate64String == null) {
                    return Result.ERROR("二维码生成失败");
                }
                return Result.SUCCESS(imageBate64String);
            }
            //微信 app支付
//            else if (Objects.equals(PayRecordEnum.TYPE_ITEM_PAY_WXPAYAPP.getIntValue(), wxPayType)){
//                log.info("调用微信APP支付：{}", payRecordPO);
//                return Result.SUCCESS(WXPayApis.postAppPlaceOrder(payRecordPO.getOrderNo(),payRecordPO.getAmount(),description));
//            }
            return Result.SUCCESS(null);
        }
        return Result.ERROR("订单生成失败");
    }
}
