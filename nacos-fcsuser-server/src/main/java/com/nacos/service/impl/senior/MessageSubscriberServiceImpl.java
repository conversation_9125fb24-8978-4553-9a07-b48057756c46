package com.nacos.service.impl.senior;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.business.db.mapper.ImgDrawRecordMapper;
import com.business.db.mapper.UserPrivateConfigMapper;
import com.business.db.model.po.ImgDrawDetlPO;
import com.business.db.model.po.SysNotificationPO;
import com.business.db.model.po.UserPrivateConfigPO;
import com.business.enums.BIntEnum;
import com.business.enums.BNotificationEnum;
import com.business.message.BMessageSendEnum;
import com.business.message.BMessageSendUtil;
import com.business.message.model.BMessageObject;
import com.business.model.po.ImgDrawRecordPO;
import com.business.model.vo.ImgDrawDetlVO;
import com.business.model.vo.ImgDrawHistoryVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.config.message.YouMengMessage;
import com.nacos.ddimg.ImgDrawUtil;
import com.nacos.enums.*;
import com.nacos.exception.IBusinessException;
import com.nacos.handler.WebSocketHandler;
import com.nacos.mjapi.model.JobStatusBO;
import com.nacos.redis.RedisUtil;
import com.nacos.service.IUserService;
import com.nacos.service.ImageService;
import com.nacos.service.ImgDrawDetlService;
import com.nacos.service.mp.IUserDDRecordService;
import com.nacos.service.senior.MessageSubscriberService;
import com.nacos.utils.ActivityUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
public class MessageSubscriberServiceImpl implements MessageSubscriberService {

    @Resource
    private IUserDDRecordService userDDRecordService;

    @Resource
    private IUserService userService;

    @Resource
    private ImgDrawRecordMapper imgDrawRecordMapper;//绘图记录

    @Resource
    private ImgDrawDetlService imgDrawDetlService;//绘图明细

    @Resource
    private ImageService imageService;

    @Autowired
    private UserPrivateConfigMapper userPrivateConfigMapper;

    @Override
    public void pushMessageDrawMJ(BMessageObject bMessageObject) throws JsonProcessingException {
        drawTaskMessagePush(JSONObject.from(bMessageObject.getObject()), bMessageObject.getRedisId());
    }

    private void drawTaskMessagePush(JSONObject jsonObjectMessagePush, String jobId) throws JsonProcessingException {
        // log.info("jsonObjectMessagePush, {}", jsonObjectMessagePush);
        if (jsonObjectMessagePush == null) {
            return;
        }
        //通过 jobId 获取 redis 缓存中的任务信息
        String jsonObjectStr = RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_JOB_ID.getStrKey(), jobId));
        if (jsonObjectStr == null) {
            log.error("通过jobId获取缓存中任务信息失败");
            return;
        }
        // log.info("====jsonObjectStr= {}", jsonObjectStr);
        // 获取 json 中绘图详情信息
        ObjectMapper objectMapper = new ObjectMapper();
        ImgDrawHistoryVO imgHistoryVO = objectMapper.readValue(jsonObjectStr, ImgDrawHistoryVO.class);
        // log.info("====imgHistoryVO= {}", imgHistoryVO);
        JobStatusBO jobStatusBO = JSON.toJavaObject(jsonObjectMessagePush, JobStatusBO.class);
        // log.info("====jobStatusBO= {}", jobStatusBO);
        if (jobStatusBO.getCurrentStatus().equals("FAILURE")) {
            log.info("任务失败, currentStatus= {}", jobStatusBO.getCurrentStatus());
            ImgDrawHistoryVO imgDrawHistoryVO = initImgDrawHistoryVO(imgHistoryVO);
            pushDrawTaskResult(imgDrawHistoryVO, null);
            pushDrawTaskRealTime(imgDrawHistoryVO);
            RedisUtil.removeKey(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_JOB_ID.getStrKey(), jobId));
            //将失败消息存放到redis中，供前端拉取使用。
            imgHistoryVO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());
            imgHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_FAIL.getValue());
            imgHistoryVO.setJobRunningSchedule(jobStatusBO.getJobRunningSchedule());
            imgHistoryVO.setJobId(jobStatusBO.getJobId());
            imgHistoryVO.setOptTitleOne(ImgOptModelEnum.getOptTitleOne(imgHistoryVO.getOptAttribute()));
            RedisUtil.setValueSeconds(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_FADE_IN_JOB_ID.getStrKey(), jobId), JSONObject.toJSONString(imgHistoryVO), 1, TimeUnit.HOURS);
            return;
        }
        for (ImgDrawDetlVO imgDrawDetlVO : imgHistoryVO.getImgDrawDetls()) {
            int index = imgHistoryVO.getImgDrawDetls().indexOf(imgDrawDetlVO);
            //判断任务是否已经完成
            if (ImgDrawUtil.getIsCommitted(jobStatusBO.getCurrentStatus())) {
                imgDrawDetlVO.setImgSourceUrl(jobStatusBO.getImagePaths().get(index));
                imgDrawDetlVO.setImgUrl(jobStatusBO.getImagePaths().get(index));
            }
            //判断任务未完成则更新图片地址
            if (!ImgDrawUtil.getIsCommitted(jobStatusBO.getCurrentStatus()) && jobStatusBO.getJobStatusRunningImgs() != null && jobStatusBO.getJobStatusRunningImgs().size() > index) {
                imgDrawDetlVO.setImgUrl(jobStatusBO.getJobStatusRunningImgs().get(index));
            }
        }
        //校验任务是否完成，完成则更新任务状态
        // TODO 需要进行锁处理，防止数据重复：任务详情锁处理
        if (ImgDrawUtil.getIsCommitted(jobStatusBO.getCurrentStatus())) {
            boolean lockAcquired = RedisUtil.acquireLock(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.TASK_LOCK_MJ_DRAW_SUCCESS_SAVE_JOB_ID.getStrKey(), jobStatusBO.getJobId()), 120);
            //未获取到锁，直接返回
            if (!lockAcquired) {
                return;
            }
            imgHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue());
            imgHistoryVO.setFunType(ImgDrawEnum.FUN_TYPE_DRAW.getValue());
            List<ImgDrawDetlVO> imgDrawDetlVOList = updateDrawTaskStatus(jobStatusBO, imgHistoryVO);
            if (!imgDrawDetlVOList.isEmpty()) {
                imgHistoryVO.setImgDrawDetls(imgDrawDetlVOList);
                RedisUtil.removeKey(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_JOB_ID.getStrKey(), jobStatusBO.getJobId()));
            }
            RedisUtil.releaseLock(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.TASK_LOCK_MJ_DRAW_SUCCESS_SAVE_JOB_ID.getStrKey(), jobStatusBO.getJobId()));//释放锁
        }
        //校验未完成则更新任务状态
        if (!ImgDrawUtil.getIsCommitted(jobStatusBO.getCurrentStatus())) {
            imgHistoryVO.setJobRunningSchedule(jobStatusBO.getJobRunningSchedule());
        }
        imgHistoryVO.setJobId(jobStatusBO.getJobId());
        imgHistoryVO.setOptTitleOne(ImgOptModelEnum.getOptTitleOne(imgHistoryVO.getOptAttribute()));

        //将消息存放到redis中，供前端拉取使用。
        RedisUtil.setValueSeconds(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DRAW_MJ_FADE_IN_JOB_ID.getStrKey(), jobId), JSONObject.toJSONString(imgHistoryVO), 1, TimeUnit.HOURS);

        System.out.println("推销消息");
        //推送任务
        if (!ImgDrawUtil.getIsCommitted(jobStatusBO.getCurrentStatus())) {
            pushDrawTaskRealTime(imgHistoryVO);
        }
    }

    /**
     * 初始化绘画历史记录
     *
     * @param imgHistoryVO
     * @return
     * @throws IBusinessException
     */
    private ImgDrawHistoryVO initImgDrawHistoryVO(ImgDrawHistoryVO imgHistoryVO) {
        ImgDrawHistoryVO imgDrawHistoryVO = new ImgDrawHistoryVO();
        imgDrawHistoryVO.setId(imgHistoryVO.getId());
        imgDrawHistoryVO.setPrompt(imgHistoryVO.getPrompt());
        imgDrawHistoryVO.setUserId(imgHistoryVO.getUserId());
        imgDrawHistoryVO.setStatus(ImgDrawEnum.STATUS_FINISH_FAIL.getValue());
        String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgHistoryVO.getOptAttribute());
        String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgHistoryVO.getOptAttribute());
        imgDrawHistoryVO.setRemark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "").concat("任务失败，请重试"));
        return imgDrawHistoryVO;
    }

    private List<ImgDrawDetlVO> updateDrawTaskStatus(JobStatusBO jobStatusBO, ImgDrawHistoryVO imgHistoryVO) {
        //校验绘图成功执行保存操作
        if (imgHistoryVO.getImgDrawDetls().isEmpty()) {
            return new ArrayList<>();
        }
        if (imgDrawRecordMapper.update(null, new LambdaUpdateWrapper<ImgDrawRecordPO>()
                .eq(ImgDrawRecordPO::getId, imgHistoryVO.getId())
                .set(ImgDrawRecordPO::getFinishTime, System.currentTimeMillis())
                .set(ImgDrawRecordPO::getStatus, ImgDrawEnum.STATUS_FINISH_SUCCESS.getValue())) < 1) {
            return new ArrayList<>();
        }
        List<ImgDrawDetlVO> imgDrawDetls = new ArrayList<>();
        List<ImgDrawDetlPO> imgDrawDetlsList = new ArrayList<>();
        log.info("持久化存储mj绘图详情信息==imgHistoryVO.getImgDrawDetls() {}", imgHistoryVO.getImgDrawDetls());
        String jobImageUrl = null;
        LambdaQueryWrapper<UserPrivateConfigPO> lamb = new LambdaQueryWrapper<UserPrivateConfigPO>();
        lamb.eq(UserPrivateConfigPO::getUserId, imgHistoryVO.getUserId());
        lamb.eq(UserPrivateConfigPO::getFunType, 1);
        UserPrivateConfigPO userPrivateConfigPO = userPrivateConfigMapper.selectOne(lamb);
        for (int i = 0; i < imgHistoryVO.getImgDrawDetls().size(); i++) {
            ImgDrawDetlVO imgDrawDetlVO = imgHistoryVO.getImgDrawDetls().get(i);
            int outputIndex = imgDrawDetlVO.getImgSourceUrl().indexOf("output");
            String outputSubstring = null;
            if (outputIndex != -1) {
                outputSubstring = imgDrawDetlVO.getImgSourceUrl().substring(outputIndex + "output".length());
            }
            ImgDrawDetlPO imgDrawDetlPO = ImgDrawDetlPO.buildImgDrawDetlPO(imgHistoryVO.getId(), imgHistoryVO.getOptAttribute(), imgHistoryVO.getModeAttribute(), imgHistoryVO.getUserId(),
                    imgDrawDetlVO.getImgIndex(), imgDrawDetlVO.getImgSourceUrl(), imgDrawDetlVO.getWhDivide(),
                    imgDrawDetlVO.getImgSize(), jobStatusBO.getEventWidth(), jobStatusBO.getEventHeight(), imgDrawDetlVO.getImgType(), imgDrawDetlVO.getImgHue());
            imgDrawDetlPO.setImgUrl(CommonStrEnum.MIDJOURNEY.getValue() + (outputSubstring != null ? outputSubstring.replace(".png", ".webp") : ""));

            if (userPrivateConfigPO != null && 1 == userPrivateConfigPO.getIsPrivate().intValue()) {
                imgDrawDetlPO.setIsPublish(CommonIntEnum.SHOW_FALSE.getIntValue());
            }
            imgDrawDetlPO.setIsPublish(BIntEnum.IMG_DRAW_IS_PUBLISH_FALSE.getIntValue());
            imgDrawDetlsList.add(imgDrawDetlPO);
            //异步上传图片
            imageService.saveImage(imgDrawDetlPO.getId(), imgDrawDetlPO.getImgUrl());
            //返回前端显示数据
            imgDrawDetlVO.setId(imgDrawDetlPO.getId());
            imgDrawDetlVO.setImgUrl(imgDrawDetlPO.getImgUrl());
            imgDrawDetls.add(imgDrawDetlVO);
            jobImageUrl = imgDrawDetlPO.getImgUrl();
        }
        log.info("持久化存储mj绘图详情信息==imgDrawDetlsList {}", imgDrawDetlsList);
        if (imgDrawDetlService.saveBatch(imgDrawDetlsList)) {
            String optTitleOne = ImgOptModelEnum.getOptTitleOne(imgHistoryVO.getOptAttribute());
            String optTitleTwo = ImgOptModelEnum.getOptTitleTwo(imgHistoryVO.getOptAttribute());
            if (optTitleOne != null) {
                imgHistoryVO.setRemark(optTitleOne.concat(optTitleTwo != null ? optTitleTwo : "").concat("已完成"));
            }
            pushDrawTaskResult(imgHistoryVO, jobImageUrl);
            return imgDrawDetls;
        }
        return new ArrayList<>();
    }

    public void pushDrawTaskRealTime(ImgDrawHistoryVO imgHistoryVO) {
        try {
            JSONObject buildPushObject = new JSONObject();
            buildPushObject.put("type", WebSocketEnum.DRAW_JOB_PUSH.getPushType());
            buildPushObject.put("userId", imgHistoryVO.getUserId());
            buildPushObject.put("object", JSONObject.toJSONString(imgHistoryVO));
            // log.info("====push success: {}", buildPushObject);
            //TODO 校验是否存在websocket实例管理
            if (WebSocketHandler.getSessionNull(String.valueOf(imgHistoryVO.getUserId()))) {
                return;
            }
            //推送到客户端
            WebSocketHandler.sendMessage(imgHistoryVO.getUserId(), buildPushObject.toString());
        } catch (Exception e) {
            log.error("pushTaskMessage= {}", e.getMessage(), e);
        }
    }

    //TODO 推送任务成功或失败通知
    private void pushDrawTaskResult(ImgDrawHistoryVO imgHistoryVO, String jobImageUrl) {
        try {
            SysNotificationPO notificationPO = SysNotificationPO.buildSysNotification(imgHistoryVO.getUserId(), BNotificationEnum.DRAW_NOTIF.getIntValue(),
                    imgHistoryVO.getRemark(), imgHistoryVO.getPrompt(), 1, imgHistoryVO.getId(), jobImageUrl);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type", WebSocketEnum.NOTIFICATION_PUSH.getPushType());
            jsonObject.put("userId", imgHistoryVO.getUserId());
            jsonObject.put("object", JSON.toJSONString(notificationPO));
            YouMengMessage.pushMessage(jsonObject.toString(), userService);
            //TODO 校验是否存在websocket实例管理
            if (WebSocketHandler.getSessionNull(String.valueOf(imgHistoryVO.getUserId()))) {
                return;
            }
            WebSocketHandler.sendMessage(imgHistoryVO.getUserId(), jsonObject.toString());
            log.info("====pushTaskMessage end= {}", jsonObject);
        } catch (Exception e) {
            log.error("pushTaskMessage= {}", e.getMessage(), e);
        }
    }


    @Override
    public void pushMessageActivityPromotion(BMessageObject bMessageObject) {
        if (ActivityUtil.getIsPushNot(bMessageObject.getUserId())) {
            return;
        }

        JSONObject adminActivityPromotionPO = ActivityUtil.getPushContext(bMessageObject.getUserId(), userDDRecordService.getUserIsVip(bMessageObject.getUserId()));
        if (adminActivityPromotionPO != null) {
            adminActivityPromotionPO.put("imgUrl", adminActivityPromotionPO.getString("imgUrl").concat("?" + new Date().getTime()));
            WebSocketHandler.sendMessage(bMessageObject.getUserId(), BMessageSendUtil.getJSONStr(bMessageObject.getUserId(), BMessageSendEnum.ACTIVITY_PUSH, JSONObject.toJSONString(adminActivityPromotionPO)));
            ActivityUtil.setPromotionPushSuccess(bMessageObject.getUserId(), adminActivityPromotionPO);
        }
    }

    @Override
    public void pushSystemMessageActivityPromotion(BMessageObject sysBMessageObject) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", WebSocketEnum.NOTIFICATION_PUSH.getPushType());
        jsonObject.put("object", JSON.toJSONString(sysBMessageObject.getObject()));
        WebSocketHandler.fanoutMessage(jsonObject.toString());
        // 友盟推送服务
        YouMengMessage.pushMessageBroadcast(JSON.toJSONString(sysBMessageObject.getObject()));

    }

}
