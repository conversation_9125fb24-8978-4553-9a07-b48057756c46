package com.nacos.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.business.db.mapper.*;
import com.business.db.model.dto.UserSignDTO;
import com.business.db.model.po.*;
import com.business.db.model.vo.InviteInfoVO;
import com.business.enums.BNotificationEnum;
import com.business.enums.BNotificationTmplEnum;
import com.business.enums.BRedisKeyEnum;
import com.business.utils.BDateUtil;
import com.nacos.auth.JwtNewUtil;
import com.nacos.enums.*;
import com.nacos.exception.IBusinessException;
import com.nacos.model.sign.DailySignTaskDTO;
import com.nacos.model.sign.DailySignTemplateVO;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.ActivityService;
import com.nacos.service.IUserService;
import com.nacos.service.sys.ISysNotificationService;
import com.nacos.utils.DailySignUtil;
import com.nacos.utils.DateUtil;
import com.nacos.utils.NotifiesUtil;
import com.nacos.utils.Utils;
import com.nacos.utils.feishu.FeiShuApiUtil;
import com.nacos.utils.wxpay.SequenceNoUtils;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Log4j2
@Service
public class ActivityServiceImpl implements ActivityService {

    /* 点子余额记录 */
    @Resource
    private UserDDRecordMapper userDDRecordMapper;
    /* 点子变动记录 */
    @Resource
    private FlowRecordMapper flowRecordMapper;
    /* 兑换码信息 */
    @Resource
    private ExchangeRecordMapper exchangeRecordMapper;
    @Resource
    private PasswordConfigMapper passwordConfigMapper;
    @Resource
    private PasswordRecordMapper passwordRecordMapper;
    @Resource
    private UserSignRecordMapper userSignRecordMapper;
    @Resource
    private UserGoodRecordMapper userGoodRecordMapper;
    @Resource
    private UserInviteRecordMapper userInviteRecordMapper;
    @Resource
    private UserAwardSvipRecordMapper userAwardSvipRecordMapper;
    @Resource
    private ISysNotificationService sysNotificationService;
    @Resource
    private DictConfigMapper dictConfigMapper;
    @Resource
    private IUserService userService;
    @Resource
    private UserMapper userMapper;
    @Autowired
    private VipConfigMapper vipConfigMapper;

    /* 支付记录 */
    @Resource
    private PayRecordMapper payRecordMapper;

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Result<String> updateExchange(String code, Long userId) throws IBusinessException {
        //查询兑换码信息是否存在
        ExchangeRecordPO exchangeRecord = exchangeRecordMapper.selectOne(
                new LambdaQueryWrapper<ExchangeRecordPO>().eq(ExchangeRecordPO::getCode, code)
                        .eq(ExchangeRecordPO::getState, ExchangeRecordEnum.STATE_NOT_USED.getIntValue())
                        .ge(ExchangeRecordPO::getExpiryTime, com.nacos.utils.DateUtil.getDateNowShanghai())
        );
        if (ObjectUtil.isNull(exchangeRecord) || exchangeRecord.getUseExpiryDay() < 1) {
            return Result.ERROR("兑换信息已失效");
        }
        exchangeRecord.setUseExpiryTime(DateUtil.getDateAddDays(DateUtil.getDateNowShanghai(), exchangeRecord.getUseExpiryDay()));
        //1 用户点子更新记录
        FlowRecordPO flowRecord = FlowRecordPO.builder()
                .recordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue())
                .num(exchangeRecord.getAccountNum())
                .userId(userId)
                .operateType(FlowRecordEnum.OPERATE_TYPE_ADD_ACTIVITY_EXCHANGE_CODE.getIntValue())
                .remark(FlowRecordEnum.OPERATE_TYPE_ADD_ACTIVITY_EXCHANGE_CODE.getStrValue())
                .build();
        if (flowRecordMapper.insert(flowRecord) < 1) {
            throw new IBusinessException("新增流水失败");
        }
        //2 更新兑换码信息
        exchangeRecord.setState(ExchangeRecordEnum.STATE_USEING.getIntValue());
        exchangeRecord.setUserId(userId);
        if (exchangeRecordMapper.updateById(exchangeRecord) < 1) {
            throw new IBusinessException("兑换码信息更新失败");
        }
        //3 新增用户点子余额信息
        if (userDDRecordMapper.insert(new UserDDRecordPO(
                userId,
                exchangeRecord.getId(),
                UserDDrecordEnum.TYPE_ACTIVITY.getIntValue(),
                UserDDrecordEnum.TYPE_ITEM_ACTIVITY_REDEMPTION_CODE.getIntValue(),
                exchangeRecord.getAccountNum(),
                exchangeRecord.getUseExpiryTime()
        )) < 1) {
            throw new IBusinessException("新增用户点子余额信息失败");
        }
        return Result.SUCCESS(Utils.doubleToString(exchangeRecord.getAccountNum()));
    }


    //.ge(PasswordConfigPO::getExpirationTime, com.nacos.utils.DateUtil.getDateNowShanghai())
    @Transactional(rollbackFor = {Exception.class})
    @Override
    public Result<String> insertPasswordRecord(String code, Long userId) throws IBusinessException {
        PasswordConfigPO passwordConfigPO = passwordConfigMapper.selectOne(
                new LambdaQueryWrapper<PasswordConfigPO>()
                        .eq(PasswordConfigPO::getCodeStr, code.trim())
                        .eq(PasswordConfigPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())
                        .orderByDesc(PasswordConfigPO::getExpirationTime)
                        .last("limit 0,1"));
        if (ObjectUtil.isNull(passwordConfigPO)) {
            return Result.ERROR("口令错误请重试");
        }
        if (passwordConfigPO.getExpirationTime() != null && !com.nacos.utils.DateUtil.dateComparisonSize(passwordConfigPO.getExpirationTime())) {
            return Result.ERROR("口令已过期");
        }
        PasswordRecordPO passwordRecordPO = passwordRecordMapper.selectOne(new LambdaQueryWrapper<PasswordRecordPO>().eq(PasswordRecordPO::getUserId, userId).eq(PasswordRecordPO::getConfigId, passwordConfigPO.getId()));
        if (ObjectUtil.isNotNull(passwordRecordPO)) {
            return Result.ERROR("您已兑换过该口令");
        }
        PasswordRecordPO passwordRecordPOInsert = new PasswordRecordPO();
        passwordRecordPOInsert.setUserId(userId);
        passwordRecordPOInsert.setConfigId(passwordConfigPO.getId());
        passwordRecordPOInsert.setExchangeTime(com.nacos.utils.DateUtil.getDateNowShanghai());
        passwordRecordPOInsert.setExchangeQuantity(Utils.getPasswordExchangeQuantity(passwordConfigPO.getDdMaxQuantity(), passwordConfigPO.getDdMinQuantity()));
        //1新增口令兑换记录
        if (passwordRecordMapper.insert(passwordRecordPOInsert) < 1) {
            throw new IBusinessException("新增口令兑换记录失败");
        }
        //2新增用户点子余额信息
        if (userDDRecordMapper.insert(new UserDDRecordPO(
                userId,
                passwordRecordPOInsert.getId(),
                UserDDrecordEnum.TYPE_ACTIVITY.getIntValue(),
                UserDDrecordEnum.TYPE_ITEM_ACTIVITY_PASSWORD.getIntValue(),
                passwordRecordPOInsert.getExchangeQuantity()
        )) < 1) {
            throw new IBusinessException("新增用户点子余额信息失败");
        }
        //3、新增点子记录信息表
        FlowRecordPO flowRecordPO = new FlowRecordPO();
        flowRecordPO.setUserId(userId);
        flowRecordPO.setRecordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue());
        flowRecordPO.setNum(passwordRecordPOInsert.getExchangeQuantity());
        flowRecordPO.setOperateType(FlowRecordEnum.OPERATE_TYPE_ADD_TASK_PASSWORD_EXCHANGE.getIntValue());
        flowRecordPO.setRemark(FlowRecordEnum.OPERATE_TYPE_ADD_TASK_PASSWORD_EXCHANGE.getStrValue());
        if (flowRecordMapper.insert(flowRecordPO) < 1) {
            return Result.ERROR("新增点子记录信息失败");
        }
        return Result.SUCCESS(Utils.doubleToString(passwordRecordPOInsert.getExchangeQuantity()));
    }

    @Override
    public Result<InviteInfoVO> getInviteInfo(Long userId) {
        //获取邀请赠送点子数量
        InviteInfoVO inviteInfoVO = new InviteInfoVO(userId);
        DictConfigPO dictConfigPO = dictConfigMapper.selectOne(
                new LambdaQueryWrapper<DictConfigPO>()
                        .eq(DictConfigPO::getDictType, DictConfigEnum.DD_ADD_TASK_GIVE_INVITE.getDictType())
                        .eq(DictConfigPO::getDictKey, DictConfigEnum.DD_ADD_TASK_GIVE_INVITE.getDictKey())
                        .eq(DictConfigPO::getIsUse, DictConfigEnum.getIsUseTrue()));
        if (ObjectUtil.isNotNull(dictConfigPO)) {
            inviteInfoVO.setDdGiveQuantity(Double.valueOf(dictConfigPO.getDictValue()));
        }

        UserPO userPO = userMapper.selectById(userId);
        if (ObjectUtil.isNull(userPO)) {
            return Result.ERROR("用户不存在");
        }
        DictConfigPO dictConfigPOCodeUrl = dictConfigMapper.selectOne(
                new LambdaQueryWrapper<DictConfigPO>()
                        .eq(DictConfigPO::getDictType, DictConfigEnum.DD_INVITE_URL.getDictType())
                        .eq(DictConfigPO::getDictKey, DictConfigEnum.DD_INVITE_URL.getDictKey())
                        .eq(DictConfigPO::getIsUse, DictConfigEnum.getIsUseTrue()));
        if (ObjectUtil.isNotNull(dictConfigPO)) {
            inviteInfoVO.setCodeUrl(dictConfigPOCodeUrl.getDictValue() + userPO.getInvitationCode());
            inviteInfoVO.setCode(userPO.getInvitationCode());
        }
        int manAllQuantity = 0;
        int manQuantity = 0;
        List<UserPO> userPOList = userMapper.selectList(new LambdaQueryWrapper<UserPO>().eq(UserPO::getInviterCode, userPO.getInvitationCode()));
        if (ObjectUtil.isNotNull(userPOList)) {
            for (UserPO po : userPOList) {
                manAllQuantity++;
                if (po.getMobile() != null && !po.getMobile().isEmpty()) {
                    manQuantity++;
                }
            }
        }
        Long countSvip = payRecordMapper.selectCount(new LambdaQueryWrapper<PayRecordPO>()
                .eq(PayRecordPO::getUserId, userId)
                .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                .eq(PayRecordPO::getType, PayRecordEnum.TYPE_ITEM_PAY_ACTIVITY_SVIP.getIntValue()));
        inviteInfoVO.setGiveSvipAllNum(String.valueOf(countSvip));
        inviteInfoVO.setManAllQuantity(manAllQuantity);
        inviteInfoVO.setManQuantity(manQuantity);
        return Result.SUCCESS(inviteInfoVO);
    }

    @Override
    public Result<DailySignTemplateVO> getDailySignTemplate(Long languageTagId, String version) throws IBusinessException {
        Long userId = null;
        if (JwtNewUtil.verifyRequestToken()) {
            userId = JwtNewUtil.getUserId();
        }

        long inviteCount = 0;
        Integer isReceive = null;
        boolean loginFlag = false;
        List<UserSignRecordPO> userSignRecordList = new ArrayList<>();
        if (ObjectUtil.isNotNull(userId)) {
            loginFlag = true;
            userSignRecordList = userSignRecordMapper.selectList(new LambdaQueryWrapper<UserSignRecordPO>()
                    .eq(UserSignRecordPO::getUserId, userId)
                    .ge(UserSignRecordPO::getCreateTime, BDateUtil.getMondayOFTheDay())
                    .le(UserSignRecordPO::getCreateTime, BDateUtil.getSundayOFTheDay())
                    .orderByAsc(UserSignRecordPO::getCreateTime)
            );
            inviteCount = userInviteRecordMapper.selectCount(userId, BDateUtil.getFirstDayOfMonth(), BDateUtil.getLastDayOfMonth());
            isReceive = userAwardSvipRecordMapper.selectState(userId, BDateUtil.getFirstDayOfMonth(), BDateUtil.getLastDayOfMonth());
            if (inviteCount >= DailySignUtil.INVITE_COUNT && version != null && !version.isEmpty() && isReceive == null) {
                userAwardSvipRecordMapper.insert(new UserAwardSvipRecordPO(userId, 0));
                isReceive = 0;
            }
        }
        DailySignTemplateVO dailySignDataVO = new DailySignTemplateVO();
        dailySignDataVO.setContSign(DailySignUtil.CONT_SIGN_IN.replace("${days}", String.valueOf(countConsecutiveRecords(userSignRecordList))));
        dailySignDataVO.setContSignExplain(DailySignUtil.CONT_SIGN_EXPLAIN);
        dailySignDataVO.setSignPeriodDTOs(DailySignUtil.getDailySignPeriodTemplate(userSignRecordList));
        if (version == null || version.isEmpty()) {
            // 兼容旧版不在维护
            dailySignDataVO.setDailyTaskDTOs(DailySignUtil.getDailyTaskTemplate(inviteCount));
        } else {
            // 新版逻辑执行次方法
            dailySignDataVO.setDailyTaskDTOs(DailySignUtil.getDailyTaskTemplate(inviteCount, isReceive));
        }

        int state = 0;
        UserGoodRecordPO googRecordPO = userGoodRecordMapper.selectOne(new LambdaQueryWrapper<UserGoodRecordPO>()
                .eq(UserGoodRecordPO::getUserId, userId)
                .eq(UserGoodRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue()));
        if (googRecordPO != null) {
            state = googRecordPO.getState();
        }
        dailySignDataVO.setNoviceTaskDTOs(DailySignUtil.getNoviceTaskTemplate(loginFlag, state));
        return Result.SUCCESS(dailySignDataVO);
    }

    @Override
    public Result<Map<String, Object>> receiveAwardSvip(Long languageTagId) throws IBusinessException {
        if (userAwardSvipRecordMapper.update(null, new LambdaUpdateWrapper<UserAwardSvipRecordPO>()
                .eq(UserAwardSvipRecordPO::getUserId, JwtNewUtil.getUserId())
                .set(UserAwardSvipRecordPO::getState, 1)) < 1) {
            return Result.ERROR("领取失败");
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("prompt", new DailySignTaskDTO(
                "奖励领取成功",
                "月度基础SVIP",
                "好的",
                DailySignTaskEnum.CLICKABLE_BUTTON.getCode(),
                1,
                null));
        resultMap.put("stateName", "已领取");
        return Result.SUCCESS(resultMap);
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public Result<Object> insertDailySign(UserSignDTO userSignDTO) throws IBusinessException {
        if (userSignDTO.getSignCode() == null || userSignDTO.getSignCode().intValue() == -1) {
            return Result.ERROR("签到人数较多，请等待一秒再重试");
        }
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        if (userSignRecordMapper.selectCount(new LambdaQueryWrapper<UserSignRecordPO>()
                .eq(UserSignRecordPO::getUserId, userSignDTO.getUserId())
                .between(UserSignRecordPO::getCreateTime, startOfDay, startOfDay.plusDays(1).minusSeconds(1))) > 0) {
            return Result.ERROR("今日已签到");
        }

        UserSignRecordPO userSignRecordPOInsert = new UserSignRecordPO();
        userSignRecordPOInsert.setUserId(userSignDTO.getUserId());
        userSignRecordPOInsert.setSignDays(userSignDTO.getSignCode());
        userSignRecordPOInsert.setGainQua(Utils.getPasswordExchangeQuantity(8.00, 2.00));
        //1新增口令兑换记录
        if (userSignRecordMapper.insert(userSignRecordPOInsert) < 1) {
            throw new IBusinessException("新增每日签到记录失败");
        }
        //2新增用户点子余额信息
        if (userDDRecordMapper.insert(new UserDDRecordPO(
                userSignDTO.getUserId(),
                userSignRecordPOInsert.getId(), //sourceId 原操作id
                UserDDrecordEnum.TYPE_TASK.getIntValue(),
                UserDDrecordEnum.TYPE_ITEM_TASK_GIVE_DAILY_SIGN.getIntValue(),
                userSignRecordPOInsert.getGainQua() //获得点数
        )) < 1) {
            throw new IBusinessException("新增用户点子余额信息失败");
        }
        //3、新增点子记录信息表
        FlowRecordPO flowRecordPO = new FlowRecordPO();
        flowRecordPO.setUserId(userSignDTO.getUserId());
        flowRecordPO.setRecordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue());
        flowRecordPO.setNum(userSignRecordPOInsert.getGainQua()); //获得点数
        flowRecordPO.setOperateType(FlowRecordEnum.OPERATE_TYPE_ADD_DAILY_SIGN.getIntValue());
        flowRecordPO.setRemark(FlowRecordEnum.OPERATE_TYPE_ADD_DAILY_SIGN.getStrValue());
        if (flowRecordMapper.insert(flowRecordPO) < 1) {
            return Result.ERROR("新增点子记录信息失败");
        }

        List<UserSignRecordPO> userSignRecordList = userSignRecordMapper.selectList(new LambdaQueryWrapper<UserSignRecordPO>()
                .eq(UserSignRecordPO::getUserId, userSignDTO.getUserId())
                .ge(UserSignRecordPO::getCreateTime, BDateUtil.getMondayOFTheDay())
                .le(UserSignRecordPO::getCreateTime, BDateUtil.getSundayOFTheDay())
                .orderByAsc(UserSignRecordPO::getCreateTime)
        );
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("gainQua", Utils.doubleToString(userSignRecordPOInsert.getGainQua()));
        resultMap.put("contSign", DailySignUtil.CONT_SIGN_IN.replace("${days}", String.valueOf(countConsecutiveRecords(userSignRecordList))));
        return Result.SUCCESS(resultMap);
    }

    // 统计连续id的数据条数
    private static int countConsecutiveRecords(List<UserSignRecordPO> userSignRecordList) {
        int count = 0;
        int consecutiveId = -1; // 初始值设为不可能的id值
        if (userSignRecordList == null || userSignRecordList.isEmpty()) {
            return 0;
        }
        for (UserSignRecordPO userSignRecord : userSignRecordList) {
            int currentId = userSignRecord.getSignDays();
            if (currentId == consecutiveId + 1) {
                count++;
            } else {
                count = 1; // 如果不连续，则重新开始计数
            }
            consecutiveId = currentId; // 更新连续id值
        }
        return count;
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public Result<Boolean> updatePromptSign(Integer promptSign) throws IBusinessException {
        Long userId = JwtNewUtil.getUserId();
        if (userMapper.update(null, new LambdaUpdateWrapper<UserPO>()
                .eq(UserPO::getId, userId)
                .set(UserPO::getPromptSign,
                        promptSign.intValue() == CommonIntEnum.DELETED_FALSE.getIntValue().intValue()
                                ? CommonIntEnum.DELETED_TRUE.getIntValue().intValue()
                                : CommonIntEnum.DELETED_FALSE.getIntValue())) > 0
        ) {
            return Result.SUCCESS(true);
        }
        return Result.ERROR("更新提示用户签到失败");
    }

    @Override
    public Result<Object> firstGoodReward(MultipartFile file) {
        try {
            UserPO userPO = userMapper.getByUserId(JwtNewUtil.getUserId());
            if (ObjectUtil.isNull(userPO)) {
                return Result.ERROR("登录已过期");
            }
            if (userGoodRecordMapper.selectCount(new LambdaUpdateWrapper<UserGoodRecordPO>()
                    .eq(UserGoodRecordPO::getUserId, userPO.getId())
                    .eq(UserGoodRecordPO::getDeleted, CommonIntEnum.DELETED_FALSE.getIntValue())) > 0) {
                return Result.ERROR("已完成该任务");
            }
            if (userGoodRecordMapper.insert(
                    new UserGoodRecordPO(userPO.getId(), DailySignTaskEnum.AUDIT_PROGRESS_STATE.getCode(), DateUtil.getDateNowShanghai(), null)) > 0) {
                String accessToken = RedisUtil.getValue(BRedisKeyEnum.FEISHU_ACCESSTOKEN_TOKEN.getKey());
                if (accessToken == null) {
                    return Result.ERROR("提交好评失败");
                }

                String getFileToken = FeiShuApiUtil.getFileToken(file, accessToken);
                if (getFileToken == null || !getFileToken.contains("data") || !getFileToken.contains("file_token")) {
                    return Result.ERROR("提交好评失败");
                }
                JSONObject jsonObject = JSON.parseObject(getFileToken);
                JSONObject dataObject = jsonObject.getJSONObject("data");
                String fileToken = dataObject.getString("file_token");

                // 向飞书文档添加数据
                String addTableRecords = FeiShuApiUtil.addTableRecords(userPO.getId(), userPO.getName(), accessToken, fileToken);
                if (addTableRecords == null) {
                    return Result.ERROR("提交好评失败");
                }
                return Result.SUCCESS(new DailySignTaskDTO(null, null,
                        DailySignTaskEnum.AUDIT_PROGRESS_STATE.getDescription(),
                        DailySignTaskEnum.NO_CLICKABLE_BUTTON.getCode(), null, null));
            }
        } catch (Exception e) {
            log.error("提交好评失败", e);
        }
        return Result.ERROR("提交好评失败");
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public Result<Object> goodAuditConfirm(Long userId) {
        if (userGoodRecordMapper.update(null, new LambdaUpdateWrapper<UserGoodRecordPO>()
                .eq(UserGoodRecordPO::getUserId, userId)
                .set(UserGoodRecordPO::getState, DailySignTaskEnum.AUDIT_APPROVE_STATE.getCode())) < 1) {
            return Result.ERROR("好评审核失败", DailySignTaskEnum.AUDIT_FAILED_STATE.getNote());
        }
        //2新增用户点子余额信息
        if (userDDRecordMapper.insert(new UserDDRecordPO(
                userId,
                null, //sourceId 原操作id
                UserDDrecordEnum.TYPE_TASK.getIntValue(),
                UserDDrecordEnum.TYPE_ITEM_TASK_GIVE_FIRST_GOOG.getIntValue(),
                10D //获得点数
        )) < 1) {
            return Result.ERROR("新增用户点子余额信息失败", DailySignTaskEnum.AUDIT_FAILED_STATE.getNote());
        }
        //3、新增点子记录信息表
        FlowRecordPO flowRecordPO = new FlowRecordPO();
        flowRecordPO.setUserId(userId);
        flowRecordPO.setRecordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue());
        flowRecordPO.setNum(10D); //获得点数
        flowRecordPO.setOperateType(FlowRecordEnum.OPERATE_TYPE_ADD_FIRST_GOOG.getIntValue());
        flowRecordPO.setRemark(FlowRecordEnum.OPERATE_TYPE_ADD_FIRST_GOOG.getStrValue());
        if (flowRecordMapper.insert(flowRecordPO) < 1) {
            return Result.ERROR("新增点子记录信息失败", DailySignTaskEnum.AUDIT_FAILED_STATE.getNote());
        }
        // 4、新增系统通知, 推送
        SysNotificationPO notificationPO = SysNotificationPO.buildSysNotification(userId, BNotificationEnum.OTHER_NOTIF.getIntValue(),
                BNotificationTmplEnum.GOOD_SUCCESS_NOTIF_TEMPLATE.getStrTitle(), BNotificationTmplEnum.GOOD_SUCCESS_NOTIF_TEMPLATE.getStrContent(),
                CommonIntEnum.SHOW_FALSE.getIntValue(), null, null);
        if (sysNotificationService.save(notificationPO)) {
            NotifiesUtil.sendNotification(WebSocketEnum.NOTIFICATION_PUSH.getPushType(), userId, notificationPO, userService);
            return Result.SUCCESS("提交好评审核通过", DailySignTaskEnum.AUDIT_APPROVE_STATE.getNote());
        }
        return Result.ERROR("好评审核失败", DailySignTaskEnum.AUDIT_FAILED_STATE.getNote());
    }

    @Override
    public Result<Object> goodAuditRefusal(Long userId) {
        if (userGoodRecordMapper.delete(new LambdaUpdateWrapper<UserGoodRecordPO>()
                .eq(UserGoodRecordPO::getUserId, userId)) < 1) {
            return Result.ERROR("好评审核失败", DailySignTaskEnum.AUDIT_FAILED_STATE.getNote());
        }
        // 1、新增系统通知, 推送
        SysNotificationPO notificationPO = SysNotificationPO.buildSysNotification(userId, BNotificationEnum.OTHER_NOTIF.getIntValue(),
                BNotificationTmplEnum.GOOD_REFUSAL_NOTIF_TEMPLATE.getStrTitle(), BNotificationTmplEnum.GOOD_REFUSAL_NOTIF_TEMPLATE.getStrContent(),
                CommonIntEnum.SHOW_FALSE.getIntValue(), null, null);
        if (sysNotificationService.save(notificationPO)) {
            NotifiesUtil.sendNotification(WebSocketEnum.NOTIFICATION_PUSH.getPushType(), userId, notificationPO, userService);
            return Result.SUCCESS("提交好评审核已驳回", DailySignTaskEnum.AUDIT_REFUSAL_STATE.getNote());
        }
        return Result.ERROR("好评审核失败", DailySignTaskEnum.AUDIT_FAILED_STATE.getNote());
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public boolean saveGiveSVip(Long userId) throws IBusinessException {
        //校验用户id是否准确
        UserPO userPO = userMapper.selectById(userId);
        if (userPO == null) {
            return false;
        }
        VipConfigPO vipConfigPO = vipConfigMapper.selectById(21);//查询固定最小Svip数量
        if (vipConfigPO == null) {
            return false;
        }

        //赠送会员接口
        PayRecordPO payRecordPO = new PayRecordPO();
        payRecordPO.setUserId(userId);
        payRecordPO.setOrderNo(SequenceNoUtils.generateNo("01"));
        payRecordPO.setVipConfigId(vipConfigPO.getId());
        payRecordPO.setCreateTime(cn.hutool.core.date.DateUtil.date());
        payRecordPO.setDiscountRatio(1.0);
        payRecordPO.setState(PayRecordEnum.STATE_PAY_SUCCESS.getIntValue());
        payRecordPO.setOrderType(PayRecordEnum.ORDER_TYPE_SVIP.getIntValue());
        payRecordPO.setTotal(new BigDecimal(vipConfigPO.getMonth().toString()).multiply(new BigDecimal(vipConfigPO.getCurrentMonthQuantity().toString())).doubleValue());
        payRecordPO.setType(PayRecordEnum.TYPE_ITEM_PAY_ACTIVITY_SVIP.getIntValue());
        payRecordPO.setMonth(vipConfigPO.getMonth());
        payRecordPO.setMonthQuantity(vipConfigPO.getCurrentMonthQuantity());
        payRecordPO.setAmount(vipConfigPO.getCurrentPrice().multiply(new BigDecimal("1")));
        /*LambdaQueryWrapper<PayRecordPO> queryWrapper = new LambdaQueryWrapper<PayRecordPO>()
                .eq(PayRecordPO::getState, PayRecordEnum.STATE_PAY_SUCCESS.getIntValue())
                .ne(PayRecordPO::getVipConfigId, 8)//8为体验会员直接过滤掉
                .eq(PayRecordPO::getUserId, userId)
                .ne(PayRecordPO::getOrderType, PayRecordEnum.ORDER_TYPE_JYB.getIntValue())
                .ge(PayRecordPO::getExpirationTime, com.nacos.utils.DateUtil.getDateNowShanghai())
                .orderByDesc(PayRecordPO::getExpirationTime).last("LIMIT 0,1");*/
        Date date = BDateUtil.getDateNowShanghai();
        /*PayRecordPO payRecordPOTime = payRecordMapper.selectOne(queryWrapper);*/
        boolean isGiveSvip = false;
        /*if (payRecordPOTime != null && payRecordPOTime.getExpirationTime()!= null){
            date = payRecordPOTime.getExpirationTime();
            isGiveSvip = true;
        }*/

        log.info("date:{}", date);
        Date expirationTime = BDateUtil.getDateAddMonth(date, vipConfigPO.getMonth());
        payRecordPO.setTaskTime(expirationTime);
        payRecordPO.setExpirationTime(expirationTime);
        payRecordPO.setRemark("会员邀请赠送SVIP");
        if (payRecordMapper.insert(payRecordPO) < 1) {
            return false;
        }
        if (!isGiveSvip) {
            //创建点子记录表
            UserDDRecordPO userDDRecordPO = new UserDDRecordPO();
            userDDRecordPO.setUserId(payRecordPO.getUserId());
            userDDRecordPO.setTotal(payRecordPO.getMonthQuantity());
            userDDRecordPO.setType(UserDDrecordEnum.TYPE_PAY.getIntValue());
            userDDRecordPO.setTypeItem(UserDDrecordEnum.TYPE_ITEM_PAY_SVIP.getIntValue());
            userDDRecordPO.setTotalUsage((double) 0);
            userDDRecordPO.setExpirationTime(payRecordPO.getExpirationTime());
            userDDRecordPO.setRemark("邀请用户赠送SVIP");
            //点点记录创建
            if (userDDRecordMapper.insert(userDDRecordPO) < 1) {
                return false;
            }

            //流水登记
            FlowRecordPO flowRecordPO = new FlowRecordPO();
            flowRecordPO.setUserId(payRecordPO.getUserId());
            flowRecordPO.setRecordType(FlowRecordEnum.RECORD_TYPE_ADD.getIntValue());
            flowRecordPO.setNum(payRecordPO.getMonthQuantity());
            flowRecordPO.setOperateType(FlowRecordEnum.OPERATE_TYPE_ADD_GIVE_SVIP.getIntValue());
            flowRecordPO.setRemark(FlowRecordEnum.OPERATE_TYPE_ADD_GIVE_SVIP.getStrValue());
            if (flowRecordMapper.insert(flowRecordPO) < 1) {
                throw new IBusinessException("创建点子登记信息失败");
            }

        }
        return true;
    }

}
