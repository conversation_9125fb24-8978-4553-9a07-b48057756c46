package com.nacos.service;

import com.business.db.model.dto.AppVersionDTO;
import com.business.db.model.po.AppVersionConfigPO;
import com.business.db.model.vo.SvipNotesNoticeVO;
import com.business.db.model.vo.UserRightsConfigShowVO;
import com.nacos.exception.IBusinessException;
import com.nacos.model.RuleConfigVO;
import com.nacos.result.Result;

import java.util.List;
import java.util.Map;

public interface ShowService {

    /**
     * 获取app版本信息
     * @param appVersionDTO 移动app版本信息提交
     * @return app版本信息返回
     */
    Result<AppVersionConfigPO> getAppVersion(AppVersionDTO appVersionDTO);

    Result<RuleConfigVO> deductDzConf();

    //是否展示通知类型
    Result<SvipNotesNoticeVO> svipNotesNotice() throws IBusinessException;

    Result<Map<String, Object>> longMemberRights(String activityType) throws IBusinessException;

    Result<Object> configCraftsmanship();
}
