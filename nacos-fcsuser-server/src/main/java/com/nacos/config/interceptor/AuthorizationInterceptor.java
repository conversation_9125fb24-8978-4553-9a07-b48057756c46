package com.nacos.config.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.business.db.model.po.UserPO;
import com.business.enums.BRedisExpireTimeEnum;
import com.business.enums.BRedisKeyEnum;
import com.nacos.config.CommonConst;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.IUserService;
import com.nacos.utils.JwtUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@Component
public class  AuthorizationInterceptor implements HandlerInterceptor {

    @Lazy
    @Resource
    private IUserService iUserService;

    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse httpServletResponse, @NotNull Object handler) throws IOException {
        // 定义匹配数字的正则表达式
        String numberRegex = ".*\\d.*";
        String servletPath = request.getRequestURI();   //判断token是否存在
        log.info("请求URI: {}", servletPath);

        int secondSlashIndex = servletPath.indexOf('/', servletPath.indexOf('/') + 1);
        if (secondSlashIndex != -1) {
            servletPath = servletPath.substring(secondSlashIndex);
            int lastSlashIndex = servletPath.lastIndexOf('/');
            if (lastSlashIndex != -1 && Pattern.compile(numberRegex).matcher(servletPath).matches()) {
                servletPath = servletPath.substring(0, lastSlashIndex + 1);
                log.info("====++++====截取结果：{}", servletPath);
            }
        }

//        log.info("请求接口地址：{}", servletPath);

        String[] targetStrings = {"/vip/rechargeVip/", "/vip/rechargeJyb/"};
        if (Boolean.parseBoolean(RedisUtil.getValue(CommonConst.REDIS_KEY_APPLE_AUDIT_SWITCH_APP))
                && containsString(targetStrings, servletPath)
                && StringUtils.isEmpty(request.getHeader("token"))) {
            return true;
        }
        if(CommandLineInitResource.urlList != null && !CommandLineInitResource.urlList.isEmpty() && !CommandLineInitResource.urlList.contains(servletPath)){
            returnResult(httpServletResponse);
            return false;
        }
        String handlerToken = request.getHeader("token");
        if (StringUtils.isEmpty(handlerToken)) {
            returnResult(httpServletResponse);
            return false;
        }
        //注解于2023-12-18
        //String md5Token = SecureUtil.md5(handlerToken);
        String token = "";
        Long userId = JwtUtil.getUserId();
        //Long userId = JwtUtil.getUserIdByToken(handlerToken);
        String md5Token = SecureUtil.md5(String.valueOf(userId));
        if (handlerToken.contains("app&")) {
            handlerToken = handlerToken.substring(4);
            token = RedisUtil.getValue(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.REDIS_KEY_PREFIX_TOKEN_APP,md5Token));

        } else {
            token = RedisUtil.getValue(BRedisKeyEnum.getAddKeyStr(BRedisKeyEnum.REDIS_KEY_PREFIX_TOKEN,md5Token));
        }
        if (StringUtils.isEmpty(token) || !JwtUtil.verifierToken(token)) {
            returnResult(httpServletResponse);
            return false;
        }
        if (!token.equals(handlerToken)) {
            returnResult(httpServletResponse);
            return false;
        }

        //注解开始于2023-9-25-----------------------------------------------------------------------------------------
        if(ObjectUtil.isNotNull(JwtUtil.getType()) && JwtUtil.getType() == 0 && servletPath.contains("sys")){
            returnResult(httpServletResponse);
            return false;
        }
        //注解结束于2023-9-25-----------------------------------------------------------------------------------------
        //如果是手机号登录则永久登录
        //如果是没有手机号，则登录1小时后无操作退出
        //User user = iUserService.getById(JwtUtil.getUserId());
        UserPO user = iUserService.getByFromUser(null, null, null, JwtUtil.getUserId());
        if(ObjectUtil.isNull(user) || user.getDeleted()==1){
            returnResult(httpServletResponse);
            return false;
        }
        if(StringUtils.isBlank(user.getMobile()) && JwtUtil.getType() == 0){ //注解于2023-9-25  && JwtUtil.getType() == 0
            RedisUtil.setKeyExpire(CommonConst.REDIS_KEY_PREFIX_TOKEN + md5Token, BRedisExpireTimeEnum.TOKEN_EXPIRE_TIME.getValue(), BRedisExpireTimeEnum.TOKEN_EXPIRE_TIME.getTimeUnit());
        }
        //距离过期5分钟加入token续命逻辑
//        Calendar c = Calendar.getInstance();
//        c.add(Calendar.MINUTE,-5);
//        if(JwtUtil.getRemainingTime(token, c.getTime())){
//            User user = iUserService.getById(JwtUtil.getUserId());
//            token = JwtUtil.createToken(user);
//            RedisUtil.setObject(CommonConst.REDIS_KEY_PREFIX_TOKEN + md5Token, token);
//        }
//        RedisUtil.expire(CommonConst.REDIS_KEY_PREFIX_TOKEN + md5Token, CommonConst.TOKEN_EXPIRE_TIME, TimeUnit.MINUTES);
        return true;
    }

    @Override
    public void postHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler,
                           ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) {

    }

    private void returnResult(HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=UTF-8");
        PrintWriter writer = response.getWriter();
        writer.print(JSONObject.toJSONString(Result.ERROR_TOKEN()));
        writer.flush();
        writer.close();
    }

    private static boolean containsString(String[] array, String target) {
        for (String element : array) {
            // Check for equality
            if (element.equals(target)) {
                return true; // Found a match
            }
        }
        return false; // No match found
    }
}
