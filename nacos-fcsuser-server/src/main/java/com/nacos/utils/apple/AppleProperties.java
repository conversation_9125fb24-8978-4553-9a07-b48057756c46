package com.nacos.utils.apple;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AppleProperties {

    public static String keyId;
    @Value(value = "${apple.keyId}")
    public void setKeyId(String keyId) {
        AppleProperties.keyId = keyId;
    }

    public static String teamId;

    @Value("${apple.teamId}")
    public void setTeamId(String teamId) {
        AppleProperties.teamId = teamId;
    }

    public static String audience;

    @Value("${apple.audience}")
    public void setAudience(String audience) {
        AppleProperties.audience = audience;
    }

    public static String clientId;

    @Value("${apple.clientId}")
    public void setClientId(String clientId) {
        AppleProperties.clientId = clientId;
    }

    public static String clientIdApp;

    @Value("${apple.clientIdApp}")
    public void setClientIdApp(String clientIdApp) {
        AppleProperties.clientIdApp = clientIdApp;
    }

    public static String grantType;

    @Value("${apple.grantType}")
    public void setGrantType(String grantType) {
        AppleProperties.grantType = grantType;
    }

    public static String tokenEndpoint;

    @Value("${apple.tokenEndpoint}")
    public void setTokenEndpoint(String tokenEndpoint) {
        AppleProperties.tokenEndpoint = tokenEndpoint;
    }

}
