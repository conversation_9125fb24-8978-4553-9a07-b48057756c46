package com.nacos.utils.wxpay.model;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "微信支付app下单参数实体", description = "微信支付app下单参数实体")
public class AppPlaceOrderRO {

    @Schema(name = "应用ID", description = "微信开放平台审核通过的应用APPID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appid;

    @Schema(name = "商户号", description = "微信支付分配的商户号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mchid;

    @Schema(name = "商品描述", description = "商品简单描述,该字段须严格按照规范传递", requiredMode = Schema.RequiredMode.REQUIRED)
    private String description;

    @Schema(name = "商户订单号", description = "商户系统内部订单号,要求32个字符内、且在同一个商户号下唯一", requiredMode = Schema.RequiredMode.REQUIRED)
    private String out_trade_no;

    @Schema(name = "交易结束时间", description = "订单失效时间,遵循ISO 8601标准格式", requiredMode = Schema.RequiredMode.REQUIRED)
    private String time_expire;

    @Schema(name = "自定义数据", description = "自定义数据,在查询API和支付通知中原样返回,可作为自定义参数使用")
    private String attach;

    @Schema(name = "通知URL", description = "接收微信支付异步通知回调地址,通知url必须为直接可访问的url,不能携带参数。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String notifyUrl;

    @Schema(name = "商品标记：待定", description = "商品标记,代金券或立减优惠功能的参数", requiredMode = Schema.RequiredMode.REQUIRED)
    private String goodsTag;

    @Schema(name = "支持开票:待定", description = "指定是否支持开具发票", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean supportFapiao;

    @Data
    @Schema(name = "订单金额信息", description = "订单金额信息")
    public static class Amount {

        @Schema(name = "总金额", description = "订单总金额,单位为分", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer total;

        @Schema(name = "币种", description = "货币类型,符合ISO 4217标准的三位字母代码,默认人民币:CNY", requiredMode = Schema.RequiredMode.REQUIRED)
        private String currency;

    }

}
