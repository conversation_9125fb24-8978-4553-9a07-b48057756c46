package com.nacos.utils.wx;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.nacos.tool.wx.WXHttpUtil;
import com.nacos.utils.wx.kfpt.WxKfptProperties;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.log4j.Log4j2;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Log4j2
public class WXLoginUtil {

    //web 微信扫码登陆
    public static WxInfoVo getWxInfoVo(String code){
        try {
            log.info("web登陆请求1 ==》code:{}",code);
            if (code == null){
                return null;
            }
            JSONObject userInfo = WXHttpUtil.getUserInfoByWxLoginAll(code, WxKfptProperties.wxkfptWebAppId, WxKfptProperties.wxkfptWebAppSecret);
            if (userInfo == null) {
                return null;
            }
            log.info("web登陆返回2 ==》code:{}",userInfo);
            return ObjectUtil.isEmpty(userInfo) ? null : new WxInfoVo(
                    userInfo.getString("access_token"),
                    userInfo.getString("openid"),
                    userInfo.getString("unionid"),
                    userInfo.getString("nickname"),
                    userInfo.getString("headimgurl")
            );
        } catch (Exception e) {
            log.error("web 扫码登陆异常 {}",e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取微信登录的参数
     * @param type 类型：登陆注册、绑定
     * @param userId 用户ID （绑定时必传）
     */
    public static Map<String, Object> getWxCodeMap(String type, String userId){
        if (type == null){
            return null;
        }
        Map<String, Object> paramMap = new HashMap<>();
        String redirectUrl = WxKfptProperties.wxkfptWebRedirectUrl;
        if (ObjectUtil.isEmpty(redirectUrl)){
            return null;
        }
        //装载占位符，登陆注册无需userId；绑定微信需要userId
        redirectUrl = String.format(redirectUrl, type, userId==null?"0":userId);
        paramMap.put("appid", WxKfptProperties.wxkfptWebAppId);
        paramMap.put("scope", "snsapi_login");
        paramMap.put("redirect_uri", URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8));
        paramMap.put("state", IdWorker.getId()+"");
        return paramMap;
    }


    //app拉取登陆操作信息
    public static WxInfoVo getAppInfoVo(HttpServletRequest request) {
        String code = request.getParameter("code");
        try {
            log.info("app登陆请求1 ==》code:{}",code);
            JSONObject objUserInfo = WXHttpUtil.getUserInfoByWxLoginAll(code, WxKfptProperties.wxkfptAppId, WxKfptProperties.wxkfptAppSecret);
            if (objUserInfo == null){
                return null;
            }
            log.info("app登陆返回2 ==》code:{}",objUserInfo);
            return new WxInfoVo(
                    objUserInfo.getString("access_token"),
                    objUserInfo.getString("openid"),
                    objUserInfo.getString("unionid"),
                    objUserInfo.getString("nickname"),
                    objUserInfo.getString("headimgurl"),
                    request.getParameter("deviceToken")
            );
        } catch (Exception e) {
            log.error("微信app登陆异常 {}",e.getMessage(), e);
            return null;
        }

    }

    public static String getVoToWxToken(WxInfoVo wxInfoVo) {
        try {
            if (wxInfoVo == null){
                return null;
            }
            return encrypt(JSONObject.toJSONString(wxInfoVo), "diandianshejiznb");
        } catch (Exception e) {
            log.error("获取微信token失败 {}",e.getMessage(), e);
            return null;
        }
    }

    public static WxInfoVo getWxTokenToVO(String wxToken) {
        try {
            if (wxToken == null){
                return null;
            }
            return JSONObject.parseObject(decrypt(wxToken, "diandianshejiznb"), WxInfoVo.class);
        } catch (Exception e) {
            log.error("获取微信token失败 {}",e.getMessage(), e);
            return null;
        }
    }

    private static final String ALGORITHM = "AES";

    /**
     * 加密
     * @param input 内容
     * @param key 密钥
     */
    public static String encrypt(String input, String key) {
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(input.getBytes());

            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("加密失败 {}",e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解密
     * @param encryptedInput 内容
     * @param key 密钥
     */
    public static String decrypt(String encryptedInput, String key) {
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedInput));

            return new String(decryptedBytes);
        } catch (Exception e) {
            log.error("解密失败 {}",e.getMessage(), e);
            return null;
        }
    }


}
