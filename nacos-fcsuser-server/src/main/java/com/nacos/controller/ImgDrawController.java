package com.nacos.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.*;
import com.business.db.model.dto.draw.MjStyleConfigQueryDTO;
import com.business.db.model.vo.ImgCommunityVO;
import com.business.db.model.vo.ImgDrawHistoryVO;
import com.business.db.model.vo.ImgGalleryVO;
import com.business.db.model.vo.draw.MjStyleConfigQueryVO;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;
import com.nacos.service.DrawService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.List;


@Tag(name = "绘图接口", description = "绘图接口：通用接口")
@RestController
@RequestMapping("/imgDraw")
@Log4j2
public class ImgDrawController {

    @Resource
    private DrawService drawService;

    @Operation(summary = "图片历史记录")
    @GetMapping(value = "/historyRecord/{languageTagId}",name = "图片历史记录")
    public Result<IPage<ImgDrawHistoryVO>> historyRecord(ImgDrawHistoryDTO imgDrawHistoryDTO, @PathVariable() Long languageTagId) {
        imgDrawHistoryDTO.setLanguageTagId(languageTagId);
        return drawService.historyRecord(imgDrawHistoryDTO);
    }

    @Operation(summary = "灵感社区分页列表")
    @PostMapping(value = "/communityPage",name = "灵感社区分页列表")
    public Result<Page<ImgCommunityVO>> queryHomePage(@Validated @RequestBody CommunityQueryDTO dto) {
        return drawService.queryHomePage(dto);
    }

    @Operation(summary = "图片历史记录VIP 提示")
    @GetMapping(value = "/historyVip/{languageTagId}",name = "图片历史记录VIP 提示")
    public Result<String> historyVip(@PathVariable Integer languageTagId) {
        return drawService.historyVip(languageTagId);
    }

    @Operation(summary = "进行中；排队中数量")
    @GetMapping(value = "/historyRecord/stateQuantity",name = "进行中；排队中数量")
    public Result<Long> historyRecordStateQuantity() {
        return drawService.historyRecordStateQuantity();
    }

    @Operation(summary = "保存到我的画廊接口")
    @GetMapping(value = "/saveToGallery/{imgDetlId}",name = "保存到我的画廊接口")
    public Result<Integer> saveToGallery(@PathVariable Long imgDetlId) {
        return drawService.saveToGallery(imgDetlId);
    }

    @Operation(summary = "绘图记录是否进行公开操作：默认1个开启，全部开启状态")
    @GetMapping(value = "/optIsPublishRecord/{imgDrawRecordId}", name = "是否进行公开")
    public Result<Object> optIsPublishRecord(@PathVariable Long imgDrawRecordId) throws IBusinessException {
        return drawService.optIsPublishRecord(imgDrawRecordId);
    }

    @Operation(summary = "批量绘图记录是否进行公开操作：默认1个开启，全部开启状态")
    @GetMapping(value = "/optIsPublishRecords", name = "是否进行公开")
    public Result<Object> optIsPublishRecords(@RequestParam List<Long> imgDrawDetlIds) throws IBusinessException {
        return drawService.optIsPublishRecords(imgDrawDetlIds);
    }

    @Operation(summary = "绘图记录详情是否进行公开操作：默认1个开启，全部开启状态")
    @GetMapping(value = "/optIsPublishDetl/{imgDrawDetlId}", name = "是否进行公开")
    public Result<Object> optIsPublishDetl(@PathVariable Long imgDrawDetlId) throws IBusinessException {
        return drawService.optIsPublishDetl(imgDrawDetlId);
    }


    @PostMapping(value = "/queryMyGalleryPage",name = "查询我的画廊--个人画廊列:app - version 2.0")
    public Result<Page<ImgGalleryVO>> myGalleryPage(@Validated @RequestBody GalleryQueryDTO dto) {
        return drawService.myGalleryPage(dto);
    }

    @Operation(summary = "根据图片id查找图片详情信息")
    @PostMapping(value = "/queryMyGalleryInfo",name = "根据图片id查找图片详情")
    public Result<ImgCommunityVO> queryGalleryByImgId(@Validated  @RequestBody GalleryInfoQueryDTO dto) {
        return drawService.queryGalleryByImgId(dto);
    }

    @Operation(summary = "查询一条历史记录信息")
    @PostMapping(value = "/aImgDrawHistory",name = "查询一条历史记录信息")
    public Result<ImgDrawHistoryVO> queryAImgDrawDetlVO(@RequestBody ImgDrawInfoDTO dto) {
        return drawService.queryAImgDrawDetlVO(dto);
    }

    @Operation(summary = "拉取mj正在进行生成中的图片信息")
    @GetMapping(value = "/pullMjTaskInfos",name = "拉取mj正在进行生成中的图片信息")
    public Result<Object> pullMjTaskInfos(@RequestParam("mjJobIds") List<String> mjJobIds) throws Exception {
        return drawService.pullMjTaskInfos(mjJobIds);
    }

    @Operation(summary = "获取风格分类列表")
    @GetMapping(value = "/styleClassifyList/{languageTagId}",name = "获取风格分类列表")
    public Result<LinkedHashMap<String, Object>> getStyleClassifyList(@PathVariable Integer languageTagId) throws Exception {
        return drawService.getStyleClassifyList(languageTagId);
    }

    @Operation(summary = "查询风格列表分页")
    @GetMapping(value = "/mjStylePage/{languageTagId}",name = "查询风格列表分页")
    public Result<IPage<MjStyleConfigQueryVO>> mjStylePage(MjStyleConfigQueryDTO mjStyleConfigQueryDTO, @PathVariable() Long languageTagId) {
        mjStyleConfigQueryDTO.setLanguageTagId(languageTagId);
        return drawService.mjStylePage(mjStyleConfigQueryDTO);
    }

    @Operation(summary = "指令匹配接口")
    @GetMapping(value = "/instruct/{languageTagId}",name = "指令匹配接口")
    public Result<LinkedHashMap<String, String>> getInstructMatching(@PathVariable Integer languageTagId) throws Exception {
        return drawService.getInstructMatching();
    }

    @Operation(summary = "批量绘图详情删除接口")
    @GetMapping(value = "/imgDrawDetlDeletes", name = "绘图详情删除接口")
    public Result<Object> imgDrawDetlDeletes(@RequestParam List<Long> imgDrawDetlIds) {
        return drawService.imgDrawDetlDeletes(imgDrawDetlIds);
    }

    @PostMapping(value = "/collects",name = "批量画廊收藏： app 2.0")
    public Result<?> collectWorks(@Validated @RequestBody List<ImgCollectDTO> dtoList) {
        return drawService.collectWorks(dtoList);
    }

    @PostMapping(value = "/queryMyCollectPage",name = "查询个人收藏列表：app 2.0")
    public Result<Page<ImgGalleryVO>> queryMyCollectPage(@RequestBody GalleryQueryDTO dto) {
        return drawService.queryCollectPage(dto);
    }

    @Operation(summary = "查询风格列表分页")
    @GetMapping(value = "/mjStyleOnVideo",name = "查询风格列表分页")
    public Result<Object> mjStyleOnVideo() {
        return drawService.mjStyleOnVideo();
    }

}
