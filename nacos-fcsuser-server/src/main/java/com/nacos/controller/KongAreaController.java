package com.nacos.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.DailyPicksDTO;
import com.business.db.model.vo.DailyPicksVO;
import com.business.db.model.vo.FunctionOptConfigVO;
import com.business.db.model.vo.ImgModelConfigVO;
import com.nacos.model.DailyPicksOptVO;
import com.nacos.result.Result;
import com.nacos.service.IKongAreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "金刚区", description = "首页功能区查询")
@Log4j2
@RestController
@RequestMapping("/kong")
public class KongAreaController {


    @Resource
    private IKongAreaService kongAreaService;

    @Operation(summary = "每日精选")
    @PostMapping(value = "/dailyPicks", name = "每日精选分页列表")
    public Result<Page<DailyPicksVO>> dailyPicks(@RequestBody DailyPicksDTO dailyPicksDTO) {
        return kongAreaService.dailyPicksPage(dailyPicksDTO);
    }

    @Operation(summary = "每日精选操作")
    @GetMapping(value = "/dailyPicks/optConfigEditList/{languageTagId}", name = "每日精选操作列表")
    public Result<List<DailyPicksOptVO>> dailyPicksOptConfigEditList(@PathVariable Long languageTagId) {
        return kongAreaService.dailyPicksOptConfigEditList(languageTagId);
    }

    @Operation(summary = "获取功能模块操作列表：列表")
    @GetMapping(value = "/functionOptConfigList/{languageTagId}", name = "获取功能模块操作列表：不同语言展示内容不同")
    public Result<List<FunctionOptConfigVO>> functionOptConfigList(@PathVariable Long languageTagId, Long functionConfigId) {
        return kongAreaService.functionOptConfigList(languageTagId, functionConfigId);
    }

    @Operation(summary = "获取视频模型列表：不同语言展示内容不同")
    @GetMapping(value = "/videoModelConfigList/{languageTagId}", name = "获取视频模型列表")
    public Result<Object> videoModelConfigList(@PathVariable Long languageTagId, @RequestParam(name = "version", required = false) String version) {
        return kongAreaService.videoModelConfigList(languageTagId, version);
    }

    @Operation(summary = "获取绘图模型海报单独配置API：不同语言展示内容不同")
    @GetMapping(value = "/draw/imageModelConfigPoster/{languageTagId}", name = "获取绘图模型海报独立配置")
    public Result<List<ImgModelConfigVO>> imageModelConfigPoster(@PathVariable Long languageTagId, @RequestParam(value = "version", required = false) String version) {
        return kongAreaService.imageModelConfigPoster(languageTagId, version);
    }
}
