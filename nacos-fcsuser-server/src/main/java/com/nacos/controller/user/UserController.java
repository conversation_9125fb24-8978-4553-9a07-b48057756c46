package com.nacos.controller.user;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.business.db.model.dto.FansOrCollectQueryDTO;
import com.business.db.model.dto.FlowRecordDTO;
import com.business.db.model.dto.PrivateQueryDTO;
import com.business.db.model.dto.UpdateUserInfoDTO;
import com.business.db.model.po.FlowRecordPO;
import com.business.db.model.vo.FansOrFollowVO;
import com.business.db.model.vo.UserDDInfoVO;
import com.business.db.model.vo.UserDDRecordVO;
import com.nacos.auth.JwtNewUtil;
import com.nacos.base.BasePageHelper;
import com.nacos.exception.IBusinessException;
import com.nacos.result.Result;
import com.nacos.service.CheckService;
import com.nacos.service.IUserPrivateConfigService;
import com.nacos.service.IUserService;
import com.nacos.service.SysUserService;
import com.nacos.utils.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;


@Tag(name = "用户相关接口", description = "用户相关接口")
@RestController
@RequestMapping("/info")
@Log4j2
public class UserController {

    @Resource
    private CheckService checkService;

    @Resource
    private IUserService iUserService;

    @Resource
    private SysUserService sysUserService;
    @Resource
    private IUserPrivateConfigService userPrivateConfigService;

    @Operation(summary = "获取用户点点信息")
    @GetMapping(value = "/ddPower", name = "获取用户点点信息")
    public Result<UserDDInfoVO> ddInfo() {
        return checkService.getUserDDInfo(JwtUtil.getUserId());
    }

    @Operation(summary = "获取用户点子余额 ")
    @GetMapping(value = "/ddResidue", name = "获取账户点点余额")
    public Result<Double> ddResidue() {
        return checkService.ddResidue(JwtUtil.getUserId());
    }

    @GetMapping(value = "/ddFlow", name = "查询点子消耗记录")
    public Result<Page<FlowRecordPO>> selectDDFlowRecord2(FlowRecordDTO flowRecordDTO) {
        return iUserService.selectDDFlowRecord2(flowRecordDTO);
    }

    @RequestMapping(value = "/updateUserInfo", name = "修改个人信息")
    public Result<?> updateUserInfo(@RequestBody UpdateUserInfoDTO dto) {
        return iUserService.updateUserInfo(dto);
    }

    @Operation(summary = "点点历史套餐记录")
    @GetMapping(value = "/ddRecord", name = "点点历史套餐记录")
    public Result<Page<UserDDRecordVO>> ddRecord(BasePageHelper basePageHelper) {
        return checkService.ddRecord(JwtUtil.getUserId(), basePageHelper);
    }

    @Operation(summary = "数字人申请")
    @GetMapping(value = "/digitalManApply", name = "数字人申请")
    public Result<Object> digitalManApply() {
        return iUserService.digitalManApply(JwtUtil.getUserId());
    }

    @Operation(summary = "数字人申请的人数获取")
    @GetMapping(value = "/queryDigitalManApplySize", name = "数字人申请")
    public Result<Object> queryDigitalManApplySize() {
        return iUserService.queryDigitalManApplySize();
    }

    @PostMapping(value = "/fansOrFollow", name = "查询粉丝或者关注列表分页")
    public Result<IPage<FansOrFollowVO>> queryFansOrFollowPage(@RequestBody FansOrCollectQueryDTO dto) throws IBusinessException {
        dto.setUserId(JwtNewUtil.getUserId());
        return sysUserService.queryFansOrFollowPage(dto);
    }

    @Operation(summary = "一键隐私")
    @PostMapping(value = "/updatePrivate", name = "一键隐私")
    public Result<Object> updatePrivate(@RequestBody PrivateQueryDTO dto) throws IBusinessException {
        return userPrivateConfigService.updatePrivate(dto);
    }

    @Operation(summary = "查询用户隐私权限")
    @GetMapping(value = "/queryPrivate", name = "PrivateQueryDTO")
    public Result<Object> queryPrivate() throws IBusinessException {
        return userPrivateConfigService.queryPrivate();
    }


}
