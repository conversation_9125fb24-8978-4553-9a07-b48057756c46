package com.nacos.controller.user;


import com.nacos.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "mj测试api", description = "mj测试api")
@RestController
@RequestMapping("/mjtest")
@Log4j2
public class MJTestController {

    @Operation(summary = "获取绘图编辑列表：不同语言展示内容不同")
    @GetMapping(value = "/image",name = "获取绘图编辑列表")
    public Result<Object> image() {
        OkHttpClient client = new OkHttpClient();
        // 替换为你要请求的 URL
//        String url = "https://nijijourney.com/api/app/submit-jobs?app_version=1.11.3";
        String url = "https://nijijourney.com/api/mobile/submit-jobs?app_version=1.11.3";
        // 构建请求体
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        String requestBodyJson = "{\n" +
                "    \"channelName\": \"Home Workspace\",\n" +
                "    \"flags\": {\n" +
                "        \"mode\": \"relaxed\",\n" +
                "        \"private\": false\n" +
                "    },\n" +
                "    \"jobType\": \"imagine\",\n" +
                "    \"parameters\": {\n" +
                "        \"ar\": \"3:2\",\n" +
                "        \"niji\": \"6\",\n" +
                "        \"stylize\": 250\n" +
                "    },\n" +
                "    \"prompt\": \"黑色的小马\",\n" +
                "    \"isMobile\": true\n" +
                "}";
        RequestBody requestBody = RequestBody.create(requestBodyJson,mediaType);

        // 构建 POST 请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("authorization", "Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..sjzmu02t-UsaBNYq.pgQ5T5n-7gMHeIos-MGMNveu_UP3yUmmg3tZozljkRlzEy3NOS_N48XPKfPOkQnF1YOreQiwou3RXgeqXImE8eOe3hQ9bhLr435zVhz1iwH71Rq7w9cVCJQ-pZOo3SHCAII33fHDVSzaWgO84p6qr_PgofZ--vqUq20xkMOtb_Ayp21a57SSJPLnzLImUxyo70rqibbN7SI-5hjZ-jcNJ51HY-YvfqaTR3zYFvkSrvaZtcYK8fTa8sHaWphgUDwwqc5Pzh6nGnynbEYzl6m05oU9iJw3TAoV-TwVstgHsOQ4kG-Y84bH9tRoHJ2kZqPBCmYFLHEe1fYIA0Sm-dE648ZchoNSYOeU0eFz_RXbYaZE7ZAdd5psAd1ojmnT2k96t2dKhNu6nqTYPLDJkZqDh42DgY_YFbhtSfPzNqe_YKfjFf4DSXQ478M_uIhwn0PYq6kCwmzsYcRGdCA8E-yGRKBb-xT09TvnId9dLd-gSBSljT42-QhP2Dq5zgjlEuYNT6ZAps_Bhh-pspAeaqnkDhoZTJfeQ3zIA5CDBpjoactyFkMsobeq7AKuxA5yBVPw9ZMcQh3y8ip2ZVqE2rEhqxLttdYFPFx2kzxWVH-jVGRLav8Z_fY4VJiUZpnW8H4U4J_DNeu38R9LOkfw7HeZ9v8zfeQaeOJW3Nf3gxbbaDa8vo7mOpDcpFAmcOwC0W5jArvpPMZ0NY2DZMe8XVqt5XR7djCE0w8zTZ71057fGVkRj_y_jrb0ZHBuF5s36XDcO3PMRQDWoV-Q8KCGNARwTWKXJpIv2mloV7yY7mgLH77UOP04YMyRWtaOI4bieEepw9_Kz6GqOUT7xdt1jg8Q5ymHdNrKHdw.C4mH0f40yNm7sPgewfOyMQ")
                .addHeader("x-app-version", "1.11.3")
                .addHeader("x-csrf-protection", "1")
                .addHeader("User-Agent", "okhttp/4.9.2")
                .addHeader("content-type", "application/json")
                .addHeader("cookie", "__cf_bm=i8N9DYlBrIvP2bmVSne7P5ugaLWpzL0hoQJPWfoLiOo-1706197020-1-AZ6gdQ/T/ytz+oMQ6rIyUBEAGEFoL8OEFYSSaq01s7ANSjjYFJQpnFPaCM0Qi7lD0Lif3GDgDL0LaszT2J2Wpj0=")
                .post(requestBody)
                .build();
        try {
            Response response = client.newCall(request).execute();
            // 打印响应内容
            System.out.println(response.body().string());
            // 从响应中获取 Cookie
            String cookie = response.header("Set-Cookie");
            System.out.println("Cookie: " + cookie);
            System.out.println("response: " + response);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.SUCCESS();
    }


    @Operation(summary = "获取绘图编辑列表：不同语言展示内容不同")
    @GetMapping(value = "/token",name = "获取绘图编辑列表")
    public Result<Object> token() {
        try {
            OkHttpClient client = new OkHttpClient.Builder().cache(null).build();
            RequestBody requestBody = RequestBody.create("{}", MediaType.parse("application/json; charset=utf-8"));
            Request request = new Request.Builder()
                    .url("https://nijijourney.com/api/mobile/refresh?app_version=1.11.3")
                    .addHeader("authorization", "Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..xk_s5PgZTG1Np4vh.9i71AM5acGrjF_UmZrlByh_V_Z7jL_je8q8rHZGnPs2S5wyWPYeiaun99Sh9NcMfKGbNUeEIbiPobgKZzTAPWjzY60MXj0Y9Z_PVJty2cKorXi3sUBocqqxFuD5TFuR3kaEYsrrSPYPcH47g6rLlODM82pwsL1JPvVtZKbo4vli40ZTIllO3-hMCkNGrkAKVdP0xMOnDxgsVoGbyiSAcaJn6XkXZ3Oim7QKPffOoANr5bC8T42nMHqGSyZ6aJuxQhoLJkboknAQYGiehdUTukJR7GWbsL3hBiviw7r3pwgOeh-_WvlfSaTJxXTUbRCcylUGJ5PeSvyg8cCnPOdO1rwnbbkv_6NDOCfCs9SkdBsG0IgKJAE4tKFc7BO6Nf6VhaC8g-VZzXOW6Xt96ybaYL3j28Cs3RbnP6GBr1OgbqUvoVF6FTn_4JHr1eSLZ31d2N8mbdC_goLklbCVBBq1jf2UWVL7HVuoo9gshpF778C8SMwXMXVt7C6CMyiOWj5kOHRRxbchXOe-64P7gOzYwuQaLr9RuyJo5rn3Uih1rCu6sIIUQe4wuXc-MbEF6nLqUtF5yXuxbgpOvT6X094jFN9tbKAAMGZxm9WPfMB2beeu8_HhlZRyhQhBGxST-ga3TMBBQXOOLRCcNfUuuFxpX2aenDg.e-JZ33DdSxoswJuPCIrpsA")
                    .addHeader("x-app-version", "1.11.3")
                    .addHeader("x-csrf-protection", "1")
                    .addHeader("User-Agent", "okhttp/4.9.2")
                    .addHeader("content-type", "application/json")
                    .addHeader("cookie", "__cf_bm=i8N9DYlBrIvP2bmVSne7P5ugaLWpzL0hoQJPWfoLiOo-1706197020-1-AZ6gdQ/T/ytz+oMQ6rIyUBEAGEFoL8OEFYSSaq01s7ANSjjYFJQpnFPaCM0Qi7lD0Lif3GDgDL0LaszT2J2Wpj0=")
                    .post(requestBody)
                    .build();
            Response response = client.newCall(request).execute();
            String responseBody = response.body().string();
            log.info("mj-api-jobs-status responseBody: {}", responseBody);
            log.info("mj-api-jobs-status response: {}", response);
            log.info("mj-api-jobs-status cookie: {}", response.header("Set-Cookie"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
        return Result.SUCCESS();
    }


}
