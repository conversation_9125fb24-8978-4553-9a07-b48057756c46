package com.nacos.controller.user;


import com.business.db.model.po.*;
import com.business.db.model.vo.*;
import com.nacos.enums.DDUseRuleEnum;
import com.nacos.enums.GlobalRedisKeyEnum;
import com.nacos.exception.IBusinessException;
import com.nacos.redis.RedisUtil;
import com.nacos.result.Result;
import com.nacos.service.GlobalService;
import com.nacos.utils.ImageUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Tag(name = "跨国接口", description = "跨国接口：语言切换")
@RestController
@RequestMapping("/global")
@Log4j2
public class GlobalController {

    @Resource
    private GlobalService globalService;

    @Operation(summary = "获取图片标签列表：不同语言展示内容不同")
    @GetMapping(value = "/appTagList/{languageTagId}", name = "获取图片标签列表")
    public Result<List<AppTagConfigPO>> appTagList(@PathVariable Long languageTagId) {
        return globalService.appTagList(languageTagId);
    }

    @Operation(summary = "获取语言列表：用于用户切换语言")
    @GetMapping(value = "/languageList", name = "获取语言列表")
    public Result<List<LanguageTagPO>> languageList() {
        return globalService.languageList();
    }

    @Operation(summary = "获取banner列表：不同语言展示内容不同")
    @GetMapping(value = "/bannerList/{languageTagId}", name = "获取banner列表")
    public Result<List<BannerConfigPO>> bannerList(@PathVariable Long languageTagId) {
        return globalService.bannerList(languageTagId);
    }

    @Operation(summary = "获取功能列表：不同语言展示内容不同")
    @GetMapping(value = "/functionList/{languageTagId}", name = "获取功能列表")
    public Result<List<FunctionConfigPO>> functionList(@PathVariable Long languageTagId, String channelVersion) {
        return globalService.functionList(languageTagId);
    }

    @Operation(summary = "获取功能列表：不同语言展示内容不同")
    @GetMapping(value = "/functionListNew/{languageTagId}", name = "获取功能列表")
    public Result<List<FunctionConfigVO>> functionListNew(@PathVariable Long languageTagId, String channelVersion) {
        return globalService.functionListNew(languageTagId);
    }

    @Operation(summary = "获取编辑功能列表：不同语言展示内容不同")
    @GetMapping(value = "/functionEditList/{languageTagId}", name = "获取编辑功能列表")
    public Result<List<FunctionConfigVO>> functionEditList(@PathVariable Long languageTagId, String channelVersion) {
        return globalService.functionEditList(languageTagId);
    }

    @Operation(summary = "获取图片标签列表：不同语言展示内容不同")
    @GetMapping(value = "/imageTagList/{languageTagId}", name = "获取图片标签列表")
    public Result<List<ImgTagConfigPO>> imageTagList(@PathVariable Long languageTagId) {
        return globalService.imageTagList(languageTagId);
    }

    @Operation(summary = "获取绘图模型列表：不同语言展示内容不同")
    @GetMapping(value = "/draw/imageModelConfigList/{languageTagId}", name = "获取绘图模型列表")
    public Result<List<ImgModelConfigVO>> imageModelConfigList(@PathVariable Long languageTagId, @RequestParam(value = "version", required = false) String version) {
        return globalService.imageModelConfigList(languageTagId, version);
    }

    @Operation(summary = "获取绘图模型列表新版：不同语言展示内容不同")
    @GetMapping(value = "/draw/imageModelConfigListNew/{languageTagId}", name = "获取绘图模型列表")
    public Result<List<ImgModelConfigVO>> imageModelConfigListNew(@PathVariable Long languageTagId, @RequestParam(value = "version", required = false) String version) {
        return globalService.imageModelConfigListNew(languageTagId, version);
    }

    @Operation(summary = "获取绘图编辑列表：全部列表")
    @GetMapping(value = "/draw/imageOptConfigEditList/{languageTagId}", name = "获取绘图编辑列表：不同语言展示内容不同")
    public Result<List<ImgOptConfigVO>> imageOptConfigEditList(@PathVariable Long languageTagId, Long imgDetlId, Integer parentOperate) throws IBusinessException {
        return globalService.imageOptConfigList(languageTagId, imgDetlId, parentOperate);
    }

    @Operation(summary = "获取绘图编辑列表：不同语言展示内容不同")
    @GetMapping(value = "/draw/imageOptConfigList/{languageTagId}/{imgDetlId}/{parentOperate}", name = "获取绘图编辑列表")
    public Result<List<ImgOptConfigVO>> imageOptConfigList(@PathVariable Long languageTagId, @PathVariable Long imgDetlId, @PathVariable Integer parentOperate) throws IBusinessException {
        return globalService.imageOptConfigList(languageTagId, imgDetlId, parentOperate);
    }

    @Operation(summary = "获取绘图编辑列表：仅第一层：不同语言展示内容不同")
    @GetMapping(value = "/draw/imageOptConfigList/{languageTagId}", name = "获取绘图编辑列表")
    public Result<List<ImgOptConfigVO>> imageOptConfigListParent(@PathVariable Long languageTagId) throws IBusinessException {
        return globalService.imageOptConfigListParent(languageTagId);
    }

    @Operation(summary = "获取海报编辑列表：不同语言展示内容不同")
    @GetMapping(value = "/draw/imageOptConfigList/{languageTagId}/{modelId}", name = "获取绘图模型列表")
    public Result<List<PosterOptConfigVO>> imageOptConfigList(@PathVariable Long languageTagId, @PathVariable Integer modelId) throws IBusinessException {
        return globalService.imageOptConfigList(languageTagId, modelId);
    }

    @Operation(summary = "VIP显示banner：不同语言展示内容不同")
    @GetMapping(value = "/vipBanner/{languageTagId}", name = "VIP显示banner")
    public Result<List<VipBannerPO>> vipBanner(@PathVariable Long languageTagId) {
        return globalService.vipBanner(languageTagId);
    }

    @Operation(summary = "获取图片Base64格式：不同语言展示内容不同")
    @GetMapping(value = "/imageUrl", name = "获取图片Base64格式")
    public Result<List<ImgOptConfigVO>> imageOk(String imageUrl) {
        //imageUrl =  "https://storage.googleapis.com/dream-machines-output/f26690d0-d8c8-4a2c-8d50-ec2774e278ac/0_0.png";
        String imgUrl = ImageUtil.getImgUrlToBate64(imageUrl);
        System.out.println(imgUrl);
        return null;
    }

    @Operation(summary = "获取绘图编辑列表：不同语言展示内容不同")
    @GetMapping(value = "/text", name = "获取绘图编辑列表")
    public Result<Object> text() {
        String redisKey = DDUseRuleEnum.OPT_ZOOM.getRedisKey();//缩放
        return Result.SUCCESS(Double.parseDouble(RedisUtil.getValue(GlobalRedisKeyEnum.getChangeKey(GlobalRedisKeyEnum.DD_USE_RULE.getStrKey(), redisKey))));
    }

    @Operation(summary = "获取视频广场的视频")
    @GetMapping(value = "/getVideoListRandom", name = "获取视频广场的视频")
    public Result<Object> 获取视频广场的视频() throws Exception {

        return globalService.getVideoListRandom();
    }

    @Operation(summary = "购买会员页面：挽留用户弹框信息")
    @GetMapping(value = "/retentionPopup/{languageTagId}", name = "查询会员权益挽留弹窗")
    public Result<RetentionPopupVO> retentionPopup(@PathVariable Long languageTagId) {
        return globalService.retentionPopup(languageTagId);
    }

}
