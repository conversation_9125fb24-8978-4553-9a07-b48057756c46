package com.nacos;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@Slf4j
// @MapperScan("com.nacos.user.mapper")
@ComponentScan(basePackages = {"com.nacos","com.business"})
@EnableTransactionManagement
@EnableAsync
@EnableScheduling
@SpringBootApplication
public class FcsUserServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(FcsUserServerApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  fcsUser启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}
