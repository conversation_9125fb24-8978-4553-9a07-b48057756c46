package com.nacos.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(name="兑换码状态枚举", description="兑换码状态枚举")
public enum ExchangeRecordEnum {
    /*0-未使用、1-已使用、2-已过期*/
    STATE_NOT_USED(0),
    STATE_USEING(1),
    STATE_EXPIRE(2)
    ;
    @Schema(description = "整型值")
    private final Integer intValue;

    ExchangeRecordEnum(Integer intValue) {
        this.intValue = intValue;
    }
}
