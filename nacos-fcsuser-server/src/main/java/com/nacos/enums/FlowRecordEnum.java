package com.nacos.enums;

import lombok.Getter;

@Getter
public enum FlowRecordEnum {

    /* 获得点子枚举 */
    OPERATE_TYPE_ADD_PAY(1001,"用户充值"),
    OPERATE_TYPE_ADD_PAY_MEMBER_RENEWAL(1001,"用户充值:会员续费"),

    OPERATE_TYPE_ADD_GIVE_SVIP(4001,"邀请好友:赠送SVIP"),


    OPERATE_TYPE_ADD_TASK(1002,"用户任务"),
    OPERATE_TYPE_ADD_TASK_PASSWORD_EXCHANGE(1002001,"用户任务:口令码兑换"),
    OPERATE_TYPE_ADD_TASK_YAOQINGREN(1002002,"用户任务:邀请好友"),
    OPERATE_TYPE_ADD_TASK_BEIYAOQINGREN(1002003,"用户任务:被邀请人"),
    OPERATE_TYPE_ADD_TASK_APP(1002004,"用户任务:app首次登陆"),
    OPERATE_TYPE_ADD_TASK_WEB(1002005,"用户任务:web首次登陆"),
    OPERATE_TYPE_ADD_DAILY_SIGN(1002006,"用户任务:每日签到"),
    OPERATE_TYPE_ADD_FIRST_GOOG(1002007,"用户任务:首次好评"),

    /* 用户活动 */
    OPERATE_TYPE_ADD_ACTIVITY(1003,"用户活动"),
    OPERATE_TYPE_ADD_ACTIVITY_EXCHANGE_CODE(1003001,"用户活动:兑换码兑换"),
    OPERATE_TYPE_ADD_ACTIVITY_REGISTER(1003001,"用户活动:注册赠送"),

    /* 扣除点子枚举 */
    OPERATE_TYPE_MINUS_DRAW(2001,"用户绘画扣除"),
    OPERATE_TYPE_MINUS_COPYWRITING(2002,"用户文案扣除"),


    RECORD_TYPE_ADD(0,"增加"),
    RECORD_TYPE_MINUS(1,"消耗"),

    ;
    final Integer intValue;

    final String strValue;

    FlowRecordEnum(Integer intValue, String strValue) {
        this.intValue = intValue;
        this.strValue = strValue;
    }
}
