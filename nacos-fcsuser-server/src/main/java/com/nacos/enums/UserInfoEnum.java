package com.nacos.enums;

import lombok.Getter;

@Getter
public enum UserInfoEnum {

    TYPE_USER(0,"用户"),
    TYPE_ADMIN(-1,"管理员"),
    TYPE_CHANNEL(1,"渠道"),

    LOGIN_TYPE_WEB(1,"web"),
    LOGIN_TYPE_APP(2,"app"),

    IS_EVENT_FALSE(0,"未关注"),
    IS_EVENT_TRUE(1,"已关注"),

    API_TYPE_FOLLOW(1,"关注"),
    API_TYPE_FANS(2,"粉丝"),

    IS_LOGIN(0,"登陆"),
    IS_REGISTER(1,"注册"),

    FANS_SWITCH_OFF(0,"关"),
    FANS_SWITCH_OPEN(1,"开"),

    ;
    final Integer intValue;

    final String strValue;

    UserInfoEnum(Integer intValue, String strValue) {
        this.intValue = intValue;
        this.strValue = strValue;
    }
}
