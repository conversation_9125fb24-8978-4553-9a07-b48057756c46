package com.nacos.enums;

import lombok.Getter;

@Getter
public enum ChannelPropertiesEnum {

    //通用的厂商调到id
    CHANNEL_ID_01("117956", "小米", "点赞、收藏、关注"),
    CHANNEL_ID_02("MARKKETING", "华为", "点赞、收藏"),
    CHANNEL_ID_03("SOCIAL", "vivo", "点赞、收藏、关注"),
    CHANNEL_ID_04("ACCOUNT", "华为、vivo", "华为（会员到期、点子到期、绘画，写真，视频，音频等完成通知）、 vivo（会员到期、绘画，写真，视频，音频等完成通知）"),
    CHANNEL_ID_05("snailsPerson", "oppo", "点子到期、会员到期"),
    CHANNEL_ID_06("117954", "小米", "会员到期"),
    CHANNEL_ID_07("117955", "小米", "支付成功"),
    CHANNEL_ID_08("EXPRESS", "华为", "支付成功"),
    CHANNEL_ID_09("ORDER", "vivo", "支付成功"),
    CHANNEL_ID_10("snailsPay", "oppo", "支付成功通知"),
    CHANNEL_ID_11("116256", "小米", "绘画，写真,视频，音频完成通知"),
    CHANNEL_ID_12("snailsDraw", "oppo", "绘画，写真,视频，音频完成通知"),
    CHANNEL_ID_13("MARKETING", "华为、vivo", "华为关注、vivo点子到期"),
    CHANNEL_ID_14("118637", "小米", "签到通知"),
    CHANNEL_ID_15("CONTENT", "vivo", "签到通知"),
    CHANNEL_ID_16("WORK", "华为", "签到通知"),
    CHANNEL_ID_17("snailsDaily", "oppo", "签到通知"),

    CHANNEL_ID_18("TODO", "vivo", "签到通知(暂时不用，以后会用到)"),
    ;

    final String channelId;
    final String channelName;
    final String notificationType;

    ChannelPropertiesEnum(String channelId, String channelName, String notificationType) {
        this.channelId = channelId;
        this.channelName = channelName;
        this.notificationType = notificationType;
    }

}
