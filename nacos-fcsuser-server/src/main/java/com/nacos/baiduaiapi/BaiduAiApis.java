package com.nacos.baiduaiapi;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.nacos.baiduaiapi.model.FaceDetectionRequestBody;
import com.nacos.baiduaiapi.model.FaceDetectionResponseBodyVO;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;

import java.io.IOException;

@Log4j2
public class BaiduAiApis {

        public static final String API_KEY = "sjEDjeGYfixj2YBNIR0lQi0E";
        public static final String SECRET_KEY = "4wXkpSV5WP3RWL1hjFVldFC91CLe2IF4";

        static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();

        public static FaceDetectionResponseBodyVO faceDetection(FaceDetectionRequestBody faceDetectionRequestBody) {
            FaceDetectionResponseBodyVO faceDetectionResponseBodyVO = new FaceDetectionResponseBodyVO();
            faceDetectionResponseBodyVO.setIs_face(false);
            try {
                MediaType mediaType = MediaType.parse("application/json");
                RequestBody body = RequestBody.create(JSON.toJSONString(faceDetectionRequestBody),mediaType);
                Request request = new Request.Builder()
                        .url("https://aip.baidubce.com/rest/2.0/face/v3/detect?access_token=" + getAccessToken())
                        .method("POST", body)
                        .addHeader("Content-Type", "application/json")
                        .build();
                Response response = HTTP_CLIENT.newCall(request).execute();
                JSONObject responseJson = JSONObject.parseObject(response.body().string());
                log.info(responseJson.toJSONString());
                JSONObject json = (JSONObject) responseJson.get("result");
                //校验是否为1张人脸
                if (json.getInteger("face_num") > 0) {
                    faceDetectionResponseBodyVO.setIs_face(true);
                    faceDetectionResponseBodyVO.setFace_num(json.getInteger("face_num"));
                    JSONArray faceList = json.getJSONArray("face_list");
                    JSONObject face = (JSONObject) faceList.get(0);
                    faceDetectionResponseBodyVO.setLeft(face.getJSONObject("location").getDouble("left"));
                    faceDetectionResponseBodyVO.setTop(face.getJSONObject("location").getDouble("top"));
                    faceDetectionResponseBodyVO.setWidth(face.getJSONObject("location").getDouble("width"));
                    faceDetectionResponseBodyVO.setHeight(face.getJSONObject("location").getDouble("height"));
                    log.info("人脸检测成功");
                } else {
                    log.info("人脸检测失败");
                    throw new Exception(response.body().string());
                }
                return faceDetectionResponseBodyVO;
            } catch (Exception e) {
                log.error("人脸检测异常 {}",e.getMessage() , e);
                return faceDetectionResponseBodyVO;
            }
        }

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    static String getAccessToken() throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + API_KEY
                + "&client_secret=" + SECRET_KEY);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        JSONObject jsonObject = JSONObject.parseObject(response.body().string());
        return jsonObject.getString("access_token");
    }

}
