package com.nacos.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 公共参数类
 * 
 * 用于存储系统中需要使用的各种配置参数
 * 
 * 这些参数可以通过Spring的@Value注解注入
 */
@Component
public class CommonPara {

    /*阿里短信*/
    public static String endpoint;
    @Value(value = "${sms.endpoint}")
    public void setEndpoint(String endpoint) {
        CommonPara.endpoint = endpoint;
    }

    /*阿里短信*/
    public static String accessKeyId;
    @Value(value = "${sms.accessKeyId}")
    public void setAccessKeyId(String accessKeyId) {
        CommonPara.accessKeyId = accessKeyId;
    }

    /*阿里短信*/
    public static String accessKeySecret;
    @Value(value = "${sms.accessKeySecret}")
    public void setAccessKeySecret(String accessKeySecret) {
        CommonPara.accessKeySecret = accessKeySecret;
    }

    /*阿里短信*/
    public static String signName;
    @Value(value = "${sms.signName}")
    public void setSignName(String signName) {
        CommonPara.signName = signName;
    }

    /*阿里云验证码*/
    public static String captchaEndpoint;
    @Value(value = "${captcha.endpoint:captcha.cn-shanghai.aliyuncs.com}")
    public void setCaptchaEndpoint(String captchaEndpoint) {
        CommonPara.captchaEndpoint = captchaEndpoint;
    }

    /*阿里云验证码*/
    public static String captchaAccessKeyId;
    @Value(value = "${captcha.accessKeyId:${sms.accessKeyId}}")
    public void setCaptchaAccessKeyId(String captchaAccessKeyId) {
        CommonPara.captchaAccessKeyId = captchaAccessKeyId;
    }

    /*阿里云验证码*/
    public static String captchaAccessKeySecret;
    @Value(value = "${captcha.accessKeySecret:${sms.accessKeySecret}}")
    public void setCaptchaAccessKeySecret(String captchaAccessKeySecret) {
        CommonPara.captchaAccessKeySecret = captchaAccessKeySecret;
    }

    //******************************************************************************
    //消息推送
    public static String umAppId;
    @Value("${umeng.android-app-id}")
    public void setUmAppId(String umAppId) {
        com.nacos.constant.CommonPara.umAppId = umAppId;
    }
    public static String umMasterSecret;
    @Value("${umeng.android-master-secret}")
    public void setUmMasterSecret(String umMasterSecret) {
        com.nacos.constant.CommonPara.umMasterSecret = umMasterSecret;
    }

    public static String umIosAppId;
    @Value("${umeng.ios-app-id}")
    public void setUmIosAppId(String umIosAppId) {
        com.nacos.constant.CommonPara.umIosAppId = umIosAppId;
    }
    public static String umIosMasterSecret;
    @Value("${umeng.ios-master-secret}")
    public void setUmIosMasterSecret(String umIosMasterSecret) {
        com.nacos.constant.CommonPara.umIosMasterSecret = umIosMasterSecret;
    }
    //******************************************************************************
    //友盟一键登陆
    public static String uVerifyAppKey;
    @Value("${umeng.u-verify-app-key}")
    public void setUVerifyAppKey(String uVerifyAppKey) {
        com.nacos.constant.CommonPara.uVerifyAppKey = uVerifyAppKey;
    }

    public static String uVerifyAppSecret;
    @Value("${umeng.u-verify-app-secret}")
    public void setUVerifyAppSecret(String uVerifyAppSecret) {
        com.nacos.constant.CommonPara.uVerifyAppSecret = uVerifyAppSecret;
    }

    public static String uVerifyAppCode;
    @Value("${umeng.u-verify-app-code}")
    public void setUVerifyAppCode(String uVerifyAppCode) {
        com.nacos.constant.CommonPara.uVerifyAppCode = uVerifyAppCode;
    }

    public static String uVerifyUrl;
    @Value("${umeng.u-verify-url}")
    public void setUVerifyUrl(String uVerifyUrl) {
        com.nacos.constant.CommonPara.uVerifyUrl = uVerifyUrl;
    }
    //******************************************************************************

    //******************************************************************************
    //极光一键登陆
    public static String auroraAppKey;
    @Value("${aurora.aurora-app-key}")
    public void setAuroraAppKey(String auroraAppKey) {
        com.nacos.constant.CommonPara.auroraAppKey = auroraAppKey;
    }

    public static String auroraMasterSecret;
    @Value("${aurora.aurora-master-secret}")
    public void setAuroraMasterSecret(String auroraMasterSecret) {
        com.nacos.constant.CommonPara.auroraMasterSecret = auroraMasterSecret;
    }

    public static String auroraVerifyUrl;
    @Value("${aurora.aurora-verify-url}")
    public void setAuroraVerifyUrl(String auroraVerifyUrl) {
        com.nacos.constant.CommonPara.auroraVerifyUrl = auroraVerifyUrl;
    }
    //******************************************************************************
}
